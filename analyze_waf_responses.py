# -*- coding: utf-8 -*-
"""
WAF响应数据分析脚本
作用：分析test_all_waf_payloads.py收集的原始响应数据，识别不同WAF的状态码模式
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import argparse
import os

class WAFResponseAnalyzer:
    def __init__(self, csv_file):
        """
        初始化WAF响应分析器
        
        参数:
        - csv_file: test_all_waf_payloads.py生成的CSV文件路径
        """
        self.csv_file = csv_file
        self.df = pd.read_csv(csv_file, low_memory=False)
        
        # 获取WAF列名（除了payload列）
        self.waf_columns = [col for col in self.df.columns if col != 'payload']
        
        # 清理数据
        for col in self.waf_columns:
            self.df[col] = self.df[col].astype(str)
        
        print(f"已加载 {len(self.df)} 个载荷的响应数据")
        print(f"检测到的WAF: {', '.join(self.waf_columns)}")
        
        # WAF厂商的已知拦截状态码模式（基于经验和文档）
        self.known_block_patterns = {
            'ALIYUN': [405, 403, 406, 444],           # 阿里云WAF
            'HUAWEI': [418, 403, 406, 444],           # 华为云WAF  
            'TENCENT': [403, 406, 444],               # 腾讯云WAF
            'BAIDU': [403, 406, 444],                 # 百度云WAF
            'CLOUDFLARE': [403, 429, 503, 520, 521, 522, 523, 524],  # Cloudflare
            'CLOUDFLARE_FREE': [403, 429, 503, 520, 521, 522, 523, 524],
            'MODSECURITY': [403, 406, 501, 502, 503], # ModSecurity
            'NGX_LUA': [403, 444, 499],               # Nginx Lua WAF
            'SAFELINE': [403, 406],                   # SafeLine
            'NAXSI': [403, 406, 418],                 # NAXSI
            'DEFAULT': [403, 405, 406, 418, 429, 444, 499, 501, 502, 503, 520, 521, 522, 523, 524]
        }
    
    def analyze_status_code_patterns(self):
        """分析每个WAF的状态码分布模式"""
        print("\n=== WAF状态码分布分析 ===")
        
        waf_patterns = {}
        
        for waf in self.waf_columns:
            print(f"\n{waf}:")
            
            # 统计状态码分布
            status_counts = self.df[waf].value_counts().sort_index()
            total_responses = len(self.df[waf].dropna())
            
            # 分析数值状态码
            numeric_codes = {}
            non_numeric_codes = {}
            
            for status, count in status_counts.items():
                percentage = (count / total_responses) * 100
                try:
                    code = int(float(status))
                    numeric_codes[code] = {'count': count, 'percentage': percentage}
                    print(f"  {code}: {count} ({percentage:.1f}%)")
                except (ValueError, TypeError):
                    non_numeric_codes[status] = {'count': count, 'percentage': percentage}
                    print(f"  {status}: {count} ({percentage:.1f}%) [非数值响应]")
            
            # 识别可能的拦截状态码
            potential_block_codes = []
            for code, stats in numeric_codes.items():
                # 如果状态码不是200且出现频率较高，可能是拦截状态码
                if code != 200 and stats['percentage'] > 1.0:
                    potential_block_codes.append(code)
            
            waf_patterns[waf] = {
                'numeric_codes': numeric_codes,
                'non_numeric_codes': non_numeric_codes,
                'potential_block_codes': potential_block_codes,
                'total_responses': total_responses
            }
            
            if potential_block_codes:
                print(f"  → 可能的拦截状态码: {potential_block_codes}")
            else:
                print(f"  → 未发现明显的拦截状态码模式")
        
        return waf_patterns
    
    def detect_waf_block_patterns(self, waf_patterns):
        """基于状态码分布检测WAF的拦截模式"""
        print("\n=== WAF拦截模式检测 ===")
        
        detected_patterns = {}
        
        for waf, pattern in waf_patterns.items():
            print(f"\n{waf}:")
            
            # 获取已知模式
            known_pattern = self.known_block_patterns.get(waf, self.known_block_patterns['DEFAULT'])
            
            # 检测实际的拦截状态码
            detected_blocks = []
            for code in pattern['potential_block_codes']:
                if code in known_pattern:
                    detected_blocks.append(code)
                    print(f"  ✓ 确认拦截状态码: {code}")
                else:
                    print(f"  ? 未知状态码: {code} (可能是新的拦截模式)")
                    detected_blocks.append(code)  # 保守地认为是拦截
            
            # 检查是否有遗漏的已知拦截状态码
            missing_codes = set(known_pattern) - set(pattern['numeric_codes'].keys())
            if missing_codes:
                print(f"  - 未出现的已知拦截状态码: {missing_codes}")
            
            detected_patterns[waf] = detected_blocks
        
        return detected_patterns
    
    def calculate_block_rates(self, detected_patterns):
        """基于检测到的拦截模式计算拦截率"""
        print("\n=== 拦截率计算 ===")
        
        block_rates = {}
        
        for waf in self.waf_columns:
            block_codes = detected_patterns.get(waf, [])
            
            blocked_count = 0
            total_count = 0
            
            for _, row in self.df.iterrows():
                status = row[waf]
                if pd.notna(status) and status not in ['nan', 'Timeout', 'Error']:
                    total_count += 1
                    try:
                        code = int(float(status))
                        if code in block_codes:
                            blocked_count += 1
                    except (ValueError, TypeError):
                        continue
            
            block_rate = blocked_count / total_count if total_count > 0 else 0
            block_rates[waf] = {
                'blocked': blocked_count,
                'total': total_count,
                'rate': block_rate,
                'block_codes': block_codes
            }
            
            print(f"{waf}: {blocked_count}/{total_count} ({block_rate:.1%}) - 拦截状态码: {block_codes}")
        
        return block_rates
    
    def analyze_payload_effectiveness(self, detected_patterns):
        """分析载荷的有效性和区分度"""
        print("\n=== 载荷有效性分析 ===")
        
        effective_payloads = []
        
        for idx, row in self.df.iterrows():
            payload = row['payload']
            waf_responses = {}
            
            # 记录每个WAF的响应（拦截/通过）
            for waf in self.waf_columns:
                status = row[waf]
                block_codes = detected_patterns.get(waf, [])
                
                is_blocked = False
                if pd.notna(status) and status not in ['nan', 'Timeout', 'Error']:
                    try:
                        code = int(float(status))
                        is_blocked = code in block_codes
                    except (ValueError, TypeError):
                        pass
                
                waf_responses[waf] = is_blocked
            
            # 计算载荷的区分度
            unique_responses = len(set(waf_responses.values()))
            if unique_responses > 1:  # 如果WAF之间有不同响应
                effective_payloads.append({
                    'payload': payload,
                    'responses': waf_responses,
                    'diversity_score': unique_responses / len(self.waf_columns)
                })
        
        print(f"找到 {len(effective_payloads)} 个有区分度的载荷")
        
        # 分析响应模式
        response_patterns = Counter()
        for payload_info in effective_payloads:
            pattern = tuple(payload_info['responses'].values())
            response_patterns[pattern] += 1
        
        print("\n最常见的响应模式:")
        for pattern, count in response_patterns.most_common(10):
            pattern_str = ', '.join([f"{waf}:{'拦截' if blocked else '通过'}" 
                                   for waf, blocked in zip(self.waf_columns, pattern)])
            print(f"  {pattern_str}: {count} 个载荷")
        
        return effective_payloads
    
    def generate_analysis_report(self, output_file):
        """生成完整的分析报告"""
        # 执行分析
        waf_patterns = self.analyze_status_code_patterns()
        detected_patterns = self.detect_waf_block_patterns(waf_patterns)
        block_rates = self.calculate_block_rates(detected_patterns)
        effective_payloads = self.analyze_payload_effectiveness(detected_patterns)
        
        # 生成报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("WAF响应数据分析报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据源: {self.csv_file}\n")
            f.write(f"载荷总数: {len(self.df)}\n")
            f.write(f"WAF数量: {len(self.waf_columns)}\n\n")
            
            # 检测到的拦截模式
            f.write("## 检测到的WAF拦截状态码模式\n\n")
            for waf, codes in detected_patterns.items():
                f.write(f"**{waf}**: {codes}\n")
            
            # 拦截率统计
            f.write("\n## WAF拦截率统计\n\n")
            for waf, stats in block_rates.items():
                f.write(f"**{waf}**: {stats['blocked']}/{stats['total']} ({stats['rate']:.1%})\n")
            
            # 载荷有效性
            f.write(f"\n## 载荷有效性\n\n")
            f.write(f"- 总载荷数: {len(self.df)}\n")
            f.write(f"- 有区分度的载荷: {len(effective_payloads)}\n")
            f.write(f"- 有效载荷比例: {len(effective_payloads)/len(self.df):.1%}\n")
            
            # 改进建议
            f.write("\n## 改进建议\n\n")
            for waf, stats in block_rates.items():
                if stats['rate'] < 0.2:
                    f.write(f"- **{waf}**: 拦截率过低({stats['rate']:.1%})，建议增加针对性载荷\n")
                elif stats['rate'] > 0.9:
                    f.write(f"- **{waf}**: 拦截率过高({stats['rate']:.1%})，建议增加绕过载荷\n")
        
        return {
            'waf_patterns': waf_patterns,
            'detected_patterns': detected_patterns,
            'block_rates': block_rates,
            'effective_payloads': effective_payloads
        }

def main():
    parser = argparse.ArgumentParser(description="WAF响应数据分析工具")
    parser.add_argument("--csv-file", required=True, help="WAF响应数据CSV文件路径")
    parser.add_argument("--output-dir", default="analysis_output", help="输出目录")
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建分析器
    analyzer = WAFResponseAnalyzer(args.csv_file)
    
    # 生成分析报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = os.path.join(args.output_dir, f"waf_analysis_report_{timestamp}.txt")
    
    analysis_results = analyzer.generate_analysis_report(report_file)
    
    print(f"\n分析完成！报告已保存到: {report_file}")
    
    return analysis_results

if __name__ == "__main__":
    main()
