# -*- coding: utf-8 -*-
"""
分析当前WAF测试数据，提供载荷集改进建议
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime

def analyze_waf_data(csv_file):
    """分析WAF测试数据"""

    # 读取数据
    df = pd.read_csv(csv_file, low_memory=False)
    print(f"已加载 {len(df)} 个载荷的测试数据")

    # 清理数据，将非数值状态码转换为字符串
    waf_columns = [col for col in df.columns if col != 'payload']
    for col in waf_columns:
        df[col] = df[col].astype(str)

    # 获取WAF列名
    waf_columns = [col for col in df.columns if col != 'payload']
    print(f"检测到的WAF: {', '.join(waf_columns)}")

    # WAF拦截状态码映射
    waf_block_codes = {
        'MODSECURITY': [403, 406, 501, 502, 503],
        'NGX_LUA': [403, 444, 499],
        'SAFELINE': [403, 406],
        'NAXSI': [403, 406, 418],
        'CLOUDFLARE': [403, 429, 503, 520, 521, 522, 523, 524],
        'CLOUDFLARE_FREE': [403, 429, 503, 520, 521, 522, 523, 524],
        'ALIYUN': [405, 403, 406, 444],
        'HUAWEI': [418, 403, 406, 444],
        'DEFAULT': [403, 405, 406, 418, 429, 444, 499, 501, 502, 503, 520, 521, 522, 523, 524]
    }

    def is_blocked(status_code, waf_name):
        """判断是否被拦截"""
        if pd.isna(status_code) or status_code in ['nan', 'Timeout', 'Error']:
            return False
        try:
            status_code = int(float(status_code))
            block_codes = waf_block_codes.get(waf_name, waf_block_codes['DEFAULT'])
            return status_code in block_codes
        except (ValueError, TypeError):
            return False

    print("\n=== 状态码分布分析 ===")
    for waf in waf_columns:
        print(f"\n{waf}:")
        status_counts = df[waf].value_counts().sort_index()
        for status, count in status_counts.items():
            percentage = (count / len(df)) * 100
            is_block = is_blocked(status, waf)
            block_indicator = " [拦截]" if is_block else " [通过]"
            print(f"  {status}: {count} ({percentage:.1f}%){block_indicator}")

    print("\n=== 拦截率分析 ===")
    block_stats = {}
    for waf in waf_columns:
        blocked_count = 0
        total_count = 0

        for _, row in df.iterrows():
            status_code = row[waf]
            if not pd.isna(status_code) and status_code not in ['Timeout', 'Error']:
                total_count += 1
                if is_blocked(status_code, waf):
                    blocked_count += 1

        block_rate = blocked_count / total_count if total_count > 0 else 0
        block_stats[waf] = {
            'blocked': blocked_count,
            'total': total_count,
            'rate': block_rate
        }
        print(f"{waf}: {blocked_count}/{total_count} ({block_rate:.1%})")

    print("\n=== WAF区分度分析 ===")
    # 分析哪些载荷能够区分不同的WAF
    distinguishing_payloads = []

    for _, row in df.iterrows():
        payload = row['payload']
        waf_responses = {}

        for waf in waf_columns:
            status = row[waf]
            waf_responses[waf] = is_blocked(status, waf)

        # 计算有多少种不同的响应模式
        response_pattern = tuple(waf_responses.values())
        unique_responses = len(set(waf_responses.values()))

        if unique_responses > 1:  # 如果WAF之间有不同的响应
            distinguishing_payloads.append({
                'payload': payload,
                'responses': waf_responses,
                'pattern': response_pattern,
                'diversity': unique_responses
            })

    print(f"找到 {len(distinguishing_payloads)} 个具有区分度的载荷")

    # 分析响应模式
    pattern_counts = Counter([p['pattern'] for p in distinguishing_payloads])
    print("\n最常见的响应模式:")
    for pattern, count in pattern_counts.most_common(10):
        pattern_str = ', '.join([f"{waf}:{('拦截' if blocked else '通过')}"
                                for waf, blocked in zip(waf_columns, pattern)])
        print(f"  {pattern_str}: {count} 个载荷")

    print("\n=== 载荷集改进建议 ===")

    # 1. 状态码覆盖建议
    print("\n1. 状态码覆盖建议:")
    for waf in waf_columns:
        expected_codes = waf_block_codes.get(waf, waf_block_codes['DEFAULT'])
        # 只获取数值状态码
        actual_codes = set()
        for code in df[waf].dropna().unique():
            try:
                actual_codes.add(int(float(code)))
            except (ValueError, TypeError):
                continue  # 跳过非数值状态码

        missing_codes = set(expected_codes) - actual_codes
        if missing_codes:
            print(f"   {waf}: 缺少状态码 {missing_codes}")
        else:
            print(f"   {waf}: 状态码覆盖完整")

    # 2. 拦截率平衡建议
    print("\n2. 拦截率平衡建议:")
    avg_block_rate = np.mean([stats['rate'] for stats in block_stats.values()])
    for waf, stats in block_stats.items():
        if stats['rate'] < 0.3:
            print(f"   {waf}: 拦截率过低 ({stats['rate']:.1%})，建议增加针对性载荷")
        elif stats['rate'] > 0.8:
            print(f"   {waf}: 拦截率过高 ({stats['rate']:.1%})，建议增加绕过载荷")
        else:
            print(f"   {waf}: 拦截率适中 ({stats['rate']:.1%})")

    # 3. 特异性载荷建议
    print("\n3. 特异性载荷分析:")
    for target_waf in waf_columns:
        specific_count = 0
        for _, row in df.iterrows():
            target_blocked = is_blocked(row[target_waf], target_waf)
            if target_blocked:
                other_blocked = sum(1 for other_waf in waf_columns
                                  if other_waf != target_waf and is_blocked(row[other_waf], other_waf))
                if other_blocked == 0:  # 只有目标WAF拦截
                    specific_count += 1

        print(f"   {target_waf}: {specific_count} 个特异性载荷")

    # 4. 载荷多样性建议
    print("\n4. 载荷多样性建议:")
    payload_types = analyze_payload_types(df['payload'].tolist())
    for payload_type, count in payload_types.items():
        percentage = (count / len(df)) * 100
        print(f"   {payload_type}: {count} ({percentage:.1f}%)")

    return df, block_stats, distinguishing_payloads

def analyze_payload_types(payloads):
    """分析载荷类型分布"""
    types = defaultdict(int)

    for payload in payloads:
        payload_lower = payload.lower()

        if 'script' in payload_lower or 'javascript' in payload_lower:
            types['XSS载荷'] += 1
        elif 'select' in payload_lower or 'union' in payload_lower or 'or' in payload_lower:
            types['SQL注入载荷'] += 1
        elif 'php' in payload_lower or 'exec' in payload_lower or 'system' in payload_lower:
            types['命令注入载荷'] += 1
        elif '<' in payload and '>' in payload:
            types['HTML标签载荷'] += 1
        elif '%' in payload:
            types['URL编码载荷'] += 1
        else:
            types['其他载荷'] += 1

    return dict(types)

def generate_improvement_suggestions(csv_file, output_file):
    """生成载荷集改进建议报告"""

    df, block_stats, distinguishing_payloads = analyze_waf_data(csv_file)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("WAF载荷集改进建议报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("## 问题分析\n\n")
        f.write("1. **不同WAF使用不同的拦截状态码**\n")
        f.write("   - 阿里云WAF: 主要使用405状态码\n")
        f.write("   - 华为云WAF: 主要使用418状态码\n")
        f.write("   - 传统WAF: 主要使用403状态码\n\n")

        f.write("2. **当前识别方法的局限性**\n")
        f.write("   - 仅基于403状态码的识别方法会误判\n")
        f.write("   - 需要建立多状态码拦截模式\n")
        f.write("   - 需要考虑WAF厂商的特定行为模式\n\n")

        f.write("## 改进建议\n\n")
        f.write("### 1. 状态码映射改进\n")
        f.write("建议为每种WAF建立专门的拦截状态码列表:\n")
        f.write("```python\n")
        f.write("WAF_BLOCK_CODES = {\n")
        f.write("    'ALIYUN': [405, 403, 406, 444],\n")
        f.write("    'HUAWEI': [418, 403, 406, 444],\n")
        f.write("    'CLOUDFLARE': [403, 429, 503, 520, 521, 522, 523, 524],\n")
        f.write("    'MODSECURITY': [403, 406, 501, 502, 503],\n")
        f.write("    # ... 其他WAF\n")
        f.write("}\n")
        f.write("```\n\n")

        f.write("### 2. 载荷集优化建议\n")
        for waf, stats in block_stats.items():
            if stats['rate'] < 0.3:
                f.write(f"- **{waf}**: 拦截率过低({stats['rate']:.1%})，建议:\n")
                f.write(f"  - 增加针对该WAF的特定攻击载荷\n")
                f.write(f"  - 研究该WAF的规则特点，设计绕过载荷\n")
            elif stats['rate'] > 0.8:
                f.write(f"- **{waf}**: 拦截率过高({stats['rate']:.1%})，建议:\n")
                f.write(f"  - 增加绕过载荷以提高识别准确性\n")
                f.write(f"  - 添加更多变形和编码技术\n")

        f.write("\n### 3. 识别算法改进\n")
        f.write("- 使用多特征融合识别方法\n")
        f.write("- 考虑响应时间、响应头等额外特征\n")
        f.write("- 建立WAF行为模式库\n")
        f.write("- 使用机器学习方法提高识别准确率\n\n")

        f.write("### 4. 测试方法改进\n")
        f.write("- 增加多轮测试以确认结果\n")
        f.write("- 使用不同的请求方法(GET, POST, PUT等)\n")
        f.write("- 测试不同的参数位置(URL, Header, Body)\n")
        f.write("- 考虑WAF的学习和适应能力\n")

if __name__ == "__main__":
    import sys

    if len(sys.argv) != 2:
        print("用法: python analyze_current_data.py <csv_file>")
        sys.exit(1)

    csv_file = sys.argv[1]
    output_file = f"waf_improvement_suggestions_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    try:
        generate_improvement_suggestions(csv_file, output_file)
        print(f"分析完成，改进建议已保存到: {output_file}")
    except Exception as e:
        print(f"分析过程中出错: {e}")
