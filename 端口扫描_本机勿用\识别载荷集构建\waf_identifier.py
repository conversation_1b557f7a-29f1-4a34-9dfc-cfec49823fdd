#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WAF识别器 - 加载特异性载荷并识别未知WAF

使用方法:
1. 识别单个URL:
   python waf_identifier.py --url http://example.com/

2. 批量识别多个URL (使用默认的urls.txt文件):
   python waf_identifier.py

3. 批量识别多个URL (指定URL文件):
   python waf_identifier.py --url-file my_urls.txt

4. 指定输出CSV文件:
   python waf_identifier.py --output-csv my_results.csv
   (默认输出到 waf_results_YYYYMMDD_HHMMSS.csv)

5. 使用更多载荷:
   python waf_identifier.py --url http://example.com/ --num-payloads 500

6. 平衡各WAF的载荷数量:
   python waf_identifier.py --url http://example.com/ --balance

7. 验证SSL证书 (默认不验证):
   python waf_identifier.py --url https://example.com/ --verify-ssl

8. 不自动跟随重定向 (解决HTTP到HTTPS重定向问题):
   python waf_identifier.py --url http://example.com/ --no-follow-redirects
"""

import os
import sys
import argparse
import pandas as pd
import requests
import time
import random
import signal
from tqdm import tqdm
import urllib3
import csv
from datetime import datetime
from requests.exceptions import SSLError, ConnectionError
from collections import defaultdict

# 全局变量，用于信号处理
interrupted = False
results = []
output_csv_path = None

def save_results():
    """保存当前结果到CSV文件"""
    global results, output_csv_path

    if not results or not output_csv_path:
        return

    try:
        # 获取所有字段
        fieldnames = set()
        for result in results:
            fieldnames.update(result.keys())

        # 按照一定顺序排列字段
        ordered_fields = ['url', 'original_url', 'status', 'redirected', 'waf_name', 'confidence', 'timestamp']
        # 添加其他字段
        for field in sorted(fieldnames):
            if field not in ordered_fields:
                ordered_fields.append(field)

        with open(output_csv_path, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=ordered_fields)
            writer.writeheader()
            writer.writerows(results)

        print(f"\n结果已保存到 {output_csv_path}")
    except Exception as e:
        print(f"保存结果时出错: {e}")

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    global interrupted

    if interrupted:  # 如果已经中断过一次，直接退出
        print("\n再次按下Ctrl+C，强制退出...")
        sys.exit(1)

    print("\n检测到Ctrl+C，正在保存当前结果并退出...")
    interrupted = True
    save_results()
    sys.exit(0)

class WAFIdentifier:
    def __init__(self, payload_dir="specific_payloads", csv_path=None):
        """
        初始化WAF识别器

        参数:
        - payload_dir: 特异性载荷目录
        - csv_path: 原始响应CSV文件路径（可选，用于获取WAF列表）
        """
        self.payload_dir = payload_dir
        self.csv_path = csv_path
        self.waf_names = []
        self.specific_payloads = {}

        # 重定向相关属性
        self.redirected_to_https = False
        self.final_url = None

        # 初始化
        self._initialize()

    def _initialize(self):
        """初始化WAF列表和载荷"""
        # 如果提供了CSV文件，从中获取WAF列表
        if self.csv_path and os.path.exists(self.csv_path):
            try:
                df = pd.read_csv(self.csv_path)
                self.waf_names = [col for col in df.columns if col != 'payload']
                print(f"从CSV文件加载了WAF列表: {self.waf_names}")
            except Exception as e:
                print(f"从CSV文件加载WAF列表时出错: {e}")

        # 如果没有从CSV获取WAF列表，尝试从载荷目录获取
        if not self.waf_names:
            try:
                files = os.listdir(self.payload_dir)
                self.waf_names = [f.replace("_specific_payloads.csv", "") for f in files
                                 if f.endswith("_specific_payloads.csv")]
                print(f"从载荷目录加载了WAF列表: {self.waf_names}")
            except Exception as e:
                print(f"从载荷目录加载WAF列表时出错: {e}")

        # 加载特异性载荷
        self.load_specific_payloads()

    def is_blocked(self, status_code):
        """判断状态码是否表示拦截"""
        # 如果状态码为0，表示请求失败（SSL错误、连接错误等），视为错误而非拦截
        if status_code == 0:
            return False

        # 只有403 Forbidden表示拦截，其他状态码表示不拦截
        # 这种判断逻辑基于:
        # 1. 大多数WAF在拦截时返回403状态码
        # 2. 训练数据中使用403表示拦截，其他状态码表示不拦截
        # 3. 基于拦截率的匹配需要与训练数据使用相同的判断标准
        return status_code == 403

    def load_specific_payloads(self):
        """从文件加载特异性载荷"""
        self.specific_payloads = {}

        for waf_name in self.waf_names:
            input_path = os.path.join(self.payload_dir, f"{waf_name}_specific_payloads.csv")
            if not os.path.exists(input_path):
                print(f"警告: 未找到 {waf_name} 的特异性载荷文件")
                continue

            try:
                df = pd.read_csv(input_path)
                payloads = []

                for _, row in df.iterrows():
                    # 提取预期行为
                    expected_behaviors = {}
                    for waf in self.waf_names:
                        expected_col = f"{waf}_expected"
                        if expected_col in row:
                            expected_behaviors[waf] = (row[expected_col] == "拦截")
                        else:
                            # 如果没有预期行为列，使用默认值
                            # 对于目标WAF，预期行为是拦截
                            # 对于其他WAF，预期行为是通过
                            expected_behaviors[waf] = (waf == waf_name)

                    payloads.append({
                        'payload': row['payload'],
                        'specificity': row['specificity'],
                        'expected_behaviors': expected_behaviors
                    })

                self.specific_payloads[waf_name] = payloads
                print(f"已加载 {waf_name} 的 {len(payloads)} 个特异性载荷")

            except Exception as e:
                print(f"加载 {waf_name} 的特异性载荷时出错: {e}")

        # 检查是否成功加载了特异性载荷
        if not self.specific_payloads:
            print("错误: 未能加载任何特异性载荷")
            return False

        return True

    def identify_waf(self, waf_url, num_payloads=60, timeout=5, verify_ssl=False, verbose=True, balance_payloads=False, follow_redirects=True):
        """
        识别未知WAF

        参数:
        - waf_url: WAF的URL (支持http和https)
        - num_payloads: 每个WAF使用的载荷数量
        - timeout: 请求超时时间（秒）
        - verify_ssl: 是否验证SSL证书（仅HTTPS时有效，默认False）
        - verbose: 是否打印详细信息
        - balance_payloads: 是否平衡各WAF的载荷数量（默认False）
        - follow_redirects: 是否自动跟随重定向（默认True）

        返回:
        - 识别结果和置信度
        """
        if not self.specific_payloads:
            raise ValueError("未加载特异性载荷，无法识别WAF")

        # 重置重定向相关属性
        self.redirected_to_https = False
        self.final_url = None

        # 存储每个WAF选择的载荷，用于后续计算匹配率
        self.selected_payloads = {}

        # 从每个WAF的特异性载荷中选择一些
        test_payloads = []

        # 检查每个WAF的载荷数量
        waf_payload_counts = {}
        for waf_name, payloads in self.specific_payloads.items():
            waf_payload_counts[waf_name] = len(payloads) if payloads else 0
            if verbose:
                if waf_payload_counts[waf_name] == 0:
                    print(f"警告: {waf_name} 没有可用的载荷")
                elif waf_payload_counts[waf_name] < num_payloads:
                    print(f"警告: {waf_name} 的载荷数量 ({waf_payload_counts[waf_name]}) 少于指定数量 ({num_payloads})")

        # 如果需要平衡载荷数量，找出所有WAF中最小的载荷数量
        if balance_payloads and len(self.specific_payloads) > 1:
            # 只考虑载荷数量大于0的WAF
            available_payloads = [count for count in waf_payload_counts.values() if count > 0]
            if available_payloads:
                min_available = min(available_payloads)
                balanced_num = min(num_payloads, min_available)
                if verbose:
                    print(f"平衡模式：每个WAF使用 {balanced_num} 个载荷")
                num_payloads = balanced_num
            else:
                if verbose:
                    print("警告: 没有WAF有可用的载荷，无法进行识别")
                return "Unknown", 0, {}, False, None

        # 检查是否有足够的载荷
        if num_payloads == 0:
            if verbose:
                print("警告: 载荷数量为0，无法进行识别")
            return "Unknown", 0, {}, False, None

        for waf_name, payloads in self.specific_payloads.items():
            if payloads:
                # 选择指定数量或所有特异性载荷（取较小值）
                num_to_select = min(num_payloads, len(payloads))
                selected = payloads[:num_to_select]
                # 存储选择的载荷
                self.selected_payloads[waf_name] = selected
                test_payloads.extend(selected)

        # 随机打乱
        random.shuffle(test_payloads)

        # 测试载荷并收集响应
        unknown_responses = {}
        session = requests.Session()
        # 设置是否自动跟随重定向
        session.allow_redirects = follow_redirects

        # 记录连接超时的次数
        timeout_count = 0
        # 设置超时阈值，超过这个次数就跳过当前URL
        max_timeout_allowed = 5

        if verbose:
            print(f"正在测试 {len(test_payloads)} 个载荷...")

        for item in tqdm(test_payloads):
            payload = item['payload']

            try:
                # 添加重试机制
                max_retries = 1
                retry_delay = 0.5

                for retry in range(max_retries):
                    try:
                        # 发送请求
                        # 注意：这里假设WAF接受GET请求，参数名为"title"
                        # 根据实际情况调整请求方式和参数
                        response = session.get(
                            waf_url,
                            params={"title": payload},
                            timeout=timeout,
                            verify=verify_ssl,  # 控制是否验证SSL证书
                            allow_redirects=follow_redirects  # 控制是否自动跟随重定向
                        )
                        # 请求成功，跳出重试循环
                        break
                    except (ConnectionError, requests.exceptions.Timeout) as e:
                        # 如果不是最后一次重试，则等待后重试
                        if retry < max_retries - 1:
                            if verbose:
                                print(f"连接超时，将在 {retry_delay} 秒后重试 ({retry+1}/{max_retries}): {e}")
                            time.sleep(retry_delay)
                            # 每次重试增加延迟
                            retry_delay *= 1.5
                        else:
                            # 最后一次重试也失败，重新抛出异常
                            raise

                # 记录状态码
                unknown_responses[payload] = response.status_code

                # 检查是否发生了重定向
                if follow_redirects and len(response.history) > 0:
                    # 获取原始URL和最终URL
                    original_url = waf_url
                    final_url = response.url

                    # 如果发生了HTTP到HTTPS的重定向，记录下来
                    if original_url.startswith('http://') and final_url.startswith('https://'):
                        # 设置标志，稍后用于更新结果中的URL
                        self.redirected_to_https = True
                        self.final_url = final_url
                        if verbose:
                            print(f"检测到HTTP到HTTPS的重定向: {original_url} -> {final_url}")

                # 添加短暂延迟，避免请求过于频繁
                time.sleep(0.5)  # 增加延迟，减少请求频率

            except SSLError as e:
                if verbose:
                    print(f"SSL证书验证失败: {e}")
                    print("提示: 默认不验证SSL证书，如果您使用了 --verify-ssl 选项，可以移除该选项")
                # 对于SSL失败的请求，记录为特定状态码
                unknown_responses[payload] = 0  # 使用0表示SSL错误（将被视为错误而非拦截）
            except ConnectionError as e:
                if verbose:
                    print(f"连接错误: {e}")
                # 增加超时计数
                timeout_count += 1
                # 检查是否超过阈值
                if timeout_count > max_timeout_allowed:
                    if verbose:
                        print(f"连接超时次数超过阈值 ({max_timeout_allowed})，跳过当前URL")
                    # 返回特殊标记，表示URL连接超时
                    return "Connection Timeout", 0, {"error": "连接超时次数过多"}, False, None
                # 对于连接失败的请求，记录为特定状态码
                unknown_responses[payload] = 0  # 使用0表示连接错误（将被视为错误而非拦截）
            except Exception as e:
                if verbose:
                    print(f"请求失败: {e}")
                # 对于其他失败的请求，记录为特定状态码
                unknown_responses[payload] = 0  # 使用0表示请求失败（将被视为错误而非拦截）

        # 计算每个WAF的匹配度
        match_scores = {}
        match_details = {}

        for waf_name, selected_payloads in self.selected_payloads.items():
            total_weight = 0
            matched_weight = 0
            tested_count = 0
            matched_count = 0

            for item in selected_payloads:
                payload = item['payload']
                specificity = item['specificity']
                expected_behaviors = item.get('expected_behaviors', {})

                # 如果载荷在未知WAF响应中
                if payload in unknown_responses:
                    tested_count += 1
                    total_weight += specificity

                    # 检查未知WAF是否拦截该载荷
                    is_blocked_by_unknown = self.is_blocked(unknown_responses[payload])

                    # 获取该WAF对该载荷的预期行为
                    expected_behavior = expected_behaviors.get(waf_name, True)  # 默认预期是拦截

                    # 如果行为匹配，增加匹配分数
                    if is_blocked_by_unknown == expected_behavior:
                        matched_weight += specificity
                        matched_count += 1

            # 计算匹配度
            if total_weight > 0:
                match_scores[waf_name] = matched_weight / total_weight
            else:
                match_scores[waf_name] = 0

            # 记录详细信息
            match_details[waf_name] = {
                'total_weight': total_weight,
                'matched_weight': matched_weight,
                'tested_count': tested_count,
                'matched_count': matched_count,
                'match_rate': match_scores[waf_name]
            }

        # 找出匹配度最高的WAF
        if not match_scores:
            return "Unknown", 0, match_details, self.redirected_to_https, self.final_url

        # 检查是否所有WAF的匹配率都低于50%
        all_low_confidence = all(score < 0.5 for score in match_scores.values())
        if all_low_confidence:
            return "未知waf", 0, match_details, self.redirected_to_https, self.final_url

        best_match = max(match_scores.items(), key=lambda x: x[1])
        waf_name, confidence = best_match

        # 打印结果
        if verbose:
            print(f"\n识别结果: {waf_name}")
            if waf_name == "未知waf":
                print("所有WAF的匹配率都低于50%，无法确定WAF类型")
            else:
                print(f"置信度: {confidence:.4f}")

            print("\n所有WAF的匹配率:")
            for waf, details in match_details.items():
                print(f"  {waf}: {details['match_rate']:.4f} ({details['matched_count']}/{details['tested_count']} 载荷匹配)")

        # 返回识别结果、置信度、匹配详情和重定向信息
        return waf_name, confidence, match_details, self.redirected_to_https, self.final_url

def main():
    """主函数"""
    global results, output_csv_path, interrupted

    # 注册信号处理函数，捕获Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="WAF识别器 - 加载特异性载荷并识别未知WAF")
    parser.add_argument("--url", help="要识别的WAF URL (支持http和https)")
    parser.add_argument("--url-file", default="urls.txt", help="包含多个URL的文本文件，每行一个URL (默认: urls.txt)")
    parser.add_argument("--output-csv", help="输出结果的CSV文件路径 (默认: waf_results_YYYYMMDD_HHMMSS.csv)")
    parser.add_argument("--payload-dir", default="identify_payloads", help="特异性载荷目录")
    parser.add_argument("--csv-path", help="原始响应CSV文件路径（可选）")
    parser.add_argument("--num-payloads", type=int, default=50, help="每个WAF使用的载荷数量（默认50）")
    parser.add_argument("--timeout", type=float, default=5, help="请求超时时间（秒，默认5）")
    parser.add_argument("--verify-ssl", action="store_true", help="验证SSL证书（默认不验证）")
    parser.add_argument("--no-follow-redirects", action="store_true", help="不自动跟随重定向")
    parser.add_argument("--balance", action="store_true", help="平衡各WAF的载荷数量")
    parser.add_argument("--quiet", action="store_true", help="静默模式，不打印详细信息")

    args = parser.parse_args()

    # 检查参数
    # 如果URL文件路径是相对路径，则相对于脚本所在目录查找
    if not args.url:
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))

        # 如果URL文件是相对路径，则相对于脚本所在目录
        if not os.path.isabs(args.url_file):
            url_file_path = os.path.join(script_dir, args.url_file)
        else:
            url_file_path = args.url_file

        # 检查文件是否存在
        if not os.path.exists(url_file_path):
            # 尝试在当前工作目录查找
            if os.path.exists(args.url_file):
                url_file_path = args.url_file
            else:
                print(f"错误: URL文件 '{args.url_file}' 不存在，且未指定 --url 参数")
                print(f"尝试查找的路径: '{url_file_path}' 和 '{os.path.abspath(args.url_file)}'")
                return 1

        # 更新URL文件路径
        args.url_file = url_file_path
        print(f"使用URL文件: {args.url_file}")

    # 设置默认的输出CSV文件名（如果未指定）
    if not args.output_csv:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        # 获取脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # 在脚本所在目录创建输出文件
        args.output_csv = os.path.join(script_dir, f"waf_results_{timestamp}.csv")
        print(f"未指定输出文件，将使用默认文件名: {args.output_csv}")
    elif not os.path.isabs(args.output_csv):
        # 如果输出文件是相对路径，则相对于脚本所在目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        args.output_csv = os.path.join(script_dir, args.output_csv)
        print(f"输出文件路径: {args.output_csv}")

    # 设置全局变量
    global output_csv_path
    output_csv_path = args.output_csv

    # 初始化WAF识别器
    identifier = WAFIdentifier(payload_dir=args.payload_dir, csv_path=args.csv_path)

    # 使用指定的载荷数量
    payload_count = args.num_payloads

    # 准备URL列表
    urls = []
    if args.url:
        urls.append(args.url)
    else:  # 使用URL文件（已在前面检查过文件是否存在）
        try:
            with open(args.url_file, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]

            if not urls:
                print(f"警告: URL文件 '{args.url_file}' 为空或格式不正确")
                return 1

            print(f"从文件 '{args.url_file}' 加载了 {len(urls)} 个URL")
        except Exception as e:
            print(f"加载URL文件时出错: {e}")
            print(f"请确保文件 '{args.url_file}' 存在且可读")
            return 1

    # 使用全局结果列表
    global results
    results = []

    # 识别WAF
    try:
        for i, url in enumerate(urls):
            # 检查是否被中断
            if interrupted:
                break

            print(f"\n[{i+1}/{len(urls)}] 正在识别 {url}")

            waf_name, confidence, details, redirected_to_https, final_url = identifier.identify_waf(
                waf_url=url,
                num_payloads=payload_count,
                timeout=args.timeout,
                verify_ssl=args.verify_ssl,  # 如果指定了--verify-ssl，则验证SSL证书
                verbose=not args.quiet,
                balance_payloads=args.balance,  # 如果指定了--balance，则平衡载荷数量
                follow_redirects=not args.no_follow_redirects  # 如果指定了--no-follow-redirects，则不自动跟随重定向
            )

            # 打印结果（即使在静默模式下也打印）
            print(f"\n识别结果: {waf_name}")
            if waf_name == "Connection Timeout":
                print("连接超时次数过多，跳过当前URL")
                confidence_str = "N/A"
            elif waf_name == "未知waf":
                print("所有WAF的匹配率都低于50%，无法确定WAF类型")
                confidence_str = "N/A"
            else:
                print(f"置信度: {confidence:.4f}")
                confidence_str = f"{confidence:.4f}"

            # 如果发生了HTTP到HTTPS的重定向，更新URL
            result_url = url
            if redirected_to_https and final_url:
                result_url = final_url
                print(f"注意: 由于HTTP到HTTPS的重定向，结果中的URL已更新为: {final_url}")

            # 记录结果
            result = {
                'url': result_url,  # 使用可能更新后的URL
                'original_url': url,  # 保留原始URL
                'waf_name': waf_name,
                'confidence': confidence_str,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'redirected': 'Yes' if redirected_to_https else 'No',  # 记录是否发生了重定向
                'status': 'Timeout' if waf_name == "Connection Timeout" else 'Success'  # 记录URL状态
            }

            # 添加每个WAF的匹配率（如果不是超时）
            if waf_name != "Connection Timeout":
                for waf, waf_details in details.items():
                    result[f'{waf}_match_rate'] = f"{waf_details['match_rate']:.4f}"
                    result[f'{waf}_matched'] = waf_details['matched_count']
                    result[f'{waf}_tested'] = waf_details['tested_count']

            results.append(result)

        # 保存结果
        if results:
            save_results()

        # 返回成功状态码
        return 0

    except SSLError as e:
        print(f"SSL证书验证错误: {e}")
        print("提示: 默认不验证SSL证书，如果您使用了 --verify-ssl 选项，可以移除该选项")
        # 返回错误状态码
        return 1
    except ConnectionError as e:
        print(f"连接错误: {e}")
        print("请检查URL是否正确，以及目标服务器是否可访问")
        # 返回错误状态码
        return 1
    except Exception as e:
        print(f"错误: {e}")
        # 返回错误状态码
        return 1

if __name__ == "__main__":
    sys.exit(main())