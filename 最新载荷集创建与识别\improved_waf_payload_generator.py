# -*- coding: utf-8 -*-
"""
改进的WAF载荷集生成脚本
作用：基于analyze_waf_responses.py的分析结果，生成针对不同WAF的特异性载荷集
支持多状态码拦截模式，解决阿里云WAF使用405、华为云WAF使用418等问题
注意：忽略CLOUDFLARE_FREE，不用于载荷集构建
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import argparse
import os
from datetime import datetime
import json

class ImprovedWAFPayloadGenerator:
    def __init__(self, csv_file, analysis_results=None):
        """
        初始化改进的WAF载荷集生成器

        参数:
        - csv_file: WAF响应数据CSV文件路径
        - analysis_results: analyze_waf_responses.py的分析结果（可选）
        """
        self.csv_file = csv_file
        self.df = pd.read_csv(csv_file, low_memory=False)

        # 获取WAF列名（忽略payload和CLOUDFLARE_FREE）
        self.waf_columns = [col for col in self.df.columns
                           if col != 'payload' and col != 'CLOUDFLARE_FREE']

        # 清理数据
        for col in self.waf_columns:
            self.df[col] = self.df[col].astype(str)

        print(f"已加载 {len(self.df)} 个载荷的响应数据")
        print(f"检测到的WAF: {', '.join(self.waf_columns)}")
        print("注意: 已忽略CLOUDFLARE_FREE，不用于载荷集构建")

        # 如果提供了分析结果，使用检测到的拦截模式
        if analysis_results and 'detected_patterns' in analysis_results:
            self.waf_block_codes = analysis_results['detected_patterns']
            print("使用分析结果中检测到的拦截模式")
        else:
            # 使用默认的拦截状态码模式
            self.waf_block_codes = self._get_default_block_patterns()
            print("使用默认的拦截状态码模式")

        # 显示使用的拦截模式
        print("\n使用的WAF拦截状态码模式:")
        for waf, codes in self.waf_block_codes.items():
            print(f"  {waf}: {codes}")

    def _get_default_block_patterns(self):
        """获取默认的WAF拦截状态码模式（忽略CLOUDFLARE_FREE）"""
        return {
            'ALIYUN': [405, 403, 406, 444],           # 阿里云WAF - 主要使用405
            'HUAWEI': [418, 403, 406, 444],           # 华为云WAF - 主要使用418
            'TENCENT': [403, 406, 444],               # 腾讯云WAF
            'BAIDU': [403, 406, 444],                 # 百度云WAF
            'CLOUDFLARE': [403, 429, 503, 520, 521, 522, 523, 524],
            'MODSECURITY': [403, 406, 501, 502, 503],
            'NGX_LUA': [403, 444, 499],
            'SAFELINE': [403, 406],
            'NAXSI': [403, 406, 418],
        }

    def is_blocked(self, status_code, waf_name):
        """
        判断给定状态码是否表示被WAF拦截

        参数:
        - status_code: HTTP状态码
        - waf_name: WAF名称

        返回:
        - bool: True表示被拦截，False表示未拦截
        """
        if pd.isna(status_code) or status_code in ['nan', 'Timeout', 'Error']:
            return False

        try:
            code = int(float(status_code))
            block_codes = self.waf_block_codes.get(waf_name, [403])  # 默认使用403
            return code in block_codes
        except (ValueError, TypeError):
            return False

    def calculate_payload_specificity(self, target_waf, payload_row):
        """
        计算载荷对目标WAF的特异性

        参数:
        - target_waf: 目标WAF名称
        - payload_row: 载荷的响应数据行

        返回:
        - dict: 包含特异性信息的字典
        """
        target_status = payload_row[target_waf]
        target_blocked = self.is_blocked(target_status, target_waf)

        # 如果目标WAF没有拦截，特异性为0
        if not target_blocked:
            return {
                'specificity': 0.0,
                'target_blocked': False,
                'other_blocked_count': 0,
                'other_total_count': 0,
                'target_status': target_status
            }

        # 计算其他WAF的拦截情况
        other_blocked = 0
        other_total = 0

        for other_waf in self.waf_columns:
            if other_waf != target_waf:
                other_status = payload_row[other_waf]
                if pd.notna(other_status) and other_status not in ['nan', 'Timeout', 'Error']:
                    other_total += 1
                    if self.is_blocked(other_status, other_waf):
                        other_blocked += 1

        # 计算特异性：目标WAF拦截，其他WAF不拦截的比例
        specificity = 1 - (other_blocked / other_total) if other_total > 0 else 0

        return {
            'specificity': specificity,
            'target_blocked': True,
            'other_blocked_count': other_blocked,
            'other_total_count': other_total,
            'target_status': target_status
        }

    def generate_specific_payloads(self, min_specificity=0.7, max_payloads_per_waf=100):
        """
        生成每个WAF的特异性载荷集

        参数:
        - min_specificity: 最小特异性阈值
        - max_payloads_per_waf: 每个WAF最大载荷数量

        返回:
        - dict: 每个WAF的特异性载荷字典
        """
        print(f"\n=== 生成特异性载荷集 ===")
        print(f"最小特异性阈值: {min_specificity}")
        print(f"每个WAF最大载荷数: {max_payloads_per_waf}")

        specific_payloads = {}

        for target_waf in self.waf_columns:
            print(f"\n处理 {target_waf}...")

            waf_payloads = []

            for idx, row in self.df.iterrows():
                payload = row['payload']
                specificity_info = self.calculate_payload_specificity(target_waf, row)

                if specificity_info['specificity'] >= min_specificity:
                    waf_payloads.append({
                        'payload': payload,
                        'specificity': specificity_info['specificity'],
                        'target_status': specificity_info['target_status'],
                        'other_blocked_rate': specificity_info['other_blocked_count'] / specificity_info['other_total_count'] if specificity_info['other_total_count'] > 0 else 0
                    })

            # 按特异性排序，取前N个
            waf_payloads.sort(key=lambda x: x['specificity'], reverse=True)
            waf_payloads = waf_payloads[:max_payloads_per_waf]

            specific_payloads[target_waf] = waf_payloads

            print(f"  找到 {len(waf_payloads)} 个特异性载荷")
            if waf_payloads:
                avg_specificity = np.mean([p['specificity'] for p in waf_payloads])
                print(f"  平均特异性: {avg_specificity:.3f}")

        return specific_payloads

    def generate_balanced_payloads(self, target_block_rate=0.5, max_payloads_per_waf=200):
        """
        生成平衡的载荷集，确保每个WAF有合适的拦截率

        参数:
        - target_block_rate: 目标拦截率
        - max_payloads_per_waf: 每个WAF最大载荷数

        返回:
        - dict: 每个WAF的平衡载荷集
        """
        print(f"\n=== 生成平衡载荷集 ===")
        print(f"目标拦截率: {target_block_rate}")

        balanced_payloads = {}

        for target_waf in self.waf_columns:
            print(f"\n处理 {target_waf}...")

            blocked_payloads = []
            unblocked_payloads = []

            for idx, row in self.df.iterrows():
                payload = row['payload']
                target_status = row[target_waf]
                is_blocked = self.is_blocked(target_status, target_waf)

                payload_info = {
                    'payload': payload,
                    'status': target_status,
                    'blocked': is_blocked
                }

                if is_blocked:
                    blocked_payloads.append(payload_info)
                else:
                    unblocked_payloads.append(payload_info)

            # 计算需要的载荷数量
            target_blocked_count = int(max_payloads_per_waf * target_block_rate)
            target_unblocked_count = max_payloads_per_waf - target_blocked_count

            # 随机选择载荷
            import random
            selected_blocked = random.sample(blocked_payloads, min(target_blocked_count, len(blocked_payloads)))
            selected_unblocked = random.sample(unblocked_payloads, min(target_unblocked_count, len(unblocked_payloads)))

            balanced_payloads[target_waf] = {
                'blocked': selected_blocked,
                'unblocked': selected_unblocked,
                'total': len(selected_blocked) + len(selected_unblocked),
                'actual_block_rate': len(selected_blocked) / (len(selected_blocked) + len(selected_unblocked)) if (len(selected_blocked) + len(selected_unblocked)) > 0 else 0
            }

            print(f"  选择了 {len(selected_blocked)} 个拦截载荷, {len(selected_unblocked)} 个通过载荷")
            print(f"  实际拦截率: {balanced_payloads[target_waf]['actual_block_rate']:.1%}")

        return balanced_payloads

    def save_payload_sets(self, specific_payloads, balanced_payloads, output_dir):
        """保存载荷集到文件，生成与identify_payloads相同格式的CSV文件"""
        print(f"\n=== 保存载荷集 ===")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存特异性载荷集为CSV格式（与identify_payloads格式相同）
        specific_dir = os.path.join(output_dir, f"identify_payloads_{timestamp}")
        os.makedirs(specific_dir, exist_ok=True)

        for target_waf, payloads in specific_payloads.items():
            if payloads:
                csv_file_path = os.path.join(specific_dir, f"{target_waf}_specific_payloads.csv")

                with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    # 写入CSV头部
                    header = ['payload', 'specificity']
                    # 添加所有WAF的期望响应列
                    for waf in self.waf_columns:
                        header.append(f"{waf}_expected")

                    csvfile.write(','.join(header) + '\n')

                    # 写入载荷数据
                    for item in payloads:
                        row = [item['payload'], str(item['specificity'])]

                        # 为每个WAF添加期望响应
                        for waf in self.waf_columns:
                            if waf == target_waf:
                                row.append('拦截')
                            else:
                                # 检查该载荷在其他WAF中的实际响应
                                payload_row = self.df[self.df['payload'] == item['payload']]
                                if not payload_row.empty:
                                    other_status = payload_row.iloc[0][waf]
                                    if self.is_blocked(other_status, waf):
                                        row.append('拦截')
                                    else:
                                        row.append('通过')
                                else:
                                    row.append('通过')  # 默认为通过

                        csvfile.write(','.join(row) + '\n')

                print(f"已保存 {target_waf} 的 {len(payloads)} 个特异性载荷到 {csv_file_path}")

        # 保存平衡载荷集（保持原有的txt格式）
        balanced_dir = os.path.join(output_dir, f"balanced_payloads_{timestamp}")
        os.makedirs(balanced_dir, exist_ok=True)

        for waf, payload_set in balanced_payloads.items():
            # 保存拦截载荷
            blocked_file = os.path.join(balanced_dir, f"{waf}_blocked.txt")
            with open(blocked_file, 'w', encoding='utf-8') as f:
                for item in payload_set['blocked']:
                    f.write(f"{item['payload']}\n")

            # 保存通过载荷
            unblocked_file = os.path.join(balanced_dir, f"{waf}_unblocked.txt")
            with open(unblocked_file, 'w', encoding='utf-8') as f:
                for item in payload_set['unblocked']:
                    f.write(f"{item['payload']}\n")

            print(f"已保存 {waf} 的平衡载荷集: {len(payload_set['blocked'])} 拦截 + {len(payload_set['unblocked'])} 通过")

        # 保存配置信息
        config = {
            'waf_block_codes': self.waf_block_codes,
            'generation_time': datetime.now().isoformat(),
            'source_file': self.csv_file,
            'total_payloads': len(self.df),
            'waf_count': len(self.waf_columns),
            'excluded_wafs': ['CLOUDFLARE_FREE']  # 记录被排除的WAF
        }

        config_file = os.path.join(output_dir, f"payload_generation_config_{timestamp}.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        print(f"已保存配置信息到 {config_file}")

def main():
    parser = argparse.ArgumentParser(description="改进的WAF载荷集生成工具")
    parser.add_argument("--csv-file", required=True, help="WAF响应数据CSV文件路径")
    parser.add_argument("--analysis-file", help="分析结果JSON文件路径（可选）")
    parser.add_argument("--output-dir", default="improved_payloads", help="输出目录")
    parser.add_argument("--min-specificity", type=float, default=0.7, help="最小特异性阈值")
    parser.add_argument("--max-specific-payloads", type=int, default=100, help="每个WAF最大特异性载荷数")
    parser.add_argument("--max-balanced-payloads", type=int, default=200, help="每个WAF最大平衡载荷数")
    parser.add_argument("--target-block-rate", type=float, default=0.5, help="目标拦截率")

    args = parser.parse_args()

    # 加载分析结果（如果提供）
    analysis_results = None
    if args.analysis_file and os.path.exists(args.analysis_file):
        with open(args.analysis_file, 'r', encoding='utf-8') as f:
            analysis_results = json.load(f)

    # 创建载荷生成器
    generator = ImprovedWAFPayloadGenerator(args.csv_file, analysis_results)

    # 生成特异性载荷集
    specific_payloads = generator.generate_specific_payloads(
        min_specificity=args.min_specificity,
        max_payloads_per_waf=args.max_specific_payloads
    )

    # 生成平衡载荷集
    balanced_payloads = generator.generate_balanced_payloads(
        target_block_rate=args.target_block_rate,
        max_payloads_per_waf=args.max_balanced_payloads
    )

    # 保存载荷集
    generator.save_payload_sets(specific_payloads, balanced_payloads, args.output_dir)

    print(f"\n载荷集生成完成！输出目录: {args.output_dir}")
    print("注意: 已忽略CLOUDFLARE_FREE，未包含在载荷集中")

if __name__ == "__main__":
    main()
