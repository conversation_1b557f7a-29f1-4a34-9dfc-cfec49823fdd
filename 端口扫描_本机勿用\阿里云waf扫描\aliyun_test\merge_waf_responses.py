# -*- coding: utf-8 -*-
"""
合并两个payload_responses.csv文件，使不同WAF对同一载荷的响应在同一行
"""

import os
import pandas as pd
from datetime import datetime

def merge_waf_responses(file1, file2, output_file):
    """
    合并两个WAF响应CSV文件
    
    参数:
    - file1: 第一个CSV文件路径
    - file2: 第二个CSV文件路径
    - output_file: 输出CSV文件路径
    """
    print(f"正在读取第一个文件: {file1}")
    try:
        df1 = pd.read_csv(file1)
        print(f"第一个文件包含 {len(df1)} 行数据和 {len(df1.columns)} 列")
        print(f"列名: {', '.join(df1.columns)}")
    except Exception as e:
        print(f"读取第一个文件时出错: {e}")
        return
    
    print(f"正在读取第二个文件: {file2}")
    try:
        df2 = pd.read_csv(file2)
        print(f"第二个文件包含 {len(df2)} 行数据和 {len(df2.columns)} 列")
        print(f"列名: {', '.join(df2.columns)}")
    except Exception as e:
        print(f"读取第二个文件时出错: {e}")
        return
    
    # 合并两个DataFrame，使用payload列作为键
    print("正在合并两个文件...")
    merged_df = pd.merge(df1, df2, on='payload', how='outer')
    
    # 统计合并结果
    total_payloads = len(merged_df)
    common_payloads = len(pd.merge(df1, df2, on='payload', how='inner'))
    only_in_file1 = len(df1) - common_payloads
    only_in_file2 = len(df2) - common_payloads
    
    print(f"合并结果:")
    print(f"- 总载荷数: {total_payloads}")
    print(f"- 两个文件共有的载荷数: {common_payloads}")
    print(f"- 仅在第一个文件中的载荷数: {only_in_file1}")
    print(f"- 仅在第二个文件中的载荷数: {only_in_file2}")
    print(f"- 合并后的列名: {', '.join(merged_df.columns)}")
    
    # 保存合并后的DataFrame
    print(f"正在保存合并结果到: {output_file}")
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        merged_df.to_csv(output_file, index=False)
        print(f"合并完成，结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存合并结果时出错: {e}")

def main():
    # 指定文件路径
    file1 = r"C:\Users\<USER>\Desktop\aliyun_test\merged_payload_responses_20250523_160236.csv"
    file2 = r"C:\Users\<USER>\Desktop\aliyun_test\payload_responses.csv"
    
    # 生成输出文件路径
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = r"C:\Users\<USER>\Desktop\aliyun_test"
    output_file = os.path.join(output_dir, f"merged_payload_responses_{timestamp}.csv")
    
    # 合并文件
    merge_waf_responses(file1, file2, output_file)

if __name__ == "__main__":
    main()
