# -*- coding: utf-8 -*-
"""
脚本用于合并两个payload_responses.csv文件，将相同载荷在不同WAF下的响应整理到同一行。
"""

import os
import pandas as pd
import argparse
from datetime import datetime

def merge_csv_files(file1, file2, output_file):
    """
    合并两个CSV文件，将相同载荷的响应整理到同一行
    
    参数:
    - file1: 第一个CSV文件路径
    - file2: 第二个CSV文件路径
    - output_file: 输出CSV文件路径
    
    返回:
    - 合并后的DataFrame
    """
    print(f"正在读取第一个文件: {file1}")
    df1 = pd.read_csv(file1)
    print(f"第一个文件包含 {len(df1)} 行数据和 {len(df1.columns)} 列")
    
    print(f"正在读取第二个文件: {file2}")
    df2 = pd.read_csv(file2)
    print(f"第二个文件包含 {len(df2)} 行数据和 {len(df2.columns)} 列")
    
    # 获取两个文件的列名（除了payload列）
    cols1 = [col for col in df1.columns if col != 'payload']
    cols2 = [col for col in df2.columns if col != 'payload']
    
    # 检查是否有重复的WAF列名
    duplicate_cols = set(cols1) & set(cols2)
    if duplicate_cols:
        print(f"警告: 两个文件中存在相同的WAF列名: {duplicate_cols}")
        print("将为第二个文件中的重复列添加后缀'_2'")
        
        # 为第二个文件中的重复列添加后缀
        rename_dict = {col: f"{col}_2" for col in duplicate_cols}
        df2 = df2.rename(columns=rename_dict)
        
        # 更新列名列表
        cols2 = [rename_dict.get(col, col) for col in cols2]
    
    # 合并两个DataFrame，使用payload列作为键
    print("正在合并两个文件...")
    merged_df = pd.merge(df1, df2, on='payload', how='outer')
    
    # 统计合并结果
    total_payloads = len(merged_df)
    common_payloads = len(pd.merge(df1, df2, on='payload', how='inner'))
    only_in_file1 = len(df1) - common_payloads
    only_in_file2 = len(df2) - common_payloads
    
    print(f"合并结果:")
    print(f"- 总载荷数: {total_payloads}")
    print(f"- 两个文件共有的载荷数: {common_payloads}")
    print(f"- 仅在第一个文件中的载荷数: {only_in_file1}")
    print(f"- 仅在第二个文件中的载荷数: {only_in_file2}")
    
    # 保存合并后的DataFrame
    print(f"正在保存合并结果到: {output_file}")
    merged_df.to_csv(output_file, index=False)
    
    return merged_df

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="合并两个payload_responses.csv文件")
    parser.add_argument("--file1", required=True, help="第一个CSV文件路径")
    parser.add_argument("--file2", required=True, help="第二个CSV文件路径")
    parser.add_argument("--output", help="输出CSV文件路径 (默认: merged_payload_responses_YYYYMMDD_HHMMSS.csv)")
    
    args = parser.parse_args()
    
    # 如果未指定输出文件，使用默认名称
    if not args.output:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        args.output = f"merged_payload_responses_{timestamp}.csv"
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    # 合并CSV文件
    merge_csv_files(args.file1, args.file2, args.output)
    
    print(f"合并完成，结果已保存到: {args.output}")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except Exception as e:
        print(f"错误: {e}")
        exit(1)
