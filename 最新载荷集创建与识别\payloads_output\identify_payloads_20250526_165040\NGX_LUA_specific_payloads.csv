"payload","specificity","MODSECURITY_expected","NGX_LUA_expected","SAFELINE_expected","NAXSI_expected","ALIYUN_expected"
"0 ) [blank] union [blank] all [blank] select [blank] 0 #","0.75","通过","拦截","通过","拦截","通过"
""" ) [blank] union [blank] all [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
"0 ) [BlANk] UNioN [BlAnK] DIStINcT [blANK] SELecT [bLank] 0 -- [bLanK]","0.75","通过","拦截","通过","拦截","通过"
"' ) [blank] union [blank] all [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] all [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
"' ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] all [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] all [blank] select [blank] 0 -- [blank]","0.75","通过","拦截","通过","拦截","通过"
""" ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
""" ) [blank] union [blank] all [blank] ( select [blank] 0 ) #","0.75","通过","拦截","通过","拦截","通过"
"' ) [blank] union [blank] all [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] distinct [blank] select [blank] 0 -- [blank]","0.75","通过","拦截","通过","拦截","通过"
"0 ) [blank] union [blank] distinct [blank] select [blank] 0 #","0.75","通过","拦截","通过","拦截","通过"
"' ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
""" ) [blank] union [blank] distinct [blank] ( select [blank] 0 ) -- [blank]","0.75","通过","拦截","通过","拦截","通过"
