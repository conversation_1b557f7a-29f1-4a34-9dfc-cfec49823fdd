import pandas as pd
import numpy as np
import os
import requests
from collections import defaultdict
import time
import random
from tqdm import tqdm

class WAFIdentifier:
    def __init__(self, csv_path, specificity_threshold=0.5):
        """
        初始化WAF识别器
        
        参数:
        - csv_path: 载荷响应CSV文件路径
        - specificity_threshold: 特异性阈值，默认0.5
        """
        self.csv_path = csv_path
        self.specificity_threshold = specificity_threshold
        self.waf_names = []
        self.payloads_df = None
        self.specific_payloads = {}
        self.expected_behaviors = {}  # 预期行为矩阵
        self.load_data()
        
    def load_data(self):
        """加载CSV数据"""
        self.payloads_df = pd.read_csv(self.csv_path)
        # 获取WAF名称（除了payload列）
        self.waf_names = [col for col in self.payloads_df.columns if col != 'payload']
        print(f"已加载 {len(self.payloads_df)} 个载荷，WAF列表: {self.waf_names}")
        
        # 构建预期行为矩阵
        self.build_expected_behaviors()
        
    def is_blocked(self, status_code):
        """判断状态码是否表示拦截"""
        # 通常200表示通过，其他状态码表示拦截
        return status_code != 200
    
    def build_expected_behaviors(self):
        """构建预期行为矩阵"""
        # 初始化预期行为矩阵
        self.expected_behaviors = {}
        
        # 对于每个载荷，记录每个WAF的实际行为
        for _, row in self.payloads_df.iterrows():
            payload = row['payload']
            self.expected_behaviors[payload] = {}
            
            for waf in self.waf_names:
                self.expected_behaviors[payload][waf] = self.is_blocked(row[waf])
    
    def calculate_specificity(self, row, target_waf):
        """
        计算载荷对目标WAF的特异性
        
        参数:
        - row: DataFrame的一行，包含载荷和各WAF的响应
        - target_waf: 目标WAF名称
        
        返回:
        - 特异性值，范围[0,1]
        """
        # 检查目标WAF是否拦截载荷
        if not self.is_blocked(row[target_waf]):
            return 0.0
        
        # 其他WAF列表
        other_wafs = [waf for waf in self.waf_names if waf != target_waf]
        
        # 计算其他WAF通过载荷的比例
        pass_count = sum(1 for waf in other_wafs if not self.is_blocked(row[waf]))
        specificity = pass_count / len(other_wafs)
        
        return specificity
    
    def generate_specific_payloads(self):
        """为每个WAF生成特异性载荷集"""
        for target_waf in self.waf_names:
            print(f"为 {target_waf} 生成特异性载荷...")
            
            # 计算每个载荷对目标WAF的特异性
            specificities = []
            for _, row in self.payloads_df.iterrows():
                specificity = self.calculate_specificity(row, target_waf)
                if specificity >= self.specificity_threshold:
                    specificities.append({
                        'payload': row['payload'],
                        'specificity': specificity,
                        # 存储该载荷对所有WAF的预期行为
                        'expected_behaviors': {waf: self.is_blocked(row[waf]) for waf in self.waf_names}
                    })
            
            # 按特异性降序排序
            specificities.sort(key=lambda x: x['specificity'], reverse=True)
            
            # 存储特异性载荷
            self.specific_payloads[target_waf] = specificities
            
            print(f"已为 {target_waf} 找到 {len(specificities)} 个特异性载荷")
            
            # 打印前5个特异性载荷示例
            if specificities:
                print("前5个特异性载荷示例:")
                for i, item in enumerate(specificities[:5]):
                    print(f"  {i+1}. 载荷: {item['payload'][:50]}... 特异性: {item['specificity']:.2f}")
                    # 打印预期行为
                    behaviors = []
                    for waf, is_blocked in item['expected_behaviors'].items():
                        behaviors.append(f"{waf}: {'拦截' if is_blocked else '通过'}")
                    print(f"     预期行为: {', '.join(behaviors)}")
    
    def save_specific_payloads(self, output_dir="identify_payloads"):
        """保存特异性载荷到文件"""
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 为每个WAF保存特异性载荷
        for waf_name, payloads in self.specific_payloads.items():
            if not payloads:
                continue
                
            output_path = os.path.join(output_dir, f"{waf_name}_specific_payloads.csv")
            
            # 创建DataFrame
            data = []
            for item in payloads:
                row = {
                    'payload': item['payload'],
                    'specificity': item['specificity']
                }
                # 添加预期行为列
                for waf, is_blocked in item['expected_behaviors'].items():
                    row[f"{waf}_expected"] = "拦截" if is_blocked else "通过"
                data.append(row)
            
            df = pd.DataFrame(data)
            df.to_csv(output_path, index=False)
            
            print(f"已将 {waf_name} 的 {len(payloads)} 个特异性载荷保存到 {output_path}")
    
    def identify_waf_with_behavior_matrix(self, unknown_waf_responses, debug=False):
        """
        使用预期行为矩阵识别未知WAF
        
        参数:
        - unknown_waf_responses: 字典，键为载荷，值为响应状态码
        - debug: 是否打印详细调试信息
        
        返回:
        - 识别结果和置信度，以及详细的匹配信息（如果debug=True）
        """
        if not self.specific_payloads:
            raise ValueError("请先生成特异性载荷")
        
        # 初始化匹配分数矩阵
        # 行：测试的载荷
        # 列：候选WAF
        match_scores = {waf: 0 for waf in self.waf_names}
        total_weights = {waf: 0 for waf in self.waf_names}
        
        # 详细匹配信息
        detailed_matches = {waf: {
            'total_payloads': 0,
            'tested_payloads': 0,
            'matched_payloads': 0,
            'total_weight': 0,
            'matched_weight': 0,
            'payload_details': []
        } for waf in self.waf_names}
        
        # 收集所有特异性载荷
        all_specific_payloads = []
        for waf, payloads in self.specific_payloads.items():
            for item in payloads:
                all_specific_payloads.append({
                    'payload': item['payload'],
                    'specificity': item['specificity'],
                    'source_waf': waf,
                    'expected_behaviors': item['expected_behaviors']
                })
        
        # 对每个特异性载荷进行测试
        for item in all_specific_payloads:
            payload = item['payload']
            specificity = item['specificity']
            source_waf = item['source_waf']
            expected_behaviors = item['expected_behaviors']
            
            # 如果载荷在未知WAF响应中
            if payload in unknown_waf_responses:
                # 检查未知WAF是否拦截该载荷
                is_blocked_by_unknown = self.is_blocked(unknown_waf_responses[payload])
                
                # 对每个候选WAF计算匹配分数
                for candidate_waf in self.waf_names:
                    # 获取候选WAF对该载荷的预期行为
                    expected_behavior = expected_behaviors[candidate_waf]
                    
                    # 更新详细信息
                    detailed_matches[candidate_waf]['total_payloads'] += 1
                    detailed_matches[candidate_waf]['tested_payloads'] += 1
                    detailed_matches[candidate_waf]['total_weight'] += specificity
                    
                    # 记录载荷详情
                    payload_detail = {
                        'payload': payload[:50] + "..." if len(payload) > 50 else payload,
                        'specificity': specificity,
                        'source_waf': source_waf,
                        'unknown_response': unknown_waf_responses[payload],
                        'is_blocked_by_unknown': is_blocked_by_unknown,
                        'expected_behavior': expected_behavior,
                        'match': is_blocked_by_unknown == expected_behavior
                    }
                    detailed_matches[candidate_waf]['payload_details'].append(payload_detail)
                    
                    # 如果行为匹配，增加匹配分数
                    if is_blocked_by_unknown == expected_behavior:
                        match_scores[candidate_waf] += specificity
                        detailed_matches[candidate_waf]['matched_weight'] += specificity
                        detailed_matches[candidate_waf]['matched_payloads'] += 1
                    
                    total_weights[candidate_waf] += specificity
        
        # 计算加权匹配度
        weighted_matches = {}
        for waf in self.waf_names:
            if total_weights[waf] > 0:
                weighted_matches[waf] = match_scores[waf] / total_weights[waf]
            else:
                weighted_matches[waf] = 0
        
        # 找出匹配度最高的WAF
        if not weighted_matches:
            return "Unknown", 0, detailed_matches if debug else None
            
        best_match = max(weighted_matches.items(), key=lambda x: x[1])
        waf_name, confidence = best_match
        
        return waf_name, confidence, detailed_matches if debug else None
    
    def test_identification(self, verbose=True):
        """
        测试WAF识别准确性
        
        参数:
        - verbose: 是否打印详细信息
        
        返回:
        - 识别准确率和详细结果
        """
        # 使用原始数据进行交叉验证
        correct = 0
        total = len(self.waf_names)
        detailed_results = []
        
        for true_waf in self.waf_names:
            if verbose:
                print(f"\n{'='*50}")
                print(f"测试识别 {true_waf}...")
            
            # 构建模拟未知WAF的响应
            unknown_responses = {}
            for _, row in self.payloads_df.iterrows():
                unknown_responses[row['payload']] = row[true_waf]
            
            # 使用预期行为矩阵识别WAF
            identified_waf, confidence, detailed_matches = self.identify_waf_with_behavior_matrix(unknown_responses, debug=True)
            
            # 记录结果
            result = {
                'true_waf': true_waf,
                'identified_waf': identified_waf,
                'confidence': confidence,
                'is_correct': identified_waf == true_waf,
                'detailed_matches': detailed_matches
            }
            detailed_results.append(result)
            
            if verbose:
                print(f"  真实WAF: {true_waf}")
                print(f"  识别结果: {identified_waf}")
                print(f"  置信度: {confidence:.4f}")
                
                if identified_waf == true_waf:
                    correct += 1
                    print("  ✓ 识别正确")
                else:
                    print("  ✗ 识别错误")
                    
                    # 打印所有WAF的匹配率，便于比较
                    print("\n  所有WAF的匹配率:")
                    for waf, match_info in detailed_matches.items():
                        if match_info['total_weight'] > 0:
                            match_rate = match_info['matched_weight'] / match_info['total_weight']
                            print(f"    {waf}: {match_rate:.4f} ({match_info['matched_payloads']}/{match_info['tested_payloads']} 载荷匹配)")
                        else:
                            print(f"    {waf}: 0.0000 (0/0 载荷匹配)")
                    
                    # 分析错误原因
                    print("\n  错误分析:")
                    true_waf_info = detailed_matches[true_waf]
                    identified_waf_info = detailed_matches[identified_waf]
                    
                    # 比较真实WAF和识别结果WAF的匹配情况
                    print(f"  真实WAF ({true_waf}) 匹配率: {true_waf_info['matched_weight']/true_waf_info['total_weight']:.4f}")
                    print(f"  识别结果WAF ({identified_waf}) 匹配率: {identified_waf_info['matched_weight']/identified_waf_info['total_weight']:.4f}")
                    
                    # 分析不匹配的载荷
                    print("\n  部分不匹配的载荷详情 (真实WAF应匹配但未匹配):")
                    mismatch_count = 0
                    for detail in true_waf_info['payload_details']:
                        if not detail['match'] and detail['source_waf'] == true_waf:
                            print(f"    载荷: {detail['payload']}")
                            print(f"    来源WAF: {detail['source_waf']}")
                            print(f"    特异性: {detail['specificity']:.4f}")
                            print(f"    未知WAF响应: {detail['unknown_response']} ({'拦截' if detail['is_blocked_by_unknown'] else '通过'})")
                            print(f"    预期行为: {'拦截' if detail['expected_behavior'] else '通过'}")
                            print()
                            mismatch_count += 1
                            if mismatch_count >= 3:  # 只显示前3个不匹配的载荷
                                break
            else:
                if identified_waf == true_waf:
                    correct += 1
        
        accuracy = correct / total
        if verbose:
            print(f"\n{'='*50}")
            print(f"总体准确率: {accuracy:.2%} ({correct}/{total})")
        
        return accuracy, detailed_results
    
    def identify_real_waf(self, waf_url, test_payloads=None, timeout=5):
        """
        识别真实WAF
        
        参数:
        - waf_url: WAF的URL
        - test_payloads: 测试载荷列表，如果为None则从所有特异性载荷中随机选择
        - timeout: 请求超时时间（秒）
        
        返回:
        - 识别结果和置信度
        """
        if not self.specific_payloads:
            raise ValueError("请先生成特异性载荷")
        
        # 如果未提供测试载荷，从所有特异性载荷中选择
        if test_payloads is None:
            # 从每个WAF的特异性载荷中选择一些
            test_payloads = []
            for waf_name, payloads in self.specific_payloads.items():
                if payloads:
                    # 选择前10个或所有特异性载荷（取较小值）
                    num_to_select = min(10, len(payloads))
                    selected = payloads[:num_to_select]
                    test_payloads.extend(selected)
            
            # 随机打乱
            random.shuffle(test_payloads)
            
            # 限制测试载荷数量
            max_payloads = 50
            if len(test_payloads) > max_payloads:
                test_payloads = test_payloads[:max_payloads]
        
        # 测试载荷并收集响应
        unknown_responses = {}
        session = requests.Session()
        
        print(f"正在测试 {len(test_payloads)} 个载荷...")
        
        for item in tqdm(test_payloads):
            payload = item['payload'] if isinstance(item, dict) else item
            
            try:
                # 发送请求
                # 注意：这里假设WAF接受GET请求，参数名为"title"
                # 根据实际情况调整请求方式和参数
                response = session.get(
                    waf_url, 
                    params={"title": payload, "action": "search"}, 
                    timeout=timeout
                )
                
                # 记录状态码
                unknown_responses[payload] = response.status_code
                
                # 添加短暂延迟，避免请求过于频繁
                time.sleep(0.5)
                
            except Exception as e:
                print(f"请求失败: {e}")
                # 对于失败的请求，可以选择跳过或记录特定状态码
                # unknown_responses[payload] = 500  # 假设500表示请求失败
        
        # 使用预期行为矩阵识别WAF
        identified_waf, confidence, detailed_matches = self.identify_waf_with_behavior_matrix(unknown_responses, debug=True)
        
        print(f"识别结果: {identified_waf}")
        print(f"置信度: {confidence:.4f}")
        
        # 打印所有WAF的匹配率，便于比较
        print("\n所有WAF的匹配率:")
        for waf_name, match_info in detailed_matches.items():
            if match_info['total_weight'] > 0:
                match_rate = match_info['matched_weight'] / match_info['total_weight']
                print(f"  {waf_name}: {match_rate:.4f} ({match_info['matched_payloads']}/{match_info['tested_payloads']} 载荷匹配)")
            else:
                print(f"  {waf_name}: 0.0000 (0/0 载荷匹配)")
        
        return identified_waf, confidence

# 使用示例
if __name__ == "__main__":
    # 初始化WAF识别器
    identifier = WAFIdentifier(csv_path="D:\\anaconda3\\envs\\danuoyi1\\Lib\\site-packages\\DaNuoYi\\output\\waf-test4.29\\payload_responses.csv", specificity_threshold=0.5)
    
    # 生成特异性载荷
    identifier.generate_specific_payloads()
    
    # 保存特异性载荷
    identifier.save_specific_payloads()
    
    # 测试识别准确性（使用原始数据进行交叉验证）
    identifier.test_identification(verbose=True)
    
    # 识别真实WAF（需要提供WAF URL）
    identifier.identify_real_waf(waf_url="http://192.168.186.139/")