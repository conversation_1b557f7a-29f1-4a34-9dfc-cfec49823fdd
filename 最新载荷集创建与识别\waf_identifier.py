# -*- coding: utf-8 -*-
"""
WAF识别脚本
作用：使用improved_waf_payload_generator.py生成的载荷集来识别未知WAF
支持多状态码拦截模式
注意：忽略CLOUDFLARE_FREE，不用于WAF识别
"""

import requests
import json
import time
import argparse
from datetime import datetime
import os
from collections import defaultdict

class WAFIdentifier:
    def __init__(self, payload_config_file):
        """
        初始化WAF识别器
        
        参数:
        - payload_config_file: 载荷生成配置文件路径
        """
        # 加载配置
        with open(payload_config_file, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        self.waf_block_codes = self.config['waf_block_codes']
        
        # 确保不包含CLOUDFLARE_FREE
        if 'CLOUDFLARE_FREE' in self.waf_block_codes:
            del self.waf_block_codes['CLOUDFLARE_FREE']
        
        print(f"已加载WAF识别配置")
        print(f"支持的WAF: {', '.join(self.waf_block_codes.keys())}")
        print("注意: 已忽略CLOUDFLARE_FREE，不用于WAF识别")
    
    def is_blocked(self, status_code, waf_name):
        """判断状态码是否表示被拦截"""
        if not isinstance(status_code, int):
            return False
        
        block_codes = self.waf_block_codes.get(waf_name, [403])
        return status_code in block_codes
    
    def test_payload_set(self, target_url, payload_file, timeout=5, delay=0.5):
        """
        使用载荷集测试目标URL
        
        参数:
        - target_url: 目标URL
        - payload_file: 载荷文件路径
        - timeout: 请求超时时间
        - delay: 请求间隔
        
        返回:
        - dict: 测试结果
        """
        print(f"测试载荷文件: {payload_file}")
        
        # 读取载荷
        with open(payload_file, 'r', encoding='utf-8') as f:
            payloads = [line.strip() for line in f if line.strip()]
        
        results = []
        session = requests.Session()
        
        for i, payload in enumerate(payloads):
            try:
                response = session.get(
                    target_url,
                    params={"title": payload, "action": "search"},
                    timeout=timeout,
                    verify=False
                )
                
                results.append({
                    'payload': payload,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds()
                })
                
                if (i + 1) % 10 == 0:
                    print(f"  已测试 {i + 1}/{len(payloads)} 个载荷")
                
                time.sleep(delay)
                
            except Exception as e:
                results.append({
                    'payload': payload,
                    'status_code': None,
                    'response_time': None,
                    'error': str(e)
                })
        
        return results
    
    def calculate_waf_similarity(self, test_results, waf_name):
        """
        计算测试结果与特定WAF的相似度
        
        参数:
        - test_results: 测试结果列表
        - waf_name: WAF名称
        
        返回:
        - float: 相似度分数 (0-1)
        """
        if not test_results:
            return 0.0
        
        blocked_count = 0
        total_count = 0
        
        for result in test_results:
            status_code = result.get('status_code')
            if status_code is not None:
                total_count += 1
                if self.is_blocked(status_code, waf_name):
                    blocked_count += 1
        
        if total_count == 0:
            return 0.0
        
        # 计算拦截率
        block_rate = blocked_count / total_count
        
        # 基于已知的WAF拦截率模式来计算相似度
        # 这些期望拦截率基于您的数据分析结果
        expected_block_rates = {
            'ALIYUN': 0.68,      # 基于您的数据：68.7%
            'MODSECURITY': 0.77, # 基于您的数据：76.9%
            'NGX_LUA': 0.19,     # 基于您的数据：18.8%
            'SAFELINE': 0.05,    # 基于您的数据：4.5%
            'NAXSI': 0.99,       # 基于您的数据：99.5%
            'CLOUDFLARE': 0.1,   # 估计值
            'HUAWEI': 0.6,       # 估计值
            'TENCENT': 0.5,      # 估计值
            'BAIDU': 0.5         # 估计值
        }
        
        expected_rate = expected_block_rates.get(waf_name, 0.5)
        similarity = 1 - abs(block_rate - expected_rate)
        
        return max(0, similarity)
    
    def identify_waf(self, target_url, payload_dir, timeout=5, delay=0.5):
        """
        识别目标URL的WAF类型
        
        参数:
        - target_url: 目标URL
        - payload_dir: 载荷目录路径
        - timeout: 请求超时时间
        - delay: 请求间隔
        
        返回:
        - dict: 识别结果
        """
        print(f"\n=== 开始识别WAF ===")
        print(f"目标URL: {target_url}")
        print(f"载荷目录: {payload_dir}")
        
        waf_scores = {}
        detailed_results = {}
        
        # 测试每个WAF的特异性载荷
        for waf_name in self.waf_block_codes.keys():
            payload_file = os.path.join(payload_dir, f"{waf_name}_specific.txt")
            
            if os.path.exists(payload_file):
                print(f"\n测试 {waf_name} 特异性载荷...")
                test_results = self.test_payload_set(target_url, payload_file, timeout, delay)
                similarity = self.calculate_waf_similarity(test_results, waf_name)
                
                waf_scores[waf_name] = similarity
                detailed_results[waf_name] = test_results
                
                print(f"  相似度分数: {similarity:.3f}")
            else:
                print(f"  警告: 未找到 {waf_name} 的载荷文件")
        
        # 排序结果
        sorted_scores = sorted(waf_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 生成识别结果
        result = {
            'target_url': target_url,
            'identification_time': datetime.now().isoformat(),
            'most_likely_waf': sorted_scores[0][0] if sorted_scores else None,
            'confidence': sorted_scores[0][1] if sorted_scores else 0,
            'all_scores': dict(sorted_scores),
            'detailed_results': detailed_results,
            'excluded_wafs': ['CLOUDFLARE_FREE']  # 记录被排除的WAF
        }
        
        print(f"\n=== 识别结果 ===")
        print(f"最可能的WAF: {result['most_likely_waf']}")
        print(f"置信度: {result['confidence']:.3f}")
        print(f"所有分数: {result['all_scores']}")
        print("注意: 已忽略CLOUDFLARE_FREE")
        
        return result
    
    def save_identification_result(self, result, output_file):
        """保存识别结果"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"识别结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="WAF识别工具")
    parser.add_argument("--target-url", required=True, help="目标URL")
    parser.add_argument("--config-file", required=True, help="载荷生成配置文件路径")
    parser.add_argument("--payload-dir", required=True, help="特异性载荷目录路径")
    parser.add_argument("--output-file", help="输出文件路径")
    parser.add_argument("--timeout", type=float, default=5, help="请求超时时间")
    parser.add_argument("--delay", type=float, default=0.5, help="请求间隔")
    
    args = parser.parse_args()
    
    # 创建识别器
    identifier = WAFIdentifier(args.config_file)
    
    # 执行识别
    result = identifier.identify_waf(
        target_url=args.target_url,
        payload_dir=args.payload_dir,
        timeout=args.timeout,
        delay=args.delay
    )
    
    # 保存结果
    if args.output_file:
        identifier.save_identification_result(result, args.output_file)
    else:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"waf_identification_result_{timestamp}.json"
        identifier.save_identification_result(result, output_file)

if __name__ == "__main__":
    main()
