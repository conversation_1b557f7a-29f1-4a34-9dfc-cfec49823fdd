# -*- coding: utf-8 -*-
"""
改进的WAF识别脚本 - 支持多种状态码模式的WAF识别
解决不同WAF厂商使用不同拦截状态码的问题
"""

import pandas as pd
import numpy as np
from collections import defaultdict
import argparse
from datetime import datetime

class ImprovedWAFIdentifier:
    def __init__(self, csv_path):
        """
        初始化WAF识别器
        
        参数:
        - csv_path: 包含WAF响应数据的CSV文件路径
        """
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path)
        
        # WAF拦截状态码映射
        self.waf_block_codes = {
            'MODSECURITY': [403, 406, 501, 502, 503],
            'NGX_LUA': [403, 444, 499],
            'SAFELINE': [403, 406],
            'NAXSI': [403, 406, 418],
            'CLOUDFLARE': [403, 429, 503, 520, 521, 522, 523, 524],
            'CLOUDFLARE_FREE': [403, 429, 503, 520, 521, 522, 523, 524],
            'ALIYUN': [405, 403, 406, 444],
            'HUAWEI': [418, 403, 406, 444],
            'TENCENT': [403, 406, 444],
            'BAIDU': [403, 406, 444],
            'DEFAULT': [403, 405, 406, 418, 429, 444, 499, 501, 502, 503, 520, 521, 522, 523, 524]
        }
        
        # 获取WAF列名（除了payload列）
        self.waf_columns = [col for col in self.df.columns if col != 'payload']
        
        print(f"已加载 {len(self.df)} 个载荷的响应数据")
        print(f"检测到的WAF: {', '.join(self.waf_columns)}")
        
        # 分析每个WAF的状态码分布
        self.analyze_status_codes()
    
    def analyze_status_codes(self):
        """分析每个WAF的状态码分布"""
        print("\n=== WAF状态码分布分析 ===")
        
        for waf in self.waf_columns:
            status_counts = self.df[waf].value_counts().sort_index()
            print(f"\n{waf}:")
            for status, count in status_counts.items():
                percentage = (count / len(self.df)) * 100
                print(f"  {status}: {count} ({percentage:.1f}%)")
    
    def is_blocked(self, status_code, waf_name):
        """
        判断给定状态码是否表示被WAF拦截
        
        参数:
        - status_code: HTTP状态码
        - waf_name: WAF名称
        
        返回:
        - bool: True表示被拦截，False表示未拦截
        """
        # 处理非数值状态码（如"Timeout", "Error"等）
        if not isinstance(status_code, (int, float)) or pd.isna(status_code):
            return False
        
        status_code = int(status_code)
        
        # 获取该WAF的拦截状态码列表
        block_codes = self.waf_block_codes.get(waf_name, self.waf_block_codes['DEFAULT'])
        
        return status_code in block_codes
    
    def calculate_block_rates(self):
        """计算每个WAF的拦截率"""
        block_rates = {}
        
        for waf in self.waf_columns:
            blocked_count = 0
            total_count = 0
            
            for _, row in self.df.iterrows():
                status_code = row[waf]
                if not pd.isna(status_code) and status_code not in ['Timeout', 'Error']:
                    total_count += 1
                    if self.is_blocked(status_code, waf):
                        blocked_count += 1
            
            block_rate = blocked_count / total_count if total_count > 0 else 0
            block_rates[waf] = {
                'blocked': blocked_count,
                'total': total_count,
                'rate': block_rate
            }
        
        return block_rates
    
    def generate_waf_signatures(self):
        """生成WAF特征签名"""
        signatures = {}
        
        for waf in self.waf_columns:
            # 计算该WAF的状态码分布
            status_dist = self.df[waf].value_counts(normalize=True).to_dict()
            
            # 计算拦截率
            block_rate = sum(
                prob for status, prob in status_dist.items()
                if self.is_blocked(status, waf)
            )
            
            # 获取最常见的拦截状态码
            blocked_statuses = {}
            for status, prob in status_dist.items():
                if self.is_blocked(status, waf):
                    blocked_statuses[status] = prob
            
            signatures[waf] = {
                'block_rate': block_rate,
                'status_distribution': status_dist,
                'blocked_statuses': blocked_statuses,
                'primary_block_code': max(blocked_statuses.keys(), key=blocked_statuses.get) if blocked_statuses else None
            }
        
        return signatures
    
    def identify_waf_by_responses(self, responses):
        """
        根据响应状态码识别WAF类型
        
        参数:
        - responses: 字典，键为载荷，值为状态码
        
        返回:
        - 识别结果字典
        """
        signatures = self.generate_waf_signatures()
        scores = {}
        
        for waf_name, signature in signatures.items():
            score = 0
            total_responses = len(responses)
            
            for payload, status_code in responses.items():
                # 检查状态码是否匹配该WAF的模式
                if status_code in signature['status_distribution']:
                    # 根据状态码在该WAF中的出现频率给分
                    score += signature['status_distribution'][status_code]
                
                # 如果是拦截状态码，额外加分
                if self.is_blocked(status_code, waf_name):
                    score += 0.5
            
            # 归一化分数
            scores[waf_name] = score / total_responses if total_responses > 0 else 0
        
        # 排序并返回结果
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'most_likely': sorted_scores[0][0] if sorted_scores else None,
            'confidence': sorted_scores[0][1] if sorted_scores else 0,
            'all_scores': dict(sorted_scores)
        }
    
    def generate_specific_payloads(self, min_specificity=0.7):
        """
        生成特异性载荷集，考虑多状态码模式
        
        参数:
        - min_specificity: 最小特异性阈值
        
        返回:
        - 每个WAF的特异性载荷字典
        """
        specific_payloads = {}
        
        for target_waf in self.waf_columns:
            waf_specific = []
            
            for _, row in self.df.iterrows():
                payload = row['payload']
                target_status = row[target_waf]
                
                # 检查目标WAF是否拦截此载荷
                if not self.is_blocked(target_status, target_waf):
                    continue
                
                # 计算其他WAF的拦截情况
                other_blocked = 0
                other_total = 0
                
                for other_waf in self.waf_columns:
                    if other_waf != target_waf:
                        other_status = row[other_waf]
                        if not pd.isna(other_status) and other_status not in ['Timeout', 'Error']:
                            other_total += 1
                            if self.is_blocked(other_status, other_waf):
                                other_blocked += 1
                
                # 计算特异性
                if other_total > 0:
                    specificity = 1 - (other_blocked / other_total)
                    if specificity >= min_specificity:
                        waf_specific.append({
                            'payload': payload,
                            'specificity': specificity,
                            'target_status': target_status,
                            'other_blocked_rate': other_blocked / other_total
                        })
            
            # 按特异性排序
            waf_specific.sort(key=lambda x: x['specificity'], reverse=True)
            specific_payloads[target_waf] = waf_specific
        
        return specific_payloads
    
    def save_analysis_report(self, output_file):
        """保存分析报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("WAF识别分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本统计
            f.write(f"总载荷数: {len(self.df)}\n")
            f.write(f"检测到的WAF: {', '.join(self.waf_columns)}\n\n")
            
            # 状态码分布
            f.write("状态码分布:\n")
            f.write("-" * 30 + "\n")
            for waf in self.waf_columns:
                f.write(f"\n{waf}:\n")
                status_counts = self.df[waf].value_counts().sort_index()
                for status, count in status_counts.items():
                    percentage = (count / len(self.df)) * 100
                    f.write(f"  {status}: {count} ({percentage:.1f}%)\n")
            
            # 拦截率分析
            f.write("\n\n拦截率分析:\n")
            f.write("-" * 30 + "\n")
            block_rates = self.calculate_block_rates()
            for waf, stats in block_rates.items():
                f.write(f"{waf}: {stats['blocked']}/{stats['total']} ({stats['rate']:.1%})\n")
            
            # WAF特征签名
            f.write("\n\nWAF特征签名:\n")
            f.write("-" * 30 + "\n")
            signatures = self.generate_waf_signatures()
            for waf, sig in signatures.items():
                f.write(f"\n{waf}:\n")
                f.write(f"  拦截率: {sig['block_rate']:.1%}\n")
                f.write(f"  主要拦截状态码: {sig['primary_block_code']}\n")
                f.write(f"  拦截状态码分布: {sig['blocked_statuses']}\n")

def main():
    parser = argparse.ArgumentParser(description="改进的WAF识别工具")
    parser.add_argument("--csv-file", required=True, help="WAF响应数据CSV文件路径")
    parser.add_argument("--output-dir", default="waf_analysis_output", help="输出目录")
    parser.add_argument("--min-specificity", type=float, default=0.7, help="最小特异性阈值")
    
    args = parser.parse_args()
    
    # 创建WAF识别器
    identifier = ImprovedWAFIdentifier(args.csv_file)
    
    # 生成分析报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"{args.output_dir}/waf_analysis_report_{timestamp}.txt"
    identifier.save_analysis_report(report_file)
    
    # 生成特异性载荷
    specific_payloads = identifier.generate_specific_payloads(args.min_specificity)
    
    # 保存特异性载荷
    for waf, payloads in specific_payloads.items():
        if payloads:
            payload_file = f"{args.output_dir}/{waf}_specific_payloads_{timestamp}.txt"
            with open(payload_file, 'w', encoding='utf-8') as f:
                for item in payloads[:50]:  # 只保存前50个最特异的载荷
                    f.write(f"{item['payload']}\n")
            print(f"已保存 {waf} 的 {len(payloads[:50])} 个特异性载荷到 {payload_file}")
    
    print(f"分析报告已保存到: {report_file}")

if __name__ == "__main__":
    main()
