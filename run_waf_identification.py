# -*- coding: utf-8 -*-
"""
运行WAF识别流程的脚本：
1. 测试所有WAF对payload的响应
2. 生成特异性载荷集
3. 测试识别准确性
"""

import os
import argparse
import subprocess
import sys
from datetime import datetime

def run_command(command, description):
    """
    运行命令并打印输出
    
    参数:
    - command: 要运行的命令（字符串或列表）
    - description: 命令描述
    
    返回:
    - 命令退出码
    """
    print(f"\n{'='*80}")
    print(f"执行: {description}")
    print(f"命令: {command if isinstance(command, str) else ' '.join(command)}")
    print(f"{'-'*80}")
    
    try:
        # 如果命令是字符串，则使用shell=True
        shell = isinstance(command, str)
        process = subprocess.Popen(
            command,
            shell=shell,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时打印输出
        for line in process.stdout:
            print(line, end='')
        
        # 等待进程完成
        exit_code = process.wait()
        
        if exit_code == 0:
            print(f"\n{description} 成功完成")
        else:
            print(f"\n{description} 失败，退出码: {exit_code}")
        
        return exit_code
    
    except Exception as e:
        print(f"\n{description} 执行出错: {e}")
        return 1

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="WAF识别流程运行工具")
    parser.add_argument("--payloads-dir", default="端口扫描_本机勿用/识别载荷集构建/payloads", 
                        help="载荷目录路径 (默认: 端口扫描_本机勿用/识别载荷集构建/payloads)")
    parser.add_argument("--output-dir", default="output", 
                        help="输出目录路径 (默认: output)")
    parser.add_argument("--timeout", type=float, default=5, 
                        help="请求超时时间（秒，默认5）")
    parser.add_argument("--no-verify-ssl", action="store_true", 
                        help="不验证SSL证书（用于自签名证书）")
    parser.add_argument("--max-workers", type=int, default=10, 
                        help="最大并行工作线程数（默认10）")
    parser.add_argument("--batch-size", type=int, default=100, 
                        help="每批处理的payload数量（默认100）")
    parser.add_argument("--specificity-threshold", type=float, default=0.5, 
                        help="特异性阈值（默认0.5）")
    parser.add_argument("--skip-testing", action="store_true", 
                        help="跳过测试阶段，直接使用现有的payload_responses.csv")
    
    args = parser.parse_args()
    
    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f"waf-test{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置输出文件路径
    payload_responses_csv = os.path.join(output_dir, "payload_responses.csv")
    
    # 步骤1: 测试所有WAF对payload的响应
    if not args.skip_testing:
        test_command = [
            sys.executable, "test_all_waf_payloads.py",
            "--payloads-dir", args.payloads_dir,
            "--output-file", payload_responses_csv,
            "--timeout", str(args.timeout),
            "--max-workers", str(args.max_workers),
            "--batch-size", str(args.batch_size)
        ]
        
        if args.no_verify_ssl:
            test_command.append("--no-verify-ssl")
        
        exit_code = run_command(test_command, "测试WAF对payload的响应")
        
        if exit_code != 0:
            print(f"测试失败，退出码: {exit_code}")
            return exit_code
    else:
        print("跳过测试阶段，使用现有的payload_responses.csv文件")
    
    # 步骤2: 复制waf_payload_set.py到输出目录
    with open("端口扫描_本机勿用/识别载荷集构建/waf_payload_set.py", "r", encoding="utf-8") as src_file:
        waf_payload_set_content = src_file.read()
    
    # 修改main部分以使用新生成的CSV文件
    waf_payload_set_content = waf_payload_set_content.replace(
        'if __name__ == "__main__":', 
        f'''if __name__ == "__main__":
    # 初始化WAF识别器
    identifier = WAFIdentifier(csv_path="{payload_responses_csv}", specificity_threshold={args.specificity_threshold})
    
    # 生成特异性载荷
    identifier.generate_specific_payloads()
    
    # 保存特异性载荷
    identifier.save_specific_payloads(output_dir="identify_payloads")
    
    # 测试识别准确性（使用原始数据进行交叉验证）
    identifier.test_identification(verbose=True)
    
    # 如果需要识别真实WAF，取消下面的注释并提供WAF URL
    # identifier.identify_real_waf(waf_url="http://your-waf-url/")
'''
    )
    
    # 保存修改后的waf_payload_set.py到输出目录
    waf_payload_set_path = os.path.join(output_dir, "waf_payload_set.py")
    with open(waf_payload_set_path, "w", encoding="utf-8") as dest_file:
        dest_file.write(waf_payload_set_content)
    
    # 步骤3: 运行waf_payload_set.py生成特异性载荷集
    waf_payload_set_command = [sys.executable, waf_payload_set_path]
    exit_code = run_command(waf_payload_set_command, "生成特异性载荷集")
    
    if exit_code != 0:
        print(f"生成特异性载荷集失败，退出码: {exit_code}")
        return exit_code
    
    print(f"\n{'='*80}")
    print(f"WAF识别流程已完成")
    print(f"- 测试结果: {payload_responses_csv}")
    print(f"- 特异性载荷集: {os.path.join(output_dir, 'identify_payloads')}")
    print(f"{'='*80}")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断，程序退出")
        exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        exit(1)
