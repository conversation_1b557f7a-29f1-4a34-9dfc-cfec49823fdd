# -*- coding: utf-8 -*-
"""
脚本用于合并两个特定的payload_responses.csv文件
"""

import os
import pandas as pd
from datetime import datetime

# 指定文件路径
FILE1 = r"D:\anaconda3\envs\danuoyi1\Lib\site-packages\DaNuoYi\端口扫描_本机勿用\识别载荷集构建\payload_responses.csv"
FILE2 = r"D:\anaconda3\envs\danuoyi1\Lib\site-packages\DaNuoYi\端口扫描_本机勿用\识别载荷集构建\payload_responses.csv"

# 生成输出文件路径
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
OUTPUT_FILE = f"D:\\anaconda3\\envs\\danuoyi1\\Lib\\site-packages\\DaNuoYi\\端口扫描_本机勿用\\识别载荷集构建\\merged_payload_responses_{timestamp}.csv"

def merge_csv_files():
    """
    合并两个特定的CSV文件
    """
    print(f"正在读取第一个文件: {FILE1}")
    df1 = pd.read_csv(FILE1)
    print(f"第一个文件包含 {len(df1)} 行数据和 {len(df1.columns)} 列")
    
    # 检查两个文件是否相同
    if FILE1 == FILE2:
        print("警告: 两个文件路径相同，将使用相同的数据")
        print("请确认您想要合并的是两个不同的文件")
        
        # 询问用户是否继续
        response = input("是否继续? (y/n): ")
        if response.lower() != 'y':
            print("操作已取消")
            return
        
        # 如果用户确认继续，使用相同的DataFrame
        df2 = df1.copy()
    else:
        print(f"正在读取第二个文件: {FILE2}")
        df2 = pd.read_csv(FILE2)
        print(f"第二个文件包含 {len(df2)} 行数据和 {len(df2.columns)} 列")
    
    # 获取两个文件的列名（除了payload列）
    cols1 = [col for col in df1.columns if col != 'payload']
    cols2 = [col for col in df2.columns if col != 'payload']
    
    # 检查是否有重复的WAF列名
    duplicate_cols = set(cols1) & set(cols2)
    if duplicate_cols:
        print(f"警告: 两个文件中存在相同的WAF列名: {duplicate_cols}")
        print("将为第二个文件中的重复列添加后缀'_2'")
        
        # 为第二个文件中的重复列添加后缀
        rename_dict = {col: f"{col}_2" for col in duplicate_cols}
        df2 = df2.rename(columns=rename_dict)
        
        # 更新列名列表
        cols2 = [rename_dict.get(col, col) for col in cols2]
    
    # 合并两个DataFrame，使用payload列作为键
    print("正在合并两个文件...")
    merged_df = pd.merge(df1, df2, on='payload', how='outer')
    
    # 统计合并结果
    total_payloads = len(merged_df)
    common_payloads = len(pd.merge(df1, df2, on='payload', how='inner'))
    only_in_file1 = len(df1) - common_payloads
    only_in_file2 = len(df2) - common_payloads
    
    print(f"合并结果:")
    print(f"- 总载荷数: {total_payloads}")
    print(f"- 两个文件共有的载荷数: {common_payloads}")
    print(f"- 仅在第一个文件中的载荷数: {only_in_file1}")
    print(f"- 仅在第二个文件中的载荷数: {only_in_file2}")
    
    # 保存合并后的DataFrame
    print(f"正在保存合并结果到: {OUTPUT_FILE}")
    merged_df.to_csv(OUTPUT_FILE, index=False)
    
    print(f"合并完成，结果已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(OUTPUT_FILE)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 合并CSV文件
        merge_csv_files()
    except Exception as e:
        print(f"错误: {e}")
        exit(1)
