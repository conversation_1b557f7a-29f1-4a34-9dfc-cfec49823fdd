# WAF载荷集创建与识别系统

本系统用于创建针对不同WAF的特异性载荷集，并基于多状态码模式进行WAF识别。

## 系统特点

- ✅ 支持多状态码拦截模式（阿里云WAF的405、华为云WAF的418等）
- ✅ 自动检测WAF的状态码模式
- ✅ 生成特异性载荷集和平衡载荷集
- ✅ 忽略CLOUDFLARE_FREE，专注于有效WAF识别
- ✅ 完整的分析报告和配置管理

## 脚本说明

### 1. analyze_waf_responses.py
**作用**: 分析WAF响应数据，识别状态码模式

**功能**:
- 读取`test_all_waf_payloads.py`生成的CSV文件
- 自动检测每个WAF使用的拦截状态码
- 计算拦截率和载荷有效性
- 生成详细的分析报告

### 2. improved_waf_payload_generator.py
**作用**: 生成改进的WAF载荷集

**功能**:
- 基于分析结果生成特异性载荷集
- 生成平衡载荷集确保合适的拦截率
- 支持多状态码拦截判断
- 保存配置信息供后续识别使用

### 3. waf_identifier.py
**作用**: 识别未知WAF类型

**功能**:
- 使用特异性载荷测试目标WAF
- 基于多状态码模式计算相似度
- 给出识别结果和置信度
- 保存详细的识别报告

## 使用流程

### 步骤1: 分析WAF响应数据
```bash
python analyze_waf_responses.py --csv-file "path/to/waf_responses.csv" --output-dir "analysis_output"
```

**输入**: `test_all_waf_payloads.py`生成的CSV文件
**输出**:
- 分析报告 (`waf_analysis_report_YYYYMMDD_HHMMSS.txt`)
- 检测到的状态码模式

### 步骤2: 生成载荷集
```bash
python improved_waf_payload_generator.py --csv-file "path/to/waf_responses.csv" --output-dir "payloads_output"
```

**参数说明**:
- `--min-specificity`: 最小特异性阈值 (默认0.7)
- `--max-specific-payloads`: 每个WAF最大特异性载荷数 (默认100)
- `--max-balanced-payloads`: 每个WAF最大平衡载荷数 (默认200)
- `--target-block-rate`: 目标拦截率 (默认0.5)

**输出**:
- 特异性载荷集目录 (`identify_payloads_YYYYMMDD_HHMMSS/`) - CSV格式，与现有identify_payloads格式兼容
- 平衡载荷集目录 (`balanced_payloads_YYYYMMDD_HHMMSS/`) - TXT格式
- 配置文件 (`payload_generation_config_YYYYMMDD_HHMMSS.json`)

### 步骤3: 识别WAF
```bash
python waf_identifier.py --target-url "http://target-waf/" --config-file "config.json" --payload-dir "specific_payloads_dir"
```

**参数说明**:
- `--target-url`: 目标WAF的URL
- `--config-file`: 载荷生成配置文件路径
- `--payload-dir`: 特异性载荷目录路径
- `--timeout`: 请求超时时间 (默认5秒)
- `--delay`: 请求间隔 (默认0.5秒)

**输出**:
- 识别结果JSON文件 (`waf_identification_result_YYYYMMDD_HHMMSS.json`)

## 支持的WAF类型

| WAF类型 | 主要拦截状态码 | 说明 |
|---------|---------------|------|
| ALIYUN | 405, 403, 406, 444 | 阿里云WAF，主要使用405 |
| HUAWEI | 418, 403, 406, 444 | 华为云WAF，主要使用418 |
| MODSECURITY | 403, 406, 501, 502, 503 | ModSecurity |
| NGX_LUA | 403, 444, 499 | Nginx Lua WAF |
| SAFELINE | 403, 406 | SafeLine WAF |
| NAXSI | 403, 406, 418 | NAXSI WAF |
| CLOUDFLARE | 403, 429, 503, 520-524 | Cloudflare WAF |
| TENCENT | 403, 406, 444 | 腾讯云WAF |
| BAIDU | 403, 406, 444 | 百度云WAF |

## 完整示例

```bash
# 1. 分析响应数据
python analyze_waf_responses.py \
    --csv-file "merged_payload_responses_20250524_173117.csv" \
    --output-dir "analysis_20250524"

# 2. 生成载荷集
python improved_waf_payload_generator.py \
    --csv-file "merged_payload_responses_20250524_173117.csv" \
    --output-dir "payloads_20250524" \
    --min-specificity 0.8 \
    --max-specific-payloads 50

# 3. 识别WAF
python waf_identifier.py \
    --target-url "http://123.57.69.221/" \
    --config-file "payloads_20250524/payload_generation_config_20250524_180000.json" \
    --payload-dir "payloads_20250524/specific_payloads_20250524_180000" \
    --timeout 10 \
    --delay 1.0
```

## 重要说明

1. **CLOUDFLARE_FREE被忽略**: 系统自动忽略CLOUDFLARE_FREE列，不用于载荷集构建和WAF识别
2. **多状态码支持**: 正确处理不同WAF厂商使用的不同拦截状态码
3. **自动模式检测**: 系统会从数据中自动学习每个WAF的状态码模式
4. **特异性载荷**: 生成的载荷集专门针对特定WAF，提高识别准确性

## 输出文件说明

### 分析报告
- 包含每个WAF的状态码分布
- 检测到的拦截模式
- 载荷有效性分析
- 改进建议

### 载荷集文件
- `{WAF}_specific_payloads.csv`: 特异性载荷（CSV格式，包含载荷、特异性和期望响应）
- `{WAF}_blocked.txt`: 被拦截的载荷（TXT格式）
- `{WAF}_unblocked.txt`: 未被拦截的载荷（TXT格式）

### CSV文件格式说明
特异性载荷CSV文件格式与现有identify_payloads文件夹中的格式完全兼容：
```csv
payload,specificity,MODSECURITY_expected,NGX_LUA_expected,SAFELINE_expected,NAXSI_expected,ALIYUN_expected
"' ) [blank] && /**/ ! ~ ' ' -- [blank] &o",1.0,通过,通过,通过,拦截,通过
"0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP--?H#*/",0.8,通过,通过,通过,拦截,通过
```

### 配置文件
- 包含WAF拦截状态码映射
- 生成参数和统计信息
- 用于后续WAF识别

### 识别结果
- 最可能的WAF类型
- 置信度分数
- 所有WAF的相似度分数
- 详细的测试结果

## 故障排除

1. **CSV文件读取错误**: 确保CSV文件格式正确，包含payload列和WAF响应列
2. **载荷文件不存在**: 确保载荷生成步骤成功完成
3. **网络连接问题**: 检查目标URL是否可访问，调整timeout和delay参数
4. **权限问题**: 确保有写入输出目录的权限
