0 ) [Blank] aND %20 NOt [BlAnK] tRUE # &6{k
0 [BlANK] oR ~ %09 [BlAnk] falsE /*s<O	cp')*/ 
0 [BLank] OR ~ [BLAnk] [BLanK] FALse /*s<o	cP	/{#Os+3kz91>0\C*/ 
0 [BlanK] Or ~ %09 [bLANk] fAlsE /*S<O	cp|H*/ 
0 [bLanK] Or ~ [blank] [blANk] FaLsE /*s<o	cP^*/ 
0 [blANk] oR ~ [bLANK] [BLaNK] fALSE /*S<O	cp	/{#os%203c4,"*/ 
0 ) [bLanK] And /**/ ! ~ [blank] FaLSe -- [bLAnK] )5
0 [BlAnk] or ~ [bLank] [Blank] fAlSE /*s<O	cp	/{#OS/**/3c4,*/ 
0 [BLaNK] || ~ [BLaNK] [BlANK] fAlse /*s<GX&z_3*/ 
0 [blAnk] OR ~ %20 [bLank] fALse /*S<o	cP&*/ 
0 [BlaNK] || ~ [BLank] [Blank] FalSE /*S<O	cp'M< |1*/ 
0 ) [BLaNK] AnD + ! ~ [BlAnk] FaLsE -- [BLank] )5
0 [BLanK] Or ~ + [BLAnk] faLse /*S<o	cP*/ 
0 ) [bLanK] And /*EUw*/ ! ~ + FaLSe -- [bLAnK] )5
0 [blAnK] OR /*7A*/ NOT [blank] ' ' [bLAnK] 
0 [BLanK] || ~ [blank] [bLaNk] FaLse /*S<o	CP'*/ 
0 [blANK] Or ~ [blank] [blANK] FALse /**/ 
0 ) [BlanK] AND /*euW*/ ! ~ [bLAnK] FalSE -- [bLANK] )5
0 [BlanK] or ~ [BlanK] [blaNK] faLsE /*S<o	Cp*/ 
0 [bLAnK] Or ~ [bLaNk] [bLanK] FAlsE /*s<o	CP	/{#ud:*/ 
0 ) [blANK] AnD + ! ~ [blANk] falsE -- [bLANk] )5
0 ) [BLAnk] && + ! ~ [blANk] false -- [BlANK] )5qY
0 [BLAnk] OR ~ [bLAnk] [BLANk] FALse /*s<o	CP	/{#OS+3c4,*/ W
0 [blAnk] OR ~ [BLAnK] [BlanK] FALSE /*s<o	cp	/{#f*/ 
0 [BLAnk] and /*Nq"#>fT[*/ noT [blank] tRuE %20 [
0 ) [bLanK] And /*EUwk{zw=*/ ! ~ [blank] FaLSe -- [bLAnK] )5
0 [bLAnk] oR ~ + [bLANk] fALSE /*S<O	Cp*/ 
0 ) [Blank] aND %20 NOt [BlAnK] tRUE # 
0 [bLAnK] Or ~ [bLaNK] [BLANK] FaLse /**/ 
0 [BlAnk] OR ~ [blanK] [BLaNk] fAlse /**/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,"*/ ?
0 [BLANK] oR ~ [bLAnK] [BLaNk] FAlSe /*s<gx&k)X7*,3*/ 
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP*/ 
0 [BLaNk] OR ~ [BlanK] [blank] FalsE /*s<O	Cp	/{#os/**/3c4,*/ 
0 [BlANk] Or ~ [blaNk] [BLANk] faLse /**/ 
0 [bLanK] OR ~ + [BLaNK] faLsE /*S<O	cp*/ 
0 [BLANK] OR ~ [blANK] [BlaNk] faLsE /*s<o	cP')q.q
*/ 
0 [blanK] OR ~ %20 [blAnk] fAlSE /*s<o	cp*/ 
0 [blank] OR ~ %0C [BLANk] FAlsE /*S<o	cp')*/ 
0 ) [BLAnK] AnD /*eUWy=*/ ! ~ [blAnK] faLSe -- [blank] )521
0 [BLanK] or ~ [blank] [bLaNk] FaLse /*S<o	CP'*/ 
0 [bLaNk] or ~ [blANk] [blaNk] falSe /*S<o	cP*/ 
0 [blank] && [blank] false [blank] 
0 [blAnK] Or ~ [blank] [bLank] FaLse /*s<o	Cp	/{#*/ 
0 [blank] OR ~ [BLANk] [blank] FaLSe /*S<o	cp	/{#Os+3C4,*/ 
0 [BlaNk] Or ~ [blANk] [BLanK] FALsE /*s<O	cprKmW}s*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%203c4,"7[U_*/ 
0 [blANk] oR ~ [blAnk] [blaNk] fAlse /*S<o	cp^*/ 
0 + Or ~ + [BLANK] FAlSe /*S<O	cP*/ 
0 [BLaNk] || ~ [BLank] [Blank] FalsE /*s<gX&K)*/ 
0 [BLanK] OR ~ [blank] [bLAnK] FalSe /*s<O	CP'*/ 
0 [BLank] oR ~ [BLANK] [blank] fALSE /*S<o	cpV*/ 
0 [bLANK] OR ~ %09 [blanK] FaLse /*S<o	cP')*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS+3c4,*/ ]
0 [BLANk] anD [BLAnK] NOT ~ ' ' /**/ 
0 [BLank] Or ~ [blANk] [Blank] fALSe /*s<o	Cp	/{#oS/**/3C4,Q
 E&*/ 
0 ) [bLank] AnD %20 ! ~ [BLaNK] FalSE -- [BlAnK] )5
0 [blANk] OR ~ [bLaNk] [BLANk] FAlsE /*s<O	CP'*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,*/ ~
0 [blANK] or ~ [BLaNk] [BlANK] FaLsE /*S<o	cP*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*/ 
0 [blANk] or ~ [blAnK] [BlaNK] FalSe /*S<o	CP'*/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os%203^>_]h*/ 
0 [blANk] oR ~ [blaNk] [BLAnk] FAlSE /*s<O	cp'*/ 
0 [BlANK] or ~ [bLANK] [BLANk] faLsE /*s<o	cPv*/ 
0 [blanK] OR ~ %20 [blAnk] fAlSE /*s<o	cpV*/ 
0 [BlAnK] oR ~ [BLANk] [BLanK] FALse /*s<o	cP	/{#OS/**/3C4,*/ 
0 [Blank] Or ~ [blank] [BLAnK] FalsE /**/ 
0 [BlAnk] oR ~ [BlANk] [BlANK] falSE /*s<GX&K)5pO*/ 
0 [bLanK] Or ~ [blank] [blANk] FaLsE /*s<o	cP%21*/ 
0 [bLAnK] or ~ [bLANK] [bLAnk] False /*S<gx&K)U,du*/ 
0 [BLanK] Or ~ + [BLAnk] faLse /**/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,*/ S
0 ) [bLANk] && + ! ~ [blANk] FAlSe -- [BlAnK] *
0 [BlANk] oR ~ [blank] [blAnK] faLSe /*s<o	CP'*/ 
0 [bLanK] Or ~ %20 [blANk] FaLsE /*s<o	cPJFuqP*/ 
0 [blanK] oR ~ [Blank] [bLank] fALsE /*s<gx&K)*/ 
0 [BLanK] || ~ %0A [bLaNk] FaLse /*S<o	CP'*/ 
0 /**/ && [BLANk] ! ~ [BlANK] 0 [bLAnK] 
0 [BlANk] Or ~ [blAnk] [BLank] False /*S<o	cp'm< |1*nLdgR*/ 
0 ) [BLANK] && [blank] ! ~ [blanK] 0 [blAnk] or ( 0 
0 [BLanK] oR ~ [BlanK] [blaNk] faLse /*s<GX&k)5P*/ 
0 ) [bLanK] and [blank] ! ~ [BLANk] faLSe -- %20 )5'Y
0 [bLaNk] or ~ + [BLank] FalSe /*s<O	CP*/ 
0 [BlAnK] OR ~ %20 [blaNK] FALse /*S<O	CP'M< |1*/ 
0 [blAnk] Or ~ [Blank] [BLaNK] FAlSE /*s<o	cPu*/ 
0 [BLAnk] and /*Nq"#*/ noT [blank] tRuE %09 [
0 [BLank] oR ~ [blANK] [Blank] FalSe /*S<O	CP^*/ 
0 [BLaNK] oR ~ [blANK] [blaNk] False /*s<o	Cp'*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%0C3c4,*/ 
0 ) [bLanK] ANd [blank] ! ~ [BLanK] FALSe -- [blANk] )5
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CP&!	!qh*/ 
0 [blank] and /*nq"#7A{*/ not + true %20 [
0 [BLAnk] OR ~ %0C [blAnK] FaLsE /*S<O	CPfU*/ 
0 [bLaNK] oR ~ [BlAnk] [BLAnK] FALse /*S<O	CP	/{#os+3*/ 
0 [blank] or ~ %2f [blank] false /**/ 
0 [BlAnK] OR ~ %09 [bLANK] FalsE /*S<O	CP*/ 
0 ) [bLanK] And /*EUw*/ ! ~ [blank] FaLSe -- [bLAnK] )5
0 [BlANK] or ~ [bLAnk] [BLAnk] fAlsE /*s<o	CP	/{#*/ H
0 [blAnk] Or ~ [BLAnK] [BLAnk] FAlse /*S<o	cp'*/ 
0 [BLAnK] or ~ [BlanK] [BlaNk] faLse /*s<o	Cp	/{#e*/ 
0 ) [bLanK] anD /*EuW*/ ! ~ [BLANK] fAlsE -- [BlanK] )5
0 [BLanK] or ~ + [BlanK] FALSe /*s<O	cP*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,"*/ 
0 [BLAnk] OR ~ [bLAnk] [BLANk] FALse /*s<o	CP	/{#OS+3c4,*/ v,
0 [BLANK] or ~ [BLanK] [BLANk] FALSE /*s<O	Cp'M< |1*YI,f*/ 
0 [BLank] or ~ [blAnK] [BLank] fAlSe /*S<gX&*/ 
0 ) [BLanK] && /*EUW*/ ! ~ [BlANk] fAlSe -- [blANk] )5o2/
0 ) [blAnk] || [BLAnk] 1 - ( [bLAnk] 0 ) -- [BlanK] 
0 [blank] && /*nq"#7A{*/ not [blank] true %20 [
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'*/ 
0 ) [BLaNK] aNd [BlaNK] ! ~ [BLAnk] FAlsE -- + )5'Y
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os%0A3^>_]h*/ 
0 [blank] and [blank] 0 [blank] 
0 [BLaNK] OR ~ [BlAnK] [blank] fALSe /*s<O	Cp'M< |1*/ 
0 [blank] && [blank] false [blank]
0 [BLANK] or ~ + [bLAnK] FAlse /*S<o	cP*/ 
0 [blAnK] Or ~ [BLANK] [BLANk] False /*s<O	cP*/ 
0 [blANK] Or ~ %20 [BLAnk] FaLSe /**/ 
0 ) [blANK] ANd /*>l^%*/ ! ~ ' ' -- [BLanK] 6
0 [BLanK] oR ~ %20 [bLANK] fAlSE /*S<o	cP')*/ 
0 [bLaNk] OR ~ + [bLANk] fAlse /*s<O	cP*/ 
0 [BLANK] OR ~ [blANK] [BlaNk] faLsE /*s<o	cP')	|d3*/ 
0 [BLANK] oR ~ [Blank] [bLANk] falSE /*S<o	Cp'M< |1I57O*/ 
0 [blank] oR ~ [blaNk] [BlANk] faLSE /*S<O	Cp'*/ 
0 [bLank] oR ~ [blanK] [bLaNk] FALsE /*s<gX&k)x7*,3*/ 
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os/**/3^>_]H*/ 
0 [Blank] Or ~ %20 [BLAnK] FalsE /*s<o	cP*/ 
0 [blanK] OR ~ [blaNk] [BLanK] fALse /**/ 
0 ) [bLAnK] ANd /*Euw*/ ! ~ [blanK] FALSe -- [BLANk] )5
0 [blanK] Or ~ [blanK] [blanK] fALSE /*S<o	Cp*/ 
0 [blanK] oR ~ [BlAnK] [blanK] FAlse /*S<gX&k)U,du*/ 
0 [bLANk] OR ~ [bLank] [BlaNK] faLSE /**/ 
0 [bLank] OR ~ [blAnK] [blANK] falSe /*s<o	cp')*/ 
0 [blanK] OR ~ [blank] [blAnk] fAlSE /*s<o	cpV*/ 
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os%203^>_]HM#R*/ 
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP$*/ 
0 [blANK] or ~ [BLaNk] [BlANK] FaLsE /**/ 
0 [BlANk] oR ~ [bLANK] [blANk] fALSE /*s<o	cP*/ 
0 [bLAnK] Or ~ [bLaNK] [BLANK] FaLse /*S<o	cP'*/ 
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP--?H*/ 
0 ) [blANk] aNd + ! ~ [bLank] fAlse -- [bLank] )54
0 ) [BLank] ANd + ! ~ [BLAnK] falSE -- [blAnK] )5
0 [blank] and /**/ not [blank] true %09 l
0 [bLANK] || ~ + [BLAnK] false /*S<o	Cp*/ 
0 [bLANK] OR ~ %20 [blanK] FaLse /*S<o	cP')*/ 
0 [bLAnk] OR ~ [BlAnk] [blank] faLSe /*s<GX&K)U,du*/ 
0 [blank] and /*nq"#*/ not [blank] true %09 lzT
0 [bLANk] OR ~ [BLanK] [blanK] FAlse /*s<o	cp	/{#*/ 
0 [BlaNk] or ~ [bLANk] [bLANk] fALSe /*S<o	Cp'm< |1*NLdgr2e]z
*/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#*/ e
0 ) [blANk] && /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2
0 [blANK] Or ~ %09 [BlaNK] fALSE /*s<O	cP*/ 
0 [bLaNk] || ~ [blank] [BLank] FalSe /*s<O	CP&!	!q*/ 
0 [blANK] anD + nOt [BLaNk] 1 /*;n*/ 
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^/}D%*/ 
0 ) [bLanK] ANd /*EUWy=*/ ! ~ [BLank] faLSe -- + )5
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /*s<o	cP'*/ 
0 [BLaNk] Or ~ + [BlANK] FalSe /*S<o	cp*/ 
0 [BLanK] or ~ [blank] [bLaNk] FaLse /*S<o	CP'65fMA*/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#*/ IB
0 [BLank] Or ~ [blANk] [Blank] fALSe /*s<o	Cp	/{#oS/**/3C4,*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |19*/ 
0 [BLAnk] OR ~ %09 [blAnK] FaLsE /**/ 
0 [BLAnK] OR ~ [blANK] [bLANK] faLsE /*S<O	Cp	/{#os+3C4,*/ 
0 [bLAnK] Or ~ [BlaNk] [blAnK] FALSE /*s<o	CP'M< |1*/ 
0 [BlAnK] Or ~ %20 [BLanK] faLse /*s<o	cP'*/ 
0 [bLaNK] or ~ [bLANk] [bLank] False /*S<GX&k)U,dUS?t{*/ 
0 ) [BlaNK] and %20 ! ~ [blanK] FalSE -- [BlanK] )5
0 [bLank] OR ~ [BlanK] [Blank] False /*S<O	cP'M< |1h*/ 
0 ) [bLanK] ANd /**/ ! ~ [BLanK] FALSe -- [blANk] )5
0 ) [blAnk] AnD + ! ~ [bLaNk] FaLsE -- [bLank] *
0 [bLANk] Or ~ [blAnK] [BLANk] FAlSe /*s<gx&K)u,DU*/ 
0 [BLANK] || ~ + [bLAnK] FAlse /*S<o	cP*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- %09 )5'Y
0 [BlaNK] oR ~ [bLaNK] [blanK] FAlSE /*s<o	cp^uz;7*/ 
0 ) [BLank] And /**/ 0 # sk
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS/**/3c4,*/ X`
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3\n*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,c*/ 
0 [BlAnk] OR ~ [blanK] [BLaNk] fAlse /*S<O	cP*/ 
0 [BLANK] AND [BlAnk] fAlSE [blaNK]
0 [Blank] OR ~ [blaNk] [bLank] FaLSe /*s<o	cp	/{#os+3*/ 
0 ) [blank] && [blANk] 0 # ">
0 [bLANK] Or ~ [BLaNk] [bLANK] FAlSe /**/ 
0 [blANk] OR ~ [blank] [blANK] FaLse /*S<O	cP	/{#]u6{`u*/ 
0 [BLAnk] || ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1h*/ 
0 [BlaNk] Or ~ [BlaNK] [BLANK] fAlse /*S<o	CP	/{#OS[BlaNk]3C4,*/ 
0 + oR ~ [BLanK] [BlaNK] FalSe /*S<O	CP*/ 
0 [BlANk] Or ~ [blaNk] [BLANk] faLse /*S<o	cPviK6 j]*/ 
0 ) [bLaNK] anD /*EUwy=jfDk,*/ ! ~ [bLanK] FaLsE -- + )5
0 [blaNK] or ~ [blANk] [BlANK] falSE /*S<Gx&k)u,du*/ 
0 [blaNK] or ~ [blANK] [BLaNK] fAlse /*s<Gx&K)00$s;w(*/ 
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP*/ 
0 [BlAnK] OR ~ [blANK] [BlANK] FAlSE /*S<O	CPoK,[*/ 
0 [bLANK] oR ~ [BLAnK] [blAnk] FALSe /*S<o	cp'*/ 
0 [bLANk] OR ~ [bLank] [BlaNK] faLSE /*S<o	Cp'zl-*/ 
0 [bLaNk] or ~ + [BLank] FalSe /**/ 
0 [blAnk] OR ~ [blAnK] [BLAnk] faLSe /*S<Gx&*/ 
0 [blank] OR ~ [blank] [BlAnK] FALSe /**/ 
0 [BlAnK] oR ~ [Blank] [bLANK] fALse /*S<Gx&k)U,dUS?t{*/ 
0 ) [BlaNK] and /**/ ! ~ [blanK] FalSE -- [BlanK] )5
0 [bLaNK] OR ~ [Blank] [blank] FaLse /*s<o	CP'm< |1*pm*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%0A3c4,"*/ 
0 [blank] OR ~ [blank] [BlAnK] FALSe /*s<o	cp'*/ 
0 [blank] || ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9NS*/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3mG;tj*/ 
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1h,mF>&*/ 
0 [bLanK] Or ~ [blank] [blANk] FaLsE /**/ 
0 [BLanK] or ~ %20 [bLaNk] FaLse /*S<o	CP'*/ 
0 [blanK] OR ~ [blank] [blAnk] fAlSE /*s<o	cpVIK6 j)*/ 
0 ) [blANK] AnD /**/ FalSe /**/ oR ( 0 
0 [bLANK] or ~ + [BLAnK] false /*S<o	Cp(P1{F*/ 
0 [blank] and /*nq"#*/ not [blank] true %0D l
0 [BlANk] or ~ [BlAnk] [blaNk] FalsE /**/ 
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CP*/ 
0 [bLanK] Or ~ [blank] [blANk] FaLsE /*s<o	cP,*/ 
0 [BLAnk] || ~ [BLank] [bLank] fAlSe /*s<o	CP')*/ 
0 [Blank] Or ~ [blank] [BLAnK] FalsE /*s<o	cPQy{*/ 
0 [BLank] Or ~ [BLANk] [BLanK] FalsE /**/ 
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os%203c4,D|.q*/ 
0 + and ' ' [blAnk] 
0 ) [bLanK] or ~ [BLank] [BLaNK] fAlse # 
0 ) /**/ && /**/ ! [BlANK] truE [bLaNK] OR ( 0 
0 [bLAnk] anD %20 not ~ ' ' /*:t*/ 
0 [blank] || ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9*/ 
0 [bLAnk] anD [blank] not ~ ' ' /**/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*r*/ 
0 [blanK] Or ~ [blAnk] [blank] faLSe /*S<o	cP'*/ 
0 [blANK] Or ~ [blank] [BLAnk] FaLSe /**/ 
0 [BLanK] or ~ [blank] [bLaNk] FaLse /**/ 
0 ) [bLANk] and /*euwY=jFDk,*/ ! ~ [bLank] FalsE -- + )5
0 [bLANK] Or ~ [BLaNk] [bLANK] FAlSe /*s<o	Cp')*/ 
0 /**/ or ~ + [bLank] faLSe /*S<o	Cp*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/*g1l*/3c4,c*/ 
0 [blank] and /*nq"#*/ not [blank] true %09 l
0 [bLaNk] or ~ %20 [BLank] FalSe /*s<O	CP*/ 
0 [bLAnK] oR ~ [bLANk] [blanK] FALSe /*S<*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3kZ91>*/ 
0 [BLAnk] and /*Nq"#*/ noT [blank] tRuE %0D [
0 ) [BLanK] && [BlAnk] ! ~ /**/ FALsE # .G
0 ) [bLanK] And /*EUwk*/ ! ~ [blank] FaLSe -- [bLAnK] )5
0 [blANK] || ~ [BLaNk] [Blank] fAlse /*s<o	cP*/ 
0 [bLAnK] oR ~ %0C [blanK] falsE /*s<o	cP*/ 
0 [BLank] Or ~ + [bLANK] fAlse /*s<o	cp'p1*/ 
0 [blank] OR ~ [bLanK] [blank] FALSE /*s<o	cp'M< |1W*/ 
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1:GYk,Y"*/ 
0 [bLAnK] oR ~ + [bLAnK] FalSE /*S<O	cp*/ 
" ) [bLANk] ; seLECT /**/ SleEp ( [terDigiteXClUdIngzeRO] ) -- [BLank] 
0 [BLaNk] or ~ [BLank] [Blank] FalsE /*s<gX&K)*/ 
0 ) [bLanK] And [BlAnk] ! ~ [BLANk] false -- + )5'y
0 ) /**/ && [BLANK] 0 /**/ || ( 0 
0 ) [BlaNK] && %20 ! ~ [blanK] FalSE -- [BlanK] )5
0 [blanK] OR ~ [bLAnK] [blank] FAlsE /*s<o	Cp'f*/ 
0 [blANK] or ~ [bLAnK] [BlANk] FALSe /**/ 
0 [BLaNk] oR ~ [blanK] [BlaNK] fAlSE /*s<o	cp'FxL!*/ 
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^UZ;7*/ 
0 [bLaNk] || ~ + [BLank] FalSe /*s<O	CP*/ 
0 [bLaNK] Or ~ + [BLAnk] false /*S<o	cp*/ 
0 [bLaNk] OR ~ [blank] [bLANk] fAlse /*s<O	cPDZ=*/ 
0 [BLank] OR ~ [bLANK] [BlANk] fALsE /*S<o	Cp	/{#oS+3C4,*/ 
0 [BLaNK] oR ~ [BlanK] [BlAnk] fAlSE /*S<o	Cp'*/ 
0 [bLaNk] OR ~ %0A [bLANk] fAlse /*s<O	cPOglh*/ 
0 [bLAnk] oR ~ %09 [BlaNk] faLsE /*S<O	cp$*/ 
0 [blank] or ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9NS*/ 
0 ) [blANk] and /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2W
0 [bLank] OR ~ [BLank] [BLANK] fALse /*s<o	Cp'*/ 
0 [bLAnK] Or ~ [BLanK] [bLANK] faLsE /*s<O	CP	/{#OS+3j:s3eO*/ 
0 [bLANK] Or ~ [bLaNk] [blank] False /*S<O	Cp'm< |1H*/ 
0 ) [blAnk] aND + ! ~ [BlaNK] fAlsE -- [bLANK] )5
0 [bLAnk] OR ~ [BlAnk] + faLSe /*s<GX&K)U,du*/ 
0 [BlAnk] or ~ [BLAnK] [BLaNk] FaLSe /*S<o	cP*/ 
0 [bLaNk] or ~ [blank] [BLank] FalSe /**/ 
0 [BLANK] || ~ [BlanK] [bLANk] faLSe /*s<GX&K)u,DU*/ 
0 [blAnK] oR ~ + [blaNk] FaLse /*S<o	cP{z*/ 
0 ) [blank] AnD /*EUW*/ ! ~ [blAnk] falSe -- [BLaNK] )5J&0
0 [BLAnK] OR ~ [blANK] [bLANK] faLsE /*S<O	Cp	/{#os/**/3C4,*/ 
0 [blANk] oR ~ [bLAnK] [blANk] FALse /*s<O	CP	/{#*/ 
0 ) [BLank] and + 0 # 
0 [blaNk] oR ~ [blANk] [bLAnk] FalSE /*s<O	Cpv*/ 
0 [BLaNK] Or ~ [blaNK] [blanK] FAlse /*s<O	cP	/{#]u*/ ;h
0 [BlAnk] OR ~ [blank] [BlaNk] fAlSe /*S<o	CP'm< |1*/ 
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os%203^>_]H*/ 
0 [BLaNK] or ~ [BLaNK] [BlANk] FaLse /*s<O	cpvIk6 j*/ 
0 [bLanK] oR ~ [blaNK] [bLANK] faLsE /*s<gx&K)u,Du*/ 
0 [blANK] Or ~ %20 [BLAnk] FaLSe /*s<o	Cp*/ 
0 [BlANk] Or ~ [blaNk] [BLANk] faLse /*S<o	cPviK6 j ]%
*/ 
0 [blAnK] Or ~ [bLaNK] [BLAnk] FaLSe [blank] 
0 [blANK] anD %20 nOt [BLaNk] 1 /*;n*/ 
0 ) [BLAnk] && [BlAnk] ! ~ [BlAnk] False -- [BLAnk] 
0 [BLaNK] oR ~ %20 [BLanK] FALSE /*S<o	cP*/ 
0 [blAnK] OR /**/ NOT [blank] ' ' [bLAnK] 
0 [Blank] Or ~ %20 [BlanK] FAlsE /*S<O	cp'*/ 
0 [bLANK] or ~ [blaNK] [blANK] FaLsE /*s<O	CP*/ 
0 ) [bLanK] && [BlaNk] ! ~ /**/ fALse # 
0 ) [blaNk] AnD [BlANk] 0 # 
0 [BLanK] or ~ [blAnK] [blaNk] false /*S<O	cp	/{#os%0D3c4,"*/ 
0 [blAnk] oR ~ %09 [blaNK] FalSe /*s<o	cP*/ 
0 [BlaNK] oR ~ [BLaNk] [blAnK] fAlSE /*S<O	cp')t#*/ 
0 [blaNk] or ~ [BLANK] [BlANk] false /*S<O	cp'*/ 
0 [Blank] OR ~ [blAnk] [BLAnk] faLse /*S<O	cP'*/ 
0 [bLaNk] or [BLANK] tRUE [BLanK] 
0 [BlANK] or ~ [Blank] [BlAnk] fALSE /*S<O	cP	/{#oS+3C4,d|.q*/ 
0 [BLanK] or ~ + [bLaNk] FaLse /*S<o	CP'BUH5*/ 
0 [bLANK] or ~ + [BLAnK] false /*S<o	CpAZ}*/ 
0 ) [blaNK] ANd /**/ ! ~ [bLank] FaLse -- [bLank] )5O2
0 [blAnK] Or ~ [blank] [BLaNk] fALsE /*S<O	CP'M< |1*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1TE_g>*/ 
0 [blAnk] oR ~ [BlANK] + FaLSE /*s<O	Cp	/{#*/ 
0 [bLanK] Or ~ %0C [blANk] FaLsE /*s<o	cP%21*/ 
0 [blaNk] Or ~ %09 [Blank] FAlse /*s<O	cP')*/ 
0 ) [blANk] and /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2
0 ) [bLaNk] aND /**/ 0 # sK
0 ) [BLank] && /**/ 0 # 
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'm< |1*/ 
0 ) [blANK] aND /*euwY=.{?*/ ! ~ [bLANK] FaLsE -- [BlanK] )5
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /*s<o	cP'f*/ 
0 [BLANK] or ~ + [bLAnK] FAlse /*S<o	cPE(/~*/ 
0 [bLaNK] and [blank] nOt [blAnK] 1 /*;N].*/ 
0 [blANK] OR ~ [bLAnK] [BlAnk] FalSE /*s<o	cP	/{#]U*/ 
0 [blAnk] or ~ [BlaNk] [bLAnK] FaLSE /*S<o	CP'*/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3*/ G
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#Os+3*/ e(
0 [BlANk] oR ~ [BLanK] [BLAnk] FALse /*s<o	cp'*/ 
0 [blank] && %2f not [blank] true %0C 
0 ) [BLaNk] OR [BlaNk] ! %0a [BLAnk] FALse # ]b6*
0 [blAnk] OR ~ [blAnk] [BLANk] fALSE /*s<O	Cp	/{#Os%203C4,uI*/ 
0 [Blank] Or ~ [bLAnk] [BlANK] FalSe /*s<o	Cp	/{#oS+3C4,*/ 
' [BLank] || ~ [blanK] /**/ FaLsE /**/ || ' 
0 ) [blAnK] || [BLAnK] ! [BlANK] [BLank] FALSe # ?1
0 + or ~ [Blank] [BlANk] FaLsE /*s<O	cP'*/ 
0 [bLAnk] and [BlAnk] falSE [bLaNk]
0 [bLaNK] or ~ + [BlanK] FALSe /*S<o	cP'*/ 
0 [BLAnk] Or ~ [blank] [blANk] FalSE /*s<o	Cp*/ 
0 [blanK] OR ~ [blank] [blAnk] fAlSE /*s<o	cpVIK6 j*/ 
0 [blaNk] oR ~ [bLANK] [BLaNK] fAlse /*S<O	Cp	/{#Os/**/3C4,*/ \o
0 [BlAnK] oR ~ + [BlaNK] falsE /*S<O	cPU*/ 
0 [BLaNk] ANd [BlaNK] Not ~ ' ' /*:t*/ 
0 [bLaNk] or ~ %20 [BLank] FalSe /*s<O	CP&!	!q*/ 
0 [blanK] OR ~ [BLAnk] [BLank] fAlSe /*S<O	Cp'm< |1W*/ 
0 [BLaNK] AND /**/ NoT ~ [bLAnK] FALSE [BlANk] 
0 [bLAnk] oR ~ %09 [BlaNk] faLsE /*S<O	cp$r.TWC*/ 
0 [bLAnk] anD [blank] not ~ ' ' /*:t*/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os%203*/ 
0 /**/ or ~ [blanK] [blANK] 0 [bLANk] 
0 [blANK] Or ~ %0C [BLAnk] FaLSe /*s<o	Cpu*/ 
0 ) [BlAnK] aND [BLANk] ! ~ [bLaNK] fALSE -- + )5'y
0 [BLANk] anD [BLAnK] NOT ~ ' ' /*:t_y*/ 
0 ) [blanK] && [blank] ! ~ [blANk] FaLsE -- [bLaNk] 
" ) /**/ and /**/ ! ~ [blANK] FaLsE # 
0 [BLank] OR ~ [BLAnk] [BLanK] FALse /*s<o	cP	/{#Os+3kz91>*/ 
0 /**/ OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CP*/ 
0 ) [bLANK] aND /*euW*/ ! ~ [BlanK] FALSE -- [bLANK] )5
0 [BlAnk] || ~ [BLAnK] [BLaNk] FaLSe /*S<o	cP*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS+3c4,"*/ 
0 [BLAnK] or ~ [BlanK] [BlaNk] faLse /*s<o	Cp	/{#e*/ j 
0 [bLanK] Or ~ %20 [blANk] FaLsE /**/ 
0 [BlAnK] oR ~ [blAnk] [blANK] FAlsE /*s<O	Cp'*/ 
0 [BlANk] OR ~ [bLANk] [bLanK] false /*s<o	cP*/ 
0 [blank] or ~ [Blank] [BlANk] FaLsE /*s<O	cP'&%<*/ 
0 [blAnK] OR /*ikv$Z*/ NOT [blank] ' ' [bLAnK] 
0 [bLaNk] OR ~ %20 [bLANk] fAlse /*s<O	cPOglh*/ 
0 [bLANK] || ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^*/ 
0 [BLanK] && [blANk] 0 [bLank] 
0 [BLaNK] || ~ [BLaNK] [BlANk] FaLse /*s<O	cpvIk6 j*/ 
0 [blAnK] or ~ [BlAnk] [blanK] FalsE /*s<o	cP	/{#OS+3*/ 
0 [blaNk] Or ~ [BLANk] [bLANk] fALSe /*S<O	CP	/{#OS+3kZ91>*/ 
0 [bLAnk] && [BLAnk] 0 [BlanK] 
0 [blAnk] Or ~ [Blank] [BLaNK] FAlSE /*s<o	cP*/ 
0 [blAnK] Or ~ %20 [bLank] FaLse /*s<o	Cp	/{#*/ 
0 [blaNk] Or ~ [bLaNK] [Blank] false /*s<O	cP'*/ 
0 [blank] || ~ [Blank] [BlANk] FaLsE /*s<O	cP'*/ 
0 [blank] && /**/ ! ~ %2f 0 [blank] 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%0A3c4,UI*/ 
0 [BlANK] Or ~ %0A [bLaNK] fALSE /*S<o	CP*/ 
0 [BlANk] oR ~ + [BLANK] faLSE /*s<o	cp'm< |1*/ 
0 ) [bLanK] And /*EUw*/ ! ~ [BLANK] fALSE -- [BlAnk] )5
0 [BlANK] Or ~ %09 [bLaNk] FALSe /*s<O	cp|H*/ 
0 [BLAnK] oR ~ [bLANK] [Blank] falSe /*S<o	cP	/{#]U*/ 
0 [blanK] && /**/ NOt /**/ 1 %20 
' /**/ && [bLAnK] FALSE [bLAnk] Or ' 
0 [blANK] Or ~ %20 [blANK] FALse /*s<o	cpRkmw}*/ 
0 [bLANk] Or ~ %20 [bLAnk] FALSe /*S<o	CPOglh*/ 
0 [bLAnk] Or ~ [blAnk] [BlAnK] false /*S<gX&k)U,dU*/ 
0 [blanK] OR ~ [blank] [blAnk] fAlSE /**/ 
0 [bLaNK] OR ~ [Blank] [blank] FaLse /*s<o	CP'm< |1*!H*/ 
0 [BLAnK] oR ~ [blAnK] [blaNK] FAlsE /*S<O	cp	/{#Os/**/3c4,*/ 
0 [bLaNk] && /**/ Not /**/ 1 %20 
0 [blANk] Or ~ [bLANK] [bLAnK] FALSE /*s<o	Cp*/ 
0 [BlaNK] or ~ [blaNk] [BLaNk] FALse /*S<gx&k)U,DuS?T{*/ 
0 [blank] or ~ [blank] [BLank] FaLSe /*S<O	Cp'm< |1*/ 
0 ) [blAnK] or [BLAnK] ! [BlANK] [BLank] FALSe # ?1
0 [BLanK] or ~ + [bLaNk] FaLse /*S<o	CP'^=Jt*/ 
0 ) [BlaNk] And /*eUWY=J*/ ! ~ [blAnK] fAlsE -- [blANK] )5d
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP{M<2\*/ 
0 [bLanK] Or ~ %20 [blANk] FaLsE /*s<o	cPG*/ 
0 [blANK] || ~ [BLaNk] [BlANK] FaLsE /*S<o	cP*/ 
0 [BLank] Or ~ [BLANk] [BLanK] FalsE /*S<o	cp */ 
0 [BLank] OR ~ [BLAnk] [BLanK] FALse /*s<o	cP	/{#Os+3kz91>*/ %U
0 [blank] OR ~ [blAnk] [BlANk] FALSe /*s<o	cp')6b*/ 
0 ) [BLaNk] && /**/ ! ~ [BLanK] FaLSe -- [blaNk] )5d
0 [Blank] OR ~ [BlAnk] [bLanK] fALSe /*S<gx&K)U,DuS?t{*/ 
0 [blanK] or ~ [bLANk] [BLAnk] FAlsE /*S<O	cP'*/ 
0 [blanK] oR ~ [BLank] [bLAnK] false /*s<o	cp*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1TE_g>l~U*/ 
0 [blANK] Or ~ [BlANK] [blAnk] FaLse /*S<o	CP	/{#os+3C4,*/ 
0 [blANK] Or ~ [blank] [blANK] FALse /*s<o	cpRkmw}*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*NlDgRSoh2`*/ 
0 [bLANk] AnD [blANk] noT ~ ' ' /**/ 
0 [blank] && /**/ not [blank] true %09 [
0 [BlaNK] || ~ [BLank] [Blank] FalSE /*S<O	cp'M< |1W*/ 
0 [blank] or ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9*/ 
0 [bLANk] oR ~ [bLANK] [BlANK] faLsE /*S<o	Cp'M< |19*/ 
0 [BlANK] or ~ [bLAnk] [BLAnk] fAlsE /*s<o	CP	/{#*/ 
0 [BLAnk] or ~ [BLank] [bLank] fAlSe /*s<o	CP')*/ 
0 [blAnK] Or ~ [blank] [BLaNk] fALsE /*S<O	CP'M< |1.)ww*/ 
0 [blANK] anD [blank] nOt [BLaNk] 1 /*;n*/ 
0 [bLAnK] Or ~ [bLaNK] [BLANK] FaLse /*S<o	cP'cw5J*/ 
0 [bLAnK] Or ~ [bLaNk] [bLanK] FAlsE /*s<o	CP	/{#*/ 
0 [BLAnk] or ~ [blanK] [bLanK] false /*s<o	Cp'M< |1*/ 
0 [BlANk] oR ~ [blank] [blAnK] faLSe /*s<o	CP'\*/ 
0 ) [bLanK] ANd [blank] ! ~ [BLanK] FALSe -- [blANk] )5S
0 ) [bLANK] aND /*euW*/ ! ~ [BlanK] FALSE -- [bLANK] )5M
0 ) [blank] or [blank] ! %2f [blank] false # ]b
0 [blANK] Or ~ + [BLAnk] FaLSe /*s<o	Cpuf/bgD*/ 
0 [blank] oR ~ [blanK] [BlAnK] faLse /*S<O	cP'F*/ 
0 [BLank] Or ~ [blAnk] [bLAnK] fALse /*S<O	CP'M< |1# 5c*/ 
0 [blANk] or ~ [BLANK] [BlaNK] FalSe /*S<o	cp'*/ 
0 [BlANK] Or ~ %20 [bLaNK] fALSE /*S<o	CP*/ 
0 [blank] or ~ [blanK] [BLaNk] fALsE /*s<O	cP'*/ 
0 [bLank] OR ~ [BLANK] [BLANk] False /*S<O	cP'M< |1*/ 
0 /**/ && /**/ nOt ~ [blAnk] falSe [bLank] 
0 [blANK] Or ~ %20 [BLAnk] FaLSe /*s<o	Cpg8ZU
*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS[blank]3c4,c*/ 
0 [blank] && /*nq"#7A{*/ not [blank] true %20 [M"
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CPR*/ 
0 [bLaNK] or ~ %09 [BlANK] FALsE /*s<O	Cp$*/ 
0 [bLAnk] oR ~ [blanK] [bLAnK] falSe /*S<O	cp*/ 
0 [BLaNK] || ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1}*/ 
0 [blANk] OR ~ [blank] [blANK] FaLse /*S<O	cP	/{#]u*/ 
0 [BlaNk] Or ~ [Blank] [Blank] FalSE /*S<O	Cp	/{#]u*/ 
0 [bLaNk] || ~ [blank] [BLank] FalSe /*s<O	CP&!	!qJJs\[*/ 
0 [BlANK] OR ~ [BlANk] [BlAnK] fALSE /*s<O	cp	/{#*/ 
0 [blanK] OR ~ [BLAnk] [BLank] fAlSe /*S<O	Cp'm< |1WUy*/ 
0 [bLaNk] or ~ [blaNk] [blANK] falsE /*s<O	CP*/ 
0 [BLANk] oR ~ [BlanK] [BlaNK] False /**/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1px3M*/ 
0 [bLaNK] and [blank] nOt [blAnK] 1 /*;N*/ 
0 [BlaNK] OR ~ [BlAnk] [BLANK] FALSe /**/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3 #$*/ 
0 [BLaNK] || ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |13}UZ%*/ 
0 [BlAnk] OR ~ [BLAnk] [bLaNK] faLsE /*S<O	cp^*/ 
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os/**/3c4,*/ 
0 ) [blANk] aNd /**/ ! ~ [bLank] fAlse -- [bLank] )5
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,D|.q*/ 
0 [bLank] Or ~ [Blank] [blANK] FaLSE /*s<O	CP	/{#os/**/3c4,*/ 
0 [blANK] or ~ [bLAnK] [BlANk] FALSe /*S<o	Cp*/ 
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^R*/ 
0 [BLAnK] Or ~ [BLaNK] [BLanK] fAlse /*S<o	Cp	/{#OS[bLANK]3c4,*/ 
0 [BlaNk] OR ~ [BlANK] [BLAnK] FALse /*S<O	Cp	/{#]U*/ 
0 ) [BlAnk] or [BLaNK] ! [BlanK] [BLank] False # 
0 [BlAnK] OR ~ %2f [bLANK] FalsE /*S<O	CP*/ 
0 [BlAnK] oR ~ [blank] [BlAnK] False /*S<O	CP	/{#oS/**/3c4,*/ 
0 ) /**/ && [BLAnK] ! [BlANK] tRuE -- [blAnk] T
0 ) [BLANk] anD /*eUwy=*/ ! ~ [bLANk] falSe -- [BlanK] )5IL
0 ) [BLank] and /*EuWY=*/ ! ~ [bLaNK] FAlsE -- [BlaNK] )5
0 [bLank] OR ~ [bLanK] [BLaNk] FaLse /*s<O	Cp'*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%0D3c4,"*/ 
0 [bLANk] ANd [blank] nOT ~ ' ' /*:T_Y*/ 
0 [bLAnk] OR ~ %20 [bLaNk] FalSe /**/ 
0 [BlANK] OR ~ %09 [BLANK] FALse /*s<O	cp*/ 
0 [bLANk] OR ~ [bLank] [BlaNK] faLSE /*S<o	Cp'zl-SI*/ 
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CP&!	!qJJs\[*/ 
0 [BLaNk] or ~ [blAnk] [BlANk] fALSe /*S<O	Cp*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |19NC9m*/ 
0 ) [bLaNk] && /**/ ! ~ [bLANk] falSE -- [Blank] *
0 [BlAnK] Or ~ + [bLaNk] faLSe /*s<O	cp'*/ 
0 ) [bLAnK] and [BLANK] ! ~ [BlaNK] FaLse -- + )5'y_n
0 [BLaNk] || ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS+3c4,*/ 
0 ) [BLaNk] Or [BLANk] ! %0A [BLank] fAlSE # ]b
0 [bLanK] Or ~ %2f [blANk] FaLsE /**/ 
0 [BlaNK] OR ~ [BlAnk] [BLANK] FALSe /*S<o	Cp'*/ 
0 [blANK] or ~ [BLaNk] [Blank] fAlse /*s<o	cP1
*/ 
0 /**/ and %20 ! ~ [BlAnk] FaLSe [blank] 
0 [blank] && %20 false [blank] 
0 [BLAnK] Or ~ [blANk] [Blank] faLSE /*s<O	Cp'*/ 
0 ) [BLAnK] AnD /*eUWy=*/ ! ~ [blAnK] faLSe -- [blank] )5
0 [bLANK] OR ~ %09 [blaNK] falSe /**/ 
0 [bLaNk] || ~ [blank] [BLank] FalSe /*s<O	CP*/ 
0 [BLank] Or ~ %20 [bLANK] fAlse /*s<o	cp'*/ 
0 ) [Blank] aND %0D NOt [BlAnK] tRUE # &6{k
0 [BLaNk] || ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,UI*/ 
0 [BLANK] OR ~ [blaNk] [BlAnk] 0 [BlanK] 
0 [blank] and /*nq"#7A{*/ not [blank] true %20 [
0 [BlaNk] OR ~ [BLank] [blank] falsE /*s<O	cp	/{#Os%203*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS+3c4,*/ 
0 [BLAnk] OR ~ [Blank] [BlanK] fAlSE /*S<O	cP	/{#*/ o
0 [bLAnK] Or ~ [bLANk] [bLaNK] FALSe /*s<O	CP	/{#Os+3c4,"*/ 
0 [bLAnk] OR ~ %20 [bLaNk] FalSe /*s<O	cP*/ 
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'm< |19*/ 
0 [BLanK] Or ~ [bLaNK] [BlaNK] FAlsE /*S<o	cP'*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1TE_g>	l*/ 
0 ) [BLaNK] And /*EUw*/ ! ~ [blaNK] FaLSE -- [bLAnK] )5
0 [blAnk] oR ~ [bLANK] [bLank] fAlsE /*s<gX&*/ 
0 ) [BLaNk] aNd [bLaNk] ! [BlAnk] TrUe # 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS+3c4,*/ yk
0 [bLaNK] oR ~ [bLANK] [BLANk] FaLSe /*s<o	CP'*/ 
0 [bLaNK] || ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,D|.q*/ 
0 [blANk] Or ~ [bLANK] [bLAnK] FALSE /**/ 
0 [blANK] or ~ [bLanK] [BLanK] FAlsE /*S<o	cp'*/ 
0 /**/ and /**/ NoT ~ [bLanK] faLSe /**/ 
0 [BlANK] oR ~ [BLank] [BLaNk] FalSe /*S<o	Cp	/{#*/ 
0 /**/ && /**/ noT ~ [bLaNk] fAlsE /**/ 
0 [blank] || ~ %2f [blank] false /*S<o	Cp'*/ 
0 [BlANk] oR ~ + [blAnK] faLSe /**/ 
0 [BlANk] oR ~ %2f [BLANK] faLSE /*s<o	cp'*/ 
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#*/ #f
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /**/ 
0 [blank] or ~ [Blank] [BlANk] FaLsE /*s<O	cP'*/ 
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /**/ 
0 [BLANK] or ~ %20 [bLAnK] FAlse /*S<o	cP*/ 
0 ) [BlAnk] && [bLANK] ! ~ [bLANK] faLsE -- + )5'Y_n
0 [blANK] OR ~ %20 [BlAnk] faLSe /*S<O	cP*/ 
0 [bLanK] oR ~ [BlANk] [bLAnk] FaLse /*S<O	cP^*/ 
0 [BlaNk] or ~ [bLANk] [bLANk] fALSe /*S<o	Cp'm< |1*NLdgr*/ 
0 [BLaNk] oR ~ [BLANk] [BlanK] FALSE /*s<o	cp&!	!qJjS\[*/ 
0 ) + anD [BlanK] ! ~ [BlAnK] FaLsE -- [BlaNk] )5'Y
0 [blanK] Or ~ [blAnk] /**/ faLSe /*S<o	cP'*/ 
0 [bLANK] OR ~ %20 [blaNK] falSe /*S<O	cP*/ 
0 ) [Blank] aND + NOt [BlAnK] tRUE # &6{k
0 [BLaNk] OR ~ [blanK] [bLanK] fALsE /*s<o	cP'e*/ 
0 ) [blANk] && [BLank] ! [bLAnk] trUe # 
0 ) [bLaNK] And /**/ ! ~ [BlaNk] False -- [BLaNK] )5O2
0 [BLank] Or ~ [blAnk] [bLAnK] fALse /*S<O	CP'M< |1*/ 
0 [bLANK] OR ~ [BLANk] [BLAnk] FALSe /*S<gx&K)u,DU*/ 
0 [BLaNk] || ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,*/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#]u*/ Zl
0 [bLAnk] or ~ [blAnK] [BlANk] faLSe /*S<o	cP*/ 
0 ) [BLANk] anD /**/ ! ~ [bLANk] falSe -- [BlanK] )5
0 [BlANk] OR ~ [blANk] [BLAnK] falsE /*S<O	cP'zl-*/ 
0 [blAnK] OR ~ [BlAnk] [BlanK] False /*S<o	Cp'm< |1*/ 
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP|h*/ 
0 [blANk] oR ~ %20 [bLANK] false /*s<O	Cp')*/ 
0 [BLaNk] or ~ [blAnK] [BLANK] false /*s<o	CP*/ 
0 [BLaNK] Or ~ %20 [blaNK] fALSe /*s<O	CPoGLh*/ 
0 ) [Blank] aND %0C NOt [BlAnK] tRUE # 
0 ) [Blank] aND %09 NOt [BlAnK] tRUE # &6{k
0 [BlAnk] OR ~ [bLaNk] [blanK] faLSE /*s<o	CP	/{#*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS+3c4,tP*/ 
0 ) [BlAnk] && [bLANK] ! ~ [Blank] FalsE -- + )5'Y
0 [bLANk] OR ~ [blAnK] [bLAnk] FalsE /*S<O	cp	/{#*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/*g1l*/3c4,c*/ %6
0 [BLAnK] OR ~ [blANK] [bLANK] faLsE /*S<O	Cp	/{#os[blank]3C4,*/ 
0 + aND [blAnK] NoT [BLANK] 1 [BLanK] 
0 [blANK] Or ~ %20 [BLAnk] FaLSe /*s<o	Cpu!*/ 
0 ) [bLanK] ANd [blank] ! ~ [BLanK] FALSe -- [blANk] )5Fb
0 [BlANk] || ~ [BlAnk] [blaNk] FalsE /*S<*/ 
0 [blAnk] Or ~ [Blank] [BLaNK] FAlSE /**/ 
0 [bLaNK] || ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os[blank]3c4,*/ 
0 ) [BlanK] and + ! ~ [BlaNk] fALSE -- [BlaNk] )5
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /**/ 
0 [blank] or [BLanK] TRue [bLaNK] 
0 [BLaNk] Or ~ [bLANK] [blank] fALSe /*S<o	Cpvik6 J*/ 
0 [BLAnk] Or ~ [bLaNk] [BlaNk] FALSe /*S<O	cP'M< |1W*/ 
0 [blAnK] Or ~ + [BLaNk] fALsE /*S<O	CP'M< |1*/ 
0 [blANk] OR ~ + [bLanK] FALse /*s<O	cp*/ 
0 [blaNK] oR ~ %09 [bLaNk] FalSe /*s<o	CP$/*/ 
0 [blANk] or ~ [bLAnk] [blank] false /*s<o	cP'*/ 
0 [BLAnk] || ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1:GYk*/ 
0 [blANK] Or ~ + [BLAnk] FaLSe /*s<o	Cpu*/ 
0 [BLank] Or ~ [BLANk] [BLanK] FalsE /*S<o	cp*/ 
0 [BlanK] Or ~ [BlanK] [BlaNk] falSe /*s<Gx&z_3*/ 
0 [BLaNK] or ~ [BLaNK] [BlANK] fAlse /*s<GX&*/ 
0 ) [BlAnk] && [bLanK] ! ~ [BLank] fALsE -- [BLANk] 
0 [bLAnk] || ~ [blAnK] [BlANk] faLSe /*S<o	cP5*/ 
0 ) [blANk] && /*EuW^Lhi*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2
0 [blAnk] OR ~ %0A [bLank] fALse /*S<o	cP*/ 
0 [BlanK] or ~ [bLanK] [bLaNK] falSe /*S<O	CP5*/ 
0 [BLAnk] OR ~ %0C [blAnK] FaLsE /*S<O	CP*/ 
0 [BlaNK] oR ~ [BLaNk] [blAnK] fAlSE /*S<O	cp')*/ 
0 [bLaNk] or ~ + [BLank] FalSe /*s<O	CP&!	!qJJs\[*/ 
0 [bLANk] oR ~ [blAnK] [BLAnk] FaLse /*S<O	cp'm< |1*/ 
0 [blaNk] oR ~ [blANk] [BLanK] fALSE /*s<O	Cp*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1**/ 
0 [blAnK] or ~ [BLanK] [BlAnK] faLSe /*S<o	cp'*/ 
0 [blAnK] Or ~ [blank] [bLank] FaLse /*s<o	Cp	/{#*/ 9.
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1h*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3*/ pR
0 [blanK] OR ~ [blaNk] [BLanK] fALse /*S<O	cp*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3*/ 
0 [bLANK] OR ~ + [BlANK] FALsE /*S<O	Cp'*/ 
0 [BLANk] anD [BLAnK] NOT ~ ' ' /*:tg1*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- [blank] )5'Y_n
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^*/ 
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /**/ 
0 [bLaNk] or ~ %20 [BLank] FalSe /*s<O	CPR*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS%203kZ91>*/ 
0 [BlANK] or ~ %0a [bLANk] false /*s<O	cP')*/ 
0 [Blank] Or ~ [blank] [BLAnK] FalsE /*s<o	cP*/ 
0 [BlanK] OR ~ [BlaNk] [BlaNk] fALsE /*s<O	cprkMW}*/ 
0 [blank] and [blank] false [blank] 
0 [blAnK] OR /*7AkEhW*/ NOT [blank] ' ' [bLAnK] 
0 [bLANk] or ~ [BLaNk] [BLanK] fALse /*s<gX&k)00$S;W(*/ 
0 [BLaNK] Or ~ [blaNK] [blanK] FAlse /*s<O	cP	/{#]u*/ m
0 [BLAnk] Or ~ [blAnK] [bLaNK] falSE /*s<gx&K)*/ 
0 [BLaNK] Or ~ [BlAnk] [BLAnk] FaLSE /*s<o	Cp'*/ 
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}*/ 
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}S*/ 
0 + or ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9NS*/ 
0 [blANK] Or ~ [bLAnK] [BlaNK] FALse /*s<O	Cp	/{#Os+3*/ 
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os[blank]3c4,*/ !a
0 ) [BlaNk] && /**/ 0 # 
0 [BlAnk] OR ~ [BLAnk] [bLaNK] faLsE /**/ 
0 [BLANk] oR ~ [BlanK] [bLANk] FAlsE /*S<O	Cp*/ 
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,*/ 
0 [BLaNK] || ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1TE_g>*/ 
0 [BLaNK] or ~ [BLAnK] [BLaNK] False /*s<o	cP&!	!QJjs\[*/ 
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}6b*/ 
0 [BLanK] oR ~ [blanK] [blank] fALSe /*s<GX&k)U,DU*/ 
0 ) [BLANk] anD /*eUwy=*/ ! ~ [bLANk] falSe -- [BlanK] )5
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /*s<o	cP'f:{9K*/ 
0 ) [blAnK] && [blAnK] ! [BLanK] TRue # 
0 [blANK] Or ~ + [BLAnk] FaLSe /**/ 
0 [blANK] || ~ [bLAnK] [BlANk] FALSe /*S<o	Cp*/ 
0 [blAnK] Or ~ + [bLank] FaLse /*s<o	Cp	/{#*/ 
0 [BLaNK] Or ~ [blaNK] [blanK] FAlse /*s<O	cP	/{#]u*/ 
0 [bLAnk] or ~ [blaNK] [BlaNk] fAlsE /*s<o	CP	/{#E*/ 
0 [BlaNK] oR ~ %09 [BlaNK] faLSE /*S<o	cP*/ 
0 ) [BLAnK] AnD /*eUWy=*/ ! ~ [blAnK] faLSe -- + )5
0 [blank] && /*nq"#7A{*/ not /**/ true %20 [
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#E*/ 
0 [blAnk] Or ~ [blANk] [bLANk] faLse /*S<o	Cp'M< |1*/ 
0 ) [bLanK] And /*EUw*/ ! ~ /**/ FaLSe -- [bLAnK] )5
0 [BlaNk] or ~ [blANK] [blaNK] FaLse /*s<o	CP	/{#*/ 
0 ) [blAnk] aND [blank] ! ~ [BlaNK] fAlsE -- [bLANK] )5
0 ) [BLaNk] aND + ! ~ [bLank] falsE -- [bLANk] )5
0 [BLank] OR ~ [BLAnk] [BLanK] FALse /*s<o	cP	/{#Os/**/3kz91>*/ 
0 ) [BlaNK] and %20 ! ~ [BLank] fAlSE -- [blaNK] )5
0 [BLAnk] OR ~ %09 [blAnK] FaLsE /*S<O	CP*/ 
0 ) + anD [BLaNk] ! ~ [blaNK] FaLse -- [BLank] )5'Y
0 [BlANk] oR ~ %20 [BLANK] faLSE /**/ 
0 [blank] or ~ %2f [blank] false /*S<o	Cp*/ 
0 [BLaNK] or ~ [BLaNK] [BlANK] fAlse /**/ 
0 [bLaNK] and [blank] nOt [blAnK] 1 /**/ 
0 [bLANK] OR ~ %0C [blaNK] falSe /*S<O	cP*/ 
0 [bLanK] Or ~ + [blANk] FaLsE /*s<o	cP*/ 
0 [bLAnk] || ~ [blAnK] [BlANk] faLSe /*S<o	cP*/ 
0 ) [bLAnK] aNd [bLAnK] ! ~ [bLank] falSE -- [blAnk] )5'Y
0 [BlAnK] Or ~ + [bLaNk] faLSe /**/ 
0 [BLaNk] OR ~ [blanK] [bLanK] fALsE /*s<o	cP'*/ 
0 [blanK] oR ~ [bLANk] [bLaNk] fALsE /*s<o	cP'zL-*/ 
0 [bLanK] or ~ [blanK] [BLank] fAlSE /*S<O	cP'M< |1H*/ 
0 [blANk] Or ~ %20 [BLANk] FAlSe /*S<O	cP*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%203c4,"*/ 
0 [BLank] oR ~ [blANk] [BLanK] FaLsE /*S<O	cP	/{#Os+3*/ hJ
0 [BLANK] OR ~ [blANK] [BlaNk] faLsE /*s<o	cP')*/ 
0 ) [BLaNK] And /**/ ! ~ [blaNK] FaLSE -- [bLAnK] )5|u
0 [BLank] OR /**/ noT + ' ' [BLaNK] 
0 [blank] and /*nq"#*/ not [blank] true %0C l
0 ) [blANk] anD /*euwy=*/ ! ~ [BLaNk] FALse -- [bLank] )5
0 [blANK] Or ~ + [BLAnk] FaLSe /*s<o	CpuxYq*/ 
0 [blAnK] Or ~ [blank] [bLank] FaLse /*s<o	Cp	/{#*/ :7
0 [bLANK] OR ~ [bLaNK] [blAnK] FaLSE /*S<gx&K)u,dus?T{*/ 
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP|h5$6*/ 
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'm< |1*NlDgR*/ 
0 [BLaNK] Or ~ [Blank] [bLANk] FAlse /**/ 
0 [BlAnk] OR ~ [blanK] [bLAnk] fAlsE /*S<o	Cp^*/ 
0 ) [BLank] && + 0 # 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- + )5'Y_n
0 [BLank] or ~ [blAnK] [BLank] fAlSe /**/ 
0 [blank] && /*nq"#7A{*/ not [blank] true %0C [
0 [BlanK] or ~ %09 [BLAnk] FaLSe /*S<O	Cp--?H*/ 
0 [blAnk] oR ~ [bLaNK] [BLANk] FAlsE /*s<o	cp'm< |1**/ 
0 [bLaNk] Or ~ [BLanK] [BlAnk] FALSe /*S<O	CP	/{#]u*/ 
0 [blanK] or ~ [BLaNK] [BlaNK] FALsE /*s<o	CP	/{#Os%203^>_]h*/ 
0 [bLANk] or ~ [BLaNk] [BLanK] fALse /*s<gX&k)00$S;W(G$k*/ 
0 [BlAnK] oR ~ [blAnk] [blANK] FAlsE /**/ 
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'm< |1**/ 
0 ) [BLaNk] ANd %20 ! ~ [BlANK] falSe -- [Blank] *
0 [bLaNK] OR ~ [blAnk] [bLANk] fALsE /*s<O	cp'M< |1:GYK*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |13}UZ%*/ 
0 [blanK] or ~ %09 [BLANk] FalSe /*S<o	cP*/ 
0 [BLanK] or ~ [blank] [bLaNk] FaLse /*S<o	CP'A6*/ 
0 [BlANK] OR ~ [BLAnk] [BlAnk] FALsE /*s<o	Cp	/{#OS/**/3C4,*/ 
0 [BlANK] or ~ [BLanK] [bLANK] fAlSe /*S<O	CP	/{#*/ 
0 [bLanK] Or ~ [BLanK] [BLank] false /*S<o	CP'm< |1H*/ 
0 [bLaNK] OR ~ [Blank] /**/ FaLse /*s<o	CP'm< |1**/ 
0 [bLank] oR ~ [BlanK] [BLANK] fALse /*s<O	CP	/{#oS%203c4,ui*/ 
0 /**/ Or ~ [BLaNk] [BLaNK] 0 [bLAnK] 
0 [bLanK] Or ~ + [blANk] FaLsE /*s<o	cP^*/ 
0 [BLaNK] or ~ [BLaNK] [BlANk] FaLse /**/ 
0 [blANk] or ~ [blank] [bLANk] FAlSE /*S<o	CP	/{#oS/**/3C4,C*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- + )5'Y_n5
0 ) [bLanK] anD /**/ ! ~ [BLANK] fAlsE -- [BlanK] )5
0 [bLaNk] OR ~ [blank] [bLANk] fAlse /**/ 
0 [BlANk] Or ~ [blaNk] [BLANk] faLse /*S<o	cPviK6 j*/ 
0 ) [blAnK] && [bLANK] ! ~ [bLAnk] false -- %20 )5'y9
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CPo*/ 
0 [blAnK] OR /**/ NOT %20 ' ' [bLAnK] 
0 [bLanK] Or ~ %20 [blANk] FaLsE /*s<o	cP%21*/ 
0 ) [blAnk] aND [blank] ! ~ [BlaNK] fAlsE -- [bLANK] )5h
0 [BLANK] or ~ [BlanK] [bLANk] faLSe /*s<GX&K)u,DU*/ 
0 [blank] and /**/ not [blank] true %20 [
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os%203^>_]HRWKd*/ 
0 ) [BLanK] && /*EUwY=j*/ ! ~ [blank] FaLse -- [BlAnk] )5D
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,\*/ 
0 [BLanK] OR ~ [blank] [bLAnK] FalSe /**/ 
0 [BLaNK] or ~ [blAnk] [blaNK] fALsE /*s<o	Cp'm< |19*/ 
0 [BLaNK] Or ~ [Blank] [bLANk] FAlse /*S<o	cP*/ 
0 + OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CP*/ 
0 [blanK] Or ~ [BLaNK] [BLanK] fALSE /*S<gx&Z_3*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS[blank]3kZ91>*/ 
0 ) [blAnK] and [blAnK] ! [BLanK] TRue # 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os[blank]3*/ 
0 [bLaNk] || ~ %20 [BLank] FalSe /*s<O	CP*/ 
0 [BlanK] or ~ [bLAnk] [Blank] False /*s<o	Cp	/{#E*/ 
0 [blanK] Or ~ [bLanK] [bLANk] falSE /*s<O	CP'*/ 
0 [blank] or ~ [Blank] [BlANk] FaLsE /**/ 
0 ) [blANk] && /**/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2
0 [bLank] Or ~ [Blank] [blANK] FaLSE /*s<O	CP	/{#os+3c4,*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS+3c4,*/ 
0 [BlAnK] oR ~ [blank] [BLANk] fAlSe /*S<o	Cp'*/ 
0 [BlAnk] OR ~ [bLaNk] [blanK] faLSE /*s<o	CP	/{#G4j*/ 
0 ) [bLANK] && [blAnK] ! ~ [BlANk] fALSe -- [bLaNk] 
0 [bLaNK] OR ~ [Blank] [blank] FaLse /*s<o	CP'm< |1**/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#Os+3*/ 1s
0 [BlaNK] Or ~ [BLANk] [BlANK] fALSE /*S<O	cP'M< |1w*/ 
0 [bLANk] OR ~ [BLank] [blaNk] FAlSE /*S<o	cP*/ 
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /*s<o	cP'D*/ 
0 [BLANK] OR ~ [bLaNK] [BlAnk] fALse /*S<gX&*/ 
0 ) [bLaNK] anD + ! ~ [blaNK] FaLSe -- [blaNk] )5
0 [blanK] Or ~ [blAnk] [blank] faLSe /**/ 
0 [Blank] OR ~ [BlAnK] [BLAnk] fALSe /*S<O	cP'*/ 
0 ) [BLAnK] AnD /**/ ! ~ [blAnK] faLSe -- [blank] )5
0 [BLANK] && [bLank] 0 [bLank] 
0 [BlANK] || ~ [bLAnk] [BLAnk] fAlsE /*s<o	CP	/{#*/ 
0 ) [blANk] && /*EuWy=*/ ! ~ [bLaNk] fALSE -- [BLaNk] )5
0 [bLANK] Or ~ [BLaNk] [bLANK] FAlSe /*s<o	Cp'kyD*/ 
0 [BlAnK] oR ~ [blank] [BlAnK] False /*S<O	CP	/{#oS/*B*/3c4,*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS/**/3c4,*/ 
0 [bLANk] OR ~ [Blank] [bLAnk] FAlSe /*s<gX&K)u,dU*/ 
0 [bLANk] or ~ [blAnk] [BLANK] faLSE /*S<o	cP'M< |1	_)^*/ 
0 [BLaNk] || ~ [BLank] [Blank] FalsE /*s<gX&K)5p*/ 
0 [blAnK] Or ~ %20 [BLaNk] fALsE /*S<O	CP'M< |1*/ 
0 [Blank] or ~ [BlanK] [BlaNk] FALsE /*s<o	cP'F*/ 
0 [BlAnK] oR ~ [Blank] [bLANK] fALse /*S<Gx&k)U,dUS?t{C`*/ 
0 [BLanK] oR ~ [BlANk] [BLANK] FAlSE /*s<O	cP'M< |1:GyK*/ 
0 ) [BLANK] aNd [BLANk] ! ~ [BLANK] FAlse -- [BLANk] )5'y
0 [BlAnk] OR ~ [bLaNk] [blanK] faLSE /*s<o	CP	/{#*/ *_
0 [BLANk] anD [BLAnK] NOT ~ ' ' /*:t*/ 
0 [BLAnk] or ~ [BLank] [bLank] fAlSe /*s<o	CP')k*/ 
0 [BLANk] Or ~ [blAnk] [BlAnK] faLSE /*s<o	CP*/ 
0 [BlaNK] OR ~ [blAnk] [BLANk] FalSe /*S<GX&k)u,DUS?T{*/ 
0 [Blank] OR ~ [BLanK] [bLANK] faLsE /*s<o	Cp'm< |1*/ 
0 + or ~ [Blank] [BlANk] FaLsE /*s<O	cP'rtW9*/ 
0 ) [bLAnk] && [bLAnk] ! [Blank] tRue # 
0 ) [blANk] ANd /**/ 0 # SK
0 [blank] && /*nq"#*/ not [blank] true %20 [
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#*/ S=
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS[blank]3c4,"*/ 
0 + And [blANk] NOT [BlanK] 1 [bLANk] 
0 [BlANk] oR ~ + [blAnK] faLSe /*s<o	CP'*/ 
0 [bLank] Or ~ [blAnK] [bLAnk] FALSe /*S<O	CP'm< |1*/ 
0 [BlaNK] OR ~ [blank] [bLAnK] FALsE /*S<O	CP	/{#OS/**/3C4,*/ 
0 [BLAnK] OR ~ [blaNk] [bLANk] FAlSE /*S<O	cp	/{#Os[blank]3kZ91>*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,9F{Se*/ 
0 [BlANk] oR ~ [blank] [blAnK] faLSe /*s<o	CP'ZL-*/ 
0 ) [bLanK] anD /*>l^%*/ ! ~ ' ' -- [bLanK] 6
0 [BLAnK] OR ~ [blaNk] [bLANk] FAlSE /*S<O	cp	/{#Os+3kZ91>&*/ 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os%203^>_]hc+8**/ 
0 [BlaNk] oR ~ [BLAnK] [blANk] FALsE /*S<o	cP*/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#Os+3j:s3e*/ `}
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,D|.q!unC7*/ 
0 [BLank] oR ~ [blANk] [BLanK] FaLsE /*S<O	cP	/{#Os+3*/ 
0 ) [BLaNK] AND + ! ~ [bLaNk] FalSe -- [bLAnK] )5
0 [BLank] Or ~ [BLANk] [BLaNK] False /*S<O	CP	/{#Os[blaNk]3J:S3e*/ 
0 ) [bLANK] && [bLank] ! ~ [BlaNk] falsE -- [bLANK] )5
0 [BlANK] OR ~ [bLank] [BlanK] FaLsE /*s<O	cP'm< |1*/ 
0 [blAnk] OR ~ [BLAnK] [BlanK] FALSE /*s<o	cp	/{#*/ Kc
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}$*/ 
0 [BLANK] oR ~ + [blanK] FAlSE /*s<O	cP_6H`;*/ 
0 /**/ && /**/ not ~ [Blank] FALse + 
0 [bLAnK] oR ~ [blAnk] [blAnK] FalsE /*s<o	Cp	/{#Os[bLANK]3j:s3E*/ 
0 [BlAnK] Or ~ [BLaNk] [BLaNk] fALsE /*s<O	cP')*/ 
0 ) [bLaNk] and /**/ ! ~ ' ' -- [BlaNK] 6
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- + )5'Y
0 ) [BlANK] ANd [blaNk] ! ~ [blaNK] FaLsE -- [blANk] )5
0 [BLaNK] or ~ [BLaNK] [BlANK] fAlse /*s<GX&z_3*/ 
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#*/ 
0 [bLanK] Or ~ [blANk] [BLaNk] fALSe /*s<o	cP'm< |1*NLDgr*/ 
0 [BLANK] oR ~ [Blank] [bLANk] falSE /*S<o	Cp'M< |1D*/ 
0 ) [blANk] && /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2S
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#Os%203*/ V
0 [blanK] Or ~ [BlAnk] [bLaNK] falSE /*s<O	CP'M< |1*nLDgr*/ 
0 [BlANk] oR ~ %20 [BLANK] faLSE /*s<o	cp'm< |13*/ 
0 [BlANK] OR ~ [bLANK] [blANk] FAlSE /*s<O	Cp&!	!Qjjs\[`e&a\*/ 
0 [BlAnk] OR ~ [bLaNk] [blanK] faLSE /*s<o	CP	/{#*/ )
0 ) [BLank] Or ~ [bLank] [bLANK] FaLse # EH
0 [bLANk] OR ~ [BLank] [blaNk] FAlSE /**/ 
0 [bLANK] OR ~ %0A [blanK] FaLse /*S<o	cP')*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*NlDgR2d*/ 
0 [bLANK] OR ~ %2f [blanK] FaLse /*S<o	cP')*/ 
0 + OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}S*/ 
0 [bLaNk] or ~ [blAnK] [blANk] fAlSE /*s<o	CP	/{#os[BLanK]3J:S3E*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,"*/ s
0 ) [bLAnK] && [bLAnK] ! ~ [blANk] FalsE -- %20 )5'Y:
0 [bLAnk] or ~ %0D [blaNk] faLsE /*S<O	cP*/ 
0 [BlAnk] OR ~ + [BLAnK] FAlsE /*S<O	cP*/ 
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CP:Mw'*/ 
0 [BlaNk] || ~ [bLANk] [bLANk] fALSe /*S<o	Cp'm< |1*NLdgr*/ 
0 ) [blANk] and /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2/
0 [bLAnk] oR ~ [blANk] [BLaNK] false /*S<O	Cp5*/ 
0 [bLanK] and [bLANK] NoT [blANK] TrUe %09 
0 [bLanK] Or ~ [blank] [blANk] FaLsE /*s<o	cP*/ 
0 [blANk] oR ~ [blank] [bLANK] false /*s<O	Cp')*/ 
0 [BLAnK] OR ~ [blANK] [bLANK] faLsE /*S<O	Cp	/{#os%203C4,*/ 
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#*/ 
0 [blanK] or ~ + [blanK] falsE /*S<O	CP'*/ 
0 [BlANK] AnD [BlANK] ! ~ [BlANK] FaLSE [blANk] 
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/*g1l;*/3c4,c*/ 
0 [BlanK] or ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#E*/ eH
0 [BLAnK] or ~ %0a [BLANk] False /*S<o	Cp'*/ 
0 [BlAnk] Or ~ [BLANk] [BlaNk] fALse /*s<O	cP*/ 
0 ) [BLANK] anD [BlANK] ! ~ [BlAnK] fALSE -- [BlaNK] )5
0 [blANK] OR ~ [blANk] [BlanK] FalsE /*s<o	cP	/{#oS+3c4,*/ 
0 [blank] or [blank] true [blank] 
0 [bLanK] Or ~ + [blANk] FaLsE /*s<o	cP%21*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3*/ B_
0 ) [BlAnk] && [bLANK] ! ~ [bLANK] faLsE -- + )5'Y
0 ) [BLanK] anD + ! ~ [BLANK] FAlse -- [blanK] )5
0 [BLAnk] OR ~ [Blank] [BlanK] fAlSE /*S<O	cP	/{#*/ 
0 ) [blANK] aND /*euwY=*/ ! ~ [bLANK] FaLsE -- [BlanK] )5
0 [BlANk] oR ~ [bLANK] [blANk] fALSE /**/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3kZ91>\y*/ 
0 ) [Blank] aND %2f NOt [BlAnK] tRUE # 
0 ) [BLaNK] And /*EUw*/ ! ~ [blaNK] FaLSE -- [bLAnK] )52F
0 ) [BlaNK] && /**/ ! ~ [blanK] FalSE -- [BlanK] )5
0 [BlaNK] OR ~ [BlAnk] [BLANK] FALSe /*S<o	Cp'Bx6*/ 
0 [BlANk] oR ~ %20 [blAnK] faLSe /*s<o	CP'ZL-*/ 
0 [blAnK] Or ~ [BlaNk] [BLaNk] FALsE /*S<o	CP'RTw9*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS/**/3c4,*/ 
0 [blank] and /*nq"#*/ not [blank] true %20 [
0 [blaNk] OR ~ [BlANk] [bLANk] FAlSE /*s<O	Cp'M< |1h*/ 
0 [bLaNk] oR ~ [BlanK] [BLAnk] FaLse /*s<o	cp	/{#oS/**/3C4,*/ 
0 ) [BLAnk] && /**/ ! ~ [Blank] fAlSe -- [BLank] )5
0 [blANK] OR ~ [BLANk] [BLAnK] fALSE /*s<o	cP^*/ 
0 [blAnK] OR /*ikv*/ NOT [blank] ' ' [bLAnK] 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,*/ m&
0 ) [BLANk] or [BLaNK] ! %0a [bLaNk] FAlSe # ]b6*
0 [bLAnk] or ~ [blAnK] [BlANk] faLSe /**/ 
0 ) [bLAnK] anD /*eUwY=jFDK,*/ ! ~ [BLaNk] FALSE -- + )5
0 [BlANK] oR ~ [bLAnk] [BLanK] fAlse /*S<GX&K)*/ 
0 [BLaNk] oR ~ [blanK] [BlaNK] fAlSE /*s<o	cp'F*/ 
0 [blank] Or ~ [bLANk] [BLaNK] FAlse /*s<gX&*/ 
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os%203^>_]H*/ r 
0 ) %0a Or ~ [blAnK] [blaNk] FaLSE # E
0 [blank] oR ~ [BLanK] [BlaNK] FalSe /*S<O	CP*/ 
0 [BlANK] OR ~ [bLANK] [blANk] FAlSE /*s<O	Cp&!	!Qjjs\[*/ 
0 [bLANK] or ~ + [BLAnK] false /**/ 
0 /*u;~P*/ OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CP*/ 
0 [bLank] Or ~ + [BLAnK] FAlSE /*S<O	CP'*/ 
0 [BLAnK] || ~ [BlanK] [BlaNk] faLse /*s<o	Cp	/{#e*/ 
0 [BlAnk] or ~ [blAnK] [BlanK] fAlse /*s<O	Cp	/{#e*/ 
0 [blANK] Or ~ [BlanK] [blAnk] FAlse /*s<GX&k)u,du*/ 
0 [bLAnk] OR ~ [bLANk] [BLaNk] fAlSE /*s<o	CprKMw}*/ 
0 ) [bLaNk] aND /**/ 0 # sK6
0 [blanK] Or ~ [blAnk] [blank] faLSe /*S<o	cP'j*/ 
0 [BlANk] oR ~ %20 [blAnK] faLSe /*s<o	CP'*/ 
0 [BLANk] oR ~ [BlanK] [BlaNK] False /*s<O	cp'*/ 
0 [BLank] OR ~ [BLAnk] [BLanK] FALse /*s<o	cP	/{#Os%203kz91>*/ 
0 ) [BLaNK] And /**/ ! ~ [blaNK] FaLSE -- [bLAnK] )5
0 [blanK] OR ~ [bLAnK] [BlAnk] FaLse /*s<GX&*/ 
0 [BlaNK] or ~ [BLank] [Blank] FalSE /*S<O	cp'M< |1*/ 
0 [blAnk] oR ~ [BlANK] [blank] FaLSE /*s<O	Cp	/{#*/ 
0 [bLaNk] OR ~ [blank] [bLANk] fAlse /*s<O	cP*/ 
0 [BLank] oR ~ [blANk] [BLanK] FaLsE /*S<O	cP	/{#Os[blank]3*/ 
0 [blAnK] Or ~ [bLaNK] [BLAnk] FaLSe + 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,*/ 
0 [bLaNk] OR ~ %20 [bLANk] fAlse /**/ 
0 [BLANK] or ~ + [bLAnK] FAlse /**/ 
0 [blAnK] or ~ %09 [BLAnK] FAlse /*S<o	Cp*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1*/ 
0 [bLank] OR ~ [bLaNK] [BlaNK] False /*s<O	Cp'm< |1h*/ 
0 ) [BlaNk] ANd [bLAnk] ! ~ [bLaNk] FaLsE -- %20 )5'y:
0 [blANK] Or ~ %20 [BLAnk] FaLSe /*s<o	Cpu*/ 
0 [BlANk] oR ~ [blank] [blAnK] faLSe /**/ 
0 [BlANk] or ~ [BlAnk] [blaNk] FalsE /*S<*/ 
0 [BLanK] OR ~ [blank] [bLAnK] FalSe /*s<O	CP'.#*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1Ss*/ 
0 ) [BlaNk] AnD [BlAnk] FALsE [BLAnK] OR ( 0 
0 [BlANk] OR ~ %09 [bLAnk] fAlse /*s<o	cp|H*/ 
0 [BLAnk] OR ~ [bLAnk] [BLANk] FALse /*s<o	CP	/{#OS+3c4,n{x*/ 
0 ) [BlanK] and [BlANK] 0 # 
0 ) [bLaNK] && [BLaNk] ! ~ [blANk] fAlSe -- [blank] )5
0 [blank] && /*nq"#*/ not [blank] true %09 [
0 [bLaNk] OR ~ %20 [bLANk] fAlse /*s<O	cP*/ 
0 [BlanK] || ~ [bLAnk] [Blank] False /*s<o	Cp	/{#E*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%203c4,c*/ 
0 [BLaNk] or ~ [BLanK] [bLAnK] FAlSe /*S<O	cP*/ 
0 [BLAnK] OR ~ [blaNk] [bLANk] FAlSE /*S<O	cp	/{#Os+3kZ91>*/ 
0 [blanK] oR [blaNk] tRUe [blaNK] 
0 ) [BLank] aNd /*eUwY=JfDK,*/ ! ~ [BLaNK] FaLSe -- + )5
0 ) [blANk] and /**/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2
0 [blAnk] oR ~ [BlANK] /**/ FaLSE /*s<O	Cp	/{#*/ 
0 [Blank] Or ~ + [BLAnK] FalsE /*s<o	cP*/ 
0 [bLANK] oR ~ [BlaNk] [blaNK] faLSE /*S<O	Cp*/ 
0 [BLank] || ~ [blAnK] [BLank] fAlSe /*S<gX&*/ 
0 ) [blaNk] && [blANk] ! [BlanK] tRUE # 
0 [BlaNk] Or ~ %20 [BlANK] faLsE /*S<O	cP')*/ 
0 [bLaNk] OR ~ [blank] [bLANk] fAlse /*s<O	cP#x;$S*/ 
0 [blank] or ~ [blank] [blank] false /*S<o	Cp	/{#Os+3C4,*/ qm
0 [bLANk] or ~ [BLank] [BlanK] FAlse /*S<O	cP*/ 
0 [bLaNk] || ~ [blank] [BLank] FalSe /*s<O	CPR*/ 
0 [BlaNK] or ~ [BLank] [Blank] FalSE /*S<O	cp'M< |1W*/ 
0 [BLaNk] OR ~ [blanK] [BLaNk] falsE /*s<o	CP	/{#]u*/ 
0 ) [bLanK] And /*EUwk*/ ! ~ /**/ FaLSe -- [bLAnK] )5
0 [blaNk] Or ~ [BLANK] [BLaNk] falsE /*s<o	CP*/ 
0 [blANk] oR ~ + [bLANK] false /*s<O	Cp')*/ 
0 [BlANk] OR ~ [bLANk] [bLanK] false /**/ 
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CP*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS[blank]3c4,*/ 
0 [blANK] Or ~ [blanK] [BlaNk] fAlSe /*s<o	cP'n`*/ 
0 [bLAnk] OR ~ [blAnK] [BLANK] FalSE /*s<o	CP')*/ 
0 [BlANK] AND /**/ ! [BLanK] TrUe [blaNK] 
0 [BlanK] || ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#E*/ 
0 [BLaNk] OR ~ [blanK] [bLanK] fALsE /**/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS[blank]3c4,UI*/ 
0 [BLAnk] Or ~ [blaNk] [BlaNK] fAlsE /*S<o	cP&!	!QjJs\[*/ 
0 ) [blANk] && /*EuW^Lhi*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2F	
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1jr*/ 
0 [BLAnk] && /*Nq"#*/ noT [blank] tRuE %20 [
0 [Blank] && /**/ NOT /**/ 1 %20 
0 ) [BLAnK] AND /*EuWU*/ ! ~ [blaNk] faLsE -- [bLANk] )5M
0 [blank] && /*nq"#*/ not [blank] true %09 l
0 [BLanK] || ~ %20 [bLaNk] FaLse /*S<o	CP'*/ 
0 [BLank] Or ~ [blANk] [Blank] fALSe /*s<o	Cp	/{#oS+3C4,*/ 
0 [bLank] Or ~ [Blank] [blANK] FaLSE /*s<O	CP	/{#os%203c4,*/ 
0 [blank] && /*nq"#*/ not [blank] true %09 [n	
0 ) [blank] and [blANk] 0 # ">
0 [blank] OR ~ [BLaNk] [BLaNk] FalsE /*S<O	CPRKmW}S)Y4e7*/ 
0 [blANK] Or ~ [blank] [BLAnk] FaLSe /*s<o	Cpu*/ 
0 ) [BlaNk] ANd /*EuW*/ ! ~ [BlaNK] FALSe -- [BLAnk] )5
0 ) [BLAnK] AnD /*eUWy=*/ ! ~ [blAnK] faLSe -- /**/ )5
0 [bLaNk] or ~ [blank] [BLank] FalSe /*s<O	CP&!	!q*/ 
0 [blANK] Or ~ %09 [BLAnk] FaLSe /*s<o	Cp*/ 
0 [bLank] or ~ [BlaNK] [BLANk] FALSE /*s<O	CP	/{#oS+3kz91>*/ 
0 [blank] OR ~ [blAnk] [BlANk] FALSe /*s<o	cp')*/ 
0 ) [blank] AnD [BlANK] 0 # 
0 [bLAnk] && [BlAnk] falSE [bLaNk]
0 [blanK] OR ~ [blank] [blAnk] fAlSE /*s<o	cp*/ 
0 [BlAnk] oR ~ [bLanK] [blank] fALse /*S<O	cP*/ 
0 [blanK] oR ~ %20 [blANK] FaLse /*s<O	cP%21*/ 
0 [blANK] or ~ [BLaNk] [Blank] fAlse /**/ 
0 [BLAnk] oR ~ [bLanK] [bLaNK] fAlSe /*s<o	cp'*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS+3c4,*/ D"
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*yI,FYN3*/ 
0 [BLanK] oR ~ [blanK] [bLAnK] FaLse /*s<o	cp'M< |1}*/ 
0 [BLank] OR ~ [BlAnK] [bLaNk] fAlsE /*s<o	cp	/{#OS%203^>_]h*/ 
0 [BlAnk] OR ~ [bLaNk] [blanK] faLSE /*s<o	CP	/{#*/ k
0 [BLanK] OR ~ + [bLAnK] FalSe /*s<O	CP'*/ 
0 [BLank] Or ~ [blANk] [Blank] fALSe /*s<o	Cp	/{#oS/**/3C4,*/ +4
0 [BLanK] OR ~ + [bLAnK] FalSe /**/ 
0 [bLanK] Or ~ %2f [blANk] FaLsE /*s<o	cP*/ 
0 ) [blAnk] and /*eUW*/ ! ~ [blank] FaLse -- [BlaNK] )5
0 [blAnk] oR ~ [BlANK] %20 FaLSE /*s<O	Cp	/{#*/ 
0 [bLanK] Or ~ %20 [blANk] FaLsE /*s<o	cP*/ 
0 [blanK] OR ~ [blaNk] [BLanK] fALse /*S<O	cp'&O*/ 
0 [bLaNk] or ~ %20 [BLank] FalSe /*s<O	CP&!	!qJJs\[*/ 
0 [blank] or ~ %2f /**/ false /*S<o	Cp'*/ 
0 [bLaNK] oR ~ [bLANK] [BLANk] FaLSe /**/ 
0 [bLAnk] Or ~ [blANK] [bLAnK] false /*S<o	Cp*/ 
0 [BLaNk] oR ~ [bLanK] [BLANK] FALsE /*s<o	Cp'f*/ 
0 [BLanK] or ~ %0A [bLaNk] FaLse /**/ 
0 [blaNk] oR ~ [bLANK] [BLaNK] fAlse /*S<O	Cp	/{#Os/**/3C4,*/ ?
0 ) [blank] or /**/ ! %2f [blank] false # 
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,D|.q*/ +G
0 [BlaNk] or ~ [bLANk] [bLANk] fALSe /*S<o	Cp'm< |1*NLdgrn4*/ 
0 [bLaNK] && [blank] nOt [blAnK] 1 /*;N*/ 
0 [BLAnk] and /*Nq"#*/ noT [blank] tRuE %20 [
0 [blank] OR ~ %20 [BLANk] FAlsE /*S<o	cp').Vf*/ 
0 [blank] and [blank] false [blank]
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os+3m */ 
0 [BLaNK] Or ~ [blaNK] [blanK] FAlse /*s<O	cP	/{#]u*/ `
0 [blank] && ' ' [blank]
0 ) [Blank] aND %0A NOt [BlAnK] tRUE # &6{k
0 [blank] or ~ %2f [blank] false /*S<o	Cp'*/ 
0 [BLAnk] Or ~ [blAnK] [bLaNK] falSE /*s<gx&K)X7*,3*/ 
0 [BlAnK] Or ~ [blanK] [BLANK] faLSE /*S<o	cpvIk6 J*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- /**/ )5'Y
0 [blank] OR ~ %20 [BLANk] FAlsE /*S<o	cp')*/ 
0 ) [BlANK] Or [blANK] ! %0A [BLANK] FALSe # ]b
0 [BLAnk] OR ~ [bLAnk] [BLANk] FALse /*s<o	CP	/{#OS+3c4,*/ 
0 [bLank] OR ~ %20 [bLank] fAlsE /*s<o	Cp'*/ 
0 [blanK] oR ~ [BlANk] [BlaNk] FaLSE /*S<O	Cp'M< |1h*/ 
0 ) /**/ && [bLaNK] ! [blANk] TRUe -- [bLanK] t
0 ) [bLAnk] Or ~ [bLAnK] [bLAnK] FalsE # 
0 ) [BLAnK] AnD /*eUWy=*/ ! ~ [blAnK] faLSe -- + )5(
0 [BlAnK] OR ~ %09 [bLANK] FalsE /**/ 
0 [bLAnK] or ~ [blANk] [BLanK] falSE /*s<O	CP'M< |1h*/ 
0 [bLANk] or ~ [blAnk] [BLANK] faLSE /*S<o	cP'M< |1*/ 
0 [BLaNK] or ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1}*/ 
0 [BLaNK] Or ~ [blaNK] [blanK] FAlse /*s<O	cP	/{#]uy]{*/ 
0 ) [Blank] ANd [blank] ! ~ [BLank] False -- %09 )5'Y:
0 [bLANK] Or ~ [BLaNk] [bLANK] FAlSe /*s<o	Cp'*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |197*/ 
0 [blANK] or ~ [BLaNk] [Blank] fAlse /*s<o	cP*/ 
0 [BlANk] oR ~ + [BLANK] faLSE /*s<o	cp'm< |1**/ 
0 ) [bLanK] And /*EUw*/ ! ~ [blank] FaLSe -- [bLAnK] )57
0 [bLANk] Or ~ [bLanK] [bLAnk] faLSE /*s<o	cp'*/ 
0 ) [bLaNk] anD /**/ ! ~ ' ' -- [blaNk] 
0 [bLANK] or ~ + [BLAnK] false /*S<o	Cp*/ 
0 [BLAnk] OR ~ [Blank] [BlanK] fAlSE /*S<O	cP	/{#CHL*/ 
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS+3c4,*/ $
0 ) [blanK] && /**/ ! ~ [blANk] FaLsE -- [bLaNk] 
0 [BLANK] or ~ [Blank] [BLANk] fAlSe /*S<o	CP*/ 
0 [BLanK] or ~ %0A [bLaNk] FaLse /*S<o	CP'*/ 
0 [Blank] OR ~ [blAnk] [BLAnk] faLse /**/ 
0 [BLAnk] Or ~ [blAnK] [bLaNK] falSE /*s<gx&K)k9h
*/ 
0 [Blank] or ~ [blaNK] [Blank] falsE /*S<o	CP	/{#os+3c4,*/ 
0 [bLAnK] oR ~ [blAnK] [BlANK] falSE /*S<o	cP'm< |1*/ 
0 [BLanK] OR ~ + [bLAnK] FalSe /*s<O	CP'Z~q\*/ 
0 ) [blAnk] AnD /**/ ! ~ [bLaNk] FaLsE -- [bLank] *
0 [blank] && /*nq"#7A{x\<*/ not [blank] true %20 [
0 ) [bLaNK] && /*EUw^lHI*/ ! ~ [bLANk] FAlse -- [bLAnK] )5o2
0 [BlAnK] OR ~ [blANK] [BlANK] FAlSE /*S<O	CP*/ 
0 [blAnk] Or ~ [Blank] [BLaNK] FAlSE /*s<o	cPU*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS+3c4,c*/ 
0 [BLaNK] || ~ [BLaNK] [BlANK] fAlse /*s<GX&*/ 
0 [BlaNK] OR ~ [BlAnk] [BLANK] FALSe /*S<o	Cp';HV%@*/ 
0 [blank] && [blank] 0 [blank] 
0 [BLank] Or ~ [BLAnK] [bLAnK] FAlSE /*s<O	Cp	/{#oS/**/3C4,*/ 
0 [bLANK] or ~ [bLanK] [BLaNK] FaLsE /*S<O	CP^BT*/ 
0 [BlANk] Or ~ [BlAnk] [blAnK] falsE /*S<O	cP	/{#Os/**/3c4,*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS[blank]3c4,*/ 
0 [blAnk] OR ~ [BLAnK] [BlanK] FALSE /*s<o	cp	/{#*/ 
0 [bLaNk] or ~ %20 [BLank] FalSe /**/ 
0 [BLANK] OR ~ [bLaNK] [BlAnk] fALse /**/ 
0 [blanK] and [BLANk] NoT [blaNK] trUE /**/ 
0 /**/ && /**/ NoT ~ [blANk] fALSE + 
0 ) [BLank] && /**/ ! ~ [BlAnK] FALsE -- [BLanK] )5
0 [bLAnk] oR ~ %09 [BlaNk] faLsE /**/ 
0 [BLANk] or ~ [BLANK] [bLaNK] fALse /*s<O	cP'f*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS%0C3c4,"*/ 
0 ) [BLAnK] AnD /**/ ! ~ [blAnK] faLSe -- + )5
0 [BlANk] oR ~ %0C [BLANK] faLSE /*s<o	cp'*/ 
0 [bLaNK] Or ~ [BlANK] [bLanK] fAlsE /*S<o	Cp	/{#oS+3Kz91>*/ 
" /**/ && [BlaNK] nOt [bLank] 1 [bLank] oR " 
0 [BlANK] or ~ [bLAnK] [bLanK] FALsE /*s<o	cP'm< |1*/ 
0 [BlANK] Or ~ [BLank] [blAnk] fALSE /*s<gX&k)U,DU*/ 
0 [BLAnk] or ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1:GYk*/ 
0 [blANK] Or ~ %0A [BLAnk] FaLSe /*s<o	Cp*/ 
0 [BLAnk] OR ~ %0C [blAnK] FaLsE /*S<O	CPIS`*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*NlDgR*/ 
0 [BLAnk] Or ~ [blAnK] [BlANK] fAlsE /*S<O	cP	/{#G4j*/ 
0 [BLank] Or ~ [blANk] [Blank] fALSe /*s<o	Cp	/{#oS/**/3C4,*/ Jf
' ) [bLANk] or /**/ NOt /**/ ' ' # 
0 ) [Blank] aND + NOt [BlAnK] tRUE # 
0 /**/ OR ~ [blAnk] [BlANk] FALSe /*s<o	cp')*/ 
0 ) [bLanK] ANd + ! ~ [BLanK] FALSe -- [blANk] )5
0 [BlaNK] Or ~ [bLAnk] [bLANk] FALsE /*s<O	cp	/{#e*/ 
0 [bLAnK] Or ~ [bLank] [blaNk] FaLsE %20 
0 [BLANK] oR ~ [Blank] [bLANk] falSE /*S<o	Cp'M< |1*/ 
0 [BLanK] AND /**/ ! [BLANK] TRUE [blANk] 
0 ) [blaNK] oR ~ [BLAnK] [blanK] falSe # e
0 [BlANK] Or ~ [BLank] [blAnk] fALSE /*s<gX&k)U,DUHr,*/ 
0 [BlANk] oR ~ [blank] [BLANK] faLSE /*s<o	cp'm< |1*yI,F*/ 
0 [blAnK] oR ~ [BlaNK] [BLANk] FalSe /*s<o	cp	/{#oS/**/3c4,*/ oD
0 [BlaNk] or ~ [BlaNk] [BLanK] FALsE /*S<O	CP	/{#OS%203^>_]H*/ 
0 [blANK] anD %20 nOt [BLaNk] 1 /**/ 
0 [BlanK] or ~ [BlAnK] [bLaNK] falSE /*s<gX&k)00$S;W(*/ 
0 [bLAnk] ANd [bLaNk] NOT ~ ' ' /*:t*/ 
0 [blanK] Or ~ + [BLAnK] False /*S<o	cP*/ 
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cP--?H#*/ 
0 [BLank] oR ~ [BLANk] [bLank] faLSe /*S<O	cp	/{#*/ 
0 [blANk] OR ~ [BLank] [blANk] faLSe /*S<O	cP&!	!qJJs\[*/ 
0 [bLaNK] OR ~ [Blank] + FaLse /*s<o	CP'm< |1**/ 
0 [BLAnk] || ~ [blAnK] [bLANk] FALsE /*S<o	cp'm< |1*/ 
0 [BLaNK] Or [BLAnk] truE [blaNK] 
0 ) [blANk] && /*EuW*/ ! ~ [BLaNk] FalSe -- [bLAnk] )5O2/
0 ) [BLank] && /*EUW*/ ! ~ [BlAnk] faLSe -- [blaNk] )5o2
0 [BLaNK] || ~ [blaNk] [blaNk] FAlsE /*s<o	CP'm< |1*/ 
0 [bLank] OR ~ [bLaNK] [BlaNK] False /*s<O	Cp'm< |1hYy*/ 
0 + OR ~ [blAnk] [BlANk] FALSe /*s<o	cp')*/ 
0 [bLaNK] oR ~ [bLANK] [bLaNK] FALse /*S<O	cp	/{#Os+3^>_]H*/ 
0 [BlAnK] OR ~ [blANK] [BlANK] FAlSE /**/ 
0 [blank] oR ~ %20 [blAnK] FaLSE /*s<o	cp%21*/ 
0 [blAnK] or ~ [BlaNK] [blAnk] faLsE /*S<o	cP	/{#OS/**/3C4,*/ 
0 [BlaNK] or ~ [BLank] [Blank] FalSE /*S<O	cp'M< |1/N*/ 
0 [bLANK] Or ~ [blANK] [BlANk] FAlSe /*S<o	cP	/{#OS/**/3c4,!O*/ 
0 [blaNK] oR ~ + [BlANk] falsE /*S<O	cPU*/ 
0 ) [blANk] and + ! ~ [blAnk] FalSe -- [BLanK] )5qY
0 ) [bLANk] anD + ! ~ [BlANK] fAlsE -- [BlaNK] *
0 ) [BlaNk] Or ~ /**/ /**/ FalSe -- [BlANk] 
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cP|hRqj<,*/ 
0 ) [bLAnk] and /*euWY=J#xGZd*/ ! ~ [bLaNk] false -- [BLANk] )5
0 [bLAnk] OR ~ %2f [bLaNk] FalSe /*s<O	cP|h*/ 
0 [bLank] Or ~ [Blank] [blANK] FaLSE /*s<O	CP	/{#os[blank]3c4,*/ 
0 [bLaNK] || ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os+3c4,*/ 
0 [BLank] Or ~ + [bLANK] fAlse /*s<o	cp'*/ 
0 [BlanK] || ~ [bLANK] [BlAnK] fALSE /*S<o	cP	/{#*/ 
0 [blank] && /**/ not [blank] true %0C [
0 [blaNk] OR ~ [BLaNk] [bLANK] false /*S<O	cP	/{#os/**/3*/ 
0 [BlaNk] Or ~ [bLank] [bLAnK] FALSe /*s<O	cP	/{#Os%203^>_]H*/ 
0 [BlANK] || [Blank] TRUE [bLANK] 
0 [BlaNk] OR ~ [blANK] [BlANk] FaLsE /*s<O	cp'M< |1:gyK,y"*/ 
0 ) [BlAnk] anD /**/ ! ~ %20 False -- [bLaNK] )5
0 %20 or ~ [Blank] [BlANk] FaLsE /*s<O	cP'*/ 
0 [blAnK] OR /*ikv$Z*/ NOT %20 ' ' [bLAnK] 
0 [bLAnk] OR ~ %0D [bLaNk] FalSe /*s<O	cP*/ 
0 [BlAnk] oR ~ [BlANk] [BlANK] falSE /*s<GX&K)5pqvV*/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS+3kZ91>*/ 0
0 [bLAnk] oR ~ %09 [BlaNk] faLsE /*S<O	cp$/*/ 
0 [blAnK] Or ~ [blank] [BLaNk] fALsE /*S<O	CP'M< |1^*/ 
0 ) [BlanK] and /*EUW*/ ! ~ [BlanK] FALse -- [blANk] )5O2/
0 [bLaNK] or ~ [BLANK] [BlANk] False /*S<O	cp	/{#Os[blank]3c4,*/ 
0 [BlAnK] oR ~ [blAnk] [blANK] FAlsE /*s<O	Cp'D*/ 
0 [blANk] Or ~ [BlAnk] [bLank] FALsE /*S<O	Cp'M< |1TE_g>*/ 
0 [bLAnk] OR ~ %09 [bLaNk] FalSe /*s<O	cPY%Z#*/ 
0 [bLaNk] or ~ + [BLank] FalSe /*s<O	CP&!	!q*/ 
0 [blaNk] oR ~ [bLANK] [BLaNK] fAlse /*S<O	Cp	/{#Os/**/3C4,*/ 
0 [BLanK] || ~ + [bLaNk] FaLse /*S<o	CP'*/ 
0 [blAnk] oR ~ [BlANK] [blank] FaLSE /*s<O	Cp	/{#
,y]O*/ 
0 [BLANk] oR ~ [blAnK] [bLANk] fALsE /*s<O	Cp	/{#*/ 
0 [bLAnk] OR ~ [BlAnk] [blank] faLSe /*s<GX&K)U,du'b_`E*/ 
0 [bLAnK] oR ~ [blAnK] [BlANK] falSE /*S<o	cP'm< |1T+F1*/ 
0 ) [BlAnk] and [bLANK] ! ~ [bLANK] faLsE -- %20 )5'Y
0 ) [blanK] OR [Blank] ! [BlANK] /**/ 0 /**/ OR ( 0 
0 [bLANK] OR ~ %0D [blaNK] falSe /*S<O	cP--?H*/ 
0 [bLANk] OR ~ [BlaNK] [bLANK] FAlSE /*s<o	Cp%21*/ 
0 [blanK] Or ~ [blAnk] + faLSe /*S<o	cP'*/ 
0 ) [BLAnk] Or ~ [BLaNk] [blAnK] fAlse # E
0 [BLAnK] OR ~ [blANK] [bLANK] faLsE /*S<O	Cp	/{#os+3C4,K]
**/ 
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS%203*/ 
0 [bLAnk] oR ~ %0D [BlaNk] faLsE /*S<O	cp$/*/ 
0 [bLanK] anD [BLANK] ! ~ [BLAnK] fALSe [bLaNK] 
0 ) [Blank] aND %0A NOt [BlAnK] tRUE # &6{k3
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,UI*/ 
0 [blaNK] Or ~ [bLaNK] [blaNk] FAlSe /*S<o	Cp'M< |1*/ 
0 [blank] OR ~ [BLanK] [BLanK] FAlSE /*S<o	cP	/{#os%203*/ 
0 [BlanK] OR ~ %20 [bLaNK] fALsE /*S<O	CP')*/ 
0 [BLanK] or ~ + [bLaNk] FaLse /*S<o	CP'*/ 
0 [blaNk] or ~ [BlAnk] [blaNK] FAlSe /*S<o	cP	/{#Os%203^>_]h*/ 
0 /**/ ANd /*vk*/ Not ~ [blANk] faLse /**/ 
0 ) [blank] or [blank] ! %2f [blank] false # 
0 [BLaNk] oR ~ [blanK] [BlaNK] fAlSE /**/ 
0 [BLAnk] Or ~ [blAnK] [bLaNK] falSE /*s<gx&K)^**/ 
0 ) [blAnk] AnD %20 ! ~ [bLaNk] FaLsE -- [bLank] *
0 [BLaNK] OR ~ [BLANk] [BlAnk] FalSE /*S<o	Cp	/{#*/ 
0 ) [BlAnk] && [blAnK] ! ~ [BlANk] FalSe -- %20 )5'Y
0 ) [blANk] aNd + ! ~ [bLank] fAlse -- [bLank] )5
0 ) [blank] and [blANk] 0 # ">
0 [BLaNk] or ~ [blaNk] [Blank] falSe /*s<O	Cp	/{#oS%203c4,*/ 
0 [BLaNk] or ~ [BLank] [Blank] FalsE /*s<gX&K)5p*/ 
0 ) [blanK] and %20 ! ~ [BLaNK] FAlSe -- [BLANK] *
0 ) [BLaNk] OR [BlaNk] ! %0a [BLAnk] FALse # ]b
0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O	cPmp */ 
" ) /**/ ANd /**/ ! ~ [BLaNk] FAlsE # 
0 /**/ AND /*P}I*/ not ~ [bLaNk] False [bLANk] 
0 [blAnk] OR ~ %20 [bLank] fALse /*S<o	cP*/ 
0 [BlaNK] oR ~ %09 [blANK] FAlse /*S<O	cp$*/ 
0 [bLAnk] OR ~ [blANK] [BLaNK] fALsE /*S<O	cp	/{#]U*/ 
0 [bLaNk] OR ~ [blaNK] [bLanK] fAlsE /*S<O	Cp	/{#oS+3C4,*/ 
0 [bLAnk] oR ~ %09 [BlaNk] faLsE /*S<O	cp$J}*/ 
0 [bLAnk] Or ~ [BlANK] [bLAnk] falsE /*S<gx&K)U,DuS?T{*/ 
0 ) [BLAnK] AND /*EuWU*/ ! ~ [blaNk] faLsE -- [bLANk] )5
0 [BlAnk] oR ~ [BlANk] [BlANK] falSE /*s<GX&K)5p*/ 
0 [bLAnk] or ~ [blAnK] [BlANk] faLSe /*S<o	cP5*/ 
