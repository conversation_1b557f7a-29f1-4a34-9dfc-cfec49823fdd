0 /**/ and /**/ ! [BlANK] true /*to|1X*/ 
0 %20 && %0c noT ~ [BLaNk] FALSE /*i7RRtNF]*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /**/ \v
0 ) /**/ && /**/ ! ~ [Blank] FAlsE [bLank] || ( 0 
0 /*X&Er#[tEG%Rh5*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"
0 /**/ and %20 NOt ~ [BlaNK] faLSE /*I*/ 
0 /**/ and /**/ NOT ~ [blanK] fALsE [BlAnK] 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \
0 /**/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21
0 ) [BLANK] AND %20 0 # DM
0 [BLANk] AND + ! [bLAnK] tRuE /*%~E*/ 
0 /*x&eR#[gH)4*/ && %0D not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %2f not ~ + false /*i*/ \
0 /*x&eR#[*/ && %20 not ~ [blank] false /**/ \CK
0 /*A|Vq*/ and %20 Not ~ [bLank] FAlse /*Iu|
\*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \q
0 [BLAnk] AnD /**/ ! [blAnK] truE /**/ 
0 [bLanK] && [blaNk] ! [bLanK] 1 /**/ 
0 /*x&er#[*/ && %0A NOT ~ [blANk] faLsE /*i7R8<l&Z*/ &"
0 /**/ && %0C NOT ~ [bLANK] falSe /*i7r*/ &"6w
0 [BlAnK] aNd [blank] ! [bLaNk] 1 /*n
s8x*/ 
0 /**/ anD /**/ NOt ~ [bLanK] fAlSe [bLAnk] 
0 /*x&eR#[*/ && %2f not ~ + false /*i*/ \CK
0 /*x&ER#[*/ && %0d Not ~ [BLaNk] FALse /*I7r=G~K*/ &"
0 /*x&er#[*/ && %0D noT ~ [blANk] FaLSe /*I7R*/ &">a
0 /*X&er#[*/ && [blank] nOt ~ [BlAnk] fALse /*i7r*/ &"
0 %20 && %0c NoT ~ [bLANK] fALSE /*I7r*/ &"
0 /*A|v*/ && %20 NOT ~ [BLank] FaLSe /*iu|
\*/ 
0 + && [BlaNK] NoT [bLaNk] 1 [blank] 
0 ) [blAnk] AnD [bLanK] FalSE [blANk] oR ( 0 
0 /*x&er#[*/ && %0A NOT ~ [blANk] faLsE /**/ &"
0 /**/ && /**/ noT ~ [bLAnk] FalSE + 
0 /*x&eR#[*/ && %2f not ~ %20 false /*iN*:*/ \-C
0 /*b[*/ ANd [blank] ! [BLanK] tRue [blaNk] 
0 /*,|*/ && /*F#."z*/ ! /**/ true /*J/FZu*/ i
0 /*x&*/ && %2f not ~ [blank] false /*i*/ 
0 /**/ and %0c NoT ~ [bLaNk] FaLse /*I*/ 
0 /*X&Er#[*/ and %0c not ~ [bLanK] FalSe /*i7r*/ &"6W
0 /*gU*/ && %0D NOt ~ [Blank] fAlsE /*I7R*/ &"6w
0 /**/ && /**/ ! [blaNK] tRue [Blank] 
0 [blank] && [blank] false [blank]
0 /*x&eR#[*/ && %0D not ~ [blank] false /*i*/ \v
0 /**/ AND /**/ ! [BLAnK] TRUE /*TO|1x*/ 
0 /*a|V^s*/ and %2f nOT ~ + fAlse /*IU|
\*/ 
0 /*x&*/ and %2f not ~ + false /*i*/ 
0 /*X&er#[@q!*/ && %0D noT ~ [Blank] False /*i7R*/ &">a
0 /*x&Er#[*/ and %09 Not ~ [blaNk] FAlsE /*i7R*/ &"
0 /*A|v(X7*/ && %20 not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*X&er#[*/ && %20 not ~ [BLanK] falsE /*I7R=G~K*/ &"
0 /*x&er#[m4*/ && %0A Not ~ [BLanK] FAlSE /*i7r*/ &"
0 [blANk] && [bLANk] ! + 1 /**/ 
0 /*x&ER#[m4*/ && %0A noT ~ [BlANK] FalsE /*I7r*/ &"
0 /*A|V*/ && %0C nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /*A|v*/ && %0D nOT ~ [BLaNk] FAlSE /*iu|
\*/ 
0 /*x&Er#[ee"\*/ && %09 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*X&ER#[te*/ && %20 noT ~ [bLaNk] fALsE /*O*/ &"
0 /*x&ER#[*/ && %0a NOt ~ [BLaNK] FaLse /*i7r*/ &"
0 /*gu*/ && %2f not ~ [blank] false /*i7r*/ &"6w
0 /*x&eR#[*/ && [blank] not ~ [blank] false /*i*/ \
0 /**/ and %2f not ~ + false /*i*/ 
0 /**/ && %09 Not ~ [bLAnk] fAlSE /*I*/ 
0 /*x&er#[*/ && %0a NOt ~ [BlANk] fAlse /*IN*:*/ \
0 /*x&ER#[*/ && %0a nOT ~ [BlANk] FALse /*i7Rzt*/ &"
0 /*X&er#[*/ && %0C nOT ~ [bLaNK] fALsE /*I7r*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \(
0 /*A|V*/ and %0D NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 ) [BlAnK] && [bLank] False # 
0 /*A|V*/ && %0D Not ~ [BLaNK] fAlSe /*IU|
\nJJ@*/ 
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /**/ &"
0 /*x&eR#[*/ && %2f not ~ + false /*iN*:*/ \
0 /*X&er#[*/ && %0c noT ~ [blANK] faLSE /*IN*:*/ \
0 /*X&ER#[*/ and %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"
0 /*x&eR#[*/ && + not ~ [BlanK] False /*I*/ \58
0 /*,|*/ and /*F#."z*/ ! [blank] true /*J/FZu*/ i
0 /*x&eR#[A%.	)*/ and %2f not ~ [blank] false /*i*/ \c
0 /**/ ANd /**/ ! [BLanK] tRue [blaNk] 
0 /**/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"
0 [BlanK] AnD [bLaNK] FalsE [blAnK]
0 /*x&eR#[ARR*/ and %2f not ~ [blank] false /*i*/ \
0 /*X&eR#[*/ and %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*a|V*/ && %20 NOt ~ [BlAnk] false /*iU|
\*/ 
0 /*X&er#[*/ && %09 nOT ~ [Blank] fALse /*In*:*/ \
0 /*X&Er#[*/ && %20 not ~ [blANK] faLsE /*I*/ \
0 %0D && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /**/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:p(O)p*/ \-C
0 ) [bLAnK] && /**/ 0 # DM
0 /*x&eR#[E*/ && %0C NOT ~ [bLaNk] FaLse /*I7R*/ &"
0 /*X&ER#[*/ && %0a noT ~ [bLaNK] faLsE /*I7RZt*/ &"
0 /*x&er#[*/ && %0c nOT ~ [blaNk] FALse /*I7R*/ &"
0 /**/ && %2f NOt ~ [BLANk] FaLSe /*i*/ 
0 /*-)aCr*/ && %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*x&eR#[Te*/ && %2f not ~ [blank] false /*o*/ &"
0 ) [BLank] && /**/ 0 # 
0 /*x&Er#[*/ && %0D Not ~ [blaNk] FAlsE /*i7R*/ &"
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /**/ &"
0 /*A|Vq*/ && /**/ Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /**/ && %20 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*x&*/ and %2f not ~ [blank] false /**/ 
0 /*A|Vq*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /**/ and %0c not ~ [blaNK] faLSE /*I*/ 
0 %2f && %0C not ~ [blank] false /*i7rrTnF]*/ &"
0 /*X&Er#[*/ && %0c nOT ~ [blAnK] FalSe /*IN*:*/ \
0 /*A|v*/ && %20 Not ~ [blAnk] faLSe /*IU|
\*/ 
" ) /**/ and /**/ ! ~ [blANK] FaLsE # 
0 ) [bLAnK] && /**/ 0 # DMv^
0 /*A|VqF4U**/ && + Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 [blank] && %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*,|*/ and /*F#."z*/ ! + true /*J/FZu*/ i
0 /*A|Vqt_;q]*/ && + Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&ER#[*/ && %20 nOT ~ [bLank] faLSe /*i*/ \
0 [bLAnk] or [blAnK] TrUE [blAnk] 
0 /*x&ER#[*/ && %09 Not ~ [BLaNK] fALsE /*i7r*/ &"
0 /*X&ER#[e*/ && %0c Not ~ [blAnK] faLse /*i7r*/ &"
0 [bLaNK] && /**/ falSe %20 
0 /*Gu*/ && %2f not ~ [bLaNK] fALse /*i7R*/ &"6W
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"g*
0 /**/ && %2f Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &",
0 /*X&Er#[ */ aNd %20 nOT ~ [BlAnK] FALSE /*i*/ \
0 /*a|vQ*/ && %0c nOt ~ [BLank] FAlSE /*Iu|
\*/ 
0 /*x&ER#[?ks6<*/ && %0D NOt ~ [Blank] FALsE /*I7R=g~k*/ &"
0 /*x&eR#[Y8{C*/ && %09 not ~ [blank] false /*iN*:*/ \
0 /**/ && %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*x&eR#[*/ && + not ~ [blank] false /**/ \58
0 /*gu*/ && %2F Not ~ [blaNK] fAlSe /*I7r*/ &"6w
0 /*A|VQ*/ && [blank] NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 /*x&eR#[*/ && %0a nOt ~ [bLanK] false /*I7R*/ &"
0 [BLanK] OR [blAnk] ! [BLAnk] [BlaNK] 0 [BLAnK] 
0 ) [BLaNk] ANd + 0 # DM
0 /*GU*/ && %2f Not ~ [blAnk] faLse /**/ &"6W
0 /*x&ER#[E*/ && %0C NoT ~ [bLaNk] faLse /*I7R*/ &"
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R3Ay6]*/ &"
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r$LF*/ &"6W
0 /*-)aCrQ/%BH*/ && %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i6`*/ \CK
0 /**/ && %20 nOT ~ [bLAnK] fALsE /*i*/ 
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R'IZp*/ &"
0 /*x&ER#[S#b**/ && + nOT ~ [bLank] faLSe /*i*/ \
0 [BlANK] AnD [BLanK] nOt ~ ' ' %09 
0 /*a|v*/ AND %20 NOt ~ [BLANK] fALSE /*iU|
\*/ 
0 /*a|v*/ && %20 not ~ [BLank] FALSE /*Iu|
\*/ 
0 /*A|VW9*/ && %09 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*A|V*/ && %2f NOt ~ [BlAnK] False /*IU|
\*/ 
0 /*x&ER#[*/ and %0D noT ~ [BlANk] fALSE /*i7r*/ &"
0 /*X&eR#[*/ && %0C NoT ~ [blaNK] faLse /*In*:*/ \
0 /*GU=*/ && %2f Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /*x&Er#[?f31*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21
0 /*A|VQ*/ && %20 nOT ~ [blaNk] FaLSE /*IU|
\*/ 
0 /*x&ER#[*/ && %2f NOt ~ [BlANK] FAlSe /*I*/ \ck
0 /*gU*/ && %0D NOt ~ [blANK] faLSE /*i7R*/ &"6W
0 /*X&er#[*/ && %0C noT ~ [BLaNk] FALsE /*i7r*/ &"
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"
0 /*x&ER#[*/ && %09 noT ~ [BlaNk] falSe /*I7r*/ &"
0 /*A|VQ*/ && %20 NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /**/ &"
0 /*x&eR#[*/ && %09 not ~ %20 false /*iN*:*/ \
0 /**/ && %20 nOT ~ [bLaNK] FalsE /*o*/ &"
0 /**/ && + not ~ [blank] false /*i*/ \58
0 %20 && %20 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*x&eR#[*/ && %2f not ~ /**/ false /*i*/ \CK
0 /*X&er#[M4*/ && %0a nOT ~ [bLank] FaLSE /*I7r*/ &"
0 /*x&Er#[e*/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"_>
0 /*a|Vq*/ && + nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 %20 && %0c noT ~ [blAnK] FaLSE /*i7rv*/ &"
0 /**/ aNd %20 nOT ~ [BlANK] FAlSe /**/ 
0 /*A|v*/ aND %20 nOt ~ [bLAnk] falsE /*IU|
\*/ 
0 /*X&Er#[tE*/ && %2f nOt ~ [blANk] faLSE /*o*/ &"
0 [bLAnk] ANd /**/ ! [BlaNK] tRUe /*j/FZU*/ 
0 /*X&er#[*/ && %2f nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*X&ER#[*/ && %20 Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*A|V*/ && %0A nOt ~ [bLAnK] FALSE /*Iu|
\*/ 
0 /*X&er#[*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"b
0 /*a|vw9*/ && %20 Not ~ [blanK] FAlsE /*iU|
\*/ 
0 /*x&eR#[Te*/ && %20 nOT ~ [bLaNK] FalsE /*o*/ &"U<
0 /*A|V*/ && %20 Not ~ [bLANk] falSe /*iU|
\*/ 
0 [bLaNK] anD /**/ ! [blANK] tRue /*j/fZuR}2*/ 
0 /*X&Er#[*/ aNd [blank] nOT ~ [BlAnK] FALSE /*i*/ \
0 /*x&Er#[*/ && %0D NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \@]
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \
\
0 /*x&eR#[A%.	)*/ and %2f not ~ [blank] false /*i*/ \
0 /**/ and %09 nOt ~ [bLaNk] FAlSE /*i*/ 
0 /*A|V]*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&Er#[*/ && %09 NOt ~ [bLank] fALsE /*in*:*/ \
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a2
0 /*A|Vq*/ and + Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[*/ and %2f not ~ [blank] false /*i*/ \
0 /**/ and %0D nOT ~ [BlanK] FaLSe /*I*/ 
0 /*A|V*/ && %20 nOt ~ [BlaNk] FAlse /*iu|
\9Ab.8*/ 
0 /*X&ER#[*/ && %20 Not ~ [BLank] fAlsE /**/ &"
0 /*x&er#[*/ && %2f NOT ~ [blANk] faLsE /*i7R*/ &"
0 /*x&eR#[m4*/ && %0a NOT ~ [blANK] FaLSe /*i7r*/ &"
0 /*:-*/ && %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*x&eR#[*/ and %20 not ~ [blank] false /*i*/ \CK
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /**/ &"
0 /*X&er#[*/ && %0C nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*X&eR#[*/ && %0d NOt ~ [bLANK] falsE /*I*/ \
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*iW89*/ \
0 /*x&eR#[*/ && %20 not ~ + false /*i*/ \CK
0 /*x&eR#[t^*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*A|Vq*/ && %2f Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /**/ anD %20 nOT ~ [BlAnk] FalSE /*i*/ 
0 /*x&eR#[ARR*/ && %2f not ~ /**/ false /*i*/ \
0 /*x&*/ AND %2F NOt ~ [bLank] false /*IJx/*/ 
0 /*a|V*/ && %09 nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 /**/ && %2F NoT ~ [BLANK] FaLse /*iY$*/ 
0 /**/ && + Not ~ [BlANK] fALSe /*I*/ 
0 /*X&eR#[*/ && %09 NoT ~ [blaNK] faLse /*In*:*/ \J
0 /*A|V*/ and %20 NOt ~ [bLAnk] fAlse /*i*/ 
0 /*a|Vq*/ && %0C nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 %2f && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /*GU*/ && %2f Not ~ [blAnk] faLse /*i7RNBt*/ &"6W
0 /*x&ER#[*/ && %2F nOt ~ [BLank] fAlsE /*In*:*/ \-cyk
0 /*a|vq*/ && %20 Not ~ [BlANK] fALse /*iU|
\*/ 
0 /*x&eR#[?ks6<*/ && %0D noT ~ [bLank] falsE /*I7r=g~K*/ &"
0 /*A|VqBw|ai*/ && %20 Not ~ [bLank] FAlse /*Iu|
\*/ 
0 /*x&eR#[*/ && %0C not ~ /**/ false /*iN*:*/ \
0 /*,|*/ && /*F#."z*/ ! + true /*J/FZu*/ i
0 /*X&er#[*/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &"hN
0 /*A|V*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /**/ && %2f not ~ %20 false /*i*/ 
0 [bLanK] ANd [blank] NOt [BlanK] 1 /**/ 
0 /*A|v*/ && %2f not ~ [blank] false /*iu|
\*/ 
0 /*A|V*/ && + Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&eR#[*/ && %2f not ~ [blaNK] FAlSe /*I*/ \v
0 /*X&eR#[*/ && %2f NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*X&ER#[*/ && %20 NoT ~ [bLank] fAlse /**/ &"
0 /*X&eR#[*/ && + NOt ~ [Blank] FaLSE /*I*/ \58
0 [bLaNk] AND %20 ! [blAnK] 1 /*n
S*/ 
0 /*x&eR#[*/ && %20 not ~ [blank] false /*iN*:*/ \)D
0 /*x&eR#[Te1*/ && %2f not ~ [blank] false /*o*/ &"3
0 /*A|V*/ && [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&Er#[ARr*/ && %2F not ~ [blaNk] False /*I*/ \
0 ) [bLank] && /**/ 0 # 
0 /**/ && %20 NOt ~ [bLanK] faLse /*i*/ 
0 %20 && [blaNk] Not ~ [BLAnK] faLSE /*I*/ 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*i*/ \
0 /*x&er#[*/ && %09 nOt ~ [bLAnk] FaLSe /*I7R*/ &"
0 /**/ && [blANk] noT [bLaNk] TrUE [BLANK] 
0 /**/ anD %20 noT ~ [bLanK] FALSE /*I*/ 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iO*/ \CK
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \CK
0 /*x&eR#[*/ && %2f not ~ %20 false /*i*/ \v
0 /*A|Vq*/ && %0C Not ~ [bLank] FAlse /*Iu|
\*/ 
0 /*A|va */ && %20 not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*x&er#[*/ && %0C NoT ~ [BlAnk] FAlsE /*I7r*/ &"6WbW
0 /*x&eR#[TE*/ && %20 NOt ~ [BLaNK] FAlSe /*O*/ &"
0 /*A|v*/ && %2f Not ~ [bLAnk] FALSe /*iU|
\T~%h*/ 
0 /**/ && /**/ nOT ~ [BLAnk] faLSe [bLAnk] 
0 /*x&er#[*/ && %20 nOt ~ [blanK] faLSE /*I*/ \cK
0 /*x&eR#[*/ and %0A not ~ [blank] false /*iN*:*/ \
0 /*A|VQ*/ and %20 NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 %20 and %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 [blank] && /**/ nOt ~ [bLAnk] False /*I*/ 
0 /*a|V*/ && %2F nOT ~ [blANk] false /*iu|
\*/ 
0 /*x&Er#[n*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /*X&ER#[*/ && %0d NoT ~ [bLaNk] fALsE /**/ &"
0 /*x&eR#[uM>*/ && %0D not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:(*/ \)D
0 /*A|V*/ && %0A NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*X&ER#[*/ && %0c not ~ [BlANK] FALSe /*i7R*/ &"
0 /**/ && [bLank] nOt [BlanK] True [BlANK] 
0 /*A|VQ*/ && %20 NOT ~ [BlaNk] fAlSE /*iu|
\*/ 
0 /*x&eR#[*/ && %09 not ~ %20 false /*iN*:*/ \)D
0 /*A|V*/ && %2f NoT ~ [bLANK] FaLsE /*Iu|
\=A;1~*/ 
0 ) [BlaNK] ; SELEct /**/ sLeeP ( [TErDigiteXcLuDINgZero] ) # 
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*X&er#[*/ && %09 nOt ~ [BLAnk] fAlSe /*I7rJ5Hg*/ &"
0 /*x&er#[M4*/ && %0a nOt ~ [BlAnK] FalsE /*i7r*/ &"
0 %20 && %0C not ~ [BlAnK] FALSe /*I7R*/ &"
0 /*a|Vq*/ && /**/ nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /*X&eR#[*/ && %0A NoT ~ [BlaNk] FaLsE /*I7r*/ &"
0 /*x&Er#[*/ && %0A NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /**/ && /**/ ! [blANk] TRue /*J/FZu
2wfH*/ 
0 /*x&Er#[*/ && %0a NoT ~ [bLank] FaLse /*i7R*/ &"
0 /**/ and %2f not ~ %20 false /*i*/ 
0 /*x&ER#[*/ && %09 nOT ~ [bLANk] faLSE /*iN*:osx*/ \
0 /*A|v*/ && %2f Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 /*X&ER#[*/ && %0a nOT ~ [bLANk] fALsE /*i7R*/ &"
0 /**/ && %20 nOt ~ [BLaNk] FALSe /*I*/ 
0 + && %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*A|V*/ && %2F nOT ~ [BlaNk] fALsE /*IU|
\*/ 
0 /*A|V*/ && %09 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*A|v*/ && %2f nOT ~ [BlANk] False /*IU|
\kI*/ 
0 [BlanK] And %20 NOT ~ [blanK] FAlSe /*i*/ 
0 /*A|V*/ and %20 NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*x&er#[*/ ANd %0D nOT ~ [bLaNk] FaLSE /*i7r*/ &"
0 /*X&eR#[*/ && %09 nOT ~ [bLank] FALSe /*in*:*/ \
0 /*x&Er#[*/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"21
0 /*x&ER#[*/ and %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 + && [blaNK] nOT [bLANK] 1 [BlanK] 
0 /*X&Er#[*/ && %09 nOt ~ [BlaNk] fAlSE /*i7r*/ &"
0 /*x&eR#[*/ && %0A not ~ [blank] false /*iN*:*/ \s9
0 /*,|*/ ANd /**/ ! + tRUe /*J/fZU*/ i
0 [BlanK] And /**/ NOT ~ [blanK] FAlSe /*i*/ 
0 %20 && %09 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*X&ER#[*/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &".
0 /**/ && %0A not ~ [blank] false /*iN*:*/ \
0 %20 && %0c noT ~ [blAnK] FaLSE /**/ &"
0 [blAnk] Or [BlANk] ! [blAnk] [BlaNk] 0 [blank] 
0 /*a|V*/ && %20 nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 /*A|VQ*/ && %20 NOt ~ [blaNK] FAlSE /*iu|
\ezLr]*/ 
0 /**/ && %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*X&ER#[Z0]*/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"
0 /*x&ER#[*/ && /**/ nOT ~ [bLank] faLSe /*i*/ \
0 [BLANk] AND + ! [bLAnK] tRuE /*%~*/ 
0 /*a|Vq*/ && [blank] nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /**/ || [bLank] TRUe [BlaNk] 
0 /*X&er#[*/ && %20 not ~ [bLAnk] FaLSE /*I7R*/ &"
0 /*x&er#[m4*/ && %09 Not ~ [BLanK] FAlSE /*i7r*/ &"
0 /*X&er#[*/ && %0C nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /**/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
" /**/ && [BlaNK] nOt [bLank] 1 [bLank] oR " 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:h*/ \-C
0 /*a|V*/ && %20 nOT ~ [BlAnk] fAlSE /*IU|
\-*/ 
0 ) [Blank] And /**/ 0 # dm
0 /*X&Er#[E*/ && %0C NoT ~ [blANK] fAlSE /*i7r*/ &"
0 /*X&Er#[arR*/ && %2f Not ~ [bLaNk] False /*i*/ \
0 [BLAnk] AnD /**/ ! [blAnK] truE /*%~{(a*/ 
0 /*X&eR#[*/ && %20 NoT ~ [Blank] faLsE /*i7R*/ &"
0 /*gu*/ && %2f not ~ [blank] false /*i7r#FYc*/ &"6w
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"s
0 /*x&Er#[*/ && %0c NOt ~ [BLaNk] faLSE /*I7R*/ &"6w
0 /*X&eR#[*/ && %0D NoT ~ [BLAnK] FAlsE /*i7r*/ &"
0 /*A|v*/ && %20 Not ~ [bLAnk] False /*iu|
\*/ 
0 [bLANK] OR %0A not [BlaNk] ' ' [BLank] 
0 /*GU*/ and %2f Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"I
0 /*X&ER#[*/ && %20 nOT ~ [bLAnk] FalSe /*I7r=g~k*/ &"
0 /*x&eR#[*/ && %0A not ~ [blank] false /*iN*:>0M*/ \
0 /*x&eR#[*/ && %0c NOt ~ [BLANk] faLsE /*I7R*/ &"
0 /**/ ANd %2f NOt ~ [blANk] FalSE /*i*/ 
0 /**/ && %0D nOt ~ [blANk] faLSE /*o*/ &"
0 /*x&er#[*/ && %0d Not ~ [BlanK] fAlse /*I7r*/ &"
0 /*x&ER#[tE1*/ && %0d NOT ~ [bLANk] FAlse /*O*/ &"3
0 /*x&eR#[E*/ && %0C NOt ~ [blAnK] FAlSe /*i7r*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \[U
0 /**/ && [bLanK] ! ~ [bLANK] 0 [BlAnk] 
0 /*X&eR#[*/ && %09 nOt ~ [BlanK] faLse /*I*/ \ck
0 /*X&eR#[*/ && %0d Not ~ [blANk] fAlSe /*I7r=G~K*/ &"
0 /*ju
*/ && %20 NOt ~ [BlaNK] faLSE /*I*/ 
0 [bLaNk] && [blANK] ! [blANK] 1 /*}*/ 
0 ) [bLAnK] && /**/ 0 # DMF#
0 /*x&er#[*/ && %09 Not ~ [blaNk] FaLSe /*IN*:*/ \)D
0 /*x&er#[m4*/ && %0A Not ~ [BLanK] FAlSE /*i7r*/ &"5
0 /**/ and /**/ nOT ~ [bLanK] FalsE [BlaNk] 
0 /*a|vQ*/ && %0c NOT ~ [BLaNk] FALse /*IU|
\*/ 
0 /*x&eR#[*/ && %0A not ~ /**/ false /*iN*:*/ \
0 /*X&eR#[*/ and %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">a
0 /*x&*/ and %2f not ~ [blank] false /*i*/ 
0 /**/ and %20 not ~ [blank] false /*i*/ \
0 [BLanK] aND %20 nOT ~ [BLaNK] faLsE /*I*/ 
0 /*x&eR#[*/ && + not ~ [blank] false /*i|/v*/ \
0 /*X&er#[*/ && %0A Not ~ [bLANK] FALSE /*I7R*/ &"
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W
0 /*x&eR#[*/ ANd %0A NoT ~ [bLANK] False /*I*/ \
0 /*GU*/ && %0D Not ~ [blAnk] faLse /*i7R*/ &"6W
0 %20 && %20 NoT ~ [BLAnK] falsE /*i*/ 
0 /*Gu*/ && %0d NOt ~ [BlAnK] falsE /*i7r*/ &"6W
0 /*A|v*/ && %20 nOT ~ [BlANk] False /*IU|
\kI*/ 
0 [bLanK] AND /**/ ! [bLanK] 1 /*n
s*/ 
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \
0 /*A|v*/ && %0A Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 /*A|v*/ && %20 NoT ~ [BlAnk] fALSE /*Iu|
\*/ 
0 /*X&Er#[L#bk;*/ aNd [blank] nOT ~ [BlAnK] FALSE /*i*/ \
0 /*A|VQpRm*/ && %20 NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 /**/ && %2f not ~ [blank] false /*i*/ \CK
0 /*a|v*/ && %20 NoT ~ [bLanK] FALSe /*Iu|
\*/ 
0 /*X&eR#[*/ && %20 NoT ~ [blAnk] fAlSE /*i7R*/ &"
0 [BlAnK] aNd /**/ ! [bLaNk] 1 /*n
somB*/ 
0 [Blank] && [blAnk] ! [blanK] 1 /**/ 
0 /*a|V*/ && %20 NoT ~ [bLaNK] FAlSE /*IU|
\*/ 
0 /*x&er#[*/ && %2F NoT ~ [BLaNK] faLSe /*i*/ \V
0 /*A|V*/ && %09 NoT ~ [Blank] FAlsE /*Iu|
\kI*/ 
0 /**/ && %20 not ~ [blank] false /*i*/ \CK
0 /*X&ER#[e*/ && %0C NOt ~ [BLank] FAlSe /*I7R*/ &"
0 /*x&eR#[*/ && + not ~ + false /*i*/ \
0 /*x&eR#[*/ && %20 nOt ~ [blaNK] fAlSe /*i7R*/ &"
0 /*A|V*/ ANd %0d nOT ~ [BlAnk] falSE /*Iu|
\*/ 
0 /**/ && %09 not ~ [BlAnK] fAlSe /*In*:*/ \)d
0 /*X&er#[@q!*/ and %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*A|V*/ && %20 NOT ~ [blanK] FAlsE /*iu|
\*/ 
0 /*x&ER#[*/ && %0a not ~ [BlaNK] faLse /*I7r*/ &"
0 /*X&Er#[*/ && %2f nOt ~ [BlanK] faLSE /*I*/ \Ck
0 /*X&ER#[*/ AnD %0d NOt ~ [blaNk] False /*i7r*/ &"N@
0 /*A|V*/ && %2f NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*A|V*/ && %20 nOT ~ [BlAnk] false /*IU|
\*/ 
0 /*a|Vq*/ && %0A nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 [blank] aNd %20 nOT ~ [BlANK] FAlSe /**/ 
0 /*o*/ and %20 nOt ~ [BLaNk] FALSe /*I*/ 
0 [blank] and %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*x&Er#[te1*/ && %0D nOt ~ [BLAnK] falSe /*O*/ &"
0 /*X&ER#[*/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"
0 /*A|Vq*/ and %0C Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /**/ &"
0 /*X&Er#[tEq>+x*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i7r*/ &"
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"9
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">a
0 /*X&er#[*/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &"%
0 [blank] || [blank] true [blank] 
0 /**/ && %20 ! [BlAnk] tRuE /*J/Fzu
2wFh*/ 
0 /*x&ER#[*/ and %0a not ~ [BlANk] FaLSe /*in*:*/ \
0 [BLANK] aNd [bLanK] noT ~ ' ' %20 
0 /*X&Er#[*/ && %2F nOt ~ [bLANk] fALSe /*iN*:*/ \
0 /*X&Er#[*/ && %0A not ~ [BlANk] FALSE /*I7R*/ &"
0 /*X&ER#[?ks6<*/ && %0D NOt ~ [blaNK] falSe /*i7r=g~K*/ &"
0 /*x&eR#[*/ && %0D NOT ~ [BLaNk] fAlsE /*i7r*/ &">A
0 /*a|v*/ && %20 nOt ~ [BlAnk] fAlsE /*iu|
\*/ 
0 /*A|Vq*/ && %2f Not ~ [bLank] FAlse /*Iu|
\*/ 
0 ) [bLaNk] aND /**/ 0 # dm
0 [blank] aNd %20 nOt ~ [BlaNK] faLsE /*I*/ 
0 /*A|vdktm)*/ && %20 nOT ~ [BlANk] False /*IU|
\*/ 
0 /*X&ER#[*/ && %0d NoT ~ [BlaNK] falSE /*i7R*/ &"
0 /*x&eR#[*/ && %0D not ~ + false /*i*/ \
0 /*A|V*/ && %20 NoT ~ [bLANK] FAlse /*iu|
\*/ 
0 /*X&eR#[*/ && %0D Not ~ [bLAnk] fAlSe /*i7R*/ &">a
0 /*A|v*/ && %09 NOt ~ [BlAnK] fALse /*Iu|
\Ki*/ 
0 /**/ && + not ~ [blank] false /*i*/ \
0 [BLanK] aNd [BlaNK] faLSe [BLAnK]
0 /*x&eR#[*/ && %0C not ~ + false /*iN*:*/ \
0 /*A|V*/ && %0C NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /**/ && %0a not ~ [BlANk] FaLSe /*in*:*/ \
0 /*A|vq*/ && + NOT ~ [BlaNK] faLSE /*Iu|
\*/ 
0 /*A|v*/ && %20 noT ~ [blaNk] FAlsE /*i*/ 
0 /*x&ER#[*/ and + nOT ~ [bLank] faLSe /*i*/ \
0 /*A|v*/ && %20 nOt ~ [blaNK] FaLse /*iU|
\*/ 
0 /*X&er#[*/ && %0A NOt ~ [bLanK] FAlSE /*i7r*/ &"
0 /*X&eR#[*/ && %0d noT ~ [BLanK] FaLSe /*I7R*/ &"
0 /*X&Er#[*/ && %0C not ~ [bLAnK] FAlSE /*I7r*/ &"6w
0 /*A|Vq*/ && + Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&*/ && %0D NOt ~ [blANk] faLSe /*I*/ 
0 /*A|V*/ && %20 NoT ~ [bLanK] faLSe /*iU|
\*/ 
0 /*x&eR#[*/ and + not ~ [blank] false /*i*/ \58
0 /*x&ER#[*/ aND %09 nOT ~ [blaNk] falSE /*i7R*/ &"
0 /*X&ER#[*/ && %20 Not ~ [BLank] fAlsE /*I7r=g~K*/ &"

0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">as
0 /**/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"
0 /*x&Er#[*/ && %0d not ~ [bLaNK] fAlSE /*I7r*/ &">a
0 /*x&eR#[8F*/ && + not ~ [blank] false /*i*/ \
0 /*x&er#[*/ && %0C NoT ~ [BLANk] false /*i7r*/ &"6w
0 [BLaNk] aND [BlAnK] ! [Blank] 1 /*N
S*/ 
0 /**/ && %09 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*X&ER#[Te*/ && %0D Not ~ [bLank] FaLSe /*o*/ &"
0 /*A|V*/ && + NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*X&eR#[*/ && %0A NOt ~ [blaNK] FALse /*I7rzT*/ &"
0 /*a|VQ*/ && %20 not ~ [bLaNk] FaLsE /*iu|
\*/ 
0 /*x&er#[*/ && %0C nOT ~ [BlANk] falSE /*I7R*/ &"6w
0 /*X&Er#[tE;MYe5*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \

0 /*A|vQ*/ && %20 NOt ~ [BLAnK] FalSe /*Iu|
\*/ 
0 /*X&er#[a%.	)*/ && %2F Not ~ [bLanK] falSE /*i*/ \\4
0 /*x&eR#[*/ && %0C not ~ [blank] false /*iN*:*/ \-C
0 /*A|V*/ And %20 NOT ~ [BLANK] fAlsE /*iu|
\*/ 
0 /**/ && + NOt ~ [BlaNK] faLSE /*I*/ 
0 /*x&eR#[*/ && /**/ not ~ [blank] false /*i*/ \
0 /*a|V*/ && %0D not ~ [bLank] FaLsE /*iu|
\*/ 
0 %20 && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /*X&ER#[*/ && %20 not ~ [BlANk] fALsE /*i7r*/ &"b
0 /**/ && %09 not ~ [blank] false /*iN*:*/ \
0 /*X&eR#[*/ && %09 NOt ~ [BLank] faLSE /*IN*:*/ \
0 %2f && %0C not ~ [blank] false /*i7r*/ &"
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \CK
0 /*X&ER#[*/ && %0d NOt ~ [BlanK] false /*i7r*/ &">a]
0 /*X&Er#[^
_Q7*/ && %2F NoT ~ [blaNk] falsE /*i*/ \
0 /*gU*/ && %0D not ~ [blaNk] falSe /*I7R*/ &"6W
0 /*x&eR#[*/ && %2f not ~ + false /*iN*:*/ \-C
0 /*gu*/ && %2f not ~ + false /*i7r*/ &"6w
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"2	
0 /*X&ER#[*/ && %0C NoT ~ [BLANk] fALSE /*i7R;*/ &"6WBw
0 /*x&eR#[A%.	)*/ && %2f not ~ [blank] false /*i*/ \
0 /*,|*/ && /*F#."z*/ ! %20 true /*J/FZu*/ i
0 /*X&eR#[*/ && + NOT ~ [blaNK] FALse /*I*/ \
0 /**/ aNd [blank] nOT ~ [BlANK] FAlSe /*i*/ 
0 [bLANK] ANd [BLank] ! [BlaNk] 1 /**/ 
0 /*x&eR#[*/ && %0D not ~ [blank] false /*iN*:*/ \-C
0 /*X&er#[t*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*x&eR#[Te*/ && %20 nOT ~ [bLaNK] FalsE /*o*/ &"
0 /**/ && /**/ nOt ~ [bLAnk] False /*I*/ 
0 %20 && %2F NoT ~ [BLANK] FaLse /**/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \8
0 /*x&eR#[T?:*/ && %09 not ~ [blank] false /*i*/ \CK
0 /*a|V*/ && %20 Not ~ [blAnK] falsE /*iU|
\*/ 
0 /*X&ER#[*/ && %09 Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*X&er#[*/ and %0D nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*a|V*/ && %0D nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 /*x&eR#[*/ && %0c nOt ~ [bLaNK] faLsE /*I7R*/ &"6W
0 ) [BlAnK] or [blaNK] ! [blank] %20 0 /**/ Or ( 0 
0 /**/ && [bLaNk] ! [bLank] trUE /*To|1X*/ 
0 /*b*/ ANd /**/ ! [BLanK] tRue [blaNk] 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*i*/ \CKZ5
0 /*X&er#[*/ and %0A nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*X&er#[*/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &"l
0 /*X&Er#[*/ && %20 nOT ~ [bLaNK] fAlSE /*i7R=g~K*/ &"
0 /*a|V*/ && %2F Not ~ [BlAnK] faLse /*Iu|
\*/ 
0 /**/ && + nOT ~ [bLank] faLSe /*i*/ \
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i7r*/ &"b
0 /*X&eR#[*/ AnD %2f NoT ~ [blANK] False /*in*:*/ \
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i7r*/ &"6wBW
0 /*A|Vq*/ && %0c Not ~ [BLanK] FalSe /*Iu|
\*/ 
0 [blANK] aND + ! [bLank] TrUE /**/ 
0 /*x&ER#[*/ && %09 nOT ~ [BLANk] FaLSE /*i7R*/ &"
0 [blaNk] && [BlanK] ! [Blank] True /*j/fzu*/ 
0 /*x&eR#[$)!a*/ && %09 not ~ [blank] false /*iN*:*/ \)D
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W_l
0 /*A|Vq*/ && %0A Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[*/ and %2f not ~ [blank] false /*iN*:*/ \
0 /*x&Er#[*/ && + NOt ~ [bLank] False /*I*/ \58
0 %20 && %09 NoT ~ [blANk] FALse /*i7rrTNF]*/ &"
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7rrV*/ &"6W_l
0 /*A|V*/ && %0A Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 %20 && %0C nOT ~ [bLanK] FaLsE /*I7rRTnf]*/ &"
0 /*x&eR#[*/ and %2f not ~ + false /*iN*:*/ \
0 /*A|vh08*/ && %20 nOT ~ [BlANk] False /*IU|
\*/ 
0 /**/ and %2F NoT ~ [blaNk] falsE /*i*/ \
0 /*x&er#[m4*/ and %0A Not ~ [BLanK] FAlSE /*i7r*/ &"
0 [BLANk] AND /**/ ! [bLAnK] tRuE /*%~*/ 
0 /*X&ER#[*/ && %0D Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 %20 && %2F NOt ~ [BlaNK] FALse /*i*/ 
0 /*x&er#[*/ && %20 NoT ~ [blANK] FALSe /*I7r*/ &"
0 /*X&er#[@q!*/ && %0C nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*A|v*/ and %2f Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \CK#<
0 /*X&Er#[*/ && %0c nOt ~ [bLANk] FALsE /*IN*:*/ \
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \58
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /**/ &"
0 /*X&er#[*/ && %20 nOT ~ [bLanK] FaLsE /*I*/ \
0 /*x&eR#[*/ and %09 not ~ [blank] false /*iN*:*/ \
0 /*x&ER#[*/ && [blank] nOT ~ [bLank] faLSe /*i*/ \
0 /*x&Er#[*/ && %09 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 %20 && %0c Not ~ [BlAnk] fAlse /*I7R*/ &"
0 /*X&er#[*/ && %20 nOt ~ [BlAnk] fALse /**/ &"
0 /*x&Er#[*/ && %0C NOT ~ [BlAnK] False /*I7r*/ &"6w
0 /*x&ER#[*/ && %2F NOt ~ [blAnk] false /*i*/ \
0 /*x&eR#[*/ && + not ~ %20 false /*i*/ \
0 /*x&eR#[m4PW*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*X&Er#[*/ && %2F Not ~ [BLANK] false /*I*/ \v
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \f
0 /*,|*/ and /**/ ! [blank] true /*J/FZu*/ i
0 /*A|V*/ and %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 ) [blaNk] || ~ [BLanK] [bLank] 0 -- [BLanK] 
0 [BLANk] AND + ! [bLAnK] tRuE /*%~	k/*/ 
0 [blank] && %20 NOt ~ [bLanK] faLse /*i*/ 
0 /*x&eR#[Te*/ && %0A nOT ~ [bLaNK] FalsE /*o*/ &"
0 /*x&ER#[*/ && %09 noT ~ [BlanK] FALse /*in*:OsX*/ \
0 /*x&Er#[*/ && %2f NOT ~ [blANk] FalsE /*I*/ \v
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \
0 /*x&Er#[*/ && %2f noT ~ [blANK] FALSE /*iN*:*/ \-C
0 /*A|Vq*/ && %0C NOT ~ [BLaNk] FalSe /*Iu|
\*/ 
0 /**/ anD /**/ not ~ [bLaNk] fAlSE [bLanK] 
0 [blANk] && [bLANk] ! [blank] 1 /**/ 
0 %20 AnD %0d NOt ~ [BLaNK] FalSE /*I*/ 
0 /*A|Vq*/ && %0D Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[*/ && %2f not ~ %20 false /*iN*:*/ \
0 /*A|VW9*/ && %0D NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*a|V*/ && %0C nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 [blanK] && /**/ ! [blaNK] 1 /**/ 
0 /*x&eR#[*/ && %0C Not ~ [BLAnk] faLsE /*i7Rt*/ &"6w
0 /*X&er#[*/ && %2f nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 [BLaNk] Or ~ [BLaNK] /**/ FAlsE /**/ 
0 %20 && %0c NOT ~ [BlAnK] faLSE /*I7r*/ &"
0 /*A|v*/ and %09 nOT ~ [BlANk] False /*IU|
\kI*/ 
0 %20 && %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /**/ && %2F noT ~ [BlANk] faLsE /**/ 
0 /*X&Er#[*/ && %2F nOt ~ [BlanK] fAlSe /*In*:*/ \-c
0 /**/ && %2f not ~ [blank] false /*i7r*/ &"6w
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">ab
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \-C
0 /*x&er#[Te*/ && %20 nOT ~ [BlAnk] FAlsE /*O*/ &"
0 /**/ anD /**/ ! [BlAnk] tRUE [BLaNK] 
0 /*X&er#[tE1*/ && %0d nOt ~ [bLANk] fAlSE /*O*/ &"
0 /**/ && %0D nOt ~ [BLaNk] FALSe /*I*/ 
0 %09 and %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*A|V*/ && %0D Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*A|Vq*/ && + Not ~ [bLank] FAlse /*Iu|
\*/ 
0 /*X&Er#[*/ && %0C noT ~ [BlAnk] fAlsE /*I7r*/ &"6W
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rrF*/ &"
0 /*x&eR#[*/ && %09 not ~ /**/ false /*iN*:*/ \)D
0 /*A|V*/ and %2f Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&ER#[*/ and %0d NOt ~ [BlanK] false /*i7r*/ &">a
0 /*X&eR#[*/ && %20 nOt ~ [bLaNk] FALse /*I7R=G~k*/ &"
0 /*A|V*/ && %2F not ~ [BLAnk] faLSe /*IU|
\*/ 
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7rX>j4*/ &">a
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 ) [blaNk] anD [BlAnK] FalsE [BLanK] or ( 0 
0 /*a|Vq*/ && %20 nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /*x&ER#[B+zz9*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 /*X&er#[*/ && %0C not ~ [blaNk] FALSE /*i7R*/ &"6W
0 /*X&er#[@q!*/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*A|VQ*/ && %20 NOT ~ [BLAnK] falSe /*iu|
\*/ 
0 /*X&Er#[tE*/ && %20 NoT ~ [blANK] fAlsE /*O*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i7r=g~k*/ &"
0 /*x&eR#[*/ && %2F NoT ~ [blANK] FaLSE /*iN*:*/ \-c
0 [BLAnk] AnD %20 ! [blAnK] truE /*%~{(a*/ 
0 /*x&ER#[*/ and %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"4&
0 /*a|VQ*/ && %20 NoT ~ [BLAnk] fAlse /*Iu|
\*/ 
0 /*x&eR#[=4*/ && %09 not ~ [blank] false /*i*/ \CK
0 [blank] or [blank] true [blank] 
0 [blAnk] aND [blank] ! [blaNk] 1 /*N
s*/ 
0 /**/ && [blank] not ~ [bLanK] fALsE /*i*/ 
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \5
0 /*x&Er#[s6P^
*/ && %09 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*X&<z*/ && %0d nOt ~ [bLaNk] FAlsE /*i*/ 
0 /*x&eR#[Te*/ && %20 nOT ~ [bLaNK] FalsE /**/ &"
0 /*A|V*/ && %20 Not ~ [bLank] FAlse /*iU|
\*/ 
0 /*a|V*/ && [blank] NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 /*X&ER#[*/ && %20 Not ~ [BLank] fAlsE /*I7r=g~K.P;*/ &"
0 /*A|V*/ && %2f Not ~ [BLaNK] fAlSe /*IU|
\PP*/ 
0 /*X&er#[tE1*/ && %0d nOt ~ [bLANk] fAlSE /*O*/ &"j-
0 %20 && %0c nOt ~ [bLaNk] FALSe /*I7rRtNf]*/ &"
0 /*gu*/ && %2f not ~ [blank] false /*i7rYVrB*/ &"6w
0 /*x&eR#[*/ and %20 not ~ [blank] false /**/ \
0 /*x&ER#[*/ && %2F Not ~ [blaNK] FAlSE /*i*/ \
0 [bLANK] ANd [BLank] ! [BlaNk] 1 /*N
s*/ 
0 /*x&ER#[*/ && %2F nOt ~ [BLank] fAlsE /*In*:*/ \-c
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"'
0 /*A|v*/ && /**/ not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*X&eR#[*/ && %20 NoT ~ [blaNK] faLse /*In*:*/ \
0 /*x&ER#[*/ && %09 NoT ~ [blanK] FaLse /*IN*:OsX*/ \
0 /*A|V*/ && %20 NOt ~ [blANK] FalSe /*iu|
\QEv*/ 
0 /*x&eR#[*/ && %20 not ~ /**/ false /*i*/ \
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \58
0 /**/ && /**/ noT ~ [bLAnk] FalSE /**/ 
0 /**/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W
0 /*x&eR#[V*/ && %0C not ~ [blank] false /*iN*:*/ \
0 /*x&eR#[Te*/ && %09 nOT ~ [bLaNK] FalsE /*o*/ &"
0 /*gu*/ && %0D NoT ~ [BLANk] FALSE /*i7R*/ &"6w
0 /**/ && %0d nOt ~ [bLANk] fAlSE /*O*/ &"
0 /*X&er#[*/ && %2F nOt ~ [Blank] FalSE /*I*/ \
0 ) [BLANK] && [blANK] 0 -- [BlaNk] 
0 /*x&eR#[*/ && %0D not ~ [blank] false /*i*/ \
0 /*a|V*/ && %09 Not ~ [BlaNK] FaLsE /*IU|
\*/ 
0 /**/ && %2F nOt ~ [BLank] fAlsE /*In*:*/ \-c
' ) [blANk] And [blAnK] ! ~ [bLanK] faLsE # 
0 /*x&eR#[R&z*/ && %2f not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %2f nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \CKl;
0 [BlaNk] oR [BLaNK] ! [blaNK] [BlanK] 0 + 
0 /*A|v*/ and %20 nOT ~ [BlANk] False /*IU|
\*/ 
0 /*A|Vq*/ and [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \v7h
0 %20 && %09 nOT ~ [bLaNk] FalsE /*i7RRtNf]*/ &"
0 %20 && %09 nOt ~ [bLAnK] FaLse /*I7rrtNF]*/ &"
0 /*x&Er#[e*/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /**/ && %09 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*X&eR#[*/ && + Not ~ [bLank] falSE /*i*/ \58
0 /**/ and /**/ ! [blANK] tRUE /*J/FZu
2wfH*/ 
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
0 /*x&eR#[J84Cb*/ && %2f not ~ [blank] false /*iN*:*/ \-C
0 /*x&eR#[*/ && %2f not ~ /**/ false /*i*/ \
0 /*X&ER#[*/ && %2f NOt ~ [BlaNK] FALsE /*In*:*/ \-c
0 /*x&Er#[*/ && %0d NoT ~ [BlaNK] fALSe /*I7R*/ &">a
0 /*x&eR#[M4*/ && %0A noT ~ [blaNk] FALse /*i7r*/ &"
0 /*X&eR#[TE*/ && %20 NOt ~ [BLaNK] FALSe /*O*/ &"
0 /*A|V*/ && %09 NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*A|V*/ && %0D NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /**/ &"6W_l
0 /*x&eR#[*/ && %09 not ~ %20 false /*i*/ \CK
0 [blank] or [blANk] TRUe [BlAnK] 
0 /*,|*/ And /*f#."z*/ ! %20 tRuE /*j/fZU*/ i
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \
0 /*SV,W*/ aNd + nOT ~ [BlANK] FAlSe /*i*/ 
0 /*A|V*/ && + nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /*A|V*/ && %20 NOt ~ [Blank] FalSe /*iu|
\*/ 
0 /*x&er#[*/ && %20 NoT ~ [bLANk] faLse /*i7r*/ &"
0 [BlaNk] Or [BLANk] ! [BlANk] [bLANk] 0 %20 
0 /**/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*x&ER#[*/ && %09 noT ~ [BlaNK] fAlsE /*I*/ \CK
0 /*a|V*/ && %20 nOt ~ [bLaNK] fALse /*iU|
\*/ 
0 [BLaNK] aNd /**/ ! [BLanK] 1 /*N
S*/ 
0 /*A|VUxY7s*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[{Q}@*/ && %2f not ~ [blank] false /*i7r*/ &"
0 /*A|V*/ and %20 nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 [bLANK] aND [BlanK] nOT ~ ' ' %20 
0 /*x&ER#[*/ && %2F nOt ~ [BLank] fAlsE /*In*:*/ \-cl
0 /*A|V*/ && %20 nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \vj 
0 /*a|V*/ && %2f NOT ~ [BlANk] faLSe /*IU|
\*/ 
0 /*X&eR#[*/ && %2f NOT ~ [BlaNK] FAlSe /*I*/ \v
0 /*X&ER#[*/ && %0C Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*x&er#[*/ && %0C nOT ~ [bLaNK] FALSE /*I7r*/ &"
0 /*A|v*/ aND [blank] nOt ~ [bLAnk] falsE /*IU|
\*/ 
0 /*x&eR#[*/ && %2f nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*a|V*/ && %09 NOT ~ [BLANK] fAlse /*iU|
\*/ 
0 %20 && %0c noT ~ [blAnK] FaLSE /*i7rfZ*/ &"
0 /*A|V*/ && %09 nOt ~ [blANK] falSE /*IU|
\*/ 
0 /*x&eR#[*/ and %2f not ~ [blank] false /*iN*:*/ \-C
0 /*x&eR#[Te*/ && %20 nOT ~ [bLaNK] FalsE /*oy*/ &"
0 /*A|v*/ && %09 nOT ~ [blANK] fALsE /*iU|
\*/ 
0 /**/ AND %0c Not ~ [blaNK] falsE /*I7R*/ &"6W
0 /*A|VW9`*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /**/ && %09 NOT ~ [bLaNK] fAlsE /*I*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /**/ \
0 /*a|v*/ && %20 nOT ~ [Blank] FalSE /*iU|
\*/ 
0 /**/ and %2f not ~ [blank] false /*i*/ 
0 /*A|v*/ && %0C not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6WC
0 /*x&er#[*/ && %09 Not ~ [BLaNk] FaLSe /*iN*:*/ \
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a)
0 /*X&Er#[*/ && %09 Not ~ [bLANk] FaLse /*IN*:*/ \)D
0 /**/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 /*A|v*/ and %20 not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*A|V*/ && %2f Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \Oe
0 [blank] && /**/ nOt ~ [bLAnk] False /**/ 
0 [BlanK] aND + ! [bLANk] 1 /*n
S*/ 
0 /*X&er#[*/ && %20 NOt ~ [BLaNK] fALSE /*I7R*/ &"B
0 [blank] && %2f not ~ %20 false /**/ 
0 /*x&ER#[*/ && + NOt ~ [blANk] FALse /*I*/ \
0 /*x&eR#[m4*/ && %0D nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*X&ER#[Te*/ && %0D nOT ~ [BlaNk] FALSE /*O*/ &"
0 /*X&eR#[*/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \9M
0 %0D AnD %2F Not ~ [Blank] False /*i*/ 
0 /*x&ER#[te1*/ && %0D NOT ~ [blaNK] FalsE /*o*/ &"
0 /*A|V*/ && /**/ nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /*X&ER#[TE1*/ && %0d NOt ~ [BLAnK] fALse /*o*/ &"
0 /*X&er#[*/ && %20 nOT ~ [bLAnk] FAlSe /*I7r*/ &"
0 /*A|v*/ && %20 NOt ~ [BlANK] FaLSe /*iu|
\*/ 
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"d
0 /*X&eR#[te*/ && %0D not ~ [bLANK] FAlsE /*O*/ &"
0 /*x&eR#[U*/ && %20 not ~ [blank] false /*i*/ \CK
0 /*X&ER#[*/ && %0d not ~ [BLANk] fALSE /*I*/ \
0 /*X&*/ aND %20 noT ~ [bLanK] FaLSE /*I*/ 
0 /*x&eR#[*/ && + not ~ /**/ false /*i*/ \
0 /*x&eR#[*/ && %0C not ~ [blank] false /*iN*:*/ \A
0 /*A|V*/ && %20 NOt ~ [blaNk] FALse /*Iu|
\*/ 
0 /*GU*/ && %0A Not ~ [blAnk] faLse /*i7R*/ &"6W
0 ) [blAnk] || [BLAnk] 1 - ( [bLAnk] 0 ) -- [BlanK] 
0 /*a|v*/ && %2F NOt ~ [blaNK] FaLsE /*iu|
\*/ 
0 /*A|V*/ && %0A Not ~ [BLaNK] fAlSe /*IU|
\>aN]*/ 
0 /*a|V8;f*/ && %20 NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 /*x&er#[*/ && %20 NoT ~ [blAnk] FaLSE /*i7r*/ &"
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21d(
0 /**/ && %2f not ~ + false /*i*/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r[IwG*/ &"6W
0 /*X&er#[*/ and %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*x&er#[*/ && %09 NOT ~ [bLANK] fAlSe /*In*:oSx*/ \
0 /*a|v*/ && %2F Not ~ [BlAnK] FaLSe /*Iu|
\*/ 
0 /*,|*/ ANd /*F#."z*/ ! + tRUe /*j/fZU*/ i
0 /**/ && %2f NOt ~ [bLanK] faLse /*i*/ 
0 /*A|v*/ and %09 nOT ~ [BlANk] False /*IU|
\*/ 
0 /*X&er#[*/ && %09 nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*a|V*/ && %0A NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 %20 and %20 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*a|Vw9*/ && %20 NOt ~ [bLaNK] fALSe /*iu|
\*/ 
0 /*b*/ ANd [blank] ! [BLanK] tRue [blaNk] 
0 /*A|Vq*/ && %0c Not ~ [blanK] faLSe /*iu|
\*/ 
0 %09 && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /**/ && %20 not ~ [BLAnK] fALSe /*I*/ 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \9!
0 /*A|vQ*/ && %20 NoT ~ [blaNK] FaLSE /*Iu|
\*/ 
0 /**/ && %09 not ~ [blank] false /*iN*:*/ \)D
0 /*A|V*/ and %2f NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*x&er#[*/ and %0A NOT ~ [blANk] faLsE /*i7R*/ &"
0 /*x&Er#[Te1*/ && %0D Not ~ [BLanK] faLSe /*o*/ &"
0 /**/ && /**/ nOT ~ [bLanK] FalsE [BlaNk] 
0 [blANk] AND [blANK] ! [BLaNK] 1 /*n
S*/ 
0 /*X&ER#[M4*/ && %0A noT ~ [BLanK] FAlSe /*i7r*/ &"
0 [bLANK] ANd [BLank] ! [BlaNk] 1 /*N
sm9N6`<24L*/ 
0 /*A|v*/ && %20 noT ~ [BLANK] fAlSe /*iu|
\*/ 
0 /*x&ER#[te*/ && %20 NOt ~ [blANK] FALSe /*O*/ &"
0 /*x&eR#[m4*/ and %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*X&er#[*/ && %09 NoT ~ [bLank] FalSE /*I7r*/ &"
0 /*A|V*/ && %09 not ~ [BlAnK] FalSe /*iU|
\ki*/ 
0 /*x&eR#[*/ && %09 not ~ [BLaNk] faLse /*IN*:OSX*/ \
0 /*x&eR#[*/ && %2f not ~ %20 false /*i*/ \CK
0 /*A|VW9*/ && %0C NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*A|v*/ && %09 nOT ~ [BlANk] False /*IU|
\H`eh*/ 
0 /*A|V*/ && /**/ Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&er#[e*/ && %0c nOT ~ [bLAnk] faLSE /*I7R*/ &"
0 /*X&eR#[*/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*x&Er#[e*/ && %0c Not ~ [BLaNK] FALse /**/ &"
0 /*a|v*/ && %2f NOT ~ [bLAnk] FaLse /*IU|
\*/ 
0 + and %09 NOt ~ [bLAnK] FaLsE /**/ 
0 /*Gu*/ && %2F noT ~ [bLaNk] faLse /*i7R*/ &"6w
0 /*A|V**/ && %2f Not ~ [BLaNK] fAlSe /*IU|
\*/ 
" ) [bLANk] ; seLECT /**/ SleEp ( [terDigiteXClUdIngzeRO] ) -- [BLank] 
0 /*a|V*/ && %0D nOT ~ [BLaNK] FAlSe /*iu|
\*/ 
0 ) [blaNk] or ~ [BLanK] [bLank] 0 -- [BLanK] 
0 /**/ anD /*kg5*/ not ~ [bLANk] fAlSE [blANK] 
0 /*X&ER#[*/ && %20 Not ~ [BLank] fAlsE /*I7r=g~K0r.*/ &"
0 /*a|V*/ and %20 nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /*iJ<*/ \
0 /*Gu*/ && %2F not ~ [BLaNK] fAlSE /*i7R*/ &"6W
0 ) /**/ && [BLank] 0 # 
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">a

0 /*x&eR#[*/ && %0C not ~ [blank] false /*iN*:*/ \
0 /*x&eR#[*/ && %0d NOt ~ [bLAnk] falsE /*I7r*/ &">a
0 /*X&er#[*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*x&Er#[e*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"A
0 /**/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /*x&ER#[*/ and %20 noT ~ [BlANk] fALSE /*i7r*/ &"
0 /**/ && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /*,X9*/ && [blank] ! ~ [BlaNK] 0 [bLaNK] 
0 /*gu*/ && %2f not ~ /**/ false /*i7r*/ &"6w
0 /**/ && %2f not ~ [blank] false /*iN*:*/ \
0 /*x&eR#[7y*/ && %0A not ~ [blank] false /*iN*:*/ \
0 /*X&eR#[*/ && %2f Not ~ [BLank] falSE /*i*/ \V
0 /*A|V*/ && + NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 ) [bLaNK] && /**/ 0 # dM
0 /*X&eR#[*/ && %0d nOt ~ [bLaNK] FALse /*i7r=G~K*/ &"
0 /*X&er#[*/ and %0A nOt ~ [BlAnk] fALse /*i7r*/ &"
0 ) [BlANk] anD /**/ fALsE /**/ Or ( 0 
0 /*X&er#[*/ and %09 nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*gu*/ && %0D noT ~ [bLAnk] FaLSe /*I7r*/ &"6W
0 /*x&eR#[*/ && %2f not ~ %20 false /*i*/ \
0 [BlANK] AnD [BLanK] nOt ~ ' ' %2f 
0 %20 && %09 not ~ [BlANK] faLse /*i7rRtNF]*/ &"
0 /**/ && %2f not ~ [blank] false /*i*/ 
0 /*X&er#[*/ && %0A NoT ~ [BlAnK] faLse /*i7rZT*/ &"
0 /**/ && /**/ Not ~ [BlANK] falsE [BlanK] 
0 /*A|V*/ and %0A Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&er#[tE1*/ and %0d nOt ~ [bLANk] fAlSE /*O*/ &"
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \U
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /**/ &">a
0 /**/ && %0D NOt ~ [bLanK] faLse /**/ 
0 [bLanK] && /**/ ! [blank] True /**/ 
0 /*a|V*/ && %20 nOT ~ [BlAnk] fAlSE /*IU|
\~NF$*/ 
0 /*x&eR#[*/ && %0D NoT ~ [bLANk] fALSe /*I*/ \
0 /*x&Er#[*/ and %09 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*-)aCr*/ && %2F NoT ~ [BLANK] FaLse /*i\vRS*/ 
0 /*A|v*/ && %0D nOT ~ [BlANk] False /*IU|
\*/ 
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rQ?*/ &"
' ) [blank] [blank] [blank] true [blank] true /**/ or ( '
0 /*A|VW9PBjyM*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*X&Er#[*/ && %0c NoT ~ [BLaNk] fALSe /*i7r*/ &"21
0 /*x&eR#[*/ && %20 not ~ [blank] false /*iN*:*/ \-C
0 /*a|V*/ && + NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 /*X&er#[*/ && %09 Not ~ [BLanK] fALSe /*I7R*/ &"
0 %20 && %09 nOT ~ [BlAnk] faLsE /*i7RRTNf]*/ &"
0 /**/ && %09 nOt ~ [bLaNk] FAlSE /*i*/ 
0 %20 && %2f not ~ [blank] false /*i7rrTnF]*/ &"
0 /*A|V*/ && %20 NOt ~ [BLanK] faLSE /*IU|
\*/ 
0 /**/ && %0C not ~ [blank] false /*iN*:*/ \
0 /*A|V*/ && %20 NoT ~ [bLaNK] fAlSE /*IU|
\*/ 
0 /*x&ER#[E*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 %20 && %0C NOT ~ [bLaNk] fALSe /*I7R*/ &"
0 /*a|V*/ && %09 noT ~ [bLAnk] FAlSE /*IU|
\kI*/ 
0 /*x&eR#[\KO*/ && %09 not ~ [blank] false /*iN*:*/ \)D
0 /*x&Er#[*/ && %0c nOt ~ [bLAnk] FaLSE /*i7R*/ &"
0 /*x&ER#[*/ && %0A NOt ~ [blAnk] falSe /*i7r*/ &"
0 /*x&eR#[K-7TG*/ && %2f not ~ [blank] false /*iN*:*/ \-C
0 /*x&eR#[*/ && %0C not ~ [blank] false /*iN*:usj.*/ \
0 /*x&er#[@q!*/ && %0d nOT ~ [blANk] FaLsE /*I7R*/ &">a
0 /*x&eR#[*/ && %0D not ~ [blank] false /**/ \
0 /*x&eR#[*/ && %0D Not ~ [BlANK] falSe /*I7r*/ &"
0 /*x&eR#[*/ && %09 not ~ + false /*iN*:*/ \)D
0 /*A|Vq,*/ and [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 %20 && %0C noT ~ [BlanK] falsE /*i7r*/ &"
0 /*X&eR#[*/ && %0C Not ~ [BLaNK] FAlSE /*i7R*/ &"6w_l
0 /*A|v*/ && %0C nOT ~ [BlANk] False /*IU|
\*/ 
0 /*A|V*/ && %20 Not ~ [blAnK] fALSe /*IU|
\*/ 
0 /*X&ER#[*/ && %2f Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*X&er#[G+u
r*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*a|Vq*/ && %20 NoT ~ [bLanK] False /*iU|
\*/ 
0 /**/ && /**/ NOT ~ [blanK] fALsE [BlAnK] 
0 [BLAnk] AnD %20 ! [blAnK] truE /*%~*/ 
0 ) [BLANk] && /**/ 0 # dM
0 /*x&ER#[e*/ && %0C noT ~ [bLAnK] fAlSe /*I7R*/ &"
0 /**/ && [bLAnK] ! ~ [bLAnK] 0 [BlANK] 
0 /*x&ER#[*/ and %2F nOt ~ [BLank] fAlsE /*In*:*/ \-c
0 /*x&eR#[q[Qh#*/ && %2f not ~ [blank] false /*i*/ \CK
0 /*x&eR#[ARRh*/ && %2f not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %0A not ~ %20 false /*iN*:*/ \
0 /*A|V*/ && %0C NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&er#[*/ && %0C not ~ [BLANK] falSe /*IN*:*/ \
0 /*X&Er#[*/ and %2F NoT ~ [blaNk] falsE /*i*/ \
0 /**/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /**/ &">a
0 /*X&er#[*/ && %0d NOT ~ [BLAnk] faLSE /*I7R*/ &">a
0 /*X&ER#[*/ && %0A not ~ [BlanK] false /*I7R*/ &"
0 /*a|vq*/ && %20 NoT ~ [BlANK] FALSe /*iu|
\*/ 
0 /*X&er#[@q!*/ && %2f nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r?*/ &">a
0 /*x&Er#[e*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 %20 and %2F NoT ~ [BLANK] FaLse /**/ 
0 /*x&eR#[*/ && %0C not ~ %20 false /*iN*:*/ \
0 /*A|V@l*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&ER#[uTSr0*/ && + nOT ~ [bLank] faLSe /*i*/ \
0 [bLaNK] && [BLaNK] ! ~ ' ' [bLAnk] 
0 [blAnK] aNd [blaNK] ! [bLaNK] TRUe /*j/FZU*/ 
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">a#
0 /*x&eR#[r*/ && %2f not ~ [blank] false /*iN*:*/ \-C
0 /*X&ER#[{Q}@*/ && %0C noT ~ [BLaNK] fALsE /*i7R*/ &"
0 /*Gu*/ && %2f noT ~ [BlaNK] faLse /*i7r*/ &"6W
0 [blANK] aND /**/ ! [bLank] TrUE /**/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /**/ &"6W
0 /*X&er#[*/ && %0d nOT ~ [BLANK] fALSe /*I*/ \
0 /*A|V*/ && %20 not ~ [blaNK] falSe /*iu|
\*/ 
0 ) [BlANk] aND /**/ 0 # dm
0 /*x&eR#[*/ && /**/ not ~ [blank] false /*i*/ \CK
0 ) [blAnK] anD /**/ 0 # DM`
0 /*a|V*/ && %20 NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 /*x&eR#[YWd*/ && %2f not ~ [blank] false /*iN*:*/ \
0 /**/ && %0d NOt ~ [BlanK] false /*i7r*/ &">a
0 /*a|v*/ && %20 nOt ~ [BLAnk] FalsE /*iu|
\*/ 
0 /*x&er#[*/ anD %20 noT ~ [blANk] FAlsE /*i7r*/ &"
0 ) [BLank] and %20 0 # 
0 /*X&er#[tE1*/ && %0d nOt ~ [bLANk] fAlSE /**/ &"
0 /**/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">a
0 /*X&eR#[*/ && %0C not ~ [BlanK] false /*IN*:*/ \
0 /*x&Er#[*/ && %0C nOt ~ [blAnk] falsE /*I7r*/ &"6w
0 /*x&eR#[*/ and %09 not ~ [blank] false /*i*/ \CK
0 /*A|v*/ && %0C Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 /*X&er#[*/ && /**/ nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*A|V*/ && %2f NoT ~ [bLANK] FaLsE /*Iu|
\A]}/.*/ 
0 /**/ && %20 NOT ~ [blAnk] FaLSE /*i*/ 
0 /*x&Er#[*/ && %0d noT ~ [BLANk] fAlSe /*i7r*/ &"
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"U
0 /*a|v*/ && %0D nOT ~ [BlanK] faLsE /*IU|
\*/ 
0 /*x&er#[*/ && %2f Not ~ [Blank] FaLSE /*I*/ \
0 /*A|vQ*/ && %0C NOT ~ [BlANk] FalSE /*iu|
\*/ 
0 /**/ && %20 not ~ [blank] false /*i*/ \
0 /*x&er#[m4*/ && %2f Not ~ [BLanK] FAlSE /*i7r*/ &"
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &"y
0 /*X&er#[*/ && %0C NoT ~ [blaNk] FalsE /*I7r*/ &"6wbW
0 /**/ aNd %20 nOT ~ [BlANK] FAlSe /*i*/ 
0 [BlAnK] aNd %20 ! [bLaNk] 1 /*n
s*/ 
0 /*x&eR#[	M$f*/ && %2f not ~ [blank] false /*i*/ \v
0 /*X&Er#[tE*/ && %09 nOt ~ [blANk] faLSE /*o*/ &"
0 /*X&Er#[E*/ && %0C noT ~ [BlAnk] FalSe /*i7R*/ &"
0 /*A|V*/ and %20 NOt ~ [bLAnk] fAlse /**/ 
0 [BlaNk] aNd /**/ ! [BLANk] tRuE /**/ 
0 /*a|vq*/ && + Not ~ [BlanK] FalSe /*iu|
\*/ 
0 /*A|Vq*/ && [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \v
0 /*A|v,*/ && %2f Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 /*x&eR#[*/ && %0C not ~ [blank] false /*i*/ \v
0 /**/ && %2F NoT ~ [blaNk] falsE /*i*/ \
0 /*X&er#[*/ && %0A nOt ~ [BLAnk] fAlSe /**/ &"
0 /*X&er#[*/ && %0c nOt ~ [BLaNK] FaLsE /*i7R*/ &"6wBW
0 /*X&ER#[?KS6<*/ && %0D noT ~ [bLAnK] false /*I7r=g~K*/ &"
0 /*X&ER#[*/ && %0d NOt ~ [BlanK] false /*i7r*/ &">a
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W_l+
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /**/ &">a
0 /*X&er#[*/ && %2f nOt ~ [bLaNk] FALSE /*i*/ \Ck
0 /**/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*X&ER#[*/ && + NOT ~ [BlAnk] FalsE /*i*/ \58
0 /*x&ER#[*/ && %0a not ~ [BlANk] FaLSe /*in*:*/ \\j
0 /*x&er#[ek*/ && %0A NOT ~ [blANk] faLsE /*i7R*/ &"
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &"=x
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \
0 /*A|V*/ && %20 NoT ~ [BlANK] fALSe /*Iu|
\*/ 
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"
0 [bLAnk] AnD %20 Not ~ [bLAnk] fAlse /*I*/ 
0 /*X&ER#[*/ && %0d NOt ~ [BlanK] false /**/ &">a
0 /*X&ER#[*/ && [blank] Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*X&ER#[*/ && %0c NoT ~ [bLAnK] FALSe /*i7R*/ &"6w
0 /**/ and %0A NOt ~ [BlaNK] faLSE /*I*/ 
0 /*x&eR#[*/ && %0C not ~ [blank] false /*iN*:?|*/ \
0 /*X&Er#[^
_Q7*/ && %2F NoT ~ [blaNk] falsE /**/ \
0 %20 AnD %2F Not ~ [Blank] False /*i*/ 
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /*i*/ \
0 /*X&Er#[*/ && %09 not ~ [bLAnk] fALsE /*in*:OSX*/ \
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"z
0 /*A|v*/ && %0D NOT ~ [bLANk] faLSE /*iu|
\*/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W_l(0
0 %20 && %0c noT ~ [blAnK] FaLSE /*i7r2%_*/ &"
0 /*a|vQ*/ && %20 noT ~ [BlAnk] fAlSe /*IU|
\*/ 
0 /*A|vQ*/ && %20 not ~ [Blank] FAlSE /*IU|
\*/ 
0 /*a|v*/ && %20 Not ~ [BLANk] FALsE /*Iu|
\*/ 
0 /**/ && [blaNk] Not ~ [BLAnK] faLSE /*I*/ 
0 /*A|v*/ && %09 nOT ~ [BlANk] False /*IU|
\kI*/ 
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">ay
0 ) [bLAnK] and %20 0 # DM`
0 /*x&eR#[*/ and %2f not ~ [blank] false /*i*/ \v
0 /*x&eR#[^o5D*/ && %2f not ~ [blank] false /*i*/ \CK
0 /*,|*/ and /**/ ! + true /*J/FZu*/ i
0 /*x&eR#[m4*/ && %20 nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*A|v.H*/ && %2f Not ~ [bLAnk] FALSe /*iU|
\*/ 
0 [blaNK] aND /**/ fALsE %20 
0 /*x&Er#[Arr*/ && %2f not ~ [blANK] faLse /*I*/ \
0 /*x&eR#[Te*/ && + nOT ~ [bLaNK] FalsE /*o*/ &"
0 /*x&*/ ANd %2F NOt ~ [BlANk] FAlse /*i*/ 
0 /*x&ER#[*/ && %0c NoT ~ [bLANK] FaLse /*i7r*/ &"6w
0 /**/ && /**/ ! [blANK] tRUE /*J/FZu*/ 
0 /*x&eR#[*/ && %2f NOT ~ [Blank] FAlSE /*i*/ \
0 /*a|VQ*/ && %0c not ~ [BLaNk] falSE /*Iu|
\*/ 
0 /*gu*/ and %2f not ~ [blank] false /*i7r*/ &"6w
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /**/ &"21
0 /*x&Er#[*/ and %09 noT ~ [bLAnK] falsE /*IN*:*/ \
0 /*X&ER#[*/ && %0d NOt ~ [BlanK] false /*i7r*/ &">a-t
0 /*x&eR#[*/ && %2f not ~ /**/ false /*i*/ \v
0 /**/ aNd /*i*/ ! [BLANk] tRue [bLAnk] 
0 ) [BLANK] AND [blank] 0 # DM
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \CKo
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \CK
0 /*x&er#[*/ && %0A NOT ~ [blANk] faLsE /*i7R*/ &"
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21Y
0 /*A|v*/ && %2f not ~ + false /*iu|
\&~*/ 
0 /*x&er#[*/ && %09 noT ~ [BlaNk] faLse /*iN*:*/ \
0 /*A|V*/ and %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 ) [blAnk] anD /**/ ! [BLaNK] TRUe [BlANK] or ( 0 
0 /*A|v*/ && %09 nOT ~ [BlANk] False /*IU|
\*/ 
0 /**/ && %09 nOT ~ [BlanK] FaLSe /*I*/ 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \)D
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /**/ \
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"8^
0 /*x&eR#[*/ && %0D not ~ [blank] false /*i*/ \CK
0 /**/ && %0a NOT ~ [BLanK] FAlsE /*I*/ 
0 /**/ && %0D NOt ~ [BlaNK] faLSE /*I*/ 
0 /*x&eR#[*/ && %0D not ~ [blank] false /*iN*:*/ \
0 /*X&er#[*/ && %20 NoT ~ [bLaNK] FalsE /*i7R=G~K*/ &"
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*obe$*/ &"
0 /*B*/ aND /**/ ! [bLAnK] True [bLanK] 
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"y

0 /**/ AND /**/ NoT ~ [BLanK] fALsE [bLaNK] 
0 /**/ AnD /**/ nOt ~ [bLanK] fAlse [blAnk] 
0 /*X&er#[*/ && %2f nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7r*/ &">ap
0 /*X&er#[*/ && %0A NOT ~ [blAnK] FaLsE /*I7r*/ &"
0 /*X&ER#[*/ && %0d noT ~ [blaNk] FAlse /*I7r*/ &"
0 /*A|V*/ && [blank] nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /**/ AND %20 noT ~ [bLaNk] FaLsE /*i*/ 
0 [blank] && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /**/ And %0D Not ~ [BLanK] fALsE /*i*/ 
0 /*,|*/ && /*F#."z*/ ! %0D true /*J/FZu*/ i
0 /*SV,W*/ aNd %20 nOT ~ [BlANK] FAlSe /*i*/ 
0 /*A|V*/ && %0d not ~ [BLAnk] faLSe /*iu|
\*/ 
0 /*A|Vq>~o=*/ && %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&eR#[7(Fa*/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*,|*/ && /**/ ! %20 true /*J/FZu*/ i
0 /*x&eR#[*/ && %0A nOT ~ [blaNk] falSe /*i7R*/ &"
0 /*x&eR#[*/ && %0A not ~ [blank] false /*i*/ \CK
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i~*/ \
0 /*x&eR#[Te*/ && /**/ nOT ~ [bLaNK] FalsE /*o*/ &"
0 [blank] && ' ' [blank]
0 /*x&Er#[eDha*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /*X&eR#[*/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \R
0 /*x&ER#[*/ && %09 Not ~ [BlaNk] FaLSe /*in*:osx*/ \
0 /**/ and %2f NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*x&Er#[*/ && %0c Not ~ [blAnK] fAlSe /*i7r;*/ &"6WBw
0 /**/ && /**/ ! [BLaNk] TRUE /*J/FZu
2wfH*/ 
0 /*x&er#[*/ && %0d NOt ~ [BLanK] FAlse /*i7r*/ &"
0 /*A|v*/ && %2f nOT ~ [BlANk] False /*IU|
\*/ 
0 /**/ and %09 not ~ [blank] false /*iN*:*/ \)D
0 /*x&ER#[3+`,*/ && %2F nOt ~ [BLank] fAlsE /*In*:*/ \-c
0 %20 && %0C not ~ [blANk] fAlSe /*i7R*/ &"
0 %20 && %0C NOt ~ [BlaNk] falsE /*I7R*/ &"
0 /**/ && /*I-fW*/ nOt ~ [BLAnk] FALSE /**/ 
0 /*x&Er#[*/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"21$Z
0 /*x&eR#[_'p
*/ && %2f not ~ [blank] false /*i*/ \
0 [BLAnK] oR ~ [BlAnK] /**/ fAlSe /**/ 
' ) [BLaNk] AnD /**/ NoT ~ ' ' # 
0 /*x&Er#[*/ && %09 NoT ~ [BlAnK] FalSE /*in*:*/ \]
0 /**/ && /**/ ! [blANK] tRUE /*J/FZu
2wfH*/ 
0 /*x&Er#[*/ && + NOt ~ [bLank] false /*i*/ \
0 [BLAnk] AnD /**/ ! [blAnK] truE /*%~*/ 
0 /*x&Er#[*/ && %0c nOt ~ [bLAnK] fAlse /*i7R*/ &"6WBw
0 /*gu*/ && %2f not ~ %20 false /*i7r*/ &"6w
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \g
0 /*x&er#[*/ && %0A NOT ~ [blANk] faLsE /*i7R */ &"
0 /*A|V*/ && %0D Not ~ [BlAnK] fALSE /*iU|
\*/ 
0 /**/ and %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*A|VQ*/ && %0D NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 /*x&er#[*/ && %20 NOT ~ [BlanK] fALsE /*I7r*/ &"B
0 /*GU*/ && %09 Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /*x&ER#[*/ && + nOT ~ [bLank] faLSe /*i*/ \
0 /*X&er#[*/ && %0C nOT ~ [bLank] fALse /*i7R*/ &"
0 [BlanK] And %20 NOT ~ [blanK] FAlSe /*i`ZUMA*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i42L*/ \v
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \v
0 /**/ && %09 NoT ~ [blaNK] faLse /*In*:*/ \
0 /*x&eR#[*/ && %2F noT ~ [blANk] FALse /*I*/ \
0 /*A|V*/ && %20 NoT ~ [bLANK] FaLsE /*Iu|
\gV*/ 
0 %20 && %0c not ~ [blaNK] false /*i7R*/ &"
0 ) [BLaNk] ANd /**/ 0 # DM
0 /*X&er#[Te*/ && %20 NoT ~ [blaNk] faLSe /*O*/ &"
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"P
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \CKK!
0 /*X&er#[Te*/ && %0d Not ~ [BLank] faLSE /*O*/ &"
0 /*x&eR#[*/ AND %20 NOT ~ [Blank] falsE /*I7R*/ &"H
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \)Dp
0 [BlAnK] aNd /**/ ! [bLaNk] 1 /*n
s*/ 
0 /*A|Vq*/ and %20 Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /**/ && + not ~ [blank] false /**/ \
0 /*x&eR#[*/ and %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*X&Er#[te1*/ && %0d NOt ~ [BLanK] fALsE /*O*/ &"
0 [BlAnK] aNd [blank] ! [bLaNk] 1 /*n
somB*/ 
0 /*X&Er#[*/ && %0c not ~ [BLaNK] FalSe /*i7R*/ &"21
0 /**/ and %09 nOT ~ [BlanK] FaLSe /*I*/ 
0 /*A|V*/ && %0C Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&Er#[*/ && %0a NOT ~ [blaNk] fALSE /*i7r*/ &"
0 /*A|V*/ && /**/ NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \b
0 /**/ && %09 not ~ [blank] false /*i*/ \CK
0 /*x&er#[m4*/ && %0A Not ~ [BLanK] FAlSE /**/ &"
0 /*x&eR#[Y~m]*/ && %09 not ~ [blank] false /*iN*:*/ \)D
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7rr*/ &"
0 /*x&eR#[*/ && %2f Not ~ [bLaNk] fALSe /*iN*:*/ \
0 /*X&ER#[0\8M
*/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"
0 /*A|VQ*/ && %0C NOT ~ [BLaNk] fALsE /*iu|
\*/ 
0 /**/ OR ~ + [bLanK] FALSe [BLAnK] 
0 /*A|v*/ && %0A not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*X&er#[*/ && %09 NoT ~ [BLank] FAlse /*In*:*/ \
0 /*x&eR#[*/ && %0C nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
0 /*x&eR#[*/ and %2f not ~ [blank] false /*i*/ \CK
0 ) [BLANK] AND /**/ 0 # DM
0 /*x&eR#[*/ && %2f not ~ [blank] false /**/ \CK
0 ) [bLANK] aND [BlANK] 0 # dm
0 /*x&ER#[{Q}@*/ && %0C not ~ [bLANK] fALSe /*i7r*/ &"
0 /*x&ER#[*/ && %0A NOt ~ [blank] FAlsE /*IN*:*/ \
0 /*x&ER#[*/ && + nOT ~ [bLank] faLSe /**/ \
0 /**/ && %0D NOt ~ [bLanK] faLse /*i*/ 
0 /**/ && %09 NOT ~ [bLANK] falSe /*i7r*/ &"6w
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /**/ \
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r&K~P*/ &"6W_l
0 /**/ and [blank] ! ~ [BlaNK] 0 [bLaNK] 
0 /*A|Vq*/ and %0c Not ~ [BLanK] FalSe /*Iu|
\*/ 
0 ) [bLAnK] && /**/ 0 # DM`
0 /**/ && %09 nOt ~ [bLANk] fAlSE /*i*/ 
0 /*a|Vq*/ && %2f nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /**/ && %2f not ~ [blank] false /*i*/ \
0 /*x&ER#[*/ && %09 NOT ~ [BLANk] False /*in*:*/ \
0 /*X&er#[*/ && %09 Not ~ [blaNk] falSE /*i7r*/ &"
0 /**/ && %0A nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*GU*/ && %2f noT ~ [bLANk] faLSe /*i7r*/ &"6w
0 /*A|v*/ && %20 NoT ~ [bLAnk] fALSe /*iU|
\*/ 
0 /*x&eR#[*/ && %20 not ~ [blank] false /**/ \
0 /*x&ER#[TE1*/ && %0D NoT ~ [BlANk] fALsE /*O*/ &"
0 /**/ && [BlANk] NOT ~ [Blank] fAlse /*IE*/ 
0 /*x&er#[*/ && %20 Not ~ [bLANk] FALsE /*i7R=g~K*/ &"
0 /*A|VW9&g*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&eR#[*/ and %20 not ~ [blank] false /*i*/ \
0 /*X&ER#[*/ and %20 Not ~ [BLank] fAlsE /*I7r=g~K*/ &"
0 /*x&Er#[*/ && %09 Not ~ [bLAnk] fALSE /*I*/ \ck
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*A|V*/ && %0A NOt ~ [BlaNk] FaLSE /*IU|
\*/ 
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \v
0 /*x&eR#[*/ && %0A not ~ [blank] false /*iN*:*/ \
0 /*X&er#[*/ && %20 nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*A|v,z*/ && %09 nOT ~ [BlANk] False /*IU|
\kI*/ 
0 /*X&er#[*/ && %0d NoT ~ [BlaNK] FALse /*I7r=G~k*/ &"
0 /*x&eR#[*/ && + not ~ [blank] false /*i3d*/ \58
0 /*x&eR#[*/ and + not ~ [blank] false /*i*/ \
0 /*A|V*/ && %0A Not ~ [BLaNK] fAlSe /*IU|
\ZiL*/ 
0 /*X&eR#[{Q}@*/ && %0c NoT ~ [bLANk] FaLsE /*i7R*/ &"
0 /*X&er#[~Ln*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*x&eR#[*/ && %0A not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %0d NOt ~ [BLAnk] faLsE /*I7r*/ &"
0 /*x&Er#[*/ && %2f nOt ~ [blAnK] FAlSE /*I*/ \
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*o*/ &"M
0 /*A|V*/ && [blank] NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /**/ && %0d Not ~ [BlAnK] fAlSE /*I7R*/ &"6W
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /*i */ \
0 /*gU*/ && %2f nOT ~ [BLaNk] FAlSE /*I7r*/ &"6w
0 /*x&eR#[*/ && %09 Not ~ [bLank] fAlSE /*In*:*/ \
0 [blaNK] anD [BLaNK] Not ~ ' ' /**/ 
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">aK
0 /*X&Er#[*/ && %0c nOt ~ [BlANK] False /*I7R*/ &"
0 /*X&Er#[f*/ && %2F NoT ~ [blaNk] falsE /*i*/ \
0 /*A|v*/ && %20 nOT ~ [BlANk] False /*IU|
\*/ 
0 /*x&eR#[*/ && %0D not ~ /**/ false /*i*/ \
0 /*,|*/ and /*F#."z*/ ! /**/ true /*J/FZu*/ i
0 /*A|v*/ && %0C nOT ~ [BlANk] False /*IU|
\kI*/ 
0 %20 && %20 NOt ~ [bLanK] faLse /*i*/ 
0 /*A|VQ*/ && + NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 [blank] && %2f not ~ %20 false /*i*/ 
0 /*x&eR#[ARR*/ && %0C not ~ [blank] false /*i*/ \
0 /*x&Er#[*/ && + not ~ [BLAnk] FalSE /*I*/ \58
0 /*X&Er#[*/ && %0a not ~ [BLanK] False /*I7r*/ &"
0 %20 && %0c noT ~ [blAnK] FaLSE /*i7r
p:kB*/ &"
0 /*X&er#[*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*x&eR#[*/ && %20 not ~ /**/ false /*i*/ \CK
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i4UvoG*/ \v
0 /*A|v*/ && [blANk] Not ~ [BlaNk] FalsE /*iu|
\*/ 
0 /*X&er#[*/ && %0C nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /*ij*/ \
0 /**/ && [bLaNK] ! ~ [blAnk] 0 [blaNK] 
' [BLank] || ~ [blanK] /**/ FaLsE /**/ || ' 
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"{G
0 /*A|V*/ && %2F noT ~ [BLank] false /*Iu|
\*/ 
0 /*x&eR#[*/ && %0C nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*x&Er#[e*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"%u
0 /*A|V*/ && [blank] NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*x&eR#[ARR*/ && %2f not ~ [blank] false /*ic~xG$*/ \
0 [bLanK] and /**/ ! [BlAnK] TrUe /**/ 
0 /*X&er#[tE1*/ && %0d nOt ~ [bLANk] fAlSE /*O`*/ &"
0 /*X&er#[*/ && %0c NOt ~ [bLanK] FAlsE /*I7R*/ &"
0 /*x&eR#[*/ and %0C not ~ [blank] false /*iN*:*/ \
0 ) [bLANk] aNd %0d 0 # DM
0 /*x&Er#[*/ && %2f NoT ~ [BlAnk] FalsE /*IN*:*/ \-C
0 ) [BLaNk] ANd [blank] 0 # DM
0 /*Sv,W*/ aNd %20 NOt ~ [BlANk] falSe /*i*/ 
0 /**/ && %0c not ~ [blaNK] faLSE /*I*/ 
0 /*gu*/ && %2F nOT ~ [bLAnK] fALSE /*i7r*/ &"6w
0 /*X&er#[*/ && %0A nOT ~ [blAnK] fAlse /*i7r*/ &"
0 /*x&eR#[*/ && + not ~ + false /*i*/ \58
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZta$4Nk*/ &"
0 /*A|v*/ && /**/ NOT ~ [blANk] fALse /*Iu|
\*/ 
0 /*A|VQ*/ && %09 NOt ~ [blaNK] FAlSE /*iu|
\*/ 
0 /**/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /*X&Er#[tE*/ && %0A nOt ~ [blANk] faLSE /*o*/ &"
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"T
0 /**/ and %09 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*A|Vq*/ && %20 Not ~ [bLank] FAlse /*Iu|
\*/ 
0 /*x&ER#[*/ && %0A noT ~ [BLAnk] falSe /*I7r*/ &"
0 /*X&Er#[{q}@*/ && %0C NoT ~ [BLaNK] faLse /*I7R*/ &"
0 /*a|V*/ && %0D Not ~ [blAnk] falSE /*IU|
\*/ 
0 /*A|VW9*/ && + NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&Er#[eeA*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /*x&eR#[*/ && %09 not ~ /**/ false /*i*/ \CK
0 /*X&ER#[*/ && %0d NoT ~ [bLaNk] fALsE /*I7r*/ &"]
0 /*A|V*/ && %0A nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /*x&Er#[*/ && %2F NoT ~ [BLank] FAlse /*I*/ \
0 /*X&er#[=d*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /**/ && %0A NOT ~ [blANk] faLsE /*i7R*/ &"
0 /**/ && %20 noT ~ [bLAnK] FaLSe /*I*/ 
0 /*,|*/ and /*F#."z*/ ! %20 true /*J/FZu*/ i
0 /*A|V*/ && %2f nOt ~ [BlaNk] FAlse /*iu|
\*/ 
0 /**/ && %2f NoT ~ + FalsE /*i*/ 
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"3
0 /*A|v*/ && %20 NOT ~ [BLank] falSe /*iU|
\*/ 
0 ) [bLAnK] && /**/ 0 # DMoz
0 /*A|VW94*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&Er#[*/ && %0C NOT ~ [blaNK] falsE /*I7R*/ &"6W
0 /**/ && %2f not ~ [blank] false /*iN*:*/ \-C
0 [bLANK] ANd [BLank] ! [BlaNk] 1 /*N
sm9N6*/ 
0 /*gu*/ && %2f not ~ [blank] false /*i7rK<z]*/ &"6w
0 /*x&er#[m4*/ && %0A Not ~ [BLanK] FAlSE /*i7r*/ &".:
0 /*x&eR#[*/ && + not ~ [blank] false /*iNm[ay*/ \
0 /*x&ER#[*/ && + nOT ~ [bLank] faLSe /*i*/ \.j
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i7r;*/ &"6wBW
0 /**/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W_l
0 /*a|vQ*/ && %20 not ~ [blaNk] falsE /*iu|
\*/ 
0 /*x&*/ ANd %2F NOt ~ [BlANk] FAlse /*ijX/*/ 
0 /*A|V*/ && /**/ NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 /*x&Er#[*/ && %0a noT ~ [BLanK] fALse /*i7r*/ &"
0 /**/ && %20 not ~ [BLAnK] fALSe /*I([GX*/ 
0 /*x&ER#[*/ && + nOT ~ [bLank] faLSe /*i*/ \xq
0 ) [bLAnK] anD /**/ 0 # dm
0 [bLaNK] anD /**/ ! [blANK] tRue /*j/fZu*/ 
0 /*X&Er#[*/ && %0a nOt ~ [Blank] FAlsE /*i7RzT*/ &"
0 /*A|Vq*/ && %0c Not ~ [BLanK] FalSe /*Iu|
\HNAF*/ 
0 /*X&er#[a%.	)*/ && %2F Not ~ [bLanK] falSE /*i*/ \
0 %20 && %0C NOT ~ [blaNK] false /*i7R*/ &"
0 [blanK] && [BlANk] faLSE [blANK]
0 /*X&ER#[*/ && %20 nOt ~ [blANk] FAlSe /*I7R=g~k*/ &"
0 [BLanK] && [BLANK] ! [BlanK] 1 /**/ 
0 /*,|*/ AND /*f#."Z*/ ! [bLAnk] tRue /*J/fZu*/ i
0 [BLAnk] AnD [blank] ! [blAnK] truE /**/ 
0 /*X&ER#[*/ && %20 noT ~ [bLAnK] FalSE /*i7R*/ &"B
0 /*x&Er#[e*/ && %0c Not ~ [BLaNK] FALse /*i7R<rM*/ &"
0 /*x&eR#[*/ && %2f not ~ [blank] false /*i*/ \CKW
0 %20 && %0c NOT ~ [BlANk] FAlse /*i7r*/ &"
0 /*X&eR#[*/ && %2f nOT ~ [Blank] FalsE /*I*/ \
0 ) [bLAnK] and /**/ 0 # DM`
0 /*x&eR#[Te*/ && %2f nOT ~ [bLaNK] FalsE /*o*/ &"
0 /*x&eR#[*/ && %0C not ~ [blank] false /*i*/ \CK
0 /*a|Vq*/ && %09 nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /*X&eR#[*/ && %09 NoT ~ [blaNK] faLse /*In*:*/ \
0 /**/ && + NOt ~ [bLAnk] FALse /*I*/ \
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &".
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \)D
0 /*X&er#[*/ && %09 nOt ~ [BLAnk] fAlSe /**/ &"
0 /*x&eR#[q&pm*/ && %2f not ~ [blank] false /*iN*:*/ \
0 [BLANk] AND + ! [bLAnK] tRuE /*%~*/ 
0 [blAnk] aND + ! [blank] 1 /*N
S*/ 
0 /*X&Er#[*/ && %2f noT ~ [blaNK] false /*i*/ \
0 /*A|v*/ AND %20 not ~ [bLanK] FaLSE /*Iu|
\*/ 
0 /*a|v*/ ANd %2f nOt ~ [BLAnk] falSe /*IU|
\*/ 
0 /*X&Er#[*/ && %09 not ~ [blank] FalSe /*IN*:*/ \
0 /*X&er#[*/ && %20 NoT ~ [BlAnk] FalSE /*i7R*/ &"
0 /*x&eR#[?Ks6<*/ && %0D noT ~ [BLAnk] FAlse /*I7R=g~K*/ &"
0 /**/ and %20 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*x&eR#[*/ && %2F NOT ~ [BlANK] faLSE /*in*:*/ \-C
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*A|v*/ && %20 not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*x&eR#[ARR*/ && %0D not ~ [blank] false /*i*/ \
0 /*X&Er#[*/ && %09 nOt ~ [bLAnk] FaLSe /*In*:OSX*/ \
0 /*x&eR#[*/ && %20 not ~ [blank] false /*iN*:*/ \
0 /*A|VW9*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&ER#[*/ && %0a not ~ [BlANk] FaLSe /*in*:*/ \3~
0 /*A|Vq*/ && %20 Not ~ [BLANk] faLse /*IU|
\*/ 
0 /*X&eR#[*/ && %09 NOT ~ [BLAnK] FALSE /*IN*:*/ \)d
0 /*a|V*/ and %20 NoT ~ [BlaNk] FAlSe /*iu|
\*/ 
0 /*x&eR#[*/ && %09 not ~ [blank] false /*iN*:*/ \`|
0 /*x&ER#[*/ && + nOT ~ [bLank] faLSe /*iN	K*/ \
0 /*a|v*/ && %2f nOt ~ [blAnK] FALse /*IU|
\*/ 
0 /*x&eR#[*/ && %0A NOt ~ [BlAnk] FalSe /*I7R*/ &"
0 /*GU*/ && %2f Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /*a|vQ*/ && %20 NOT ~ [BlANK] faLse /*iU|
\*/ 
0 /*X&er#[itjXc*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*a|vQ*/ && %20 nOt ~ [BLANK] faLSE /*Iu|
\*/ 
0 /*X&er#[*/ && %09 nOt ~ [BLAnk] fAlSe /*I7r*/ &"c
0 /*b*/ ANd + ! [BLanK] tRue [blaNk] 
0 /*A|vc*/ && %20 not ~ [blaNk] FaLSe /*IU|
\*/ 
0 /*X&Er#[te1*/ && %0D NOT ~ [bLank] falSe /*O*/ &"3
0 /**/ && %20 ! [blANk] TRue /*J/FZu
2wfH*/ 
0 /**/ && %20 ! [bLanK] TRUE /*J/fZu
2WFh*/ 
0 /*x&eR#[*/ and %0A not ~ [blank] false /*i*/ \
0 [BlAnK] aNd [blank] ! [bLaNk] 1 /*n
s*/ 
0 /*x&eR#[Te*/ and %20 nOT ~ [bLaNK] FalsE /*o*/ &"
0 /*X&Er#[tE*/ and %0D nOt ~ [blANk] faLSE /*o*/ &"
0 [blaNk] And %20 nOt ~ [BlANK] FAlSe /*i*/ 
0 /**/ and %0c not ~ [blaNK] faLSE /**/ 
0 /*x&Er#[*/ && %2f noT ~ [BLAnK] FALse /*i*/ \CK
0 + && %0c noT ~ [blAnK] FaLSE /*i7r*/ &"
0 /*x&ER#[*s*/ && + nOT ~ [bLank] faLSe /*i*/ \
0 /*x&eR#[*/ && %20 not ~ [blAnk] faLSe /*I*/ \ck
0 /*X&er#[*/ and %20 nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*X&er#[*/ && %0c nOT ~ [BlanK] faLSE /*i7r*/ &"
0 /*X&er#[*/ && %09 nOT ~ [BlAnK] falSE /*IN*:*/ \)d
0 /*X&eR#[*/ && %0c NOt ~ [blanK] FALSE /*i7r*/ &"
0 /**/ && %20 NOt ~ [BlaNK] faLSE /*I*/ 
0 /*x&eR#[qqSG*/ && %2f not ~ [blank] false /*iN*:*/ \
0 /*x&er#[*/ && %0A NOT ~ [blANk] faLsE /*i7R*/ &"
0 /*x&ER#[*/ && %0D nOt ~ [bLAnk] falSE /*I7R*/ &"
0 [BlANK] AnD [BLanK] nOt ~ ' ' %20 
0 /*x&er#[m4*/ && %0A Not ~ [BLanK] FAlSE /*i7rdf<F*/ &"
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt[>*/ &"
0 [blank] aNd %20 nOT ~ [BlANK] FAlSe /*i*/ 
0 /*x&ER#[*/ && %20 NoT ~ [BLAnk] FALSE /*i7R*/ &"B
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*o$U2$N*/ &"
0 /*X&eR#[I'Q*/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*X&er#[*/ && %0D nOt ~ [BlAnk] fALse /*i7r<dS?*/ &"
0 /*X&er#[*/ && %0d Not ~ [bLAnk] fALSE /*I7R*/ &">A
0 [blAnk] Or [BlANk] ! [blAnk] [BlaNk] 0 %20 
0 /*x&eR#[*/ && %0A Not ~ [BlANK] FALSE /*In*:*/ \
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21
0 /*X&*/ && [blaNk] Not ~ [BLAnK] faLSE /*Ie*/ 
0 /*x&eR#[SE*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
0 %20 and %2F NoT ~ [BLANK] FaLse /*i*/ 
0 /*x&eR#[*/ and %0D not ~ [blank] false /*i*/ \
0 /*x&eR#[*/ && %0D not ~ [blank] false /*iIK6 j*/ \
0 /*a|V*/ && %0d NOT ~ [blaNK] false /*iU|
\*/ 
0 /*A|VqR>*/ and [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*A|VW9*/ and %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /**/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7rcP*/ &"6W_l
0 /**/ && %20 nOt ~ [BlANK] FALSe /*i*/ 
0 /*x&eR#[ARR*/ && %2f not ~ %20 false /*i*/ \
0 /*GU*/ && %20 Not ~ [blAnk] faLse /*i7R*/ &"6W
0 /**/ && %09 NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*x&Er#[*/ && %2f NoT ~ [BlAnK] fALse /*in*:*/ \-C
0 /*x&Er#[*/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &"
0 /*x&eR#[m4*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"C
0 /**/ and %2F noT ~ [BlANk] faLsE /*i*/ 
0 /**/ && [BlAnk] ! [blanK] TRuE /*to|1x*/ 
0 /*X&Er#[M4*/ && %0A nOT ~ [bLAnk] falSE /*I7R*/ &"
0 /*X&eR#[*/ and %09 NoT ~ [blaNK] faLse /*In*:*/ \
0 [BLaNk] aND [BlAnK] ! [Blank] 1 /**/ 
0 /**/ && %0C not ~ [BlAnK] fAlSe /*In*:*/ \)d
0 /*A|v*/ && + nOT ~ [BlANk] False /*IU|
\*/ 
0 /*X&er#[TE1*/ && %0d NOt ~ [BLAnK] false /*o*/ &"
0 /*x&ER#[*/ && %0D Not ~ [bLANk] FalSE /*i7r*/ &">a
0 /*A|Vq,*/ && [blank] Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*x&ER#[*/ && %0D NOt ~ [bLAnk] FAlse /*i7R*/ &"
0 /*x&eR#[ARR*/ && %2f not ~ + false /*i*/ \
0 /*A|VW9*/ && [blank] NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&eR#[*/ && %09 not ~ + false /*i*/ \CK
0 /**/ && %2f not ~ [blank] false /*i*/ \v
0 /*A|VQ*/ && %20 Not ~ [BlAnK] faLSe /*IU|
\*/ 
0 /*x&Er#[*/ && + Not ~ [BLAnk] fAlsE /*I*/ \
0 /*X&Er#[;Wd3e*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W
0 /*x&eR#[*/ && %0A not ~ [blank] false /*iN*:*/ \-C
0 [bLaNk] AND %0A ! [blAnK] 1 /*n
S*/ 
0 /*X&er#[*/ && %0A nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 /*X&Er#[TE1*/ && %0d Not ~ [BLANK] fAlSe /*O*/ &"
0 /*x&eR#[*/ and %0A nOT ~ [BlaNK] FAlSe /*I7rZt*/ &"
0 /*a|V*/ && %20 NoT ~ [BlaNk] FAlSe /*iu|
\yu'k*/ 
0 /**/ and /**/ nOT ~ [BLAnk] faLSe [bLAnk] 
0 /*x&Er#[aRr*/ && %2f nOt ~ [BLAnK] FaLSe /*I*/ \
0 /*x&Er#[*/ && %0C Not ~ [bLanK] FAlSE /*i7r*/ &"6W_l
0 /*X&er#[*/ && %0A nOt ~ [BlAnk] fALse /**/ &"
0 /**/ and %20 nOt ~ [BLaNk] FALSe /*I*/ 
0 /*x&eR#[*/ && + not ~ [blank] false /**/ \
0 [BlAnK] aNd %20 ! [bLaNk] 1 /*n
somB*/ 
0 /*a|V*/ && %09 NOt ~ [bLank] FAlse /*iu|
\*/ 
0 /**/ && [blank] ! ~ [BlaNK] 0 [bLaNK] 
0 /*x&Er#[NV*/ && %09 Not ~ [blaNk] FAlsE /*i7R*/ &"
0 /*X&Er#[tE*/ && %0D nOt ~ [blANk] faLSE /*oj`*/ &"
0 [BLAnk] ANd [BlaNK] nOT ~ ' ' [BLAnK] 
0 /*x&eR#[*/ && %0A nOT ~ [BlaNK] FAlSe /*I7rZt4XH*/ &"
0 [blAnK] aNd [blaNK] ! [bLaNK] TRUe /*j/FZUCL*/ 
0 /*a|vQ*/ && %20 noT ~ [blanK] falSE /*Iu|
\*/ 
0 /*x&ER#[*/ && %0c Not ~ [BLAnk] FAlSE /*i7r*/ &"3U
0 + and %09 NOt ~ [bLAnK] FaLsE /*i*/ 
0 /*A|VQ*/ && %20 not ~ [blanK] faLSe /*IU|
\*/ 
0 /*XU*/ && %2f not ~ [blank] false /*i*/ 
0 /*X&er#[y.h[*/ && %0A nOt ~ [BLAnk] fAlSe /*I7r*/ &"
0 ) [blanK] && /**/ NOt /**/ 1 [blank] || ( 0 
0 /*X&er#[*/ && %0C nOt ~ [blANK] FAlSe /*i7r*/ &"6W
0 /*X&*/ && [BlANk] NOT ~ [Blank] fAlse /*IE*/ 
0 /*x&Er#[*/ && %20 NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*x&eR#[*/ && %2f not ~ [blank] false /*iN*:*/ \-C
0 /*A|Vq*/ && %0C Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 /*X&er#[*/ && %09 nOt ~ [BLaNK] FalSE /*i*/ \cK
0 /*X&eR#[{q}@*/ && %0c nOT ~ [blANK] falsE /*I7R*/ &"
0 /*X&Er#[*/ aNd %20 nOT ~ [BlAnK] FALSE /*i*/ \
0 /*a|V.j*/ && %20 nOT ~ [BlAnk] fAlSE /*IU|
\*/ 
0 /*X&er#[*/ && %20 NOT ~ [bLaNK] faLSE /*I7R*/ &"b
0 /*a|Vq*/ and %20 nOT ~ [BlAnk] faLSE /*iu|
\*/ 
0 /*X&eR#[*/ && %0a nOT ~ [BLanK] falsE /*i7R*/ &"
0 /*x&Er#[*/ && %0c Not ~ [BLaNK] FALse /*i7R*/ &"21Mc
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \'@
0 /*,|*/ && /*F#."ztRt*/ ! /**/ true /*J/FZu*/ i
0 /*x&Er#[*/ && %0C NoT ~ [BlAnK] FalSE /*in*:*/ \
0 /*A|vN2E*/ && %09 nOT ~ [BlANk] False /*IU|
\kI*/ 
0 /*x&er#[*/ && %0a not ~ [blaNk] FAlSe /*i7r*/ &"
0 /*X&Er#[*/ && %0a not ~ [BlAnk] False /*I7R*/ &"
0 /*a|VQ*/ && %0C NOt ~ [BLAnk] falsE /*Iu|
\*/ 
0 /**/ && %0A NOt ~ [BlaNK] faLSE /*I*/ 
0 /*a|vq*/ && %20 nOT ~ [blaNk] FaLSe /*iu|
\*/ 
0 [blank] and [blank] false [blank]
0 /*X&er#[@q!*/ && %0D nOt ~ [BlAnk] fALse /*i7r*/ &">a
0 /*x&eR#[*/ && %2f not ~ + false /*i*/ \v
0 /*A|Vq*/ && %20 NoT ~ [bLank] faLsE /*iU|
\*/ 
0 /*,|*/ ANd /*F#."z*/ ! + tRUe /*J/fZU*/ i
0 [blank] || [blank] 1 [blank] 
0 /*X&er#[*/ && %0A nOt ~ [BlAnk] fALse /*i7r*/ &"
0 /*X&eR#[*/ && %0d NOT ~ [blaNk] fAlSe /*I7rIOr*/ &">a
0 /*X&er#[*/ && %0C nOT ~ [BlANk] fAlSE /*I7r*/ &"
0 /*x&ER#[*/ && %0c NOT ~ [bLank] fALsE /*I7R*/ &"
0 /*x&eR#[*/ && %20 not ~ [blank] false /*i*/ \
0 /*x&Er#[@Q!*/ && %0D Not ~ [BlanK] FAlse /*I7r*/ &">A
0 /**/ && /**/ ! [BlaNk] tRUe /**/ 
0 /*X&Er#[*/ && %0c not ~ [bLanK] FalSe /*i7r*/ &"6W_lZX
0 /*x&eR#[*/ && %2F not ~ [bLaNK] FaLSe /*In*:*/ \-C
0 /*x&eR#[*/ && %09 not ~ [blank] false /*i*/ \CK
0 /*X&ER#[*/ && %0d NOt ~ [BlanK] false /*i7r@x,K*/ &">a
0 /*x&eR#[*/ && %0A not ~ + false /*iN*:*/ \
0 /*A|V*/ && %20 NOt ~ [blANK] FalSe /*iu|
\*/ 
0 /*x&eR#[*/ && %09 not ~ /**/ false /*iN*:*/ \
0 [blank] and /**/ nOT ~ [BLAnk] faLSe [bLAnk] 
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \qy
0 [BlANK] AnD [BLanK] nOt ~ ' ' %0C 
0 [bLaNk] AND %20 ! [blAnK] 1 /**/ 
0 /*A|V*/ and %0D Not ~ [BLaNK] fAlSe /*IU|
\*/ 
0 [BlAnK] aNd + ! [bLaNk] 1 /*n
s*/ 
0 /*x&Er#[*/ && %0A NOT ~ [BlanK] fAlSE /*i7r*/ &"
0 /*x&Er#[*/ and %0c Not ~ [BLaNK] FALse /*i7R*/ &"
0 %20 && %09 nOT ~ [BLANk] fALSe /*I7RRTNF]*/ &"
0 [blAnk] aND + ! [blank] 1 /**/ 
0 /*A|ve*/ && %20 nOT ~ [BlANk] False /*IU|
\*/ 
0 /*x&er#[*/ AnD %0c noT ~ [blaNk] fALSE /*I7rT*/ &"6W
0 /*X&er#[*/ && %0A not ~ [bLANK] fAlse /*in*:*/ \
0 /*X&Er#[tE*/ && %20 NoT ~ [BlaNK] fAlSe /*o*/ &"
0 /*X&er#[*/ && %0D Not ~ [BLaNk] fAlse /*i7R=G~k*/ &"
0 /*x&eR#[IP4~*/ && %0A nOT ~ [BlaNK] FAlSe /*I7r*/ &"
0 /**/ && %2F noT ~ [BlANk] faLsE /*i*/ 
0 /*X&eR#[*/ && %0d Not ~ [bLANK] fALse /*i7R*/ &">A
0 %2f && %09 not ~ [blank] false /*i7rrTnF]*/ &"
0 /*A|V*/ && %20 NoT ~ [bLANK] FaLsE /*Iu|
\*/ 
0 %20 && %0D NOt ~ [bLanK] faLse /*i*/ 
0 [blAnk] aND /**/ ! [blank] 1 /*N
S*/ 
0 /*x&Er#[*/ && %09 nOt ~ [bLaNK] falsE /*in*:*/ \
0 /*x&eR#[*/ && %09 not ~ [blank] false /**/ \CK
0 /*A|v*/ && [bLAnK] NOT ~ [BlAnk] FAlSe /*I*/ 
0 /*x&eR#[*/ and %09 not ~ [blank] false /*iN*:*/ \)D
0 /*X&eR#[*/ && %0A NoT ~ [blaNK] faLse /*In*:oSx*/ \
0 /*x&eR#[*/ && %2f not ~ /**/ false /*iN*:*/ \
0 /*x&er#[*/ && %09 NOT ~ [blANk] faLsE /*i7R*/ &"
0 %20 && %0c nOT ~ [BlANK] fALSE /*i7R*/ &"
0 /*X&ER#[te1*/ && %0D nOT ~ [BlAnk] falSe /*o*/ &"
0 /*X&Er#[*/ && %2F NoT ~ [blaNk] falsE /*i*/ \4
0 /*x&er#[Te*/ && %0d nOt ~ [BLank] FAlSE /*o*/ &"
0 [BLANk] AND /**/ ! [bLAnK] tRuE /*%~*/ 
0 /*X&*/ && [blaNk] Not ~ [BLAnK] faLSE /*I*/ 
0 /*X&er#[*/ && %0D not ~ [BlANK] fAlSE /*I7r*/ &"
0 /*x&ER#[*/ && %0a not ~ [BlANk] FaLSe /*in*:*/ \
0 /*A|v*/ && %09 nOT ~ [BlANk] False /*IU|
\kI#v*/ 
0 /*x&eR#[*/ && %09 NoT ~ [bLank] faLsE /*In*:*/ \
0 [BlAnk] && /**/ ! [BLaNk] 1 /**/ 
0 /*x&Er#[*/ && %0A NoT ~ [BlANk] FaLse /*i7r*/ &"
0 /*X&er#[*/ && %20 nOt ~ [BlAnk] fALse /*i7r*/ &"3r
0 /*a|vq*/ && + Not ~ [blANK] fALSe /*iu|
\*/ 
0 /*a|V*/ && %2f nOT ~ [bLaNK] FaLse /*IU|
\*/ 
0 ) [BLank] and /**/ 0 # 
0 /*X&er#[*/ && %0a Not ~ [bLank] FaLSe /*i7r*/ &"
