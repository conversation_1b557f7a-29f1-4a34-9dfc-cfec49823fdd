payload,specificity,MODSECURITY_expected,NGX_LUA_expected,SAFELINE_expected,NAXSI_expected,ALIYUN_expected
0 [blank] || /**/ ! ~ /**/ false /**/ is /**/ false [blank],0.75,通过,通过,拦截,拦截,通过
" ) /**/ or ~ /**/ /**/ false [blank] || ( ",0.75,通过,通过,拦截,拦截,通过
0 /**/ && %20 NOT ~ [blAnk] FaLSE /*i*/,0.75,通过,通过,拦截,拦截,通过
0 ) [blank] or /**/ true /**/ like /**/ true /**/ || ( 0,0.75,通过,通过,拦截,拦截,通过
" [blank] or ~ /**/ /**/ 0 = /**/ ( ~ /**/ ' ' ) [blank] || ",0.75,通过,通过,拦截,拦截,通过
0 ) [blank] || ~ /**/ /**/ false - ( /**/ ! ~ /**/ 0 ) /**/ or ( 0,0.75,通过,通过,拦截,拦截,通过
' [blank] || ~ /**/ /**/ 0 /**/ or ',0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ not /**/ ' ' - ( ' ' ) /**/,0.75,通过,通过,拦截,拦截,通过
0 ) /**/ and /**/ not /**/ true [blank] or ( 0,0.75,通过,通过,拦截,拦截,通过
0 /**/ or /**/ true = [blank] ( ~ /**/ /**/ 0 ) [blank],0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ ! /**/ /**/ false /**/ || ",0.75,通过,通过,拦截,拦截,通过
" [blank] || /**/ not /**/ /**/ 0 /**/ or ",0.75,通过,通过,拦截,拦截,通过
0 %0a netstat () { :;};,0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ 1 /**/ like /**/ true /**/ || ",0.75,通过,通过,拦截,拦截,通过
0 ) /**/ || /**/ 0 /**/ is [blank] false #,0.75,通过,通过,拦截,拦截,通过
0 /*A|V*/ and %20 NOt ~ [blANK] FalSe /*iu|,0.75,通过,通过,拦截,拦截,通过
0 [blank] or /**/ false = /**/ ( /**/ not ~ /**/ 0 ) [blank],0.75,通过,通过,拦截,拦截,通过
" ) [blank] and /**/ ! ~ /**/ false /**/ || ( ",0.75,通过,通过,拦截,拦截,通过
' [blank] or /**/ true = /**/ ( ~ /**/ /**/ 0 ) /**/ || ',0.75,通过,通过,拦截,拦截,通过
' ) /**/ and /**/ not [blank] true #,0.75,通过,通过,拦截,拦截,通过
' ) [blank] || /**/ ! ~ /**/ 0 /**/ is /**/ false [blank] or ( ',0.75,通过,通过,拦截,拦截,通过
0 /**/ or /**/ not [blank] ' ' /**/,0.75,通过,通过,拦截,拦截,通过
' ) /**/ or /**/ ! [blank] /**/ false -- [blank],0.75,通过,通过,拦截,拦截,通过
' ) [blank] and /**/ not ~ /**/ 0 -- [blank],0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ ! /**/ ' ' = [blank] ( /**/ true ) /**/,0.75,通过,通过,拦截,拦截,通过
0 /**/ or /**/ 0 = [blank] ( ' ' ) [blank],0.75,通过,通过,拦截,拦截,通过
0 /*x&eR#[*/ && + not ~ [blank] false /*i*/ \v,0.75,通过,通过,拦截,拦截,通过
' ) /**/ and /**/ ! [blank] true -- /**/,0.75,通过,通过,拦截,拦截,通过
" ) /**/ or /**/ 1 = [blank] ( ~ /**/ /**/ false ) #,0.75,通过,通过,拦截,拦截,通过
' ) [blank] || /**/ 0 = /**/ ( /**/ ! /**/ 1 ) /**/ or ( ',0.75,通过,通过,拦截,拦截,通过
" [blank] and /**/ ! ~ ' ' /**/ or ",0.75,通过,通过,拦截,拦截,通过
" ) [blank] || /**/ not ~ ' ' = /**/ ( /**/ not ~ /**/ false ) [blank] || ( ",0.75,通过,通过,拦截,拦截,通过
" /**/ or /**/ 1 = [blank] ( [blank] true ) /**/ or ",0.75,通过,通过,拦截,拦截,通过
0 ) /**/ || /**/ ! ~ /**/ false /**/ is /**/ false [blank] || ( 0,0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ 1 - ( /**/ ! /**/ true ) [blank] or ",0.75,通过,通过,拦截,拦截,通过
' [blank] && /**/ not ~ /**/ false /**/ || ',0.75,通过,通过,拦截,拦截,通过
' [blank] and /**/ not ~ /**/ false [blank] || ',0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ ! /**/ ' ' /**/ is /**/ true /**/,0.75,通过,通过,拦截,拦截,通过
0 ) /**/ || /**/ 1 /**/ is /**/ true [blank] or ( 0,0.75,通过,通过,拦截,拦截,通过
" ) /**/ and /**/ not ~ /**/ false [blank] || ( ",0.75,通过,通过,拦截,拦截,通过
" /**/ or /**/ true [blank] like /**/ true /**/ || ",0.75,通过,通过,拦截,拦截,通过
' ) /**/ && /**/ not ~ /**/ false [blank] or ( ',0.75,通过,通过,拦截,拦截,通过
0 ) [blank] or /**/ not /**/ 1 = [blank] ( /**/ not ~ /**/ 0 ) /**/ || ( 0,0.75,通过,通过,拦截,拦截,通过
" ) /**/ || ~ /**/ /**/ false [blank] or ( ",0.75,通过,通过,拦截,拦截,通过
0 [blank] || ' ' /**/ is /**/ false [blank],0.75,通过,通过,拦截,拦截,通过
0 /**/ || /**/ ! /**/ true = [blank] ( /**/ 0 ) [blank],0.75,通过,通过,拦截,拦截,通过
0 /**/ or /**/ ! [blank] ' ' /**/,0.75,通过,通过,拦截,拦截,通过
' /**/ || [blank] not [blank] [blank] 0 = /**/ ( ~ /**/ ' ' ) /**/ or ',0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ 1 /**/ is /**/ true [blank] || ",0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ 1 /**/ is /**/ true /**/ or ",0.75,通过,通过,拦截,拦截,通过
0 %0a which [blank] curl () { :;};,0.75,通过,通过,拦截,拦截,通过
0 ) [blank] and /**/ not ~ /**/ 0 /**/ or ( 0,0.75,通过,通过,拦截,拦截,通过
0 /*A|V*/ and %20 Not ~ [BLaNK] fAlSe /*IU|,0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ true /**/ is [blank] true /**/,0.75,通过,通过,拦截,拦截,通过
" [blank] || /**/ true - ( /**/ false ) /**/ or ",0.75,通过,通过,拦截,拦截,通过
0 () { :;}; which [blank] curl () { :;}; which [blank] curl $,0.75,通过,通过,拦截,拦截,通过
' [blank] or /**/ not /**/ /**/ false /**/ || ',0.75,通过,通过,拦截,拦截,通过
0 /*A|v*/ && /**/ not ~ [blaNk] FaLSe /*IU|,0.75,通过,通过,拦截,拦截,通过
0 /**/ or /**/ not [blank] true = /**/ ( /**/ not ~ /**/ false ) /**/,0.75,通过,通过,拦截,拦截,通过
' /**/ or /**/ ! /**/ /**/ false = [blank] ( /**/ true ) [blank] or ',0.75,通过,通过,拦截,拦截,通过
" [blank] and /**/ ! ~ /**/ 0 /**/ || ",0.75,通过,通过,拦截,拦截,通过
' ) /**/ && /**/ not ~ /**/ false [blank] || ( ',0.75,通过,通过,拦截,拦截,通过
0 /**/ || /**/ not /**/ /**/ 0 [blank],0.75,通过,通过,拦截,拦截,通过
' [blank] || ~ /**/ ' ' - ( /**/ not ~ ' ' ) [blank] || ',0.75,通过,通过,拦截,拦截,通过
0 [blank] or /**/ 1 - ( /**/ ! /**/ true ) /**/,0.75,通过,通过,拦截,拦截,通过
' /**/ or ~ /**/ /**/ false [blank] or ',0.75,通过,通过,拦截,拦截,通过
" ) [blank] && /**/ not /**/ 1 /**/ or ( ",0.75,通过,通过,拦截,拦截,通过
" [blank] || ~ /**/ /**/ false [blank] or ",0.75,通过,通过,拦截,拦截,通过
0 ) /**/ || /**/ true = [blank] ( [blank] true ) /**/ || ( 0,0.75,通过,通过,拦截,拦截,通过
0 ) [blank] and /**/ not ~ /**/ 0 /**/ || ( 0,0.75,通过,通过,拦截,拦截,通过
' ) [blank] and /**/ ! /**/ 1 /**/ || ( ',0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ ! /**/ 1 /**/ is /**/ false [blank],0.75,通过,通过,拦截,拦截,通过
' sleep %20 1 () { :;};,0.75,通过,通过,拦截,拦截,通过
" [blank] or ~ /**/ ' ' /**/ is /**/ true [blank] || ",0.75,通过,通过,拦截,拦截,通过
0 ) /**/ or /**/ 1 /**/ like [blank] true -- [blank],0.75,通过,通过,拦截,拦截,通过
" [blank] or /**/ ! /**/ true = /**/ ( /**/ not ~ ' ' ) [blank] || ",0.75,通过,通过,拦截,拦截,通过
" ) /**/ or /**/ ! [blank] /**/ false -- [blank],0.75,通过,通过,拦截,拦截,通过
0 /*a|v*/ AND %20 NOt ~ [BLANK] fALSE /*iU|,0.75,通过,通过,拦截,拦截,通过
0 ) [blank] or /**/ true [blank] like /**/ 1 #,0.75,通过,通过,拦截,拦截,通过
0 /*A|V*/ and %20 NoT ~ [bLANK] FaLsE /*Iu|,0.75,通过,通过,拦截,拦截,通过
0 + Or ~ + [BLANK] FAlSe /*S<O	cP*/,0.75,通过,通过,拦截,拦截,通过
%0a sleep [blank] 1 () { :;};,0.75,通过,通过,拦截,拦截,通过
' [blank] and /**/ not /**/ 1 /**/ || ',0.75,通过,通过,拦截,拦截,通过
' ) /**/ or /**/ 0 /**/ is /**/ false [blank] or ( ',0.75,通过,通过,拦截,拦截,通过
0 /*x&*/ && %0D NOt ~ [blANk] faLSe /*I*/,0.75,通过,通过,拦截,拦截,通过
0 [blank] || /**/ 1 /**/ is [blank] true /**/,0.75,通过,通过,拦截,拦截,通过
' ) [blank] or /**/ ! /**/ /**/ 0 /**/ || ( ',0.75,通过,通过,拦截,拦截,通过
0 & which %20 curl () { :;};,0.75,通过,通过,拦截,拦截,通过
' ) [blank] or ~ /**/ ' ' /**/ || ( ',0.75,通过,通过,拦截,拦截,通过
' ) /**/ || /**/ 1 /**/ is [blank] true #,0.75,通过,通过,拦截,拦截,通过
" ) /**/ or /**/ true [blank] is /**/ true [blank] || ( ",0.75,通过,通过,拦截,拦截,通过
" /**/ || /**/ true [blank] is /**/ true [blank] or ",0.75,通过,通过,拦截,拦截,通过
0 ) [blank] || ~ /**/ /**/ false - ( /**/ not /**/ true ) -- [blank],0.75,通过,通过,拦截,拦截,通过
0 /**/ and /**/ NoT ~ [bLanK] faLSe /**/,0.75,通过,通过,拦截,拦截,通过
0 $ which %20 curl () { :;}; which %20 curl,0.75,通过,通过,拦截,拦截,通过
' ) [blank] || /**/ ! /**/ /**/ false /**/ or ( ',0.75,通过,通过,拦截,拦截,通过
0 ) [blank] || /**/ not ~ /**/ 0 /**/ is /**/ false [blank] || ( 0,0.75,通过,通过,拦截,拦截,通过
0 ) /**/ || /**/ false /**/ is /**/ false [blank] or ( 0,0.75,通过,通过,拦截,拦截,通过
0 ) [blank] or ~ /**/ /**/ 0 /**/ is /**/ true -- [blank],0.75,通过,通过,拦截,拦截,通过
' ) [blank] || /**/ not /**/ true [blank] is /**/ false -- [blank],0.75,通过,通过,拦截,拦截,通过
