# -*- coding: utf-8 -*-
"""
测试CSV生成功能的脚本
"""

import pandas as pd
import csv
import re

def test_payload_cleaning():
    """测试载荷清理功能"""
    
    def clean_payload_for_csv(payload):
        """清理载荷内容，确保CSV格式正确"""
        if pd.isna(payload):
            return ""
        
        # 转换为字符串
        clean_payload = str(payload)
        
        # 替换换行符、回车符、制表符为空格
        clean_payload = clean_payload.replace('\n', ' ')
        clean_payload = clean_payload.replace('\r', ' ')
        clean_payload = clean_payload.replace('\t', ' ')
        
        # 替换其他控制字符
        clean_payload = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', ' ', clean_payload)
        
        # 压缩多个连续空格为单个空格
        clean_payload = re.sub(r'\s+', ' ', clean_payload)
        
        # 去除首尾空格
        clean_payload = clean_payload.strip()
        
        return clean_payload
    
    # 测试问题载荷
    problematic_payloads = [
        "' ) [blank] && /**/ ! ~ ' ' -- [blank] &o",
        "0 [bLANK] OR ~ %09 [blaNK] falSe /*S<O\tcP--?H#*/",
        "char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]what\"\"\n%7d %7d",
        "> < %68 e a %44 %45 R %20 %6f %6e m S %49 n %65 %72 T %69 A s %74 A %72 %54 like &#X61;&#6c;&#x65;&#x72;&#X74;&#X28;&#X31;&#X29; %20 > >",
        "+ %6f n %42 l %55 %52 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f"
    ]
    
    print("=== 载荷清理测试 ===")
    for i, payload in enumerate(problematic_payloads, 1):
        cleaned = clean_payload_for_csv(payload)
        print(f"{i}. 原始: {repr(payload)}")
        print(f"   清理: {repr(cleaned)}")
        print()

def test_csv_writing():
    """测试CSV写入功能"""
    
    # 模拟数据
    test_data = [
        {
            'payload': "' ) [blank] && /**/ ! ~ ' ' -- [blank] &o",
            'specificity': 1.0,
            'MODSECURITY_expected': '通过',
            'NGX_LUA_expected': '通过',
            'SAFELINE_expected': '通过',
            'NAXSI_expected': '拦截',
            'ALIYUN_expected': '通过'
        },
        {
            'payload': "char# { char# %7b %3C ? %70 %48 %70 %20 echo[blank]what\"\"\n%7d %7d",
            'specificity': 1.0,
            'MODSECURITY_expected': '通过',
            'NGX_LUA_expected': '通过',
            'SAFELINE_expected': '通过',
            'NAXSI_expected': '拦截',
            'ALIYUN_expected': '通过'
        }
    ]
    
    def clean_payload_for_csv(payload):
        """清理载荷内容，确保CSV格式正确"""
        if pd.isna(payload):
            return ""
        
        # 转换为字符串
        clean_payload = str(payload)
        
        # 替换换行符、回车符、制表符为空格
        clean_payload = clean_payload.replace('\n', ' ')
        clean_payload = clean_payload.replace('\r', ' ')
        clean_payload = clean_payload.replace('\t', ' ')
        
        # 替换其他控制字符
        clean_payload = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', ' ', clean_payload)
        
        # 压缩多个连续空格为单个空格
        clean_payload = re.sub(r'\s+', ' ', clean_payload)
        
        # 去除首尾空格
        clean_payload = clean_payload.strip()
        
        return clean_payload
    
    print("=== CSV写入测试 ===")
    
    # 写入测试CSV文件
    test_file = "test_output.csv"
    
    with open(test_file, 'w', newline='', encoding='utf-8') as csvfile:
        header = ['payload', 'specificity', 'MODSECURITY_expected', 'NGX_LUA_expected', 
                 'SAFELINE_expected', 'NAXSI_expected', 'ALIYUN_expected']
        
        writer = csv.writer(csvfile, quoting=csv.QUOTE_ALL)
        writer.writerow(header)
        
        for item in test_data:
            clean_payload = clean_payload_for_csv(item['payload'])
            row = [
                clean_payload,
                str(item['specificity']),
                item['MODSECURITY_expected'],
                item['NGX_LUA_expected'],
                item['SAFELINE_expected'],
                item['NAXSI_expected'],
                item['ALIYUN_expected']
            ]
            writer.writerow(row)
    
    print(f"测试CSV文件已生成: {test_file}")
    
    # 读取并验证
    try:
        df = pd.read_csv(test_file)
        print(f"成功读取CSV文件，包含 {len(df)} 行数据")
        print("CSV内容预览:")
        print(df.to_string())
        
        # 检查是否有格式问题
        for i, row in df.iterrows():
            payload = row['payload']
            if '\n' in str(payload) or '\t' in str(payload):
                print(f"警告: 第{i+1}行仍包含换行符或制表符")
            
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")

def main():
    """主函数"""
    test_payload_cleaning()
    test_csv_writing()

if __name__ == "__main__":
    main()
