# -*- coding: utf-8 -*-
"""
脚本用于加载payloads文件夹下所有载荷，并分别对五种WAF发送HTTP请求，
记录返回状态码到CSV文件，用于后续waf_payload_set.py的处理。
"""

import os
import requests
import csv
import time
import argparse
from tqdm import tqdm
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"waf_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 默认WAF配置
DEFAULT_WAFS = {
    # 'MODSECURITY': 'http://192.168.186.128:80/',
    # 'NGX_LUA': 'http://192.168.186.146:80/',
    # 'SAFELINE': 'http://192.168.186.139:80/',
    # 'NAXSI': 'http://192.168.186.142:80/',
    # 如果需要添加第五种WAF，请在此处添加
    'CLOUDFLARE': 'https://danuoyi.xyz/wordpress/'
}

def load_payloads(payloads_dir):
    """
    读取payloads目录及其子目录下所有txt文件，将载荷去重后返回列表。
    
    参数:
    - payloads_dir: payload目录路径
    
    返回:
    - 去重后的payload列表
    """
    logger.info(f"开始从 {payloads_dir} 加载载荷...")
    payload_set = set()
    
    # 遍历目录
    for root, dirs, files in os.walk(payloads_dir):
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):  # 忽略空行和注释行
                                payload_set.add(line)
                    logger.info(f"已加载 {file_path}")
                except Exception as e:
                    logger.error(f"加载 {file_path} 时出错: {e}")
    
    payloads = list(payload_set)
    logger.info(f"共加载 {len(payloads)} 个去重后的载荷")
    return payloads

def test_payload(payload, wafs, timeout, verify_ssl, max_retries=3, retry_delay=1):
    """
    测试单个payload对所有WAF的响应
    
    参数:
    - payload: 要测试的payload
    - wafs: WAF字典，键为WAF名称，值为URL
    - timeout: 请求超时时间（秒）
    - verify_ssl: 是否验证SSL证书
    - max_retries: 最大重试次数
    - retry_delay: 重试延迟（秒）
    
    返回:
    - 包含payload和各WAF响应状态码的字典
    """
    result = {'payload': payload}
    session = requests.Session()
    
    for waf_name, waf_url in wafs.items():
        for retry in range(max_retries):
            try:
                # 发送请求，使用title参数和action=search
                response = session.get(
                    waf_url, 
                    params={"title": payload, "action": "search"}, 
                    timeout=timeout,
                    verify=verify_ssl
                )
                result[waf_name] = response.status_code
                # 成功获取响应，跳出重试循环
                break
            except requests.exceptions.Timeout:
                logger.warning(f"请求 {waf_name} 超时 (尝试 {retry+1}/{max_retries})")
                if retry == max_retries - 1:  # 最后一次重试
                    result[waf_name] = "Timeout"
                else:
                    time.sleep(retry_delay)
            except Exception as e:
                logger.warning(f"请求 {waf_name} 时出错: {e} (尝试 {retry+1}/{max_retries})")
                if retry == max_retries - 1:  # 最后一次重试
                    result[waf_name] = f"Error: {str(e)[:50]}"
                else:
                    time.sleep(retry_delay)
        
        # 请求之间添加短暂延迟，避免请求过于频繁
        time.sleep(0.2)
    
    return result

def test_payloads_parallel(payloads, wafs, output_file, timeout=5, verify_ssl=False, max_workers=10, batch_size=100):
    """
    并行测试多个payload对所有WAF的响应，并将结果写入CSV文件
    
    参数:
    - payloads: 要测试的payload列表
    - wafs: WAF字典，键为WAF名称，值为URL
    - output_file: 输出CSV文件路径
    - timeout: 请求超时时间（秒）
    - verify_ssl: 是否验证SSL证书
    - max_workers: 最大并行工作线程数
    - batch_size: 每批处理的payload数量
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
    
    # 准备CSV文件头
    fieldnames = ['payload'] + list(wafs.keys())
    
    # 打开CSV文件并写入头
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # 分批处理payload
        for i in range(0, len(payloads), batch_size):
            batch = payloads[i:i+batch_size]
            logger.info(f"处理批次 {i//batch_size + 1}/{(len(payloads)-1)//batch_size + 1} ({len(batch)} 个载荷)")
            
            # 使用线程池并行处理
            results = []
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_payload = {
                    executor.submit(test_payload, payload, wafs, timeout, verify_ssl): payload 
                    for payload in batch
                }
                
                # 处理完成的任务
                for future in tqdm(as_completed(future_to_payload), total=len(batch), desc="测试进度"):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        payload = future_to_payload[future]
                        logger.error(f"处理载荷 '{payload[:50]}...' 时出错: {e}")
            
            # 将结果写入CSV
            for result in results:
                writer.writerow(result)
            
            # 每批次完成后刷新文件，确保数据写入
            csvfile.flush()
            
            logger.info(f"批次 {i//batch_size + 1} 完成，已写入 {len(results)} 条结果")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="WAF载荷测试工具 - 测试payload对多个WAF的响应")
    parser.add_argument("--payloads-dir", default="payloads", 
                        help="载荷目录路径 (默认: 端口扫描_本机勿用/识别载荷集构建/payloads)")
    parser.add_argument("--output-file", default="payload_responses.csv", 
                        help="输出CSV文件路径 (默认: 端口扫描_本机勿用/识别载荷集构建/payload_responses.csv)")
    parser.add_argument("--timeout", type=float, default=5, 
                        help="请求超时时间（秒，默认5）")
    parser.add_argument("--no-verify-ssl", action="store_true", 
                        help="不验证SSL证书（用于自签名证书）")
    parser.add_argument("--max-workers", type=int, default=10, 
                        help="最大并行工作线程数（默认10）")
    parser.add_argument("--batch-size", type=int, default=100, 
                        help="每批处理的payload数量（默认100）")
    
    args = parser.parse_args()
    
    # 加载payload
    payloads = load_payloads(args.payloads_dir)
    
    # 如果没有找到payload，退出
    if not payloads:
        logger.error(f"未在 {args.payloads_dir} 找到任何载荷，请检查目录路径")
        return 1
    
    # 测试payload
    logger.info(f"开始测试 {len(payloads)} 个载荷对 {len(DEFAULT_WAFS)} 个WAF的响应...")
    test_payloads_parallel(
        payloads=payloads,
        wafs=DEFAULT_WAFS,
        output_file=args.output_file,
        timeout=args.timeout,
        verify_ssl=not args.no_verify_ssl,
        max_workers=args.max_workers,
        batch_size=args.batch_size
    )
    
    logger.info(f"测试完成，结果已写入: {args.output_file}")
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        logger.info("用户中断，程序退出")
        exit(1)
    except Exception as e:
        logger.exception(f"程序执行出错: {e}")
        exit(1)
